#!/bin/bash

# EDC Research Project 快速启动脚本
# 用于本地开发和测试

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置参数
PROJECT_NAME="edc-research-project"
JAR_NAME="edc-research-project-remote.jar"
JAR_PATH="edc-research-center/edc-research-api/target"
CONFIG_FILE="application-remote.yml"
CONFIG_PATH="edc-research-center/edc-research-api/src/main/resources"
PROFILE="edc-research-remote"
SPRING_PROFILE="dev"
PID_FILE="${PROJECT_NAME}.pid"
LOG_FILE="${PROJECT_NAME}.log"

# JVM 参数
JVM_OPTS="-Xmx512m -Xms256m -Xss256k"
SPRING_OPTS="--spring.profiles.active=${SPRING_PROFILE}"

show_usage() {
    echo "EDC Research Project 快速启动脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  build     构建项目"
    echo "  start     启动应用"
    echo "  stop      停止应用"
    echo "  restart   重启应用"
    echo "  status    查看状态"
    echo "  logs      查看日志"
    echo "  clean     清理构建文件"
    echo "  help      显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 build && $0 start    # 构建并启动"
    echo "  $0 logs                 # 查看实时日志"
    echo "  $0 restart              # 重启应用"
}

check_java() {
    if ! command -v java &> /dev/null; then
        log_error "Java 未安装或不在 PATH 中"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    log_info "Java 版本: $JAVA_VERSION"
}

check_maven() {
    if ! command -v mvn &> /dev/null; then
        log_error "Maven 未安装或不在 PATH 中"
        exit 1
    fi
    
    MVN_VERSION=$(mvn -version | head -n 1 | cut -d' ' -f3)
    log_info "Maven 版本: $MVN_VERSION"
}

build_project() {
    log_info "开始构建项目..."
    check_maven
    
    # 清理并构建
    mvn clean package -DskipTests=true -P ${PROFILE}
    
    if [ $? -eq 0 ]; then
        log_success "项目构建成功"
        
        # 检查 JAR 文件
        if [ -f "${JAR_PATH}/${JAR_NAME}" ]; then
            JAR_SIZE=$(du -h "${JAR_PATH}/${JAR_NAME}" | cut -f1)
            log_info "生成的 JAR 文件: ${JAR_PATH}/${JAR_NAME} (${JAR_SIZE})"
        else
            log_error "JAR 文件未找到: ${JAR_PATH}/${JAR_NAME}"
            exit 1
        fi
    else
        log_error "项目构建失败"
        exit 1
    fi
}

start_application() {
    log_info "启动应用..."
    check_java
    
    # 检查是否已经在运行
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            log_warning "应用已在运行 (PID: $PID)"
            return 0
        else
            log_warning "PID 文件存在但进程不存在，删除 PID 文件"
            rm -f "$PID_FILE"
        fi
    fi
    
    # 检查 JAR 文件
    if [ ! -f "${JAR_PATH}/${JAR_NAME}" ]; then
        log_error "JAR 文件不存在: ${JAR_PATH}/${JAR_NAME}"
        log_info "请先运行: $0 build"
        exit 1
    fi
    
    # 创建日志目录
    mkdir -p logs
    
    # 启动应用
    log_info "启动命令: java ${JVM_OPTS} -jar ${JAR_PATH}/${JAR_NAME} ${SPRING_OPTS}"

    nohup java ${JVM_OPTS} -jar "${JAR_PATH}/${JAR_NAME}" ${SPRING_OPTS} > "logs/${LOG_FILE}" 2>&1 &
    PID=$!
    echo $PID > "$PID_FILE"
    
    log_success "应用已启动 (PID: $PID)"
    log_info "日志文件: logs/${LOG_FILE}"
    log_info "PID 文件: ${PID_FILE}"
    
    # 等待应用启动
    log_info "等待应用启动..."
    sleep 5
    
    # 检查应用是否成功启动
    if ps -p $PID > /dev/null 2>&1; then
        log_success "应用启动成功！"
        log_info "应用端口: 8089 (dev环境)"
        log_info "API 地址: http://localhost:8089/api"
        log_info "Swagger 文档: http://localhost:8089/api/swagger-ui.html"
        log_info "查看日志: $0 logs"
    else
        log_error "应用启动失败，请查看日志文件"
        rm -f "$PID_FILE"
        exit 1
    fi
}

stop_application() {
    log_info "停止应用..."
    
    if [ ! -f "$PID_FILE" ]; then
        log_warning "PID 文件不存在，应用可能未运行"
        return 0
    fi
    
    PID=$(cat "$PID_FILE")
    
    if ps -p $PID > /dev/null 2>&1; then
        log_info "正在停止应用 (PID: $PID)..."
        kill -15 $PID
        
        # 等待进程结束
        for i in {1..10}; do
            if ! ps -p $PID > /dev/null 2>&1; then
                break
            fi
            sleep 1
        done
        
        # 如果进程仍在运行，强制终止
        if ps -p $PID > /dev/null 2>&1; then
            log_warning "进程仍在运行，强制终止..."
            kill -9 $PID
            sleep 2
        fi
        
        rm -f "$PID_FILE"
        log_success "应用已停止"
    else
        log_warning "进程不存在 (PID: $PID)"
        rm -f "$PID_FILE"
    fi
}

check_status() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            UPTIME=$(ps -o etime= -p $PID | tr -d ' ')
            log_success "✅ 应用正在运行"
            log_info "   PID: $PID"
            log_info "   运行时间: $UPTIME"
            log_info "   日志文件: logs/${LOG_FILE}"
            
            # 检查端口
            if command -v lsof &> /dev/null; then
                if lsof -p $PID | grep -q "LISTEN"; then
                    PORT=$(lsof -p $PID | grep LISTEN | head -1 | awk '{print $9}' | cut -d':' -f2)
                    log_info "   端口状态: $PORT (监听中)"
                else
                    log_warning "   端口状态: 未监听"
                fi
            fi
        else
            log_error "❌ 应用未运行 (PID 文件存在但进程不存在)"
            rm -f "$PID_FILE"
        fi
    else
        log_error "❌ 应用未运行 (PID 文件不存在)"
    fi
}

show_logs() {
    if [ -f "logs/${LOG_FILE}" ]; then
        log_info "显示实时日志 (按 Ctrl+C 退出)..."
        tail -f "logs/${LOG_FILE}"
    else
        log_error "日志文件不存在: logs/${LOG_FILE}"
    fi
}

clean_project() {
    log_info "清理项目..."
    
    # 停止应用
    if [ -f "$PID_FILE" ]; then
        stop_application
    fi
    
    # 清理构建文件
    if command -v mvn &> /dev/null; then
        mvn clean -q
    fi
    
    # 清理日志和 PID 文件
    rm -f "$PID_FILE"
    rm -rf logs target
    
    log_success "清理完成"
}

# 主函数
main() {
    case "${1:-help}" in
        "build")
            build_project
            ;;
        "start")
            start_application
            ;;
        "stop")
            stop_application
            ;;
        "restart")
            stop_application
            sleep 2
            start_application
            ;;
        "status")
            check_status
            ;;
        "logs")
            show_logs
            ;;
        "clean")
            clean_project
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            log_error "未知命令: $1"
            show_usage
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
