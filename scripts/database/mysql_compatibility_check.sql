-- =====================================================
-- MySQL 版本兼容性检查脚本
-- 用于检查当前 MySQL 版本并提供兼容性建议
-- =====================================================

-- 显示当前 MySQL 版本信息
SELECT 
    VERSION() as mysql_version,
    @@version_comment as version_comment,
    @@sql_mode as sql_mode;

-- 检查是否支持 JSON 数据类型 (MySQL 5.7.8+)
SELECT 
    CASE 
        WHEN VERSION() >= '5.7.8' THEN 'JSON数据类型: 支持'
        ELSE 'JSON数据类型: 不支持，建议升级到MySQL 5.7.8+'
    END as json_support;

-- 检查是否支持生成列 (MySQL 5.7.6+)
SELECT 
    CASE 
        WHEN VERSION() >= '5.7.6' THEN '生成列: 支持'
        ELSE '生成列: 不支持，建议升级到MySQL 5.7.6+'
    END as generated_column_support;

-- 检查是否支持 CREATE OR REPLACE VIEW (MySQL 5.7.7+)
SELECT 
    CASE 
        WHEN VERSION() >= '5.7.7' THEN 'CREATE OR REPLACE VIEW: 支持'
        ELSE 'CREATE OR REPLACE VIEW: 不支持，已使用DROP+CREATE替代'
    END as create_or_replace_view_support;

-- 检查是否支持部分索引/过滤索引 (MySQL 8.0.13+)
SELECT 
    CASE 
        WHEN VERSION() >= '8.0.13' THEN '部分索引(WHERE条件): 支持'
        ELSE '部分索引(WHERE条件): 不支持，已移除WHERE条件'
    END as partial_index_support;

-- 检查当前数据库字符集
SELECT 
    DEFAULT_CHARACTER_SET_NAME as db_charset,
    DEFAULT_COLLATION_NAME as db_collation
FROM information_schema.SCHEMATA 
WHERE SCHEMA_NAME = DATABASE();

-- 检查 InnoDB 引擎状态
SHOW ENGINE INNODB STATUS\G

-- 显示当前数据库大小
SELECT 
    table_schema as database_name,
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as size_mb
FROM information_schema.tables 
WHERE table_schema = DATABASE()
GROUP BY table_schema;

-- 检查系统变量配置
SELECT 
    @@max_connections as max_connections,
    @@innodb_buffer_pool_size as innodb_buffer_pool_size,
    @@query_cache_size as query_cache_size,
    @@tmp_table_size as tmp_table_size,
    @@max_heap_table_size as max_heap_table_size;

-- =====================================================
-- 兼容性建议
-- =====================================================

/*
MySQL 版本兼容性说明：

1. MySQL 5.6 及以下版本：
   - 不支持 JSON 数据类型
   - 不支持生成列
   - 不支持 CREATE OR REPLACE VIEW
   - 不支持部分索引
   - 建议升级到 MySQL 5.7 或更高版本

2. MySQL 5.7 版本：
   - 支持大部分现代特性
   - 不支持部分索引 (WHERE 条件)
   - 推荐版本，兼容性良好

3. MySQL 8.0 及以上版本：
   - 支持所有现代特性
   - 性能最佳
   - 推荐用于生产环境

当前脚本已针对 MySQL 5.7+ 进行优化，确保最大兼容性。

如果使用 MySQL 5.6 或更早版本，建议：
1. 升级到 MySQL 5.7 或 8.0
2. 或者联系开发团队提供 MySQL 5.6 兼容版本
*/
