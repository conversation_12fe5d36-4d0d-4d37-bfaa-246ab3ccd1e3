#!/bin/bash

# Git Hooks安装脚本
# 安装pre-checkout hook来提醒用户处理未提交的更改

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查是否在Git仓库中
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo "错误：当前目录不是Git仓库！"
    exit 1
fi

# 获取Git hooks目录
HOOKS_DIR=$(git rev-parse --git-dir)/hooks

print_info "正在安装Git hooks到: $HOOKS_DIR"

# 创建pre-checkout hook
cat > "$HOOKS_DIR/pre-checkout" << 'EOF'
#!/bin/bash

# Pre-checkout hook - 在切换分支前检查未提交的更改

# 颜色定义
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查是否有未提交的更改
if ! git diff-index --quiet HEAD --; then
    echo -e "${YELLOW}⚠️  警告：检测到未提交的更改！${NC}"
    echo
    echo -e "${BLUE}建议的操作：${NC}"
    echo "1. 提交更改: git add . && git commit -m '描述信息'"
    echo "2. 暂存更改: git stash push -u -m '描述信息'"
    echo "3. 使用安全切换脚本: ./scripts/safe-checkout.sh <branch>"
    echo
    echo -e "${RED}如果继续，可能会丢失未提交的更改！${NC}"
    echo
    
    read -p "确定要继续吗？(y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "操作已取消"
        exit 1
    fi
fi
EOF

# 设置执行权限
chmod +x "$HOOKS_DIR/pre-checkout"

print_success "Pre-checkout hook已安装"

# 创建commit-msg hook来规范提交信息
cat > "$HOOKS_DIR/commit-msg" << 'EOF'
#!/bin/bash

# Commit message hook - 检查提交信息格式

commit_regex='^(feat|fix|docs|style|refactor|test|chore)(\(.+\))?: .{1,50}'

if ! grep -qE "$commit_regex" "$1"; then
    echo "❌ 提交信息格式不正确！"
    echo
    echo "正确格式: <type>(<scope>): <description>"
    echo
    echo "类型 (type):"
    echo "  feat:     新功能"
    echo "  fix:      修复bug"
    echo "  docs:     文档更新"
    echo "  style:    代码格式化"
    echo "  refactor: 重构代码"
    echo "  test:     测试相关"
    echo "  chore:    构建/工具相关"
    echo
    echo "示例:"
    echo "  feat(user): 添加用户登录功能"
    echo "  fix(api): 修复数据查询bug"
    echo "  docs: 更新README文档"
    echo
    exit 1
fi
EOF

chmod +x "$HOOKS_DIR/commit-msg"

print_success "Commit-msg hook已安装"

# 创建pre-push hook来检查分支状态
cat > "$HOOKS_DIR/pre-push" << 'EOF'
#!/bin/bash

# Pre-push hook - 推送前检查

protected_branch='main'
current_branch=$(git symbolic-ref HEAD | sed -e 's,.*/\(.*\),\1,')

# 检查是否直接推送到主分支
if [ $protected_branch = $current_branch ]; then
    echo "❌ 不允许直接推送到 $protected_branch 分支！"
    echo "请使用Pull Request流程"
    exit 1
fi

# 检查是否有未提交的更改
if ! git diff-index --quiet HEAD --; then
    echo "⚠️  警告：存在未提交的更改，建议先提交"
    git status --short
    echo
fi

echo "✅ 推送检查通过"
EOF

chmod +x "$HOOKS_DIR/pre-push"

print_success "Pre-push hook已安装"

print_info "所有Git hooks已安装完成！"
echo
print_info "已安装的hooks："
echo "  - pre-checkout: 切换分支前检查未提交更改"
echo "  - commit-msg:   规范提交信息格式"
echo "  - pre-push:     推送前检查"
echo
print_warning "注意：这些hooks只在本地生效，不会影响其他开发者"
