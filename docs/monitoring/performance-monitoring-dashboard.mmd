%% EDC项目性能监控仪表板
%% 生成时间: 2025-06-26
%% 用途: 性能监控指标可视化

graph TB
    %% 样式定义
    classDef critical fill:#ffebee,stroke:#c62828,stroke-width:3px,color:#000
    classDef warning fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#000
    classDef normal fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    classDef info fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#000
    
    %% 主监控面板
    A[EDC性能监控仪表板<br/>Performance Monitoring Dashboard]:::info
    
    %% 应用层监控
    A --> B[应用层监控<br/>Application Monitoring]:::info
    B --> B1[响应时间监控<br/>Response Time<br/>目标: P95 < 500ms]:::normal
    B --> B2[吞吐量监控<br/>Throughput<br/>目标: QPS > 1000]:::normal
    B --> B3[错误率监控<br/>Error Rate<br/>目标: < 0.1%]:::warning
    B --> B4[并发用户监控<br/>Concurrent Users<br/>目标: 1000+]:::normal
    
    %% JVM监控
    A --> C[JVM监控<br/>JVM Monitoring]:::info
    C --> C1[堆内存使用<br/>Heap Memory<br/>告警: > 80%]:::warning
    C --> C2[GC频率<br/>GC Frequency<br/>告警: > 10次/分钟]:::warning
    C --> C3[线程数量<br/>Thread Count<br/>告警: > 500]:::normal
    C --> C4[类加载<br/>Class Loading<br/>监控: 加载/卸载]:::normal
    
    %% 数据库监控
    A --> D[数据库监控<br/>Database Monitoring]:::info
    D --> D1[连接池使用率<br/>Connection Pool<br/>告警: > 80%]:::warning
    D --> D2[慢查询监控<br/>Slow Queries<br/>告警: > 1秒]:::critical
    D --> D3[数据库CPU<br/>DB CPU Usage<br/>告警: > 70%]:::warning
    D --> D4[锁等待<br/>Lock Waits<br/>告警: > 100ms]:::warning
    
    %% 缓存监控
    A --> E[缓存监控<br/>Cache Monitoring]:::info
    E --> E1[Redis内存使用<br/>Redis Memory<br/>告警: > 80%]:::warning
    E --> E2[缓存命中率<br/>Cache Hit Rate<br/>目标: > 90%]:::normal
    E --> E3[Redis连接数<br/>Redis Connections<br/>监控: 活跃连接]:::normal
    E --> E4[缓存过期<br/>Cache Expiration<br/>监控: 过期策略]:::normal
    
    %% 搜索引擎监控
    A --> F[搜索引擎监控<br/>ElasticSearch Monitoring]:::info
    F --> F1[集群健康<br/>Cluster Health<br/>状态: Green/Yellow/Red]:::normal
    F --> F2[索引性能<br/>Index Performance<br/>监控: 索引/搜索速度]:::normal
    F --> F3[存储使用<br/>Storage Usage<br/>告警: > 85%]:::warning
    F --> F4[查询延迟<br/>Query Latency<br/>目标: < 100ms]:::normal
    
    %% 系统资源监控
    A --> G[系统资源监控<br/>System Resources]:::info
    G --> G1[CPU使用率<br/>CPU Usage<br/>告警: > 80%]:::warning
    G --> G2[内存使用率<br/>Memory Usage<br/>告警: > 85%]:::warning
    G --> G3[磁盘使用率<br/>Disk Usage<br/>告警: > 90%]:::critical
    G --> G4[网络IO<br/>Network I/O<br/>监控: 带宽使用]:::normal
    
    %% 业务监控
    A --> H[业务监控<br/>Business Monitoring]:::info
    H --> H1[用户登录成功率<br/>Login Success Rate<br/>目标: > 99%]:::normal
    H --> H2[数据同步成功率<br/>Data Sync Success<br/>目标: > 95%]:::normal
    H --> H3[文件上传成功率<br/>File Upload Success<br/>目标: > 98%]:::normal
    H --> H4[API调用分布<br/>API Call Distribution<br/>监控: 热点接口]:::info
    
    %% 告警规则
    subgraph "告警规则 Alert Rules"
        direction TB
        AR1[🔴 P0告警: 系统不可用<br/>响应时间 > 5秒<br/>错误率 > 5%]:::critical
        AR2[🟡 P1告警: 性能下降<br/>响应时间 > 2秒<br/>CPU > 80%]:::warning
        AR3[🟢 P2告警: 资源预警<br/>内存 > 85%<br/>磁盘 > 90%]:::normal
    end
    
    %% 监控工具栈
    subgraph "监控工具栈 Monitoring Stack"
        direction LR
        MS1[Micrometer<br/>指标收集]
        MS2[Prometheus<br/>指标存储]
        MS3[Grafana<br/>可视化展示]
        MS4[AlertManager<br/>告警管理]
    end
    
    %% 性能基线
    subgraph "性能基线 Performance Baseline"
        direction TB
        PB1[响应时间基线<br/>P50: 100ms, P95: 300ms, P99: 500ms]
        PB2[吞吐量基线<br/>正常: 800 QPS, 峰值: 1500 QPS]
        PB3[资源使用基线<br/>CPU: 40%, 内存: 60%, 磁盘: 70%]
    end
