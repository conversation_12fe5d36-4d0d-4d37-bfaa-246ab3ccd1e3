# WebSocket智能日志提取配置说明

## 概述

SpringLogWebSocketHandler现在支持可配置的智能日志提取功能，用户可以通过配置文件控制是否对日志内容进行智能提取，默认显示完整的日志内容。

## 配置项说明

### 1. 智能提取相关配置

在 `application-dev.yml` 或 `application-local.yml` 中添加以下配置：

```yaml
websocket:
  log:
    # ========== 智能日志提取配置 ==========
    # 是否启用智能日志内容提取（默认false，显示完整日志）
    smart-extract-enabled: false
    # 是否显示完整日志内容（默认true，优先级高于智能提取）
    show-full-content: true
    # 是否只提取日志内容部分（默认false，当智能提取启用时生效）
    extract-content-only: false
    # ========== WebSocket日志测试任务配置 ==========
    # 是否启用WebSocket日志测试任务（仅在local环境启用）
    test-task-enabled: false
```

### 2. 配置项详细说明

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `smart-extract-enabled` | `false` | 是否启用智能日志内容提取功能 |
| `show-full-content` | `true` | 是否显示完整日志内容，优先级最高 |
| `extract-content-only` | `false` | 当智能提取启用时，是否只显示提取的内容部分 |
| `test-task-enabled` | `false` | 是否启用WebSocket日志测试任务（仅建议在local环境启用） |

### 3. 配置优先级

1. **最高优先级**: `show-full-content: true` - 直接显示完整日志，忽略其他设置
2. **中等优先级**: `smart-extract-enabled: false` - 不进行智能提取，显示原始日志
3. **最低优先级**: `extract-content-only` - 仅在智能提取启用时生效

## 使用场景

### 场景1: 默认配置（推荐）
```yaml
websocket:
  log:
    smart-extract-enabled: false
    show-full-content: true
    extract-content-only: false
```
**效果**: 显示完整的日志内容，包含时间戳、日志级别、线程信息、类名等所有信息。

### 场景2: 启用智能提取
```yaml
websocket:
  log:
    smart-extract-enabled: true
    show-full-content: false
    extract-content-only: false
```
**效果**: 尝试智能提取日志的主要内容部分，如果提取失败则显示完整日志。

### 场景3: 仅显示提取内容
```yaml
websocket:
  log:
    smart-extract-enabled: true
    show-full-content: false
    extract-content-only: true
```
**效果**: 只显示智能提取的内容部分，如果提取失败则显示完整日志。

## 智能提取支持的日志格式

智能提取功能支持以下常见的日志格式：

1. **冒号分隔格式**: `时间戳 级别 [线程] 类名 : 消息内容`
2. **破折号分隔格式**: `时间戳 级别 [线程] 类名 - 消息内容`
3. **类名分析格式**: `时间戳 级别 [线程] com.example.Class 消息内容`

## 环境配置说明

### WebSocket日志测试任务环境配置

| 环境 | 配置文件 | test-task-enabled | 说明 |
|------|----------|-------------------|------|
| local | application-local.yml | `true` | 本地开发环境，启用测试任务 |
| dev | application-dev.yml | `false` | 开发环境，禁用测试任务 |
| feature | application-feature.yml | `false` | 功能测试环境，禁用测试任务 |
| remote | application-remote.yml | `false` | 远程环境，禁用测试任务 |

**注意**: WebSocket日志测试任务会每秒输出测试日志，仅建议在local环境启用，避免在其他环境产生不必要的日志输出。

## 测试API

### 1. 查看当前配置
```http
GET /api/websocket-test/log-extract-config
```

### 2. 查看测试任务状态
```http
GET /api/websocket-test/test-task-status
```

### 3. 测试不同日志格式
```http
POST /api/websocket-test/test-log-formats
```

### 4. 查看WebSocket状态
```http
GET /api/websocket-test/status
```

## 调试信息

当 `websocket.log.enabled: true` 时，系统会输出详细的调试信息：

- 智能提取过程的日志
- 配置生效情况的日志
- 日志处理结果的日志

## 注意事项

1. **性能考虑**: 智能提取会增加一定的CPU开销，建议在生产环境中谨慎使用
2. **兼容性**: 默认配置确保向后兼容，不会影响现有功能
3. **实时生效**: 配置修改需要重启应用才能生效
4. **日志格式**: 智能提取的效果取决于日志的格式，建议先测试确认效果

## 示例

### 原始日志
```
2025-07-23 14:30:25.123 INFO [main] com.haoys.user.service.UserService : 用户登录成功，用户ID: 12345
```

### 不同配置下的显示效果

**默认配置 (show-full-content: true)**:
```
2025-07-23 14:30:25.123 INFO [main] com.haoys.user.service.UserService : 用户登录成功，用户ID: 12345
```

**智能提取 (smart-extract-enabled: true, show-full-content: false)**:
```
用户登录成功，用户ID: 12345
```

**仅提取内容 (extract-content-only: true)**:
```
用户登录成功，用户ID: 12345
```
