package com.haoys.xinjiang.controller;


import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.elasticsearch.TreeSearchParam;
import com.haoys.xinjiang.domain.dto.PatientNaPiSearchDto;
import com.haoys.xinjiang.domain.param.SearchExportParam;
import com.haoys.xinjiang.domain.param.SearchParam;
import com.haoys.xinjiang.model.PatientExportFile;
import com.haoys.xinjiang.model.PatientNapiSearch;
import com.haoys.xinjiang.service.PatientExportFileService;
import com.haoys.xinjiang.service.PatientNaPiSearchTreeService;
import com.haoys.xinjiang.service.PatientNaPiSearchV1Service;
import com.haoys.xinjiang.service.PatientNaPiSearchV2Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@RestController
@Api(tags = "专病库数据库-纳排搜索条件")
@RequestMapping("/xinjiang/naPiSearch")
public class PatientNaPiSearchController extends BaseController {

    @Autowired
    private PatientNaPiSearchV1Service naPiSearchService;

    @Autowired
    private PatientNaPiSearchTreeService naPiSearchTreeService;

    @Autowired
    private PatientExportFileService patientExportFileService;

    @Autowired
    private PatientNaPiSearchV2Service naPiSearchV2Service;

    /**
     * 搜索下拉列表
     *
     * @return
     */
    @ApiOperation("纳排-搜索下拉列表")
    @GetMapping(value = "searchList")
    /**
     * diseaseType 病种
     */
    public CommonResult<List<PatientNapiSearch>> searchList() {
        return naPiSearchService.searchList();
    }



    /**
     * 纳排-列表
     *
     * @return
     */
    @ApiOperation("纳排-列表")
    @PostMapping(value = "list")
    public CommonResult list(@RequestBody SearchParam param) {
        return naPiSearchV2Service.list(param);
    }

    @ApiOperation("纳排搜索/患者病历高级检索-列表")
    @PostMapping(value = "treeSearch")
    public CommonResult<Map<String, Object>> treeSearch(@RequestBody TreeSearchParam param) throws IOException {
        return naPiSearchTreeService.list(param);
    }
    /**
     * 保存/更新搜索条件
     *
     * @return
     */
    @ApiOperation("纳排-保存/更新搜索条件")
    @PostMapping(value = "su")
    public CommonResult<Object> su(@RequestBody PatientNaPiSearchDto patientNaPiSearchDto) {
        return naPiSearchService.saveOrUpdateSearch(patientNaPiSearchDto);
    }


    /**
     * 删除搜索条件
     *
     * @return
     */
    @ApiOperation("纳排-删除搜索条件")
    @GetMapping(value = "del")
    public CommonResult<Object> del(String id) {
        return naPiSearchService.remove(id);
    }


    /**
     * 删除搜索条件
     *
     * @return
     */
    @ApiOperation("纳排-getWhere")
    @GetMapping(value = "getWhere")
    public CommonResult<String> getWhere(String id) {
        PatientNapiSearch search = naPiSearchService.getById(id);
        if (search==null){
            return CommonResult.failed();
        }
        return CommonResult.success(naPiSearchService.getWhere(search));
    }


    @ApiOperation("纳排-export")
    @PostMapping(value = "export")
    public CommonResult<String> export(@RequestBody SearchExportParam searchExportParam) {
        naPiSearchService.export(searchExportParam);
        return CommonResult.success("");
    }


    @ApiOperation("纳排-下载列表")
    @GetMapping(value = "exportList")
    public CommonResult<List<PatientExportFile>> exportList() {
        List<PatientExportFile> list = patientExportFileService.exportList();
        return CommonResult.success(list);
    }

    /**
     * 下载列表
     *
     * @return
     */
    @ApiOperation("纳排-下载删除")
    @GetMapping(value = "removeFile")
    public CommonResult<Object> removeFile(String id) {
        return patientExportFileService.removeFile(id);
    }


    /**
     * 下载列表
     *
     * @return
     */
    @ApiOperation("纳排-已下载")
    @GetMapping(value = "alreadyDown")
    public CommonResult<Object> alreadyDown(String id) {
        return patientExportFileService.alreadyDown(id);
    }
}
