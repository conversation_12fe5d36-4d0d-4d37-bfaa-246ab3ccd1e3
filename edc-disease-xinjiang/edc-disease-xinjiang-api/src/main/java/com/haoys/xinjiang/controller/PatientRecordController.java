package com.haoys.xinjiang.controller;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.xinjiang.domain.param.PatientRecordSearchParam;
import com.haoys.xinjiang.domain.vo.DemographyInformationWrapper;
import com.haoys.xinjiang.domain.vo.PatientRecordVo;
import com.haoys.xinjiang.domain.vo.PatientVisitTimeLineVo;
import com.haoys.xinjiang.domain.wrapper.PatientDataViewWrapper;
import com.haoys.xinjiang.service.PatientRecordService;
import com.haoys.xinjiang.service.PatientVariableRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@Api(tags = "专病库数据库-患者病历和视图查询")
@RequestMapping("/xinjiang/patient-base")
public class PatientRecordController extends BaseController {

    @Autowired
    private PatientRecordService patientRecordService;
    @Autowired
    private PatientVariableRecordService patientVariableRecordService;


    @ApiOperation("患者病历检索分页列表")
    @RequestMapping(value = "/getPatientMedicalRecordForPage", method = RequestMethod.POST)
    public CommonResult<PatientRecordVo> getPatientMedicalRecordForPage(@RequestBody PatientRecordSearchParam patientRecordSearchParam) {
        PatientRecordVo patientRecordVo = new PatientRecordVo();
        CommonPage<DemographyInformationWrapper> patientMedicalRecordList = patientRecordService.getPatientMedicalRecordForPage(patientRecordVo, patientRecordSearchParam);
        patientRecordVo.setPatientModelVariableRecordList(patientMedicalRecordList);
        return CommonResult.success(patientRecordVo);
    }

    @ApiOperation("患者360视图-明细")
    @GetMapping(value = "/getPatientRecordViewDetail")
    public CommonResult<PatientDataViewWrapper> getPatientRecordViewDetail(String modelSourceCode, String patientId,  String visitSn,
                                                                           @RequestParam(defaultValue = "1") Integer pageNum,
                                                                           @RequestParam(defaultValue = "10") Integer pageSize) {
        PatientDataViewWrapper patientDataViewWrapper = patientRecordService.getPatientRecordViewDetail(modelSourceCode, patientId, visitSn, pageNum, pageSize);
        return CommonResult.success(patientDataViewWrapper);
    }

    @ApiOperation("患者就诊时间轴")
    @GetMapping(value = "/getPatientVisitTimeLineRecord")
    public CommonResult<List<PatientVisitTimeLineVo>> getPatientVisitTimeLineRecord(String patientId, String visitSn) {
        List<PatientVisitTimeLineVo> patientVisitTimeLineVoList = patientRecordService.getPatientVisitTimeLineRecord(patientId, visitSn);
        return CommonResult.success(patientVisitTimeLineVoList);
    }


}
