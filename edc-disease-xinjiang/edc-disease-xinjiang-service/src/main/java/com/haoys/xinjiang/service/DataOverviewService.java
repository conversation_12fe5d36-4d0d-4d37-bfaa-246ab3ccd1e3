package com.haoys.xinjiang.service;

import com.haoys.user.common.api.CommonResult;
import com.haoys.xinjiang.domain.param.CountPatientToAgeParam;
import com.haoys.xinjiang.domain.param.CountPatientToAgeWrapperParam;
import com.haoys.xinjiang.domain.vo.CountPatientToAreaVo;
import com.haoys.xinjiang.domain.vo.CountPatientToYearVo;
import com.haoys.xinjiang.domain.vo.DataOverViewVo;

import java.util.List;
import java.util.Map;

/**
 * 数据概览服务类
 */
public interface DataOverviewService {

    /**
     * 患者统计:入库总病例人数、门诊就诊人数，住院就诊人数，患者性别比例
     * @return
     */
    CommonResult<DataOverViewVo> totalPatient();

    /**
     * 最近一年患者趋势图
     * @param year 年度
     * @return
     */
    CountPatientToYearVo countPatientToYear(String year);


    /**
     * 患者年龄段分布图
     *
     * @param params 年龄分布集合
     * @return
     */
    CommonResult<List<CountPatientToAgeParam>> countPatientToAge(CountPatientToAgeWrapperParam params);

    /**
     * 患者区域分布图
     * @return
     */
    CommonResult<List<CountPatientToAreaVo>> countPatientToAre();


    /**
     * 季节性患者就诊人数
     */
    CommonResult<DataOverViewVo.QuarterPatientCount> countQuarterPatient();
    
    CommonResult<List<Map<String, Object>>> getPatientAgeDistributionStatTable();
    
    CommonResult<List<Map<String, Object>>> getPatientMedicalTreatmentStatTable(String fieldName);
    
    CommonResult<Map<String, Object>> getPatientUseDrugStatTable();
    
    CommonResult<Map<String, Object>> getTbDrugResistantType();
}
