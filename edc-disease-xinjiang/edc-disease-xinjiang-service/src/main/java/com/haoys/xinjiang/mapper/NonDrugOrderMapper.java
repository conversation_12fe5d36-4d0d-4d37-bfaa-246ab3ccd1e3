package com.haoys.xinjiang.mapper;

import com.haoys.xinjiang.model.NonDrugOrder;
import com.haoys.xinjiang.model.NonDrugOrderExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface NonDrugOrderMapper {
    long countByExample(NonDrugOrderExample example);

    int deleteByExample(NonDrugOrderExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(NonDrugOrder record);

    int insertSelective(NonDrugOrder record);

    List<NonDrugOrder> selectByExample(NonDrugOrderExample example);

    NonDrugOrder selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") NonDrugOrder record, @Param("example") NonDrugOrderExample example);

    int updateByExample(@Param("record") NonDrugOrder record, @Param("example") NonDrugOrderExample example);

    int updateByPrimaryKeySelective(NonDrugOrder record);

    int updateByPrimaryKey(NonDrugOrder record);
}