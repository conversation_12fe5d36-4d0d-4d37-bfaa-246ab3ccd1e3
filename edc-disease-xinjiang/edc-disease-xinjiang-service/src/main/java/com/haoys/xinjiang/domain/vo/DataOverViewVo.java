package com.haoys.xinjiang.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DataOverViewVo {

    @ApiModelProperty(value = "统计入库总病例人数")
    private Long countPatient;

    @ApiModelProperty(value = "统计门诊就诊人数")
    private Long countOutpatient;

    @ApiModelProperty(value = "统计住院就诊人数")
    private Long countVisits;

    @ApiModelProperty(value = "患者性别比例统计")
    private PatientSexCount patientSexCount;

    /**
     * 患者性别比例统计
     */
    @Data
    public static class PatientSexCount{

        @ApiModelProperty(value = "男性人数")
        private Long countManPatient;

        @ApiModelProperty(value = "女性人数")
        private Long countWomanPatient;

        @ApiModelProperty(value = "男性占比")
        private BigDecimal manPatientProportion;

        @ApiModelProperty(value = "女性占比")
        private BigDecimal womanPatientProportion;
    }

    /**
     * 季节性患者就诊人数
     */
    @Data
    public static class QuarterPatientCount{

        @ApiModelProperty(value = "春")
        private Long spring ;

        @ApiModelProperty(value = "夏")
        private Long summer;

        @ApiModelProperty(value = "秋")
        private Long autumn;

        @ApiModelProperty(value = "冬")
        private Long winter;
    }

}
