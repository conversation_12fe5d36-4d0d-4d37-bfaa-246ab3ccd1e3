package com.haoys.xinjiang.domain.vo;

import com.haoys.user.common.api.CommonPage;
import com.haoys.xinjiang.domain.wrapper.PatientBaseWrapper;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class PatientMedicalRecordVo {

    @ApiModelProperty(value = "患者病历检索列表")
    private CommonPage<PatientBaseWrapper> patientModelVariableRecordList;

    @ApiModelProperty(value = "患者列表自定义标题配置")
    private List<ModelVariableHeadRowConfig> variableHeadRowList = new ArrayList<>();

    @Data
    public static class ModelVariableHeadRowConfig {

        @ApiModelProperty(value = "表单来源批次")
        private String modelSourceId;

        @ApiModelProperty(value = "表单来源code")
        private String modelSourceCode;

        @ApiModelProperty(value = "分组名称")
        private String groupName;

        @ApiModelProperty(value = "变量ID")
        private String variableId;

        @ApiModelProperty(value = "变量名称")
        private String variableName;

        @ApiModelProperty(value = "变量code")
        private String variableCode;

        @ApiModelProperty(value = "变量类型")
        private String variableType;

        @ApiModelProperty(value = "变量下拉框数据")
        private String comboboxData;

        @ApiModelProperty(value = "是否自定义表单变量")
        private Boolean customVariable;

        @ApiModelProperty(value = "默认查询字段")
        private Boolean defaultQuery = false;

        @ApiModelProperty(value = "字段排序")
        private Integer sort;
    }

}
