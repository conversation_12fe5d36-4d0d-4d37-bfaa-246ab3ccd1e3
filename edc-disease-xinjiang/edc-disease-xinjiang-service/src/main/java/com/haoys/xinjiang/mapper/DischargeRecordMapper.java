package com.haoys.xinjiang.mapper;

import com.haoys.xinjiang.model.DischargeRecord;
import com.haoys.xinjiang.model.DischargeRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DischargeRecordMapper {
    long countByExample(DischargeRecordExample example);

    int deleteByExample(DischargeRecordExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(DischargeRecord record);

    int insertSelective(DischargeRecord record);

    List<DischargeRecord> selectByExample(DischargeRecordExample example);

    DischargeRecord selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") DischargeRecord record, @Param("example") DischargeRecordExample example);

    int updateByExample(@Param("record") DischargeRecord record, @Param("example") DischargeRecordExample example);

    int updateByPrimaryKeySelective(DischargeRecord record);

    int updateByPrimaryKey(DischargeRecord record);
}