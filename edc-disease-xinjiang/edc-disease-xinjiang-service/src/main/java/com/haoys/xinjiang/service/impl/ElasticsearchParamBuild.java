package com.haoys.xinjiang.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch._types.query_dsl.NestedQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.RangeQuery;
import co.elastic.clients.json.JsonData;
import com.haoys.user.common.constants.DefineConstant;
import com.haoys.user.elasticsearch.SearchDto;
import com.haoys.xinjiang.domain.emums.PatientNaPiSearchEnum;

public class ElasticsearchParamBuild {

    /**
     * 构建嵌套查询条件
     * @param builder
     * @param searchDto 搜索条件
     * @param addPath 是否添加path
     */
    public static void builderPath(NestedQuery.Builder builder, SearchDto searchDto, boolean addPath) {
        String formCode = StrUtil.toCamelCase(searchDto.getFormCode());
        if (addPath){
            builder.path(formCode);
        }
        final String filed = formCode + "." + StrUtil.toCamelCase(searchDto.getFieldCode());
        extracted(builder, searchDto, filed);
    }

    /**
     * 构建嵌套查询条件
     * @param builder
     * @param searchDto 搜索条件
     */
    public static void builder(NestedQuery.Builder builder, SearchDto searchDto) {
        String formCode = StrUtil.toCamelCase(searchDto.getFormCode());
        builder.path(formCode);
        final String filed = formCode + "." + StrUtil.toCamelCase(searchDto.getFieldCode());
        extracted(builder, searchDto, filed);
    }


    /**
     * 构建嵌套查询条件
     * @param builder
     */
    public static void builder(NestedQuery.Builder builder, String path,String filed, String searchValue) {
        String formCode = StrUtil.toCamelCase(path);
        builder.path(formCode);
        searchLike(builder, filed,searchValue);
    }


    private static void extracted(NestedQuery.Builder builder, SearchDto searchDto, String filed) {
        if (DefineConstant.CONTAINS_CODE.equals(searchDto.getValueType())) {
            // 包含搜索条件构建
            searchLike(searchDto, builder, filed);
        } else if (DefineConstant.NOT_CONTAINS_CODE.equals(searchDto.getValueType())) {
            // 不包含搜索条件构建
            searchNotLike(searchDto, builder, filed);
        } else if (DefineConstant.LESS_THAN_CODE.equals(searchDto.getValueType())) {
            // 小于搜索条件构建
            searchLt(searchDto, builder, filed);
        } else if (DefineConstant.GREATER_THAN_CODE.equals(searchDto.getValueType())) {
            // 大于搜索条件构建
            searchGt(searchDto, builder, filed);
        } else if (DefineConstant.EQUAL_CODE.equals(searchDto.getValueType())) {
            // 精确匹配搜索条件构建
            searchEqual(searchDto, filed, builder);
        } else if (DefineConstant.NOT_EQUAL_CODE.equals(searchDto.getValueType())) {
            // 不等于精确匹配搜索条件构建
            searchNotEqual(searchDto, filed, builder);
        } else if (DefineConstant.LESS_THAN_EQUAL_CODE.equals(searchDto.getValueType())) {
            // 小于等于搜索条件构建
            searchLte(searchDto, builder, filed);
        } else if (DefineConstant.GREATER_THAN_EQUAL_CODE.equals(searchDto.getValueType())) {
            // 大于等于搜索条件构建
            searchGte(searchDto, builder, filed);
        }
    }



    /**
     * 大于等于搜索条件构建
     * @param searchDto
     * @param builder
     * @param filed
     */
    public static void searchGte(SearchDto searchDto, NestedQuery.Builder builder, String filed) {
        if (PatientNaPiSearchEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool.must(gte(searchDto,filed))));
        } else if (PatientNaPiSearchEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool.should(gte(searchDto,filed))));
        } else if (PatientNaPiSearchEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool.mustNot(gte(searchDto,filed))));
        }
    }


    /**
     * 大于等于
     * @param searchDto
     * @param filed
     */
    public static Query gte(SearchDto searchDto, String filed) {
        RangeQuery.Builder query = new RangeQuery.Builder();
        query.field(filed).gte(JsonData.of(searchDto.getSearchValue()));
        String dataFormat = formatDateType(searchDto.getFormatType());
        if (StrUtil.isNotBlank(dataFormat)){
            query.format(dataFormat);
        }
        return Query.of(queryBuilder -> queryBuilder.range(query.build()));
    }


    /**
     * 小于等于搜索条件构建
     * @param searchDto
     * @param builder
     * @param filed
     */
    public static void searchLte(SearchDto searchDto, NestedQuery.Builder builder, String filed) {
        if (PatientNaPiSearchEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool.must(lte(searchDto,filed))));
        } else if (PatientNaPiSearchEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool.should(lte(searchDto,filed))));
        } else if (PatientNaPiSearchEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool.mustNot(lte(searchDto,filed))));
        }
    }


    /**
     * 小于等于
     * @param searchDto
     * @param filed
     */
    public static Query lte(SearchDto searchDto, String filed) {
        RangeQuery.Builder query = new RangeQuery.Builder();
        query.field(filed).lte(JsonData.of(searchDto.getSearchValue()));
        String dataFormat = formatDateType(searchDto.getFormatType());
        if (StrUtil.isNotBlank(dataFormat)){
            query.format(dataFormat);
        }
        return Query.of(queryBuilder -> queryBuilder.range(query.build()));
    }


    /**
     * 不等于搜索条件构建
     * @param searchDto
     * @param filed
     * @param builder
     */
    public static void searchNotEqual(SearchDto searchDto, String filed, NestedQuery.Builder builder) {
        String dataFormat = formatDateType(searchDto.getFormatType());
        if (StrUtil.isNotBlank(dataFormat)){
            // 是日期类型
            searchNotEQDate(searchDto,builder,filed);
            return;
        }
        if (PatientNaPiSearchEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool .mustNot(eq(searchDto,filed))));
        } else if (PatientNaPiSearchEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool .should(eq(searchDto,filed))));
        } else if (PatientNaPiSearchEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool .must(eq(searchDto,filed))));
        }
    }

    /**
     * 针对日期类型的搜索条件进行处理
     * @param searchDto
     * @param builder
     * @param filed
     */
    public static void searchEQDate(SearchDto searchDto, NestedQuery.Builder builder, String filed) {
        if (PatientNaPiSearchEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool .must(eqdate(searchDto,filed))));
        } else if (PatientNaPiSearchEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool .should(eqdate(searchDto,filed))));
        } else if (PatientNaPiSearchEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool .mustNot(eqdate(searchDto,filed))));
        }
    }


    /**
     * 针对日期类型的搜索条件进行处理
     * @param searchDto
     * @param builder
     * @param filed
     */
    public static void searchNotEQDate(SearchDto searchDto, NestedQuery.Builder builder, String filed) {
        if (PatientNaPiSearchEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool .mustNot(eqdate(searchDto,filed))));
        } else if (PatientNaPiSearchEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool .should(eqdate(searchDto,filed))));
        } else if (PatientNaPiSearchEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool .must(eqdate(searchDto,filed))));
        }
    }

    /**
     * 针对日期类型的搜索条件进行处理
     * @param searchDto
     * @param filed
     */
    public static Query eqdate(SearchDto searchDto, String filed) {
        RangeQuery.Builder rangeQuery = new RangeQuery.Builder();
        String formatType = searchDto.getFormatType();
        if (StrUtil.isNotBlank(formatType)){
            if (formatType.contains("d")) {
                // 获取某一天
                rangeQuery.field(filed).gte(JsonData.of(searchDto.getSearchValue())).lte(JsonData.of(searchDto.getSearchValue()));
            }else if (formatType.contains("m")) {
                // 获取当月的第一天
                String startDate = DateUtil.format(DateUtil.beginOfMonth(DateUtil.parse(searchDto.getSearchValue() + "-01")), "yyyy-MM-dd");
                // 获取当月的最后一天
                String endDate = DateUtil.format(DateUtil.endOfMonth(DateUtil.parse(searchDto.getSearchValue() + "-01")), "yyyy-MM-dd");

                rangeQuery.field(filed).gte(JsonData.of(startDate)).lte(JsonData.of(endDate));

            }else if (formatType.contains("Y")) {
                // 获取当年的第一天
                String startDate = DateUtil.format(DateUtil.beginOfYear(DateUtil.parse(searchDto.getSearchValue() + "-01-01")), "yyyy-MM-dd");
                // 获取当年的最后一天
                String endDate = DateUtil.format(DateUtil.endOfYear(DateUtil.parse(searchDto.getSearchValue() + "-01-01")), "yyyy-MM-dd");
                rangeQuery.field(filed).gte(JsonData.of(startDate)).lte(JsonData.of(endDate));

            }
        }
        rangeQuery.format("yyyy-MM-dd");
        return Query.of(q->q.range(rangeQuery.build()));
    }


    /**
     * 精确匹配搜索条件构建
     *
     * @param searchDto
     * @param filed
     * @param builder
     */
    public static void searchEqual(SearchDto searchDto, String filed, NestedQuery.Builder builder) {
        String dataFormat = formatDateType(searchDto.getFormatType());
        if (StrUtil.isNotBlank(dataFormat)){
            // 是日期类型
            searchEQDate(searchDto,builder,filed);
            return;
        }

        if (PatientNaPiSearchEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool .must(eq(searchDto,filed))));
        } else if (PatientNaPiSearchEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool .should(eq(searchDto,filed))));
        } else if (PatientNaPiSearchEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool .mustNot(eq(searchDto,filed))));
        }
    }


    /**
     * 等于
     *
     * @param searchDto
     * @param filed
     */
    public static Query eq(SearchDto searchDto, String filed) {
        String filedValue = filed;
        if ("varchar".equals(searchDto.getVariableType())
                || "date".equals(searchDto.getVariableType())
                || "timestamp".equals(searchDto.getVariableType())) {
            filedValue += ".keyword";
        }
        String finalFiledValue = filedValue;
        return Query.of(queryBuilder -> queryBuilder.term(w -> w.field(finalFiledValue).value(searchDto.getSearchValue())));

    }

    /**
     * 大于搜索条件构建
     * @param searchDto
     * @param builder
     * @param filed
     */
    public static void searchGt(SearchDto searchDto, NestedQuery.Builder builder, String filed) {
        if (PatientNaPiSearchEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool.must(gt(searchDto,filed))));
        } else if (PatientNaPiSearchEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool.should(gt(searchDto,filed))));
        } else if (PatientNaPiSearchEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool.mustNot(gt(searchDto,filed))));
        }
    }

    /**
     * 大于
     * @param searchDto
     * @param filed
     */
    public static Query gt(SearchDto searchDto, String filed) {
        RangeQuery.Builder query = new RangeQuery.Builder();
        query.field(filed).gt(JsonData.of(searchDto.getSearchValue()));
        String dataFormat = formatDateType(searchDto.getFormatType());
        if (StrUtil.isNotBlank(dataFormat)){
            query.format(dataFormat);
        }
        return Query.of(queryBuilder -> queryBuilder.range(query.build()));
    }

    /**
     * 小于搜索条件构建
     * @param searchDto
     * @param builder
     * @param filed
     */
    public static void searchLt(SearchDto searchDto, NestedQuery.Builder builder, String filed) {
        if (PatientNaPiSearchEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool.must(lt(searchDto,filed))));
        } else if (PatientNaPiSearchEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool.should(lt(searchDto,filed))));
        } else if (PatientNaPiSearchEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool.mustNot(lt(searchDto,filed))));
        }
    }

    /**
     * 小于
     * @param searchDto
     * @param filed
     */
    public static Query lt(SearchDto searchDto, String filed) {
        RangeQuery.Builder query = new RangeQuery.Builder();
        query.field(filed).lt(JsonData.of(searchDto.getSearchValue()));
        String dataFormat = formatDateType(searchDto.getFormatType());
        if (StrUtil.isNotBlank(dataFormat)){
            query.format(dataFormat);
        }
        return Query.of(queryBuilder -> queryBuilder.range(query.build()));
    }



    /**
     * 不包含搜索条件构建
     * @param searchDto
     * @param builder
     * @param filed
     */
    public static void searchNotLike(SearchDto searchDto, NestedQuery.Builder builder, String filed) {
        if (PatientNaPiSearchEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool .mustNot(like(filed,searchDto.getSearchValue()))));
        } else if (PatientNaPiSearchEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool.should(like(filed,searchDto.getSearchValue()))));
        } else if (PatientNaPiSearchEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool.must(like(filed,searchDto.getSearchValue()))));
        }

    }

    /**
     * 包含搜索条件构建
     *
     * @param searchDto
     * @param builder
     * @param filed
     */
    public static void searchLike(SearchDto searchDto, NestedQuery.Builder builder, String filed) {
        if (PatientNaPiSearchEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool.must(like( filed,searchDto.getSearchValue()))));
        } else if (PatientNaPiSearchEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool.should(like(filed,searchDto.getSearchValue()))));
        } else if (PatientNaPiSearchEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool .mustNot(like(filed,searchDto.getSearchValue()))));
        }
    }

    /**
     * 包含搜索条件构建
     *
     * @param builder
     * @param filed
     */
    public static void searchLike( NestedQuery.Builder builder, String filed,String value) {
        builder.query(nestedQuery -> nestedQuery.bool(bool -> bool.must(like( filed,value))));
    }


    /**
     * 包含
     *
     * @param filed
     */
    public static Query like(String filed,String value) {
        return Query.of(queryBuilder -> queryBuilder.wildcard(w -> w.field(filed).wildcard("*"+value+"*")));
    }


    /**
     * 格式化日期类型
     * @param formatType
     * @return
     */
    public static String formatDateType(String formatType) {
        if (StrUtil.isNotBlank(formatType)) {
            if (formatType.indexOf("d") > 0) {
                return "yyyy-MM-dd";
            } else if (formatType.indexOf("m") > 0) {
                return "yyyy-MM";
            } else if(formatType.indexOf("Y") > 0){
                return "yyyy";
            }
        }
        return "";
    }

}
