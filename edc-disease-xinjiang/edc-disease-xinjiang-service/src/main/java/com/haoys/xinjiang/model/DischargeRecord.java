package com.haoys.xinjiang.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class DischargeRecord implements Serializable {
    private Integer id;

    @ApiModelProperty(value = "入院日期")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date admissionDatetime;

    @ApiModelProperty(value = "出院日期")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dischargeDatetime;

    @ApiModelProperty(value = "入院情况")
    private String admissionStatus;

    @ApiModelProperty(value = "诊疗过程描述")
    private String treatProcess;

    @ApiModelProperty(value = "出院情况")
    private String dischargeStatus;

    @ApiModelProperty(value = "出院医嘱")
    private String dischargeOrder;

    @ApiModelProperty(value = "患者唯一标识")
    private String patientSn;

    @ApiModelProperty(value = "就诊唯一标识")
    private String visitSn;

    @ApiModelProperty(value = "pkid")
    private String pkid;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getAdmissionDatetime() {
        return admissionDatetime;
    }

    public void setAdmissionDatetime(Date admissionDatetime) {
        this.admissionDatetime = admissionDatetime;
    }

    public Date getDischargeDatetime() {
        return dischargeDatetime;
    }

    public void setDischargeDatetime(Date dischargeDatetime) {
        this.dischargeDatetime = dischargeDatetime;
    }

    public String getAdmissionStatus() {
        return admissionStatus;
    }

    public void setAdmissionStatus(String admissionStatus) {
        this.admissionStatus = admissionStatus;
    }

    public String getTreatProcess() {
        return treatProcess;
    }

    public void setTreatProcess(String treatProcess) {
        this.treatProcess = treatProcess;
    }

    public String getDischargeStatus() {
        return dischargeStatus;
    }

    public void setDischargeStatus(String dischargeStatus) {
        this.dischargeStatus = dischargeStatus;
    }

    public String getDischargeOrder() {
        return dischargeOrder;
    }

    public void setDischargeOrder(String dischargeOrder) {
        this.dischargeOrder = dischargeOrder;
    }

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }

    public String getVisitSn() {
        return visitSn;
    }

    public void setVisitSn(String visitSn) {
        this.visitSn = visitSn;
    }

    public String getPkid() {
        return pkid;
    }

    public void setPkid(String pkid) {
        this.pkid = pkid;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", admissionDatetime=").append(admissionDatetime);
        sb.append(", dischargeDatetime=").append(dischargeDatetime);
        sb.append(", admissionStatus=").append(admissionStatus);
        sb.append(", treatProcess=").append(treatProcess);
        sb.append(", dischargeStatus=").append(dischargeStatus);
        sb.append(", dischargeOrder=").append(dischargeOrder);
        sb.append(", patientSn=").append(patientSn);
        sb.append(", visitSn=").append(visitSn);
        sb.append(", pkid=").append(pkid);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}