package com.haoys.xinjiang.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.mapping.DateProperty;
import co.elastic.clients.elasticsearch._types.mapping.IntegerNumberProperty;
import co.elastic.clients.elasticsearch._types.mapping.KeywordProperty;
import co.elastic.clients.elasticsearch._types.mapping.Property;
import co.elastic.clients.elasticsearch._types.mapping.TextProperty;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.NestedQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.CountRequest;
import co.elastic.clients.elasticsearch.core.CountResponse;
import co.elastic.clients.elasticsearch.core.GetResponse;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.indices.CreateIndexResponse;
import co.elastic.clients.json.JsonData;
import co.elastic.clients.transport.endpoints.BooleanResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.constants.CommomDataModelConstants;
import com.haoys.user.common.constants.DefineConstant;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.common.sql.HumpToLineUtil;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.DateUtil;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.config.PatientXinjiangDiseaseCenterConfig;
import com.haoys.user.elasticsearch.ElasticSearchService;
import com.haoys.user.exception.ServiceException;
import com.haoys.xinjiang.ConstantsModel;
import com.haoys.xinjiang.domain.param.PatientDiseaseSearchParam;
import com.haoys.xinjiang.domain.param.PatientRecordSearchParam;
import com.haoys.xinjiang.domain.vo.DemographyInformationWrapper;
import com.haoys.xinjiang.domain.vo.PatientModelDefineVo;
import com.haoys.xinjiang.domain.vo.PatientModelVariableVo;
import com.haoys.xinjiang.domain.vo.PatientRecordVo;
import com.haoys.xinjiang.domain.vo.PatientVisitTimeLineVo;
import com.haoys.xinjiang.domain.wrapper.PatientDataViewWrapper;
import com.haoys.xinjiang.mapper.CcInfoMapper;
import com.haoys.xinjiang.mapper.DemographyInformationMapper;
import com.haoys.xinjiang.mapper.HormoneTherapyInfoMapper;
import com.haoys.xinjiang.mapper.IsaTherapyInfoMapper;
import com.haoys.xinjiang.mapper.PatientModelVariableMapper;
import com.haoys.xinjiang.mapper.PatientNapiSearchMapper;
import com.haoys.xinjiang.mapper.VisitInformationMapper;
import com.haoys.xinjiang.model.CcInfo;
import com.haoys.xinjiang.model.CcInfoExample;
import com.haoys.xinjiang.model.DemographyInformation;
import com.haoys.xinjiang.model.DemographyInformationExample;
import com.haoys.xinjiang.model.HormoneTherapyInfo;
import com.haoys.xinjiang.model.HormoneTherapyInfoExample;
import com.haoys.xinjiang.model.IsaTherapyInfo;
import com.haoys.xinjiang.model.IsaTherapyInfoExample;
import com.haoys.xinjiang.model.PatientAnalysisDataset;
import com.haoys.xinjiang.model.PatientModelDefine;
import com.haoys.xinjiang.model.PatientModelVariable;
import com.haoys.xinjiang.model.PatientModelVariableExample;
import com.haoys.xinjiang.service.PatientAnalysisDataService;
import com.haoys.xinjiang.service.PatientModelDefineService;
import com.haoys.xinjiang.service.PatientRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@DS("disease_xinjiang")
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class PatientRecordServiceImpl extends BaseService implements PatientRecordService {

    private final DemographyInformationMapper demographyInformationMapper;
    private final VisitInformationMapper visitInformationMapper;
    private final PatientModelVariableMapper patientModelVariableMapper;
    private final PatientAnalysisDataService patientAnalysisDataService;
    private final PatientNapiSearchMapper patientNaPiSearchMapper;
    private final PatientXinjiangDiseaseCenterConfig patientXinjiangDiseaseCenterConfig;
    private final ElasticSearchService elasticSearchService;
    private final PatientModelDefineService patientModelDefineService;
    private final CcInfoMapper ccInfoMapper;
    private final HormoneTherapyInfoMapper hormoneTherapyInfoMapper;
    private final IsaTherapyInfoMapper isaTherapyInfoMapper;
    private final RedisTemplateService redisTemplateService;

    @Resource(name = "clientByApiKey")
    private ElasticsearchClient elasticsearchClient;

    @Override
    public CommonPage<DemographyInformationWrapper> getPatientMedicalRecordForPage(PatientRecordVo patientRecordVo, PatientRecordSearchParam patientRecordSearchParam) {
        Page<Object> page = PageHelper.startPage(patientRecordSearchParam.getPageNum(), patientRecordSearchParam.getPageSize());
        if (StringUtils.isBlank(patientRecordSearchParam.getSortField())) {
            patientRecordSearchParam.setSortField(CommomDataModelConstants.COMMON_SORT_FIELD);
        }
        if (StringUtils.isBlank(patientRecordSearchParam.getSortType())) {
            patientRecordSearchParam.setSortType(CommomDataModelConstants.COMMON_SORT_TYPE_ASC);
        }
        String dataSetId = patientRecordSearchParam.getDataSetId();
        if (StringUtils.isNotEmpty(dataSetId)) {
            PatientAnalysisDataset patientAnalysisDataset = patientAnalysisDataService.selectByPrimaryKey(dataSetId);
            if (patientAnalysisDataset != null) {
                return getDataSetList(patientRecordVo, patientRecordSearchParam);
            }
        }
        List<Map<String, Object>> patientMedicalRecordList = getPatientsRecordElasticDataSource(patientRecordSearchParam);
        page.setTotal(patientRecordSearchParam.getTotalCount());
        page.setPageSize(patientRecordSearchParam.getPageSize());
        page.setPageNum(patientRecordSearchParam.getPageNum());
        return commonPageListWrapper(patientRecordSearchParam.getPageNum(), patientRecordSearchParam.getPageSize(), page, patientMedicalRecordList);
    }

    private CommonPage<DemographyInformationWrapper> getDataSetList(PatientRecordVo patientRecordVo, PatientRecordSearchParam patientRecordSearchParam) {
        if (StringUtils.isNotEmpty(patientRecordSearchParam.getDataBaseId())) {
            PatientAnalysisDataset dataset = patientAnalysisDataService.selectByPrimaryKey(patientRecordSearchParam.getDataSetId());
            if (dataset != null) {

                List<PatientRecordVo.ModelVariableHeadRowConfig> modelVariableHeadRowConfigList = new ArrayList<>();
                List<PatientModelVariableVo> head = patientModelDefineService.getPatientDefaultModelVariableConfig(patientRecordSearchParam.getModelSourceCode());
                for (PatientModelVariableVo patientModelVariable : head) {
                    PatientRecordVo.ModelVariableHeadRowConfig modelVariableHeadRowConfig = new PatientRecordVo.ModelVariableHeadRowConfig();
                    BeanUtils.copyProperties(patientModelVariable, modelVariableHeadRowConfig);
                    modelVariableHeadRowConfig.setVariableCode(patientModelVariable.getVariableCode());
                    if (patientRecordSearchParam.getExportPatientRecord()) {
                        modelVariableHeadRowConfig.setVariableName(patientModelVariable.getVariableName());
                    }
                    modelVariableHeadRowConfigList.add(modelVariableHeadRowConfig);
                }
                patientRecordVo.setVariableHeadRowList(modelVariableHeadRowConfigList);
                // 根据搜索id获取纳排搜索信息
                String unique = " and patients.patient_sn in (select patient_sn from patient_analysis_record where dataset_id='" + dataset.getId() + "' )";
                // 获取要查询的表单
                Set<String> formList = new HashSet<>();
                // 处理展示条件中的表单信息
                // 构建group by 条件
                StringBuffer groupBy = new StringBuffer(" group by patients.patient_sn,patients.gender,patients.date_of_birth ");
                StringBuffer filed = new StringBuffer();
                filed.append(" DISTINCT on ( patients.patient_sn ) patients.patient_sn, ");
                filed.append(" patients.gender,patients.date_of_birth");
                if (CollectionUtil.isNotEmpty(head)) {
                    for (PatientModelVariableVo field : head) {
                        if (!"patient_sn".equals(field.getModelSourceCode())) {
                            formList.add(field.getModelSourceCode());
                        }
                        filed.append(",").append(field.getModelSourceCode()).append(".").append(field.getVariableCode());
                        groupBy.append(",").append(field.getModelSourceCode()).append(".").append(field.getVariableCode());
                    }
                }
                // 构建join表的查询语句
                StringBuffer join = new StringBuffer();
                formList.forEach(form -> {
                    if (!"patients".equals(form)) {
                        join.append(" left join ").append(form).append(" on ").append(form).append(".").append("patient_sn").append("=").append("patients.patient_sn ");
                    }
                });
                String limit = " LIMIT " + patientRecordSearchParam.getPageSize() + " OFFSET " + (patientRecordSearchParam.getPageNum() - 1) * patientRecordSearchParam.getPageSize();
                // 构建查询列表语句
                StringBuffer sql = new StringBuffer();
                sql.append("select ").append(filed).append(" from patients ").append(join).append("where 1=1 ").append(unique).append(limit);
                // 构建查询总条数语句
                List<Map<String, Object>> list = patientNaPiSearchMapper.selectList(sql.toString());


                // 构建查询总条数语句
                StringBuffer countSql = new StringBuffer();
                countSql.append("select count(0) from ( ").append("select DISTINCT on ( patients.patient_sn ) patients.patient_sn from patients ").append(join).append("where 1=1 ").append(unique).append(") m");
                long count = patientNaPiSearchMapper.count(countSql.toString());
                CommonPage commonPage = commonPageListWrapper(patientRecordSearchParam.getPageNum(), patientRecordSearchParam.getPageSize(), new Page<>(), new ArrayList<>());
                commonPage.setTotal(count);
                commonPage.setPageNum(patientRecordSearchParam.getPageNum());
                commonPage.setPageSize(patientRecordSearchParam.getPageSize());
                commonPage.setList(list);
                return commonPage;

            }
        }
        return null;
    }

    private List<Map<String, Object>> getPatientsRecordElasticDataSource(PatientRecordSearchParam patientRecordSearchParam) {
        if (StringUtils.isBlank(patientRecordSearchParam.getSortField())) {
            patientRecordSearchParam.setSortField(CommomDataModelConstants.COMMON_SORT_FIELD);
        }
        if (StringUtils.isBlank(patientRecordSearchParam.getSortType())) {
            patientRecordSearchParam.setSortType(CommomDataModelConstants.COMMON_SORT_TYPE_ASC);
        }
        String startDate = patientRecordSearchParam.getStartDate();
        String endDate = patientRecordSearchParam.getEndDate();
        if (CommomDataModelConstants.PATIENT_QUERY_SEGMENT_0.equals(patientRecordSearchParam.getQuerySegment())) {
            startDate = "now-10y";
            endDate = "now";
        }
        if (CommomDataModelConstants.PATIENT_QUERY_SEGMENT_1.equals(patientRecordSearchParam.getQuerySegment())) {
            startDate = "now-1y";
            endDate = "now";
        }
        if (CommomDataModelConstants.PATIENT_QUERY_SEGMENT_3.equals(patientRecordSearchParam.getQuerySegment())) {
            startDate = "now-3y";
            endDate = "now";
        }
        if (CommomDataModelConstants.PATIENT_QUERY_SEGMENT_5.equals(patientRecordSearchParam.getQuerySegment())) {
            startDate = "now-5y";
            endDate = "now";
        }
        // 高级检索
        if (CommomDataModelConstants.PATIENT_SEARCH_TYPE_3.equals(patientRecordSearchParam.getSearchType())) {
            patientRecordSearchParam.setStartDate(startDate);
            patientRecordSearchParam.setEndDate(endDate);
            List<Map<String, Object>> patientDataMapList = getPatientsRecordAdvancedSearchElasticDataSource(patientRecordSearchParam, null);
            return patientDataMapList;
        }
        String searchWord = "\"" + patientRecordSearchParam.getSearchWord() + "\"";
        String finalStartDate = startDate;
        String finalEndDate = endDate;
        SearchRequest request;
        CountRequest countRequest;
        // 全文搜索
        if (StringUtils.isEmpty(patientRecordSearchParam.getModelSourceCode())) {
            String searchIndexName = getSearchIndexName(patientRecordSearchParam);
            if (StringUtils.isEmpty(patientRecordSearchParam.getSearchWord())) {
                request = SearchRequest.of(i -> {
                    SearchRequest.Builder builder = i.index(searchIndexName).from((patientRecordSearchParam.getPageNum() - 1) * patientRecordSearchParam.getPageSize()).size(patientRecordSearchParam.getPageSize());
                    builder.sort(s -> s.field(f -> f.field(patientRecordSearchParam.getSortField()).order(SortOrder.Asc)));
                    builder.query(q -> q.bool(b -> b.must(m -> m.matchAll(matchAll -> matchAll.boost(1.0f))).must(m -> m.range(qs -> qs.field("visitInformation.visitOrAdmissionDatetime").gte(JsonData.of(finalStartDate)).lte(JsonData.of(finalEndDate))))));
                    builder.source(s -> s.filter(f -> f.includes("demographyInformation", "visitInformation")));
                    return builder;
                });
                countRequest = CountRequest.of(i -> {
                    CountRequest.Builder builder = i.index(searchIndexName);
                    builder.query(q -> q.bool(b -> b.must(m -> m.matchAll(matchAll -> matchAll.boost(1.0f))).must(m -> m.range(qs -> qs.field("visitInformation.visitOrAdmissionDatetime").gte(JsonData.of(finalStartDate)).lte(JsonData.of(finalEndDate))))));
                    return builder;
                });
            } else {
                request = SearchRequest.of(i -> {
                    SearchRequest.Builder builder = i.index(searchIndexName).from((patientRecordSearchParam.getPageNum() - 1) * patientRecordSearchParam.getPageSize()).size(patientRecordSearchParam.getPageSize());
                    builder.sort(s -> s.field(f -> f.field(patientRecordSearchParam.getSortField()).order(SortOrder.Asc)));
                    builder.highlight(h -> h.fields("*", f -> f.preTags("<font color='red'>").postTags("</font>")));
                    builder.query(q -> q.bool(b -> b.must(m -> m.queryString(qs -> qs.query(searchWord).analyzer("ik_max_word"))).must(m -> m.range(qs -> qs.field("visitInformation.visitOrAdmissionDatetime").gte(JsonData.of(finalStartDate)).lte(JsonData.of(finalEndDate))))));
                    builder.source(s -> s.filter(f -> f.includes("demographyInformation", "visitInformation")));
                    return builder;
                });
                countRequest = CountRequest.of(i -> {
                    CountRequest.Builder builder = i.index(searchIndexName);
                    builder.query(q -> q.bool(b -> b.must(m -> m.queryString(qs -> qs.query(searchWord).analyzer("ik_max_word"))).must(m -> m.range(qs -> qs.field("visitInformation.visitOrAdmissionDatetime").gte(JsonData.of(finalStartDate)).lte(JsonData.of(finalEndDate))))));
                    return builder;
                });
            }
        } else {
            // 多个modelSourceCode查询
            request = SearchRequest.of(i -> {
                SearchRequest.Builder builder = i.index(patientXinjiangDiseaseCenterConfig.getPatient_join_visit_index())
                        .from((patientRecordSearchParam.getPageNum() - 1) * patientRecordSearchParam.getPageSize())
                        .size(patientRecordSearchParam.getPageSize());
                String modelSourceCode = HumpToLineUtil.lineToHump(patientRecordSearchParam.getModelSourceCode());
                String searchView = patientRecordSearchParam.getSearchView();
                multiModelSourceCodeNestedBuilderQuery(builder, searchView, modelSourceCode, searchWord, finalStartDate, finalEndDate);
                builder.highlight(h -> h.fields("*", f -> f.preTags("<font color='red'>").postTags("</font>")));
                builder.source(s -> s.filter(f -> f.includes("demographyInformation", "visitInformation")));
                return builder;
            });
            countRequest = CountRequest.of(i -> {
                CountRequest.Builder builder = i.index(patientXinjiangDiseaseCenterConfig.getPatient_join_visit_index());
                String modelSourceCode = HumpToLineUtil.lineToHump(patientRecordSearchParam.getModelSourceCode());
                String searchView = patientRecordSearchParam.getSearchView();
                multiModelSourceCodeCountNestedBuilderQuery(builder, searchView, modelSourceCode, searchWord, finalStartDate, finalEndDate);
                return builder;
            });
        }
        SearchResponse<JSONObject> response;
        try {
            log.info("PatientsRecordElasticDataSource request params: {}, searchView: {}", request.toString(), patientRecordSearchParam.getSearchView());
            response = elasticsearchClient.search(request, JSONObject.class);
            try {
                CountResponse count = elasticsearchClient.count(countRequest);
                log.info("PatientsRecordElasticDataSource count response: {}, countRequest: {}", count.count(), countRequest.toString());
                patientRecordSearchParam.setTotalCount(count.count());
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        ArrayList<Map<String, Object>> objectArrayList = new ArrayList<>();
        List<Hit<JSONObject>> hits = response.hits().hits();
        /*try {
            getPatientRecordForEsHitsCount(patientRecordSearchParam);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }*/
        if (CollectionUtil.isNotEmpty(hits)) {
            for (Hit<JSONObject> hit : hits) {
                Map<String, Object> dataMap = JSONObject.parseObject(hit.source().toJSONString(), new TypeReference<Map<String, Object>>(){});
                Object visitInformation = dataMap.get("visitInformation");
                if (visitInformation instanceof JSONArray) {
                    JSONArray visitInformationList = (JSONArray) visitInformation;
                    visitInformationList.sort(Comparator.comparing(obj -> ((JSONObject) obj).getDate("visitOrAdmissionDatetime"), Comparator.naturalOrder()));
                    dataMap.put("visitInformation", visitInformationList);
                }
                Object demographyInformation = dataMap.get("demographyInformation");
                if (demographyInformation instanceof JSONObject) {
                    JSONObject demographyInformationObject = (JSONObject) demographyInformation;
                    DemographyInformation demographyInformationData = JSON.parseObject(demographyInformation.toString(), DemographyInformation.class);
                    Set<Map.Entry<String, Object>> entrySet = demographyInformationObject.entrySet();
                    for (Map.Entry<String, Object> entry : entrySet) {
                        String entryKey = entry.getKey();
                        if(ConstantsModel.PATIENT_NAME.equals(entryKey)){
                            demographyInformationData.setName(DesensitizedUtil.chineseName(demographyInformationData.getName()));
                        }
                        if(ConstantsModel.PATIENT_CONTACT_PHONE.equals(entryKey)){
                            demographyInformationData.setContactPhone(DesensitizedUtil.mobilePhone(demographyInformationData.getContactPhone()));
                        }
                        if(ConstantsModel.PATIENT_ID_NO.equals(entryKey)){
                            demographyInformationData.setIdNo(DesensitizedUtil.idCardNum(demographyInformationData.getIdNo(), 1, 2));
                        }
                        if(ConstantsModel.PATIENT_HOME_ADDRESS.equals(entryKey)){
                            demographyInformationData.setHomeAddress(DesensitizedUtil.address(demographyInformationData.getHomeAddress(), 10));
                        }
                    }
                    dataMap.put("demographyInformation", demographyInformationData);
                }
                objectArrayList.add(dataMap);
            }
            return objectArrayList;
        }
        return new ArrayList<>();
    }

    private void getPatientRecordForEsHitsCount(PatientRecordSearchParam patientMedicalRecordSearchParam) throws IOException {
        if(CommomDataModelConstants.PATIENT_VIEW.equals(patientMedicalRecordSearchParam.getSearchView())){
            CountResponse count = elasticsearchClient.count(builder -> builder.index(getSearchIndexName(patientMedicalRecordSearchParam)));
            patientMedicalRecordSearchParam.setTotalCount(count.count());
        }
        if(CommomDataModelConstants.VISIT_VIEW.equals(patientMedicalRecordSearchParam.getSearchView())){
            CountResponse count = elasticsearchClient.count(builder -> builder.index(patientXinjiangDiseaseCenterConfig.getPatient_join_visit_index()));
            patientMedicalRecordSearchParam.setTotalCount(count.count());
        }
    }


    private String getSearchIndexName(PatientRecordSearchParam rdrDataCenterConfig) {
        String searchIndexName = patientXinjiangDiseaseCenterConfig.getPatient_full_text_index();
        String searchView = rdrDataCenterConfig.getSearchView();
        if(CommomDataModelConstants.PATIENT_VIEW.equals(searchView)){
            searchIndexName = patientXinjiangDiseaseCenterConfig.getPatient_full_text_index();
        }
        if(CommomDataModelConstants.VISIT_VIEW.equals(searchView)){
            searchIndexName = patientXinjiangDiseaseCenterConfig.getPatient_visit_full_text_index();
        }
        return searchIndexName;
    }

    /**
     * 多表嵌套查询
     *
     * @param searchBuilder
     * @param searchView
     * @param modelSourceCodeValue
     * @param searchWord
     * @param finalStartDate
     * @param finalEndDate
     * @return
     */
    private static SearchRequest.Builder multiModelSourceCodeNestedBuilderQuery(SearchRequest.Builder searchBuilder, String searchView, String modelSourceCodeValue, String searchWord, String finalStartDate, String finalEndDate) {
        BoolQuery.Builder boolBuilder = new BoolQuery.Builder();
        String[] modelSourceCodeArray = modelSourceCodeValue.split(",");
        BoolQuery.Builder boolMultiNestedBuilder = new BoolQuery.Builder();
        for (int i = 0; i < modelSourceCodeArray.length; i++) {
            String modelSourceCode = modelSourceCodeArray[i];
            NestedQuery.Builder nestedQuery = new NestedQuery.Builder();
            nestedQuery.path(modelSourceCode);
            nestedQuery.query(q -> q.bool(b -> b.must(m -> m.queryString(qs -> qs.query(searchWord)))));
            boolMultiNestedBuilder.should(q -> q.nested(nested -> nestedQuery));
        }
        boolBuilder.must(q -> q.bool(b -> b.must(m -> m.bool(boolMultiNestedBuilder.build()))));
        if(CommomDataModelConstants.PATIENT_VIEW.equals(searchView)){
            boolBuilder.must(q -> q.term(t -> t.field("docType").value("patient")));
        }
        if(CommomDataModelConstants.VISIT_VIEW.equals(searchView)){
            boolBuilder.must(q -> q.term(t -> t.field("docType").value("visit")));
        }
        //boolBuilder.must(q -> q.bool(b -> b.must(m -> m.bool(boolMultiNestedBuilder.build()))));
        NestedQuery.Builder nestedQuery = new NestedQuery.Builder();
        nestedQuery.path("visitInformation");
        nestedQuery.query(q -> q.bool(b -> b.must(m -> m.range(qs -> qs.field("visitInformation.visitOrAdmissionDatetime").gte(JsonData.of(finalStartDate)).lte(JsonData.of(finalEndDate))))));
        boolBuilder.must(q -> q.nested(nested -> nestedQuery));
        searchBuilder.query(boolBuilder.build()._toQuery());
        return searchBuilder;
    }

    private CountRequest.Builder multiModelSourceCodeCountNestedBuilderQuery(CountRequest.Builder searchBuilder, String searchView, String modelSourceCodeValue, String searchWord, String finalStartDate, String finalEndDate) {
        BoolQuery.Builder boolBuilder = new BoolQuery.Builder();
        String[] modelSourceCodeArray = modelSourceCodeValue.split(",");
        BoolQuery.Builder boolMultiNestedBuilder = new BoolQuery.Builder();
        for (int i = 0; i < modelSourceCodeArray.length; i++) {
            String modelSourceCode = modelSourceCodeArray[i];
            NestedQuery.Builder nestedQuery = new NestedQuery.Builder();
            nestedQuery.path(modelSourceCode);
            nestedQuery.query(q -> q.bool(b -> b.must(m -> m.queryString(qs -> qs.query(searchWord)))));
            boolMultiNestedBuilder.should(q -> q.nested(nested -> nestedQuery));
        }
        boolBuilder.must(q -> q.bool(b -> b.must(m -> m.bool(boolMultiNestedBuilder.build()))));
        if(CommomDataModelConstants.PATIENT_VIEW.equals(searchView)){
            boolBuilder.must(q -> q.term(t -> t.field("docType").value("patient")));
        }
        if(CommomDataModelConstants.VISIT_VIEW.equals(searchView)){
            boolBuilder.must(q -> q.term(t -> t.field("docType").value("visit")));
        }
        //boolBuilder.must(q -> q.bool(b -> b.must(m -> m.bool(boolMultiNestedBuilder.build()))));
        NestedQuery.Builder nestedQuery = new NestedQuery.Builder();
        nestedQuery.path("visitInformation");
        nestedQuery.query(q -> q.bool(b -> b.must(m -> m.range(qs -> qs.field("visitInformation.visitOrAdmissionDatetime").gte(JsonData.of(finalStartDate)).lte(JsonData.of(finalEndDate))))));
        boolBuilder.must(q -> q.nested(nested -> nestedQuery));
        searchBuilder.query(boolBuilder.build()._toQuery());
        return searchBuilder;
    }

    private List<Map<String, Object>> getPatientsRecordAdvancedSearchElasticDataSource(PatientRecordSearchParam patientRecordSearchParam, List<PatientModelVariableVo> head) {
        List<String> modelSourceCodeList = new ArrayList<>();
        SearchRequest request = SearchRequest.of(i -> {
            SearchRequest.Builder builder = i.index(patientXinjiangDiseaseCenterConfig.getPatient_join_visit_index())
                    .from((patientRecordSearchParam.getPageNum() - 1) * patientRecordSearchParam.getPageSize())
                    .size(patientRecordSearchParam.getPageSize());
            Query query = advancedSearchBuildQuery(patientRecordSearchParam);
            builder.query(query);
            patientRecordSearchParam.getDataList().forEach(data -> {
                modelSourceCodeList.add(HumpToLineUtil.lineToHump(data.getModelSourceCode()));
            });
            builder.highlight(h -> h.fields("*", f -> f.preTags("<font color='red'>").postTags("</font>")));
            builder.source(s -> s.filter(f -> f.includes("demographyInformation", "visitInformation")));
            return builder;
        });


        CountRequest countRequest = CountRequest.of(i -> {
            CountRequest.Builder builder = i.index(patientXinjiangDiseaseCenterConfig.getPatient_join_visit_index());
            Query query = advancedSearchBuildQuery(patientRecordSearchParam);
            builder.query(query);
            patientRecordSearchParam.getDataList().forEach(data -> {
                modelSourceCodeList.add(HumpToLineUtil.lineToHump(data.getModelSourceCode()));
            });
            return builder;
        });

        SearchResponse<JSONObject> response;
        try {
            log.info("getPatientsRecordByVariableElasticDataSource request params: {}", request.toString());
            response = elasticsearchClient.search(request, JSONObject.class);
        } catch (IOException e) {
            log.error("getPatientsRecordByVariableElasticDataSource error: {}", e.getMessage());
            throw new RuntimeException(e);
        }
        try {
            log.info("getPatientsRecordByVariableElasticDataSource request params: {}", request.toString());
            CountResponse count = elasticsearchClient.count(countRequest);
            patientRecordSearchParam.setTotalCount(count.count());
        } catch (IOException e) {
            log.error("getPatientsRecordByVariableElasticDataSource error: {}", e.getMessage());
            throw new RuntimeException(e);
        }

        ArrayList<Map<String, Object>> objectArrayList = new ArrayList<>();
        List<Hit<JSONObject>> hits = response.hits().hits();
        if (CollectionUtil.isNotEmpty(hits)) {
            for (Hit<JSONObject> hit : hits) {
                Map<String, Object> dataMap = JSONObject.parseObject(hit.source().toJSONString(), new TypeReference<Map<String, Object>>(){});
                Object visitInformation = dataMap.get("visitInformation");
                if (visitInformation instanceof JSONArray) {
                    JSONArray visitInformationList = (JSONArray) visitInformation;
                    visitInformationList.sort(Comparator.comparing(obj -> ((JSONObject) obj).getDate("visitOrAdmissionDatetime"), Comparator.naturalOrder()));
                    dataMap.put("visitInformation", visitInformationList);
                }
                Object demographyInformation = dataMap.get("demographyInformation");
                if (demographyInformation instanceof JSONObject) {
                    JSONObject demographyInformationObject = (JSONObject) demographyInformation;
                    DemographyInformation demographyInformationData = JSON.parseObject(demographyInformation.toString(), DemographyInformation.class);
                    Set<Map.Entry<String, Object>> entrySet = demographyInformationObject.entrySet();
                    for (Map.Entry<String, Object> entry : entrySet) {
                        String entryKey = entry.getKey();
                        if(ConstantsModel.PATIENT_NAME.equals(entryKey)){
                            demographyInformationData.setName(DesensitizedUtil.chineseName(demographyInformationData.getName()));
                        }
                        if(ConstantsModel.PATIENT_CONTACT_PHONE.equals(entryKey)){
                            demographyInformationData.setContactPhone(DesensitizedUtil.mobilePhone(demographyInformationData.getContactPhone()));
                        }
                        if(ConstantsModel.PATIENT_ID_NO.equals(entryKey)){
                            demographyInformationData.setIdNo(DesensitizedUtil.idCardNum(demographyInformationData.getIdNo(), 1, 2));
                        }
                        if(ConstantsModel.PATIENT_HOME_ADDRESS.equals(entryKey)){
                            demographyInformationData.setHomeAddress(DesensitizedUtil.address(demographyInformationData.getHomeAddress(), 10));
                        }
                    }
                    dataMap.put("demographyInformation", demographyInformationData);
                }
                objectArrayList.add(dataMap);
            }
            return objectArrayList;
        }
        return new ArrayList<>();
    }

    private Query advancedSearchBuildQuery(PatientRecordSearchParam patientRecordSearchParam) {
        List<PatientRecordSearchParam.SearcherModeRule> searcherMustRuleList = new ArrayList<>();
        List<PatientRecordSearchParam.SearcherModeRule> searcherModeRuleList = patientRecordSearchParam.getDataList();

        BoolQuery.Builder boolBuilder = new BoolQuery.Builder();
        //BoolQuery.Builder boolMultiNestedBuilder = new BoolQuery.Builder();
        BoolQuery.Builder mustBoolBuilder = new BoolQuery.Builder();
        for (PatientRecordSearchParam.SearcherModeRule searcherModeRule : searcherModeRuleList) {
            PatientDiseaseSearchParam diseaseSearchParam = new PatientDiseaseSearchParam();
            BeanUtils.copyProperties(searcherModeRule, diseaseSearchParam);
            diseaseSearchParam.setSearchValue(searcherModeRule.getSearchWord());
            diseaseSearchParam.setSearchType(searcherModeRule.getOperatorValue());
            diseaseSearchParam.setOperatorType(searcherModeRule.getQueryConfig());
            String searchWord = searcherModeRule.getSearchWord();
            String operatorValue = searcherModeRule.getOperatorValue();
            if (DefineConstant.SEARCH_OPERATE_NOT.equals(operatorValue)) {
                searcherMustRuleList.add(searcherModeRule);
            } else {
                NestedQuery.Builder nestedQueryBuilder = new NestedQuery.Builder();
                nestedQueryBuilderWrapper(nestedQueryBuilder, diseaseSearchParam, searchWord);
                mustBoolBuilder.must(q -> q.nested(nested -> nestedQueryBuilder));
            }
        }

        String searchView = patientRecordSearchParam.getSearchView();
        if(CommomDataModelConstants.PATIENT_VIEW.equals(searchView)){
            boolBuilder.must(q -> q.bool(b -> b.must(m -> m.term(t -> t.field("docType").value("patient")))));
        }
        if(CommomDataModelConstants.VISIT_VIEW.equals(searchView)){
            boolBuilder.must(q -> q.bool(b -> b.must(m -> m.term(t -> t.field("docType").value("visit")))));
        }
        //boolBuilder.must(must -> must.bool(b -> b.must(m -> m.bool(mustBoolBuilder.build()))));
        boolBuilder.must(q -> q.bool(b -> b.must(m -> m.bool(mustBoolBuilder.build()))));

        BoolQuery.Builder mustNotBoolBuilder = new BoolQuery.Builder();
        if (CollectionUtil.isNotEmpty(searcherMustRuleList)) {
            for (PatientRecordSearchParam.SearcherModeRule searcherModeRule : searcherMustRuleList) {
                String searchWord = searcherModeRule.getSearchWord();
                PatientDiseaseSearchParam diseaseSearchParam = new PatientDiseaseSearchParam();
                BeanUtils.copyProperties(searcherModeRule, diseaseSearchParam);
                diseaseSearchParam.setSearchValue(searcherModeRule.getSearchWord());
                diseaseSearchParam.setSearchType(searcherModeRule.getOperatorValue());
                diseaseSearchParam.setOperatorType(searcherModeRule.getQueryConfig());
                NestedQuery.Builder nestedQueryBuilder = new NestedQuery.Builder();
                // 多个条件查询
                nestedQueryBuilderWrapper(nestedQueryBuilder, diseaseSearchParam, searchWord);
                mustNotBoolBuilder.must(q -> q.nested(nested -> nestedQueryBuilder));
            }
            boolBuilder.mustNot(mustNot -> mustNot.bool(b -> b.must(m -> m.bool(mustNotBoolBuilder.build()))));
        }
        /*NestedQuery.Builder nestedQueryBuilder = new NestedQuery.Builder();
        nestedQueryBuilder.path("visitInformation");
        nestedQueryBuilder.query(q -> q.bool(b -> b.must(m -> m.range(qs -> qs.field("visitInformation.visitOrAdmissionDatetime").gte(JsonData.of(patientRecordSearchParam.getStartDate())).lte(JsonData.of(patientRecordSearchParam.getEndDate()))))));
        boolBuilder.must(q -> q.nested(nested -> nestedQueryBuilder));*/
        return boolBuilder.build()._toQuery();
    }

    private void nestedQueryBuilderWrapper(NestedQuery.Builder builder, PatientDiseaseSearchParam diseaseSearchParam, String searchValue) {
        String modelSourceCode = diseaseSearchParam.getModelSourceCode();
        String variableCode = diseaseSearchParam.getVariableCode();
        String variableType = diseaseSearchParam.getVariableType();
        builder.path(StrUtil.toCamelCase(modelSourceCode));
        String filedName = StrUtil.toCamelCase(modelSourceCode) + "." + StrUtil.toCamelCase(variableCode);
        // 构建搜索条件的字段
        if ("varchar".equals(variableType)||"text".equals(variableType)){
            // 字符串
            buildString(diseaseSearchParam, builder, filedName, searchValue);
        }else  if (variableType.contains("int") || variableType.contains("float")){
            // 数字类型
            buildNumber(diseaseSearchParam, builder, filedName, searchValue);
        }else  if (variableType.contains("timestamp") || variableType.contains("date")|| variableType.contains("datetime")){
            // 日期类型
            String searchStartDate = diseaseSearchParam.getSearchStartDate();
            String searchEndDate = diseaseSearchParam.getSearchEndDate();
            if(diseaseSearchParam.getSearchType().equals(DefineConstant.BETWEEN)){
                List<String> dateList = JSON.parseArray(searchValue, String.class);
                if (CollectionUtil.isNotEmpty(dateList)) {
                    searchStartDate = dateList.get(0);
                    searchEndDate = dateList.get(1);
                }
            }
            buildDate(diseaseSearchParam, builder, filedName, searchValue, searchStartDate, searchEndDate);
        }else  if (variableType.contains("bool")){
            // bool类型
            buildBool(diseaseSearchParam, builder, filedName, searchValue);
        }
    }

    private void buildBool(PatientDiseaseSearchParam diseaseSearchParam, NestedQuery.Builder searchBuilder, String filedName, String searchValue) {
        boolean isBool;
        if ("是".equals(searchValue)||"true".equals(searchValue)){
            isBool = true;
        } else {
            isBool = false;
        }
        if (DefineConstant.EMPTY.equals(diseaseSearchParam.getOperatorType())){
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.exists(exists -> exists.field(filedName)))
                    )
            );
        }else if (DefineConstant.NOT_EMPTY.equals(diseaseSearchParam.getOperatorType())){
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.mustNot(mustNot -> mustNot.exists(exists -> exists.field(filedName)))
                    )
            );
        }else if (DefineConstant.EQUAL_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 等于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.term(t -> t.field(filedName).value(isBool)))
                    )
            );
        } else if (DefineConstant.NOT_EQUAL_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 不等于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.mustNot(mustNot -> mustNot.term(t -> t.field(filedName).value(isBool)))
                    )
            );
        }
    }

    private void buildDate(PatientDiseaseSearchParam diseaseSearchParam, NestedQuery.Builder searchBuilder, String filedName, String searchValue, String startDate, String endDate) {
        if (DefineConstant.GREATER_THAN_CODE.equals(diseaseSearchParam.getOperatorType())) {
            //  大于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(term -> term.field(filedName).gt(JsonData.of(searchValue))))
                    )
            );
        } else if (DefineConstant.LESS_THAN_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 小于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(term -> term.field(filedName).lt(JsonData.of(searchValue))))
                    )
            );
        }else if (DefineConstant.EQUAL_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 等于搜索条件构建
            log.info("buildDate searchValue:{}, filedName:{}, startDate:{}, endDate:{}", searchValue, filedName, startDate, endDate);
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(term -> term.field(filedName).gte(JsonData.of(searchValue)).lte(JsonData.of(searchValue)).format("yyyy-MM-dd")))
                    )
            );
        } else if (DefineConstant.GREATER_THAN_EQUAL_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 大于等于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(term -> term.field(filedName).gte(JsonData.of(searchValue))))
                    )
            );
        } else if (DefineConstant.LESS_THAN_EQUAL_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 小于等于条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(term -> term.field(filedName).lte(JsonData.of(searchValue))))
                    )
            );
        }else if (DefineConstant.BETWEEN.equals(diseaseSearchParam.getOperatorType())) {
            // 范围区间搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(term -> term.field(filedName)
                                    .gte(JsonData.of(startDate))
                                    .lte(JsonData.of(endDate))))
                    )
            );
        } else if (DefineConstant.EMPTY.equals(diseaseSearchParam.getOperatorType())) {
            // 空值搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.mustNot(mustNot -> mustNot.wildcard(w -> w.field(filedName).wildcard(searchValue)))
                    )
            );
        }else if (DefineConstant.NOT_EMPTY.equals(diseaseSearchParam.getOperatorType())) {
            // 非空搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.wildcard(w -> w.field(filedName).wildcard(searchValue)))
                    )
            );
        }
    }

    private void buildNumber(PatientDiseaseSearchParam diseaseSearchParam, NestedQuery.Builder searchBuilder, String filedName, String searchValue) {
        if (DefineConstant.GREATER_THAN_CODE.equals(diseaseSearchParam.getOperatorType())) {
            //  大于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(term -> term.field(filedName).gt(JsonData.of(searchValue))))
                    )
            );
        } else if (DefineConstant.LESS_THAN_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 小于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(term -> term.field(filedName).lt(JsonData.of(searchValue))))
                    )
            );
        }else if (DefineConstant.EQUAL_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 等于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.term(term -> term.field(filedName).value(searchValue)))
                    )
            );
        } else if (DefineConstant.NOT_CONTAINS_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 大于等于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(term -> term.field(filedName).gte(JsonData.of(searchValue))))
                    )
            );
        } else if (DefineConstant.GREATER_THAN_EQUAL_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 小于等于条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(term -> term.field(filedName).lte(JsonData.of(searchValue))))
                    )
            );
        }else if (DefineConstant.BETWEEN.equals(diseaseSearchParam.getOperatorType())) {
            // 范围区间搜索条件构建
            String[] searchValueArray = searchValue.split(",");
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(term -> term.field(filedName)
                                    .gte(JsonData.of(searchValueArray[0]))
                                    .lte(JsonData.of(searchValueArray[1]))))
                    )
            );
        } else if (DefineConstant.EMPTY.equals(diseaseSearchParam.getOperatorType())) {
            // 空值搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.mustNot(mustNot -> mustNot.wildcard(w -> w.field(filedName).wildcard(searchValue)))
                    )
            );
        }else if (DefineConstant.NOT_EMPTY.equals(diseaseSearchParam.getOperatorType())) {
            // 非空搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.wildcard(w -> w.field(filedName).wildcard(searchValue)))
                    )
            );
        }
    }

    private void buildString(PatientDiseaseSearchParam diseaseSearchParam, NestedQuery.Builder searchBuilder, String filedName, String searchValue) {
        if (DefineConstant.EQUAL_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 等于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.term(term -> term.field(filedName).value(searchValue)))
                    )
            );
        } else if (DefineConstant.NOT_EQUAL_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 不等于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.mustNot(mustNot -> mustNot.term(term -> term.field(filedName).value(searchValue)))
                    )
            );
        }else if (DefineConstant.CONTAINS_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 包含搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.should(should -> should.match(match -> match.field(filedName).query(searchValue)))
                    )
            );
        } else if (DefineConstant.NOT_CONTAINS_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 不包含搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.mustNot(mustNot -> mustNot.match(match -> match.field(filedName).query(searchValue)))
                    )
            );
        } else if (DefineConstant.EMPTY.equals(diseaseSearchParam.getOperatorType())) {
            // 空值搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.mustNot(mustNot -> mustNot.wildcard(w -> w.field(filedName).wildcard(searchValue)))
                    )
            );
        }else if (DefineConstant.NOT_EMPTY.equals(diseaseSearchParam.getOperatorType())) {
            // 非空搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.wildcard(w -> w.field(filedName).wildcard(searchValue)))
                    )
            );
        }
    }

    @Override
    public Long countPatientForGenderGroup(String gender) {
        DemographyInformationExample demographyInformationExample = new DemographyInformationExample();
        DemographyInformationExample.Criteria criteria = demographyInformationExample.createCriteria();
        if (StringUtils.isNotBlank(gender)) {
            criteria.andGenderEqualTo(gender);
        }
        return demographyInformationMapper.countByExample(demographyInformationExample);
    }

    @Override
    public List<DemographyInformation> getPatientListForPage(int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        DemographyInformationExample demographyInformationExample = new DemographyInformationExample();
        demographyInformationExample.setOrderByClause("patient_sn desc");
        return demographyInformationMapper.selectByExample(demographyInformationExample);
    }

    public PatientDataViewWrapper getPatientRecordViewDetail(String modelSourceCodeView, String patientSn, String visitSn, Integer pageNum, Integer pageSize) {
        PatientDataViewWrapper patientDataViewWrapper = new PatientDataViewWrapper();
        List<Map<String, Object>> dataList = new ArrayList<>();
        // tableHead
        List<PatientDataViewWrapper.TableHeadVo> tableHeadVoList = new ArrayList<>();
        List<PatientModelVariableVo> rdrPatientModelVariableList = patientModelDefineService.getPatientModelConfigByModelSourceCode(modelSourceCodeView, true);
        rdrPatientModelVariableList.forEach(variable -> {
            PatientDataViewWrapper.TableHeadVo tableHeadVo = new PatientDataViewWrapper.TableHeadVo();
            tableHeadVo.setLabelName(variable.getVariableName());
            String variableCode = HumpToLineUtil.lineToHump(variable.getVariableCode());
            tableHeadVo.setLabelCode(variableCode);
            tableHeadVoList.add(tableHeadVo);
        });
        patientDataViewWrapper.setTableHeadVoList(tableHeadVoList);
        // dataList
        String modelSourceCodeValue = HumpToLineUtil.lineToHump(modelSourceCodeView);
        SearchRequest request = SearchRequest.of(i -> {
            SearchRequest.Builder builder = i.index(patientXinjiangDiseaseCenterConfig.getPatient_join_visit_index());
            //builder.query(q -> q.bool(bool -> bool.must(boolBuilder -> boolBuilder.must(m -> m.term(t -> t.field("docType").value("visit"))))));
            if (StringUtils.isEmpty(visitSn)) {
                //builder.query(q1 -> q1.bool(b -> b.must(m -> m.term(match -> match.field("patientSn").value(patientSn)))));
                builder.query(q->q.ids(id->id.values(patientSn)));
            } else {
                builder.query(q -> q.nested(nested -> nested.path(modelSourceCodeValue).innerHits(innerHits -> innerHits.size(5)).query(q1 -> q1.bool(b -> b.must(m -> m.term(match -> match.field(modelSourceCodeValue.concat(".visitSn.keyword")).value(visitSn)))))));
            }
            builder.source(s -> s.filter(f -> f.includes(Arrays.asList(modelSourceCodeValue))));
            return builder;
        });
        log.info("getPatientRecordViewDetailForElastic：{}", request.toString());
        SearchResponse<JSONObject> response;
        try {
            response = elasticsearchClient.search(request, JSONObject.class);
        } catch (IOException e) {
            log.error("获取患者病历数据失败", e);
            throw new ServiceException("获取患者病历数据失败");
        }
        List<Hit<JSONObject>> hits = response.hits().hits();
        hits.forEach(hit -> {
            JSONObject source = hit.source();
            Map<String, Object> dataMap = new HashMap<>();
            LinkedHashMap<String, Object> linkedHashMap;
            ArrayList<LinkedHashMap<String, Object>> arrayList;
            Object dataValue = source.get(modelSourceCodeValue);
            if (dataValue instanceof LinkedHashMap) {
                linkedHashMap = ((LinkedHashMap) dataValue);
                linkedHashMap.forEach((key, value) -> {
                    if(Boolean.TRUE.equals(value)){value = "是";}
                    if(Boolean.FALSE.equals(value)){value = "否";}
                    dataMap.put(key, value);
                    String pkId = linkedHashMap.get("pkid") != null ? linkedHashMap.get("pkid").toString() : "";
                    String visitSnValue = linkedHashMap.get("visitSn") != null ? linkedHashMap.get("visitSn").toString() : "";
                    if(key.equals("chiefComplaint")){
                        PatientDataViewWrapper ccInfo = getPatientViewDetailFromPostgres("cc_info", patientSn, visitSnValue, pkId);
                        if(!ccInfo.getSpecializedDiseaseFieldIsNull()){
                            dataMap.put("ccInfo", ccInfo);
                        }
                    }
                    if(key.equals("isIsaTherapy")){
                        PatientDataViewWrapper isaTherapyInfo = getPatientViewDetailFromPostgres("isa_therapy_info", patientSn, visitSnValue, pkId);
                        if(!isaTherapyInfo.getSpecializedDiseaseFieldIsNull()){
                            dataMap.put("isaTherapyInfo", isaTherapyInfo);
                        }
                    }
                    if(key.equals("isHormoneTherapy")){
                        PatientDataViewWrapper hormoneTherapyInfo = getPatientViewDetailFromPostgres("hormone_therapy_info", patientSn, visitSnValue, pkId);
                        if(!hormoneTherapyInfo.getSpecializedDiseaseFieldIsNull()){
                            dataMap.put("hormoneTherapyInfo", hormoneTherapyInfo);
                        }
                    }
                    if(ConstantsModel.PATIENT_NAME.equals(key)){
                        dataMap.put(key, DesensitizedUtil.chineseName(value.toString()));
                    }
                    if(ConstantsModel.PATIENT_CONTACT_PHONE.equals(key)){
                        dataMap.put(key, DesensitizedUtil.mobilePhone(value.toString()));
                    }
                    if(ConstantsModel.PATIENT_ID_NO.equals(key)){
                        dataMap.put(key, DesensitizedUtil.idCardNum(value.toString(),1, 2));
                    }
                    if(key.contains(ConstantsModel.PATIENT_HOME_ADDRESS)){
                        dataMap.put(key, DesensitizedUtil.address(value.toString(), 10));
                    }
                });
                if(MapUtil.isNotEmpty(dataMap)){
                    dataList.add(dataMap);
                }
            }
            if (dataValue instanceof ArrayList) {
                arrayList = ((ArrayList) dataValue);
                arrayList.forEach(data -> {
                    if (data.get("visitSn") != null) {
                        String visitSnValue = data.get("visitSn").toString();
                        if (StringUtils.isNotEmpty(visitSn) && !visitSn.equals(visitSnValue)) {
                            return;
                        }
                    }
                    Map<String, Object> arrayDataMap = new HashMap<>();
                    String pkId = data.get("pkid") != null ? data.get("pkid").toString() : "";
                    String visitSnValue = data.get("visitSn") != null ? data.get("visitSn").toString() : "";
                    data.forEach((key, value) -> {
                        if(Boolean.TRUE.equals(value)){value = "是";}
                        if(Boolean.FALSE.equals(value)){value = "否";}
                        arrayDataMap.put(key, value);
                        if(key.equals("chiefComplaint")){
                            PatientDataViewWrapper ccInfo = getPatientViewDetailFromPostgres("cc_info", patientSn, visitSnValue, pkId);
                            if(!ccInfo.getSpecializedDiseaseFieldIsNull()){
                                arrayDataMap.put("ccInfo", ccInfo);
                            }
                        }
                        if(key.equals("isIsaTherapy")){
                            PatientDataViewWrapper isaTherapyInfo = getPatientViewDetailFromPostgres("isa_therapy_info", patientSn, visitSnValue, pkId);
                            if(!isaTherapyInfo.getSpecializedDiseaseFieldIsNull()){
                                arrayDataMap.put("isaTherapyInfo", isaTherapyInfo);
                            }
                        }
                        if(key.equals("isHormoneTherapy")){
                            PatientDataViewWrapper hormoneTherapyInfo = getPatientViewDetailFromPostgres("hormone_therapy_info", patientSn, visitSnValue, pkId);
                            if(!hormoneTherapyInfo.getSpecializedDiseaseFieldIsNull()){
                                arrayDataMap.put("hormoneTherapyInfo", hormoneTherapyInfo);
                            }
                        }
                    });
                    if(MapUtil.isNotEmpty(arrayDataMap)){
                        dataList.add(arrayDataMap);
                    }
                });
            }
        });
        patientDataViewWrapper.setDataType(modelSourceCodeView);
        List<String> testRecordRelForms = Arrays.asList("test_record", "micro_smear_test_record", "micro_culture_drug_sensitivity_test_record");
        if (testRecordRelForms.contains(modelSourceCodeView)) {
            List<Map<String, Object>> testRecordList = testRecord(pageNum, pageSize, dataList);
            patientDataViewWrapper.setDataList(testRecordList);
            return patientDataViewWrapper;
        }
        if ("visit_information".equals(modelSourceCodeView)) {
            dataList.sort(Comparator.comparing((Map<String, Object> h) -> (DateUtil.getAutoParseDate(h.get("visitOrAdmissionDatetime").toString(), "yyyy-MM-dd HH:mm:ss")).getTime()));
            patientDataViewWrapper.setDataList(dataList);
            return patientDataViewWrapper;
        }
        if ("exam_record".equals(modelSourceCodeView)) {
            List<Map<String, Object>> examRecordList = examRecord(pageNum, pageSize, dataList);
            examRecordList.sort(Comparator.comparing((Map<String, Object> h) -> (DateUtil.getAutoParseDate(h.get("examTime").toString(), "yyyy-MM-dd HH:mm:ss")).getTime()));
            patientDataViewWrapper.setDataList(examRecordList);
            return patientDataViewWrapper;
        }
        List<String> pathologyReportForms = Arrays.asList("pathology_record");
        if (pathologyReportForms.contains(modelSourceCodeView)) {
            List<Map<String, Object>> pathologyRecordList = pathologyRecord(pageNum, pageSize, dataList);
            patientDataViewWrapper.setDataList(pathologyRecordList);
            return patientDataViewWrapper;
        }
        if ("all_diagnosis".equals(modelSourceCodeView)){
            dataList.sort(Comparator.comparing((Map<String, Object> m) -> (String) m.get("diagnosisDatetime"), Comparator.naturalOrder())
                       .thenComparing((Map<String, Object> m) -> (Integer) m.get("diagnosisOrderNo"), Comparator.naturalOrder()));
            List<Map<String, Object>> collectMapList = dataList.stream().skip((pageNum - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
            patientDataViewWrapper.setTotalCount(dataList.size());
            patientDataViewWrapper.setDataList(collectMapList);
            return patientDataViewWrapper;
        }
        List<String> drugOrderForms = Arrays.asList("drug_order", "non_drug_order");
        if (drugOrderForms.contains(modelSourceCodeView)){
            dataList.sort(Comparator.comparing((Map<String, Object> m) -> (String) m.get("orderCreateDatetime"), Comparator.naturalOrder()));
            List<Map<String, Object>> collectMapList = dataList.stream().skip((pageNum - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
            patientDataViewWrapper.setTotalCount(dataList.size());
            patientDataViewWrapper.setDataList(collectMapList);
            return patientDataViewWrapper;
        }

        List<String> treatmentForms = Arrays.asList("tb_smear_test","tb_culture_test","tb_drug_sensitive_test","tb_molecule_drug_sensitive_test","tb_nucleic_acid_test","tb_antibody_test","tb_infection_t_cell_test","tb_skin_test");
        if (treatmentForms.contains(modelSourceCodeView)){
            List<Map<String, Object>> tuberculosisRelatedFormList = tuberculosisRelatedForms(pageNum, pageSize, dataList);
            patientDataViewWrapper.setDataList(tuberculosisRelatedFormList);
            return patientDataViewWrapper;
        }
        patientDataViewWrapper.setDataList(dataList);
        return patientDataViewWrapper;
    }

    /*private Object extracted(String modelSourceCode, List<PatientModelVariableRecordParam.ModelVariableConfig> modelVariableConfigList, Class clazz) throws NoSuchFieldException, InstantiationException, IllegalAccessException {
        Object instance = clazz.newInstance();
        List<TableModelVo> tableModelColumnList = patientModelDefineService.getTableModelColumnByModelCode(modelSourceCode);
        for (PatientModelVariableRecordParam.ModelVariableConfig modelVariableConfig : modelVariableConfigList) {
            for (TableModelVo tableModelVo : tableModelColumnList) {
                if(tableModelVo.getColumnName().equals(modelVariableConfig.getVariableCode())){
                    String modelVariableCode = HumpToLineUtil.lineToHump(modelVariableConfig.getVariableCode());
                    Field declaredField = clazz.getDeclaredField(modelVariableCode);
                    declaredField.setAccessible(true);
                    //Field declaredField = instance.getDeclaredField(modelVariableCode);
                    //declaredField.setAccessible(true);
                    ReflectUtil.setFieldValue(instance, modelVariableCode, modelVariableConfig.getVariableValue());
                }
            }
        }
        return instance;
    }
    */
    private List<Map<String, Object>> tuberculosisRelatedForms(int pageNum, int pageSize, List<Map<String, Object>> dataList) {
        List<Map<String, Object>> testGroupItems = dataList.stream().collect(Collectors.collectingAndThen(Collectors.toMap(entry -> entry.get("testGroupItems"), Function.identity(), (existing, replacement) -> existing), map -> new ArrayList<>(map.values())));
        List<Map<String, Object>> mapList = new ArrayList<>();
        for (Map<String, Object> ma : testGroupItems) {
            Map<String, Object> m = new HashMap<>();
            m.put("examTime", ma.get("examDatetime"));
            m.put("itemName", ma.get("testGroupItems"));
            List<Map<String, Object>> objectArrayList = new ArrayList<>();
            for (Map<String, Object> map : dataList) {
                Object testGroupItem = map.get("testGroupItems");
                if(ObjectUtil.equals(testGroupItem, ma.get("testGroupItems"))){
                    objectArrayList.add(map);
                }
            }
            List<Map<String, Object>> collectMapList = objectArrayList.stream().skip((pageNum - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
            m.put("data", collectMapList);
            m.put("totalCount", objectArrayList.size());
            mapList.add(m);
        }
        return mapList;
    }
    public List<Map<String, Object>> testRecord(int pageNum, int pageSize, List<Map<String, Object>> dataList) {
        List<Map<String, Object>> testGroupItems = dataList.stream().collect(Collectors.collectingAndThen(Collectors.toMap(entry -> entry.get("testGroupItems"), Function.identity(), (existing, replacement) -> existing), map -> new ArrayList<>(map.values())));
        List<Map<String, Object>> mapList = new ArrayList<>();
        for (Map<String, Object> ma : testGroupItems) {
            Map<String, Object> m = new HashMap<>();
            m.put("examTime", ma.get("examDatetime"));
            m.put("itemName", ma.get("testGroupItems"));
            List<Map<String, Object>> objectArrayList = new ArrayList<>();
            for (Map<String, Object> map : dataList) {
                Object testGroupItem = map.get("testGroupItems");
                if (ma.get("testGroupItems").equals(testGroupItem)) {
                    objectArrayList.add(map);
                }
            }
            List<Map<String, Object>> collectMapList = objectArrayList.stream().skip((pageNum - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
            m.put("data", collectMapList);
            m.put("totalCount", objectArrayList.size());
            mapList.add(m);
        }
        return mapList;
    }

    public List<Map<String, Object>> examRecord(int pageNum, int pageSize, List<Map<String, Object>> dataList) {
        List<Map<String, Object>> examClass = dataList.stream().collect(Collectors.collectingAndThen(Collectors.toMap(entry -> entry.get("examId"), Function.identity(), (existing, replacement) -> existing), map -> new ArrayList<>(map.values())));
        List<Map<String, Object>> mapList = new ArrayList<>();
        for (Map<String, Object> ma : examClass) {
            Map<String, Object> m = new HashMap<>();
            m.put("examTime", ma.get("examDatetime"));
            m.put("itemName", ma.get("examName"));
            List<Map<String, Object>> objectArrayList = new ArrayList<>();
            for (Map<String, Object> map : dataList) {
                Object testGroupItem = map.get("examId");
                if (ma.get("examId").equals(testGroupItem)) {
                    objectArrayList.add(map);
                }
            }
            List<Map<String, Object>> collectMapList = objectArrayList.stream().skip((pageNum - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
            m.put("data", collectMapList);
            m.put("totalCount", objectArrayList.size());
            mapList.add(m);
        }
        //mapList.sort(Comparator.comparing((Map<String, Object> h) -> (DateUtil.getDate(h.get("examTime").toString(), "yyyy-MM-dd HH:mm:ss")).getTime()));
        /*Collections.sort(mapList , new Comparator<Map<String, Object>>() {
            @Override
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                Long compareValue1 = DateUtil.getDate(o1.get("examTime").toString(), "yyyy-MM-dd HH:mm:ss").getTime();
                Long compareValue2 = DateUtil.getDate(o2.get("examTime").toString(), "yyyy-MM-dd HH:mm:ss").getTime();
                return compareValue1.compareTo(compareValue2);
            }
        });*/
        return mapList;
    }

    public List<Map<String, Object>> pathologyRecord(int pageNum, int pageSize, List<Map<String, Object>> dataList) {
        List<Map<String, Object>> examClass = dataList.stream().collect(Collectors.collectingAndThen(Collectors.toMap(entry -> entry.get("examName"), Function.identity(), (existing, replacement) -> existing), map -> new ArrayList<>(map.values())));
        List<Map<String, Object>> mapList = new ArrayList<>();
        for (Map<String, Object> ma : examClass) {
            Map<String, Object> m = new HashMap<>();
            m.put("examTime", ma.get("reportDatetime"));
            m.put("itemName", ma.get("examName"));
            List<Map<String, Object>> objectArrayList = new ArrayList<>();
            for (Map<String, Object> map : dataList) {
                Object testGroupItem = map.get("examName");
                if (ma.get("examName").equals(testGroupItem)) {
                    objectArrayList.add(map);
                }
            }
            List<Map<String, Object>> collectMapList = objectArrayList.stream().skip((pageNum - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
            m.put("data", collectMapList);
            m.put("totalCount", objectArrayList.size());
            mapList.add(m);
        }
        return mapList;
    }

    public PatientDataViewWrapper getPatientViewDetailFromPostgres(String modelSourceCode, String patientSn, String visitSn, String pkId) {
        PatientDataViewWrapper patientDataViewWrapper = new PatientDataViewWrapper();
        List<Map<String, Object>> dataList = new ArrayList<>();
        List<PatientDataViewWrapper.TableHeadVo> tableHeadVoList = getSourceTableHeadViewList(modelSourceCode);
        patientDataViewWrapper.setTableHeadVoList(tableHeadVoList);

        if("cc_info".equals(modelSourceCode)){
            CcInfoExample example = new CcInfoExample();
            example.createCriteria().andPatientSnEqualTo(patientSn).andVisitSnEqualTo(visitSn).andPkidEqualTo(pkId);
            List<CcInfo> ccInfoList = ccInfoMapper.selectByExample(example);
            if (CollectionUtil.isNotEmpty(ccInfoList)) {
                patientDataViewWrapper.setSpecializedDiseaseFieldIsNull(false);
                for (CcInfo ccInfo : ccInfoList) {
                    Map<String, Object> objectMap = BeanUtil.beanToMap(ccInfo, false, true);
                    dataList.add(objectMap);
                }
            }
        }

        if("isa_therapy_info".equals(modelSourceCode)){
            IsaTherapyInfoExample isaTherapyInfoExample = new IsaTherapyInfoExample();
            isaTherapyInfoExample.createCriteria().andPatientSnEqualTo(patientSn).andVisitSnEqualTo(visitSn).andPkidEqualTo(pkId);
            List<IsaTherapyInfo> isaTherapyInfoList = isaTherapyInfoMapper.selectByExample(isaTherapyInfoExample);
            patientDataViewWrapper.setSpecializedDiseaseFieldIsNull(false);
            if (CollectionUtil.isNotEmpty(isaTherapyInfoList)) {
                for (IsaTherapyInfo isaTherapyInfo : isaTherapyInfoList) {
                    Map<String, Object> objectMap = BeanUtil.beanToMap(isaTherapyInfo, false, true);
                    dataList.add(objectMap);
                }
            }
        }
        if("hormone_therapy_info".equals(modelSourceCode)){
            HormoneTherapyInfoExample hormoneTherapyInfoExample = new HormoneTherapyInfoExample();
            hormoneTherapyInfoExample.createCriteria().andPatientSnEqualTo(patientSn).andVisitSnEqualTo(visitSn).andPkidEqualTo(pkId);
            List<HormoneTherapyInfo> hormoneTherapyInfoList = hormoneTherapyInfoMapper.selectByExample(hormoneTherapyInfoExample);
            patientDataViewWrapper.setSpecializedDiseaseFieldIsNull(false);
            if (CollectionUtil.isNotEmpty(hormoneTherapyInfoList)) {
                for (HormoneTherapyInfo hormoneTherapyInfo : hormoneTherapyInfoList) {
                    Map<String, Object> objectMap = BeanUtil.beanToMap(hormoneTherapyInfo, false, true);
                    dataList.add(objectMap);
                }
            }
        }
        patientDataViewWrapper.setDataType(modelSourceCode);
        patientDataViewWrapper.setDataList(dataList);
        return patientDataViewWrapper;
    }

    private List<PatientDataViewWrapper.TableHeadVo> getSourceTableHeadViewList(String modelSourceCode) {
        List<PatientDataViewWrapper.TableHeadVo> tableHeadVoList = new ArrayList<>();
        Object sourceTableHeadViewData = redisTemplateService.get(modelSourceCode);
        if(sourceTableHeadViewData != null){
            List<PatientDataViewWrapper.TableHeadVo> tableHeadViewList = JSON.parseArray(sourceTableHeadViewData.toString(), PatientDataViewWrapper.TableHeadVo.class);
            return tableHeadViewList;
        }
        List<PatientModelVariableVo> rdrPatientModelVariableList = patientModelDefineService.getPatientModelConfigByModelSourceCode(modelSourceCode, false);
        rdrPatientModelVariableList.forEach(variable -> {
            PatientDataViewWrapper.TableHeadVo tableHeadVo = new PatientDataViewWrapper.TableHeadVo();
            tableHeadVo.setLabelName(variable.getVariableName());
            String variableCode = HumpToLineUtil.lineToHump(variable.getVariableCode());
            tableHeadVo.setLabelCode(variableCode);
            tableHeadVoList.add(tableHeadVo);
        });
        redisTemplateService.set(modelSourceCode, JSON.toJSON(tableHeadVoList));
        return tableHeadVoList;
    }


    @Override
    public List<PatientVisitTimeLineVo> getPatientVisitTimeLineRecord(String patientSn, String visitSn) {
        return visitInformationMapper.getPatientVisitTimeLineRecord(patientSn, visitSn);
    }

    @Override
    public boolean createIndex(String indexName, boolean isNested, boolean isCreateRelation) throws IOException {
        BooleanResponse exists = elasticsearchClient.indices().exists(ex -> ex.index(indexName));
        if (exists.value()) {
            elasticsearchClient.indices().delete(deleteIndexBuilder -> deleteIndexBuilder.index(indexName));
        }
        // 获取索引的mapping
        Map<String, Property> documentMap = getElaticIndexMapping(indexName, isNested);
        if (isCreateRelation) {
            List<String> relation = new ArrayList<>();
            relation.add("visit");
            documentMap.put("relation", Property.of(property ->
                    property.join(nes -> nes.relations(MapUtil.of("patient", relation))))
            );
        }
        CreateIndexResponse indexResponse = elasticsearchClient.indices().create(createIndexBuilder -> createIndexBuilder
                .index(indexName)
                .mappings(mappings ->
                        mappings.properties(documentMap))
                .settings(settings -> settings.otherSettings(getIndexSettingMap()))
        );
        return indexResponse.acknowledged();
    }

    private Map<String, Property> getElaticIndexMapping(String indexName, boolean isNested) {
        List<PatientModelDefine> patientModelSourceCodeConfigList = patientModelDefineService.getPatientModelSourceCodeConfigList();
        Map<String, Property> documentMap = new HashMap<>();
        documentMap.put("visitSn", Property.of(property ->
                property.keyword(KeywordProperty.of(keywordProperty ->
                        keywordProperty.ignoreAbove(256).index(true))))
        );
        documentMap.put("patientSn", Property.of(property ->
                property.keyword(KeywordProperty.of(keywordProperty ->
                        keywordProperty.ignoreAbove(256).index(true))))
        );
        documentMap.put("docType", Property.of(property ->
                property.keyword(KeywordProperty.of(keywordProperty ->
                        keywordProperty.ignoreAbove(256).index(true))))
        );
        if (CollectionUtil.isNotEmpty(patientModelSourceCodeConfigList)) {
            for (PatientModelDefine patientModelDefine : patientModelSourceCodeConfigList) {
                Map<String, Property> filedMap = new HashMap<>();
                List<PatientModelVariableVo> variableList = patientModelDefineService.getPatientModelConfigByModelSourceCode(patientModelDefine.getModelSourceCode(), false);
                if (!variableList.isEmpty()) {
                    for (PatientModelVariableVo variable : variableList) {
                        String filed = StrUtil.toCamelCase(variable.getVariableCode());
                        List<String> variableTypeList = Arrays.asList("int4", "float8", "varchar[]", "int[]", "float[]", "varchar");
                        if (variableTypeList.contains(variable.getVariableType())) {
                            Map<String, Property> dataMap = new HashMap<>();
                            dataMap.put("keyword", Property.of(property -> property.keyword(data -> data.index(true).ignoreAbove(256))));
                            filedMap.put(filed, Property.of(property ->
                                            property.text(TextProperty.of(textProperty ->
                                                            textProperty.fields(dataMap).analyzer("ik_max_word").searchAnalyzer("ik_max_word")
                                                    )
                                            )
                                    )
                            );
                        }
                        if (variable.getVariableType().contains("int") || variable.getVariableType().contains("float")) {
                            filedMap.put(filed, Property.of(property ->
                                    property.integer(IntegerNumberProperty.of(integerNumberProperty
                                            -> integerNumberProperty.index(true))))
                            );
                        }
                        if (variable.getVariableType().contains("timestamp") || variable.getVariableType().contains("date")) {
                            filedMap.put(filed, Property.of(property ->
                                    property.date(DateProperty.of(dateProperty
                                            -> dateProperty.index(true).format("yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"))))
                            );
                        }
                    }
                }
                if (isNested) {
                    documentMap.put(StrUtil.toCamelCase(patientModelDefine.getModelSourceCode()), Property.of(property ->
                            property.nested(nes -> nes.properties(filedMap)))
                    );
                } else {
                    documentMap.put(StrUtil.toCamelCase(patientModelDefine.getModelSourceCode()), Property.of(property ->
                            property.object(obj -> obj.properties(filedMap)))
                    );
                }
            }
        }
        return documentMap;
    }

    /**
     * 设置ES索引字段长度，和索引字段数量，文档大小等防止大文档存入报错
     *
     * @return
     */
    private Map<String, JsonData> getIndexSettingMap() {
        Map<String, JsonData> settingMap = new HashMap<>();
        settingMap.put("index.mapping.total_fields.limit", JsonData.fromJson("50000"));
        settingMap.put("index.mapping.nested_objects.limit", JsonData.fromJson("100000"));
        settingMap.put("index.mapping.nested_fields.limit", JsonData.fromJson("50000"));
        settingMap.put("index.highlight.max_analyzed_offset", JsonData.fromJson("100000000"));
        return settingMap;
    }

    @Override
    public int getPatientRecordCount() {
        return demographyInformationMapper.getPatientRecordCount();
    }

    @Override
    public Map<String, Object> savePatientRecordToElasticSearch(String patientSn, String indexName) {
        Map<String, Object> patientMap = new HashMap<>();
        try {
            GetResponse<JSONObject> response = elasticSearchService.selectByIdAndFilterFiled(indexName, patientSn, "patientSn");
            //if (response.source() == null) {
            patientExtractedRecord(patientSn, patientMap);
            return patientMap;
            //} else {
            //return null;
            //}
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public List<Map<String, Object>> saveVisitRecordToElasticSearch(String patientSn, String indexName) throws IOException {
        List<Map<String, Object>> data = new ArrayList<>();
        CommonResult<List<PatientModelDefineVo>> modelVariableTreeConfig = patientModelDefineService.getModelVariableTreeConfig();
        List<PatientModelDefineVo> patientModelDefineVoList = modelVariableTreeConfig.getData();
        List<Map<String, Object>> list = selectData(patientSn, null, "visit_information");
        if (CollectionUtil.isNotEmpty(list)) {
            for (Map<String, Object> map : list) {
                Map<String, Object> visitMap = new HashMap<>();
                Object visitSn = map.get("visitSn");
                GetResponse<JSONObject> response = elasticSearchService.selectByIdAndFilterFiled(indexName, visitSn.toString(), "visitSn");
                if (response.source() == null) {
                    // 设置患者基本信息
                    visitMap.put("patients", demographyInformationMapper.getDemographyInformationByPatientSn(patientSn));
                    // 获取访问SN
                    // 将当前访问信息合并到patientMap中,同时插入一条
                    visitMap.put("visitSn", visitSn);
                    visitMap.put("patientSn", patientSn);
                    visitMap.put("visitInformation", map);
                    visitMap.put("docType", "visit");
                    for (PatientModelDefineVo patientModelDefineVo : patientModelDefineVoList) {
                        if (!"visit_information".equals(patientModelDefineVo.getVariableCode()) && !"demography_information".equals(patientModelDefineVo.getVariableCode())) {
                            List<Map<String, Object>> dataList = selectData(patientSn, visitSn.toString(), patientModelDefineVo.getVariableCode());
                            if (CommomDataModelConstants.PATIENT_360_VIEW_SINGLE.equals(patientModelDefineVo.getExpand())) {
                                if (dataList.size() == 1) {
                                    visitMap.put(StrUtil.toCamelCase(patientModelDefineVo.getVariableCode()), JSON.toJSON(dataList.get(0)));
                                } else {
                                    visitMap.put(StrUtil.toCamelCase(patientModelDefineVo.getVariableCode()), JSON.toJSON(new HashMap<>()));
                                }
                            } else {
                                visitMap.put(StrUtil.toCamelCase(patientModelDefineVo.getVariableCode()), JSON.toJSON(dataList));
                            }
                        }
                    }
                    Map<String, String> relation = new HashMap<>();
                    relation.put("name", "visit");
                    relation.put("parent", patientSn);
                    visitMap.put("relation", relation);
                    data.add(visitMap);
                }
            }
        }
        return data;
    }

    private void patientExtractedRecord(String patientSn, Map<String, Object> patientMap) {
        patientMap.put("patientSn", patientSn);
        List<PatientModelDefine> patientModelSourceCodeConfigList = patientModelDefineService.getPatientModelSourceCodeConfigList();
        for (PatientModelDefine patientModelDefine : patientModelSourceCodeConfigList) {
            List<Map<String, Object>> list = selectData(patientSn, null, patientModelDefine.getModelSourceCode());
            // 如果数据只有一条，返回对象，否则是集合。
            if (patientModelDefine.getModelSourceCode().equals("demography_information")) {
                patientMap.put(StrUtil.toCamelCase(patientModelDefine.getModelSourceCode()), JSON.toJSON(list.get(0)));
            } else {
                patientMap.put(StrUtil.toCamelCase(patientModelDefine.getModelSourceCode()), JSON.toJSON(list));
            }
        }
        Map<String, String> relation = new HashMap<>();
        relation.put("name", "patient");
        patientMap.put("relation", relation);
    }


    private List<Map<String, Object>> selectData(String patientSn, String visitSn, String moduleSourceCode) {
        PatientModelVariableExample example = new PatientModelVariableExample();
        PatientModelVariableExample.Criteria criteria = example.createCriteria();
        criteria.andModelSourceCodeEqualTo(moduleSourceCode);
        List<PatientModelVariable> variables = patientModelVariableMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(variables)) {
            // 把所有的变量封装到list中
            List<String> vs = variables.stream().map(PatientModelVariable::getVariableCode).collect(Collectors.toList());
            // 开始拼接sql，字段返回的名字为驼峰命名
            StringBuilder sql = new StringBuilder("select");
            for (PatientModelVariable v : variables) {
                if (v.getVariableType().contains("timestamp") || v.getVariableType().contains("date")) {
                    sql.append(" to_char(" + v.getVariableCode() + ", 'yyyy-MM-dd HH:mm:ss')").append(" as \"").append(StrUtil.toCamelCase(v.getVariableCode())).append("\" ,");
                } else {
                    sql.append(" ").append(v.getVariableCode()).append(" as \"").append(StrUtil.toCamelCase(v.getVariableCode())).append("\" ,");
                }
            }
            sql = new StringBuilder(sql.substring(0, sql.length() - 1));
            sql.append(" from ").append(moduleSourceCode).append(" where patient_sn='").append(patientSn).append("'");
            if (StringUtils.isNotEmpty(visitSn)) {
                sql.append(" and visit_sn='").append(visitSn).append("'");
            }
            // 根据sql 查询数据返回一个map
            return patientNaPiSearchMapper.selectList(sql.toString());
        }
        return new ArrayList<>();
    }

}
