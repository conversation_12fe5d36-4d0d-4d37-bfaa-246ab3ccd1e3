package com.haoys.xinjiang.service;

import com.haoys.user.common.api.CommonResult;
import com.haoys.xinjiang.domain.vo.PatientVisitVo;

import java.util.List;
import java.util.Map;

public interface PatientVisitInfoService {

    /**
     * 获取就诊信息
     * @param patientId 患者id
     * @return
     */
    CommonResult<List<PatientVisitVo>> list(String patientId);

    /**
     * 统计就诊人数
     * @param visitType 就诊类型
     * @return
     */
    Long countPatient(String visitType);
    /**
     * 统计就诊人数(月份统计)
     * @param months 月份
     * @return
     */
    Long countQuarter(List<Integer> months);

    /**
     * 统计就诊人数(月份统计)(PostGreSQL数据库)
     * @param months 月份
     * @return
     */
    Long countPgSqlQuarter(List<Integer> months);

    /**
     * 统计就诊人数(年份统计)
     *
     * @param year      年份
     * @param visitType 就诊类型
     * @return
     */
    Map<Integer, Map<String, Long>> countVisitInfoByYear(String year, String visitType);

    /**
     * 统计患者人数(年份统计)(PostGreSQL数据库)
     * @param year 年份
     * @return
     */
    Map<Integer, Map<String, Long>> countPgSqlVisitInfoByYear(String year, String visitType);

    /**
     * 统计患者人数(年份统计)
     * @param year 年份
     * @return
     */
    Map<Integer, Map<String, Long>> countPatientByYear(String year);

    /**
     * 统计患者人数(年份统计)(PostGreSQL数据库)
     * @param year 年份
     * @return
     */
    Map<Integer, Map<String, Long>> countPgSqlPatientByYear(String year);

}
