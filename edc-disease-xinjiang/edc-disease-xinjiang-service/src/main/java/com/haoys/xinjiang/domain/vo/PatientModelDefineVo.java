package com.haoys.xinjiang.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PatientModelDefineVo {

    @ApiModelProperty(value = "字段id")
    private String variableId;

    @ApiModelProperty(value = "字段code")
    private String variableCode;
    
    private String parentVariableCode;

    @ApiModelProperty(value = "字段名称")
    private String variableName;

    @ApiModelProperty(value = "字段类型")
    private String variableType;

    @ApiModelProperty(value = "是否默认查询")
    private Boolean defaultQuery = false;
    
    private String expand;

    private List<PatientModelDefineVo> variables;
}
