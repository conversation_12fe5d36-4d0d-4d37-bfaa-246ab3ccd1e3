package com.haoys.xinjiang.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.haoys.user.common.api.CommonResult;
import com.haoys.xinjiang.domain.vo.PatientVisitVo;
import com.haoys.xinjiang.mapper.VisitInformationMapper;
import com.haoys.xinjiang.model.VisitInformationExample;
import com.haoys.xinjiang.service.PatientVisitInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@DS("disease_xinjiang")
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class PatientVisitInfoServiceImpl implements PatientVisitInfoService {

    private final VisitInformationMapper visitInformationMapper;
    
    @Override
    public CommonResult<List<PatientVisitVo>> list(String patientId) {
        return null;
    }
    
    /**
     * 统计就诊人数
     * @param visitType 就诊类型
     * @return
     */
    @Override
    public Long countPatient(String visitType) {
        VisitInformationExample example = new VisitInformationExample();
        VisitInformationExample.Criteria criteria = example.createCriteria();
        criteria.andVisitTypeEqualTo(visitType);
        return visitInformationMapper.countByExample(example);
    }

    /**
     * 统计就诊人数
     * @param months 月份
     * @return
     */
    @Override
    public Long countQuarter(List<Integer> months) {
        return null;
    }
    /**
     * 统计就诊人数 (PostGreSQL数据库)
     * @param months 月份
     * @return、||
     */
    @Override
    public Long countPgSqlQuarter(List<Integer> months) {
        return null;
    }

    @Override
    public Map<Integer, Map<String, Long>> countVisitInfoByYear(String year, String visitType) {
        return null;
    }
    /**
     * 统计患者人数(年份统计)(PostGreSQL数据库)
     * @param year 年份
     * @return
     */
    @Override
    public Map<Integer, Map<String, Long>> countPgSqlVisitInfoByYear(String year, String visitType) {
        return null;
    }

    /**
     * 统计患者人数(年份统计)
     * @param year 年份
     * @return
     */
    @Override
    public Map<Integer, Map<String, Long>> countPatientByYear(String year) {
        return null;
    }


    /**
     * 统计患者人数(年份统计)(PostGreSQL数据库)
     * @param year 年份
     * @return
     */
    @Override
    public Map<Integer, Map<String, Long>> countPgSqlPatientByYear(String year) {
        return null;
    }
}
