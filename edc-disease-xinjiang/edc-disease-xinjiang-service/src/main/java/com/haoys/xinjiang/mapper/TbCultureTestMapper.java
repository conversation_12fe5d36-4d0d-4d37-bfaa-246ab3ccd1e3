package com.haoys.xinjiang.mapper;

import com.haoys.xinjiang.model.TbCultureTest;
import com.haoys.xinjiang.model.TbCultureTestExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TbCultureTestMapper {
    long countByExample(TbCultureTestExample example);

    int deleteByExample(TbCultureTestExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TbCultureTest record);

    int insertSelective(TbCultureTest record);

    List<TbCultureTest> selectByExample(TbCultureTestExample example);

    TbCultureTest selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TbCultureTest record, @Param("example") TbCultureTestExample example);

    int updateByExample(@Param("record") TbCultureTest record, @Param("example") TbCultureTestExample example);

    int updateByPrimaryKeySelective(TbCultureTest record);

    int updateByPrimaryKey(TbCultureTest record);
}