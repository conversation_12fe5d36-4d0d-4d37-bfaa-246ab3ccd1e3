package com.haoys.xinjiang.service;


import com.haoys.user.common.api.CommonResult;
import com.haoys.xinjiang.domain.dto.PatientNaPiSearchDto;
import com.haoys.xinjiang.domain.param.SearchExportParam;
import com.haoys.xinjiang.domain.param.SearchParam;
import com.haoys.xinjiang.model.PatientNapiSearch;

import java.util.List;

/**
 * 纳排搜索(实体表搜索的方式)
 */

public interface PatientNaPiSearchV1Service {

    /**
     * 保存或者更新搜索条件
     * @param dto
     * @return
     */
    CommonResult<Object> saveOrUpdateSearch(PatientNaPiSearchDto dto);

    /**
     * 列表
     * @return
     */
    CommonResult<List<PatientNapiSearch>> searchList();

    /**
     * 构建搜索条件
     * @return 返回搜索条件
     */
    String getWhere(PatientNapiSearch search);

    /**
     * 根据id进行删除
     * @param id 纳排搜索条件的id
     * @return
     */
    CommonResult<Object> remove(String id);

    /**
     * 根据id获取
     * @param id 搜索条件的id
     * @return
     */
    PatientNapiSearch getById(String id);

    /**
     * 根据搜索条件进行查询
     * @param param 搜索和显示信息
     * @return
     */
    CommonResult list(SearchParam param);

    /**
     * 导出
     * @param exportParam
     * @return
     */
    CommonResult export(SearchExportParam exportParam);
}
