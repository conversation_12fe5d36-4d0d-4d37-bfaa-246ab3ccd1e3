package com.haoys.xinjiang.mapper;

import com.haoys.xinjiang.model.AntituberculosisDrugTherapy;
import com.haoys.xinjiang.model.AntituberculosisDrugTherapyExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AntituberculosisDrugTherapyMapper {
    long countByExample(AntituberculosisDrugTherapyExample example);

    int deleteByExample(AntituberculosisDrugTherapyExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(AntituberculosisDrugTherapy record);

    int insertSelective(AntituberculosisDrugTherapy record);

    List<AntituberculosisDrugTherapy> selectByExample(AntituberculosisDrugTherapyExample example);

    AntituberculosisDrugTherapy selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") AntituberculosisDrugTherapy record, @Param("example") AntituberculosisDrugTherapyExample example);

    int updateByExample(@Param("record") AntituberculosisDrugTherapy record, @Param("example") AntituberculosisDrugTherapyExample example);

    int updateByPrimaryKeySelective(AntituberculosisDrugTherapy record);

    int updateByPrimaryKey(AntituberculosisDrugTherapy record);
}