package com.haoys.xinjiang.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class TbAntibodyTest implements Serializable {
    private Integer id;

    @ApiModelProperty(value = "同报告单id")
    private String labResultId;

    @ApiModelProperty(value = "检验日期")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date examDatetime;

    @ApiModelProperty(value = "报告日期")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportDatetime;

    @ApiModelProperty(value = "检验套餐名称")
    private String testGroupItems;

    @ApiModelProperty(value = "检验标本名称")
    private String spcmType;

    @ApiModelProperty(value = "检验项目名称")
    private String itemName;

    @ApiModelProperty(value = "检查结果原值")
    private String rawResult;

    @ApiModelProperty(value = "检查结果")
    private String result;

    @ApiModelProperty(value = "患者唯一标识")
    private String patientSn;

    @ApiModelProperty(value = "就诊唯一标识")
    private String visitSn;

    @ApiModelProperty(value = "pkid")
    private String pkid;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLabResultId() {
        return labResultId;
    }

    public void setLabResultId(String labResultId) {
        this.labResultId = labResultId;
    }

    public Date getExamDatetime() {
        return examDatetime;
    }

    public void setExamDatetime(Date examDatetime) {
        this.examDatetime = examDatetime;
    }

    public Date getReportDatetime() {
        return reportDatetime;
    }

    public void setReportDatetime(Date reportDatetime) {
        this.reportDatetime = reportDatetime;
    }

    public String getTestGroupItems() {
        return testGroupItems;
    }

    public void setTestGroupItems(String testGroupItems) {
        this.testGroupItems = testGroupItems;
    }

    public String getSpcmType() {
        return spcmType;
    }

    public void setSpcmType(String spcmType) {
        this.spcmType = spcmType;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getRawResult() {
        return rawResult;
    }

    public void setRawResult(String rawResult) {
        this.rawResult = rawResult;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }

    public String getVisitSn() {
        return visitSn;
    }

    public void setVisitSn(String visitSn) {
        this.visitSn = visitSn;
    }

    public String getPkid() {
        return pkid;
    }

    public void setPkid(String pkid) {
        this.pkid = pkid;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", labResultId=").append(labResultId);
        sb.append(", examDatetime=").append(examDatetime);
        sb.append(", reportDatetime=").append(reportDatetime);
        sb.append(", testGroupItems=").append(testGroupItems);
        sb.append(", spcmType=").append(spcmType);
        sb.append(", itemName=").append(itemName);
        sb.append(", rawResult=").append(rawResult);
        sb.append(", result=").append(result);
        sb.append(", patientSn=").append(patientSn);
        sb.append(", visitSn=").append(visitSn);
        sb.append(", pkid=").append(pkid);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}