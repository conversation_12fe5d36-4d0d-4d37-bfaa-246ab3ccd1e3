package com.haoys.xinjiang.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

public class HormoneTherapyInfo implements Serializable {
    private Integer id;

    @ApiModelProperty(value = "激素信息.药物名称名称")
    private String drugName;

    @ApiModelProperty(value = "激素信息.药物剂量")
    private String drugDose;

    @ApiModelProperty(value = "患者唯一标识")
    private String patientSn;

    @ApiModelProperty(value = "就诊唯一标识")
    private String visitSn;

    @ApiModelProperty(value = "pkid")
    private String pkid;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDrugName() {
        return drugName;
    }

    public void setDrugName(String drugName) {
        this.drugName = drugName;
    }

    public String getDrugDose() {
        return drugDose;
    }

    public void setDrugDose(String drugDose) {
        this.drugDose = drugDose;
    }

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }

    public String getVisitSn() {
        return visitSn;
    }

    public void setVisitSn(String visitSn) {
        this.visitSn = visitSn;
    }

    public String getPkid() {
        return pkid;
    }

    public void setPkid(String pkid) {
        this.pkid = pkid;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", drugName=").append(drugName);
        sb.append(", drugDose=").append(drugDose);
        sb.append(", patientSn=").append(patientSn);
        sb.append(", visitSn=").append(visitSn);
        sb.append(", pkid=").append(pkid);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}