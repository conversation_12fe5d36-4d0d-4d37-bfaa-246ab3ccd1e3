package com.haoys.xinjiang.mapper;

import com.haoys.xinjiang.model.TbPathologyReport;
import com.haoys.xinjiang.model.TbPathologyReportExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TbPathologyReportMapper {
    long countByExample(TbPathologyReportExample example);

    int deleteByExample(TbPathologyReportExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TbPathologyReport record);

    int insertSelective(TbPathologyReport record);

    List<TbPathologyReport> selectByExample(TbPathologyReportExample example);

    TbPathologyReport selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TbPathologyReport record, @Param("example") TbPathologyReportExample example);

    int updateByExample(@Param("record") TbPathologyReport record, @Param("example") TbPathologyReportExample example);

    int updateByPrimaryKeySelective(TbPathologyReport record);

    int updateByPrimaryKey(TbPathologyReport record);
}