package com.haoys.xinjiang.mapper;

import com.haoys.xinjiang.model.KeyEvent;
import com.haoys.xinjiang.model.KeyEventExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface KeyEventMapper {
    long countByExample(KeyEventExample example);

    int deleteByExample(KeyEventExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(KeyEvent record);

    int insertSelective(KeyEvent record);

    List<KeyEvent> selectByExample(KeyEventExample example);

    KeyEvent selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") KeyEvent record, @Param("example") KeyEventExample example);

    int updateByExample(@Param("record") KeyEvent record, @Param("example") KeyEventExample example);

    int updateByPrimaryKeySelective(KeyEvent record);

    int updateByPrimaryKey(KeyEvent record);
}