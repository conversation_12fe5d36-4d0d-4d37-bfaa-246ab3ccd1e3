package com.haoys.xinjiang.mapper;

import com.haoys.xinjiang.model.MicroCultureDrugSensitivityTestRecord;
import com.haoys.xinjiang.model.MicroCultureDrugSensitivityTestRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MicroCultureDrugSensitivityTestRecordMapper {
    long countByExample(MicroCultureDrugSensitivityTestRecordExample example);

    int deleteByExample(MicroCultureDrugSensitivityTestRecordExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(MicroCultureDrugSensitivityTestRecord record);

    int insertSelective(MicroCultureDrugSensitivityTestRecord record);

    List<MicroCultureDrugSensitivityTestRecord> selectByExample(MicroCultureDrugSensitivityTestRecordExample example);

    MicroCultureDrugSensitivityTestRecord selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") MicroCultureDrugSensitivityTestRecord record, @Param("example") MicroCultureDrugSensitivityTestRecordExample example);

    int updateByExample(@Param("record") MicroCultureDrugSensitivityTestRecord record, @Param("example") MicroCultureDrugSensitivityTestRecordExample example);

    int updateByPrimaryKeySelective(MicroCultureDrugSensitivityTestRecord record);

    int updateByPrimaryKey(MicroCultureDrugSensitivityTestRecord record);
}