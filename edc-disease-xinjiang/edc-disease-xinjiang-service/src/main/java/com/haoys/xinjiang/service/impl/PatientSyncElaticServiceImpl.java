package com.haoys.xinjiang.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.mapping.*;
import co.elastic.clients.elasticsearch.core.GetResponse;
import co.elastic.clients.elasticsearch.indices.CreateIndexResponse;
import co.elastic.clients.json.JsonData;
import co.elastic.clients.transport.endpoints.BooleanResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.elasticsearch.ElasticSearchService;
import com.haoys.user.storge.cloud.OssStorageConfig;
import com.haoys.xinjiang.domain.vo.PatientModelVariableVo;
import com.haoys.xinjiang.mapper.*;
import com.haoys.xinjiang.model.PatientModelDefine;
import com.haoys.xinjiang.model.PatientModelDefineExample;
import com.haoys.xinjiang.model.PatientModelVariable;
import com.haoys.xinjiang.model.PatientModelVariableExample;
import com.haoys.xinjiang.service.RatientSyncElaticService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

@Slf4j
@DS("disease_xinjiang")
@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class PatientSyncElaticServiceImpl extends BaseService implements RatientSyncElaticService {

    private final DemographyInformationMapper patientsMapper;
    private final OssStorageConfig ossStorageConfig;
    private final ElasticSearchService elasticSearchService;
    private final PatientNapiSearchMapper patientNaPiSearchMapper;
    @Resource(name = "clientByApiKey")
    private ElasticsearchClient elasticsearchClient;

    private final PatientModelDefineMapper modelDefineMapper;
    private final PatientModelVariableMapper variableMapper;



    /**
     * 根据表单模型创建索引
     * @param indexName 索引名称
     * @param isNested 是否是嵌套类型
     * @return 是否创建成功
     * @throws Exception
     */
    @Override
    public boolean createIndex(String indexName, boolean isNested,boolean isCreateRelation) throws IOException {
        BooleanResponse exists = elasticsearchClient.indices().exists(ex -> ex.index(indexName));
        if (exists.value()){
            elasticsearchClient.indices().delete(deleteIndexBuilder -> deleteIndexBuilder.index(indexName));
        }
        // 获取索引的mapping
        Map<String, Property> documentMap = getElaticIndexMapping(indexName, isNested);
        // 创建父子关系（如果没有创建了也没事）
        if (isCreateRelation){
            List<String>  relation = new ArrayList<>();
            relation.add("visit");
            documentMap.put("relation", Property.of(property ->
                    property.join(nes->nes.relations(MapUtil.of("patient",relation))))
            );
        }
        CreateIndexResponse response0 = elasticsearchClient.indices().create(createIndexBuilder -> createIndexBuilder
                .index(indexName)
                .mappings(mappings ->
                        mappings.properties(documentMap))
                .settings(settings -> settings.otherSettings(getIndexSettingMap()))
        );
        return response0.acknowledged();
    }



    /**
     * 同步患者视图索引到ES
     * @param patientSn 患者编号
     * @param indexName 索引名称
     * @param isNested 是否是嵌套类型
     * @return 单个患者所有的信息文档
     * @throws IOException
     */
    @Override
    public Map<String, Object> syncPatientToElasticSearch(String patientSn, String indexName, boolean isNested,List<PatientModelDefine> modelDefineVos) throws IOException {
        Map<String, Object> patientMap = new HashMap<>();
        // 校验是否已经存在
        GetResponse<JSONObject> response = elasticSearchService.selectByIdAndFilterFiled(indexName, patientSn, "patientSn");
        if (response.source() == null) {
            patientMap.put("patientSn", patientSn);
            patientMap.put("docType", "patient");
            // 获取表单定义表信息
            for (PatientModelDefine vo : modelDefineVos) {
                    // 查询该表下的所有的变量
                List<Map<String, Object>> list = selectData(patientSn, null, vo.getModelSourceCode());
                // 如果数据只有一条，返回对象，否则是集合。
                if (vo.getModelSourceCode().equals("demography_information")) {
                    patientMap.put(StrUtil.toCamelCase(vo.getModelSourceCode()), JSON.toJSON(list.get(0)));
                } else {
                    patientMap.put(StrUtil.toCamelCase(vo.getModelSourceCode()), JSON.toJSON(list));
                }
            }
            Map<String,String> relation = new HashMap<>();
            relation.put("name","patient");
            patientMap.put("relation",relation);
        }else {
            return null;
        }
        return patientMap;
    }


    /**
     * 同步病历视图索引到ES
     * @param patientSn 患者编号
     * @param indexName 索引名称
     * @param isNested 是否是嵌套类型
     * @return 单个患者 多个病例集合
     * @throws IOException
     */
    @Override
    public List<Map<String, Object>> syncVisitToElasticSearch(String patientSn, String indexName, boolean isNested, List<PatientModelDefine> modelDefineVos) throws IOException {

        List<Map<String, Object>> data = new ArrayList<>();
        // 校验是否已经存在
        // 根据患者SN查询住院访问信息
        List<Map<String, Object>> list = selectData(patientSn, null, "visit_information");
        // 检查是否有住院访问信息
        if (CollectionUtil.isNotEmpty(list)) {
            // 遍历住院访问信息列表
            for (Map<String, Object> map : list) {
                Map<String, Object> visitMap = new HashMap<>();
                // 检查访问SN是否存在
                Object visitSn = map.get("visitSn");
                Object visitDate = map.get("visitOrAdmissionDatetime");
                GetResponse<JSONObject> response = elasticSearchService.selectByIdAndFilterFiled(indexName, visitSn.toString(), "visitSn");

                if (response.source() == null) {
                    // 设置患者基本信息
                    visitMap.put("demographyInformation", patientsMapper.getDemographyInformationByPatientSn(patientSn));
                    // 获取访问SN
                    // 将当前访问信息合并到patientMap中,同时插入一条
                    visitMap.put("visitSn", visitSn);
                    visitMap.put("visitDate", visitDate);
                    visitMap.put("patientSn", patientSn);
                    visitMap.put("visitInformation", map);
                    visitMap.put("docType", "visit");
                    // 创建一个对象用于存储患者数据

                    // 遍历模型变量配置信息
                    for (PatientModelDefine vo : modelDefineVos) {
                        if (!"visit_information".equals(vo.getModelSourceCode()) && !"demography_information".equals(vo.getModelSourceCode())) {
                            // 根据患者SN和访问SN查询变量数据
                            // 查询该表下的所有的变量
                            List<Map<String, Object>> dataList = selectData(patientSn, visitSn.toString(), vo.getModelSourceCode());
                            // 根据数据条数决定是存储单个对象还是对象列表
                            if (vo.getExpand().equals("1")){
                                if (dataList.size()==1){
                                    visitMap.put(StrUtil.toCamelCase(vo.getModelSourceCode()), JSON.toJSON(dataList.get(0)));
                                }else {
                                    visitMap.put(StrUtil.toCamelCase(vo.getModelSourceCode()), JSON.toJSON(new HashMap<>()));
                                }
                            }else {
                                visitMap.put(StrUtil.toCamelCase(vo.getModelSourceCode()), JSON.toJSON(dataList));
                            }
                        }
                    }
                    Map<String, String> relation = new HashMap<>();
                    relation.put("name", "visit");
                    relation.put("parent", patientSn);
                    visitMap.put("relation", relation);
                    data.add(visitMap);
                }
            }
        }
        return data;
    }


    /**
     * 获取索引的mapping
     * @param indexName 索引名称
     * @param isNested 是否为嵌套模型
     * @return
     * @throws IOException
     */
    @NotNull
    private Map<String, Property> getElaticIndexMapping(String indexName, boolean isNested) throws IOException {
        List<PatientModelDefine> modelDefineVos = modelDefineMapper.selectByExample(new PatientModelDefineExample());
        Map<String, Property> documentMap = new HashMap<>();
        documentMap.put("visitSn", Property.of(property ->
                property.keyword(KeywordProperty.of(keywordProperty ->
                        keywordProperty.ignoreAbove(256).index(true))))
        );
        documentMap.put("patientSn", Property.of(property ->
                property.keyword(KeywordProperty.of(keywordProperty ->
                        keywordProperty.ignoreAbove(256).index(true))))
        );
        documentMap.put("docType", Property.of(property ->
                property.keyword(KeywordProperty.of(keywordProperty ->
                        keywordProperty.ignoreAbove(256).index(true))))
        );
        if (CollectionUtil.isNotEmpty(modelDefineVos)) {
            for (PatientModelDefine modelDefineVo : modelDefineVos) {
                Map<String, Property> filedMap = new HashMap<>();
                List<PatientModelVariableVo> variableList = modelDefineMapper.getPatientVariableConfigListByModelSourceCode(modelDefineVo.getModelSourceCode(), false);
                if (!variableList.isEmpty()) {
                    for (PatientModelVariableVo variable : variableList) {
                        String filed = StrUtil.toCamelCase(variable.getVariableCode());
                        List<String> variableTypeList = Arrays.asList("int4", "float8", "varchar[]", "int[]", "float[]", "varchar");
                        if (variableTypeList.contains(variable.getVariableType())) {
                            Map<String, Property> dataMap = new HashMap<>();
                            dataMap.put("keyword", Property.of(property -> property.keyword(data -> data.index(true).ignoreAbove(256))));
                            filedMap.put(filed, Property.of(property ->
                                            property.text(TextProperty.of(textProperty ->
                                                            textProperty.fields(dataMap).analyzer("ik_max_word").searchAnalyzer("ik_max_word")
                                                    )
                                            )
                                    )
                            );
                        }
                        if (variable.getVariableType().contains("int") || variable.getVariableType().contains("float")) {
                            // 文本字段
                            filedMap.put(filed, Property.of(property ->
                                    property.integer(IntegerNumberProperty.of(integerNumberProperty
                                            -> integerNumberProperty.index(true))))
                            );
                        }
                        if (variable.getVariableType().contains("timestamp") || variable.getVariableType().contains("date")) {
                            filedMap.put(filed, Property.of(property ->
                                    property.date(DateProperty.of(dateProperty
                                            -> dateProperty.index(true).format("yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"))))
                            );
                        }
                        if (variable.getVariableType().contains("bool")) {
                            filedMap.put(filed, Property.of(property ->
                                    property.boolean_(BooleanProperty.of(bool
                                            -> bool.index(true))))
                            );
                        }
                    }
                }
                if (isNested) {
                    documentMap.put(StrUtil.toCamelCase(modelDefineVo.getModelSourceCode()), Property.of(property ->
                            property.nested(nes -> nes.properties(filedMap)))
                    );
                } else {
                    documentMap.put(StrUtil.toCamelCase(modelDefineVo.getModelSourceCode()), Property.of(property ->
                            property.object(obj -> obj.properties(filedMap)))
                    );
                }
            }
        }
        return documentMap;
    }

    /**
     * 设置ES索引字段长度，和索引字段数量，文档大小等防止大文档存入报错
     * @return
     */
    @NotNull
    private static Map<String, JsonData> getIndexSettingMap() {
        Map<String,JsonData> settingMap = new HashMap<>();
        settingMap.put("index.mapping.total_fields.limit", JsonData.fromJson("50000"));
        settingMap.put("index.mapping.nested_objects.limit", JsonData.fromJson("100000"));
        settingMap.put("index.mapping.nested_fields.limit", JsonData.fromJson("50000"));
        settingMap.put("index.highlight.max_analyzed_offset", JsonData.fromJson("100000000"));
        return settingMap;
    }

    /**
     * 根据表单定义表信息，查询数据
     * @param patientSn 患者编号
     * @param visitSn 就诊编号
     * @param moduleSourceCode 表单定义表code
     * @return 数据集合
     */
    private List<Map<String, Object>> selectData(String patientSn, String visitSn, String moduleSourceCode) {
        // 获取就诊信息
        PatientModelVariableExample example = new PatientModelVariableExample();
        PatientModelVariableExample.Criteria criteria = example.createCriteria();
        criteria.andModelSourceCodeEqualTo(moduleSourceCode);
        criteria.andCustomVariableEqualTo(false);
        List<PatientModelVariable> variables = variableMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(variables)) {
            // 开始拼接sql，字段返回的名字为驼峰命名
            return getData(patientSn, visitSn, variables);
        }
        return new ArrayList<>();
    }

    public List<Map<String, Object>> getData(String patientSn, String visitSn,List<PatientModelVariable> variables) {
        StringBuilder sql = new StringBuilder("select");
        for (PatientModelVariable v : variables) {
            if (!v.getCustomVariable()){
                if (v.getVariableType().contains("timestamp") || v.getVariableType().contains("date")) {
                    sql.append(" to_char("+v.getVariableCode()+", 'yyyy-MM-dd HH:mm:ss')").append(" as \"").append(StrUtil.toCamelCase(v.getVariableCode())).append("\" ,");
                }else {
                    sql.append(" ").append(v.getVariableCode()).append(" as \"").append(StrUtil.toCamelCase(v.getVariableCode())).append("\" ,");
                }
            }
        }
        sql = new StringBuilder(sql.substring(0, sql.length() - 1));
        sql.append(" from ").append(variables.get(0).getModelSourceCode()).append(" where patient_sn='").append(patientSn).append("'");
        if (StringUtils.isNotEmpty(visitSn)) {
            sql.append(" and visit_sn='").append(visitSn).append("'");
        }
        // 根据sql 查询数据返回一个map
        return patientNaPiSearchMapper.selectList(sql.toString());
    }
}
