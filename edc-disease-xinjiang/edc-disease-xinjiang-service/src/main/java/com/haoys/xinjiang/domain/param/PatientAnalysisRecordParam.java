package com.haoys.xinjiang.domain.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class PatientAnalysisRecordParam implements Serializable {

    @ApiModelProperty(value = "主键id")
    private String dataSetId;

    @ApiModelProperty(value = "主键id")
    private String diseaseType;

    @ApiModelProperty(value = "数据集code")
    private String batchCode;

    @ApiModelProperty(value = "患者id数据记录")
    private List<String> patientIds = new ArrayList<>();

    @ApiModelProperty(value = "描述")
    private String description;

}