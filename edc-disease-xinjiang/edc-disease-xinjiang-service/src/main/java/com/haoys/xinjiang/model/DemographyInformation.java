package com.haoys.xinjiang.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class DemographyInformation implements Serializable {
    private Integer id;

    @ApiModelProperty(value = "患者唯一标识")
    private String patientSn;

    @ApiModelProperty(value = "患者姓名")
    private String name;

    @ApiModelProperty(value = "出生日期")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dateOfBirth;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "民族")
    private String nation;

    @ApiModelProperty(value = "婚姻状况")
    private String marriage;

    @ApiModelProperty(value = "国籍")
    private String citizenship;

    @ApiModelProperty(value = "ABO血型")
    private String aboBloodType;

    @ApiModelProperty(value = "RH血型")
    private String rhBloodType;

    @ApiModelProperty(value = "籍贯")
    private String nativePlace;

    @ApiModelProperty(value = "籍贯省(区、市)")
    private String nativePlaceProvince;

    @ApiModelProperty(value = "籍贯市")
    private String nativePlaceCity;

    @ApiModelProperty(value = "职业类型")
    private String occupationType;

    @ApiModelProperty(value = "文化程度")
    private String educationDegree;

    @ApiModelProperty(value = "现住址")
    private String homeAddress;

    @ApiModelProperty(value = "现住址-省(区、市)")
    private String homeAddressProvince;

    @ApiModelProperty(value = "现住址-市")
    private String homeAddressCity;

    @ApiModelProperty(value = "联系方式")
    private String contactPhone;

    @ApiModelProperty(value = "身份证号")
    private String idNo;

    @ApiModelProperty(value = "死亡时间")
    private String deathDatetime;

    @ApiModelProperty(value = "是否死亡")
    private Boolean isDeath;

    @ApiModelProperty(value = "死亡原因")
    private String deathCause;

    @ApiModelProperty(value = "住院次数")
    private String hospitalizationCount;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(Date dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getMarriage() {
        return marriage;
    }

    public void setMarriage(String marriage) {
        this.marriage = marriage;
    }

    public String getCitizenship() {
        return citizenship;
    }

    public void setCitizenship(String citizenship) {
        this.citizenship = citizenship;
    }

    public String getAboBloodType() {
        return aboBloodType;
    }

    public void setAboBloodType(String aboBloodType) {
        this.aboBloodType = aboBloodType;
    }

    public String getRhBloodType() {
        return rhBloodType;
    }

    public void setRhBloodType(String rhBloodType) {
        this.rhBloodType = rhBloodType;
    }

    public String getNativePlace() {
        return nativePlace;
    }

    public void setNativePlace(String nativePlace) {
        this.nativePlace = nativePlace;
    }

    public String getNativePlaceProvince() {
        return nativePlaceProvince;
    }

    public void setNativePlaceProvince(String nativePlaceProvince) {
        this.nativePlaceProvince = nativePlaceProvince;
    }

    public String getNativePlaceCity() {
        return nativePlaceCity;
    }

    public void setNativePlaceCity(String nativePlaceCity) {
        this.nativePlaceCity = nativePlaceCity;
    }

    public String getOccupationType() {
        return occupationType;
    }

    public void setOccupationType(String occupationType) {
        this.occupationType = occupationType;
    }

    public String getEducationDegree() {
        return educationDegree;
    }

    public void setEducationDegree(String educationDegree) {
        this.educationDegree = educationDegree;
    }

    public String getHomeAddress() {
        return homeAddress;
    }

    public void setHomeAddress(String homeAddress) {
        this.homeAddress = homeAddress;
    }

    public String getHomeAddressProvince() {
        return homeAddressProvince;
    }

    public void setHomeAddressProvince(String homeAddressProvince) {
        this.homeAddressProvince = homeAddressProvince;
    }

    public String getHomeAddressCity() {
        return homeAddressCity;
    }

    public void setHomeAddressCity(String homeAddressCity) {
        this.homeAddressCity = homeAddressCity;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getDeathDatetime() {
        return deathDatetime;
    }

    public void setDeathDatetime(String deathDatetime) {
        this.deathDatetime = deathDatetime;
    }

    public Boolean getIsDeath() {
        return isDeath;
    }

    public void setIsDeath(Boolean isDeath) {
        this.isDeath = isDeath;
    }

    public String getDeathCause() {
        return deathCause;
    }

    public void setDeathCause(String deathCause) {
        this.deathCause = deathCause;
    }

    public String getHospitalizationCount() {
        return hospitalizationCount;
    }

    public void setHospitalizationCount(String hospitalizationCount) {
        this.hospitalizationCount = hospitalizationCount;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", patientSn=").append(patientSn);
        sb.append(", name=").append(name);
        sb.append(", dateOfBirth=").append(dateOfBirth);
        sb.append(", gender=").append(gender);
        sb.append(", nation=").append(nation);
        sb.append(", marriage=").append(marriage);
        sb.append(", citizenship=").append(citizenship);
        sb.append(", aboBloodType=").append(aboBloodType);
        sb.append(", rhBloodType=").append(rhBloodType);
        sb.append(", nativePlace=").append(nativePlace);
        sb.append(", nativePlaceProvince=").append(nativePlaceProvince);
        sb.append(", nativePlaceCity=").append(nativePlaceCity);
        sb.append(", occupationType=").append(occupationType);
        sb.append(", educationDegree=").append(educationDegree);
        sb.append(", homeAddress=").append(homeAddress);
        sb.append(", homeAddressProvince=").append(homeAddressProvince);
        sb.append(", homeAddressCity=").append(homeAddressCity);
        sb.append(", contactPhone=").append(contactPhone);
        sb.append(", idNo=").append(idNo);
        sb.append(", deathDatetime=").append(deathDatetime);
        sb.append(", isDeath=").append(isDeath);
        sb.append(", deathCause=").append(deathCause);
        sb.append(", hospitalizationCount=").append(hospitalizationCount);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}