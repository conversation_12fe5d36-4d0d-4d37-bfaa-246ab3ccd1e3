package com.haoys.xinjiang.mapper;

import com.haoys.xinjiang.model.TbNucleicAcidTest;
import com.haoys.xinjiang.model.TbNucleicAcidTestExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TbNucleicAcidTestMapper {
    long countByExample(TbNucleicAcidTestExample example);

    int deleteByExample(TbNucleicAcidTestExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TbNucleicAcidTest record);

    int insertSelective(TbNucleicAcidTest record);

    List<TbNucleicAcidTest> selectByExample(TbNucleicAcidTestExample example);

    TbNucleicAcidTest selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TbNucleicAcidTest record, @Param("example") TbNucleicAcidTestExample example);

    int updateByExample(@Param("record") TbNucleicAcidTest record, @Param("example") TbNucleicAcidTestExample example);

    int updateByPrimaryKeySelective(TbNucleicAcidTest record);

    int updateByPrimaryKey(TbNucleicAcidTest record);
}