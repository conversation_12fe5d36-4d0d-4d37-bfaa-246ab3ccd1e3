package com.haoys.xinjiang.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class PatientModelVariable implements Serializable {
    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "表单模型code")
    private String modelSourceCode;

    @ApiModelProperty(value = "字段code")
    private String variableCode;

    @ApiModelProperty(value = "字段名称")
    private String variableName;

    @ApiModelProperty(value = "字段类型")
    private String variableType;

    @ApiModelProperty(value = "字段下拉框数据")
    private String comboboxData;

    @ApiModelProperty(value = "引用字典")
    private String dictionaryValue;

    @ApiModelProperty(value = "默认查询字段")
    private Boolean defaultQuery;

    @ApiModelProperty(value = "是否自定义表单字段")
    private Boolean customVariable;

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "字段排序")
    private Integer sort;

    @ApiModelProperty(value = "描述信息")
    private String description;

    @ApiModelProperty(value = "创建时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getModelSourceCode() {
        return modelSourceCode;
    }

    public void setModelSourceCode(String modelSourceCode) {
        this.modelSourceCode = modelSourceCode;
    }

    public String getVariableCode() {
        return variableCode;
    }

    public void setVariableCode(String variableCode) {
        this.variableCode = variableCode;
    }

    public String getVariableName() {
        return variableName;
    }

    public void setVariableName(String variableName) {
        this.variableName = variableName;
    }

    public String getVariableType() {
        return variableType;
    }

    public void setVariableType(String variableType) {
        this.variableType = variableType;
    }

    public String getComboboxData() {
        return comboboxData;
    }

    public void setComboboxData(String comboboxData) {
        this.comboboxData = comboboxData;
    }

    public String getDictionaryValue() {
        return dictionaryValue;
    }

    public void setDictionaryValue(String dictionaryValue) {
        this.dictionaryValue = dictionaryValue;
    }

    public Boolean getDefaultQuery() {
        return defaultQuery;
    }

    public void setDefaultQuery(Boolean defaultQuery) {
        this.defaultQuery = defaultQuery;
    }

    public Boolean getCustomVariable() {
        return customVariable;
    }

    public void setCustomVariable(Boolean customVariable) {
        this.customVariable = customVariable;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", modelSourceCode=").append(modelSourceCode);
        sb.append(", variableCode=").append(variableCode);
        sb.append(", variableName=").append(variableName);
        sb.append(", variableType=").append(variableType);
        sb.append(", comboboxData=").append(comboboxData);
        sb.append(", dictionaryValue=").append(dictionaryValue);
        sb.append(", defaultQuery=").append(defaultQuery);
        sb.append(", customVariable=").append(customVariable);
        sb.append(", groupName=").append(groupName);
        sb.append(", sort=").append(sort);
        sb.append(", description=").append(description);
        sb.append(", createTime=").append(createTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}