package com.haoys.xinjiang.mapper;

import com.haoys.xinjiang.domain.schmea.TableModelVo;
import com.haoys.xinjiang.domain.vo.PatientModelDefineGroup;
import com.haoys.xinjiang.domain.vo.PatientModelDefineVo;
import com.haoys.xinjiang.domain.vo.PatientModelVariableVo;
import com.haoys.xinjiang.domain.vo.PatientTableModelVo;
import com.haoys.xinjiang.model.PatientModelDefine;
import com.haoys.xinjiang.model.PatientModelDefineExample;
import com.haoys.xinjiang.model.PatientModelVariable;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public interface PatientModelDefineMapper {
    long countByExample(PatientModelDefineExample example);
    
    int deleteByExample(PatientModelDefineExample example);
    
    int deleteByPrimaryKey(String modelSourceId);
    
    int insert(PatientModelDefine record);
    
    int insertSelective(PatientModelDefine record);
    
    List<PatientModelDefine> selectByExample(PatientModelDefineExample example);
    
    PatientModelDefine selectByPrimaryKey(String modelSourceId);
    
    int updateByExampleSelective(@Param("record") PatientModelDefine record, @Param("example") PatientModelDefineExample example);
    
    int updateByExample(@Param("record") PatientModelDefine record, @Param("example") PatientModelDefineExample example);
    
    int updateByPrimaryKeySelective(PatientModelDefine record);
    
    int updateByPrimaryKey(PatientModelDefine record);
    
    /**
     * 通过modelSourceId修改表单模型
     * @param modelSourceId
     */
    void updatePatientModelDefineBySourceId(String modelSourceId);
    
    /**
     * 通过modelSourceId查询表单模型
     * @param modelSourceId
     * @return
     */
    PatientModelDefine getPatientModelDefineByModelSourceId(String modelSourceId);
    
    /**
     * 通过modelSourceCode查询表单模型
     * @param modelSourceCode
     * @return
     */
    PatientModelDefine getPatientModelDefineByModelSourceCode(String modelSourceCode);
    
    /**
     * 通过modelSourceCode查询表单字段列表
     *
     * @param modelSourceCode
     * @param enableCustomVariable
     * @return
     */
    List<PatientModelVariableVo> getPatientVariableConfigListByModelSourceCode(String modelSourceCode, boolean enableCustomVariable);
    
    List<TableModelVo> getTableModelDefineList(String dataScheme);
    
    List<TableModelVo> getTableModelColumnByModelCode(String tableName);
    
    PatientModelVariable getPatientVariableConfig(String modelSourceCode, String variableCode);
    
    List<PatientModelDefineVo> getPatientModelSourceCodeListForDisease();
    
    List<PatientModelVariableVo> getPatientDefaultModelVariableConfig();
    
    PatientModelVariable getPatientVariableConfigById(String modelVariableId);
    
    void updatePatientModelVariableByCondition(PatientModelVariable patientModelVariable);
    
    List<PatientModelDefineVo> getModelVariablesConfig(String modelSourceCode);
    
    List<PatientTableModelVo> getPatientTableModelConfigList();
    
    List<PatientTableModelVo> getTableModelColumnOrdinalPositionBySourceModelCode(String tableName);
    
    List<PatientTableModelVo> getTableModelColumnBySourceModelCode(String tableName);
    
    List<PatientModelDefineGroup> getModelSourceCodeListByOwnerGroup(String groupCode);
    
    void truncateTable();
    
}