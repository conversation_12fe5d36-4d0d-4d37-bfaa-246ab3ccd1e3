package com.haoys.xinjiang.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DischargeRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public DischargeRecordExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("\"id\" is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("\"id\" is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("\"id\" =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("\"id\" <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("\"id\" >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"id\" >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("\"id\" <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("\"id\" <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("\"id\" in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("\"id\" not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("\"id\" between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("\"id\" not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeIsNull() {
            addCriterion("\"admission_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeIsNotNull() {
            addCriterion("\"admission_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeEqualTo(Date value) {
            addCriterion("\"admission_datetime\" =", value, "admissionDatetime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeNotEqualTo(Date value) {
            addCriterion("\"admission_datetime\" <>", value, "admissionDatetime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeGreaterThan(Date value) {
            addCriterion("\"admission_datetime\" >", value, "admissionDatetime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"admission_datetime\" >=", value, "admissionDatetime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeLessThan(Date value) {
            addCriterion("\"admission_datetime\" <", value, "admissionDatetime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"admission_datetime\" <=", value, "admissionDatetime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeIn(List<Date> values) {
            addCriterion("\"admission_datetime\" in", values, "admissionDatetime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeNotIn(List<Date> values) {
            addCriterion("\"admission_datetime\" not in", values, "admissionDatetime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"admission_datetime\" between", value1, value2, "admissionDatetime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"admission_datetime\" not between", value1, value2, "admissionDatetime");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeIsNull() {
            addCriterion("\"discharge_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeIsNotNull() {
            addCriterion("\"discharge_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeEqualTo(Date value) {
            addCriterion("\"discharge_datetime\" =", value, "dischargeDatetime");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeNotEqualTo(Date value) {
            addCriterion("\"discharge_datetime\" <>", value, "dischargeDatetime");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeGreaterThan(Date value) {
            addCriterion("\"discharge_datetime\" >", value, "dischargeDatetime");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"discharge_datetime\" >=", value, "dischargeDatetime");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeLessThan(Date value) {
            addCriterion("\"discharge_datetime\" <", value, "dischargeDatetime");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"discharge_datetime\" <=", value, "dischargeDatetime");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeIn(List<Date> values) {
            addCriterion("\"discharge_datetime\" in", values, "dischargeDatetime");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeNotIn(List<Date> values) {
            addCriterion("\"discharge_datetime\" not in", values, "dischargeDatetime");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"discharge_datetime\" between", value1, value2, "dischargeDatetime");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"discharge_datetime\" not between", value1, value2, "dischargeDatetime");
            return (Criteria) this;
        }

        public Criteria andAdmissionStatusIsNull() {
            addCriterion("\"admission_status\" is null");
            return (Criteria) this;
        }

        public Criteria andAdmissionStatusIsNotNull() {
            addCriterion("\"admission_status\" is not null");
            return (Criteria) this;
        }

        public Criteria andAdmissionStatusEqualTo(String value) {
            addCriterion("\"admission_status\" =", value, "admissionStatus");
            return (Criteria) this;
        }

        public Criteria andAdmissionStatusNotEqualTo(String value) {
            addCriterion("\"admission_status\" <>", value, "admissionStatus");
            return (Criteria) this;
        }

        public Criteria andAdmissionStatusGreaterThan(String value) {
            addCriterion("\"admission_status\" >", value, "admissionStatus");
            return (Criteria) this;
        }

        public Criteria andAdmissionStatusGreaterThanOrEqualTo(String value) {
            addCriterion("\"admission_status\" >=", value, "admissionStatus");
            return (Criteria) this;
        }

        public Criteria andAdmissionStatusLessThan(String value) {
            addCriterion("\"admission_status\" <", value, "admissionStatus");
            return (Criteria) this;
        }

        public Criteria andAdmissionStatusLessThanOrEqualTo(String value) {
            addCriterion("\"admission_status\" <=", value, "admissionStatus");
            return (Criteria) this;
        }

        public Criteria andAdmissionStatusLike(String value) {
            addCriterion("\"admission_status\" like", value, "admissionStatus");
            return (Criteria) this;
        }

        public Criteria andAdmissionStatusNotLike(String value) {
            addCriterion("\"admission_status\" not like", value, "admissionStatus");
            return (Criteria) this;
        }

        public Criteria andAdmissionStatusIn(List<String> values) {
            addCriterion("\"admission_status\" in", values, "admissionStatus");
            return (Criteria) this;
        }

        public Criteria andAdmissionStatusNotIn(List<String> values) {
            addCriterion("\"admission_status\" not in", values, "admissionStatus");
            return (Criteria) this;
        }

        public Criteria andAdmissionStatusBetween(String value1, String value2) {
            addCriterion("\"admission_status\" between", value1, value2, "admissionStatus");
            return (Criteria) this;
        }

        public Criteria andAdmissionStatusNotBetween(String value1, String value2) {
            addCriterion("\"admission_status\" not between", value1, value2, "admissionStatus");
            return (Criteria) this;
        }

        public Criteria andTreatProcessIsNull() {
            addCriterion("\"treat_process\" is null");
            return (Criteria) this;
        }

        public Criteria andTreatProcessIsNotNull() {
            addCriterion("\"treat_process\" is not null");
            return (Criteria) this;
        }

        public Criteria andTreatProcessEqualTo(String value) {
            addCriterion("\"treat_process\" =", value, "treatProcess");
            return (Criteria) this;
        }

        public Criteria andTreatProcessNotEqualTo(String value) {
            addCriterion("\"treat_process\" <>", value, "treatProcess");
            return (Criteria) this;
        }

        public Criteria andTreatProcessGreaterThan(String value) {
            addCriterion("\"treat_process\" >", value, "treatProcess");
            return (Criteria) this;
        }

        public Criteria andTreatProcessGreaterThanOrEqualTo(String value) {
            addCriterion("\"treat_process\" >=", value, "treatProcess");
            return (Criteria) this;
        }

        public Criteria andTreatProcessLessThan(String value) {
            addCriterion("\"treat_process\" <", value, "treatProcess");
            return (Criteria) this;
        }

        public Criteria andTreatProcessLessThanOrEqualTo(String value) {
            addCriterion("\"treat_process\" <=", value, "treatProcess");
            return (Criteria) this;
        }

        public Criteria andTreatProcessLike(String value) {
            addCriterion("\"treat_process\" like", value, "treatProcess");
            return (Criteria) this;
        }

        public Criteria andTreatProcessNotLike(String value) {
            addCriterion("\"treat_process\" not like", value, "treatProcess");
            return (Criteria) this;
        }

        public Criteria andTreatProcessIn(List<String> values) {
            addCriterion("\"treat_process\" in", values, "treatProcess");
            return (Criteria) this;
        }

        public Criteria andTreatProcessNotIn(List<String> values) {
            addCriterion("\"treat_process\" not in", values, "treatProcess");
            return (Criteria) this;
        }

        public Criteria andTreatProcessBetween(String value1, String value2) {
            addCriterion("\"treat_process\" between", value1, value2, "treatProcess");
            return (Criteria) this;
        }

        public Criteria andTreatProcessNotBetween(String value1, String value2) {
            addCriterion("\"treat_process\" not between", value1, value2, "treatProcess");
            return (Criteria) this;
        }

        public Criteria andDischargeStatusIsNull() {
            addCriterion("\"discharge_status\" is null");
            return (Criteria) this;
        }

        public Criteria andDischargeStatusIsNotNull() {
            addCriterion("\"discharge_status\" is not null");
            return (Criteria) this;
        }

        public Criteria andDischargeStatusEqualTo(String value) {
            addCriterion("\"discharge_status\" =", value, "dischargeStatus");
            return (Criteria) this;
        }

        public Criteria andDischargeStatusNotEqualTo(String value) {
            addCriterion("\"discharge_status\" <>", value, "dischargeStatus");
            return (Criteria) this;
        }

        public Criteria andDischargeStatusGreaterThan(String value) {
            addCriterion("\"discharge_status\" >", value, "dischargeStatus");
            return (Criteria) this;
        }

        public Criteria andDischargeStatusGreaterThanOrEqualTo(String value) {
            addCriterion("\"discharge_status\" >=", value, "dischargeStatus");
            return (Criteria) this;
        }

        public Criteria andDischargeStatusLessThan(String value) {
            addCriterion("\"discharge_status\" <", value, "dischargeStatus");
            return (Criteria) this;
        }

        public Criteria andDischargeStatusLessThanOrEqualTo(String value) {
            addCriterion("\"discharge_status\" <=", value, "dischargeStatus");
            return (Criteria) this;
        }

        public Criteria andDischargeStatusLike(String value) {
            addCriterion("\"discharge_status\" like", value, "dischargeStatus");
            return (Criteria) this;
        }

        public Criteria andDischargeStatusNotLike(String value) {
            addCriterion("\"discharge_status\" not like", value, "dischargeStatus");
            return (Criteria) this;
        }

        public Criteria andDischargeStatusIn(List<String> values) {
            addCriterion("\"discharge_status\" in", values, "dischargeStatus");
            return (Criteria) this;
        }

        public Criteria andDischargeStatusNotIn(List<String> values) {
            addCriterion("\"discharge_status\" not in", values, "dischargeStatus");
            return (Criteria) this;
        }

        public Criteria andDischargeStatusBetween(String value1, String value2) {
            addCriterion("\"discharge_status\" between", value1, value2, "dischargeStatus");
            return (Criteria) this;
        }

        public Criteria andDischargeStatusNotBetween(String value1, String value2) {
            addCriterion("\"discharge_status\" not between", value1, value2, "dischargeStatus");
            return (Criteria) this;
        }

        public Criteria andDischargeOrderIsNull() {
            addCriterion("\"discharge_order\" is null");
            return (Criteria) this;
        }

        public Criteria andDischargeOrderIsNotNull() {
            addCriterion("\"discharge_order\" is not null");
            return (Criteria) this;
        }

        public Criteria andDischargeOrderEqualTo(String value) {
            addCriterion("\"discharge_order\" =", value, "dischargeOrder");
            return (Criteria) this;
        }

        public Criteria andDischargeOrderNotEqualTo(String value) {
            addCriterion("\"discharge_order\" <>", value, "dischargeOrder");
            return (Criteria) this;
        }

        public Criteria andDischargeOrderGreaterThan(String value) {
            addCriterion("\"discharge_order\" >", value, "dischargeOrder");
            return (Criteria) this;
        }

        public Criteria andDischargeOrderGreaterThanOrEqualTo(String value) {
            addCriterion("\"discharge_order\" >=", value, "dischargeOrder");
            return (Criteria) this;
        }

        public Criteria andDischargeOrderLessThan(String value) {
            addCriterion("\"discharge_order\" <", value, "dischargeOrder");
            return (Criteria) this;
        }

        public Criteria andDischargeOrderLessThanOrEqualTo(String value) {
            addCriterion("\"discharge_order\" <=", value, "dischargeOrder");
            return (Criteria) this;
        }

        public Criteria andDischargeOrderLike(String value) {
            addCriterion("\"discharge_order\" like", value, "dischargeOrder");
            return (Criteria) this;
        }

        public Criteria andDischargeOrderNotLike(String value) {
            addCriterion("\"discharge_order\" not like", value, "dischargeOrder");
            return (Criteria) this;
        }

        public Criteria andDischargeOrderIn(List<String> values) {
            addCriterion("\"discharge_order\" in", values, "dischargeOrder");
            return (Criteria) this;
        }

        public Criteria andDischargeOrderNotIn(List<String> values) {
            addCriterion("\"discharge_order\" not in", values, "dischargeOrder");
            return (Criteria) this;
        }

        public Criteria andDischargeOrderBetween(String value1, String value2) {
            addCriterion("\"discharge_order\" between", value1, value2, "dischargeOrder");
            return (Criteria) this;
        }

        public Criteria andDischargeOrderNotBetween(String value1, String value2) {
            addCriterion("\"discharge_order\" not between", value1, value2, "dischargeOrder");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andPkidIsNull() {
            addCriterion("\"pkid\" is null");
            return (Criteria) this;
        }

        public Criteria andPkidIsNotNull() {
            addCriterion("\"pkid\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkidEqualTo(String value) {
            addCriterion("\"pkid\" =", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotEqualTo(String value) {
            addCriterion("\"pkid\" <>", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThan(String value) {
            addCriterion("\"pkid\" >", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThanOrEqualTo(String value) {
            addCriterion("\"pkid\" >=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThan(String value) {
            addCriterion("\"pkid\" <", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThanOrEqualTo(String value) {
            addCriterion("\"pkid\" <=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLike(String value) {
            addCriterion("\"pkid\" like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotLike(String value) {
            addCriterion("\"pkid\" not like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidIn(List<String> values) {
            addCriterion("\"pkid\" in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotIn(List<String> values) {
            addCriterion("\"pkid\" not in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidBetween(String value1, String value2) {
            addCriterion("\"pkid\" between", value1, value2, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotBetween(String value1, String value2) {
            addCriterion("\"pkid\" not between", value1, value2, "pkid");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}