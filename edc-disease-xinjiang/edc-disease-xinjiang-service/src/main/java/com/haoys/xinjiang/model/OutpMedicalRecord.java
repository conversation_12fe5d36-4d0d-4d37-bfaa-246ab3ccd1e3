package com.haoys.xinjiang.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class OutpMedicalRecord implements Serializable {
    private Integer id;

    @ApiModelProperty(value = "就诊时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date visitDatetime;

    @ApiModelProperty(value = "门(急)诊病历主诉的原始文本")
    private String chiefComplaint;

    @ApiModelProperty(value = "门(急)诊病历现病史的原始文本")
    private String hyPresent;

    @ApiModelProperty(value = "门(急)诊病历既往史的原始文本")
    private String hyPast;

    @ApiModelProperty(value = "门(急)诊病历体格检查的原始文本")
    private String physicalExam;

    @ApiModelProperty(value = "门(急)诊病历辅助检查的原始文本")
    private String supplementaryExam;

    @ApiModelProperty(value = "主诉中结构化提取的结核症状、结核体征")
    private Object pmhPositiveSymptom;

    @ApiModelProperty(value = "患者唯一标识")
    private String patientSn;

    @ApiModelProperty(value = "就诊唯一标识")
    private String visitSn;

    @ApiModelProperty(value = "pkid")
    private String pkid;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getVisitDatetime() {
        return visitDatetime;
    }

    public void setVisitDatetime(Date visitDatetime) {
        this.visitDatetime = visitDatetime;
    }

    public String getChiefComplaint() {
        return chiefComplaint;
    }

    public void setChiefComplaint(String chiefComplaint) {
        this.chiefComplaint = chiefComplaint;
    }

    public String getHyPresent() {
        return hyPresent;
    }

    public void setHyPresent(String hyPresent) {
        this.hyPresent = hyPresent;
    }

    public String getHyPast() {
        return hyPast;
    }

    public void setHyPast(String hyPast) {
        this.hyPast = hyPast;
    }

    public String getPhysicalExam() {
        return physicalExam;
    }

    public void setPhysicalExam(String physicalExam) {
        this.physicalExam = physicalExam;
    }

    public String getSupplementaryExam() {
        return supplementaryExam;
    }

    public void setSupplementaryExam(String supplementaryExam) {
        this.supplementaryExam = supplementaryExam;
    }

    public Object getPmhPositiveSymptom() {
        return pmhPositiveSymptom;
    }

    public void setPmhPositiveSymptom(Object pmhPositiveSymptom) {
        this.pmhPositiveSymptom = pmhPositiveSymptom;
    }

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }

    public String getVisitSn() {
        return visitSn;
    }

    public void setVisitSn(String visitSn) {
        this.visitSn = visitSn;
    }

    public String getPkid() {
        return pkid;
    }

    public void setPkid(String pkid) {
        this.pkid = pkid;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", visitDatetime=").append(visitDatetime);
        sb.append(", chiefComplaint=").append(chiefComplaint);
        sb.append(", hyPresent=").append(hyPresent);
        sb.append(", hyPast=").append(hyPast);
        sb.append(", physicalExam=").append(physicalExam);
        sb.append(", supplementaryExam=").append(supplementaryExam);
        sb.append(", pmhPositiveSymptom=").append(pmhPositiveSymptom);
        sb.append(", patientSn=").append(patientSn);
        sb.append(", visitSn=").append(visitSn);
        sb.append(", pkid=").append(pkid);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}