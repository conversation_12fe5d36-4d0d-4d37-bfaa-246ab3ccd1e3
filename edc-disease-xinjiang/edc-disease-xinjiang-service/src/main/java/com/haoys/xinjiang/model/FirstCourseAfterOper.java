package com.haoys.xinjiang.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class FirstCourseAfterOper implements Serializable {
    private Integer id;

    @ApiModelProperty(value = "手术时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operationTime;

    @ApiModelProperty(value = "手术名称")
    private String operationName;

    @ApiModelProperty(value = "手术持续时间")
    private String operationDuration;

    @ApiModelProperty(value = "麻醉方式")
    private String anesthesiaMethod;

    @ApiModelProperty(value = "手术经过")
    private String operationProcess;

    @ApiModelProperty(value = "术后处置")
    private String postOperation;

    @ApiModelProperty(value = "术后注意事项")
    private String postOpConsideration;

    @ApiModelProperty(value = "记录时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date recordTime;

    @ApiModelProperty(value = "患者唯一标识")
    private String patientSn;

    @ApiModelProperty(value = "就诊唯一标识")
    private String visitSn;

    @ApiModelProperty(value = "pkid")
    private String pkid;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(Date operationTime) {
        this.operationTime = operationTime;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public String getOperationDuration() {
        return operationDuration;
    }

    public void setOperationDuration(String operationDuration) {
        this.operationDuration = operationDuration;
    }

    public String getAnesthesiaMethod() {
        return anesthesiaMethod;
    }

    public void setAnesthesiaMethod(String anesthesiaMethod) {
        this.anesthesiaMethod = anesthesiaMethod;
    }

    public String getOperationProcess() {
        return operationProcess;
    }

    public void setOperationProcess(String operationProcess) {
        this.operationProcess = operationProcess;
    }

    public String getPostOperation() {
        return postOperation;
    }

    public void setPostOperation(String postOperation) {
        this.postOperation = postOperation;
    }

    public String getPostOpConsideration() {
        return postOpConsideration;
    }

    public void setPostOpConsideration(String postOpConsideration) {
        this.postOpConsideration = postOpConsideration;
    }

    public Date getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(Date recordTime) {
        this.recordTime = recordTime;
    }

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }

    public String getVisitSn() {
        return visitSn;
    }

    public void setVisitSn(String visitSn) {
        this.visitSn = visitSn;
    }

    public String getPkid() {
        return pkid;
    }

    public void setPkid(String pkid) {
        this.pkid = pkid;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", operationTime=").append(operationTime);
        sb.append(", operationName=").append(operationName);
        sb.append(", operationDuration=").append(operationDuration);
        sb.append(", anesthesiaMethod=").append(anesthesiaMethod);
        sb.append(", operationProcess=").append(operationProcess);
        sb.append(", postOperation=").append(postOperation);
        sb.append(", postOpConsideration=").append(postOpConsideration);
        sb.append(", recordTime=").append(recordTime);
        sb.append(", patientSn=").append(patientSn);
        sb.append(", visitSn=").append(visitSn);
        sb.append(", pkid=").append(pkid);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}