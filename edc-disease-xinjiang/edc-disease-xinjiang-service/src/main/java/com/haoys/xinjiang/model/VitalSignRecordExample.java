package com.haoys.xinjiang.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class VitalSignRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public VitalSignRecordExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("\"id\" is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("\"id\" is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("\"id\" =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("\"id\" <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("\"id\" >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"id\" >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("\"id\" <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("\"id\" <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("\"id\" in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("\"id\" not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("\"id\" between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("\"id\" not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeIsNull() {
            addCriterion("\"record_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeIsNotNull() {
            addCriterion("\"record_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeEqualTo(Date value) {
            addCriterion("\"record_datetime\" =", value, "recordDatetime");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeNotEqualTo(Date value) {
            addCriterion("\"record_datetime\" <>", value, "recordDatetime");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeGreaterThan(Date value) {
            addCriterion("\"record_datetime\" >", value, "recordDatetime");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"record_datetime\" >=", value, "recordDatetime");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeLessThan(Date value) {
            addCriterion("\"record_datetime\" <", value, "recordDatetime");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"record_datetime\" <=", value, "recordDatetime");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeIn(List<Date> values) {
            addCriterion("\"record_datetime\" in", values, "recordDatetime");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeNotIn(List<Date> values) {
            addCriterion("\"record_datetime\" not in", values, "recordDatetime");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"record_datetime\" between", value1, value2, "recordDatetime");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"record_datetime\" not between", value1, value2, "recordDatetime");
            return (Criteria) this;
        }

        public Criteria andTemperatureIsNull() {
            addCriterion("\"temperature\" is null");
            return (Criteria) this;
        }

        public Criteria andTemperatureIsNotNull() {
            addCriterion("\"temperature\" is not null");
            return (Criteria) this;
        }

        public Criteria andTemperatureEqualTo(Double value) {
            addCriterion("\"temperature\" =", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureNotEqualTo(Double value) {
            addCriterion("\"temperature\" <>", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureGreaterThan(Double value) {
            addCriterion("\"temperature\" >", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureGreaterThanOrEqualTo(Double value) {
            addCriterion("\"temperature\" >=", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureLessThan(Double value) {
            addCriterion("\"temperature\" <", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureLessThanOrEqualTo(Double value) {
            addCriterion("\"temperature\" <=", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureIn(List<Double> values) {
            addCriterion("\"temperature\" in", values, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureNotIn(List<Double> values) {
            addCriterion("\"temperature\" not in", values, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureBetween(Double value1, Double value2) {
            addCriterion("\"temperature\" between", value1, value2, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureNotBetween(Double value1, Double value2) {
            addCriterion("\"temperature\" not between", value1, value2, "temperature");
            return (Criteria) this;
        }

        public Criteria andPulseRateIsNull() {
            addCriterion("\"pulse_rate\" is null");
            return (Criteria) this;
        }

        public Criteria andPulseRateIsNotNull() {
            addCriterion("\"pulse_rate\" is not null");
            return (Criteria) this;
        }

        public Criteria andPulseRateEqualTo(Integer value) {
            addCriterion("\"pulse_rate\" =", value, "pulseRate");
            return (Criteria) this;
        }

        public Criteria andPulseRateNotEqualTo(Integer value) {
            addCriterion("\"pulse_rate\" <>", value, "pulseRate");
            return (Criteria) this;
        }

        public Criteria andPulseRateGreaterThan(Integer value) {
            addCriterion("\"pulse_rate\" >", value, "pulseRate");
            return (Criteria) this;
        }

        public Criteria andPulseRateGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"pulse_rate\" >=", value, "pulseRate");
            return (Criteria) this;
        }

        public Criteria andPulseRateLessThan(Integer value) {
            addCriterion("\"pulse_rate\" <", value, "pulseRate");
            return (Criteria) this;
        }

        public Criteria andPulseRateLessThanOrEqualTo(Integer value) {
            addCriterion("\"pulse_rate\" <=", value, "pulseRate");
            return (Criteria) this;
        }

        public Criteria andPulseRateIn(List<Integer> values) {
            addCriterion("\"pulse_rate\" in", values, "pulseRate");
            return (Criteria) this;
        }

        public Criteria andPulseRateNotIn(List<Integer> values) {
            addCriterion("\"pulse_rate\" not in", values, "pulseRate");
            return (Criteria) this;
        }

        public Criteria andPulseRateBetween(Integer value1, Integer value2) {
            addCriterion("\"pulse_rate\" between", value1, value2, "pulseRate");
            return (Criteria) this;
        }

        public Criteria andPulseRateNotBetween(Integer value1, Integer value2) {
            addCriterion("\"pulse_rate\" not between", value1, value2, "pulseRate");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateIsNull() {
            addCriterion("\"respiratory_rate\" is null");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateIsNotNull() {
            addCriterion("\"respiratory_rate\" is not null");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateEqualTo(Integer value) {
            addCriterion("\"respiratory_rate\" =", value, "respiratoryRate");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateNotEqualTo(Integer value) {
            addCriterion("\"respiratory_rate\" <>", value, "respiratoryRate");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateGreaterThan(Integer value) {
            addCriterion("\"respiratory_rate\" >", value, "respiratoryRate");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"respiratory_rate\" >=", value, "respiratoryRate");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateLessThan(Integer value) {
            addCriterion("\"respiratory_rate\" <", value, "respiratoryRate");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateLessThanOrEqualTo(Integer value) {
            addCriterion("\"respiratory_rate\" <=", value, "respiratoryRate");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateIn(List<Integer> values) {
            addCriterion("\"respiratory_rate\" in", values, "respiratoryRate");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateNotIn(List<Integer> values) {
            addCriterion("\"respiratory_rate\" not in", values, "respiratoryRate");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateBetween(Integer value1, Integer value2) {
            addCriterion("\"respiratory_rate\" between", value1, value2, "respiratoryRate");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateNotBetween(Integer value1, Integer value2) {
            addCriterion("\"respiratory_rate\" not between", value1, value2, "respiratoryRate");
            return (Criteria) this;
        }

        public Criteria andSystolicBloodPressureIsNull() {
            addCriterion("\"systolic_blood_pressure\" is null");
            return (Criteria) this;
        }

        public Criteria andSystolicBloodPressureIsNotNull() {
            addCriterion("\"systolic_blood_pressure\" is not null");
            return (Criteria) this;
        }

        public Criteria andSystolicBloodPressureEqualTo(Integer value) {
            addCriterion("\"systolic_blood_pressure\" =", value, "systolicBloodPressure");
            return (Criteria) this;
        }

        public Criteria andSystolicBloodPressureNotEqualTo(Integer value) {
            addCriterion("\"systolic_blood_pressure\" <>", value, "systolicBloodPressure");
            return (Criteria) this;
        }

        public Criteria andSystolicBloodPressureGreaterThan(Integer value) {
            addCriterion("\"systolic_blood_pressure\" >", value, "systolicBloodPressure");
            return (Criteria) this;
        }

        public Criteria andSystolicBloodPressureGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"systolic_blood_pressure\" >=", value, "systolicBloodPressure");
            return (Criteria) this;
        }

        public Criteria andSystolicBloodPressureLessThan(Integer value) {
            addCriterion("\"systolic_blood_pressure\" <", value, "systolicBloodPressure");
            return (Criteria) this;
        }

        public Criteria andSystolicBloodPressureLessThanOrEqualTo(Integer value) {
            addCriterion("\"systolic_blood_pressure\" <=", value, "systolicBloodPressure");
            return (Criteria) this;
        }

        public Criteria andSystolicBloodPressureIn(List<Integer> values) {
            addCriterion("\"systolic_blood_pressure\" in", values, "systolicBloodPressure");
            return (Criteria) this;
        }

        public Criteria andSystolicBloodPressureNotIn(List<Integer> values) {
            addCriterion("\"systolic_blood_pressure\" not in", values, "systolicBloodPressure");
            return (Criteria) this;
        }

        public Criteria andSystolicBloodPressureBetween(Integer value1, Integer value2) {
            addCriterion("\"systolic_blood_pressure\" between", value1, value2, "systolicBloodPressure");
            return (Criteria) this;
        }

        public Criteria andSystolicBloodPressureNotBetween(Integer value1, Integer value2) {
            addCriterion("\"systolic_blood_pressure\" not between", value1, value2, "systolicBloodPressure");
            return (Criteria) this;
        }

        public Criteria andDiastolicBloodPressureIsNull() {
            addCriterion("\"diastolic_blood_pressure\" is null");
            return (Criteria) this;
        }

        public Criteria andDiastolicBloodPressureIsNotNull() {
            addCriterion("\"diastolic_blood_pressure\" is not null");
            return (Criteria) this;
        }

        public Criteria andDiastolicBloodPressureEqualTo(Integer value) {
            addCriterion("\"diastolic_blood_pressure\" =", value, "diastolicBloodPressure");
            return (Criteria) this;
        }

        public Criteria andDiastolicBloodPressureNotEqualTo(Integer value) {
            addCriterion("\"diastolic_blood_pressure\" <>", value, "diastolicBloodPressure");
            return (Criteria) this;
        }

        public Criteria andDiastolicBloodPressureGreaterThan(Integer value) {
            addCriterion("\"diastolic_blood_pressure\" >", value, "diastolicBloodPressure");
            return (Criteria) this;
        }

        public Criteria andDiastolicBloodPressureGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"diastolic_blood_pressure\" >=", value, "diastolicBloodPressure");
            return (Criteria) this;
        }

        public Criteria andDiastolicBloodPressureLessThan(Integer value) {
            addCriterion("\"diastolic_blood_pressure\" <", value, "diastolicBloodPressure");
            return (Criteria) this;
        }

        public Criteria andDiastolicBloodPressureLessThanOrEqualTo(Integer value) {
            addCriterion("\"diastolic_blood_pressure\" <=", value, "diastolicBloodPressure");
            return (Criteria) this;
        }

        public Criteria andDiastolicBloodPressureIn(List<Integer> values) {
            addCriterion("\"diastolic_blood_pressure\" in", values, "diastolicBloodPressure");
            return (Criteria) this;
        }

        public Criteria andDiastolicBloodPressureNotIn(List<Integer> values) {
            addCriterion("\"diastolic_blood_pressure\" not in", values, "diastolicBloodPressure");
            return (Criteria) this;
        }

        public Criteria andDiastolicBloodPressureBetween(Integer value1, Integer value2) {
            addCriterion("\"diastolic_blood_pressure\" between", value1, value2, "diastolicBloodPressure");
            return (Criteria) this;
        }

        public Criteria andDiastolicBloodPressureNotBetween(Integer value1, Integer value2) {
            addCriterion("\"diastolic_blood_pressure\" not between", value1, value2, "diastolicBloodPressure");
            return (Criteria) this;
        }

        public Criteria andHeartRateIsNull() {
            addCriterion("\"heart_rate\" is null");
            return (Criteria) this;
        }

        public Criteria andHeartRateIsNotNull() {
            addCriterion("\"heart_rate\" is not null");
            return (Criteria) this;
        }

        public Criteria andHeartRateEqualTo(Integer value) {
            addCriterion("\"heart_rate\" =", value, "heartRate");
            return (Criteria) this;
        }

        public Criteria andHeartRateNotEqualTo(Integer value) {
            addCriterion("\"heart_rate\" <>", value, "heartRate");
            return (Criteria) this;
        }

        public Criteria andHeartRateGreaterThan(Integer value) {
            addCriterion("\"heart_rate\" >", value, "heartRate");
            return (Criteria) this;
        }

        public Criteria andHeartRateGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"heart_rate\" >=", value, "heartRate");
            return (Criteria) this;
        }

        public Criteria andHeartRateLessThan(Integer value) {
            addCriterion("\"heart_rate\" <", value, "heartRate");
            return (Criteria) this;
        }

        public Criteria andHeartRateLessThanOrEqualTo(Integer value) {
            addCriterion("\"heart_rate\" <=", value, "heartRate");
            return (Criteria) this;
        }

        public Criteria andHeartRateIn(List<Integer> values) {
            addCriterion("\"heart_rate\" in", values, "heartRate");
            return (Criteria) this;
        }

        public Criteria andHeartRateNotIn(List<Integer> values) {
            addCriterion("\"heart_rate\" not in", values, "heartRate");
            return (Criteria) this;
        }

        public Criteria andHeartRateBetween(Integer value1, Integer value2) {
            addCriterion("\"heart_rate\" between", value1, value2, "heartRate");
            return (Criteria) this;
        }

        public Criteria andHeartRateNotBetween(Integer value1, Integer value2) {
            addCriterion("\"heart_rate\" not between", value1, value2, "heartRate");
            return (Criteria) this;
        }

        public Criteria andWeightIsNull() {
            addCriterion("\"weight\" is null");
            return (Criteria) this;
        }

        public Criteria andWeightIsNotNull() {
            addCriterion("\"weight\" is not null");
            return (Criteria) this;
        }

        public Criteria andWeightEqualTo(Double value) {
            addCriterion("\"weight\" =", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightNotEqualTo(Double value) {
            addCriterion("\"weight\" <>", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightGreaterThan(Double value) {
            addCriterion("\"weight\" >", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightGreaterThanOrEqualTo(Double value) {
            addCriterion("\"weight\" >=", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightLessThan(Double value) {
            addCriterion("\"weight\" <", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightLessThanOrEqualTo(Double value) {
            addCriterion("\"weight\" <=", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightIn(List<Double> values) {
            addCriterion("\"weight\" in", values, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightNotIn(List<Double> values) {
            addCriterion("\"weight\" not in", values, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightBetween(Double value1, Double value2) {
            addCriterion("\"weight\" between", value1, value2, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightNotBetween(Double value1, Double value2) {
            addCriterion("\"weight\" not between", value1, value2, "weight");
            return (Criteria) this;
        }

        public Criteria andDataSourceIsNull() {
            addCriterion("\"data_source\" is null");
            return (Criteria) this;
        }

        public Criteria andDataSourceIsNotNull() {
            addCriterion("\"data_source\" is not null");
            return (Criteria) this;
        }

        public Criteria andDataSourceEqualTo(String value) {
            addCriterion("\"data_source\" =", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceNotEqualTo(String value) {
            addCriterion("\"data_source\" <>", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceGreaterThan(String value) {
            addCriterion("\"data_source\" >", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceGreaterThanOrEqualTo(String value) {
            addCriterion("\"data_source\" >=", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceLessThan(String value) {
            addCriterion("\"data_source\" <", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceLessThanOrEqualTo(String value) {
            addCriterion("\"data_source\" <=", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceLike(String value) {
            addCriterion("\"data_source\" like", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceNotLike(String value) {
            addCriterion("\"data_source\" not like", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceIn(List<String> values) {
            addCriterion("\"data_source\" in", values, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceNotIn(List<String> values) {
            addCriterion("\"data_source\" not in", values, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceBetween(String value1, String value2) {
            addCriterion("\"data_source\" between", value1, value2, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceNotBetween(String value1, String value2) {
            addCriterion("\"data_source\" not between", value1, value2, "dataSource");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andPkidIsNull() {
            addCriterion("\"pkid\" is null");
            return (Criteria) this;
        }

        public Criteria andPkidIsNotNull() {
            addCriterion("\"pkid\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkidEqualTo(String value) {
            addCriterion("\"pkid\" =", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotEqualTo(String value) {
            addCriterion("\"pkid\" <>", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThan(String value) {
            addCriterion("\"pkid\" >", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThanOrEqualTo(String value) {
            addCriterion("\"pkid\" >=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThan(String value) {
            addCriterion("\"pkid\" <", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThanOrEqualTo(String value) {
            addCriterion("\"pkid\" <=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLike(String value) {
            addCriterion("\"pkid\" like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotLike(String value) {
            addCriterion("\"pkid\" not like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidIn(List<String> values) {
            addCriterion("\"pkid\" in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotIn(List<String> values) {
            addCriterion("\"pkid\" not in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidBetween(String value1, String value2) {
            addCriterion("\"pkid\" between", value1, value2, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotBetween(String value1, String value2) {
            addCriterion("\"pkid\" not between", value1, value2, "pkid");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}