package com.haoys.xinjiang.domain.vo;

import com.haoys.user.common.api.CommonPage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class PatientRecordVo {

    @ApiModelProperty(value = "患者病历检索列表")
    private CommonPage<DemographyInformationWrapper> patientModelVariableRecordList;

    @ApiModelProperty(value = "患者列表自定义标题配置")
    private List<ModelVariableHeadRowConfig> variableHeadRowList = new ArrayList<>();

    @Data
    public static class ModelVariableHeadRowConfig {

        @ApiModelProperty(value = "表单来源code")
        private String modelSourceCode;

        @ApiModelProperty(value = "变量名称")
        private String variableName;

        @ApiModelProperty(value = "变量code")
        private String variableCode;

        @ApiModelProperty(value = "变量类型")
        private String variableType;

        @ApiModelProperty(value = "默认查询字段")
        private Boolean defaultQuery = false;

        @ApiModelProperty(value = "字段排序")
        private Integer sort;
    }
}
