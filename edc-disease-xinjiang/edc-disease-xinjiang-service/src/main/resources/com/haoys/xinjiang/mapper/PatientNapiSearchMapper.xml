<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.xinjiang.mapper.PatientNapiSearchMapper">
  <resultMap id="BaseResultMap" type="com.haoys.xinjiang.model.PatientNapiSearch">
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
    <result column="rule_desc" jdbcType="VARCHAR" property="ruleDesc" />
    <result column="disease_type" jdbcType="VARCHAR" property="diseaseType" />
    <result column="na_search" jdbcType="VARCHAR" property="naSearch" />
    <result column="ex_search" jdbcType="VARCHAR" property="exSearch" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "id", "rule_name", "rule_desc", "disease_type", "na_search", "ex_search", "create_user_id",
    "update_time", "create_time"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.xinjiang.model.PatientNapiSearchExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."patient_napi_search"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.haoys.xinjiang.model.PatientNapiSearchExample">
    delete from "public"."patient_napi_search"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.xinjiang.model.PatientNapiSearch">
    insert into "public"."patient_napi_search" ("id", "rule_name", "rule_desc",
      "disease_type", "na_search", "ex_search",
      "create_user_id", "update_time", "create_time"
      )
    values (#{id,jdbcType=VARCHAR}, #{ruleName,jdbcType=VARCHAR}, #{ruleDesc,jdbcType=VARCHAR},
      #{diseaseType,jdbcType=VARCHAR}, #{naSearch,jdbcType=VARCHAR}, #{exSearch,jdbcType=VARCHAR},
      #{createUserId,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.xinjiang.model.PatientNapiSearch">
    insert into "public"."patient_napi_search"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        "id",
      </if>
      <if test="ruleName != null">
        "rule_name",
      </if>
      <if test="ruleDesc != null">
        "rule_desc",
      </if>
      <if test="diseaseType != null">
        "disease_type",
      </if>
      <if test="naSearch != null">
        "na_search",
      </if>
      <if test="exSearch != null">
        "ex_search",
      </if>
      <if test="createUserId != null">
        "create_user_id",
      </if>
      <if test="updateTime != null">
        "update_time",
      </if>
      <if test="createTime != null">
        "create_time",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="ruleName != null">
        #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="ruleDesc != null">
        #{ruleDesc,jdbcType=VARCHAR},
      </if>
      <if test="diseaseType != null">
        #{diseaseType,jdbcType=VARCHAR},
      </if>
      <if test="naSearch != null">
        #{naSearch,jdbcType=VARCHAR},
      </if>
      <if test="exSearch != null">
        #{exSearch,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.xinjiang.model.PatientNapiSearchExample" resultType="java.lang.Long">
    select count(*) from "public"."patient_napi_search"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."patient_napi_search"
    <set>
      <if test="record.id != null">
        "id" = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleName != null">
        "rule_name" = #{record.ruleName,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleDesc != null">
        "rule_desc" = #{record.ruleDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.diseaseType != null">
        "disease_type" = #{record.diseaseType,jdbcType=VARCHAR},
      </if>
      <if test="record.naSearch != null">
        "na_search" = #{record.naSearch,jdbcType=VARCHAR},
      </if>
      <if test="record.exSearch != null">
        "ex_search" = #{record.exSearch,jdbcType=VARCHAR},
      </if>
      <if test="record.createUserId != null">
        "create_user_id" = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        "update_time" = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        "create_time" = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."patient_napi_search"
    set "id" = #{record.id,jdbcType=VARCHAR},
      "rule_name" = #{record.ruleName,jdbcType=VARCHAR},
      "rule_desc" = #{record.ruleDesc,jdbcType=VARCHAR},
      "disease_type" = #{record.diseaseType,jdbcType=VARCHAR},
      "na_search" = #{record.naSearch,jdbcType=VARCHAR},
      "ex_search" = #{record.exSearch,jdbcType=VARCHAR},
      "create_user_id" = #{record.createUserId,jdbcType=VARCHAR},
      "update_time" = #{record.updateTime,jdbcType=TIMESTAMP},
      "create_time" = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <select id="selectList" resultType="java.util.Map">
    ${sql}
  </select>

  <select id="count" resultType="long">
    ${sql}
  </select>

  <update id="updateById">
    update patient_napi_search
    <set>
      <if test="record.ruleName != null">
        rule_name = #{record.ruleName,jdbcType=VARCHAR},
      </if>
      <if test="record.diseasetype != null">
        disease_type = #{record.diseaseType,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleDesc != null">
        rule_desc = #{record.ruleDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.naSearch != null">
        na_search = #{record.naSearch,jdbcType=VARCHAR},
      </if>
      <if test="record.exSearch != null">
        ex_search = #{record.exSearch,jdbcType=VARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id=#{record.id}
  </update>

</mapper>
