<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.xinjiang.mapper.ExamRecordMapper">
  <resultMap id="BaseResultMap" type="com.haoys.xinjiang.model.ExamRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="exam_datetime" jdbcType="TIMESTAMP" property="examDatetime" />
    <result column="report_datetime" jdbcType="TIMESTAMP" property="reportDatetime" />
    <result column="exam_id" jdbcType="VARCHAR" property="examId" />
    <result column="exam_class" jdbcType="VARCHAR" property="examClass" />
    <result column="exam_name" jdbcType="VARCHAR" property="examName" />
    <result column="exam_part" jdbcType="VARCHAR" property="examPart" />
    <result column="exam_finding" jdbcType="VARCHAR" property="examFinding" />
    <result column="exam_conclusion" jdbcType="VARCHAR" property="examConclusion" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="pkid" jdbcType="VARCHAR" property="pkid" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "id", "exam_datetime", "report_datetime", "exam_id", "exam_class", "exam_name", "exam_part", 
    "exam_finding", "exam_conclusion", "patient_sn", "visit_sn", "pkid"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.xinjiang.model.ExamRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."exam_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."exam_record"
    where "id" = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from "public"."exam_record"
    where "id" = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.xinjiang.model.ExamRecordExample">
    delete from "public"."exam_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.xinjiang.model.ExamRecord">
    insert into "public"."exam_record" ("id", "exam_datetime", "report_datetime", 
      "exam_id", "exam_class", "exam_name", 
      "exam_part", "exam_finding", "exam_conclusion", 
      "patient_sn", "visit_sn", "pkid"
      )
    values (#{id,jdbcType=INTEGER}, #{examDatetime,jdbcType=TIMESTAMP}, #{reportDatetime,jdbcType=TIMESTAMP}, 
      #{examId,jdbcType=VARCHAR}, #{examClass,jdbcType=VARCHAR}, #{examName,jdbcType=VARCHAR}, 
      #{examPart,jdbcType=VARCHAR}, #{examFinding,jdbcType=VARCHAR}, #{examConclusion,jdbcType=VARCHAR}, 
      #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR}, #{pkid,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.xinjiang.model.ExamRecord">
    insert into "public"."exam_record"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        "id",
      </if>
      <if test="examDatetime != null">
        "exam_datetime",
      </if>
      <if test="reportDatetime != null">
        "report_datetime",
      </if>
      <if test="examId != null">
        "exam_id",
      </if>
      <if test="examClass != null">
        "exam_class",
      </if>
      <if test="examName != null">
        "exam_name",
      </if>
      <if test="examPart != null">
        "exam_part",
      </if>
      <if test="examFinding != null">
        "exam_finding",
      </if>
      <if test="examConclusion != null">
        "exam_conclusion",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="pkid != null">
        "pkid",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="examDatetime != null">
        #{examDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="reportDatetime != null">
        #{reportDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="examId != null">
        #{examId,jdbcType=VARCHAR},
      </if>
      <if test="examClass != null">
        #{examClass,jdbcType=VARCHAR},
      </if>
      <if test="examName != null">
        #{examName,jdbcType=VARCHAR},
      </if>
      <if test="examPart != null">
        #{examPart,jdbcType=VARCHAR},
      </if>
      <if test="examFinding != null">
        #{examFinding,jdbcType=VARCHAR},
      </if>
      <if test="examConclusion != null">
        #{examConclusion,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="pkid != null">
        #{pkid,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.xinjiang.model.ExamRecordExample" resultType="java.lang.Long">
    select count(*) from "public"."exam_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."exam_record"
    <set>
      <if test="record.id != null">
        "id" = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.examDatetime != null">
        "exam_datetime" = #{record.examDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reportDatetime != null">
        "report_datetime" = #{record.reportDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.examId != null">
        "exam_id" = #{record.examId,jdbcType=VARCHAR},
      </if>
      <if test="record.examClass != null">
        "exam_class" = #{record.examClass,jdbcType=VARCHAR},
      </if>
      <if test="record.examName != null">
        "exam_name" = #{record.examName,jdbcType=VARCHAR},
      </if>
      <if test="record.examPart != null">
        "exam_part" = #{record.examPart,jdbcType=VARCHAR},
      </if>
      <if test="record.examFinding != null">
        "exam_finding" = #{record.examFinding,jdbcType=VARCHAR},
      </if>
      <if test="record.examConclusion != null">
        "exam_conclusion" = #{record.examConclusion,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.pkid != null">
        "pkid" = #{record.pkid,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."exam_record"
    set "id" = #{record.id,jdbcType=INTEGER},
      "exam_datetime" = #{record.examDatetime,jdbcType=TIMESTAMP},
      "report_datetime" = #{record.reportDatetime,jdbcType=TIMESTAMP},
      "exam_id" = #{record.examId,jdbcType=VARCHAR},
      "exam_class" = #{record.examClass,jdbcType=VARCHAR},
      "exam_name" = #{record.examName,jdbcType=VARCHAR},
      "exam_part" = #{record.examPart,jdbcType=VARCHAR},
      "exam_finding" = #{record.examFinding,jdbcType=VARCHAR},
      "exam_conclusion" = #{record.examConclusion,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "pkid" = #{record.pkid,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.xinjiang.model.ExamRecord">
    update "public"."exam_record"
    <set>
      <if test="examDatetime != null">
        "exam_datetime" = #{examDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="reportDatetime != null">
        "report_datetime" = #{reportDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="examId != null">
        "exam_id" = #{examId,jdbcType=VARCHAR},
      </if>
      <if test="examClass != null">
        "exam_class" = #{examClass,jdbcType=VARCHAR},
      </if>
      <if test="examName != null">
        "exam_name" = #{examName,jdbcType=VARCHAR},
      </if>
      <if test="examPart != null">
        "exam_part" = #{examPart,jdbcType=VARCHAR},
      </if>
      <if test="examFinding != null">
        "exam_finding" = #{examFinding,jdbcType=VARCHAR},
      </if>
      <if test="examConclusion != null">
        "exam_conclusion" = #{examConclusion,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="pkid != null">
        "pkid" = #{pkid,jdbcType=VARCHAR},
      </if>
    </set>
    where "id" = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.xinjiang.model.ExamRecord">
    update "public"."exam_record"
    set "exam_datetime" = #{examDatetime,jdbcType=TIMESTAMP},
      "report_datetime" = #{reportDatetime,jdbcType=TIMESTAMP},
      "exam_id" = #{examId,jdbcType=VARCHAR},
      "exam_class" = #{examClass,jdbcType=VARCHAR},
      "exam_name" = #{examName,jdbcType=VARCHAR},
      "exam_part" = #{examPart,jdbcType=VARCHAR},
      "exam_finding" = #{examFinding,jdbcType=VARCHAR},
      "exam_conclusion" = #{examConclusion,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "pkid" = #{pkid,jdbcType=VARCHAR}
    where "id" = #{id,jdbcType=INTEGER}
  </update>
</mapper>