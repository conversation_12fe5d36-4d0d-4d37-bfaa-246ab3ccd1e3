<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.xinjiang.mapper.TbSmearTestMapper">
  <resultMap id="BaseResultMap" type="com.haoys.xinjiang.model.TbSmearTest">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="lab_result_id" jdbcType="VARCHAR" property="labResultId" />
    <result column="exam_datetime" jdbcType="TIMESTAMP" property="examDatetime" />
    <result column="report_datetime" jdbcType="TIMESTAMP" property="reportDatetime" />
    <result column="test_group_items" jdbcType="VARCHAR" property="testGroupItems" />
    <result column="spcm_type" jdbcType="VARCHAR" property="spcmType" />
    <result column="result" jdbcType="VARCHAR" property="result" />
    <result column="raw_result" jdbcType="VARCHAR" property="rawResult" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="pkid" jdbcType="VARCHAR" property="pkid" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "id", "lab_result_id", "exam_datetime", "report_datetime", "test_group_items", "spcm_type", 
    "result", "raw_result", "patient_sn", "visit_sn", "pkid"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.xinjiang.model.TbSmearTestExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."tb_smear_test"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."tb_smear_test"
    where "id" = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from "public"."tb_smear_test"
    where "id" = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.xinjiang.model.TbSmearTestExample">
    delete from "public"."tb_smear_test"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.xinjiang.model.TbSmearTest">
    insert into "public"."tb_smear_test" ("id", "lab_result_id", "exam_datetime", 
      "report_datetime", "test_group_items", "spcm_type", 
      "result", "raw_result", "patient_sn", 
      "visit_sn", "pkid")
    values (#{id,jdbcType=INTEGER}, #{labResultId,jdbcType=VARCHAR}, #{examDatetime,jdbcType=TIMESTAMP}, 
      #{reportDatetime,jdbcType=TIMESTAMP}, #{testGroupItems,jdbcType=VARCHAR}, #{spcmType,jdbcType=VARCHAR}, 
      #{result,jdbcType=VARCHAR}, #{rawResult,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, 
      #{visitSn,jdbcType=VARCHAR}, #{pkid,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.xinjiang.model.TbSmearTest">
    insert into "public"."tb_smear_test"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        "id",
      </if>
      <if test="labResultId != null">
        "lab_result_id",
      </if>
      <if test="examDatetime != null">
        "exam_datetime",
      </if>
      <if test="reportDatetime != null">
        "report_datetime",
      </if>
      <if test="testGroupItems != null">
        "test_group_items",
      </if>
      <if test="spcmType != null">
        "spcm_type",
      </if>
      <if test="result != null">
        "result",
      </if>
      <if test="rawResult != null">
        "raw_result",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="pkid != null">
        "pkid",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="labResultId != null">
        #{labResultId,jdbcType=VARCHAR},
      </if>
      <if test="examDatetime != null">
        #{examDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="reportDatetime != null">
        #{reportDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="testGroupItems != null">
        #{testGroupItems,jdbcType=VARCHAR},
      </if>
      <if test="spcmType != null">
        #{spcmType,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        #{result,jdbcType=VARCHAR},
      </if>
      <if test="rawResult != null">
        #{rawResult,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="pkid != null">
        #{pkid,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.xinjiang.model.TbSmearTestExample" resultType="java.lang.Long">
    select count(*) from "public"."tb_smear_test"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."tb_smear_test"
    <set>
      <if test="record.id != null">
        "id" = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.labResultId != null">
        "lab_result_id" = #{record.labResultId,jdbcType=VARCHAR},
      </if>
      <if test="record.examDatetime != null">
        "exam_datetime" = #{record.examDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reportDatetime != null">
        "report_datetime" = #{record.reportDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.testGroupItems != null">
        "test_group_items" = #{record.testGroupItems,jdbcType=VARCHAR},
      </if>
      <if test="record.spcmType != null">
        "spcm_type" = #{record.spcmType,jdbcType=VARCHAR},
      </if>
      <if test="record.result != null">
        "result" = #{record.result,jdbcType=VARCHAR},
      </if>
      <if test="record.rawResult != null">
        "raw_result" = #{record.rawResult,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.pkid != null">
        "pkid" = #{record.pkid,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."tb_smear_test"
    set "id" = #{record.id,jdbcType=INTEGER},
      "lab_result_id" = #{record.labResultId,jdbcType=VARCHAR},
      "exam_datetime" = #{record.examDatetime,jdbcType=TIMESTAMP},
      "report_datetime" = #{record.reportDatetime,jdbcType=TIMESTAMP},
      "test_group_items" = #{record.testGroupItems,jdbcType=VARCHAR},
      "spcm_type" = #{record.spcmType,jdbcType=VARCHAR},
      "result" = #{record.result,jdbcType=VARCHAR},
      "raw_result" = #{record.rawResult,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "pkid" = #{record.pkid,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.xinjiang.model.TbSmearTest">
    update "public"."tb_smear_test"
    <set>
      <if test="labResultId != null">
        "lab_result_id" = #{labResultId,jdbcType=VARCHAR},
      </if>
      <if test="examDatetime != null">
        "exam_datetime" = #{examDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="reportDatetime != null">
        "report_datetime" = #{reportDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="testGroupItems != null">
        "test_group_items" = #{testGroupItems,jdbcType=VARCHAR},
      </if>
      <if test="spcmType != null">
        "spcm_type" = #{spcmType,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        "result" = #{result,jdbcType=VARCHAR},
      </if>
      <if test="rawResult != null">
        "raw_result" = #{rawResult,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="pkid != null">
        "pkid" = #{pkid,jdbcType=VARCHAR},
      </if>
    </set>
    where "id" = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.xinjiang.model.TbSmearTest">
    update "public"."tb_smear_test"
    set "lab_result_id" = #{labResultId,jdbcType=VARCHAR},
      "exam_datetime" = #{examDatetime,jdbcType=TIMESTAMP},
      "report_datetime" = #{reportDatetime,jdbcType=TIMESTAMP},
      "test_group_items" = #{testGroupItems,jdbcType=VARCHAR},
      "spcm_type" = #{spcmType,jdbcType=VARCHAR},
      "result" = #{result,jdbcType=VARCHAR},
      "raw_result" = #{rawResult,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "pkid" = #{pkid,jdbcType=VARCHAR}
    where "id" = #{id,jdbcType=INTEGER}
  </update>
</mapper>