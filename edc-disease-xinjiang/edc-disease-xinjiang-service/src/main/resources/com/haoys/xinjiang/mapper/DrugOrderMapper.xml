<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.xinjiang.mapper.DrugOrderMapper">
  <resultMap id="BaseResultMap" type="com.haoys.xinjiang.model.DrugOrder">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_create_datetime" jdbcType="TIMESTAMP" property="orderCreateDatetime" />
    <result column="order_start_datetime" jdbcType="TIMESTAMP" property="orderStartDatetime" />
    <result column="order_stop_datetime" jdbcType="TIMESTAMP" property="orderStopDatetime" />
    <result column="order_end_datetime" jdbcType="TIMESTAMP" property="orderEndDatetime" />
    <result column="order_text" jdbcType="VARCHAR" property="orderText" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="generic_name" jdbcType="VARCHAR" property="genericName" />
    <result column="drug_type" jdbcType="VARCHAR" property="drugType" />
    <result column="drug_spec" jdbcType="VARCHAR" property="drugSpec" />
    <result column="dosage" jdbcType="VARCHAR" property="dosage" />
    <result column="dosage_units" jdbcType="VARCHAR" property="dosageUnits" />
    <result column="frequency" jdbcType="VARCHAR" property="frequency" />
    <result column="administration_route" jdbcType="VARCHAR" property="administrationRoute" />
    <result column="order_memo" jdbcType="VARCHAR" property="orderMemo" />
    <result column="order_class" jdbcType="VARCHAR" property="orderClass" />
    <result column="order_status" jdbcType="VARCHAR" property="orderStatus" />
    <result column="order_source" jdbcType="VARCHAR" property="orderSource" />
    <result column="order_create_dept" jdbcType="VARCHAR" property="orderCreateDept" />
    <result column="order_create_doctor" jdbcType="VARCHAR" property="orderCreateDoctor" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="pkid" jdbcType="VARCHAR" property="pkid" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "id", "order_create_datetime", "order_start_datetime", "order_stop_datetime", "order_end_datetime", 
    "order_text", "brand_name", "generic_name", "drug_type", "drug_spec", "dosage", "dosage_units", 
    "frequency", "administration_route", "order_memo", "order_class", "order_status", 
    "order_source", "order_create_dept", "order_create_doctor", "patient_sn", "visit_sn", 
    "pkid"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.xinjiang.model.DrugOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."drug_order"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."drug_order"
    where "id" = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from "public"."drug_order"
    where "id" = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.xinjiang.model.DrugOrderExample">
    delete from "public"."drug_order"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.xinjiang.model.DrugOrder">
    insert into "public"."drug_order" ("id", "order_create_datetime", "order_start_datetime", 
      "order_stop_datetime", "order_end_datetime", 
      "order_text", "brand_name", "generic_name", 
      "drug_type", "drug_spec", "dosage", 
      "dosage_units", "frequency", "administration_route", 
      "order_memo", "order_class", "order_status", 
      "order_source", "order_create_dept", "order_create_doctor", 
      "patient_sn", "visit_sn", "pkid"
      )
    values (#{id,jdbcType=INTEGER}, #{orderCreateDatetime,jdbcType=TIMESTAMP}, #{orderStartDatetime,jdbcType=TIMESTAMP}, 
      #{orderStopDatetime,jdbcType=TIMESTAMP}, #{orderEndDatetime,jdbcType=TIMESTAMP}, 
      #{orderText,jdbcType=VARCHAR}, #{brandName,jdbcType=VARCHAR}, #{genericName,jdbcType=VARCHAR}, 
      #{drugType,jdbcType=VARCHAR}, #{drugSpec,jdbcType=VARCHAR}, #{dosage,jdbcType=VARCHAR}, 
      #{dosageUnits,jdbcType=VARCHAR}, #{frequency,jdbcType=VARCHAR}, #{administrationRoute,jdbcType=VARCHAR}, 
      #{orderMemo,jdbcType=VARCHAR}, #{orderClass,jdbcType=VARCHAR}, #{orderStatus,jdbcType=VARCHAR}, 
      #{orderSource,jdbcType=VARCHAR}, #{orderCreateDept,jdbcType=VARCHAR}, #{orderCreateDoctor,jdbcType=VARCHAR}, 
      #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR}, #{pkid,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.xinjiang.model.DrugOrder">
    insert into "public"."drug_order"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        "id",
      </if>
      <if test="orderCreateDatetime != null">
        "order_create_datetime",
      </if>
      <if test="orderStartDatetime != null">
        "order_start_datetime",
      </if>
      <if test="orderStopDatetime != null">
        "order_stop_datetime",
      </if>
      <if test="orderEndDatetime != null">
        "order_end_datetime",
      </if>
      <if test="orderText != null">
        "order_text",
      </if>
      <if test="brandName != null">
        "brand_name",
      </if>
      <if test="genericName != null">
        "generic_name",
      </if>
      <if test="drugType != null">
        "drug_type",
      </if>
      <if test="drugSpec != null">
        "drug_spec",
      </if>
      <if test="dosage != null">
        "dosage",
      </if>
      <if test="dosageUnits != null">
        "dosage_units",
      </if>
      <if test="frequency != null">
        "frequency",
      </if>
      <if test="administrationRoute != null">
        "administration_route",
      </if>
      <if test="orderMemo != null">
        "order_memo",
      </if>
      <if test="orderClass != null">
        "order_class",
      </if>
      <if test="orderStatus != null">
        "order_status",
      </if>
      <if test="orderSource != null">
        "order_source",
      </if>
      <if test="orderCreateDept != null">
        "order_create_dept",
      </if>
      <if test="orderCreateDoctor != null">
        "order_create_doctor",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="pkid != null">
        "pkid",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="orderCreateDatetime != null">
        #{orderCreateDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStartDatetime != null">
        #{orderStartDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStopDatetime != null">
        #{orderStopDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderEndDatetime != null">
        #{orderEndDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderText != null">
        #{orderText,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="genericName != null">
        #{genericName,jdbcType=VARCHAR},
      </if>
      <if test="drugType != null">
        #{drugType,jdbcType=VARCHAR},
      </if>
      <if test="drugSpec != null">
        #{drugSpec,jdbcType=VARCHAR},
      </if>
      <if test="dosage != null">
        #{dosage,jdbcType=VARCHAR},
      </if>
      <if test="dosageUnits != null">
        #{dosageUnits,jdbcType=VARCHAR},
      </if>
      <if test="frequency != null">
        #{frequency,jdbcType=VARCHAR},
      </if>
      <if test="administrationRoute != null">
        #{administrationRoute,jdbcType=VARCHAR},
      </if>
      <if test="orderMemo != null">
        #{orderMemo,jdbcType=VARCHAR},
      </if>
      <if test="orderClass != null">
        #{orderClass,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=VARCHAR},
      </if>
      <if test="orderSource != null">
        #{orderSource,jdbcType=VARCHAR},
      </if>
      <if test="orderCreateDept != null">
        #{orderCreateDept,jdbcType=VARCHAR},
      </if>
      <if test="orderCreateDoctor != null">
        #{orderCreateDoctor,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="pkid != null">
        #{pkid,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.xinjiang.model.DrugOrderExample" resultType="java.lang.Long">
    select count(*) from "public"."drug_order"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."drug_order"
    <set>
      <if test="record.id != null">
        "id" = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.orderCreateDatetime != null">
        "order_create_datetime" = #{record.orderCreateDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderStartDatetime != null">
        "order_start_datetime" = #{record.orderStartDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderStopDatetime != null">
        "order_stop_datetime" = #{record.orderStopDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderEndDatetime != null">
        "order_end_datetime" = #{record.orderEndDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderText != null">
        "order_text" = #{record.orderText,jdbcType=VARCHAR},
      </if>
      <if test="record.brandName != null">
        "brand_name" = #{record.brandName,jdbcType=VARCHAR},
      </if>
      <if test="record.genericName != null">
        "generic_name" = #{record.genericName,jdbcType=VARCHAR},
      </if>
      <if test="record.drugType != null">
        "drug_type" = #{record.drugType,jdbcType=VARCHAR},
      </if>
      <if test="record.drugSpec != null">
        "drug_spec" = #{record.drugSpec,jdbcType=VARCHAR},
      </if>
      <if test="record.dosage != null">
        "dosage" = #{record.dosage,jdbcType=VARCHAR},
      </if>
      <if test="record.dosageUnits != null">
        "dosage_units" = #{record.dosageUnits,jdbcType=VARCHAR},
      </if>
      <if test="record.frequency != null">
        "frequency" = #{record.frequency,jdbcType=VARCHAR},
      </if>
      <if test="record.administrationRoute != null">
        "administration_route" = #{record.administrationRoute,jdbcType=VARCHAR},
      </if>
      <if test="record.orderMemo != null">
        "order_memo" = #{record.orderMemo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderClass != null">
        "order_class" = #{record.orderClass,jdbcType=VARCHAR},
      </if>
      <if test="record.orderStatus != null">
        "order_status" = #{record.orderStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.orderSource != null">
        "order_source" = #{record.orderSource,jdbcType=VARCHAR},
      </if>
      <if test="record.orderCreateDept != null">
        "order_create_dept" = #{record.orderCreateDept,jdbcType=VARCHAR},
      </if>
      <if test="record.orderCreateDoctor != null">
        "order_create_doctor" = #{record.orderCreateDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.pkid != null">
        "pkid" = #{record.pkid,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."drug_order"
    set "id" = #{record.id,jdbcType=INTEGER},
      "order_create_datetime" = #{record.orderCreateDatetime,jdbcType=TIMESTAMP},
      "order_start_datetime" = #{record.orderStartDatetime,jdbcType=TIMESTAMP},
      "order_stop_datetime" = #{record.orderStopDatetime,jdbcType=TIMESTAMP},
      "order_end_datetime" = #{record.orderEndDatetime,jdbcType=TIMESTAMP},
      "order_text" = #{record.orderText,jdbcType=VARCHAR},
      "brand_name" = #{record.brandName,jdbcType=VARCHAR},
      "generic_name" = #{record.genericName,jdbcType=VARCHAR},
      "drug_type" = #{record.drugType,jdbcType=VARCHAR},
      "drug_spec" = #{record.drugSpec,jdbcType=VARCHAR},
      "dosage" = #{record.dosage,jdbcType=VARCHAR},
      "dosage_units" = #{record.dosageUnits,jdbcType=VARCHAR},
      "frequency" = #{record.frequency,jdbcType=VARCHAR},
      "administration_route" = #{record.administrationRoute,jdbcType=VARCHAR},
      "order_memo" = #{record.orderMemo,jdbcType=VARCHAR},
      "order_class" = #{record.orderClass,jdbcType=VARCHAR},
      "order_status" = #{record.orderStatus,jdbcType=VARCHAR},
      "order_source" = #{record.orderSource,jdbcType=VARCHAR},
      "order_create_dept" = #{record.orderCreateDept,jdbcType=VARCHAR},
      "order_create_doctor" = #{record.orderCreateDoctor,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "pkid" = #{record.pkid,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.xinjiang.model.DrugOrder">
    update "public"."drug_order"
    <set>
      <if test="orderCreateDatetime != null">
        "order_create_datetime" = #{orderCreateDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStartDatetime != null">
        "order_start_datetime" = #{orderStartDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStopDatetime != null">
        "order_stop_datetime" = #{orderStopDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderEndDatetime != null">
        "order_end_datetime" = #{orderEndDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderText != null">
        "order_text" = #{orderText,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        "brand_name" = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="genericName != null">
        "generic_name" = #{genericName,jdbcType=VARCHAR},
      </if>
      <if test="drugType != null">
        "drug_type" = #{drugType,jdbcType=VARCHAR},
      </if>
      <if test="drugSpec != null">
        "drug_spec" = #{drugSpec,jdbcType=VARCHAR},
      </if>
      <if test="dosage != null">
        "dosage" = #{dosage,jdbcType=VARCHAR},
      </if>
      <if test="dosageUnits != null">
        "dosage_units" = #{dosageUnits,jdbcType=VARCHAR},
      </if>
      <if test="frequency != null">
        "frequency" = #{frequency,jdbcType=VARCHAR},
      </if>
      <if test="administrationRoute != null">
        "administration_route" = #{administrationRoute,jdbcType=VARCHAR},
      </if>
      <if test="orderMemo != null">
        "order_memo" = #{orderMemo,jdbcType=VARCHAR},
      </if>
      <if test="orderClass != null">
        "order_class" = #{orderClass,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        "order_status" = #{orderStatus,jdbcType=VARCHAR},
      </if>
      <if test="orderSource != null">
        "order_source" = #{orderSource,jdbcType=VARCHAR},
      </if>
      <if test="orderCreateDept != null">
        "order_create_dept" = #{orderCreateDept,jdbcType=VARCHAR},
      </if>
      <if test="orderCreateDoctor != null">
        "order_create_doctor" = #{orderCreateDoctor,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="pkid != null">
        "pkid" = #{pkid,jdbcType=VARCHAR},
      </if>
    </set>
    where "id" = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.xinjiang.model.DrugOrder">
    update "public"."drug_order"
    set "order_create_datetime" = #{orderCreateDatetime,jdbcType=TIMESTAMP},
      "order_start_datetime" = #{orderStartDatetime,jdbcType=TIMESTAMP},
      "order_stop_datetime" = #{orderStopDatetime,jdbcType=TIMESTAMP},
      "order_end_datetime" = #{orderEndDatetime,jdbcType=TIMESTAMP},
      "order_text" = #{orderText,jdbcType=VARCHAR},
      "brand_name" = #{brandName,jdbcType=VARCHAR},
      "generic_name" = #{genericName,jdbcType=VARCHAR},
      "drug_type" = #{drugType,jdbcType=VARCHAR},
      "drug_spec" = #{drugSpec,jdbcType=VARCHAR},
      "dosage" = #{dosage,jdbcType=VARCHAR},
      "dosage_units" = #{dosageUnits,jdbcType=VARCHAR},
      "frequency" = #{frequency,jdbcType=VARCHAR},
      "administration_route" = #{administrationRoute,jdbcType=VARCHAR},
      "order_memo" = #{orderMemo,jdbcType=VARCHAR},
      "order_class" = #{orderClass,jdbcType=VARCHAR},
      "order_status" = #{orderStatus,jdbcType=VARCHAR},
      "order_source" = #{orderSource,jdbcType=VARCHAR},
      "order_create_dept" = #{orderCreateDept,jdbcType=VARCHAR},
      "order_create_doctor" = #{orderCreateDoctor,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "pkid" = #{pkid,jdbcType=VARCHAR}
    where "id" = #{id,jdbcType=INTEGER}
  </update>
</mapper>