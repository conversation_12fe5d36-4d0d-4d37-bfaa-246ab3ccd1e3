<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.xinjiang.mapper.OutpMedicalRecordMapper">
  <resultMap id="BaseResultMap" type="com.haoys.xinjiang.model.OutpMedicalRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="visit_datetime" jdbcType="TIMESTAMP" property="visitDatetime" />
    <result column="chief_complaint" jdbcType="VARCHAR" property="chiefComplaint" />
    <result column="hy_present" jdbcType="VARCHAR" property="hyPresent" />
    <result column="hy_past" jdbcType="VARCHAR" property="hyPast" />
    <result column="physical_exam" jdbcType="VARCHAR" property="physicalExam" />
    <result column="supplementary_exam" jdbcType="VARCHAR" property="supplementaryExam" />
    <result column="pmh_positive_symptom" jdbcType="ARRAY" property="pmhPositiveSymptom" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="pkid" jdbcType="VARCHAR" property="pkid" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "id", "visit_datetime", "chief_complaint", "hy_present", "hy_past", "physical_exam", 
    "supplementary_exam", "pmh_positive_symptom", "patient_sn", "visit_sn", "pkid"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.xinjiang.model.OutpMedicalRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."outp_medical_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."outp_medical_record"
    where "id" = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from "public"."outp_medical_record"
    where "id" = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.xinjiang.model.OutpMedicalRecordExample">
    delete from "public"."outp_medical_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.xinjiang.model.OutpMedicalRecord">
    insert into "public"."outp_medical_record" ("id", "visit_datetime", "chief_complaint", 
      "hy_present", "hy_past", "physical_exam", 
      "supplementary_exam", "pmh_positive_symptom", "patient_sn", 
      "visit_sn", "pkid")
    values (#{id,jdbcType=INTEGER}, #{visitDatetime,jdbcType=TIMESTAMP}, #{chiefComplaint,jdbcType=VARCHAR}, 
      #{hyPresent,jdbcType=VARCHAR}, #{hyPast,jdbcType=VARCHAR}, #{physicalExam,jdbcType=VARCHAR}, 
      #{supplementaryExam,jdbcType=VARCHAR}, #{pmhPositiveSymptom,jdbcType=ARRAY}, #{patientSn,jdbcType=VARCHAR}, 
      #{visitSn,jdbcType=VARCHAR}, #{pkid,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.xinjiang.model.OutpMedicalRecord">
    insert into "public"."outp_medical_record"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        "id",
      </if>
      <if test="visitDatetime != null">
        "visit_datetime",
      </if>
      <if test="chiefComplaint != null">
        "chief_complaint",
      </if>
      <if test="hyPresent != null">
        "hy_present",
      </if>
      <if test="hyPast != null">
        "hy_past",
      </if>
      <if test="physicalExam != null">
        "physical_exam",
      </if>
      <if test="supplementaryExam != null">
        "supplementary_exam",
      </if>
      <if test="pmhPositiveSymptom != null">
        "pmh_positive_symptom",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="pkid != null">
        "pkid",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="visitDatetime != null">
        #{visitDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="chiefComplaint != null">
        #{chiefComplaint,jdbcType=VARCHAR},
      </if>
      <if test="hyPresent != null">
        #{hyPresent,jdbcType=VARCHAR},
      </if>
      <if test="hyPast != null">
        #{hyPast,jdbcType=VARCHAR},
      </if>
      <if test="physicalExam != null">
        #{physicalExam,jdbcType=VARCHAR},
      </if>
      <if test="supplementaryExam != null">
        #{supplementaryExam,jdbcType=VARCHAR},
      </if>
      <if test="pmhPositiveSymptom != null">
        #{pmhPositiveSymptom,jdbcType=ARRAY},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="pkid != null">
        #{pkid,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.xinjiang.model.OutpMedicalRecordExample" resultType="java.lang.Long">
    select count(*) from "public"."outp_medical_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."outp_medical_record"
    <set>
      <if test="record.id != null">
        "id" = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.visitDatetime != null">
        "visit_datetime" = #{record.visitDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.chiefComplaint != null">
        "chief_complaint" = #{record.chiefComplaint,jdbcType=VARCHAR},
      </if>
      <if test="record.hyPresent != null">
        "hy_present" = #{record.hyPresent,jdbcType=VARCHAR},
      </if>
      <if test="record.hyPast != null">
        "hy_past" = #{record.hyPast,jdbcType=VARCHAR},
      </if>
      <if test="record.physicalExam != null">
        "physical_exam" = #{record.physicalExam,jdbcType=VARCHAR},
      </if>
      <if test="record.supplementaryExam != null">
        "supplementary_exam" = #{record.supplementaryExam,jdbcType=VARCHAR},
      </if>
      <if test="record.pmhPositiveSymptom != null">
        "pmh_positive_symptom" = #{record.pmhPositiveSymptom,jdbcType=ARRAY},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.pkid != null">
        "pkid" = #{record.pkid,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."outp_medical_record"
    set "id" = #{record.id,jdbcType=INTEGER},
      "visit_datetime" = #{record.visitDatetime,jdbcType=TIMESTAMP},
      "chief_complaint" = #{record.chiefComplaint,jdbcType=VARCHAR},
      "hy_present" = #{record.hyPresent,jdbcType=VARCHAR},
      "hy_past" = #{record.hyPast,jdbcType=VARCHAR},
      "physical_exam" = #{record.physicalExam,jdbcType=VARCHAR},
      "supplementary_exam" = #{record.supplementaryExam,jdbcType=VARCHAR},
      "pmh_positive_symptom" = #{record.pmhPositiveSymptom,jdbcType=ARRAY},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "pkid" = #{record.pkid,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.xinjiang.model.OutpMedicalRecord">
    update "public"."outp_medical_record"
    <set>
      <if test="visitDatetime != null">
        "visit_datetime" = #{visitDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="chiefComplaint != null">
        "chief_complaint" = #{chiefComplaint,jdbcType=VARCHAR},
      </if>
      <if test="hyPresent != null">
        "hy_present" = #{hyPresent,jdbcType=VARCHAR},
      </if>
      <if test="hyPast != null">
        "hy_past" = #{hyPast,jdbcType=VARCHAR},
      </if>
      <if test="physicalExam != null">
        "physical_exam" = #{physicalExam,jdbcType=VARCHAR},
      </if>
      <if test="supplementaryExam != null">
        "supplementary_exam" = #{supplementaryExam,jdbcType=VARCHAR},
      </if>
      <if test="pmhPositiveSymptom != null">
        "pmh_positive_symptom" = #{pmhPositiveSymptom,jdbcType=ARRAY},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="pkid != null">
        "pkid" = #{pkid,jdbcType=VARCHAR},
      </if>
    </set>
    where "id" = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.xinjiang.model.OutpMedicalRecord">
    update "public"."outp_medical_record"
    set "visit_datetime" = #{visitDatetime,jdbcType=TIMESTAMP},
      "chief_complaint" = #{chiefComplaint,jdbcType=VARCHAR},
      "hy_present" = #{hyPresent,jdbcType=VARCHAR},
      "hy_past" = #{hyPast,jdbcType=VARCHAR},
      "physical_exam" = #{physicalExam,jdbcType=VARCHAR},
      "supplementary_exam" = #{supplementaryExam,jdbcType=VARCHAR},
      "pmh_positive_symptom" = #{pmhPositiveSymptom,jdbcType=ARRAY},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "pkid" = #{pkid,jdbcType=VARCHAR}
    where "id" = #{id,jdbcType=INTEGER}
  </update>
</mapper>