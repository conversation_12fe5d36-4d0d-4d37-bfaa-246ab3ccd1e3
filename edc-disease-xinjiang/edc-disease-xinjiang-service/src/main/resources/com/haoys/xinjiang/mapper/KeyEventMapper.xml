<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.xinjiang.mapper.KeyEventMapper">
  <resultMap id="BaseResultMap" type="com.haoys.xinjiang.model.KeyEvent">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="first_visit_datetime" jdbcType="TIMESTAMP" property="firstVisitDatetime" />
    <result column="first_visit_age" jdbcType="INTEGER" property="firstVisitAge" />
    <result column="first_visit_dept" jdbcType="VARCHAR" property="firstVisitDept" />
    <result column="first_visit_doctor" jdbcType="VARCHAR" property="firstVisitDoctor" />
    <result column="disease_time" jdbcType="TIMESTAMP" property="diseaseTime" />
    <result column="symptom_sign" jdbcType="ARRAY" property="symptomSign" />
    <result column="is_complication" jdbcType="BIT" property="isComplication" />
    <result column="complication" jdbcType="ARRAY" property="complication" />
    <result column="tb_therapy_type" jdbcType="VARCHAR" property="tbTherapyType" />
    <result column="is_antituberculosis_therapy" jdbcType="BIT" property="isAntituberculosisTherapy" />
    <result column="antituberculosis_therapy_drug_name" jdbcType="ARRAY" property="antituberculosisTherapyDrugName" />
    <result column="fist_antituberculosis_drug_therapy" jdbcType="TIMESTAMP" property="fistAntituberculosisDrugTherapy" />
    <result column="last_antituberculosis_drug_therapy" jdbcType="TIMESTAMP" property="lastAntituberculosisDrugTherapy" />
    <result column="tb_patient_discovery_way" jdbcType="VARCHAR" property="tbPatientDiscoveryWay" />
    <result column="is_tb_smear_positive" jdbcType="VARCHAR" property="isTbSmearPositive" />
    <result column="first_tb_smear_positive_exam_time" jdbcType="TIMESTAMP" property="firstTbSmearPositiveExamTime" />
    <result column="is_tb_culture_positive" jdbcType="VARCHAR" property="isTbCulturePositive" />
    <result column="first_tb_culture_positive_exam_time" jdbcType="TIMESTAMP" property="firstTbCulturePositiveExamTime" />
    <result column="is_tb_antibody_positive" jdbcType="VARCHAR" property="isTbAntibodyPositive" />
    <result column="is_tb_nucleic_acid_positive" jdbcType="VARCHAR" property="isTbNucleicAcidPositive" />
    <result column="first_tb_nucleic_acid_positive_exam_time" jdbcType="TIMESTAMP" property="firstTbNucleicAcidPositiveExamTime" />
    <result column="first_tb_antibody_positive_exam_time" jdbcType="TIMESTAMP" property="firstTbAntibodyPositiveExamTime" />
    <result column="is_tb_infection_t_cell_positive" jdbcType="VARCHAR" property="isTbInfectionTCellPositive" />
    <result column="first_tb_infection_t_cell_positive_exam_time" jdbcType="TIMESTAMP" property="firstTbInfectionTCellPositiveExamTime" />
    <result column="is_tb_skin_positive" jdbcType="VARCHAR" property="isTbSkinPositive" />
    <result column="first_tb_skin_positive_exam_time" jdbcType="TIMESTAMP" property="firstTbSkinPositiveExamTime" />
    <result column="is_tb_drug_resistant" jdbcType="VARCHAR" property="isTbDrugResistant" />
    <result column="tb_drug_resistant_drug_name" jdbcType="ARRAY" property="tbDrugResistantDrugName" />
    <result column="tb_drug_resistant_type" jdbcType="VARCHAR" property="tbDrugResistantType" />
    <result column="is_aids" jdbcType="BIT" property="isAids" />
    <result column="is_diabetes" jdbcType="BIT" property="isDiabetes" />
    <result column="is_copd" jdbcType="BIT" property="isCopd" />
    <result column="is_interstitial_lung_disease" jdbcType="BIT" property="isInterstitialLungDisease" />
    <result column="is_pulmonary_embolism" jdbcType="BIT" property="isPulmonaryEmbolism" />
    <result column="is_age_ge_65" jdbcType="BIT" property="isAgeGe65" />
    <result column="tuberculosis_type" jdbcType="ARRAY" property="tuberculosisType" />
    <result column="pulmonary_tuberculosis_type" jdbcType="VARCHAR" property="pulmonaryTuberculosisType" />
    <result column="extra_pulmonary_tuberculosis_type" jdbcType="ARRAY" property="extraPulmonaryTuberculosisType" />
    <result column="is_drug_allergy_history" jdbcType="BIT" property="isDrugAllergyHistory" />
    <result column="allergy_drug_name" jdbcType="ARRAY" property="allergyDrugName" />
    <result column="smoking_history" jdbcType="VARCHAR" property="smokingHistory" />
    <result column="is_dust_exposure_history" jdbcType="BIT" property="isDustExposureHistory" />
    <result column="is_isa_therapy" jdbcType="BIT" property="isIsaTherapy" />
    <result column="isa_therapy_drug_name" jdbcType="ARRAY" property="isaTherapyDrugName" />
    <result column="hormone_therapy_drug_name" jdbcType="ARRAY" property="hormoneTherapyDrugName" />
    <result column="is_hormone_therapy" jdbcType="BIT" property="isHormoneTherapy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "id", "patient_sn", "visit_sn", "first_visit_datetime", "first_visit_age", "first_visit_dept", 
    "first_visit_doctor", "disease_time", "symptom_sign", "is_complication", "complication", 
    "tb_therapy_type", "is_antituberculosis_therapy", "antituberculosis_therapy_drug_name", 
    "fist_antituberculosis_drug_therapy", "last_antituberculosis_drug_therapy", "tb_patient_discovery_way", 
    "is_tb_smear_positive", "first_tb_smear_positive_exam_time", "is_tb_culture_positive", 
    "first_tb_culture_positive_exam_time", "is_tb_antibody_positive", "is_tb_nucleic_acid_positive", 
    "first_tb_nucleic_acid_positive_exam_time", "first_tb_antibody_positive_exam_time", 
    "is_tb_infection_t_cell_positive", "first_tb_infection_t_cell_positive_exam_time", 
    "is_tb_skin_positive", "first_tb_skin_positive_exam_time", "is_tb_drug_resistant", 
    "tb_drug_resistant_drug_name", "tb_drug_resistant_type", "is_aids", "is_diabetes", 
    "is_copd", "is_interstitial_lung_disease", "is_pulmonary_embolism", "is_age_ge_65", 
    "tuberculosis_type", "pulmonary_tuberculosis_type", "extra_pulmonary_tuberculosis_type", 
    "is_drug_allergy_history", "allergy_drug_name", "smoking_history", "is_dust_exposure_history", 
    "is_isa_therapy", "isa_therapy_drug_name", "hormone_therapy_drug_name", "is_hormone_therapy"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.xinjiang.model.KeyEventExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."key_event"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."key_event"
    where "id" = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from "public"."key_event"
    where "id" = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.xinjiang.model.KeyEventExample">
    delete from "public"."key_event"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.xinjiang.model.KeyEvent">
    insert into "public"."key_event" ("id", "patient_sn", "visit_sn", 
      "first_visit_datetime", "first_visit_age", "first_visit_dept", 
      "first_visit_doctor", "disease_time", "symptom_sign", 
      "is_complication", "complication", "tb_therapy_type", 
      "is_antituberculosis_therapy", "antituberculosis_therapy_drug_name", 
      "fist_antituberculosis_drug_therapy", "last_antituberculosis_drug_therapy", 
      "tb_patient_discovery_way", "is_tb_smear_positive", 
      "first_tb_smear_positive_exam_time", "is_tb_culture_positive", 
      "first_tb_culture_positive_exam_time", "is_tb_antibody_positive", 
      "is_tb_nucleic_acid_positive", "first_tb_nucleic_acid_positive_exam_time", 
      "first_tb_antibody_positive_exam_time", "is_tb_infection_t_cell_positive", 
      "first_tb_infection_t_cell_positive_exam_time", "is_tb_skin_positive", 
      "first_tb_skin_positive_exam_time", "is_tb_drug_resistant", 
      "tb_drug_resistant_drug_name", "tb_drug_resistant_type", 
      "is_aids", "is_diabetes", "is_copd", "is_interstitial_lung_disease", 
      "is_pulmonary_embolism", "is_age_ge_65", "tuberculosis_type", 
      "pulmonary_tuberculosis_type", "extra_pulmonary_tuberculosis_type", 
      "is_drug_allergy_history", "allergy_drug_name", "smoking_history", 
      "is_dust_exposure_history", "is_isa_therapy", "isa_therapy_drug_name", 
      "hormone_therapy_drug_name", "is_hormone_therapy")
    values (#{id,jdbcType=INTEGER}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR}, 
      #{firstVisitDatetime,jdbcType=TIMESTAMP}, #{firstVisitAge,jdbcType=INTEGER}, #{firstVisitDept,jdbcType=VARCHAR}, 
      #{firstVisitDoctor,jdbcType=VARCHAR}, #{diseaseTime,jdbcType=TIMESTAMP}, #{symptomSign,jdbcType=ARRAY}, 
      #{isComplication,jdbcType=BIT}, #{complication,jdbcType=ARRAY}, #{tbTherapyType,jdbcType=VARCHAR}, 
      #{isAntituberculosisTherapy,jdbcType=BIT}, #{antituberculosisTherapyDrugName,jdbcType=ARRAY}, 
      #{fistAntituberculosisDrugTherapy,jdbcType=TIMESTAMP}, #{lastAntituberculosisDrugTherapy,jdbcType=TIMESTAMP}, 
      #{tbPatientDiscoveryWay,jdbcType=VARCHAR}, #{isTbSmearPositive,jdbcType=VARCHAR}, 
      #{firstTbSmearPositiveExamTime,jdbcType=TIMESTAMP}, #{isTbCulturePositive,jdbcType=VARCHAR}, 
      #{firstTbCulturePositiveExamTime,jdbcType=TIMESTAMP}, #{isTbAntibodyPositive,jdbcType=VARCHAR}, 
      #{isTbNucleicAcidPositive,jdbcType=VARCHAR}, #{firstTbNucleicAcidPositiveExamTime,jdbcType=TIMESTAMP}, 
      #{firstTbAntibodyPositiveExamTime,jdbcType=TIMESTAMP}, #{isTbInfectionTCellPositive,jdbcType=VARCHAR}, 
      #{firstTbInfectionTCellPositiveExamTime,jdbcType=TIMESTAMP}, #{isTbSkinPositive,jdbcType=VARCHAR}, 
      #{firstTbSkinPositiveExamTime,jdbcType=TIMESTAMP}, #{isTbDrugResistant,jdbcType=VARCHAR}, 
      #{tbDrugResistantDrugName,jdbcType=ARRAY}, #{tbDrugResistantType,jdbcType=VARCHAR}, 
      #{isAids,jdbcType=BIT}, #{isDiabetes,jdbcType=BIT}, #{isCopd,jdbcType=BIT}, #{isInterstitialLungDisease,jdbcType=BIT}, 
      #{isPulmonaryEmbolism,jdbcType=BIT}, #{isAgeGe65,jdbcType=BIT}, #{tuberculosisType,jdbcType=ARRAY}, 
      #{pulmonaryTuberculosisType,jdbcType=VARCHAR}, #{extraPulmonaryTuberculosisType,jdbcType=ARRAY}, 
      #{isDrugAllergyHistory,jdbcType=BIT}, #{allergyDrugName,jdbcType=ARRAY}, #{smokingHistory,jdbcType=VARCHAR}, 
      #{isDustExposureHistory,jdbcType=BIT}, #{isIsaTherapy,jdbcType=BIT}, #{isaTherapyDrugName,jdbcType=ARRAY}, 
      #{hormoneTherapyDrugName,jdbcType=ARRAY}, #{isHormoneTherapy,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.xinjiang.model.KeyEvent">
    insert into "public"."key_event"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        "id",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="firstVisitDatetime != null">
        "first_visit_datetime",
      </if>
      <if test="firstVisitAge != null">
        "first_visit_age",
      </if>
      <if test="firstVisitDept != null">
        "first_visit_dept",
      </if>
      <if test="firstVisitDoctor != null">
        "first_visit_doctor",
      </if>
      <if test="diseaseTime != null">
        "disease_time",
      </if>
      <if test="symptomSign != null">
        "symptom_sign",
      </if>
      <if test="isComplication != null">
        "is_complication",
      </if>
      <if test="complication != null">
        "complication",
      </if>
      <if test="tbTherapyType != null">
        "tb_therapy_type",
      </if>
      <if test="isAntituberculosisTherapy != null">
        "is_antituberculosis_therapy",
      </if>
      <if test="antituberculosisTherapyDrugName != null">
        "antituberculosis_therapy_drug_name",
      </if>
      <if test="fistAntituberculosisDrugTherapy != null">
        "fist_antituberculosis_drug_therapy",
      </if>
      <if test="lastAntituberculosisDrugTherapy != null">
        "last_antituberculosis_drug_therapy",
      </if>
      <if test="tbPatientDiscoveryWay != null">
        "tb_patient_discovery_way",
      </if>
      <if test="isTbSmearPositive != null">
        "is_tb_smear_positive",
      </if>
      <if test="firstTbSmearPositiveExamTime != null">
        "first_tb_smear_positive_exam_time",
      </if>
      <if test="isTbCulturePositive != null">
        "is_tb_culture_positive",
      </if>
      <if test="firstTbCulturePositiveExamTime != null">
        "first_tb_culture_positive_exam_time",
      </if>
      <if test="isTbAntibodyPositive != null">
        "is_tb_antibody_positive",
      </if>
      <if test="isTbNucleicAcidPositive != null">
        "is_tb_nucleic_acid_positive",
      </if>
      <if test="firstTbNucleicAcidPositiveExamTime != null">
        "first_tb_nucleic_acid_positive_exam_time",
      </if>
      <if test="firstTbAntibodyPositiveExamTime != null">
        "first_tb_antibody_positive_exam_time",
      </if>
      <if test="isTbInfectionTCellPositive != null">
        "is_tb_infection_t_cell_positive",
      </if>
      <if test="firstTbInfectionTCellPositiveExamTime != null">
        "first_tb_infection_t_cell_positive_exam_time",
      </if>
      <if test="isTbSkinPositive != null">
        "is_tb_skin_positive",
      </if>
      <if test="firstTbSkinPositiveExamTime != null">
        "first_tb_skin_positive_exam_time",
      </if>
      <if test="isTbDrugResistant != null">
        "is_tb_drug_resistant",
      </if>
      <if test="tbDrugResistantDrugName != null">
        "tb_drug_resistant_drug_name",
      </if>
      <if test="tbDrugResistantType != null">
        "tb_drug_resistant_type",
      </if>
      <if test="isAids != null">
        "is_aids",
      </if>
      <if test="isDiabetes != null">
        "is_diabetes",
      </if>
      <if test="isCopd != null">
        "is_copd",
      </if>
      <if test="isInterstitialLungDisease != null">
        "is_interstitial_lung_disease",
      </if>
      <if test="isPulmonaryEmbolism != null">
        "is_pulmonary_embolism",
      </if>
      <if test="isAgeGe65 != null">
        "is_age_ge_65",
      </if>
      <if test="tuberculosisType != null">
        "tuberculosis_type",
      </if>
      <if test="pulmonaryTuberculosisType != null">
        "pulmonary_tuberculosis_type",
      </if>
      <if test="extraPulmonaryTuberculosisType != null">
        "extra_pulmonary_tuberculosis_type",
      </if>
      <if test="isDrugAllergyHistory != null">
        "is_drug_allergy_history",
      </if>
      <if test="allergyDrugName != null">
        "allergy_drug_name",
      </if>
      <if test="smokingHistory != null">
        "smoking_history",
      </if>
      <if test="isDustExposureHistory != null">
        "is_dust_exposure_history",
      </if>
      <if test="isIsaTherapy != null">
        "is_isa_therapy",
      </if>
      <if test="isaTherapyDrugName != null">
        "isa_therapy_drug_name",
      </if>
      <if test="hormoneTherapyDrugName != null">
        "hormone_therapy_drug_name",
      </if>
      <if test="isHormoneTherapy != null">
        "is_hormone_therapy",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="firstVisitDatetime != null">
        #{firstVisitDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="firstVisitAge != null">
        #{firstVisitAge,jdbcType=INTEGER},
      </if>
      <if test="firstVisitDept != null">
        #{firstVisitDept,jdbcType=VARCHAR},
      </if>
      <if test="firstVisitDoctor != null">
        #{firstVisitDoctor,jdbcType=VARCHAR},
      </if>
      <if test="diseaseTime != null">
        #{diseaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="symptomSign != null">
        #{symptomSign,jdbcType=ARRAY},
      </if>
      <if test="isComplication != null">
        #{isComplication,jdbcType=BIT},
      </if>
      <if test="complication != null">
        #{complication,jdbcType=ARRAY},
      </if>
      <if test="tbTherapyType != null">
        #{tbTherapyType,jdbcType=VARCHAR},
      </if>
      <if test="isAntituberculosisTherapy != null">
        #{isAntituberculosisTherapy,jdbcType=BIT},
      </if>
      <if test="antituberculosisTherapyDrugName != null">
        #{antituberculosisTherapyDrugName,jdbcType=ARRAY},
      </if>
      <if test="fistAntituberculosisDrugTherapy != null">
        #{fistAntituberculosisDrugTherapy,jdbcType=TIMESTAMP},
      </if>
      <if test="lastAntituberculosisDrugTherapy != null">
        #{lastAntituberculosisDrugTherapy,jdbcType=TIMESTAMP},
      </if>
      <if test="tbPatientDiscoveryWay != null">
        #{tbPatientDiscoveryWay,jdbcType=VARCHAR},
      </if>
      <if test="isTbSmearPositive != null">
        #{isTbSmearPositive,jdbcType=VARCHAR},
      </if>
      <if test="firstTbSmearPositiveExamTime != null">
        #{firstTbSmearPositiveExamTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isTbCulturePositive != null">
        #{isTbCulturePositive,jdbcType=VARCHAR},
      </if>
      <if test="firstTbCulturePositiveExamTime != null">
        #{firstTbCulturePositiveExamTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isTbAntibodyPositive != null">
        #{isTbAntibodyPositive,jdbcType=VARCHAR},
      </if>
      <if test="isTbNucleicAcidPositive != null">
        #{isTbNucleicAcidPositive,jdbcType=VARCHAR},
      </if>
      <if test="firstTbNucleicAcidPositiveExamTime != null">
        #{firstTbNucleicAcidPositiveExamTime,jdbcType=TIMESTAMP},
      </if>
      <if test="firstTbAntibodyPositiveExamTime != null">
        #{firstTbAntibodyPositiveExamTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isTbInfectionTCellPositive != null">
        #{isTbInfectionTCellPositive,jdbcType=VARCHAR},
      </if>
      <if test="firstTbInfectionTCellPositiveExamTime != null">
        #{firstTbInfectionTCellPositiveExamTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isTbSkinPositive != null">
        #{isTbSkinPositive,jdbcType=VARCHAR},
      </if>
      <if test="firstTbSkinPositiveExamTime != null">
        #{firstTbSkinPositiveExamTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isTbDrugResistant != null">
        #{isTbDrugResistant,jdbcType=VARCHAR},
      </if>
      <if test="tbDrugResistantDrugName != null">
        #{tbDrugResistantDrugName,jdbcType=ARRAY},
      </if>
      <if test="tbDrugResistantType != null">
        #{tbDrugResistantType,jdbcType=VARCHAR},
      </if>
      <if test="isAids != null">
        #{isAids,jdbcType=BIT},
      </if>
      <if test="isDiabetes != null">
        #{isDiabetes,jdbcType=BIT},
      </if>
      <if test="isCopd != null">
        #{isCopd,jdbcType=BIT},
      </if>
      <if test="isInterstitialLungDisease != null">
        #{isInterstitialLungDisease,jdbcType=BIT},
      </if>
      <if test="isPulmonaryEmbolism != null">
        #{isPulmonaryEmbolism,jdbcType=BIT},
      </if>
      <if test="isAgeGe65 != null">
        #{isAgeGe65,jdbcType=BIT},
      </if>
      <if test="tuberculosisType != null">
        #{tuberculosisType,jdbcType=ARRAY},
      </if>
      <if test="pulmonaryTuberculosisType != null">
        #{pulmonaryTuberculosisType,jdbcType=VARCHAR},
      </if>
      <if test="extraPulmonaryTuberculosisType != null">
        #{extraPulmonaryTuberculosisType,jdbcType=ARRAY},
      </if>
      <if test="isDrugAllergyHistory != null">
        #{isDrugAllergyHistory,jdbcType=BIT},
      </if>
      <if test="allergyDrugName != null">
        #{allergyDrugName,jdbcType=ARRAY},
      </if>
      <if test="smokingHistory != null">
        #{smokingHistory,jdbcType=VARCHAR},
      </if>
      <if test="isDustExposureHistory != null">
        #{isDustExposureHistory,jdbcType=BIT},
      </if>
      <if test="isIsaTherapy != null">
        #{isIsaTherapy,jdbcType=BIT},
      </if>
      <if test="isaTherapyDrugName != null">
        #{isaTherapyDrugName,jdbcType=ARRAY},
      </if>
      <if test="hormoneTherapyDrugName != null">
        #{hormoneTherapyDrugName,jdbcType=ARRAY},
      </if>
      <if test="isHormoneTherapy != null">
        #{isHormoneTherapy,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.xinjiang.model.KeyEventExample" resultType="java.lang.Long">
    select count(*) from "public"."key_event"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."key_event"
    <set>
      <if test="record.id != null">
        "id" = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.firstVisitDatetime != null">
        "first_visit_datetime" = #{record.firstVisitDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.firstVisitAge != null">
        "first_visit_age" = #{record.firstVisitAge,jdbcType=INTEGER},
      </if>
      <if test="record.firstVisitDept != null">
        "first_visit_dept" = #{record.firstVisitDept,jdbcType=VARCHAR},
      </if>
      <if test="record.firstVisitDoctor != null">
        "first_visit_doctor" = #{record.firstVisitDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.diseaseTime != null">
        "disease_time" = #{record.diseaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.symptomSign != null">
        "symptom_sign" = #{record.symptomSign,jdbcType=ARRAY},
      </if>
      <if test="record.isComplication != null">
        "is_complication" = #{record.isComplication,jdbcType=BIT},
      </if>
      <if test="record.complication != null">
        "complication" = #{record.complication,jdbcType=ARRAY},
      </if>
      <if test="record.tbTherapyType != null">
        "tb_therapy_type" = #{record.tbTherapyType,jdbcType=VARCHAR},
      </if>
      <if test="record.isAntituberculosisTherapy != null">
        "is_antituberculosis_therapy" = #{record.isAntituberculosisTherapy,jdbcType=BIT},
      </if>
      <if test="record.antituberculosisTherapyDrugName != null">
        "antituberculosis_therapy_drug_name" = #{record.antituberculosisTherapyDrugName,jdbcType=ARRAY},
      </if>
      <if test="record.fistAntituberculosisDrugTherapy != null">
        "fist_antituberculosis_drug_therapy" = #{record.fistAntituberculosisDrugTherapy,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastAntituberculosisDrugTherapy != null">
        "last_antituberculosis_drug_therapy" = #{record.lastAntituberculosisDrugTherapy,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tbPatientDiscoveryWay != null">
        "tb_patient_discovery_way" = #{record.tbPatientDiscoveryWay,jdbcType=VARCHAR},
      </if>
      <if test="record.isTbSmearPositive != null">
        "is_tb_smear_positive" = #{record.isTbSmearPositive,jdbcType=VARCHAR},
      </if>
      <if test="record.firstTbSmearPositiveExamTime != null">
        "first_tb_smear_positive_exam_time" = #{record.firstTbSmearPositiveExamTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isTbCulturePositive != null">
        "is_tb_culture_positive" = #{record.isTbCulturePositive,jdbcType=VARCHAR},
      </if>
      <if test="record.firstTbCulturePositiveExamTime != null">
        "first_tb_culture_positive_exam_time" = #{record.firstTbCulturePositiveExamTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isTbAntibodyPositive != null">
        "is_tb_antibody_positive" = #{record.isTbAntibodyPositive,jdbcType=VARCHAR},
      </if>
      <if test="record.isTbNucleicAcidPositive != null">
        "is_tb_nucleic_acid_positive" = #{record.isTbNucleicAcidPositive,jdbcType=VARCHAR},
      </if>
      <if test="record.firstTbNucleicAcidPositiveExamTime != null">
        "first_tb_nucleic_acid_positive_exam_time" = #{record.firstTbNucleicAcidPositiveExamTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.firstTbAntibodyPositiveExamTime != null">
        "first_tb_antibody_positive_exam_time" = #{record.firstTbAntibodyPositiveExamTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isTbInfectionTCellPositive != null">
        "is_tb_infection_t_cell_positive" = #{record.isTbInfectionTCellPositive,jdbcType=VARCHAR},
      </if>
      <if test="record.firstTbInfectionTCellPositiveExamTime != null">
        "first_tb_infection_t_cell_positive_exam_time" = #{record.firstTbInfectionTCellPositiveExamTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isTbSkinPositive != null">
        "is_tb_skin_positive" = #{record.isTbSkinPositive,jdbcType=VARCHAR},
      </if>
      <if test="record.firstTbSkinPositiveExamTime != null">
        "first_tb_skin_positive_exam_time" = #{record.firstTbSkinPositiveExamTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isTbDrugResistant != null">
        "is_tb_drug_resistant" = #{record.isTbDrugResistant,jdbcType=VARCHAR},
      </if>
      <if test="record.tbDrugResistantDrugName != null">
        "tb_drug_resistant_drug_name" = #{record.tbDrugResistantDrugName,jdbcType=ARRAY},
      </if>
      <if test="record.tbDrugResistantType != null">
        "tb_drug_resistant_type" = #{record.tbDrugResistantType,jdbcType=VARCHAR},
      </if>
      <if test="record.isAids != null">
        "is_aids" = #{record.isAids,jdbcType=BIT},
      </if>
      <if test="record.isDiabetes != null">
        "is_diabetes" = #{record.isDiabetes,jdbcType=BIT},
      </if>
      <if test="record.isCopd != null">
        "is_copd" = #{record.isCopd,jdbcType=BIT},
      </if>
      <if test="record.isInterstitialLungDisease != null">
        "is_interstitial_lung_disease" = #{record.isInterstitialLungDisease,jdbcType=BIT},
      </if>
      <if test="record.isPulmonaryEmbolism != null">
        "is_pulmonary_embolism" = #{record.isPulmonaryEmbolism,jdbcType=BIT},
      </if>
      <if test="record.isAgeGe65 != null">
        "is_age_ge_65" = #{record.isAgeGe65,jdbcType=BIT},
      </if>
      <if test="record.tuberculosisType != null">
        "tuberculosis_type" = #{record.tuberculosisType,jdbcType=ARRAY},
      </if>
      <if test="record.pulmonaryTuberculosisType != null">
        "pulmonary_tuberculosis_type" = #{record.pulmonaryTuberculosisType,jdbcType=VARCHAR},
      </if>
      <if test="record.extraPulmonaryTuberculosisType != null">
        "extra_pulmonary_tuberculosis_type" = #{record.extraPulmonaryTuberculosisType,jdbcType=ARRAY},
      </if>
      <if test="record.isDrugAllergyHistory != null">
        "is_drug_allergy_history" = #{record.isDrugAllergyHistory,jdbcType=BIT},
      </if>
      <if test="record.allergyDrugName != null">
        "allergy_drug_name" = #{record.allergyDrugName,jdbcType=ARRAY},
      </if>
      <if test="record.smokingHistory != null">
        "smoking_history" = #{record.smokingHistory,jdbcType=VARCHAR},
      </if>
      <if test="record.isDustExposureHistory != null">
        "is_dust_exposure_history" = #{record.isDustExposureHistory,jdbcType=BIT},
      </if>
      <if test="record.isIsaTherapy != null">
        "is_isa_therapy" = #{record.isIsaTherapy,jdbcType=BIT},
      </if>
      <if test="record.isaTherapyDrugName != null">
        "isa_therapy_drug_name" = #{record.isaTherapyDrugName,jdbcType=ARRAY},
      </if>
      <if test="record.hormoneTherapyDrugName != null">
        "hormone_therapy_drug_name" = #{record.hormoneTherapyDrugName,jdbcType=ARRAY},
      </if>
      <if test="record.isHormoneTherapy != null">
        "is_hormone_therapy" = #{record.isHormoneTherapy,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."key_event"
    set "id" = #{record.id,jdbcType=INTEGER},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "first_visit_datetime" = #{record.firstVisitDatetime,jdbcType=TIMESTAMP},
      "first_visit_age" = #{record.firstVisitAge,jdbcType=INTEGER},
      "first_visit_dept" = #{record.firstVisitDept,jdbcType=VARCHAR},
      "first_visit_doctor" = #{record.firstVisitDoctor,jdbcType=VARCHAR},
      "disease_time" = #{record.diseaseTime,jdbcType=TIMESTAMP},
      "symptom_sign" = #{record.symptomSign,jdbcType=ARRAY},
      "is_complication" = #{record.isComplication,jdbcType=BIT},
      "complication" = #{record.complication,jdbcType=ARRAY},
      "tb_therapy_type" = #{record.tbTherapyType,jdbcType=VARCHAR},
      "is_antituberculosis_therapy" = #{record.isAntituberculosisTherapy,jdbcType=BIT},
      "antituberculosis_therapy_drug_name" = #{record.antituberculosisTherapyDrugName,jdbcType=ARRAY},
      "fist_antituberculosis_drug_therapy" = #{record.fistAntituberculosisDrugTherapy,jdbcType=TIMESTAMP},
      "last_antituberculosis_drug_therapy" = #{record.lastAntituberculosisDrugTherapy,jdbcType=TIMESTAMP},
      "tb_patient_discovery_way" = #{record.tbPatientDiscoveryWay,jdbcType=VARCHAR},
      "is_tb_smear_positive" = #{record.isTbSmearPositive,jdbcType=VARCHAR},
      "first_tb_smear_positive_exam_time" = #{record.firstTbSmearPositiveExamTime,jdbcType=TIMESTAMP},
      "is_tb_culture_positive" = #{record.isTbCulturePositive,jdbcType=VARCHAR},
      "first_tb_culture_positive_exam_time" = #{record.firstTbCulturePositiveExamTime,jdbcType=TIMESTAMP},
      "is_tb_antibody_positive" = #{record.isTbAntibodyPositive,jdbcType=VARCHAR},
      "is_tb_nucleic_acid_positive" = #{record.isTbNucleicAcidPositive,jdbcType=VARCHAR},
      "first_tb_nucleic_acid_positive_exam_time" = #{record.firstTbNucleicAcidPositiveExamTime,jdbcType=TIMESTAMP},
      "first_tb_antibody_positive_exam_time" = #{record.firstTbAntibodyPositiveExamTime,jdbcType=TIMESTAMP},
      "is_tb_infection_t_cell_positive" = #{record.isTbInfectionTCellPositive,jdbcType=VARCHAR},
      "first_tb_infection_t_cell_positive_exam_time" = #{record.firstTbInfectionTCellPositiveExamTime,jdbcType=TIMESTAMP},
      "is_tb_skin_positive" = #{record.isTbSkinPositive,jdbcType=VARCHAR},
      "first_tb_skin_positive_exam_time" = #{record.firstTbSkinPositiveExamTime,jdbcType=TIMESTAMP},
      "is_tb_drug_resistant" = #{record.isTbDrugResistant,jdbcType=VARCHAR},
      "tb_drug_resistant_drug_name" = #{record.tbDrugResistantDrugName,jdbcType=ARRAY},
      "tb_drug_resistant_type" = #{record.tbDrugResistantType,jdbcType=VARCHAR},
      "is_aids" = #{record.isAids,jdbcType=BIT},
      "is_diabetes" = #{record.isDiabetes,jdbcType=BIT},
      "is_copd" = #{record.isCopd,jdbcType=BIT},
      "is_interstitial_lung_disease" = #{record.isInterstitialLungDisease,jdbcType=BIT},
      "is_pulmonary_embolism" = #{record.isPulmonaryEmbolism,jdbcType=BIT},
      "is_age_ge_65" = #{record.isAgeGe65,jdbcType=BIT},
      "tuberculosis_type" = #{record.tuberculosisType,jdbcType=ARRAY},
      "pulmonary_tuberculosis_type" = #{record.pulmonaryTuberculosisType,jdbcType=VARCHAR},
      "extra_pulmonary_tuberculosis_type" = #{record.extraPulmonaryTuberculosisType,jdbcType=ARRAY},
      "is_drug_allergy_history" = #{record.isDrugAllergyHistory,jdbcType=BIT},
      "allergy_drug_name" = #{record.allergyDrugName,jdbcType=ARRAY},
      "smoking_history" = #{record.smokingHistory,jdbcType=VARCHAR},
      "is_dust_exposure_history" = #{record.isDustExposureHistory,jdbcType=BIT},
      "is_isa_therapy" = #{record.isIsaTherapy,jdbcType=BIT},
      "isa_therapy_drug_name" = #{record.isaTherapyDrugName,jdbcType=ARRAY},
      "hormone_therapy_drug_name" = #{record.hormoneTherapyDrugName,jdbcType=ARRAY},
      "is_hormone_therapy" = #{record.isHormoneTherapy,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.xinjiang.model.KeyEvent">
    update "public"."key_event"
    <set>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="firstVisitDatetime != null">
        "first_visit_datetime" = #{firstVisitDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="firstVisitAge != null">
        "first_visit_age" = #{firstVisitAge,jdbcType=INTEGER},
      </if>
      <if test="firstVisitDept != null">
        "first_visit_dept" = #{firstVisitDept,jdbcType=VARCHAR},
      </if>
      <if test="firstVisitDoctor != null">
        "first_visit_doctor" = #{firstVisitDoctor,jdbcType=VARCHAR},
      </if>
      <if test="diseaseTime != null">
        "disease_time" = #{diseaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="symptomSign != null">
        "symptom_sign" = #{symptomSign,jdbcType=ARRAY},
      </if>
      <if test="isComplication != null">
        "is_complication" = #{isComplication,jdbcType=BIT},
      </if>
      <if test="complication != null">
        "complication" = #{complication,jdbcType=ARRAY},
      </if>
      <if test="tbTherapyType != null">
        "tb_therapy_type" = #{tbTherapyType,jdbcType=VARCHAR},
      </if>
      <if test="isAntituberculosisTherapy != null">
        "is_antituberculosis_therapy" = #{isAntituberculosisTherapy,jdbcType=BIT},
      </if>
      <if test="antituberculosisTherapyDrugName != null">
        "antituberculosis_therapy_drug_name" = #{antituberculosisTherapyDrugName,jdbcType=ARRAY},
      </if>
      <if test="fistAntituberculosisDrugTherapy != null">
        "fist_antituberculosis_drug_therapy" = #{fistAntituberculosisDrugTherapy,jdbcType=TIMESTAMP},
      </if>
      <if test="lastAntituberculosisDrugTherapy != null">
        "last_antituberculosis_drug_therapy" = #{lastAntituberculosisDrugTherapy,jdbcType=TIMESTAMP},
      </if>
      <if test="tbPatientDiscoveryWay != null">
        "tb_patient_discovery_way" = #{tbPatientDiscoveryWay,jdbcType=VARCHAR},
      </if>
      <if test="isTbSmearPositive != null">
        "is_tb_smear_positive" = #{isTbSmearPositive,jdbcType=VARCHAR},
      </if>
      <if test="firstTbSmearPositiveExamTime != null">
        "first_tb_smear_positive_exam_time" = #{firstTbSmearPositiveExamTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isTbCulturePositive != null">
        "is_tb_culture_positive" = #{isTbCulturePositive,jdbcType=VARCHAR},
      </if>
      <if test="firstTbCulturePositiveExamTime != null">
        "first_tb_culture_positive_exam_time" = #{firstTbCulturePositiveExamTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isTbAntibodyPositive != null">
        "is_tb_antibody_positive" = #{isTbAntibodyPositive,jdbcType=VARCHAR},
      </if>
      <if test="isTbNucleicAcidPositive != null">
        "is_tb_nucleic_acid_positive" = #{isTbNucleicAcidPositive,jdbcType=VARCHAR},
      </if>
      <if test="firstTbNucleicAcidPositiveExamTime != null">
        "first_tb_nucleic_acid_positive_exam_time" = #{firstTbNucleicAcidPositiveExamTime,jdbcType=TIMESTAMP},
      </if>
      <if test="firstTbAntibodyPositiveExamTime != null">
        "first_tb_antibody_positive_exam_time" = #{firstTbAntibodyPositiveExamTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isTbInfectionTCellPositive != null">
        "is_tb_infection_t_cell_positive" = #{isTbInfectionTCellPositive,jdbcType=VARCHAR},
      </if>
      <if test="firstTbInfectionTCellPositiveExamTime != null">
        "first_tb_infection_t_cell_positive_exam_time" = #{firstTbInfectionTCellPositiveExamTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isTbSkinPositive != null">
        "is_tb_skin_positive" = #{isTbSkinPositive,jdbcType=VARCHAR},
      </if>
      <if test="firstTbSkinPositiveExamTime != null">
        "first_tb_skin_positive_exam_time" = #{firstTbSkinPositiveExamTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isTbDrugResistant != null">
        "is_tb_drug_resistant" = #{isTbDrugResistant,jdbcType=VARCHAR},
      </if>
      <if test="tbDrugResistantDrugName != null">
        "tb_drug_resistant_drug_name" = #{tbDrugResistantDrugName,jdbcType=ARRAY},
      </if>
      <if test="tbDrugResistantType != null">
        "tb_drug_resistant_type" = #{tbDrugResistantType,jdbcType=VARCHAR},
      </if>
      <if test="isAids != null">
        "is_aids" = #{isAids,jdbcType=BIT},
      </if>
      <if test="isDiabetes != null">
        "is_diabetes" = #{isDiabetes,jdbcType=BIT},
      </if>
      <if test="isCopd != null">
        "is_copd" = #{isCopd,jdbcType=BIT},
      </if>
      <if test="isInterstitialLungDisease != null">
        "is_interstitial_lung_disease" = #{isInterstitialLungDisease,jdbcType=BIT},
      </if>
      <if test="isPulmonaryEmbolism != null">
        "is_pulmonary_embolism" = #{isPulmonaryEmbolism,jdbcType=BIT},
      </if>
      <if test="isAgeGe65 != null">
        "is_age_ge_65" = #{isAgeGe65,jdbcType=BIT},
      </if>
      <if test="tuberculosisType != null">
        "tuberculosis_type" = #{tuberculosisType,jdbcType=ARRAY},
      </if>
      <if test="pulmonaryTuberculosisType != null">
        "pulmonary_tuberculosis_type" = #{pulmonaryTuberculosisType,jdbcType=VARCHAR},
      </if>
      <if test="extraPulmonaryTuberculosisType != null">
        "extra_pulmonary_tuberculosis_type" = #{extraPulmonaryTuberculosisType,jdbcType=ARRAY},
      </if>
      <if test="isDrugAllergyHistory != null">
        "is_drug_allergy_history" = #{isDrugAllergyHistory,jdbcType=BIT},
      </if>
      <if test="allergyDrugName != null">
        "allergy_drug_name" = #{allergyDrugName,jdbcType=ARRAY},
      </if>
      <if test="smokingHistory != null">
        "smoking_history" = #{smokingHistory,jdbcType=VARCHAR},
      </if>
      <if test="isDustExposureHistory != null">
        "is_dust_exposure_history" = #{isDustExposureHistory,jdbcType=BIT},
      </if>
      <if test="isIsaTherapy != null">
        "is_isa_therapy" = #{isIsaTherapy,jdbcType=BIT},
      </if>
      <if test="isaTherapyDrugName != null">
        "isa_therapy_drug_name" = #{isaTherapyDrugName,jdbcType=ARRAY},
      </if>
      <if test="hormoneTherapyDrugName != null">
        "hormone_therapy_drug_name" = #{hormoneTherapyDrugName,jdbcType=ARRAY},
      </if>
      <if test="isHormoneTherapy != null">
        "is_hormone_therapy" = #{isHormoneTherapy,jdbcType=BIT},
      </if>
    </set>
    where "id" = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.xinjiang.model.KeyEvent">
    update "public"."key_event"
    set "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "first_visit_datetime" = #{firstVisitDatetime,jdbcType=TIMESTAMP},
      "first_visit_age" = #{firstVisitAge,jdbcType=INTEGER},
      "first_visit_dept" = #{firstVisitDept,jdbcType=VARCHAR},
      "first_visit_doctor" = #{firstVisitDoctor,jdbcType=VARCHAR},
      "disease_time" = #{diseaseTime,jdbcType=TIMESTAMP},
      "symptom_sign" = #{symptomSign,jdbcType=ARRAY},
      "is_complication" = #{isComplication,jdbcType=BIT},
      "complication" = #{complication,jdbcType=ARRAY},
      "tb_therapy_type" = #{tbTherapyType,jdbcType=VARCHAR},
      "is_antituberculosis_therapy" = #{isAntituberculosisTherapy,jdbcType=BIT},
      "antituberculosis_therapy_drug_name" = #{antituberculosisTherapyDrugName,jdbcType=ARRAY},
      "fist_antituberculosis_drug_therapy" = #{fistAntituberculosisDrugTherapy,jdbcType=TIMESTAMP},
      "last_antituberculosis_drug_therapy" = #{lastAntituberculosisDrugTherapy,jdbcType=TIMESTAMP},
      "tb_patient_discovery_way" = #{tbPatientDiscoveryWay,jdbcType=VARCHAR},
      "is_tb_smear_positive" = #{isTbSmearPositive,jdbcType=VARCHAR},
      "first_tb_smear_positive_exam_time" = #{firstTbSmearPositiveExamTime,jdbcType=TIMESTAMP},
      "is_tb_culture_positive" = #{isTbCulturePositive,jdbcType=VARCHAR},
      "first_tb_culture_positive_exam_time" = #{firstTbCulturePositiveExamTime,jdbcType=TIMESTAMP},
      "is_tb_antibody_positive" = #{isTbAntibodyPositive,jdbcType=VARCHAR},
      "is_tb_nucleic_acid_positive" = #{isTbNucleicAcidPositive,jdbcType=VARCHAR},
      "first_tb_nucleic_acid_positive_exam_time" = #{firstTbNucleicAcidPositiveExamTime,jdbcType=TIMESTAMP},
      "first_tb_antibody_positive_exam_time" = #{firstTbAntibodyPositiveExamTime,jdbcType=TIMESTAMP},
      "is_tb_infection_t_cell_positive" = #{isTbInfectionTCellPositive,jdbcType=VARCHAR},
      "first_tb_infection_t_cell_positive_exam_time" = #{firstTbInfectionTCellPositiveExamTime,jdbcType=TIMESTAMP},
      "is_tb_skin_positive" = #{isTbSkinPositive,jdbcType=VARCHAR},
      "first_tb_skin_positive_exam_time" = #{firstTbSkinPositiveExamTime,jdbcType=TIMESTAMP},
      "is_tb_drug_resistant" = #{isTbDrugResistant,jdbcType=VARCHAR},
      "tb_drug_resistant_drug_name" = #{tbDrugResistantDrugName,jdbcType=ARRAY},
      "tb_drug_resistant_type" = #{tbDrugResistantType,jdbcType=VARCHAR},
      "is_aids" = #{isAids,jdbcType=BIT},
      "is_diabetes" = #{isDiabetes,jdbcType=BIT},
      "is_copd" = #{isCopd,jdbcType=BIT},
      "is_interstitial_lung_disease" = #{isInterstitialLungDisease,jdbcType=BIT},
      "is_pulmonary_embolism" = #{isPulmonaryEmbolism,jdbcType=BIT},
      "is_age_ge_65" = #{isAgeGe65,jdbcType=BIT},
      "tuberculosis_type" = #{tuberculosisType,jdbcType=ARRAY},
      "pulmonary_tuberculosis_type" = #{pulmonaryTuberculosisType,jdbcType=VARCHAR},
      "extra_pulmonary_tuberculosis_type" = #{extraPulmonaryTuberculosisType,jdbcType=ARRAY},
      "is_drug_allergy_history" = #{isDrugAllergyHistory,jdbcType=BIT},
      "allergy_drug_name" = #{allergyDrugName,jdbcType=ARRAY},
      "smoking_history" = #{smokingHistory,jdbcType=VARCHAR},
      "is_dust_exposure_history" = #{isDustExposureHistory,jdbcType=BIT},
      "is_isa_therapy" = #{isIsaTherapy,jdbcType=BIT},
      "isa_therapy_drug_name" = #{isaTherapyDrugName,jdbcType=ARRAY},
      "hormone_therapy_drug_name" = #{hormoneTherapyDrugName,jdbcType=ARRAY},
      "is_hormone_therapy" = #{isHormoneTherapy,jdbcType=BIT}
    where "id" = #{id,jdbcType=INTEGER}
  </update>
</mapper>