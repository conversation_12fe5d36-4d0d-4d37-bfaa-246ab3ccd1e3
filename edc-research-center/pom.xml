<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.haoys.edc</groupId>
        <artifactId>edc-research-master</artifactId>
        <version>1.0.0</version>        
    </parent>

    <!-- 继承父项目的groupId和version，只需要指定artifactId -->
    <artifactId>edc-research-center</artifactId>
    <packaging>pom</packaging>

    <name>edc-research-center</name>
    <url>http://maven.apache.org</url>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <modules>
        <module>edc-research-service</module>
        <module>edc-research-api</module>
        <module>edc-research-quartz</module>
    </modules>

</project>
