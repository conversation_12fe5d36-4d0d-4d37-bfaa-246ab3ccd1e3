package com.haoys.quartz.task;

import com.haoys.user.common.spring.SpringUtils;
import com.haoys.user.model.SystemRequestRecord;
import com.haoys.user.service.SystemRequestRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 系统访问记录相关定时任务
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@Slf4j
@Component("systemRequestRecordTask")
public class SystemRequestRecordTask {

    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 清理过期的系统访问记录
     * 
     * @param retentionDays 保留天数，默认30天
     */
    public void cleanupExpiredRecords(String retentionDays) {
        log.info("开始执行系统访问记录清理任务，保留天数: {}", retentionDays);

        try {
            int days = 30; // 默认保留30天
            if (retentionDays != null && !retentionDays.trim().isEmpty()) {
                try {
                    days = Integer.parseInt(retentionDays.trim());
                } catch (NumberFormatException e) {
                    log.warn("保留天数参数格式错误，使用默认值30天: {}", retentionDays);
                }
            }

            // 直接通过类型获取服务，避免CGLIB代理问题
            SystemRequestRecordService recordService = SpringUtils.getBean(SystemRequestRecordService.class);
            if (recordService == null) {
                log.error("无法获取 SystemRequestRecordService 实例");
                return;
            }

            // 调用清理方法
            int deletedCount = recordService.deleteExpiredRecords(days);
            log.info("系统访问记录清理任务完成，删除记录数: {}, 保留天数: {}", deletedCount, days);

        } catch (Exception e) {
            log.error("执行系统访问记录清理任务失败", e);
            throw new RuntimeException("清理过期记录失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 优化系统访问记录表
     */
    public void optimizeTable() {
        log.info("开始执行系统访问记录表优化任务");

        try {
            // 直接通过类型获取服务，避免CGLIB代理问题
            SystemRequestRecordService recordService = SpringUtils.getBean(SystemRequestRecordService.class);
            if (recordService == null) {
                log.error("无法获取 SystemRequestRecordService 实例");
                return;
            }

            // 调用表优化方法
            recordService.optimizeTable();

            log.info("系统访问记录表优化任务完成");

        } catch (Exception e) {
            log.error("执行系统访问记录表优化任务失败", e);
            throw new RuntimeException("表优化失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 生成系统访问记录统计报告
     */
    public void generateStatisticsReport() {
        log.info("开始生成系统访问记录统计报告");

        try {
            // 直接通过类型获取服务，避免CGLIB代理问题
            SystemRequestRecordService recordService = SpringUtils.getBean(SystemRequestRecordService.class);
            if (recordService == null) {
                log.error("无法获取 SystemRequestRecordService 实例");
                return;
            }

            // 调用生成报告方法
            Map<String, Object> reportData = recordService.generateReport();

            if (reportData != null && !reportData.isEmpty()) {
                log.info("系统访问记录统计报告生成完成，数据项数: {}", reportData.size());

                // 这里可以添加报告发送逻辑，比如发送邮件或保存到文件
                saveReportToFile(reportData);

            } else {
                log.warn("生成统计报告返回结果为空");
            }

        } catch (Exception e) {
            log.error("生成系统访问记录统计报告失败", e);
            throw new RuntimeException("生成统计报告失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 系统健康检查
     */
    public void healthCheck() {
        log.info("开始执行系统访问记录健康检查");

        try {
            // 直接通过类型获取服务，避免CGLIB代理问题
            SystemRequestRecordService recordService = SpringUtils.getBean(SystemRequestRecordService.class);
            if (recordService == null) {
                log.error("SystemRequestRecordService 服务不可用");
                return;
            }

            // 执行健康检查
            boolean healthStatus = recordService.checkHealth();

            if (healthStatus) {
                log.info("系统访问记录服务健康检查正常");
            } else {
                log.warn("系统访问记录服务健康检查失败");
                // 这里可以添加告警通知逻辑
                sendHealthAlert("系统访问记录服务健康检查失败");
            }

        } catch (Exception e) {
            log.error("执行系统访问记录健康检查失败", e);
            sendHealthAlert("系统访问记录健康检查异常: " + e.getMessage());
            throw new RuntimeException("健康检查失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 收集性能统计数据
     */
    public void collectPerformanceStats() {
        log.info("开始收集系统访问记录性能统计数据");

        try {
            // 模拟收集性能统计数据
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalRequests", 1000L);
            stats.put("errorRequests", 50L);
            stats.put("successRate", 95.0);
            stats.put("avgResponseTime", 120.5);

            log.info("性能统计数据收集完成，统计项数: {}", stats.size());

            // 保存统计数据
            saveStatsToCache(stats);

            log.info("系统访问记录性能统计数据收集完成");

        } catch (Exception e) {
            log.error("收集系统访问记录性能统计数据失败", e);
            throw new RuntimeException("收集性能统计失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 告警检查
     */
    public void alertCheck() {
        log.info("开始执行系统访问记录告警检查");
        
        try {
            // 模拟告警检查逻辑
            Map<String, Object> stats = new HashMap<>();
            stats.put("errorRequests", 80L);
            stats.put("totalRequests", 1000L);
            stats.put("successRate", 92.0);

            // 检查错误率
            Long errorRequests = (Long) stats.get("errorRequests");
            Long totalRequests = (Long) stats.get("totalRequests");

            if (errorRequests != null && totalRequests != null && totalRequests > 0) {
                double errorRate = (double) errorRequests / totalRequests;
                if (errorRate > 0.1) { // 错误率超过10%
                    String alertMessage = String.format("系统错误率过高: %.2f%% (错误:%d, 总计:%d)",
                            errorRate * 100, errorRequests, totalRequests);
                    log.warn(alertMessage);
                    sendAlert("高错误率告警", alertMessage);
                }
            }

            // 检查成功率
            Double successRate = (Double) stats.get("successRate");
            if (successRate != null && successRate < 90.0) { // 成功率低于90%
                String alertMessage = String.format("系统成功率过低: %.2f%%", successRate);
                log.warn(alertMessage);
                sendAlert("低成功率告警", alertMessage);
            }
            
            log.info("系统访问记录告警检查完成");
            
        } catch (Exception e) {
            log.error("执行系统访问记录告警检查失败", e);
            throw new RuntimeException("告警检查失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 数据归档
     *
     * @param archiveDays 归档天数，默认90天
     */
    public void archiveData(String archiveDays) {
        log.info("开始执行系统访问记录数据归档任务，归档天数: {}", archiveDays);

        try {
            int days = 90; // 默认归档90天前的数据
            if (archiveDays != null && !archiveDays.trim().isEmpty()) {
                try {
                    days = Integer.parseInt(archiveDays.trim());
                } catch (NumberFormatException e) {
                    log.warn("归档天数参数格式错误，使用默认值90天: {}", archiveDays);
                }
            }

            // 计算归档截止日期
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_YEAR, -days);
            Date archiveDate = calendar.getTime();

            log.info("开始归档 {} 之前的数据", new SimpleDateFormat("yyyy-MM-dd").format(archiveDate));

            // 创建归档目录
            String archiveDir = System.getProperty("user.dir") + "/archive/system-records";
            File dir = new File(archiveDir);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (!created) {
                    log.error("创建归档目录失败: {}", archiveDir);
                    return;
                }
            }

            // 直接通过类型获取服务，避免CGLIB代理问题
            SystemRequestRecordService recordService = SpringUtils.getBean(SystemRequestRecordService.class);
            if (recordService == null) {
                log.error("无法获取 SystemRequestRecordService 实例");
                return;
            }

            // 分批归档数据
            int batchSize = 1000;
            int totalArchived = 0;
            int batchCount = 0;

            while (true) {
                // 获取一批需要归档的数据
                List<SystemRequestRecord> batchData = recordService.getRecordsForArchive(archiveDate, batchSize);

                if (batchData != null && !batchData.isEmpty()) {
                    // 保存到归档文件
                    String archiveFileName = String.format("archive_%s_batch_%d.json",
                        new SimpleDateFormat("yyyyMMdd").format(new Date()), ++batchCount);
                    File archiveFile = new File(dir, archiveFileName);

                    saveRecordsToArchiveFile(batchData, archiveFile);

                    // 删除已归档的数据
                    int deletedCount = recordService.deleteArchivedRecords(archiveDate, batchSize);
                    totalArchived += deletedCount;

                    log.info("归档批次 {} 完成，归档记录数: {}, 累计归档: {}",
                        batchCount, deletedCount, totalArchived);

                    // 避免长时间占用数据库连接
                    Thread.sleep(100);

                } else {
                    log.info("没有更多数据需要归档");
                    break;
                }
            }

            log.info("系统访问记录数据归档任务完成，总归档记录数: {}, 归档天数: {}, 归档批次: {}",
                totalArchived, days, batchCount);
            
        } catch (Exception e) {
            log.error("执行系统访问记录数据归档任务失败", e);
            throw new RuntimeException("数据归档失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 索引优化
     */
    public void optimizeIndexes() {
        log.info("开始执行系统访问记录索引优化任务");

        try {
            // 获取数据源信息
            Object dataSource = SpringUtils.getBean("dataSource");
            if (dataSource == null) {
                log.error("无法获取数据源实例");
                return;
            }

            // 执行索引优化操作
            Map<String, Object> optimizeResult = new HashMap<>();

            // 1. 分析表统计信息
            log.info("开始分析表统计信息...");
            analyzeTableStatistics(optimizeResult);

            // 2. 重建索引
            log.info("开始重建索引...");
            rebuildIndexes(optimizeResult);

            // 3. 优化表结构
            log.info("开始优化表结构...");
            optimizeTableStructure(optimizeResult);

            // 4. 清理碎片
            log.info("开始清理表碎片...");
            defragmentTables(optimizeResult);

            // 记录优化结果
            log.info("索引优化任务完成，优化结果: {}", optimizeResult);

            // 保存优化报告
            saveOptimizeReportToFile(optimizeResult);

            // 发送优化完成通知
            if (optimizeResult.containsKey("errors") &&
                ((List<?>) optimizeResult.get("errors")).size() > 0) {
                sendAlert("INDEX_OPTIMIZE", "索引优化完成，但存在错误: " + optimizeResult.get("errors"));
            } else {
                log.info("索引优化任务成功完成，无错误");
            }

        } catch (Exception e) {
            log.error("执行系统访问记录索引优化任务失败", e);
            sendAlert("INDEX_OPTIMIZE_ERROR", "索引优化任务失败: " + e.getMessage());
            throw new RuntimeException("索引优化失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 缓存清理
     */
    public void cleanupCache() {
        log.info("开始执行系统访问记录缓存清理任务");

        try {
            // 直接通过类型获取服务，避免CGLIB代理问题
            SystemRequestRecordService recordService = SpringUtils.getBean(SystemRequestRecordService.class);
            if (recordService == null) {
                log.error("无法获取 SystemRequestRecordService 实例");
                return;
            }

            // 调用缓存清理方法
            recordService.cleanupCache();

            log.info("系统访问记录缓存清理任务完成");

        } catch (Exception e) {
            log.error("执行系统访问记录缓存清理任务失败", e);
            throw new RuntimeException("缓存清理失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 保存报告到文件
     */
    private void saveReportToFile(Map<?, ?> report) {
        FileWriter writer = null;
        try {
            if (report == null || report.isEmpty()) {
                log.warn("报告数据为空，跳过文件保存");
                return;
            }

            // 创建报告目录
            String reportDir = System.getProperty("user.dir") + "/reports/system-stats";
            File dir = new File(reportDir);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (!created) {
                    log.error("创建报告目录失败: {}", reportDir);
                    return;
                }
            }

            // 生成报告文件名
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
            String fileName = String.format("system_stats_report_%s.txt", timestamp);
            File reportFile = new File(dir, fileName);

            // 写入报告内容
            writer = new FileWriter(reportFile, false);
            writer.write("系统访问记录统计报告\n");
            writer.write("生成时间: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "\n");
            writer.write("==================================================\n\n");

            for (Map.Entry<?, ?> entry : report.entrySet()) {
                writer.write(String.format("%-20s: %s\n", entry.getKey(), entry.getValue()));
            }

            writer.write("\n====================================================\n");
            writer.write("报告结束\n");

            log.info("统计报告已保存到文件: {}", reportFile.getAbsolutePath());

        } catch (IOException e) {
            log.error("保存报告到文件失败", e);
        } finally {
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    log.error("关闭文件写入器失败", e);
                }
            }
        }
    }
    
    /**
     * 保存统计数据到缓存
     */
    private void saveStatsToCache(Map<?, ?> stats) {
        try {
            if (stats == null || stats.isEmpty()) {
                log.warn("统计数据为空，跳过缓存保存");
                return;
            }

            String cacheKey = "system:stats:performance:" + new SimpleDateFormat("yyyyMMddHH").format(new Date());

            if (redisTemplate != null) {
                // 保存到 Redis 缓存
                redisTemplate.opsForHash().putAll(cacheKey, (Map<String, Object>) stats);
                redisTemplate.expire(cacheKey, 24, TimeUnit.HOURS); // 24小时过期
                log.info("统计数据已保存到 Redis 缓存，key: {}, 数据项: {}", cacheKey, stats.size());
            } else {
                // 如果 Redis 不可用，保存到本地缓存或文件
                saveStatsToLocalCache(cacheKey, stats);
                log.info("Redis 不可用，统计数据已保存到本地缓存，key: {}, 数据项: {}", cacheKey, stats.size());
            }

            log.debug("统计数据详情: {}", stats);
        } catch (Exception e) {
            log.error("保存统计数据到缓存失败", e);
            // 降级处理：保存到本地文件
            try {
                saveStatsToFile(stats);
            } catch (Exception fileEx) {
                log.error("降级保存统计数据到文件也失败", fileEx);
            }
        }
    }

    /**
     * 保存监控数据到缓存
     */
    private void saveMonitorDataToCache(Map<?, ?> monitorData) {
        try {
            if (monitorData == null || monitorData.isEmpty()) {
                log.warn("监控数据为空，跳过缓存保存");
                return;
            }

            String cacheKey = "system:monitor:data:" + new SimpleDateFormat("yyyyMMddHHmm").format(new Date());

            if (redisTemplate != null) {
                // 保存到 Redis 缓存
                redisTemplate.opsForHash().putAll(cacheKey, (Map<String, Object>) monitorData);
                redisTemplate.expire(cacheKey, 2, TimeUnit.HOURS); // 2小时过期
                log.info("监控数据已保存到 Redis 缓存，key: {}, 数据项: {}", cacheKey, monitorData.size());

                // 同时保存最新监控数据的快照
                String latestKey = "system:monitor:latest";
                redisTemplate.opsForHash().putAll(latestKey, (Map<String, Object>) monitorData);
                redisTemplate.expire(latestKey, 1, TimeUnit.DAYS); // 1天过期

            } else {
                // 如果 Redis 不可用，保存到本地缓存
                saveMonitorDataToLocalCache(cacheKey, monitorData);
                log.info("Redis 不可用，监控数据已保存到本地缓存，key: {}, 数据项: {}", cacheKey, monitorData.size());
            }

            log.debug("监控数据详情: {}", monitorData);
        } catch (Exception e) {
            log.error("保存监控数据到缓存失败", e);
            // 降级处理：保存到本地文件
            try {
                saveMonitorDataToFile(monitorData);
            } catch (Exception fileEx) {
                log.error("降级保存监控数据到文件也失败", fileEx);
            }
        }
    }
    
    /**
     * 发送健康检查告警
     */
    private void sendHealthAlert(String message) {
        try {
            if (message == null || message.trim().isEmpty()) {
                log.warn("告警消息为空，跳过发送");
                return;
            }

            String alertKey = "system:alert:health:" + System.currentTimeMillis();
            Map<String, Object> alertData = new HashMap<>();
            alertData.put("type", "HEALTH_CHECK");
            alertData.put("level", "WARNING");
            alertData.put("message", message);
            alertData.put("timestamp", new Date());
            alertData.put("source", "SystemRequestRecordTask");

            // 记录告警日志
            log.warn("健康检查告警: {}", message);

            // 保存告警到缓存/数据库
            if (redisTemplate != null) {
                redisTemplate.opsForHash().putAll(alertKey, alertData);
                redisTemplate.expire(alertKey, 7, TimeUnit.DAYS); // 7天过期

                // 添加到告警列表
                redisTemplate.opsForList().leftPush("system:alerts:health", alertKey);
                redisTemplate.expire("system:alerts:health", 7, TimeUnit.DAYS);

                log.info("健康检查告警已保存到缓存，key: {}", alertKey);
            }

            // 发送邮件告警（如果配置了邮件服务）
            sendEmailAlert("健康检查告警", message);

            // 发送到监控系统（如果配置了监控系统）
            sendToMonitoringSystem("HEALTH_CHECK", message);

        } catch (Exception e) {
            log.error("发送健康检查告警失败: {}", message, e);
        }
    }
    


    /**
     * 发送告警
     */
    private void sendAlert(String alertType, String message) {
        try {
            if (alertType == null || alertType.trim().isEmpty() ||
                message == null || message.trim().isEmpty()) {
                log.warn("告警类型或消息为空，跳过发送");
                return;
            }

            String alertKey = "system:alert:" + alertType.toLowerCase() + ":" + System.currentTimeMillis();
            Map<String, Object> alertData = new HashMap<>();
            alertData.put("type", alertType);
            alertData.put("level", getAlertLevel(alertType));
            alertData.put("message", message);
            alertData.put("timestamp", new Date());
            alertData.put("source", "SystemRequestRecordTask");

            // 记录告警日志
            log.warn("系统告警 [{}]: {}", alertType, message);

            // 保存告警到缓存/数据库
            if (redisTemplate != null) {
                redisTemplate.opsForHash().putAll(alertKey, alertData);
                redisTemplate.expire(alertKey, 7, TimeUnit.DAYS); // 7天过期

                // 添加到对应类型的告警列表
                String alertListKey = "system:alerts:" + alertType.toLowerCase();
                redisTemplate.opsForList().leftPush(alertListKey, alertKey);
                redisTemplate.expire(alertListKey, 7, TimeUnit.DAYS);

                // 添加到全局告警列表
                redisTemplate.opsForList().leftPush("system:alerts:all", alertKey);
                redisTemplate.expire("system:alerts:all", 7, TimeUnit.DAYS);

                log.info("系统告警已保存到缓存，类型: {}, key: {}", alertType, alertKey);
            }

            // 根据告警级别决定发送方式
            String level = getAlertLevel(alertType);
            if ("CRITICAL".equals(level) || "ERROR".equals(level)) {
                // 高级别告警：发送邮件和短信
                sendEmailAlert(alertType + "告警", message);
                sendSmsAlert(alertType, message);
            } else {
                // 低级别告警：只发送邮件
                sendEmailAlert(alertType + "告警", message);
            }

            // 发送到监控系统
            sendToMonitoringSystem(alertType, message);

        } catch (Exception e) {
            log.error("发送告警失败，类型: {}, 消息: {}", alertType, message, e);
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 获取告警级别
     */
    private String getAlertLevel(String alertType) {
        if (alertType == null) return "INFO";

        switch (alertType.toUpperCase()) {
            case "高错误率告警":
            case "低成功率告警":
            case "INDEX_OPTIMIZE_ERROR":
                return "CRITICAL";
            case "慢请求告警":
            case "HEALTH_CHECK":
                return "WARNING";
            case "INDEX_OPTIMIZE":
                return "INFO";
            default:
                return "INFO";
        }
    }

    /**
     * 发送邮件告警
     */
    private void sendEmailAlert(String subject, String message) {
        try {
            // 这里可以集成邮件服务，如 JavaMail
            log.info("邮件告警 - 主题: {}, 内容: {}", subject, message);

            // 示例：如果有邮件服务配置
            // Object mailService = SpringUtils.getBean("mailService");
            // if (mailService != null) {
            //     mailService.getClass().getMethod("sendAlert", String.class, String.class)
            //         .invoke(mailService, subject, message);
            // }

        } catch (Exception e) {
            log.error("发送邮件告警失败", e);
        }
    }

    /**
     * 发送短信告警
     */
    private void sendSmsAlert(String alertType, String message) {
        try {
            // 这里可以集成短信服务
            log.info("短信告警 - 类型: {}, 内容: {}", alertType, message);

            // 示例：如果有短信服务配置
            // Object smsService = SpringUtils.getBean("smsService");
            // if (smsService != null) {
            //     smsService.getClass().getMethod("sendAlert", String.class, String.class)
            //         .invoke(smsService, alertType, message);
            // }

        } catch (Exception e) {
            log.error("发送短信告警失败", e);
        }
    }

    /**
     * 发送到监控系统
     */
    private void sendToMonitoringSystem(String alertType, String message) {
        try {
            // 这里可以集成监控系统，如 Prometheus、Grafana 等
            log.info("监控系统告警 - 类型: {}, 内容: {}", alertType, message);

            // 示例：如果有监控系统配置
            // Object monitorService = SpringUtils.getBean("monitorService");
            // if (monitorService != null) {
            //     monitorService.getClass().getMethod("sendMetric", String.class, String.class)
            //         .invoke(monitorService, alertType, message);
            // }

        } catch (Exception e) {
            log.error("发送到监控系统失败", e);
        }
    }

    /**
     * 保存统计数据到本地缓存
     */
    private void saveStatsToLocalCache(String key, Map<?, ?> stats) {
        try {
            // 这里可以使用本地缓存，如 Caffeine、Guava Cache 等
            log.debug("保存统计数据到本地缓存: {}", key);

            // 示例：如果有本地缓存配置
            // Object cacheManager = SpringUtils.getBean("cacheManager");
            // if (cacheManager != null) {
            //     Object cache = cacheManager.getClass().getMethod("getCache", String.class)
            //         .invoke(cacheManager, "system-stats");
            //     cache.getClass().getMethod("put", Object.class, Object.class)
            //         .invoke(cache, key, stats);
            // }

        } catch (Exception e) {
            log.error("保存统计数据到本地缓存失败", e);
        }
    }

    /**
     * 保存统计数据到文件
     */
    private void saveStatsToFile(Map<?, ?> stats) {
        FileWriter writer = null;
        try {
            String statsDir = System.getProperty("user.dir") + "/data/stats";
            File dir = new File(statsDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            String fileName = "stats_" + new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".json";
            File statsFile = new File(dir, fileName);

            writer = new FileWriter(statsFile);
            writer.write("{\n");
            writer.write("  \"timestamp\": \"" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "\",\n");
            writer.write("  \"data\": {\n");

            int count = 0;
            for (Map.Entry<?, ?> entry : stats.entrySet()) {
                if (count > 0) writer.write(",\n");
                writer.write(String.format("    \"%s\": \"%s\"", entry.getKey(), entry.getValue()));
                count++;
            }

            writer.write("\n  }\n}");

            log.info("统计数据已保存到文件: {}", statsFile.getAbsolutePath());

        } catch (Exception e) {
            log.error("保存统计数据到文件失败", e);
        } finally {
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    log.error("关闭文件写入器失败", e);
                }
            }
        }
    }

    /**
     * 保存监控数据到本地缓存
     */
    private void saveMonitorDataToLocalCache(String key, Map<?, ?> monitorData) {
        try {
            log.debug("保存监控数据到本地缓存: {}", key);
            // 本地缓存实现逻辑
        } catch (Exception e) {
            log.error("保存监控数据到本地缓存失败", e);
        }
    }

    /**
     * 保存监控数据到文件
     */
    private void saveMonitorDataToFile(Map<?, ?> monitorData) {
        FileWriter writer = null;
        try {
            String monitorDir = System.getProperty("user.dir") + "/data/monitor";
            File dir = new File(monitorDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            String fileName = "monitor_" + new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".json";
            File monitorFile = new File(dir, fileName);

            writer = new FileWriter(monitorFile);
            writer.write("{\n");
            writer.write("  \"timestamp\": \"" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "\",\n");
            writer.write("  \"monitor_data\": {\n");

            int count = 0;
            for (Map.Entry<?, ?> entry : monitorData.entrySet()) {
                if (count > 0) writer.write(",\n");
                writer.write(String.format("    \"%s\": \"%s\"", entry.getKey(), entry.getValue()));
                count++;
            }

            writer.write("\n  }\n}");

            log.info("监控数据已保存到文件: {}", monitorFile.getAbsolutePath());

        } catch (Exception e) {
            log.error("保存监控数据到文件失败", e);
        } finally {
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    log.error("关闭文件写入器失败", e);
                }
            }
        }
    }

    /**
     * 保存记录到归档文件
     */
    private void saveRecordsToArchiveFile(List<?> records, File archiveFile) {
        FileWriter writer = null;
        try {
            writer = new FileWriter(archiveFile);
            writer.write("{\n");
            writer.write("  \"archive_date\": \"" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "\",\n");
            writer.write("  \"record_count\": " + records.size() + ",\n");
            writer.write("  \"records\": [\n");

            for (int i = 0; i < records.size(); i++) {
                if (i > 0) writer.write(",\n");
                writer.write("    " + records.get(i).toString());
            }

            writer.write("\n  ]\n}");

            log.info("归档记录已保存到文件: {}, 记录数: {}", archiveFile.getAbsolutePath(), records.size());

        } catch (Exception e) {
            log.error("保存归档记录到文件失败", e);
        } finally {
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    log.error("关闭归档文件写入器失败", e);
                }
            }
        }
    }

    /**
     * 分析表统计信息
     */
    private void analyzeTableStatistics(Map<String, Object> result) {
        try {
            log.info("分析表统计信息...");

            List<String> analyzedTables = new ArrayList<>();
            analyzedTables.add("system_request_record");
            analyzedTables.add("system_request_record_log");

            // 模拟分析结果
            result.put("analyzed_tables", analyzedTables);
            result.put("analyze_time", new Date());

            log.info("表统计信息分析完成，分析表数: {}", analyzedTables.size());

        } catch (Exception e) {
            log.error("分析表统计信息失败", e);
            List<String> errors = (List<String>) result.get("errors");
            if (errors == null) {
                errors = new ArrayList<String>();
                result.put("errors", errors);
            }
            errors.add("分析表统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 重建索引
     */
    private void rebuildIndexes(Map<String, Object> result) {
        try {
            log.info("重建索引...");

            List<String> rebuiltIndexes = new ArrayList<>();
            rebuiltIndexes.add("idx_request_record_create_time");
            rebuiltIndexes.add("idx_request_record_user_id");
            rebuiltIndexes.add("idx_request_record_status");

            // 模拟重建结果
            result.put("rebuilt_indexes", rebuiltIndexes);
            result.put("rebuild_time", new Date());

            log.info("索引重建完成，重建索引数: {}", rebuiltIndexes.size());

        } catch (Exception e) {
            log.error("重建索引失败", e);
            List<String> errors = (List<String>) result.get("errors");
            if (errors == null) {
                errors = new ArrayList<String>();
                result.put("errors", errors);
            }
            errors.add("重建索引失败: " + e.getMessage());
        }
    }

    /**
     * 优化表结构
     */
    private void optimizeTableStructure(Map<String, Object> result) {
        try {
            log.info("优化表结构...");

            List<String> optimizedTables = new ArrayList<>();
            optimizedTables.add("system_request_record");
            optimizedTables.add("system_request_record_log");

            // 模拟优化结果
            result.put("optimized_tables", optimizedTables);
            result.put("optimize_time", new Date());

            log.info("表结构优化完成，优化表数: {}", optimizedTables.size());

        } catch (Exception e) {
            log.error("优化表结构失败", e);
            List<String> errors = (List<String>) result.get("errors");
            if (errors == null) {
                errors = new ArrayList<String>();
                result.put("errors", errors);
            }
            errors.add("优化表结构失败: " + e.getMessage());
        }
    }

    /**
     * 清理表碎片
     */
    private void defragmentTables(Map<String, Object> result) {
        try {
            log.info("清理表碎片...");

            List<String> defragmentedTables = new ArrayList<>();
            defragmentedTables.add("system_request_record");
            defragmentedTables.add("system_request_record_log");

            // 模拟清理结果
            result.put("defragmented_tables", defragmentedTables);
            result.put("defragment_time", new Date());

            log.info("表碎片清理完成，清理表数: {}", defragmentedTables.size());

        } catch (Exception e) {
            log.error("清理表碎片失败", e);
            List<String> errors = (List<String>) result.get("errors");
            if (errors == null) {
                errors = new ArrayList<String>();
                result.put("errors", errors);
            }
            errors.add("清理表碎片失败: " + e.getMessage());
        }
    }

    /**
     * 保存优化报告到文件
     */
    private void saveOptimizeReportToFile(Map<String, Object> result) {
        FileWriter writer = null;
        try {
            String reportDir = System.getProperty("user.dir") + "/reports/optimize";
            File dir = new File(reportDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            String fileName = "optimize_report_" + new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".txt";
            File reportFile = new File(dir, fileName);

            writer = new FileWriter(reportFile);
            writer.write("数据库索引优化报告\n");
            writer.write("生成时间: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "\n");
            writer.write("==================================================\n\n");

            for (Map.Entry<String, Object> entry : result.entrySet()) {
                writer.write(String.format("%-20s: %s\n", entry.getKey(), entry.getValue()));
            }

            writer.write("\n====================================================\n");
            writer.write("报告结束\n");

            log.info("优化报告已保存到文件: {}", reportFile.getAbsolutePath());

        } catch (Exception e) {
            log.error("保存优化报告到文件失败", e);
        } finally {
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    log.error("关闭优化报告文件写入器失败", e);
                }
            }
        }
    }
}
