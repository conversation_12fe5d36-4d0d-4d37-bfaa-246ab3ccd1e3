package com.haoys.quartz.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统访问记录定时任务执行日志实体
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@Data
public class SystemRequestRecordJobLog implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 日志ID */
    private Long logId;
    
    /** 任务ID */
    private Long jobId;
    
    /** 任务名称 */
    private String jobName;
    
    /** 任务组名 */
    private String jobGroup;
    
    /** 调用目标字符串 */
    private String invokeTarget;
    
    /** 日志信息 */
    private String jobMessage;
    
    /** 执行状态（0正常 1失败） */
    private String status;
    
    /** 异常信息 */
    private String exceptionInfo;
    
    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    
    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    
    /** 执行时间（毫秒） */
    private Long executeTime;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /** 任务类型 */
    private Integer jobType;
    
    /** 执行参数 */
    private String jobParams;
    
    /** 执行结果 */
    private String executeResult;
    
    /** 执行节点 */
    private String executeNode;
    
    /** 服务器IP */
    private String serverIp;
    
    /** 服务器名称 */
    private String serverName;
    
    /** 进程ID */
    private String processId;
    
    /** 线程ID */
    private String threadId;
    
    /** 线程名称 */
    private String threadName;
    
    /** 内存使用量（MB） */
    private Long memoryUsage;
    
    /** CPU使用率（%） */
    private Double cpuUsage;
    
    /** 处理记录数 */
    private Long processedRecords;
    
    /** 成功记录数 */
    private Long successRecords;
    
    /** 失败记录数 */
    private Long failureRecords;
    
    /** 跳过记录数 */
    private Long skippedRecords;
    
    /** 数据库连接数 */
    private Integer dbConnections;
    
    /** 缓存命中率（%） */
    private Double cacheHitRate;
    
    /** 网络IO（KB） */
    private Long networkIo;
    
    /** 磁盘IO（KB） */
    private Long diskIo;
    
    /** 任务优先级 */
    private Integer priority;
    
    /** 重试次数 */
    private Integer retryCount;
    
    /** 是否最后一次重试 */
    private Boolean lastRetry;
    
    /** 触发器类型 */
    private String triggerType;
    
    /** 触发器名称 */
    private String triggerName;
    
    /** 触发器组 */
    private String triggerGroup;
    
    /** 预期执行时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date scheduledTime;
    
    /** 实际执行时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualTime;
    
    /** 延迟时间（毫秒） */
    private Long delayTime;
    
    /** 任务版本 */
    private String jobVersion;
    
    /** 执行环境 */
    private String environment;
    
    /** 配置快照 */
    private String configSnapshot;
    
    /** 执行上下文 */
    private String executeContext;
    
    /** 业务数据 */
    private String businessData;
    
    /** 扩展字段1 */
    private String ext1;
    
    /** 扩展字段2 */
    private String ext2;
    
    /** 扩展字段3 */
    private String ext3;
    
    /** 租户ID */
    private String tenantId;
    
    // 非数据库字段
    
    /** 状态名称 */
    private String statusName;
    
    /** 任务类型名称 */
    private String jobTypeName;
    
    /** 执行时间描述 */
    private String executeTimeDesc;
    
    /** 延迟时间描述 */
    private String delayTimeDesc;
    
    /** 成功率 */
    private Double successRate;
    
    /** 处理速度（记录/秒） */
    private Double processSpeed;
    
    /**
     * 获取状态名称
     */
    public String getStatusName() {
        if ("0".equals(this.status)) {
            return "成功";
        } else if ("1".equals(this.status)) {
            return "失败";
        }
        return "未知";
    }
    
    /**
     * 获取任务类型名称
     */
    public String getJobTypeName() {
        if (this.jobType == null) {
            return "未知";
        }
        switch (this.jobType) {
            case 1: return "数据清理";
            case 2: return "表优化";
            case 3: return "统计报告";
            case 4: return "健康检查";
            case 5: return "性能统计";
            case 6: return "告警检查";
            case 7: return "数据归档";
            case 8: return "索引优化";
            case 9: return "缓存清理";
            default: return "其他";
        }
    }
    
    /**
     * 获取执行时间描述
     */
    public String getExecuteTimeDesc() {
        if (this.executeTime == null) {
            return "未知";
        }
        
        if (this.executeTime < 1000) {
            return this.executeTime + "毫秒";
        } else if (this.executeTime < 60000) {
            return String.format("%.2f秒", this.executeTime / 1000.0);
        } else {
            long minutes = this.executeTime / 60000;
            long seconds = (this.executeTime % 60000) / 1000;
            return minutes + "分" + seconds + "秒";
        }
    }
    
    /**
     * 获取延迟时间描述
     */
    public String getDelayTimeDesc() {
        if (this.delayTime == null || this.delayTime <= 0) {
            return "无延迟";
        }
        
        if (this.delayTime < 1000) {
            return this.delayTime + "毫秒";
        } else if (this.delayTime < 60000) {
            return String.format("%.2f秒", this.delayTime / 1000.0);
        } else {
            long minutes = this.delayTime / 60000;
            long seconds = (this.delayTime % 60000) / 1000;
            return minutes + "分" + seconds + "秒";
        }
    }
    
    /**
     * 计算成功率
     */
    public Double getSuccessRate() {
        if (this.processedRecords == null || this.processedRecords == 0) {
            return 0.0;
        }
        
        long successCount = this.successRecords != null ? this.successRecords : 0;
        return (double) successCount / this.processedRecords * 100;
    }
    
    /**
     * 计算处理速度（记录/秒）
     */
    public Double getProcessSpeed() {
        if (this.processedRecords == null || this.executeTime == null || this.executeTime == 0) {
            return 0.0;
        }
        
        return (double) this.processedRecords / (this.executeTime / 1000.0);
    }
    
    /**
     * 检查执行是否成功
     */
    public boolean isSuccess() {
        return "0".equals(this.status);
    }
    
    /**
     * 检查执行是否失败
     */
    public boolean isFailure() {
        return "1".equals(this.status);
    }
    
    /**
     * 检查是否有异常信息
     */
    public boolean hasException() {
        return this.exceptionInfo != null && !this.exceptionInfo.trim().isEmpty();
    }
    
    /**
     * 检查是否有延迟
     */
    public boolean hasDelay() {
        return this.delayTime != null && this.delayTime > 0;
    }
    
    /**
     * 检查是否为重试执行
     */
    public boolean isRetryExecution() {
        return this.retryCount != null && this.retryCount > 0;
    }
}
