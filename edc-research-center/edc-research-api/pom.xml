<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" 
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.haoys.edc</groupId>
        <artifactId>edc-research-center</artifactId>
        <version>1.0.0</version>        
    </parent>

    <!-- 继承父项目的groupId和version -->
    <artifactId>edc-research-api</artifactId>
    <packaging>jar</packaging>

    <name>edc-research-center-api</name>
    <description>EDC科研中心API模块 - 提供科研项目管理、数据分析等REST API接口</description>

    <dependencies>
        <!-- ==================== Spring Boot 核心依赖 ==================== -->
        <!-- Spring Security 安全认证 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>

        <!-- ==================== 监控相关 ==================== -->
        <!-- Spring Boot Actuator 应用监控 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- ==================== 内部模块依赖 ==================== -->
        <!-- 科研中心服务层 - 使用dependencyManagement管理版本 -->
        <dependency>
            <groupId>com.haoys.edc</groupId>
            <artifactId>edc-research-service</artifactId>
        </dependency>

        <!-- 用户中心通用模块 - 使用dependencyManagement管理版本 -->
        <dependency>
            <groupId>com.haoys.edc</groupId>
            <artifactId>edc-user-common</artifactId>
        </dependency>

        <!-- 用户中心安全模块 - 使用dependencyManagement管理版本 -->
        <dependency>
            <groupId>com.haoys.edc</groupId>
            <artifactId>edc-user-security</artifactId>
        </dependency>

        <!-- 用户中心API模块 - 使用dependencyManagement管理版本 -->
        <dependency>
            <groupId>com.haoys.edc</groupId>
            <artifactId>edc-user-api</artifactId>
        </dependency>

        <!-- 科研中心定时任务模块 - 使用dependencyManagement管理版本 -->
        <dependency>
            <groupId>com.haoys.edc</groupId>
            <artifactId>edc-research-quartz</artifactId>
        </dependency>

        <!-- ==================== 疾病数据库模块 ==================== -->
        <!-- RDR中心API - 使用dependencyManagement管理版本 -->
        <dependency>
            <groupId>com.haoys.edc</groupId>
            <artifactId>edc-rdr-center-api</artifactId>
        </dependency>

        <!-- 宁夏疾病数据库API - 使用dependencyManagement管理版本 -->
        <dependency>
            <groupId>com.haoys.edc</groupId>
            <artifactId>edc-disease-ningxia-api</artifactId>
        </dependency>

        <!-- 新疆疾病数据库API - 使用dependencyManagement管理版本 -->
        <!--<dependency>
            <groupId>com.haoys.edc</groupId>
            <artifactId>edc-disease-xinjiang-api</artifactId>
        </dependency>-->

        <!-- ==================== 工具依赖 ==================== -->
        <!-- Lombok - 版本由父POM管理 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!-- ==================== 测试依赖 ==================== -->
        <!-- Spring Boot Test Starter - 版本由Spring Boot父POM管理 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Spring Security Test - 版本由Spring Boot父POM管理 -->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Servlet API - 测试需要 -->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- ==================== 验证码服务 ==================== -->
        <!-- 滑动验证码组件 - 版本由父POM管理 -->
        <dependency>
            <groupId>com.anji-plus</groupId>
            <artifactId>spring-boot-starter-captcha</artifactId>
            <version>1.3.0</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- Spring Boot Maven插件 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.18</version>
                <configuration>
                    <mainClass>com.haoys.user.ResearchEdcApplication</mainClass>
                </configuration>
            </plugin>

            <!-- Maven JAR插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.3.0</version>
            </plugin>

            <!-- Maven编译插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                    <parameters>true</parameters>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
