package com.haoys.user.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * 事务测试服务类
 * 
 * <p>用于测试全局事务配置是否生效</p>
 * <p>包含各种方法前缀的测试方法</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-25
 */
@Service
public class TransactionTestService {

    @Autowired(required = false)
    private JdbcTemplate jdbcTemplate;

    // ==================== 写事务测试方法 ====================
    
    /**
     * 测试 insert 前缀 - 应该有写事务
     */
    public String insertTestData(String data) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        boolean isReadOnly = TransactionSynchronizationManager.isCurrentTransactionReadOnly();
        
        return String.format("insertTestData: Transaction=%s, ReadOnly=%s, Data=%s", 
                           isTransactionActive, isReadOnly, data);
    }
    
    /**
     * 测试 create 前缀 - 应该有写事务
     */
    public String createTestRecord(String record) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        boolean isReadOnly = TransactionSynchronizationManager.isCurrentTransactionReadOnly();
        
        return String.format("createTestRecord: Transaction=%s, ReadOnly=%s, Record=%s", 
                           isTransactionActive, isReadOnly, record);
    }
    
    /**
     * 测试 add 前缀 - 应该有写事务
     */
    public String addTestItem(String item) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        boolean isReadOnly = TransactionSynchronizationManager.isCurrentTransactionReadOnly();
        
        return String.format("addTestItem: Transaction=%s, ReadOnly=%s, Item=%s", 
                           isTransactionActive, isReadOnly, item);
    }
    
    /**
     * 测试 save 前缀 - 应该有写事务
     */
    public String saveTestEntity(String entity) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        boolean isReadOnly = TransactionSynchronizationManager.isCurrentTransactionReadOnly();
        
        return String.format("saveTestEntity: Transaction=%s, ReadOnly=%s, Entity=%s", 
                           isTransactionActive, isReadOnly, entity);
    }
    
    /**
     * 测试 register 前缀 - 应该有写事务
     */
    public String registerTestUser(String user) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        boolean isReadOnly = TransactionSynchronizationManager.isCurrentTransactionReadOnly();
        
        return String.format("registerTestUser: Transaction=%s, ReadOnly=%s, User=%s", 
                           isTransactionActive, isReadOnly, user);
    }
    
    /**
     * 测试 batchSave 前缀 - 应该有写事务（原有前缀）
     */
    public String batchSaveTestData(String data) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        boolean isReadOnly = TransactionSynchronizationManager.isCurrentTransactionReadOnly();
        
        return String.format("batchSaveTestData: Transaction=%s, ReadOnly=%s, Data=%s", 
                           isTransactionActive, isReadOnly, data);
    }
    
    /**
     * 测试 update 前缀 - 应该有写事务
     */
    public String updateTestRecord(String record) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        boolean isReadOnly = TransactionSynchronizationManager.isCurrentTransactionReadOnly();
        
        return String.format("updateTestRecord: Transaction=%s, ReadOnly=%s, Record=%s", 
                           isTransactionActive, isReadOnly, record);
    }
    
    /**
     * 测试 delete 前缀 - 应该有写事务
     */
    public String deleteTestItem(String item) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        boolean isReadOnly = TransactionSynchronizationManager.isCurrentTransactionReadOnly();
        
        return String.format("deleteTestItem: Transaction=%s, ReadOnly=%s, Item=%s", 
                           isTransactionActive, isReadOnly, item);
    }
    
    /**
     * 测试 remove 前缀 - 应该有写事务
     */
    public String removeTestEntity(String entity) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        boolean isReadOnly = TransactionSynchronizationManager.isCurrentTransactionReadOnly();
        
        return String.format("removeTestEntity: Transaction=%s, ReadOnly=%s, Entity=%s", 
                           isTransactionActive, isReadOnly, entity);
    }
    
    /**
     * 测试 check 前缀 - 应该有写事务（可能涉及状态更新）
     */
    public String checkTestStatus(String status) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        boolean isReadOnly = TransactionSynchronizationManager.isCurrentTransactionReadOnly();
        
        return String.format("checkTestStatus: Transaction=%s, ReadOnly=%s, Status=%s", 
                           isTransactionActive, isReadOnly, status);
    }
    
    /**
     * 测试 join 前缀 - 应该有写事务（原有前缀）
     */
    public String joinTestGroup(String group) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        boolean isReadOnly = TransactionSynchronizationManager.isCurrentTransactionReadOnly();
        
        return String.format("joinTestGroup: Transaction=%s, ReadOnly=%s, Group=%s", 
                           isTransactionActive, isReadOnly, group);
    }
    
    /**
     * 测试 init 前缀 - 应该有写事务
     */
    public String initTestSystem(String system) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        boolean isReadOnly = TransactionSynchronizationManager.isCurrentTransactionReadOnly();
        
        return String.format("initTestSystem: Transaction=%s, ReadOnly=%s, System=%s", 
                           isTransactionActive, isReadOnly, system);
    }
    
    /**
     * 测试 accredit 前缀 - 应该有写事务（原有前缀）
     */
    public String accreditTestPermission(String permission) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        boolean isReadOnly = TransactionSynchronizationManager.isCurrentTransactionReadOnly();
        
        return String.format("accreditTestPermission: Transaction=%s, ReadOnly=%s, Permission=%s", 
                           isTransactionActive, isReadOnly, permission);
    }

    // ==================== 读事务测试方法 ====================
    
    /**
     * 测试 select 前缀 - 应该有只读事务
     */
    public String selectTestData(String criteria) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        boolean isReadOnly = TransactionSynchronizationManager.isCurrentTransactionReadOnly();
        
        return String.format("selectTestData: Transaction=%s, ReadOnly=%s, Criteria=%s", 
                           isTransactionActive, isReadOnly, criteria);
    }
    
    /**
     * 测试 get 前缀 - 应该有只读事务
     */
    public String getTestRecord(String id) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        boolean isReadOnly = TransactionSynchronizationManager.isCurrentTransactionReadOnly();
        
        return String.format("getTestRecord: Transaction=%s, ReadOnly=%s, ID=%s", 
                           isTransactionActive, isReadOnly, id);
    }
    
    /**
     * 测试 query 前缀 - 应该有只读事务
     */
    public String queryTestItems(String query) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        boolean isReadOnly = TransactionSynchronizationManager.isCurrentTransactionReadOnly();
        
        return String.format("queryTestItems: Transaction=%s, ReadOnly=%s, Query=%s", 
                           isTransactionActive, isReadOnly, query);
    }
    
    /**
     * 测试 search 前缀 - 应该有只读事务
     */
    public String searchTestEntities(String keyword) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        boolean isReadOnly = TransactionSynchronizationManager.isCurrentTransactionReadOnly();
        
        return String.format("searchTestEntities: Transaction=%s, ReadOnly=%s, Keyword=%s", 
                           isTransactionActive, isReadOnly, keyword);
    }
    
    /**
     * 测试 count 前缀 - 应该有只读事务
     */
    public String countTestRecords(String filter) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        boolean isReadOnly = TransactionSynchronizationManager.isCurrentTransactionReadOnly();
        
        return String.format("countTestRecords: Transaction=%s, ReadOnly=%s, Filter=%s", 
                           isTransactionActive, isReadOnly, filter);
    }
    
    /**
     * 测试 find 前缀 - 应该有只读事务
     */
    public String findTestUsers(String condition) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        boolean isReadOnly = TransactionSynchronizationManager.isCurrentTransactionReadOnly();
        
        return String.format("findTestUsers: Transaction=%s, ReadOnly=%s, Condition=%s", 
                           isTransactionActive, isReadOnly, condition);
    }

    // ==================== 无事务测试方法 ====================
    
    /**
     * 测试无匹配前缀的方法 - 应该没有事务
     */
    public String processWithoutTransaction(String data) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        boolean isReadOnly = TransactionSynchronizationManager.isCurrentTransactionReadOnly();
        
        return String.format("processWithoutTransaction: Transaction=%s, ReadOnly=%s, Data=%s", 
                           isTransactionActive, isReadOnly, data);
    }
    
    /**
     * 测试异常回滚 - 写事务中抛出RuntimeException
     */
    public String saveTestDataWithException(String data) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        boolean isReadOnly = TransactionSynchronizationManager.isCurrentTransactionReadOnly();
        
        String result = String.format("saveTestDataWithException: Transaction=%s, ReadOnly=%s, Data=%s", 
                                     isTransactionActive, isReadOnly, data);
        
        // 抛出RuntimeException测试回滚
        if ("exception".equals(data)) {
            throw new RuntimeException("测试事务回滚");
        }
        
        return result;
    }
}
