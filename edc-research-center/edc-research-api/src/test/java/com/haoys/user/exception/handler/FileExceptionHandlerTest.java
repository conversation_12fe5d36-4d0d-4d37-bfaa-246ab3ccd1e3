package com.haoys.user.exception.handler;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.ResultCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import javax.servlet.http.HttpServletRequest;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.*;

/**
 * FileExceptionHandler测试类
 * 
 * <p>测试文件上传异常处理器的功能</p>
 * <p>验证用户友好的错误消息返回</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@ExtendWith(MockitoExtension.class)
@ActiveProfiles("test")
public class FileExceptionHandlerTest {

    @InjectMocks
    private FileExceptionHandler fileExceptionHandler;

    @Mock
    private HttpServletRequest request;

    @Mock
    private MaxUploadSizeExceededException exception;

    @BeforeEach
    void setUp() {
        // 设置基本的请求信息
        when(request.getRequestURI()).thenReturn("/api/fileManage/uploadProjectFile");
        when(request.getContentLengthLong()).thenReturn(18874368L); // 18.1MB
        when(request.getHeader("User-Agent")).thenReturn("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)");
        when(request.getHeader("X-Trace-Id")).thenReturn(null); // 没有现有的TraceId
        when(request.getHeader(anyString())).thenReturn(null); // 其他header默认返回null

        // 设置异常信息
        when(exception.getMessage()).thenReturn("Maximum upload size exceeded");
        when(exception.getMaxUploadSize()).thenReturn(15728640L); // 15MB
    }

    @Test
    void testHandleMaxUploadSizeExceeded_ShouldReturnUserFriendlyMessage() {
        // 执行测试
        CommonResult<String> result = fileExceptionHandler.handleMaxUploadSizeExceeded(exception, request);

        // 验证结果
        assertNotNull(result, "返回结果不应为null");
        assertEquals(ResultCode.RETURN_MESSAGE_FILE_SIZE_LIMIT_EXCEEDED.getCode(), result.getCode(), 
                    "错误码应为文件大小超限");
        assertNotNull(result.getMessage(), "错误消息不应为null");
        assertNotNull(result.getTraceId(), "TraceId不应为null");
        
        // 验证错误消息包含用户友好的提示
        String message = result.getMessage();
        assertTrue(message.contains("您上传的文件大小为"), "应包含文件大小信息");
        assertTrue(message.contains("超过了系统限制"), "应包含超限提示");
        assertTrue(message.contains("建议您压缩文件"), "应包含压缩建议");
        
        System.out.println("测试通过 - 用户友好的错误消息: " + message);
        System.out.println("TraceId: " + result.getTraceId());
    }

    @Test
    void testHandleMaxUploadSizeExceeded_WithLargeFile_ShouldSuggestSplitting() {
        // 设置超大文件（超过限制的2倍）
        when(request.getContentLengthLong()).thenReturn(31457280L); // 30MB (超过15MB的2倍)

        // 执行测试
        CommonResult<String> result = fileExceptionHandler.handleMaxUploadSizeExceeded(exception, request);

        // 验证结果
        assertNotNull(result);
        String message = result.getMessage();
        assertTrue(message.contains("分割成多个小文件"), "超大文件应建议分割");
        
        System.out.println("测试通过 - 超大文件建议: " + message);
    }

    @Test
    void testHandleMaxUploadSizeExceeded_WithEmptyFile_ShouldHandleGracefully() {
        // 设置空文件
        when(request.getContentLengthLong()).thenReturn(0L);

        // 执行测试
        CommonResult<String> result = fileExceptionHandler.handleMaxUploadSizeExceeded(exception, request);

        // 验证结果
        assertNotNull(result);
        String message = result.getMessage();
        assertTrue(message.contains("检测到空文件"), "应检测到空文件");
        assertTrue(message.contains("请确认文件内容完整"), "应提示确认文件内容");
        
        System.out.println("测试通过 - 空文件处理: " + message);
    }

    @Test
    void testHandleMaxUploadSizeExceeded_WithUnknownSize_ShouldProvideGenericMessage() {
        // 设置未知文件大小
        when(request.getContentLengthLong()).thenReturn(-1L);

        // 执行测试
        CommonResult<String> result = fileExceptionHandler.handleMaxUploadSizeExceeded(exception, request);

        // 验证结果
        assertNotNull(result);
        String message = result.getMessage();
        assertTrue(message.contains("您上传的文件过大"), "应提供通用的过大提示");
        
        System.out.println("测试通过 - 未知大小处理: " + message);
    }

    @Test
    void testHandleMaxUploadSizeExceeded_ShouldSetRequestAttribute() {
        // 执行测试
        fileExceptionHandler.handleMaxUploadSizeExceeded(exception, request);

        // 验证请求属性被设置
        verify(request).setAttribute("FILE_UPLOAD_EXCEPTION_HANDLED", true);
        
        System.out.println("测试通过 - 请求属性设置正确");
    }

    @Test
    void testHandleMaxUploadSizeExceeded_ShouldLogDetailedInformation() {
        // 执行测试
        CommonResult<String> result = fileExceptionHandler.handleMaxUploadSizeExceeded(exception, request);

        // 验证基本功能
        assertNotNull(result);
        assertEquals(ResultCode.RETURN_MESSAGE_FILE_SIZE_LIMIT_EXCEEDED.getCode(), result.getCode());
        
        // 验证TraceId格式
        String traceId = result.getTraceId();
        assertNotNull(traceId);
        assertTrue(traceId.startsWith("EDC-"), "TraceId应以EDC-开头");
        
        System.out.println("测试通过 - 详细日志记录功能正常");
        System.out.println("生成的TraceId: " + traceId);
    }
}
