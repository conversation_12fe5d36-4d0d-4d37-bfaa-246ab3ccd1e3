package com.haoys.user.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 全局事务测试类
 * 
 * <p>通过配置项动态开启测试事务并输出日志</p>
 * 
 * <p>配置说明：</p>
 * <ul>
 *   <li>transaction.test.enabled=true - 启用事务测试</li>
 *   <li>transaction.test.log-enabled=true - 启用事务测试日志</li>
 *   <li>transaction.test.auto-run-on-startup=true - 启动时自动运行测试</li>
 * </ul>
 * 
 * <p>使用方法：</p>
 * <ul>
 *   <li>在application-dev.yml中设置transaction.test.enabled=true</li>
 *   <li>运行测试：mvn test -Dtest=GlobalTransactionTest</li>
 *   <li>或使用IDE直接运行测试类</li>
 * </ul>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-03
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("dev")
@ConditionalOnProperty(name = "transaction.test.enabled", havingValue = "true")
@DisplayName("全局事务配置测试")
public class GlobalTransactionTest {

    @Autowired
    private TransactionTestService transactionTestService;

    @Value("${transaction.test.log-enabled:true}")
    private boolean logEnabled;

    @Value("${transaction.test.log-level:INFO}")
    private String logLevel;

    @Value("${transaction.test.output-format:DETAILED}")
    private String outputFormat;

    private static final String SEPARATOR = createRepeatedString("=", 80);
    private static final String SUB_SEPARATOR = createRepeatedString("-", 60);

    /**
     * Java 8兼容的字符串重复方法
     */
    private static String createRepeatedString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    @BeforeEach
    void setUp() {
        if (logEnabled) {
            log.info("\n{}\n🔍 开始全局事务配置测试\n{}", SEPARATOR, SEPARATOR);
            log.info("📋 测试配置:");
            log.info("  - 日志启用: {}", logEnabled);
            log.info("  - 日志级别: {}", logLevel);
            log.info("  - 输出格式: {}", outputFormat);
            log.info("{}", SUB_SEPARATOR);
        }
    }

    @Test
    @DisplayName("测试写事务方法")
    void testWriteTransactionMethods() {
        if (logEnabled) {
            log.info("🔧 测试写事务方法（应该有写事务，ReadOnly=false）");
        }

        // 测试各种写事务前缀
        String[] testData = {"test-data-1", "test-data-2", "test-data-3"};
        
        for (String data : testData) {
            // 测试 insert 前缀
            String insertResult = transactionTestService.insertTestData(data);
            logTestResult("insertTestData", insertResult, true, false);
            
            // 测试 save 前缀
            String saveResult = transactionTestService.saveTestEntity(data);
            logTestResult("saveTestEntity", saveResult, true, false);
            
            // 测试 create 前缀
            String createResult = transactionTestService.createTestRecord(data);
            logTestResult("createTestRecord", createResult, true, false);
            
            // 测试 add 前缀
            String addResult = transactionTestService.addTestItem(data);
            logTestResult("addTestItem", addResult, true, false);
            
            // 测试 update 前缀
            String updateResult = transactionTestService.updateTestRecord(data);
            logTestResult("updateTestRecord", updateResult, true, false);

            // 测试 delete 前缀
            String deleteResult = transactionTestService.deleteTestItem(data);
            logTestResult("deleteTestItem", deleteResult, true, false);
        }
        
        if (logEnabled) {
            log.info("✅ 写事务方法测试完成");
        }
    }

    @Test
    @DisplayName("测试读事务方法")
    void testReadTransactionMethods() {
        if (logEnabled) {
            log.info("🔍 测试读事务方法（应该有只读事务，ReadOnly=true）");
        }

        String[] testCriteria = {"criteria-1", "criteria-2", "criteria-3"};
        
        for (String criteria : testCriteria) {
            // 测试 select 前缀
            String selectResult = transactionTestService.selectTestData(criteria);
            logTestResult("selectTestData", selectResult, true, true);
            
            // 测试 get 前缀
            String getResult = transactionTestService.getTestRecord(criteria);
            logTestResult("getTestRecord", getResult, true, true);
            
            // 测试 query 前缀
            String queryResult = transactionTestService.queryTestItems(criteria);
            logTestResult("queryTestItems", queryResult, true, true);
            
            // 测试 find 前缀
            String findResult = transactionTestService.findTestUsers(criteria);
            logTestResult("findTestUsers", findResult, true, true);
            
            // 测试 search 前缀
            String searchResult = transactionTestService.searchTestEntities(criteria);
            logTestResult("searchTestEntities", searchResult, true, true);
            
            // 测试 count 前缀
            String countResult = transactionTestService.countTestRecords(criteria);
            logTestResult("countTestRecords", countResult, true, true);
        }
        
        if (logEnabled) {
            log.info("✅ 读事务方法测试完成");
        }
    }

    @Test
    @DisplayName("测试无事务方法")
    void testNoTransactionMethods() {
        if (logEnabled) {
            log.info("🚫 测试无事务方法（应该没有事务，Transaction=false）");
        }

        String testData = "no-transaction-data";
        
        // 测试无匹配前缀的方法
        String result = transactionTestService.processWithoutTransaction(testData);
        logTestResult("processWithoutTransaction", result, false, false);
        
        if (logEnabled) {
            log.info("✅ 无事务方法测试完成");
        }
    }

    @Test
    @DisplayName("测试事务回滚")
    void testTransactionRollback() {
        if (logEnabled) {
            log.info("🔄 测试事务回滚机制");
        }

        // 测试正常情况（不抛异常）
        String normalResult = transactionTestService.saveTestDataWithException("normal");
        logTestResult("saveTestDataWithException(normal)", normalResult, true, false);
        
        // 测试异常情况（应该抛出RuntimeException）
        try {
            transactionTestService.saveTestDataWithException("exception");
            fail("应该抛出RuntimeException");
        } catch (RuntimeException e) {
            if (logEnabled) {
                log.info("✅ 事务回滚测试成功，捕获到预期异常: {}", e.getMessage());
            }
            assertEquals("测试事务回滚", e.getMessage());
        }
        
        if (logEnabled) {
            log.info("✅ 事务回滚测试完成");
        }
    }

    @Test
    @DisplayName("测试事务状态验证")
    void testTransactionStatusValidation() {
        if (logEnabled) {
            log.info("🔍 验证事务状态的准确性");
        }

        // 在测试方法外部，应该没有事务
        boolean isTransactionActiveOutside = TransactionSynchronizationManager.isActualTransactionActive();
        assertFalse(isTransactionActiveOutside, "测试方法外部不应该有事务");
        
        if (logEnabled) {
            log.info("✅ 测试方法外部事务状态验证通过: Transaction={}", isTransactionActiveOutside);
        }
        
        // 调用业务方法，验证事务状态
        String insertResult = transactionTestService.insertTestData("validation-test");
        assertTrue(insertResult.contains("Transaction=true"), "insert方法应该有事务");
        assertTrue(insertResult.contains("ReadOnly=false"), "insert方法应该是写事务");
        
        String selectResult = transactionTestService.selectTestData("validation-test");
        assertTrue(selectResult.contains("Transaction=true"), "select方法应该有事务");
        assertTrue(selectResult.contains("ReadOnly=true"), "select方法应该是只读事务");
        
        if (logEnabled) {
            log.info("✅ 事务状态验证完成");
        }
    }

    /**
     * 记录测试结果
     */
    private void logTestResult(String methodName, String result, boolean expectedTransaction, boolean expectedReadOnly) {
        if (!logEnabled) {
            return;
        }

        boolean actualTransaction = result.contains("Transaction=true");
        boolean actualReadOnly = result.contains("ReadOnly=true");
        
        String status = "✅";
        if (actualTransaction != expectedTransaction || actualReadOnly != expectedReadOnly) {
            status = "❌";
        }
        
        if ("DETAILED".equals(outputFormat)) {
            log.info("{} {} - 期望[事务:{}, 只读:{}] 实际[事务:{}, 只读:{}]", 
                    status, methodName, expectedTransaction, expectedReadOnly, actualTransaction, actualReadOnly);
            log.info("   完整结果: {}", result);
        } else {
            log.info("{} {} - Transaction:{}, ReadOnly:{}", 
                    status, methodName, actualTransaction, actualReadOnly);
        }
        
        // 断言验证
        assertEquals(expectedTransaction, actualTransaction, 
                String.format("%s 事务状态不符合预期", methodName));
        assertEquals(expectedReadOnly, actualReadOnly, 
                String.format("%s 只读状态不符合预期", methodName));
    }
}
