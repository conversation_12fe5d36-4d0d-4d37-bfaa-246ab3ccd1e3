package com.haoys.user.python;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * iBrainPy服务测试类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@SpringBootTest
@ActiveProfiles("test")
@DisplayName("iBrainPy脚本服务测试")
class IBrainPyServiceTest {

    private IBrainPyRequest testRequest;

    @BeforeEach
    void setUp() {
        // 创建测试请求对象
        testRequest = new IBrainPyRequest();
        testRequest.setInputDir("/tmp/test_input");
        testRequest.setOutputDir("/tmp/test_output");
        testRequest.setAge(45);
        testRequest.setGender(1);
        testRequest.setIsOneKeyFromT1Raw(0);
    }

    @Test
    @DisplayName("测试请求参数验证")
    void testRequestValidation() {
        // 测试有效参数
        assertNotNull(testRequest.getInputDir());
        assertNotNull(testRequest.getOutputDir());
        assertTrue(testRequest.getAge() >= 20 && testRequest.getAge() <= 90);
        assertTrue(testRequest.getGender() == 0 || testRequest.getGender() == 1);

        // 测试性别描述
        assertEquals("男性", testRequest.getGenderDescription());
        
        testRequest.setGender(0);
        assertEquals("女性", testRequest.getGenderDescription());

        // 测试输入格式
        assertFalse(testRequest.isDicomInput());
        assertEquals("NIFTI", testRequest.getInputFormatDescription());

        testRequest.setIsOneKeyFromT1Raw(1);
        assertTrue(testRequest.isDicomInput());
        assertEquals("DICOM", testRequest.getInputFormatDescription());
    }

    @Test
    @DisplayName("测试年龄边界值")
    void testAgeBoundary() {
        // 测试最小年龄
        testRequest.setAge(20);
        assertEquals(20, testRequest.getAge());

        // 测试最大年龄
        testRequest.setAge(90);
        assertEquals(90, testRequest.getAge());
    }

    @Test
    @DisplayName("测试性别值")
    void testGenderValues() {
        // 测试女性
        testRequest.setGender(0);
        assertEquals(0, testRequest.getGender());
        assertEquals("女性", testRequest.getGenderDescription());

        // 测试男性
        testRequest.setGender(1);
        assertEquals(1, testRequest.getGender());
        assertEquals("男性", testRequest.getGenderDescription());
    }

    @Test
    @DisplayName("测试DICOM输入标志")
    void testDicomInputFlag() {
        // 测试默认NIFTI
        testRequest.setIsOneKeyFromT1Raw(null);
        assertFalse(testRequest.isDicomInput());

        testRequest.setIsOneKeyFromT1Raw(0);
        assertFalse(testRequest.isDicomInput());

        // 测试DICOM
        testRequest.setIsOneKeyFromT1Raw(1);
        assertTrue(testRequest.isDicomInput());
    }

    @Test
    @DisplayName("测试响应对象创建")
    void testResponseCreation() {
        // 测试成功响应
        IBrainPyResponse successResponse = IBrainPyResponse.success(
            testRequest, 
            null, 
            "/tmp/output.json", 
            "/tmp/report.docx", 
            5000L
        );

        assertTrue(successResponse.isSuccess());
        assertEquals("success", successResponse.getStatus());
        assertEquals("iBrainPy脚本执行成功", successResponse.getMessage());
        assertEquals(testRequest, successResponse.getInputParams());
        assertEquals("/tmp/output.json", successResponse.getJsonOutputPath());
        assertEquals("/tmp/report.docx", successResponse.getDocxReportPath());

        // 测试失败响应
        IBrainPyResponse failedResponse = IBrainPyResponse.failed(
            testRequest, 
            "执行失败", 
            "script output", 
            "script error", 
            1
        );

        assertFalse(failedResponse.isSuccess());
        assertEquals("failed", failedResponse.getStatus());
        assertEquals("执行失败", failedResponse.getMessage());
        assertEquals(1, failedResponse.getExitCode());
    }

    @Test
    @DisplayName("测试执行时间描述")
    void testExecutionTimeDescription() {
        IBrainPyResponse response = new IBrainPyResponse();
        
        // 测试毫秒
        response.setExecutionTimeMs(500L);
        assertEquals("500毫秒", response.getExecutionTimeDescription());

        // 测试秒
        response.setExecutionTimeMs(5000L);
        assertEquals("5.00秒", response.getExecutionTimeDescription());

        // 测试分钟
        response.setExecutionTimeMs(125000L);
        assertEquals("2分5秒", response.getExecutionTimeDescription());

        // 测试null
        response.setExecutionTimeMs(null);
        assertEquals("未知", response.getExecutionTimeDescription());
    }

    @Test
    @DisplayName("测试toString方法")
    void testToStringMethods() {
        // 测试请求对象toString
        String requestStr = testRequest.toString();
        assertNotNull(requestStr);
        assertTrue(requestStr.contains("inputDir"));
        assertTrue(requestStr.contains("outputDir"));
        assertTrue(requestStr.contains("age"));
        assertTrue(requestStr.contains("gender"));

        // 测试响应对象toString
        IBrainPyResponse response = IBrainPyResponse.success(testRequest, null, null, null, 1000L);
        String responseStr = response.toString();
        assertNotNull(responseStr);
        assertTrue(responseStr.contains("status"));
        assertTrue(responseStr.contains("message"));
    }
}
