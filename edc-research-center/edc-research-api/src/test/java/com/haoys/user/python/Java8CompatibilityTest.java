package com.haoys.user.python;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Java 8兼容性测试
 * 验证所有iBrainPy相关类都符合Java 8标准
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@SpringBootTest
@ActiveProfiles("test")
@DisplayName("Java 8兼容性测试")
class Java8CompatibilityTest {

    @Test
    @DisplayName("测试IBrainPyRequest类Java 8兼容性")
    void testIBrainPyRequestJava8Compatibility() {
        // 创建请求对象
        IBrainPyRequest request = new IBrainPyRequest();
        request.setInputDir("/test/input");
        request.setOutputDir("/test/output");
        request.setAge(30);
        request.setGender(1);
        request.setIsOneKeyFromT1Raw(0);

        // 验证基本功能
        assertNotNull(request);
        assertEquals("/test/input", request.getInputDir());
        assertEquals("/test/output", request.getOutputDir());
        assertEquals(Integer.valueOf(30), request.getAge());
        assertEquals(Integer.valueOf(1), request.getGender());
        assertEquals(Integer.valueOf(0), request.getIsOneKeyFromT1Raw());

        // 验证方法调用
        assertEquals("男性", request.getGenderDescription());
        assertFalse(request.isDicomInput());
        assertEquals("NIFTI", request.getInputFormatDescription());

        // 测试DICOM模式
        request.setIsOneKeyFromT1Raw(1);
        assertTrue(request.isDicomInput());
        assertEquals("DICOM", request.getInputFormatDescription());
    }

    @Test
    @DisplayName("测试IBrainPyResponse类Java 8兼容性")
    void testIBrainPyResponseJava8Compatibility() {
        // 创建测试请求
        IBrainPyRequest request = new IBrainPyRequest();
        request.setInputDir("/test/input");
        request.setOutputDir("/test/output");
        request.setAge(25);
        request.setGender(0);

        // 测试成功响应创建 - 使用Java 8兼容的Map创建方式
        Map<String, Object> jsonContent = new HashMap<>();
        jsonContent.put("result", "success");
        jsonContent.put("value", 123.45);

        IBrainPyResponse successResponse = IBrainPyResponse.success(
            request, 
            jsonContent, 
            "/test/output.json", 
            "/test/report.docx", 
            5000L
        );

        assertNotNull(successResponse);
        assertTrue(successResponse.isSuccess());
        assertEquals("success", successResponse.getStatus());
        assertEquals("iBrainPy脚本执行成功", successResponse.getMessage());
        assertEquals(request, successResponse.getInputParams());
        assertEquals("/test/output.json", successResponse.getJsonOutputPath());
        assertEquals("/test/report.docx", successResponse.getDocxReportPath());
        assertEquals(jsonContent, successResponse.getJsonContent());

        // 测试失败响应创建
        IBrainPyResponse failedResponse = IBrainPyResponse.failed(
            request, 
            "测试错误", 
            "script output", 
            "script error", 
            1
        );

        assertNotNull(failedResponse);
        assertFalse(failedResponse.isSuccess());
        assertEquals("failed", failedResponse.getStatus());
        assertEquals("测试错误", failedResponse.getMessage());
        assertEquals(Integer.valueOf(1), failedResponse.getExitCode());
    }

    @Test
    @DisplayName("测试执行时间描述Java 8兼容性")
    void testExecutionTimeDescriptionJava8Compatibility() {
        IBrainPyResponse response = new IBrainPyResponse();

        // 测试毫秒
        response.setExecutionTimeMs(500L);
        assertEquals("500毫秒", response.getExecutionTimeDescription());

        // 测试秒
        response.setExecutionTimeMs(2500L);
        assertEquals("2.50秒", response.getExecutionTimeDescription());

        // 测试分钟
        response.setExecutionTimeMs(125000L);
        assertEquals("2分5秒", response.getExecutionTimeDescription());

        // 测试null值
        response.setExecutionTimeMs(null);
        assertEquals("未知", response.getExecutionTimeDescription());
    }

    @Test
    @DisplayName("测试Map创建Java 8兼容性")
    void testMapCreationJava8Compatibility() {
        // 测试Java 8兼容的Map创建方式
        Map<String, Object> config = new HashMap<>();
        config.put("supportedInputFormats", new String[]{"NIFTI", "DICOM"});
        
        Map<String, Integer> ageRange = new HashMap<>();
        ageRange.put("min", 20);
        ageRange.put("max", 90);
        config.put("ageRange", ageRange);
        
        Map<String, String> genderOptions = new HashMap<>();
        genderOptions.put("0", "女性");
        genderOptions.put("1", "男性");
        config.put("genderOptions", genderOptions);
        
        config.put("outputFormats", new String[]{"JSON", "DOCX"});
        config.put("description", "iBrainPy医学影像分析脚本，支持脑部影像处理和报告生成");

        // 验证Map内容
        assertNotNull(config);
        assertTrue(config.containsKey("supportedInputFormats"));
        assertTrue(config.containsKey("ageRange"));
        assertTrue(config.containsKey("genderOptions"));
        assertTrue(config.containsKey("outputFormats"));
        assertTrue(config.containsKey("description"));

        // 验证嵌套Map
        Map<String, Integer> retrievedAgeRange = (Map<String, Integer>) config.get("ageRange");
        assertEquals(Integer.valueOf(20), retrievedAgeRange.get("min"));
        assertEquals(Integer.valueOf(90), retrievedAgeRange.get("max"));

        Map<String, String> retrievedGenderOptions = (Map<String, String>) config.get("genderOptions");
        assertEquals("女性", retrievedGenderOptions.get("0"));
        assertEquals("男性", retrievedGenderOptions.get("1"));
    }

    @Test
    @DisplayName("测试字符串操作Java 8兼容性")
    void testStringOperationsJava8Compatibility() {
        // 测试StringBuilder用法（Java 8兼容）
        StringBuilder validationInfo = new StringBuilder();
        validationInfo.append("参数验证通过:\n");
        validationInfo.append("- 输入目录: ").append("/test/input").append("\n");
        validationInfo.append("- 输出目录: ").append("/test/output").append("\n");
        validationInfo.append("- 年龄: ").append(30).append("岁\n");
        validationInfo.append("- 性别: ").append("男性").append("\n");
        validationInfo.append("- 输入格式: ").append("NIFTI");

        String result = validationInfo.toString();
        assertNotNull(result);
        assertTrue(result.contains("参数验证通过"));
        assertTrue(result.contains("/test/input"));
        assertTrue(result.contains("/test/output"));
        assertTrue(result.contains("30岁"));
        assertTrue(result.contains("男性"));
        assertTrue(result.contains("NIFTI"));
    }

    @Test
    @DisplayName("测试条件表达式Java 8兼容性")
    void testConditionalExpressionsJava8Compatibility() {
        // 测试三元运算符（Java 8兼容）
        Integer gender = 1;
        String genderDesc = gender != null && gender == 1 ? "男性" : "女性";
        assertEquals("男性", genderDesc);

        gender = 0;
        genderDesc = gender != null && gender == 1 ? "男性" : "女性";
        assertEquals("女性", genderDesc);

        gender = null;
        genderDesc = gender != null && gender == 1 ? "男性" : "女性";
        assertEquals("女性", genderDesc);

        // 测试布尔表达式
        Integer isOneKeyFromT1Raw = 1;
        boolean isDicom = isOneKeyFromT1Raw != null && isOneKeyFromT1Raw == 1;
        assertTrue(isDicom);

        isOneKeyFromT1Raw = 0;
        isDicom = isOneKeyFromT1Raw != null && isOneKeyFromT1Raw == 1;
        assertFalse(isDicom);

        isOneKeyFromT1Raw = null;
        isDicom = isOneKeyFromT1Raw != null && isOneKeyFromT1Raw == 1;
        assertFalse(isDicom);
    }
}
