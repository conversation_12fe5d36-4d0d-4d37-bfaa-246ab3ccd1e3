<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"  "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="application/xhtml+xml; charset=UTF-8"/>
    <meta http-equiv="Content-Style-Type" content="text/css"/>
    <style type="text/css">
        body {
            font-family: SimSun, serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .cover-page {
            text-align: center;
            padding: 100px 20px;
            page-break-after: always;
            min-height: 600px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .cover-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 30px;
            color: #333;
        }
        .cover-info {
            font-size: 14px;
            margin: 15px 0;
            color: #666;
        }
        .content-page {
            page-break-before: auto;
            padding: 20px 0;
        }
        /* 确保内容不会产生额外的空白页 */
        .content-page:last-child {
            page-break-after: avoid !important;
        }
        /* 防止表格和其他元素产生不必要的分页 */
        table, div, p {
            page-break-inside: avoid;
        }
        /* 移除可能导致空白页的多余空白 */
        * {
            box-sizing: border-box;
        }
        html, body {
            height: auto;
            overflow: visible;
        }
        /* 特别处理动态生成的分页标签 */
        .content-page div[style*="page-break-after: always"]:last-child {
            page-break-after: avoid !important;
        }
        /* 移除内容末尾的分页符 */
        .content-page > div:last-child[style*="page-break-after"] {
            page-break-after: avoid !important;
        }
    </style>
    <title>参与者信息</title>
</head>
<body>
    <!-- 封面页 -->
    <div class="cover-page">
        <div class="cover-title">${projectName}</div>
        <div class="cover-info">${version}</div>
        <div class="cover-info">参与者编号: ${testeeCode}</div>
        <div class="cover-info">研究中心: ${orgName}</div>
    </div>

    <!-- 内容页 -->
    <div class="content-page">
        ${content}
    </div>
</body>
</html>
