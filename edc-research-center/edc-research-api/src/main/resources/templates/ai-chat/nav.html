<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能对话系统 - EDC科研协作平台</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/element-ui/2.15.13/theme-chalk/index.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            max-width: 1200px;
            width: 100%;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
            color: white;
        }
        
        .main-title {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 20px;
            opacity: 0.9;
            margin-bottom: 10px;
        }
        
        .description {
            font-size: 16px;
            opacity: 0.8;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .feature-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            text-decoration: none;
            color: inherit;
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: white;
            margin-bottom: 20px;
        }
        
        .feature-icon.chat {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }
        
        .feature-icon.config {
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }
        
        .feature-icon.stats {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
        }
        
        .feature-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .feature-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .feature-highlights {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .feature-highlights li {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            color: #555;
            font-size: 14px;
        }
        
        .feature-highlights li i {
            color: #67c23a;
            margin-right: 8px;
            width: 16px;
        }
        
        .quick-start {
            background: rgba(255,255,255,0.1);
            border-radius: 16px;
            padding: 30px;
            text-align: center;
            color: white;
            backdrop-filter: blur(10px);
        }
        
        .quick-start-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .quick-start-description {
            margin-bottom: 25px;
            opacity: 0.9;
        }
        
        .quick-start-button {
            background: white;
            color: #667eea;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .quick-start-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            text-decoration: none;
            color: #667eea;
        }
        
        .tech-stack {
            margin-top: 40px;
            text-align: center;
            color: white;
            opacity: 0.8;
        }
        
        .tech-stack-title {
            font-size: 18px;
            margin-bottom: 15px;
        }
        
        .tech-badges {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .tech-badge {
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            backdrop-filter: blur(5px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="main-title">
                <i class="fas fa-robot"></i> AI智能对话系统
            </h1>
            <div class="subtitle">EDC科研协作平台 - 人工智能助手</div>
            <div class="description">
                支持同义千问、Kimi等多种大模型，提供多轮对话、文档解析、流式响应等功能，
                助力科研工作者提升效率，探索AI在医学研究中的无限可能。
            </div>
        </div>
        
        <div class="features-grid">
            <a href="index.html" class="feature-card">
                <div class="feature-icon chat">
                    <i class="fas fa-comments"></i>
                </div>
                <div class="feature-title">智能对话</div>
                <div class="feature-description">
                    与AI进行自然对话，支持多轮上下文理解，可上传文档进行分析解读。
                </div>
                <ul class="feature-highlights">
                    <li><i class="fas fa-check"></i> 支持通义千问、Kimi等多种模型</li>
                    <li><i class="fas fa-check"></i> 流式和普通两种响应模式</li>
                    <li><i class="fas fa-check"></i> 支持图片、文档、表格解析</li>
                    <li><i class="fas fa-check"></i> 多轮对话上下文记忆</li>
                </ul>
            </a>
            
            <a href="config.html" class="feature-card">
                <div class="feature-icon config">
                    <i class="fas fa-cogs"></i>
                </div>
                <div class="feature-title">模型配置</div>
                <div class="feature-description">
                    灵活配置AI模型参数，管理API密钥，设置成本控制和使用限制。
                </div>
                <ul class="feature-highlights">
                    <li><i class="fas fa-check"></i> 多模型统一管理</li>
                    <li><i class="fas fa-check"></i> 参数精细调节</li>
                    <li><i class="fas fa-check"></i> 成本控制设置</li>
                    <li><i class="fas fa-check"></i> 连接状态监控</li>
                </ul>
            </a>
            
            <a href="statistics.html" class="feature-card">
                <div class="feature-icon stats">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="feature-title">使用统计</div>
                <div class="feature-description">
                    详细的Token使用统计，成本分析，用户使用排行，帮助优化资源配置。
                </div>
                <ul class="feature-highlights">
                    <li><i class="fas fa-check"></i> Token消费统计</li>
                    <li><i class="fas fa-check"></i> 成本分析报告</li>
                    <li><i class="fas fa-check"></i> 用户使用排行</li>
                    <li><i class="fas fa-check"></i> 模型使用分布</li>
                </ul>
            </a>
        </div>
        
        <div class="quick-start">
            <div class="quick-start-title">
                <i class="fas fa-rocket"></i> 快速开始
            </div>
            <div class="quick-start-description">
                立即体验AI智能对话，开启您的人工智能助手之旅
            </div>
            <a href="index.html" class="quick-start-button">
                <i class="fas fa-play"></i> 开始对话
            </a>
        </div>
        
        <div class="tech-stack">
            <div class="tech-stack-title">技术栈</div>
            <div class="tech-badges">
                <span class="tech-badge">Spring Boot</span>
                <span class="tech-badge">Vue.js</span>
                <span class="tech-badge">Element UI</span>
                <span class="tech-badge">通义千问</span>
                <span class="tech-badge">Kimi</span>
                <span class="tech-badge">MySQL</span>
                <span class="tech-badge">Redis</span>
                <span class="tech-badge">SSE</span>
            </div>
        </div>
    </div>
</body>
</html>
