<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token使用统计 - EDC科研协作平台</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/element-ui/2.15.13/theme-chalk/index.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stats-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .stats-card-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .stats-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .stats-item:last-child {
            border-bottom: none;
        }
        
        .stats-label {
            color: #666;
            font-size: 14px;
        }
        
        .stats-value {
            font-weight: 600;
            font-size: 16px;
        }
        
        .stats-value.tokens {
            color: #409eff;
        }
        
        .stats-value.cost {
            color: #f56c6c;
        }
        
        .stats-value.requests {
            color: #67c23a;
        }
        
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .period-selector {
            display: flex;
            gap: 10px;
        }
        
        .model-usage-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .model-usage-item:last-child {
            border-bottom: none;
        }
        
        .model-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .model-type-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .model-type-qwen {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .model-type-kimi {
            background: #f3e5f5;
            color: #7b1fa2;
        }
        
        .model-type-chatgpt {
            background: #e8f5e8;
            color: #388e3c;
        }
        
        .usage-bar {
            flex: 1;
            margin: 0 15px;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .usage-bar-fill {
            height: 100%;
            background: linear-gradient(90deg, #409eff, #67c23a);
            transition: width 0.3s ease;
        }
        
        .usage-stats {
            text-align: right;
            font-size: 12px;
            color: #666;
        }
        
        .top-users-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .user-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .user-item:last-child {
            border-bottom: none;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #409eff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .user-name {
            font-weight: 500;
            color: #2c3e50;
        }
        
        .user-stats {
            text-align: right;
            font-size: 12px;
            color: #666;
        }
        
        .loading-container {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="page-header">
            <div class="page-title">
                <i class="fas fa-chart-bar"></i> Token使用统计
            </div>
            <div class="page-description">
                查看AI对话系统的Token使用情况、成本统计和用户使用排行
            </div>
        </div>
        
        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stats-card">
                <div class="stats-card-title">
                    <i class="fas fa-coins"></i> 今日使用情况
                </div>
                <div v-if="todayStats">
                    <div class="stats-item">
                        <span class="stats-label">总Token数</span>
                        <span class="stats-value tokens">{{ formatNumber(todayStats.totalTokens) }}</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">总成本</span>
                        <span class="stats-value cost">¥{{ formatCost(todayStats.totalCost) }}</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">请求次数</span>
                        <span class="stats-value requests">{{ formatNumber(todayStats.requestCount) }}</span>
                    </div>
                </div>
                <div v-else class="loading-container">
                    <el-loading></el-loading>
                </div>
            </div>
            
            <div class="stats-card">
                <div class="stats-card-title">
                    <i class="fas fa-calendar-week"></i> 本周使用情况
                </div>
                <div v-if="weekStats">
                    <div class="stats-item">
                        <span class="stats-label">总Token数</span>
                        <span class="stats-value tokens">{{ formatNumber(weekStats.totalTokens) }}</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">总成本</span>
                        <span class="stats-value cost">¥{{ formatCost(weekStats.totalCost) }}</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">请求次数</span>
                        <span class="stats-value requests">{{ formatNumber(weekStats.requestCount) }}</span>
                    </div>
                </div>
                <div v-else class="loading-container">
                    <el-loading></el-loading>
                </div>
            </div>
            
            <div class="stats-card">
                <div class="stats-card-title">
                    <i class="fas fa-calendar-alt"></i> 本月使用情况
                </div>
                <div v-if="monthStats">
                    <div class="stats-item">
                        <span class="stats-label">总Token数</span>
                        <span class="stats-value tokens">{{ formatNumber(monthStats.totalTokens) }}</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">总成本</span>
                        <span class="stats-value cost">¥{{ formatCost(monthStats.totalCost) }}</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">请求次数</span>
                        <span class="stats-value requests">{{ formatNumber(monthStats.requestCount) }}</span>
                    </div>
                </div>
                <div v-else class="loading-container">
                    <el-loading></el-loading>
                </div>
            </div>
        </div>
        
        <!-- 模型使用分布 -->
        <div class="chart-container">
            <div class="chart-title">
                <span><i class="fas fa-robot"></i> 模型使用分布</span>
                <div class="period-selector">
                    <el-radio-group v-model="modelUsagePeriod" size="small" @change="loadModelUsage">
                        <el-radio-button label="today">今日</el-radio-button>
                        <el-radio-button label="week">本周</el-radio-button>
                        <el-radio-button label="month">本月</el-radio-button>
                    </el-radio-group>
                </div>
            </div>
            
            <div v-if="modelUsage.length > 0">
                <div v-for="model in modelUsage" :key="`${model.modelType}-${model.modelName}`" class="model-usage-item">
                    <div class="model-info">
                        <span :class="['model-type-badge', `model-type-${model.modelType}`]">
                            {{ model.modelType.toUpperCase() }}
                        </span>
                        <span>{{ model.modelName }}</span>
                    </div>
                    
                    <div class="usage-bar">
                        <div class="usage-bar-fill" :style="{ width: getUsagePercentage(model.totalTokens) + '%' }"></div>
                    </div>
                    
                    <div class="usage-stats">
                        <div>{{ formatNumber(model.totalTokens) }} Tokens</div>
                        <div>¥{{ formatCost(model.totalCost) }}</div>
                    </div>
                </div>
            </div>
            <div v-else class="loading-container">
                <el-loading></el-loading>
            </div>
        </div>
        
        <!-- Top用户排行 -->
        <div class="chart-container">
            <div class="chart-title">
                <span><i class="fas fa-users"></i> 用户使用排行</span>
                <div class="period-selector">
                    <el-radio-group v-model="topUsersPeriod" size="small" @change="loadTopUsers">
                        <el-radio-button label="today">今日</el-radio-button>
                        <el-radio-button label="week">本周</el-radio-button>
                        <el-radio-button label="month">本月</el-radio-button>
                    </el-radio-group>
                </div>
            </div>
            
            <div class="top-users-list">
                <div v-if="topUsers.length > 0">
                    <div v-for="(user, index) in topUsers" :key="user.userId" class="user-item">
                        <div class="user-info">
                            <div class="user-avatar">{{ index + 1 }}</div>
                            <div>
                                <div class="user-name">{{ user.userName || user.userId }}</div>
                            </div>
                        </div>
                        
                        <div class="user-stats">
                            <div>{{ formatNumber(user.totalTokens) }} Tokens</div>
                            <div>¥{{ formatCost(user.totalCost) }}</div>
                            <div>{{ user.requestCount }} 次请求</div>
                        </div>
                    </div>
                </div>
                <div v-else class="loading-container">
                    <el-loading></el-loading>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/vue/2.6.14/vue.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/element-ui/2.15.13/index.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/axios/0.27.2/axios.min.js"></script>

    <!-- 用户信息初始化脚本 -->
    <script>
        // 页面加载时尝试获取用户信息
        (function() {
            // 尝试从服务器端获取用户信息（如果是通过模板引擎渲染的）
            if (typeof window.serverUserInfo !== 'undefined' && window.serverUserInfo) {
                console.log('AI统计页面从服务器端获取到用户信息:', window.serverUserInfo);

                // 存储到sessionStorage
                try {
                    sessionStorage.setItem('userInfo', JSON.stringify(window.serverUserInfo));
                    console.log('用户信息已存储到sessionStorage');
                } catch (e) {
                    console.warn('存储用户信息到sessionStorage失败:', e);
                }

                // 设置到全局变量
                window.currentUser = window.serverUserInfo;
            }

            // 尝试从cookie中获取用户信息（如果登录时设置了）
            function getCookie(name) {
                const value = `; ${document.cookie}`;
                const parts = value.split(`; ${name}=`);
                if (parts.length === 2) return parts.pop().split(';').shift();
                return null;
            }

            // 如果没有从服务器端获取到用户信息，尝试从cookie获取
            if (!window.currentUser) {
                const userId = getCookie('userId');
                const userName = getCookie('userName');
                const realName = getCookie('realName');

                if (userId) {
                    const userInfo = {
                        userId: decodeURIComponent(userId),
                        userName: userName ? decodeURIComponent(userName) : '用户',
                        realName: realName ? decodeURIComponent(realName) : '用户'
                    };

                    console.log('AI统计页面从cookie获取到用户信息:', userInfo);

                    // 存储到sessionStorage
                    try {
                        sessionStorage.setItem('userInfo', JSON.stringify(userInfo));
                        console.log('用户信息已存储到sessionStorage');
                    } catch (e) {
                        console.warn('存储用户信息到sessionStorage失败:', e);
                    }

                    // 设置到全局变量
                    window.currentUser = userInfo;
                }
            }

            console.log('AI统计页面当前用户信息:', window.currentUser || '未登录');
        })();
    </script>

    <script src="/api/ai-chat/js/ai-statistics.js"></script>
</body>
</html>
