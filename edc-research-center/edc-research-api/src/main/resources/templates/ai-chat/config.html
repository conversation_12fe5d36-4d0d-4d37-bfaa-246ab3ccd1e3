<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI模型配置管理 - EDC科研协作平台</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/element-ui/2.15.13/theme-chalk/index.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .page-description {
            color: #666;
            font-size: 14px;
        }
        
        .content-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .card-header {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .model-type-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .model-type-qwen {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .model-type-kimi {
            background: #f3e5f5;
            color: #7b1fa2;
        }
        
        .model-type-chatgpt {
            background: #e8f5e8;
            color: #388e3c;
        }
        
        .priority-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }
        
        .form-section {
            margin-bottom: 24px;
        }
        
        .form-section-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #409eff;
        }
        
        .cost-info {
            background: #fff7e6;
            border: 1px solid #ffd591;
            border-radius: 4px;
            padding: 12px;
            margin-top: 8px;
        }
        
        .cost-info-title {
            font-weight: 600;
            color: #d46b08;
            margin-bottom: 4px;
        }
        
        .cost-info-text {
            font-size: 12px;
            color: #ad6800;
        }

        /* 修复操作按钮样式 */
        .el-table .cell {
            white-space: nowrap;
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            flex-wrap: nowrap;
        }

        .action-buttons .el-button {
            margin: 0 !important;
            padding: 5px 8px;
            font-size: 12px;
            min-width: auto;
        }

        .action-buttons .el-button + .el-button {
            margin-left: 0 !important;
        }

        /* 确保操作列有足够宽度并居中 */
        .el-table td.el-table__cell {
            text-align: center;
        }

        .el-table .operation-column .cell {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 5px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="page-header">
            <div class="page-title">
                <i class="fas fa-cogs"></i> AI模型配置管理
            </div>
            <div class="page-description">
                管理AI对话系统中的模型配置，包括API密钥、参数设置、成本控制等
            </div>
        </div>
        
        <div class="content-card">
            <div class="card-header">
                <div class="card-title">模型配置列表</div>
                <el-button type="primary" @click="showAddDialog">
                    <i class="fas fa-plus"></i> 添加模型
                </el-button>
            </div>
            
            <el-table :data="modelConfigs" style="width: 100%" v-loading="loading">
                <el-table-column prop="modelType" label="模型类型" width="120">
                    <template slot-scope="scope">
                        <span :class="['model-type-tag', `model-type-${scope.row.modelType}`]">
                            {{ scope.row.modelType.toUpperCase() }}
                        </span>
                    </template>
                </el-table-column>
                
                <el-table-column prop="modelName" label="模型名称" width="180">
                </el-table-column>
                
                <el-table-column prop="description" label="描述" min-width="200">
                </el-table-column>
                
                <el-table-column prop="maxTokens" label="最大Token" width="100" align="center">
                </el-table-column>
                
                <el-table-column prop="temperature" label="温度" width="80" align="center">
                </el-table-column>
                
                <el-table-column prop="priority" label="优先级" width="100" align="center">
                    <template slot-scope="scope">
                        <div class="priority-badge">
                            <i class="fas fa-star" style="color: #f39c12;"></i>
                            {{ scope.row.priority }}
                        </div>
                    </template>
                </el-table-column>
                
                <el-table-column prop="enabled" label="状态" width="100" align="center">
                    <template slot-scope="scope">
                        <el-switch v-model="scope.row.enabled" 
                                  @change="toggleModel(scope.row)">
                        </el-switch>
                    </template>
                </el-table-column>
                
                <el-table-column label="操作" width="240" align="center" class-name="operation-column">
                    <template slot-scope="scope">
                        <div class="action-buttons">
                            <el-button size="mini" @click="editModel(scope.row)">
                                <i class="fas fa-edit"></i> 编辑
                            </el-button>
                            <el-button size="mini" type="success" @click="testModel(scope.row)">
                                <i class="fas fa-vial"></i> 测试
                            </el-button>
                            <el-button size="mini" type="danger" @click="deleteModel(scope.row)">
                                <i class="fas fa-trash"></i> 删除
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        
        <!-- 添加/编辑模型对话框 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="800px" @close="resetForm">
            <el-form :model="modelForm" :rules="formRules" ref="modelForm" label-width="120px">
                <div class="form-section">
                    <div class="form-section-title">基本信息</div>
                    
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="模型类型" prop="modelType">
                                <el-select v-model="modelForm.modelType" placeholder="请选择模型类型">
                                    <el-option label="通义千问" value="qwen"></el-option>
                                    <el-option label="Kimi" value="kimi"></el-option>
                                    <el-option label="ChatGPT" value="chatgpt"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="模型名称" prop="modelName">
                                <el-input v-model="modelForm.modelName" placeholder="如: qwen-turbo"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    
                    <el-form-item label="模型描述" prop="description">
                        <el-input v-model="modelForm.description" type="textarea" :rows="2" 
                                 placeholder="简要描述模型的特点和适用场景"></el-input>
                    </el-form-item>
                </div>
                
                <div class="form-section">
                    <div class="form-section-title">API配置</div>
                    
                    <el-form-item label="API密钥" prop="apiKey">
                        <el-input v-model="modelForm.apiKey" type="password" show-password 
                                 placeholder="请输入API密钥"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="API地址" prop="apiUrl">
                        <el-input v-model="modelForm.apiUrl" placeholder="API接口地址"></el-input>
                    </el-form-item>
                </div>
                
                <div class="form-section">
                    <div class="form-section-title">模型参数</div>
                    
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="最大Token" prop="maxTokens">
                                <el-input-number v-model="modelForm.maxTokens" :min="1" :max="200000" 
                                               style="width: 100%;"></el-input-number>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="温度参数" prop="temperature">
                                <el-input-number v-model="modelForm.temperature" :min="0" :max="2" 
                                               :step="0.1" :precision="2" style="width: 100%;"></el-input-number>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="Top-P" prop="topP">
                                <el-input-number v-model="modelForm.topP" :min="0" :max="1" 
                                               :step="0.1" :precision="2" style="width: 100%;"></el-input-number>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    
                    <el-form-item label="系统提示词" prop="systemPrompt">
                        <el-input v-model="modelForm.systemPrompt" type="textarea" :rows="3" 
                                 placeholder="系统级提示词，用于设定AI的角色和行为"></el-input>
                    </el-form-item>
                </div>
                
                <div class="form-section">
                    <div class="form-section-title">成本控制</div>
                    
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="输入Token单价" prop="tokenPriceInput">
                                <el-input-number v-model="modelForm.tokenPriceInput" :min="0" 
                                               :step="0.000001" :precision="6" style="width: 100%;"></el-input-number>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="输出Token单价" prop="tokenPriceOutput">
                                <el-input-number v-model="modelForm.tokenPriceOutput" :min="0" 
                                               :step="0.000001" :precision="6" style="width: 100%;"></el-input-number>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    
                    <div class="cost-info">
                        <div class="cost-info-title">成本说明</div>
                        <div class="cost-info-text">
                            Token单价用于计算每次对话的成本，单位为元/Token。不同模型的定价策略可能不同，请参考官方文档设置准确的价格。
                        </div>
                    </div>
                </div>
                
                <div class="form-section">
                    <div class="form-section-title">其他设置</div>
                    
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="优先级" prop="priority">
                                <el-input-number v-model="modelForm.priority" :min="0" :max="100" 
                                               style="width: 100%;"></el-input-number>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="每分钟请求限制" prop="rateLimitRpm">
                                <el-input-number v-model="modelForm.rateLimitRpm" :min="1" 
                                               style="width: 100%;"></el-input-number>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="启用状态">
                                <el-switch v-model="modelForm.enabled"></el-switch>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
            </el-form>
            
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="saveModel" :loading="saving">保存</el-button>
            </div>
        </el-dialog>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/vue/2.6.14/vue.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/element-ui/2.15.13/index.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/axios/0.27.2/axios.min.js"></script>

    <!-- 用户信息初始化脚本 -->
    <script>
        // 页面加载时尝试获取用户信息
        (function() {
            // 尝试从服务器端获取用户信息（如果是通过模板引擎渲染的）
            if (typeof window.serverUserInfo !== 'undefined' && window.serverUserInfo) {
                console.log('AI配置页面从服务器端获取到用户信息:', window.serverUserInfo);

                // 存储到sessionStorage
                try {
                    sessionStorage.setItem('userInfo', JSON.stringify(window.serverUserInfo));
                    console.log('用户信息已存储到sessionStorage');
                } catch (e) {
                    console.warn('存储用户信息到sessionStorage失败:', e);
                }

                // 设置到全局变量
                window.currentUser = window.serverUserInfo;
            }

            // 尝试从cookie中获取用户信息（如果登录时设置了）
            function getCookie(name) {
                const value = `; ${document.cookie}`;
                const parts = value.split(`; ${name}=`);
                if (parts.length === 2) return parts.pop().split(';').shift();
                return null;
            }

            // 如果没有从服务器端获取到用户信息，尝试从cookie获取
            if (!window.currentUser) {
                const userId = getCookie('userId');
                const userName = getCookie('userName');
                const realName = getCookie('realName');

                if (userId) {
                    const userInfo = {
                        userId: decodeURIComponent(userId),
                        userName: userName ? decodeURIComponent(userName) : '用户',
                        realName: realName ? decodeURIComponent(realName) : '用户'
                    };

                    console.log('AI配置页面从cookie获取到用户信息:', userInfo);

                    // 存储到sessionStorage
                    try {
                        sessionStorage.setItem('userInfo', JSON.stringify(userInfo));
                        console.log('用户信息已存储到sessionStorage');
                    } catch (e) {
                        console.warn('存储用户信息到sessionStorage失败:', e);
                    }

                    // 设置到全局变量
                    window.currentUser = userInfo;
                }
            }

            console.log('AI配置页面当前用户信息:', window.currentUser || '未登录');
        })();
    </script>

    <script src="/api/ai-chat/js/ai-config.js"></script>
</body>
</html>
