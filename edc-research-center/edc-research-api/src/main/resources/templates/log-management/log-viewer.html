<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EDC 日志管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            background: #1e1e1e;
            color: #d4d4d4;
            height: 100vh;
            overflow: hidden;
        }

        .header {
            background: #2d2d30;
            padding: 10px 20px;
            border-bottom: 1px solid #3e3e42;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #569cd6;
            font-size: 18px;
            font-weight: normal;
        }

        .status {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #f44747;
        }

        .status-dot.connected {
            background: #4ec9b0;
        }

        .status-dot.valid {
            background: #28a745;
        }

        .status-dot.invalid {
            background: #dc3545;
        }

        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.2s;
            margin-left: 10px;
        }

        .logout-btn:hover {
            background: #c82333;
        }

        .main-content {
            display: flex;
            height: calc(100vh - 60px);
        }

        .sidebar {
            width: 300px;
            background: #252526;
            border-right: 1px solid #3e3e42;
            padding: 15px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .sidebar-section {
            margin-bottom: 20px;
        }

        .sidebar-section:last-child {
            flex: 1;
            display: flex;
            flex-direction: column;
            margin-bottom: 0;
        }

        .sidebar-section h3 {
            color: #cccccc;
            font-size: 14px;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #3e3e42;
        }

        .form-group {
            margin-bottom: 10px;
        }

        .form-group label {
            display: block;
            color: #cccccc;
            font-size: 12px;
            margin-bottom: 5px;
        }

        .form-control {
            width: 100%;
            padding: 8px;
            background: #3c3c3c;
            border: 1px solid #5a5a5a;
            color: #d4d4d4;
            font-size: 12px;
            border-radius: 3px;
        }

        .form-control:focus {
            outline: none;
            border-color: #007acc;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: #0e639c;
            color: white;
        }

        .btn-primary:hover {
            background: #1177bb;
        }

        .btn-secondary {
            background: #5a5a5a;
            color: white;
        }

        .btn-secondary:hover {
            background: #6a6a6a;
        }

        .btn-success {
            background: #16825d;
            color: white;
        }

        .btn-success:hover {
            background: #1e9973;
        }

        .btn-danger {
            background: #c5282f;
            color: white;
        }

        .btn-danger:hover {
            background: #d73a41;
        }

        .file-list {
            flex: 1;
            overflow-y: auto;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            background: #3c3c3c;
            min-height: 200px;
        }

        .file-item {
            padding: 8px 12px;
            border-bottom: 1px solid #5a5a5a;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .file-item:hover {
            background: #4a4a4a;
        }

        .file-item.selected {
            background: #094771;
        }

        .file-info {
            flex: 1;
        }

        .file-name {
            font-size: 12px;
            color: #d4d4d4;
        }

        .file-meta {
            font-size: 10px;
            color: #858585;
            margin-top: 2px;
        }

        .file-actions {
            display: flex;
            gap: 5px;
        }

        .btn-small {
            padding: 4px 8px;
            font-size: 10px;
        }

        .log-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #1e1e1e;
        }

        .log-controls {
            background: #2d2d30;
            padding: 10px 15px;
            border-bottom: 1px solid #3e3e42;
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .log-output {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .log-entry {
            margin-bottom: 2px;
            padding: 2px 0;
        }

        .log-entry.error {
            color: #f44747;
        }

        .log-entry.warn {
            color: #ffcc02;
        }

        .log-entry.info {
            color: #4fc1ff;
        }

        .log-entry.debug {
            color: #b5cea8;
        }

        .log-entry.system {
            color: #dcdcaa;
        }

        .loading {
            text-align: center;
            color: #858585;
            padding: 20px;
        }

        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 4px;
            color: white;
            font-size: 12px;
            z-index: 1000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .message.show {
            opacity: 1;
            transform: translateX(0);
        }

        .message.success {
            background: #16825d;
        }

        .message.error {
            background: #c5282f;
        }

        .message.info {
            background: #0e639c;
        }

        .filter-group {
            display: flex;
            gap: 5px;
            align-items: center;
        }

        .checkbox-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .checkbox-group label {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 11px;
            color: #cccccc;
            cursor: pointer;
        }

        .checkbox-group input[type="checkbox"] {
            margin: 0;
        }

        .search-box {
            flex: 1;
            max-width: 200px;
        }

        .stats {
            font-size: 11px;
            color: #858585;
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #2d2d30;
        }

        ::-webkit-scrollbar-thumb {
            background: #5a5a5a;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #6a6a6a;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 EDC 日志管理系统</h1>
        <div class="status">
            <div class="status-item">
                <div class="status-dot" id="connectionStatus"></div>
                <span id="connectionText">未连接</span>
            </div>
            <div class="status-item">
                <div class="status-dot" id="tokenStatus"></div>
                <span id="tokenStatusText">验证中...</span>
            </div>
            <div class="status-item">
                <span>过期时间: </span>
                <span id="tokenExpireTime">--</span>
            </div>
            <div class="status-item">
                <span>📊</span>
                <span id="logCount">0 条日志</span>
            </div>
            <div class="status-item">
                <span>⏱️</span>
                <span id="lastUpdate">--:--:--</span>
            </div>
            <button class="logout-btn" id="logoutBtn" onclick="logout()" style="display: none;">退出</button>
        </div>
    </div>

    <div class="main-content">
        <div class="sidebar">
            <div class="sidebar-section">
                <h3>🔗 连接设置</h3>
                <div class="form-group">
                    <label for="serverUrl">服务器地址</label>
                    <input type="text" id="serverUrl" class="form-control" placeholder="自动获取...">
                </div>
                <div class="form-group">
                    <label for="clientId">客户端ID</label>
                    <input type="text" id="clientId" class="form-control" value="log-viewer-001">
                </div>
                <div class="form-group">
                    <button id="connectBtn" class="btn btn-primary" style="width: 100%;">连接</button>
                </div>
            </div>

            <div class="sidebar-section">
                <h3>📁 日志文件</h3>
                <div class="form-group">
                    <button id="refreshFilesBtn" class="btn btn-secondary" style="width: 100%;">刷新文件列表</button>
                </div>
                <div id="fileList" class="file-list">
                    <div class="loading">加载中...</div>
                </div>
            </div>
        </div>

        <div class="log-container">
            <div class="log-controls">
                <div class="filter-group">
                    <label>级别过滤:</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" id="filterError" checked> ERROR</label>
                        <label><input type="checkbox" id="filterWarn" checked> WARN</label>
                        <label><input type="checkbox" id="filterInfo" checked> INFO</label>
                        <label><input type="checkbox" id="filterDebug" checked> DEBUG</label>
                        <label><input type="checkbox" id="filterSystem" checked> SYSTEM</label>
                    </div>
                </div>
                
                <div class="filter-group">
                    <label for="searchBox">搜索:</label>
                    <input type="text" id="searchBox" class="form-control search-box" placeholder="关键词搜索...">
                </div>
                
                <div class="filter-group">
                    <button id="clearLogsBtn" class="btn btn-secondary btn-small">清空日志</button>
                    <button id="exportLogsBtn" class="btn btn-success btn-small">导出日志</button>
                    <button id="pauseBtn" class="btn btn-secondary btn-small">暂停</button>
                </div>
                
                <div class="stats">
                    <span id="filteredCount">显示: 0</span> | 
                    <span id="totalCount">总计: 0</span>
                </div>
            </div>
            
            <div id="logOutput" class="log-output">
                <div class="loading">等待连接...</div>
            </div>
        </div>
    </div>

    <div id="messageContainer"></div>

    <script>
        class LogViewer {
            constructor() {
                this.websocket = null;
                this.isConnected = false;
                this.isPaused = false;
                this.logs = [];
                this.filteredLogs = [];
                this.serverConfig = null;
                this.elements = {};
                this.currentFile = null;
                this.realTimeUpdateTimer = null;
                this.lastFileSize = 0;
                this.isRealTimeMode = false;
            }

            init() {
                this.initElements();
                this.bindEvents();
                this.validateToken().then(isValid => {
                    if (isValid) {
                        this.loadServerConfig();
                        this.refreshFileList();
                    } else {
                        this.redirectToLogin();
                    }
                });
            }

            initElements() {
                this.elements = {
                    serverUrl: document.getElementById('serverUrl'),
                    clientId: document.getElementById('clientId'),
                    connectBtn: document.getElementById('connectBtn'),
                    refreshFilesBtn: document.getElementById('refreshFilesBtn'),
                    fileList: document.getElementById('fileList'),
                    logOutput: document.getElementById('logOutput'),
                    connectionStatus: document.getElementById('connectionStatus'),
                    connectionText: document.getElementById('connectionText'),
                    logCount: document.getElementById('logCount'),
                    lastUpdate: document.getElementById('lastUpdate'),
                    clearLogsBtn: document.getElementById('clearLogsBtn'),
                    exportLogsBtn: document.getElementById('exportLogsBtn'),
                    pauseBtn: document.getElementById('pauseBtn'),
                    searchBox: document.getElementById('searchBox'),
                    filteredCount: document.getElementById('filteredCount'),
                    totalCount: document.getElementById('totalCount'),
                    filterError: document.getElementById('filterError'),
                    filterWarn: document.getElementById('filterWarn'),
                    filterInfo: document.getElementById('filterInfo'),
                    filterDebug: document.getElementById('filterDebug'),
                    filterSystem: document.getElementById('filterSystem'),
                    tokenStatus: document.getElementById('tokenStatus'),
                    tokenStatusText: document.getElementById('tokenStatusText'),
                    tokenExpireTime: document.getElementById('tokenExpireTime'),
                    logoutBtn: document.getElementById('logoutBtn')
                };
            }

            bindEvents() {
                this.elements.connectBtn.addEventListener('click', () => this.toggleConnection());
                this.elements.refreshFilesBtn.addEventListener('click', () => this.refreshFileList());
                this.elements.clearLogsBtn.addEventListener('click', () => this.clearLogs());
                this.elements.exportLogsBtn.addEventListener('click', () => this.exportLogs());
                this.elements.pauseBtn.addEventListener('click', () => this.togglePause());
                this.elements.searchBox.addEventListener('input', () => this.filterLogs());

                // 过滤器事件
                [this.elements.filterError, this.elements.filterWarn, this.elements.filterInfo,
                 this.elements.filterDebug, this.elements.filterSystem].forEach(checkbox => {
                    checkbox.addEventListener('change', () => this.filterLogs());
                });

                // 键盘快捷键
                document.addEventListener('keydown', (e) => {
                    if (e.ctrlKey || e.metaKey) {
                        switch(e.key) {
                            case 'k':
                                e.preventDefault();
                                this.clearLogs();
                                break;
                            case 'f':
                                e.preventDefault();
                                this.elements.searchBox.focus();
                                break;
                            case 's':
                                e.preventDefault();
                                this.exportLogs();
                                break;
                        }
                    }
                });
            }

            async loadServerConfig() {
                try {
                    // 优先使用注入的配置
                    if (window.SERVER_CONFIG) {
                        this.serverConfig = window.SERVER_CONFIG;
                        this.elements.serverUrl.value = this.serverConfig.websocketUrl.replace('{clientId}', '');
                        this.elements.serverUrl.placeholder = '已通过配置注入获取服务器地址';
                        console.log('✅ 使用注入的服务器配置');
                        return;
                    }

                    // 回退到API获取（需要accessToken）
                    const urlParams = new URLSearchParams(window.location.search);
                    const accessToken = urlParams.get('accessToken') || urlParams.get('token');

                    if (accessToken) {
                        const response = await fetch(this.buildApiUrl(`/viewer?accessToken=${accessToken}`));
                        if (response.ok) {
                            const result = await response.json();
                            if (result.code === 200) {
                                this.serverConfig = result.data;
                                this.elements.serverUrl.value = result.data.websocketUrl.replace('{clientId}', '');
                                this.elements.serverUrl.placeholder = '已通过API获取服务器配置';
                                console.log('✅ 通过API获取服务器配置');
                                return;
                            }
                        }
                    }

                    // 最后回退到默认配置
                    this.elements.serverUrl.value = 'ws://localhost:8000/api/websocket/logs/';
                    this.elements.serverUrl.placeholder = '使用默认配置';
                    console.log('⚠️ 使用默认WebSocket配置');

                } catch (error) {
                    console.error('加载服务器配置失败:', error);
                    this.showMessage('加载服务器配置失败', 'error');
                }
            }

            buildApiUrl(path) {
                // 当前URL: http://localhost:8000/api/api/log-management/viewer/page
                // 目标API: http://localhost:8000/api/log-management/*

                const origin = window.location.origin; // http://localhost:8000
                const pathname = window.location.pathname; // /api/api/log-management/viewer/page

                // 查找第一个 /api/ 的位置，保留到第一个 /api
                const firstApiIndex = pathname.indexOf('/api/');
                if (firstApiIndex !== -1) {
                    // 找到第二个 /api/ 的位置
                    const secondApiIndex = pathname.indexOf('/api/', firstApiIndex + 1);
                    if (secondApiIndex !== -1) {
                        // 如果有重复的 /api/，使用第一个 /api 构建基础路径
                        const basePath = pathname.substring(0, firstApiIndex + 4); // /api
                        return origin + basePath + '/log-management' + path;
                    }
                }

                // 回退逻辑：直接构建正确的API路径
                return origin + '/api/log-management' + path;
            }

            async refreshFileList() {
                try {
                    const accessToken = this.getAccessToken();
                    if (!accessToken) {
                        this.showMessage('缺少访问令牌，无法获取文件列表', 'error');
                        return;
                    }

                    const response = await fetch(this.buildApiUrl(`/files?accessToken=${accessToken}`));
                    if (response.ok) {
                        const result = await response.json();
                        if (result.code === 200) {
                            this.lastFileList = result.data.files; // 保存文件列表
                            this.renderFileList(result.data.files);
                            this.showMessage(`已加载 ${result.data.count} 个日志文件`, 'success');
                        } else {
                            this.showMessage(`获取文件列表失败: ${result.message}`, 'error');
                        }
                    } else {
                        this.showMessage('获取文件列表失败', 'error');
                    }
                } catch (error) {
                    console.error('刷新文件列表失败:', error);
                    this.showMessage('刷新文件列表失败', 'error');
                }
            }

            renderFileList(files) {
                if (!files || files.length === 0) {
                    this.elements.fileList.innerHTML = '<div class="loading">暂无日志文件</div>';
                    return;
                }

                const html = files.map(file => `
                    <div class="file-item" data-filename="${file.name}">
                        <div class="file-info">
                            <div class="file-name">${file.name}</div>
                            <div class="file-meta">${this.formatFileSize(file.size)} | ${this.formatDate(file.lastModified)}</div>
                        </div>
                        <div class="file-actions">
                            <button class="btn btn-primary btn-small" onclick="logViewer.viewFile('${file.name}')">查看</button>
                            <button class="btn btn-success btn-small" onclick="logViewer.downloadFile('${file.name}')">下载</button>
                        </div>
                    </div>
                `).join('');

                this.elements.fileList.innerHTML = html;

                // 标记当前查看的文件
                if (this.currentFile) {
                    const currentItem = this.elements.fileList.querySelector(`[data-filename="${this.currentFile}"]`);
                    if (currentItem) {
                        currentItem.classList.add('selected');
                    }
                }
            }

            async viewFile(filename) {
                try {
                    const accessToken = this.getAccessToken();
                    if (!accessToken) {
                        throw new Error('缺少访问令牌');
                    }

                    // 停止之前的实时更新
                    this.stopRealTimeUpdate();

                    // 设置当前文件
                    this.currentFile = filename;
                    this.isRealTimeMode = true;

                    const response = await fetch(this.buildApiUrl(`/tail/${filename}?lines=100&accessToken=${accessToken}`));
                    if (response.ok) {
                        const result = await response.json();
                        if (result.code === 200) {
                            this.clearLogs();
                            result.data.lines.forEach((line, index) => {
                                this.addLogEntry({
                                    timestamp: new Date().toISOString(),
                                    level: this.detectLogLevel(line),
                                    body: line,
                                    sequence: Date.now() + index
                                });
                            });

                            // 记录文件大小用于实时更新
                            this.lastFileSize = result.data.fileSize || 0;

                            this.showMessage(`已加载文件 ${filename} 的最后 ${result.data.actualLines} 行`, 'success');

                            // 启动实时更新
                            this.startRealTimeUpdate();

                            // 更新文件列表选中状态
                            this.renderFileList(this.lastFileList || []);
                        }
                    }
                } catch (error) {
                    this.showMessage(`查看文件失败: ${error.message}`, 'error');
                }
            }

            async downloadFile(filename) {
                try {
                    const accessToken = this.getAccessToken();
                    if (!accessToken) {
                        throw new Error('缺少访问令牌');
                    }

                    // 修复：使用正确的下载URL和处理方式
                    const downloadUrl = this.buildApiUrl(`/download/${filename}?accessToken=${accessToken}`);

                    // 创建隐藏的下载链接
                    const link = document.createElement('a');
                    link.href = downloadUrl;
                    link.download = filename;
                    link.style.display = 'none';

                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    this.showMessage(`开始下载文件: ${filename}`, 'success');
                } catch (error) {
                    console.error('下载文件失败:', error);
                    this.showMessage(`下载文件失败: ${error.message}`, 'error');
                }
            }

            getAccessToken() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get('accessToken') || urlParams.get('token');
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 B';
                const k = 1024;
                const sizes = ['B', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            formatDate(dateString) {
                const date = new Date(dateString);
                return date.toLocaleString('zh-CN');
            }

            toggleConnection() {
                if (this.isConnected) {
                    this.disconnect();
                } else {
                    this.connect();
                }
            }

            connect() {
                try {
                    const serverUrl = this.elements.serverUrl.value.trim();
                    const clientId = this.elements.clientId.value.trim();

                    if (!serverUrl || !clientId) {
                        this.showMessage('请填写服务器地址和客户端ID', 'error');
                        return;
                    }

                    const wsUrl = serverUrl.replace('{clientId}', clientId) + clientId;
                    console.log('连接WebSocket:', wsUrl);

                    this.websocket = new WebSocket(wsUrl);

                    this.websocket.onopen = () => {
                        this.isConnected = true;
                        this.updateConnectionStatus();
                        this.showMessage('WebSocket连接成功', 'success');
                        this.elements.connectBtn.textContent = '断开连接';
                        this.elements.connectBtn.className = 'btn btn-danger';
                    };

                    this.websocket.onmessage = (event) => {
                        console.log('📨 收到WebSocket消息:', event.data);
                        if (!this.isPaused) {
                            this.handleMessage(event.data);
                        } else {
                            console.log('⏸️ 日志输出已暂停，跳过消息处理');
                        }
                    };

                    this.websocket.onclose = () => {
                        this.isConnected = false;
                        this.updateConnectionStatus();
                        this.showMessage('WebSocket连接已断开', 'info');
                        this.elements.connectBtn.textContent = '连接';
                        this.elements.connectBtn.className = 'btn btn-primary';
                    };

                    this.websocket.onerror = (error) => {
                        console.error('WebSocket错误:', error);
                        this.showMessage('WebSocket连接错误', 'error');
                    };

                } catch (error) {
                    console.error('连接失败:', error);
                    this.showMessage('连接失败: ' + error.message, 'error');
                }
            }

            disconnect() {
                if (this.websocket) {
                    this.websocket.close();
                    this.websocket = null;
                }
                this.stopRealTimeUpdate(); // 断开连接时停止实时更新
            }

            handleMessage(data) {
                try {
                    let logMessage;

                    // 尝试解析JSON格式的消息
                    try {
                        logMessage = JSON.parse(data);
                        console.log('✅ 成功解析JSON消息:', logMessage);
                    } catch (e) {
                        // 如果不是JSON，创建简单的日志对象
                        logMessage = {
                            timestamp: new Date().toISOString(),
                            level: 'INFO',
                            body: data,
                            sequence: Date.now()
                        };
                        console.log('📝 创建简单日志对象:', logMessage);
                    }

                    this.addLogEntry(logMessage);
                    console.log('📊 当前日志总数:', this.logs.length);
                } catch (error) {
                    console.error('处理消息失败:', error);
                }
            }

            addLogEntry(logMessage) {
                this.logs.push(logMessage);

                // 限制日志数量，避免内存溢出
                if (this.logs.length > 10000) {
                    this.logs = this.logs.slice(-5000);
                }

                this.filterLogs();
                this.updateStats();
                this.updateLastUpdate();
            }

            filterLogs() {
                const searchTerm = this.elements.searchBox.value.toLowerCase();
                const filters = {
                    error: this.elements.filterError.checked,
                    warn: this.elements.filterWarn.checked,
                    info: this.elements.filterInfo.checked,
                    debug: this.elements.filterDebug.checked,
                    system: this.elements.filterSystem.checked
                };

                this.filteredLogs = this.logs.filter(log => {
                    // 级别过滤
                    const level = (log.level || 'info').toLowerCase();
                    if (!filters[level] && !filters.system) {
                        return false;
                    }

                    // 搜索过滤
                    if (searchTerm) {
                        const content = (log.body || log.formattedMessage || '').toLowerCase();
                        return content.includes(searchTerm);
                    }

                    return true;
                });

                this.renderLogs();
                this.updateStats();
            }

            renderLogs() {
                if (this.filteredLogs.length === 0) {
                    this.elements.logOutput.innerHTML = '<div class="loading">暂无日志数据</div>';
                    return;
                }

                const html = this.filteredLogs.map(log => {
                    const level = (log.level || 'info').toLowerCase();
                    const timestamp = log.timestamp ? new Date(log.timestamp).toLocaleTimeString() : '';
                    const content = log.body || log.formattedMessage || '';

                    return `<div class="log-entry ${level}">[${timestamp}] ${content}</div>`;
                }).join('');

                this.elements.logOutput.innerHTML = html;

                // 自动滚动到底部
                this.elements.logOutput.scrollTop = this.elements.logOutput.scrollHeight;
            }

            clearLogs() {
                this.logs = [];
                this.filteredLogs = [];
                this.elements.logOutput.innerHTML = '<div class="loading">日志已清空</div>';
                this.updateStats();
                this.showMessage('日志已清空', 'info');
                this.stopRealTimeUpdate();
            }

            // 启动实时更新
            startRealTimeUpdate() {
                if (!this.currentFile || !this.isRealTimeMode) return;

                this.stopRealTimeUpdate(); // 确保没有重复的定时器

                this.realTimeUpdateTimer = setInterval(async () => {
                    await this.checkForLogUpdates();
                }, 3000); // 每3秒检查一次更新

                console.log('✅ 启动实时日志更新');
            }

            // 停止实时更新
            stopRealTimeUpdate() {
                if (this.realTimeUpdateTimer) {
                    clearInterval(this.realTimeUpdateTimer);
                    this.realTimeUpdateTimer = null;
                    console.log('⏹️ 停止实时日志更新');
                }
                this.isRealTimeMode = false;
            }

            // 检查日志文件更新
            async checkForLogUpdates() {
                if (!this.currentFile || this.isPaused) return;

                try {
                    const accessToken = this.getAccessToken();
                    if (!accessToken) return;

                    // 获取文件最新的几行内容
                    const response = await fetch(this.buildApiUrl(`/tail/${this.currentFile}?lines=10&accessToken=${accessToken}`));
                    if (response.ok) {
                        const result = await response.json();
                        if (result.code === 200) {
                            const currentFileSize = result.data.fileSize || 0;

                            // 如果文件大小发生变化，说明有新内容
                            if (currentFileSize > this.lastFileSize) {
                                const newLines = result.data.lines;

                                // 添加新的日志条目
                                newLines.forEach((line, index) => {
                                    // 避免重复添加已存在的日志
                                    if (!this.logs.some(log => log.body === line)) {
                                        this.addLogEntry({
                                            timestamp: new Date().toISOString(),
                                            level: this.detectLogLevel(line),
                                            body: line,
                                            sequence: Date.now() + index
                                        });
                                    }
                                });

                                this.lastFileSize = currentFileSize;

                                // 限制日志条目数量，避免内存溢出
                                if (this.logs.length > 1000) {
                                    this.logs = this.logs.slice(-800); // 保留最新的800条
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.error('检查日志更新失败:', error);
                }
            }

            // 检测日志级别
            detectLogLevel(logLine) {
                const line = logLine.toLowerCase();
                if (line.includes('error') || line.includes('错误')) return 'ERROR';
                if (line.includes('warn') || line.includes('警告')) return 'WARN';
                if (line.includes('debug') || line.includes('调试')) return 'DEBUG';
                if (line.includes('info') || line.includes('信息')) return 'INFO';
                return 'INFO';
            }

            exportLogs() {
                if (this.filteredLogs.length === 0) {
                    this.showMessage('没有日志可导出', 'error');
                    return;
                }

                const content = this.filteredLogs.map(log => {
                    const timestamp = log.timestamp ? new Date(log.timestamp).toISOString() : '';
                    const level = log.level || 'INFO';
                    const body = log.body || log.formattedMessage || '';
                    return `[${timestamp}] [${level}] ${body}`;
                }).join('\n');

                const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `edc-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
                link.click();
                URL.revokeObjectURL(url);

                this.showMessage(`已导出 ${this.filteredLogs.length} 条日志`, 'success');
            }

            togglePause() {
                this.isPaused = !this.isPaused;
                this.elements.pauseBtn.textContent = this.isPaused ? '继续' : '暂停';
                this.elements.pauseBtn.className = this.isPaused ? 'btn btn-success btn-small' : 'btn btn-secondary btn-small';
                this.showMessage(this.isPaused ? '日志输出已暂停' : '日志输出已恢复', 'info');
            }

            updateConnectionStatus() {
                if (this.isConnected) {
                    this.elements.connectionStatus.className = 'status-dot connected';
                    this.elements.connectionText.textContent = '已连接';
                } else {
                    this.elements.connectionStatus.className = 'status-dot';
                    this.elements.connectionText.textContent = '未连接';
                }
            }

            updateStats() {
                this.elements.logCount.textContent = `${this.logs.length} 条日志`;
                this.elements.filteredCount.textContent = `显示: ${this.filteredLogs.length}`;
                this.elements.totalCount.textContent = `总计: ${this.logs.length}`;
            }

            updateLastUpdate() {
                this.elements.lastUpdate.textContent = new Date().toLocaleTimeString();
            }

            showMessage(text, type = 'info') {
                const message = document.createElement('div');
                message.className = `message ${type}`;
                message.textContent = text;

                document.getElementById('messageContainer').appendChild(message);

                // 显示消息
                setTimeout(() => message.classList.add('show'), 100);

                // 3秒后隐藏消息
                setTimeout(() => {
                    message.classList.remove('show');
                    setTimeout(() => {
                        if (message.parentNode) {
                            message.parentNode.removeChild(message);
                        }
                    }, 300);
                }, 3000);
            }

            // Token验证相关方法
            async validateToken() {
                const accessToken = this.getAccessToken();
                if (!accessToken) {
                    this.updateTokenStatus(false, '缺少访问令牌');
                    return false;
                }

                try {
                    // 通过API验证token
                    const response = await fetch(this.buildApiUrl(`/viewer?accessToken=${encodeURIComponent(accessToken)}`));
                    const result = await response.json();

                    if (result.code === 200 && result.data) {
                        // Token有效，更新状态和过期时间
                        this.updateTokenStatus(true, 'Token有效');
                        this.updateTokenExpireTime(result.data.tokenExpireTime);
                        this.elements.logoutBtn.style.display = 'inline-block';
                        return true;
                    } else {
                        this.updateTokenStatus(false, result.message || 'Token验证失败');
                        return false;
                    }
                } catch (error) {
                    console.error('Token验证失败:', error);
                    this.updateTokenStatus(false, 'Token验证异常');
                    return false;
                }
            }

            updateTokenStatus(isValid, message) {
                if (isValid) {
                    this.elements.tokenStatus.className = 'status-dot valid';
                    this.elements.tokenStatusText.textContent = message || 'Token有效';
                } else {
                    this.elements.tokenStatus.className = 'status-dot invalid';
                    this.elements.tokenStatusText.textContent = message || 'Token无效';
                }
            }

            updateTokenExpireTime(expireTime) {
                if (expireTime) {
                    this.elements.tokenExpireTime.textContent = expireTime;
                } else {
                    this.elements.tokenExpireTime.textContent = '--';
                }
            }

            redirectToLogin() {
                const currentUrl = window.location.href;
                const loginUrl = '/api/log-management/log-login.html';
                console.log('Token验证失败，跳转到登录页面:', loginUrl);
                window.location.href = loginUrl;
            }
        }

        // 全局变量
        let logViewer = null;

        // 全局退出函数
        function logout() {
            if (confirm('确定要退出日志管理系统吗？')) {
                // 清除可能的本地存储
                localStorage.removeItem('accessToken');
                sessionStorage.removeItem('accessToken');

                // 跳转到登录页面
                window.location.href = '/api/log-management/log-login.html';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            logViewer = new LogViewer();
            logViewer.init();
        });
    </script>
</body>
</html>
