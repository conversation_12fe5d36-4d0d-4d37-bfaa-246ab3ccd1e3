########## datasource config ###########
server:
  port: 8090
  servlet:
    context-path: /api
    encoding:
      enabled: true
      force: true
      charset: utf-8

spring:
  messages:
    basename: config/messages
  application:
    name: edc-research-project
  servlet:
    multipart:
      enabled: true
      max-file-size: 50MB
      max-request-size: 50MB
      file-size-threshold: 2KB
      location: ${java.io.tmpdir}
  main:
    allow-circular-references: true
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

################## 数据源配置 ################
  datasource:
    dynamic:
      enabled: true
      primary: master
      strict: false
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ********************************************************************************************************************************************************************************************='STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION'
          username: edcre
          password: U5Q5zr39husVTC!y

################## redis配置 ################
  redis:
    host: ************
    database: 15
    port: 6379
    password: kjyygy123456
    timeout: 30000

################## mongodb配置 ################
mongodb:
  enabled: false
  uri: ************:29018
  database: admin
  username: edc-reseacher
  password: K0qjYhod5qJvioZ2
  authenticationDatabase: admin

################## elasticsearch配置################
elasticsearch:
  # 多个IP逗号隔开
  hosts: ************:9200
  username: kibana_system
  passwd: bc+rAAOkBNkIylofKY=E
  apikey: eWZPeUhKSUJkbzNvUzFkMXlwNC06eEVXVVRnY0RSSkNZMEVrYmwxRDl6QQ==
  certificate_path: certificates/edc_remote_http_ca.crt
  enabled_ssl: true

################## 数据中心和专病库配置 ################
rdr-center:
  database_name: edc_disease_rdr
  patient_full_text_index: edc_xjbfy_full_text_index
  patient_visit_full_text_index: xjbfy_visit_full_text_index
  patient_join_visit_index: patient_join_visit_index

disease-xinjiang-center:
  database_name: edc_disease_xinjiang
  patient_full_text_index: xinjiang_patient_full_text_index
  patient_visit_full_text_index: xinjiang_visit_full_text_index
  patient_join_visit_index: xinjiang_patient_join_visit_index

################## 随机系统随机配置python脚本路径 ################
rcts:
  random_static_script_path: /webserver/python-script/rcts/staticRandomized.py
  random_dynamic_script_path: /webserver/python-script/rcts/dynamicRandomized.py
  random_bind_record_path: /webserver/python-script/rcts/record

################## mybatis配置 ################
mybatis:
  mapper-locations:
    - classpath:dao/*.xml
    - classpath*:com/**/mapper/*.xml
    - classpath*:mapper/**/*.xml
  configuration:
    map-underscore-to-camel-case: true
    call-setters-on-nulls: true

############ 安全认证配置 ############
# JWT Token配置
jwt:
  # Token存储的请求头名称
  tokenHeader: Authorization
  # JWT加解密密钥（生产环境建议使用环境变量）
  secret: mis-admin-secret
  # Token过期时间（秒）1800=30分钟
  expiration: 1800
  # Token前缀
  tokenHead: 'Bearer '
  # Token刷新时间（秒）900=15分钟
  refresh-time: 900

# 安全Token管理配置
secure-token:
  # 密钥配置
  secret-key: "EDC-SECURE-TOKEN-SECRET-KEY-2025"
  # Code有效期（秒）- 2分钟
  code-expiration: 120
  # AccessToken有效期（秒）- 1小时
  access-token-expiration: 3600
  # Redis键前缀
  redis-prefix: "secure:token:"
  # 是否启用
  enabled: true

# Redis数据管理配置
redis:
  management:
    # 管理密钥配置
    secret-key: "EDC-REDIS-MANAGEMENT-SECRET-2025"
    # 是否启用Redis管理功能
    enabled: true
    # 查询限制数量
    query-limit: 1000
    # 删除确认要求
    require-confirmation: true
    # 允许的操作类型
    allowed-operations: ["query", "delete"]
    # 禁止操作的key前缀
    forbidden-prefixes: ["system:", "security:", "session:"]

# 业务表管理配置
business:
  table:
    # 是否自动创建表
    auto-create: true
    # 启动时是否检查表
    check-on-startup: true
    # 创建失败时是否中止启动
    fail-on-error: false


############ 分页插件配置 ############
# PageHelper分页配置 - 优化性能
pagehelper:
  # 自动检测数据库方言
  auto-dialect: true
  # 关闭运行时自动检测，提升性能
  auto-runtime-dialect: false
  # 指定数据库方言
  helper-dialect: mysql
  # 分页合理化参数
  reasonable: true
  # 支持通过方法参数传递分页参数
  support-methods-arguments: true
  # 分页参数
  params: count=countSql
  # 当pageSize=0时是否返回全部结果
  page-size-zero: false
  # 关闭分页插件的自动检测，提升性能
  close-conn: true

########### 安全路径白名单 ###########
secure:
  ignored:
    urls:
      # ========== 系统基础资源 ==========
      # 静态资源文件
      - /**/*.js
      - /**/*.css
      - /**/*.png
      - /**/*.jpeg
      - /**/*.jpg
      - /**/*.ico
      - /**/*.html
      - /**/*.md
      - /**/*.sh
      - /static/**
      - /file/**

      # ========== API文档相关 ==========
      # Swagger文档接口
      - /swagger-ui.html
      - /swagger-resources/**
      - /swagger/**
      - /doc.html
      - /**/v2/api-docs
      - /webjars/springfox-swagger-ui/**

      # ========== 监控和管理 ==========
      # 应用监控端点
      - /actuator/**
      # 数据库监控（如果启用Druid）
      - /druid/**

      # ========== 访问日志管理 ==========
      # 访问日志管理页面
      - /access-log/management
      # 访问日志认证接口
      - /access-log/auth/**
      # 访问日志管理API接口（需要AccessToken认证）
      - /access-log/management/**

      # ========== 定时任务管理 ==========
      # 定时任务管理页面
      - /quartz/job/management
      # 定时任务认证接口
      - /quartz/job/auth/**

      # 系统监控页面
      - /monitor/**
      - /templates/system-monitor/**
      # 系统监控API接口
      - /system/monitor/**
      # 安全令牌验证
      - /secure/token/validate

      # ========== 用户认证相关 ==========
      # 管理员登录注册（核心认证接口）
      - /admin/login
      - /admin/register
      - /admin/savePatientUser
      - /admin/verificationCodeLogin
      - /admin/updateTesteePassword
      - /admin/getUserBaseInfoByUserName
      - /admin/checkVerificationCodeByAccount
      - /admin/findUserPassword
      - /admin/getSystemUserInfoByTokenValue
      - /admin/checkToken
      - /admin/actUser
      - /admin/externalSystemRegisterUser
      - /admin/getUserLoginTokenByAccessToken
      - /admin/getUserBaseInfoByAccountName
      - /admin/getUserLoginResult
      - /admin/externalSystemUserLogin
      - /admin/getSystemUserActiveStatus

      # 移动端用户（移动端认证）
      - /mobileUser/register
      - /mobileUser/checkVerification
      - /mobileLogin/**

      # 验证码服务（登录必需）
      - /captcha/**

      # ========== 消息通知服务 ==========
      # 短信和邮件服务（验证码发送）
      - /message/sendMobileMessageCode
      - /message/sendMessageCodeByEmail

      # ========== 文件服务 ==========
      # 文件上传接口
      - /minio/upload
      - /postFileUpload
      #- /fileManage/**

      # ========== 第三方集成接口 ==========
      # 报表系统集成
      - /thirdPartyApi/getAjreportLoginToken
      - /thirdPartyApi/getUserLoginInfo

      # ========== 数据同步接口 ==========
      # 患者数据同步（外部系统调用）
      - /patients/**
      - /patient-data/**
      #- /patient-model/**
      - /patientSync/**
      - /xinjiang/patient-data/**
      - /xinjiang/patientSync/**

      # ========== 系统配置接口 ==========
      # 组织信息配置（系统初始化）
      - /sysOrgInfo/add
      - /sysOrgInfo/list
      - /sysOrgInfo/getThirdSystemOrgInfoByName

      # ========== 数据展示接口 ==========
      # 数据大屏（公开展示，无需认证）
      - /rdr-bigScreen/**

      # ========== 外部系统集成 ==========
      # 科研系统Token获取
      - /keYanSystem/getToken/**

      # ========== 安全Token管理接口 ==========
      # 安全Token相关接口
      - /secure/token

      # ========== Redis数据管理接口 ==========
      # Redis管理静态页面
      - /redis-management/**
      # Redis管理API接口（需要AccessToken认证）
      - /redis/management/**

      # ========== 日志服务接口 ==========
      # 日志查看器相关接口（旧路径）
      - /logs/health
      - /logs/viewer
      # 新的日志管理接口（使用accessToken验证）
      - /log-management/viewer
      - /log-management/viewer/**
      - /log-management/files/**
      - /log-management/download/**
      - /log-management/tail/**
      - /log-management/content/**
      # 完整路径（包含context path）
      - /api/log-management/**
      # WebSocket连接端点
      - /websocket/**
      # 前端测试页面
      - /frontend-tests/**
      - /api/enhanced-generator/**

########### 短信验证码 ###########
sms:
  secretId: AKIDHOX6Vm8SefSF7FMJMRPM9i6FAtOey49A
  secretKey: 247HYgcaXL5ubSKAqTD1pV6Xs382367y
  sdkAppId: **********
  signName: 北京健康在线
  # 登录和找回密码模板
  templateId : 1705939
  # 邀请用户模板
  inviteTemplateId: 2442001
  subject : 好医生集团科研协作平台

########### 百度OCR ###########
baiduyun:
    ocr:
      APIKey: hwDGxThlKUAQSXFVukYnejLP
      SecretKey: qgtgY7WZRGlZCOTPFthqGwVohqlCEocS
    nlp:
      APIKey: f1uF2nBglEhFvB6TfTa4yuzG
      SecretKey: 4EDGwGu0V3YEzPH3VFHiqdLQx2bmlBhM


############ 日志查看器配置 ############
# 日志查看器安全配置
log:
  viewer:
    # 访问令牌（生产环境请使用更安全的令牌）
    access-token: edc-log-viewer-2025-dev
    # 令牌有效期（分钟）
    token-expire-minutes: 120
    # 最大连接数限制
    max-connections: 10
    # 日志推送间隔（秒）
    push-interval: 3

############ WebSocket日志配置 ############
# WebSocket连接和日志输出控制配置
websocket:
  # 服务器配置（支持远程部署）
  server:
    # WebSocket服务器地址（开发环境使用localhost）
    address: ************
  # 日志输出控制配置
  log:
    # 是否启用WebSocket相关日志输出
    enabled: false
    # 是否启用在线日志输出功能
    online-output-enabled: false
    # 是否启用日志打印功能
    print-enabled: false
    # 日志级别（DEBUG, INFO, WARN, ERROR）
    level: WARN
    # 扫描间隔（秒）
    scan-interval: 5
    # 最大行数
    max-lines: 500
    # ========== 智能日志提取配置 ==========
    # 是否启用智能日志内容提取（默认false，显示完整日志）
    smart-extract-enabled: false
    # 是否显示完整日志内容（默认true，优先级高于智能提取）
    show-full-content: true
    # 是否只提取日志内容部分（默认false，当智能提取启用时生效）
    extract-content-only: false
    # ========== WebSocket日志测试任务配置 ==========
    # 是否启用WebSocket日志测试任务（仅在local环境启用）
    test-task-enabled: false

############ 事务测试配置 ############
# 全局事务测试配置
transaction:
  test:
    # 是否启用事务测试
    enabled: false
    # 是否输出事务测试日志
    log-enabled: false
    # 事务测试日志级别
    log-level: WARN
    # 是否在启动时自动运行测试
    auto-run-on-startup: false
    # 测试结果输出格式（SIMPLE, DETAILED）
    output-format: SIMPLE

############ 安全防护配置 ############
# XSS攻击防护配置（使用增强版XSS过滤器）
xss:
  # 启用XSS过滤
  enabled: true
  # 排除不需要过滤的接口（支持Ant路径模式）
  excludes: /api/upload/**,/api/file/**,/doc.html,/swagger-ui/**,/captcha/**,/actuator/**
  # 需要过滤的URL模式
  urlPatterns: /api/**
  # 严格模式（启用更严格的XSS检测）
  strictMode: true
  # 最大输入长度限制
  maxInputLength: 10000


############ 平台通用配置说明 ############
platform:
  oss:
    # 0:阿里云OSS，1:七牛云OSS，2:又拍云OSS，3:本地存储，4:MINIO存储OSS
    ossType: 1
    endpoint: 12
    accessKeyId: NpOWhsXxLniKSnT_VbilS8K3iOyqMVqE82jHUQx1
    accessKeySecret: Rx6t35hhyTz9koX7_JV_Y2__AMZTffENOqEJFFez
    bucketName: hysh-dev
    rootPath: edc-research-project
    domain: https://hysh-dev.haoyisheng.info
    uploadFolder: /webserver/upload/edc-remote/
    accessPathPattern: /file/**
    viewUrl: https://src-demo.haoyisheng.info/api/
  swagger:
    enable: false

############ LLM配置说明 ############
llm:
  chatbot_url: http://172.30.0.2:5000/chat
  chatbot_backup_url: https://mbgldev.haoyisheng.com/optimus/aiBoat/chat
  data_analysis_url: http://172.30.0.2:5001/data_analysis
  report_url: http://172.30.0.2:5001/report
  view_url: https://nlp.haoyisheng.info/

############ 科研数据分析服务地址 ############
thirdservice:
  research_url: https://src-demo.haoyisheng.info/sci
  sync_type: shiyong

############ 同步bdp用户服务地址 ############
bdpconfig:
  bdp_url: http://192.168.191.52:8380/bdp-web/login/loginByPhoneForOrigin
  enable: false

############ 系统日志配置说明 ############
# 使用Spring Boot原生日志配置，支持在线实时查看和管理
logging:
  # 日志级别配置
  level:
    root: INFO
    # 业务模块日志级别（开发环境使用DEBUG便于调试）
    com.haoys.user: DEBUG
    com.haoys.rdr: DEBUG
    com.haoys.disease: DEBUG
    com.haoys.xinjiang: DEBUG
    # 第三方组件日志级别优化（减少无用日志）
    org.springframework.data.convert.CustomConversions: ERROR
    org.mongodb.driver.connection: ERROR
    org.springframework.security: WARN
    org.springframework.web: INFO
    org.springframework.boot: INFO
    # 数据库相关日志
    com.zaxxer.hikari: WARN
    # 缓存和搜索引擎日志
    io.lettuce.core: WARN
    org.elasticsearch.client: WARN
    # HTTP客户端日志
    org.apache.http: WARN

  # 日志文件配置（统一存放便于管理）
  file:
    # 主日志文件路径
    name: /webserver/upload/edc-remote/logs/edc-research-remote.log
    # 单个日志文件最大大小（10MB，优化文件大小便于查看和传输）
    max-size: 10MB
    # 日志文件保留天数（30天，满足问题追溯需求）
    max-history: 30
    # 所有日志文件总大小限制（10GB，防止磁盘空间耗尽）
    total-size-cap: 10GB

  # 日志输出格式配置
  pattern:
    # 控制台输出格式（彩色，便于开发调试）
    console: "%clr(%d{HH:mm:ss.SSS}){faint} %clr(%5p) %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n"
    # 文件输出格式（详细信息，便于生产问题排查）
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}:%line] [TraceId:%X{traceId:-}] - %msg%n"
    # 滚动文件命名模式（按日期和大小分割）
    rolling-file-name: /webserver/upload/edc-remote/logs/edc-research-remote.%d{yyyy-MM-dd}.%i.log

  # 日志配置组（便于批量管理相关日志级别）
  group:
    # 业务日志组
    business: com.haoys.user,com.haoys.mis,com.haoys.rdr,com.haoys.disease,com.haoys.xinjiang
    # 数据库日志组
    database: org.hibernate,com.zaxxer.hikari,org.springframework.jdbc
    # 缓存日志组
    cache: io.lettuce,org.springframework.cache
    # 安全日志组
    security: org.springframework.security,com.haoys.user.security

## 前端地址
user:
  act-page: https://src-demo.haoyisheng.info/#/

## 是否开启密码策略
pwd:
  open-pwd-config: false

## 验证码
aj:
  captcha:
    jigsaw: classpath:static/images/jigsaw
    pic-click: classpath:static/images/pic-click
    cache-type: redis
    cache-number: 1000
    timing-clear: 180
    type: default
    water-mark: SRC-EDC
    slip-offset: 15
    aes-status: true
    interference-options: 0
    history-data-clear-enable: true
    req-frequency-limit-enable: true
    req-get-lock-limit: 5
    req-get-lock-seconds: 60
    req-get-minute-limit: 30
    req-check-minute-limit: 60
    req-verify-minute-limit: 60


#是否开启表单审核功能
form:
  audit: false

############ 系统监控配置 ############
# 异常告警配置（简化版）
exception:
  alert:
    # 启用异常告警
    enabled: true
    # 异常阈值（每分钟）
    threshold-per-minute: 10
    # 告警静默期（分钟）
    silence-period: 5
    # 告警接收人配置
    contacts:
      # 手机号（多个用逗号分隔）
      phones: "13341080362"
      # 邮箱（多个用逗号分隔）
      emails: "<EMAIL>"

# 安全审计配置（简化版）
security:
  audit:
    # 启用安全审计
    enabled: true
    # 记录级别（DEBUG, INFO, WARN, ERROR）
    level: INFO
    # 数据保留天数
    retention-days: 30
    # 存储类型（database, file）
    storage-type: database

############ 系统性能配置 ############
# 线程池配置
thread-pool:
  # 核心线程数
  core-size: 10
  # 最大线程数
  max-size: 50
  # 队列容量
  queue-capacity: 200
  # 线程空闲时间（秒）
  keep-alive-seconds: 60
  # 线程名前缀
  thread-name-prefix: "edc-async-"

# 缓存配置
cache:
  # 默认过期时间（秒）
  default-ttl: 3600


############ 系统许可证配置 ############
system:
  license:
    # 管理员访问令牌（用于远程管理许可证）
    access-token: edc-license-admin-2025-dev
    # 默认许可证有效期（天数）
    default-expire-days: 365
    # 许可证验证间隔（分钟）
    validation-interval: 60
    # 最大验证失败次数
    max-validation-failures: 5
    # 是否开启证书限制使用
    certificate-restriction-enabled: false

############ 性能监控配置 ############
performance:
  monitor:
    # 启用性能监控
    enabled: true
    # 慢方法阈值（毫秒）
    slow-method-threshold: 1000
    # 慢SQL阈值（毫秒）
    slow-sql-threshold: 500
    # 非常慢SQL阈值（毫秒）
    very-slow-sql-threshold: 2000
    # 统计信息保留时间（小时）
    stats-retention-hours: 24


############ 系统访问日志配置 ############
request:
  access:
    log:
      enabled: true                    # 是否启用系统日志记录
      async-enabled: true              # 是否启用异步日志记录
      save-request-data: true          # 是否记录请求参数
      save-response-data: true         # 是否记录响应数据
      save-device-info: true           # 是否记录设备信息
      save-location-info: true         # 是否记录地理位置信息
      max-param-length: 2000           # 最大参数长度限制
      max-response-length: 5000        # 最大响应结果长度限制
      slow-request-threshold: 5000     # 慢请求阈值（毫秒）
      data-retention-days: 90          # 数据保留天数
      auto-cleanup-enabled: true       # 是否启用数据自动清理
      async-queue-size: 1000           # 异步队列大小
      async-thread-pool-size: 5        # 异步线程池大小
      filter-static-resources: true    # 是否过滤静态资源
      filter-health-check: true        # 是否过滤健康检查请求
      sensitive-keys:                  # 敏感参数关键字列表
        - password
        - pwd
        - token
        - secret
        - key
        - authorization
        - passwd
        - credential
        - auth
        - sign
        - signature
      exclude-url-patterns:            # 需要过滤的URL模式
        - /actuator/**
        - /health/**
        - /static/**
        - /css/**
        - /js/**
        - /images/**
        - /favicon.ico
        - /webjars/**
        - "/*.css"
        - "/*.js"
        - "/*.png"
        - "/*.jpg"

      management:
        # 管理密钥配置
        secret-key: "EDC-QUARTZ-MANAGEMENT-SECRET-2025"
        # 是否启用访问日志管理功能
        enabled: true

############ 系统定时任务配置 ############
access:
  monitor:
    # 是否启用系统监控功能（总开关）
    enabled: true
    # 访问日志监控配置
    enable-access-log: true
    # 访问日志保留天数（超过此天数的日志将被自动清理）
    access-log-retention-days: 30
    # 在线用户监控配置
    enable-online-user: true
    # 在线用户超时时间（分钟），超过此时间未活动的用户将被标记为离线
    online-user-timeout-minutes: 120
    # 异常日志监控配置
    enable-exception-log: true
    # 统计数据配置
    enable-auto-statistics: true
    # 统计数据生成时间（cron表达式）- 每天凌晨1点自动生成前一天的统计数据
    statistics-cron: "0 0 1 * * ?"
    # 实时监控配置
    enable-real-time-monitor: true
    # 实时监控数据刷新间隔（秒）
    real-time-refresh-interval: 30