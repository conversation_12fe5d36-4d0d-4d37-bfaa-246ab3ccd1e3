// AI聊天应用

// 动态获取API基础路径
function getApiBaseUrl() {
    const protocol = window.location.protocol;
    const host = window.location.host;
    return `${protocol}//${host}/api`;
}

// 配置axios请求拦截器，自动添加用户身份信息
axios.interceptors.request.use(function (config) {
    // 尝试从多个来源获取用户ID
    let userId = null;
    let userName = null;
    let realName = null;

    // 1. 从sessionStorage获取（如果有的话）
    try {
        const userInfo = sessionStorage.getItem('userInfo');
        if (userInfo) {
            const user = JSON.parse(userInfo);
            userId = user.userId || user.id;
            userName = user.userName || user.username;
            realName = user.realName || user.name;
        }
    } catch (e) {
        console.debug('从sessionStorage获取用户信息失败:', e);
    }

    // 2. 从localStorage获取（如果有的话）
    if (!userId) {
        try {
            const userInfo = localStorage.getItem('userInfo');
            if (userInfo) {
                const user = JSON.parse(userInfo);
                userId = user.userId || user.id;
                userName = user.userName || user.username;
                realName = user.realName || user.name;
            }
        } catch (e) {
            console.debug('从localStorage获取用户信息失败:', e);
        }
    }

    // 3. 从cookie获取（如果有的话）
    if (!userId) {
        try {
            const cookies = document.cookie.split(';');
            let accessToken = null;

            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'userId') {
                    userId = decodeURIComponent(value);
                } else if (name === 'userName') {
                    userName = decodeURIComponent(value);
                } else if (name === 'realName') {
                    realName = decodeURIComponent(value);
                } else if (name === 'access_token') {
                    accessToken = decodeURIComponent(value);
                }
            }

            // 如果有access_token但没有直接的用户信息，设置token到请求头
            // 后端会通过access_token获取用户信息
            if (accessToken && !userId) {
                console.debug('从cookie获取到access_token，将设置到请求头');
                // 这里不设置用户信息，让后端通过access_token获取
                // 但我们可以设置一个标识，表明有token
                config.headers['X-Access-Token'] = accessToken;
            }
        } catch (e) {
            console.debug('从cookie获取用户信息失败:', e);
        }
    }

    // 4. 从全局变量获取（如果页面设置了的话）
    if (!userId && typeof window.currentUser !== 'undefined') {
        userId = window.currentUser.userId || window.currentUser.id;
        userName = window.currentUser.userName || window.currentUser.username;
        realName = window.currentUser.realName || window.currentUser.name;
    }

    // 设置请求头
    if (userId) {
        config.headers['X-User-Id'] = userId;
        console.debug('设置请求头 X-User-Id:', userId);
    }
    if (userName) {
        config.headers['X-User-Name'] = userName;
        console.debug('设置请求头 X-User-Name:', userName);
    }
    if (realName) {
        config.headers['X-Real-Name'] = realName;
        console.debug('设置请求头 X-Real-Name:', realName);
    }

    return config;
}, function (error) {
    return Promise.reject(error);
});

new Vue({
    el: '#app',
    data() {
        return {
            // 会话相关
            sessions: [],
            currentSessionId: null,
            currentSession: null,

            // 消息相关
            messages: [],
            inputMessage: '',
            uploadedFiles: [],
            isLoading: false,
            isTyping: false,

            // 模型相关
            availableModels: [],
            selectedModel: 'qwen:qwen-turbo',
            streamMode: false,

            // 用户认证状态
            authStatus: {
                isAuthenticated: false,
                userId: 'anonymousUser',
                userName: '匿名用户',
                userType: '匿名用户',
                hasUsageLimit: true,
                dailyLimit: 20
            },

            // 使用统计
            usageStats: {
                requestCount: 0,
                remainingCount: 20
            },

            // 其他
            eventSource: null,
            tokenUsage: null,
            authCheckTimer: null
        }
    },
    
    mounted() {
        this.init();

        // 设置定时器，每30秒检测一次认证状态
        this.authCheckTimer = setInterval(() => {
            this.refreshAuthStatus();
        }, 30000);
    },
    
    beforeDestroy() {
        if (this.eventSource) {
            this.eventSource.close();
        }

        // 清理认证状态检测定时器
        if (this.authCheckTimer) {
            clearInterval(this.authCheckTimer);
        }
    },
    
    methods: {
        // 初始化
        async init() {
            try {
                // 检测用户认证状态
                await this.checkAuthStatus();

                await this.loadAvailableModels();
                await this.loadSessions();

                // 如果有会话，选择第一个
                if (this.sessions.length > 0) {
                    this.selectSession(this.sessions[0].sessionId);
                }

                // 如果是匿名用户，加载使用统计
                if (!this.authStatus.isAuthenticated) {
                    await this.loadUsageStats();
                }
            } catch (error) {
                console.error('初始化失败:', error);
                this.$message.error('初始化失败');
            }
        },
        
        // 加载可用模型
        async loadAvailableModels() {
            try {
                const response = await axios.get('/api/ai/chat/models');
                if (response.data.code === 200) {
                    this.availableModels = response.data.data;
                }
            } catch (error) {
                console.error('加载模型失败:', error);
            }
        },
        
        // 加载会话列表
        async loadSessions() {
            try {
                const response = await axios.get('/api/ai/chat/sessions');
                if (response.data.code === 200) {
                    this.sessions = response.data.data.list || [];
                }
            } catch (error) {
                console.error('加载会话失败:', error);
            }
        },
        
        // 创建新会话
        async createNewSession() {
            try {
                const [modelType, modelName] = this.selectedModel.split(':');
                const response = await axios.post('/api/ai/chat/session/create', null, {
                    params: {
                        title: '新对话',
                        modelType,
                        modelName
                    }
                });
                
                if (response.data.code === 200) {
                    const newSession = response.data.data;
                    this.sessions.unshift(newSession);
                    this.selectSession(newSession.sessionId);
                    this.$message.success('新会话创建成功');
                } else {
                    this.$message.error(response.data.message);
                }
            } catch (error) {
                console.error('创建会话失败:', error);
                this.$message.error('创建会话失败');
            }
        },
        
        // 选择会话
        async selectSession(sessionId) {
            try {
                this.currentSessionId = sessionId;
                
                // 获取会话详情
                const sessionResponse = await axios.get(`/api/ai/chat/session/${sessionId}`);
                if (sessionResponse.data.code === 200) {
                    this.currentSession = sessionResponse.data.data;
                }
                
                // 获取消息历史
                const messagesResponse = await axios.get(`/api/ai/chat/session/${sessionId}/messages`);
                if (messagesResponse.data.code === 200) {
                    this.messages = messagesResponse.data.data || [];
                    this.$nextTick(() => {
                        this.scrollToBottom();
                    });
                }
            } catch (error) {
                console.error('选择会话失败:', error);
                this.$message.error('加载会话失败');
            }
        },
        
        // 发送快速消息
        async sendQuickMessage(message) {
            this.inputMessage = message;
            await this.sendMessage();
        },

        // 发送消息
        async sendMessage() {
            if (!this.inputMessage.trim()) {
                return;
            }

            if (!this.currentSessionId) {
                await this.createNewSession();
                if (!this.currentSessionId) {
                    return;
                }
            }

            const message = this.inputMessage.trim();
            this.inputMessage = '';

            // 添加用户消息到界面
            const userMessage = {
                messageId: 'temp-' + Date.now(),
                role: 'user',
                content: message,
                createTime: new Date()
            };
            this.messages.push(userMessage);
            this.scrollToBottom();

            if (this.streamMode) {
                await this.sendStreamMessage(message);
            } else {
                await this.sendNormalMessage(message);
            }
        },
        
        // 发送普通消息
        async sendNormalMessage(message) {
            try {
                this.isLoading = true;
                this.isTyping = true;
                
                const [modelType, modelName] = this.selectedModel.split(':');
                const response = await axios.post('/api/ai/chat/send', {
                    sessionId: this.currentSessionId,
                    content: message,
                    modelType,
                    modelName,
                    stream: false
                });
                
                if (response.data.code === 200) {
                    const aiMessage = {
                        messageId: response.data.data.messageId,
                        role: 'assistant',
                        content: response.data.data.content,
                        createTime: response.data.data.createTime,
                        tokenUsage: response.data.data.tokenUsage
                    };
                    this.messages.push(aiMessage);
                    this.scrollToBottom();
                    
                    // 更新会话信息
                    if (response.data.data.sessionInfo) {
                        this.updateSessionInfo(response.data.data.sessionInfo);
                    }
                } else {
                    this.$message.error(response.data.message);
                }
            } catch (error) {
                console.error('发送消息失败:', error);
                this.$message.error('发送消息失败');
            } finally {
                this.isLoading = false;
                this.isTyping = false;
            }
        },
        
        // 发送流式消息
        async sendStreamMessage(message) {
            try {
                this.isLoading = true;
                this.isTyping = true;

                const [modelType, modelName] = this.selectedModel.split(':');

                // 使用fetch进行流式请求
                const response = await fetch(`${getApiBaseUrl()}/ai/chat/stream`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        sessionId: this.currentSessionId,
                        content: message,
                        modelType,
                        modelName,
                        stream: true
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                let aiMessage = {
                    messageId: 'stream-' + Date.now(),
                    role: 'assistant',
                    content: '',
                    createTime: new Date()
                };
                this.messages.push(aiMessage);
                this.scrollToBottom();

                // 处理SSE流式响应
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';

                const processStream = async () => {
                    try {
                        while (true) {
                            const { done, value } = await reader.read();
                            if (done) {
                                console.log('流式响应结束');
                                this.isTyping = false;
                                break;
                            }

                            buffer += decoder.decode(value, { stream: true });
                            const lines = buffer.split('\n');
                            buffer = lines.pop() || ''; // 保留最后一个不完整的行

                            for (const line of lines) {
                                if (line.trim() === '') continue; // 跳过空行

                                if (line.startsWith('data: ')) {
                                    try {
                                        const jsonStr = line.substring(6).trim();
                                        if (jsonStr === '' || jsonStr === '[DONE]') continue;

                                        const data = JSON.parse(jsonStr);
                                        console.log('收到流式数据:', data);
                                        console.log('当前消息列表长度:', this.messages.length);
                                        console.log('AI消息ID:', aiMessage.messageId);

                                        if (data.status === 'started') {
                                            console.log('流式响应开始');
                                        } else if (data.status === 'streaming') {
                                            // 直接更新AI消息内容
                                            aiMessage.content = data.content || '';
                                            // 强制Vue更新视图
                                            this.$forceUpdate();
                                            this.scrollToBottom();
                                        } else if (data.status === 'completed') {
                                            // 完成响应
                                            aiMessage.messageId = data.messageId;
                                            aiMessage.content = data.content || '';
                                            aiMessage.createTime = data.createTime;
                                            aiMessage.tokenUsage = data.tokenUsage;
                                            this.isTyping = false;
                                            this.$forceUpdate();
                                            this.scrollToBottom();

                                            if (data.sessionInfo) {
                                                this.updateSessionInfo(data.sessionInfo);
                                            }
                                            return; // 完成后退出
                                        } else if (data.status === 'error') {
                                            this.$message.error(data.errorMessage || '流式响应出错');
                                            // 移除错误的消息
                                            const index = this.messages.findIndex(m => m.messageId === aiMessage.messageId);
                                            if (index > -1) {
                                                this.messages.splice(index, 1);
                                            }
                                            this.isTyping = false;
                                            return; // 出错后退出
                                        }
                                    } catch (parseError) {
                                        console.error('解析流式数据失败:', parseError, 'line:', line);
                                    }
                                } else if (line.startsWith('event: ')) {
                                    // 处理事件类型
                                    const eventType = line.substring(7).trim();
                                    console.log('收到事件类型:', eventType);

                                    if (eventType === 'complete') {
                                        // 下一行应该是data
                                        continue;
                                    } else if (eventType === 'error') {
                                        // 下一行应该是错误data
                                        continue;
                                    }
                                }
                            }
                        }
                    } catch (streamError) {
                        console.error('读取流数据失败:', streamError);
                        this.$message.error('读取流数据失败');
                        this.isTyping = false;
                    }
                };

                // 开始处理流
                await processStream();

            } catch (error) {
                console.error('发送流式消息失败:', error);
                this.$message.error('发送流式消息失败: ' + error.message);
                this.isTyping = false;
            } finally {
                this.isLoading = false;
            }
        },
        
        // 处理文件上传
        async handleFileUpload(file) {
            try {
                const formData = new FormData();
                formData.append('file', file);
                if (this.currentSessionId) {
                    formData.append('sessionId', this.currentSessionId);
                }
                formData.append('question', '请分析这个文件的内容');
                
                this.isLoading = true;
                const response = await axios.post('/api/ai/chat/upload', formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                });
                
                if (response.data.code === 200) {
                    // 添加文件上传消息
                    const fileMessage = {
                        messageId: 'file-' + Date.now(),
                        role: 'user',
                        content: `📎 已上传文件: ${file.name}`,
                        createTime: new Date()
                    };
                    this.messages.push(fileMessage);
                    
                    // 添加AI回复
                    const aiMessage = {
                        messageId: response.data.data.messageId,
                        role: 'assistant',
                        content: response.data.data.content,
                        createTime: response.data.data.createTime,
                        tokenUsage: response.data.data.tokenUsage
                    };
                    this.messages.push(aiMessage);
                    
                    this.scrollToBottom();
                    this.$message.success('文件上传并分析成功');
                } else {
                    this.$message.error(response.data.message);
                }
            } catch (error) {
                console.error('文件上传失败:', error);
                this.$message.error('文件上传失败');
            } finally {
                this.isLoading = false;
            }
            
            return false; // 阻止默认上传行为
        },
        
        // 模型切换
        onModelChange() {
            console.log('模型切换为:', this.selectedModel);
        },
        
        // 更新会话信息
        updateSessionInfo(sessionInfo) {
            if (this.currentSession) {
                Object.assign(this.currentSession, sessionInfo);
            }
            
            // 更新会话列表中的信息
            const sessionIndex = this.sessions.findIndex(s => s.sessionId === this.currentSessionId);
            if (sessionIndex !== -1) {
                Object.assign(this.sessions[sessionIndex], sessionInfo);
            }
        },
        
        // 滚动到底部
        scrollToBottom() {
            this.$nextTick(() => {
                const container = this.$refs.messagesContainer;
                if (container) {
                    container.scrollTop = container.scrollHeight;
                }
            });
        },
        
        // 格式化消息内容
        formatMessage(content) {
            if (!content) return '';

            // 使用marked库渲染Markdown
            if (typeof marked !== 'undefined') {
                try {
                    // 配置marked选项
                    marked.setOptions({
                        highlight: function(code, lang) {
                            // 如果有highlight.js，使用它进行代码高亮
                            if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
                                try {
                                    return hljs.highlight(code, { language: lang }).value;
                                } catch (err) {
                                    console.warn('代码高亮失败:', err);
                                }
                            }
                            return code;
                        },
                        breaks: true,
                        gfm: true
                    });

                    return marked.parse(content);
                } catch (err) {
                    console.warn('Markdown解析失败:', err);
                    return this.formatPlainText(content);
                }
            }

            return this.formatPlainText(content);
        },

        // 格式化纯文本
        formatPlainText(content) {
            return content
                .replace(/\n/g, '<br>')
                .replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code class="language-$1">$2</code></pre>')
                .replace(/`([^`]+)`/g, '<code>$1</code>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>');
        },
        
        // 格式化时间
        formatTime(time) {
            if (!time) return '';
            const date = new Date(time);
            const now = new Date();
            const diff = now - date;

            if (diff < 60000) { // 1分钟内
                return '刚刚';
            } else if (diff < 3600000) { // 1小时内
                return Math.floor(diff / 60000) + '分钟前';
            } else if (diff < 86400000) { // 1天内
                return Math.floor(diff / 3600000) + '小时前';
            } else {
                return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
            }
        },

        // 复制消息内容
        async copyMessage(content) {
            try {
                // 移除HTML标签，只保留纯文本
                const textContent = content.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ');

                if (navigator.clipboard && window.isSecureContext) {
                    await navigator.clipboard.writeText(textContent);
                } else {
                    // 降级方案
                    const textArea = document.createElement('textarea');
                    textArea.value = textContent;
                    textArea.style.position = 'fixed';
                    textArea.style.left = '-999999px';
                    textArea.style.top = '-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    document.execCommand('copy');
                    textArea.remove();
                }

                this.$message.success('消息已复制到剪贴板');
            } catch (err) {
                console.error('复制失败:', err);
                this.$message.error('复制失败，请手动复制');
            }
        },

        // 重新生成消息
        async regenerateMessage(message) {
            try {
                // 找到这条消息的前一条用户消息
                const messageIndex = this.messages.findIndex(m => m.messageId === message.messageId);
                if (messageIndex <= 0) {
                    this.$message.error('无法找到对应的用户消息');
                    return;
                }

                // 找到最近的用户消息
                let userMessage = null;
                for (let i = messageIndex - 1; i >= 0; i--) {
                    if (this.messages[i].role === 'user') {
                        userMessage = this.messages[i];
                        break;
                    }
                }

                if (!userMessage) {
                    this.$message.error('无法找到对应的用户消息');
                    return;
                }

                // 删除当前AI消息
                this.messages.splice(messageIndex, 1);

                // 重新发送用户消息
                if (this.streamMode) {
                    await this.sendStreamMessage(userMessage.content);
                } else {
                    await this.sendNormalMessage(userMessage.content);
                }

            } catch (err) {
                console.error('重新生成失败:', err);
                this.$message.error('重新生成失败');
            }
        },

        // 删除消息
        deleteMessage(message) {
            this.$confirm('确定要删除这条消息吗？', '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const index = this.messages.findIndex(m => m.messageId === message.messageId);
                if (index !== -1) {
                    this.messages.splice(index, 1);
                    this.$message.success('消息已删除');
                }
            }).catch(() => {
                // 用户取消删除
            });
        },

        // 清空输入
        clearInput() {
            this.inputMessage = '';
            this.uploadedFiles = [];
        },

        // 添加换行
        addNewLine() {
            this.inputMessage += '\n';
        },

        // 显示Markdown帮助
        showMarkdownHelp() {
            this.$alert(`
                <div style="text-align: left; line-height: 1.6;">
                    <h4>Markdown语法帮助</h4>
                    <p><strong>粗体：</strong> **文字** 或 __文字__</p>
                    <p><strong>斜体：</strong> *文字* 或 _文字_</p>
                    <p><strong>代码：</strong> \`代码\`</p>
                    <p><strong>代码块：</strong></p>
                    <pre>\`\`\`语言名\n代码内容\n\`\`\`</pre>
                    <p><strong>链接：</strong> [链接文字](URL)</p>
                    <p><strong>列表：</strong> - 项目 或 1. 项目</p>
                    <p><strong>引用：</strong> > 引用内容</p>
                    <p><strong>标题：</strong> # 一级标题 ## 二级标题</p>
                </div>
            `, 'Markdown语法帮助', {
                dangerouslyUseHTMLString: true,
                confirmButtonText: '知道了'
            });
        },

        // 获取文件图标
        getFileIcon(fileName) {
            const ext = fileName.split('.').pop().toLowerCase();
            const iconMap = {
                'pdf': 'fas fa-file-pdf',
                'doc': 'fas fa-file-word',
                'docx': 'fas fa-file-word',
                'xls': 'fas fa-file-excel',
                'xlsx': 'fas fa-file-excel',
                'ppt': 'fas fa-file-powerpoint',
                'pptx': 'fas fa-file-powerpoint',
                'txt': 'fas fa-file-alt',
                'md': 'fab fa-markdown',
                'jpg': 'fas fa-file-image',
                'jpeg': 'fas fa-file-image',
                'png': 'fas fa-file-image',
                'gif': 'fas fa-file-image'
            };
            return iconMap[ext] || 'fas fa-file';
        },

        // 格式化文件大小
        formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        },

        // 移除文件
        removeFile(index) {
            this.uploadedFiles.splice(index, 1);
        },

        // 检测用户认证状态
        async checkAuthStatus() {
            try {
                const response = await axios.get('/api/ai/chat/auth/status');
                if (response.data.code === 200) {
                    this.authStatus = response.data.data;
                    console.log('用户认证状态:', this.authStatus);

                    // 显示用户状态信息
                    if (this.authStatus.isAuthenticated) {
                        this.$message.success(`欢迎回来，${this.authStatus.userName}！您可以无限制使用AI聊天功能。`);
                    } else {
                        this.$message.info(`您当前以匿名用户身份使用，每日限制${this.authStatus.dailyLimit}次对话。`);
                    }
                }
            } catch (error) {
                console.error('获取认证状态失败:', error);
                // 使用默认的匿名用户状态
                this.authStatus = {
                    isAuthenticated: false,
                    userId: 'anonymousUser',
                    userName: '匿名用户',
                    userType: '匿名用户',
                    hasUsageLimit: true,
                    dailyLimit: 20
                };
            }
        },

        // 加载使用统计（仅匿名用户）
        async loadUsageStats() {
            if (this.authStatus.isAuthenticated) {
                return; // 登录用户不需要加载使用统计
            }

            try {
                const response = await axios.get('/api/ai/chat/usage/user?period=today&userId=anonymousUser');
                if (response.data.code === 200) {
                    const stats = response.data.data;
                    this.usageStats.requestCount = stats.requestCount || 0;
                    this.usageStats.remainingCount = Math.max(0, this.authStatus.dailyLimit - this.usageStats.requestCount);

                    console.log('匿名用户使用统计:', this.usageStats);

                    // 如果接近限制，显示警告
                    if (this.usageStats.remainingCount <= 5 && this.usageStats.remainingCount > 0) {
                        this.$message.warning(`您今日还可以使用${this.usageStats.remainingCount}次AI对话，建议登录获得无限制使用权限。`);
                    } else if (this.usageStats.remainingCount === 0) {
                        this.$message.error('您今日的使用次数已达上限，请登录后继续使用。');
                    }
                }
            } catch (error) {
                console.error('获取使用统计失败:', error);
            }
        },

        // 刷新认证状态（用于定时检测）
        async refreshAuthStatus() {
            const oldStatus = this.authStatus.isAuthenticated;
            await this.checkAuthStatus();

            // 如果认证状态发生变化
            if (oldStatus !== this.authStatus.isAuthenticated) {
                if (this.authStatus.isAuthenticated) {
                    this.$message.success('检测到您已登录，现在可以无限制使用AI聊天功能！');
                    // 清除使用统计
                    this.usageStats = { requestCount: 0, remainingCount: -1 };
                } else {
                    this.$message.info('您已退出登录，切换为匿名用户模式。');
                    // 重新加载使用统计
                    await this.loadUsageStats();
                }

                // 重新加载会话列表
                await this.loadSessions();
            }
        }
    }
});
