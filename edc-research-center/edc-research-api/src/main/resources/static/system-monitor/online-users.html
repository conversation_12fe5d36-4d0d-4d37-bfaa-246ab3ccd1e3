<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线用户监控</title>
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
            background-color: #f5f7fa;
        }
        .page-header {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 20px 0;
            margin-bottom: 20px;
        }
        .card {
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border: none;
        }
        .table th {
            font-weight: 600;
            color: #2c3e50;
            border-top: none;
        }
        .search-form {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .status-badge {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }
        .status-online { background-color: #e8f5e9; color: #2e7d32; }
        .status-idle { background-color: #fff8e1; color: #f57f17; }
        .status-offline { background-color: #ffebee; color: #c62828; }
        .token-status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        .token-info {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        .logout-btn {
            width: 100%;
            padding: 8px;
            font-size: 12px;
        }
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 12px;
        }
    </style>
</head>
<body>


    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col">
                    <h1 class="h3 mb-0">
                        <i class="fa fa-users text-success"></i> 在线用户监控
                    </h1>
                    <p class="text-muted mb-0">实时监控在线用户状态和活动情况</p>
                </div>
                <div class="col-auto d-flex align-items-center">
                    <!-- Token状态显示 -->
                    <div id="tokenStatus" class="mr-3">
                        <div class="d-flex align-items-center bg-light rounded px-3 py-2">
                            <div class="mr-2">
                                <span id="token-status" class="badge badge-success">Token有效</span>
                            </div>
                            <div class="mr-2">
                                <small class="text-muted">剩余: <span id="token-remaining-time">--</span></small>
                            </div>
                            <button class="btn btn-sm btn-outline-secondary" onclick="logout()">
                                <i class="fa fa-sign-out"></i> 退出
                            </button>
                        </div>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary" onclick="navigateToPage('index.html')">
                            <i class="fa fa-home"></i> 首页
                        </button>
                        <button class="btn btn-outline-primary" onclick="navigateToPage('access-logs.html')">
                            <i class="fa fa-list-alt"></i> 访问日志
                        </button>
                        <button class="btn btn-outline-primary" onclick="navigateToPage('exception-logs.html')">
                            <i class="fa fa-exclamation-triangle"></i> 异常日志
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- 统计概览 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-success" id="totalOnline">--</h3>
                        <p class="text-muted mb-0">在线用户</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-warning" id="totalIdle">--</h3>
                        <p class="text-muted mb-0">空闲用户</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-primary" id="todayLogins">--</h3>
                        <p class="text-muted mb-0">今日登录</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-info" id="avgSessionTime">--</h3>
                        <p class="text-muted mb-0">平均会话时长</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索表单 -->
        <div class="search-form">
            <form id="searchForm">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>用户ID</label>
                            <input type="text" class="form-control" id="userId" placeholder="输入用户ID">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>用户名</label>
                            <input type="text" class="form-control" id="username" placeholder="输入用户名">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>状态</label>
                            <select class="form-control" id="status">
                                <option value="">全部</option>
                                <option value="online">在线</option>
                                <option value="idle">空闲</option>
                                <option value="offline">离线</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>IP地址</label>
                            <input type="text" class="form-control" id="ipAddress" placeholder="输入IP地址">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-search"></i> 搜索
                                </button>
                                <button type="button" class="btn btn-outline-secondary ml-2" onclick="resetSearch()">
                                    <i class="fa fa-refresh"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- 数据表格 -->
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-0">在线用户列表</h5>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshData()">
                            <i class="fa fa-refresh"></i> 刷新
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="exportData()">
                            <i class="fa fa-download"></i> 导出
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="cleanOfflineUsers()">
                            <i class="fa fa-trash"></i> 清理离线
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="alertContainer"></div>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="thead-light">
                            <tr>
                                <th>用户</th>
                                <th>状态</th>
                                <th>IP地址</th>
                                <th>登录时间</th>
                                <th>最后活动</th>
                                <th>会话时长</th>
                                <th>浏览器</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="dataTableBody">
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="sr-only">加载中...</span>
                                    </div>
                                    <div class="mt-2">正在加载数据...</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div class="row mt-3">
            <div class="col-md-6">
                <div class="d-flex align-items-center">
                    <span class="text-muted">每页显示</span>
                    <select class="form-control form-control-sm mx-2" style="width: auto;" id="pageSize" onchange="changePageSize()">
                        <option value="10">10</option>
                        <option value="20" selected>20</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                    <span class="text-muted">条记录</span>
                </div>
            </div>
            <div class="col-md-6">
                <nav>
                    <ul class="pagination justify-content-end mb-0" id="pagination">
                        <!-- 分页按钮将通过JavaScript生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.bundle.min.js"></script>
    <script src="common.js"></script>
    <script>
        let currentPage = 1;
        let currentPageSize = 20;
        let totalPages = 0;
        let totalRecords = 0;

        $(document).ready(function() {
            // 检查登录状态
            if (!checkLoginStatus()) {
                return;
            }

            // 初始化Token状态显示
            updateTokenStatus();
            // 每秒更新Token状态
            setInterval(updateTokenStatus, 1000);

            // 立即加载数据
            setTimeout(() => {
                loadOnlineUsers();
                loadStatistics();
            }, 100); // 延迟100ms确保页面完全加载

            // 定时刷新数据
            setInterval(() => {
                loadOnlineUsers();
                loadStatistics();
            }, 30000); // 30秒刷新一次

            // 搜索表单提交
            $('#searchForm').on('submit', function(e) {
                e.preventDefault();
                currentPage = 1;
                loadOnlineUsers();
            });
        });

        // 加载在线用户数据
        async function loadOnlineUsers() {
            try {
                showLoading();
                
                const params = {
                    pageNum: currentPage,
                    pageSize: currentPageSize,
                    userId: $('#userId').val().trim(),
                    username: $('#username').val().trim(),
                    status: $('#status').val(),
                    ipAddress: $('#ipAddress').val().trim()
                };

                const url = `${getApiBaseUrl()}/monitor/online-users/list`;
                const response = await makeApiRequest(url, {
                    method: 'GET',
                    data: params
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    displayOnlineUsers(result.data);
                    showAlert('alertContainer', '数据加载成功', 'success');
                } else {
                    showAlert('alertContainer', result.message || '加载数据失败', 'danger');
                }
            } catch (error) {
                console.error('加载在线用户失败:', error);

                // 检查是否是token过期错误
                if (error.message.includes('访问令牌无效') || error.message.includes('已过期')) {
                    console.log('Token已过期，即将跳转到登录页面');
                    setTimeout(() => {
                        logout();
                    }, 1000);
                    return;
                }

                showAlert('alertContainer', '网络请求失败: ' + error.message, 'danger');
            }
        }

        // 加载统计数据
        async function loadStatistics() {
            try {
                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/online-users/statistics`);
                const result = await response.json();
                
                if (result.code === 200 && result.data) {
                    const data = result.data;
                    $('#totalOnline').text(data.totalOnline || 0);
                    $('#totalIdle').text(data.totalIdle || 0);
                    $('#todayLogins').text(data.todayLogins || 0);
                    $('#avgSessionTime').text(formatDuration(data.avgSessionTime) || '--');
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);

                // 检查是否是token过期错误
                if (error.message.includes('访问令牌无效') || error.message.includes('已过期')) {
                    console.log('Token已过期，即将跳转到登录页面');
                    setTimeout(() => {
                        logout();
                    }, 1000);
                    return;
                }
            }
        }

        // 显示在线用户数据
        function displayOnlineUsers(data) {
            const tbody = $('#dataTableBody');
            tbody.empty();

            if (!data || !data.list || data.list.length === 0) {
                tbody.html(`
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <i class="fa fa-users text-muted" style="font-size: 48px;"></i>
                            <div class="mt-2 text-muted">暂无在线用户</div>
                        </td>
                    </tr>
                `);
                return;
            }

            totalPages = data.totalPage || 0;
            totalRecords = data.total || 0;

            data.list.forEach(user => {
                const row = `
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="user-avatar mr-2">${(user.username || user.userId || 'U').charAt(0).toUpperCase()}</div>
                                <div>
                                    <div class="font-weight-bold">${user.username || user.userId || '-'}</div>
                                    <small class="text-muted">ID: ${user.userId || '-'}</small>
                                </div>
                            </div>
                        </td>
                        <td><span class="status-badge status-${user.status || 'offline'}">${getStatusText(user.status)}</span></td>
                        <td>${user.ipAddress || '-'}</td>
                        <td>${formatDateTime(user.loginTime)}</td>
                        <td>${formatDateTime(user.lastActiveTime)}</td>
                        <td>${formatDuration(user.sessionDuration) || '-'}</td>
                        <td title="${user.userAgent || '-'}">${getBrowserInfo(user.userAgent)}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewUserDetail('${user.userId}')">
                                <i class="fa fa-eye"></i> 详情
                            </button>
                            <button class="btn btn-sm btn-outline-warning" onclick="forceLogout('${user.userId}')">
                                <i class="fa fa-sign-out"></i> 强制下线
                            </button>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });

            updatePagination();
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'online': '在线',
                'idle': '空闲',
                'offline': '离线'
            };
            return statusMap[status] || '未知';
        }

        // 获取浏览器信息
        function getBrowserInfo(userAgent) {
            if (!userAgent) return '-';
            
            if (userAgent.includes('Chrome')) return 'Chrome';
            if (userAgent.includes('Firefox')) return 'Firefox';
            if (userAgent.includes('Safari')) return 'Safari';
            if (userAgent.includes('Edge')) return 'Edge';
            if (userAgent.includes('Opera')) return 'Opera';
            
            return '其他';
        }

        // 格式化持续时间
        function formatDuration(milliseconds) {
            if (!milliseconds) return '--';
            
            const seconds = Math.floor(milliseconds / 1000);
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            
            if (hours > 0) {
                return `${hours}小时${minutes}分钟`;
            } else if (minutes > 0) {
                return `${minutes}分钟${secs}秒`;
            } else {
                return `${secs}秒`;
            }
        }

        // 显示加载状态
        function showLoading() {
            $('#dataTableBody').html(`
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <div class="mt-2">正在加载数据...</div>
                    </td>
                </tr>
            `);
        }

        // 更新分页
        function updatePagination() {
            const pagination = $('#pagination');
            pagination.empty();

            if (totalPages <= 1) return;

            // 上一页
            const prevDisabled = currentPage <= 1 ? 'disabled' : '';
            pagination.append(`
                <li class="page-item ${prevDisabled}">
                    <a class="page-link" href="javascript:void(0)" onclick="changePage(${currentPage - 1})">上一页</a>
                </li>
            `);

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                const active = i === currentPage ? 'active' : '';
                pagination.append(`
                    <li class="page-item ${active}">
                        <a class="page-link" href="javascript:void(0)" onclick="changePage(${i})">${i}</a>
                    </li>
                `);
            }

            // 下一页
            const nextDisabled = currentPage >= totalPages ? 'disabled' : '';
            pagination.append(`
                <li class="page-item ${nextDisabled}">
                    <a class="page-link" href="javascript:void(0)" onclick="changePage(${currentPage + 1})">下一页</a>
                </li>
            `);
        }

        // 切换页码
        function changePage(page) {
            if (page < 1 || page > totalPages || page === currentPage) return;
            currentPage = page;
            loadOnlineUsers();
        }

        // 改变页面大小
        function changePageSize() {
            currentPageSize = parseInt($('#pageSize').val());
            currentPage = 1;
            loadOnlineUsers();
        }

        // 重置搜索
        function resetSearch() {
            $('#searchForm')[0].reset();
            currentPage = 1;
            loadOnlineUsers();
        }

        // 刷新数据
        function refreshData() {
            loadOnlineUsers();
            loadStatistics();
        }

        // 查看用户详情
        async function viewUserDetail(sessionId) {
            try {
                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/online-users/detail/${sessionId}`);
                const result = await response.json();

                if (result.code === 200) {
                    showUserDetailModal(result.data);
                } else {
                    showAlert('alertContainer', result.message || '获取用户详情失败', 'danger');
                }
            } catch (error) {
                // 检查是否是token过期错误
                if (error.message.includes('访问令牌无效') || error.message.includes('已过期')) {
                    console.log('Token已过期，即将跳转到登录页面');
                    setTimeout(() => {
                        logout();
                    }, 1000);
                    return;
                }

                showAlert('alertContainer', '获取用户详情失败: ' + error.message, 'danger');
            }
        }

        // 显示用户详情模态框
        function showUserDetailModal(user) {
            const modalHtml = `
                <div class="modal fade" id="userDetailModal" tabindex="-1" role="dialog">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">在线用户详情</h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>用户ID:</strong> ${user.userId || '-'}</p>
                                        <p><strong>用户名:</strong> ${user.userName || '-'}</p>
                                        <p><strong>真实姓名:</strong> ${user.realName || '-'}</p>
                                        <p><strong>登录IP:</strong> ${user.loginIp || '-'}</p>
                                        <p><strong>登录位置:</strong> ${user.loginLocation || '-'}</p>
                                        <p><strong>状态:</strong> <span class="status-badge status-${user.status || 'offline'}">${getStatusText(user.status)}</span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>登录时间:</strong> ${formatDateTime(user.loginTime)}</p>
                                        <p><strong>最后活动:</strong> ${formatDateTime(user.lastAccessTime)}</p>
                                        <p><strong>最后登录:</strong> ${formatDateTime(user.lastLoginTime)}</p>
                                        <p><strong>在线时长:</strong> ${formatDuration(user.onlineDuration) || '-'}</p>
                                        <p><strong>访问次数:</strong> ${user.accessCount || 0}</p>
                                        <p><strong>登录成功次数:</strong> ${user.loginSuccessCount || 0}</p>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>浏览器:</strong> ${user.browser || '-'}</p>
                                        <p><strong>操作系统:</strong> ${user.os || '-'}</p>
                                        <p><strong>设备类型:</strong> ${user.deviceType || '-'}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>登录类型:</strong> ${user.loginType || '-'}</p>
                                        <p><strong>手机号:</strong> ${user.phoneNumber || '-'}</p>
                                        <p><strong>会话ID:</strong> ${user.sessionId || '-'}</p>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <p><strong>User Agent:</strong></p>
                                        <div class="bg-light p-2 rounded"><small>${user.userAgent || '-'}</small></div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-danger" onclick="forceLogout('${user.sessionId}')">强制下线</button>
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的模态框
            $('#userDetailModal').remove();
            // 添加新的模态框
            $('body').append(modalHtml);
            // 显示模态框
            $('#userDetailModal').modal('show');
        }

        // 更新Token状态显示
        function updateTokenStatus() {
            const statusElement = $('#token-status');
            const timeElement = $('#token-remaining-time');
            const tokenStatusDiv = $('#tokenStatus');

            if (statusElement.length) {
                const isExpired = TokenManager.isTokenExpired();
                statusElement.text(isExpired ? '已过期' : 'Token有效');
                statusElement.removeClass('badge-success badge-danger').addClass(isExpired ? 'badge-danger' : 'badge-success');
            }

            if (timeElement.length) {
                timeElement.text(TokenManager.formatRemainingTime());
            }

            // 显示token状态区域
            if (tokenStatusDiv.length) {
                tokenStatusDiv.show();
            }
        }

        // 强制用户下线
        async function forceLogout(userId) {
            if (!confirm('确定要强制该用户下线吗？')) return;
            
            try {
                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/online-users/force-logout`, {
                    method: 'POST',
                    body: JSON.stringify({ userId: userId })
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    showAlert('alertContainer', '用户已强制下线', 'success');
                    loadOnlineUsers();
                } else {
                    showAlert('alertContainer', result.message || '操作失败', 'danger');
                }
            } catch (error) {
                console.error('强制下线失败:', error);

                // 检查是否是token过期错误
                if (error.message.includes('访问令牌无效') || error.message.includes('已过期')) {
                    console.log('Token已过期，即将跳转到登录页面');
                    setTimeout(() => {
                        logout();
                    }, 1000);
                    return;
                }

                showAlert('alertContainer', '网络请求失败: ' + error.message, 'danger');
            }
        }

        // 清理离线用户
        async function cleanOfflineUsers() {
            if (!confirm('确定要清理所有离线用户记录吗？')) return;
            
            try {
                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/online-users/clean-offline`, {
                    method: 'POST'
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    showAlert('alertContainer', `已清理 ${result.data || 0} 个离线用户记录`, 'success');
                    loadOnlineUsers();
                    loadStatistics();
                } else {
                    showAlert('alertContainer', result.message || '清理失败', 'danger');
                }
            } catch (error) {
                console.error('清理离线用户失败:', error);

                // 检查是否是token过期错误
                if (error.message.includes('访问令牌无效') || error.message.includes('已过期')) {
                    console.log('Token已过期，即将跳转到登录页面');
                    setTimeout(() => {
                        logout();
                    }, 1000);
                    return;
                }

                showAlert('alertContainer', '网络请求失败: ' + error.message, 'danger');
            }
        }

        // 导出数据
        function exportData() {
            // TODO: 实现数据导出功能
            alert('数据导出功能待实现');
        }
    </script>
</body>
</html>
