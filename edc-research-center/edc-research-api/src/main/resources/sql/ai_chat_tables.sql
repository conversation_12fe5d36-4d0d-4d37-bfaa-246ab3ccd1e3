-- AI聊天系统相关表结构

-- 1. AI聊天会话表
CREATE TABLE IF NOT EXISTS `ai_chat_session` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `user_name` varchar(100) DEFAULT NULL COMMENT '用户名',
  `title` varchar(200) DEFAULT NULL COMMENT '会话标题',
  `model_type` varchar(50) NOT NULL COMMENT '使用的模型类型(qwen,kimi,chatgpt等)',
  `model_name` varchar(100) NOT NULL COMMENT '具体模型名称',
  `status` tinyint(1) DEFAULT 1 COMMENT '会话状态(0-已结束,1-进行中)',
  `total_tokens` bigint(20) DEFAULT 0 COMMENT '总消耗Token数',
  `total_cost` decimal(10,4) DEFAULT 0.0000 COMMENT '总消费金额',
  `message_count` int(11) DEFAULT 0 COMMENT '消息数量',
  `last_message_time` datetime DEFAULT NULL COMMENT '最后消息时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_model_type` (`model_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI聊天会话表';

-- 2. AI聊天消息表
CREATE TABLE IF NOT EXISTS `ai_chat_message` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `message_id` varchar(64) NOT NULL COMMENT '消息ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `role` varchar(20) NOT NULL COMMENT '角色(user,assistant,system)',
  `content` longtext NOT NULL COMMENT '消息内容',
  `content_type` varchar(20) DEFAULT 'text' COMMENT '内容类型(text,image,file)',
  `file_info` json DEFAULT NULL COMMENT '文件信息(JSON格式)',
  `tokens` int(11) DEFAULT 0 COMMENT '消息Token数',
  `cost` decimal(8,4) DEFAULT 0.0000 COMMENT '消息消费金额',
  `response_time` int(11) DEFAULT 0 COMMENT '响应时间(毫秒)',
  `is_stream` tinyint(1) DEFAULT 0 COMMENT '是否流式响应',
  `error_info` text DEFAULT NULL COMMENT '错误信息',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_message_id` (`message_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI聊天消息表';

-- 3. AI模型配置表
CREATE TABLE IF NOT EXISTS `ai_model_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `model_type` varchar(50) NOT NULL COMMENT '模型类型(qwen,kimi,chatgpt等)',
  `model_name` varchar(100) NOT NULL COMMENT '模型名称',
  `api_key` varchar(500) NOT NULL COMMENT 'API密钥',
  `api_url` varchar(500) DEFAULT NULL COMMENT 'API地址',
  `max_tokens` int(11) DEFAULT 4000 COMMENT '最大Token数',
  `temperature` decimal(3,2) DEFAULT 0.70 COMMENT '温度参数',
  `top_p` decimal(3,2) DEFAULT 0.90 COMMENT 'Top-P参数',
  `frequency_penalty` decimal(3,2) DEFAULT 0.00 COMMENT '频率惩罚',
  `presence_penalty` decimal(3,2) DEFAULT 0.00 COMMENT '存在惩罚',
  `system_prompt` text DEFAULT NULL COMMENT '系统提示词',
  `token_price_input` decimal(8,6) DEFAULT 0.000000 COMMENT '输入Token单价',
  `token_price_output` decimal(8,6) DEFAULT 0.000000 COMMENT '输出Token单价',
  `rate_limit_rpm` int(11) DEFAULT 60 COMMENT '每分钟请求限制',
  `rate_limit_tpm` int(11) DEFAULT 60000 COMMENT '每分钟Token限制',
  `enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `priority` int(11) DEFAULT 0 COMMENT '优先级(数字越大优先级越高)',
  `description` varchar(500) DEFAULT NULL COMMENT '模型描述',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_model_type_name` (`model_type`, `model_name`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI模型配置表';

-- 4. AI Token使用统计表
CREATE TABLE IF NOT EXISTS `ai_token_usage` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `user_name` varchar(100) DEFAULT NULL COMMENT '用户名',
  `model_type` varchar(50) NOT NULL COMMENT '模型类型',
  `model_name` varchar(100) NOT NULL COMMENT '模型名称',
  `session_id` varchar(64) DEFAULT NULL COMMENT '会话ID',
  `message_id` varchar(64) DEFAULT NULL COMMENT '消息ID',
  `input_tokens` int(11) DEFAULT 0 COMMENT '输入Token数',
  `output_tokens` int(11) DEFAULT 0 COMMENT '输出Token数',
  `total_tokens` int(11) DEFAULT 0 COMMENT '总Token数',
  `input_cost` decimal(8,4) DEFAULT 0.0000 COMMENT '输入成本',
  `output_cost` decimal(8,4) DEFAULT 0.0000 COMMENT '输出成本',
  `total_cost` decimal(8,4) DEFAULT 0.0000 COMMENT '总成本',
  `usage_date` date NOT NULL COMMENT '使用日期',
  `usage_hour` tinyint(2) NOT NULL COMMENT '使用小时(0-23)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_model_type` (`model_type`),
  KEY `idx_usage_date` (`usage_date`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI Token使用统计表';

-- 5. AI文档解析记录表
CREATE TABLE IF NOT EXISTS `ai_document_parse` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `parse_id` varchar(64) NOT NULL COMMENT '解析ID',
  `session_id` varchar(64) DEFAULT NULL COMMENT '关联会话ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `user_name` varchar(100) DEFAULT NULL COMMENT '用户名',
  `file_name` varchar(500) NOT NULL COMMENT '文件名',
  `file_path` varchar(1000) NOT NULL COMMENT '文件路径',
  `file_size` bigint(20) DEFAULT 0 COMMENT '文件大小(字节)',
  `file_type` varchar(50) NOT NULL COMMENT '文件类型(image,excel,ppt,pdf等)',
  `mime_type` varchar(100) DEFAULT NULL COMMENT 'MIME类型',
  `parse_type` varchar(50) NOT NULL COMMENT '解析类型(ocr,table,slide等)',
  `parse_status` tinyint(1) DEFAULT 0 COMMENT '解析状态(0-处理中,1-成功,2-失败)',
  `parse_result` longtext DEFAULT NULL COMMENT '解析结果',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `tokens_used` int(11) DEFAULT 0 COMMENT '使用Token数',
  `parse_cost` decimal(8,4) DEFAULT 0.0000 COMMENT '解析成本',
  `parse_time` int(11) DEFAULT 0 COMMENT '解析耗时(毫秒)',
  `model_type` varchar(50) DEFAULT NULL COMMENT '使用的模型类型',
  `model_name` varchar(100) DEFAULT NULL COMMENT '使用的模型名称',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_parse_id` (`parse_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_parse_status` (`parse_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI文档解析记录表';

-- 插入默认模型配置
INSERT INTO `ai_model_config` (`model_type`, `model_name`, `api_key`, `api_url`, `max_tokens`, `temperature`, `system_prompt`, `token_price_input`, `token_price_output`, `enabled`, `priority`, `description`) VALUES
('qwen', 'qwen-turbo', 'sk-6a023ee24768497c93f04f87990c370c', 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', 4000, 0.70, 'You are a helpful assistant.', 0.000002, 0.000006, 1, 100, '通义千问Turbo模型，响应速度快，适合日常对话'),
('qwen', 'qwen-plus', 'sk-6a023ee24768497c93f04f87990c370c', 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', 8000, 0.70, 'You are a helpful assistant.', 0.000004, 0.000012, 1, 90, '通义千问Plus模型，能力更强，适合复杂任务'),
('qwen', 'qwen-max', 'sk-6a023ee24768497c93f04f87990c370c', 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', 8000, 0.70, 'You are a helpful assistant.', 0.000020, 0.000060, 1, 80, '通义千问Max模型，最强能力，适合专业场景'),
('kimi', 'moonshot-v1-8k', '', 'https://api.moonshot.cn/v1/chat/completions', 8000, 0.70, 'You are a helpful assistant.', 0.000012, 0.000012, 0, 70, 'Kimi 8K上下文模型'),
('kimi', 'moonshot-v1-32k', '', 'https://api.moonshot.cn/v1/chat/completions', 32000, 0.70, 'You are a helpful assistant.', 0.000024, 0.000024, 0, 60, 'Kimi 32K上下文模型'),
('kimi', 'moonshot-v1-128k', '', 'https://api.moonshot.cn/v1/chat/completions', 128000, 0.70, 'You are a helpful assistant.', 0.000060, 0.000060, 0, 50, 'Kimi 128K上下文模型');
