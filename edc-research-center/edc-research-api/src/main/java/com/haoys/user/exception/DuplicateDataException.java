package com.haoys.user.exception;

/**
 * 重复数据异常
 *
 * <p>当数据重复时抛出此异常</p>
 * <p>例如：唯一约束冲突、业务唯一性验证失败等</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0
 */
public class DuplicateDataException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 数据类型
     */
    private final String dataType;

    /**
     * 重复字段
     */
    private final String duplicateField;

    /**
     * 重复值
     */
    private final Object duplicateValue;

    /**
     * 构造函数
     *
     * @param message 异常消息
     */
    public DuplicateDataException(String message) {
        super(message);
        this.dataType = null;
        this.duplicateField = null;
        this.duplicateValue = null;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param dataType 数据类型
     * @param duplicateField 重复字段
     */
    public DuplicateDataException(String message, String dataType, String duplicateField) {
        super(message);
        this.dataType = dataType;
        this.duplicateField = duplicateField;
        this.duplicateValue = null;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param dataType 数据类型
     * @param duplicateField 重复字段
     * @param duplicateValue 重复值
     */
    public DuplicateDataException(String message, String dataType, String duplicateField, Object duplicateValue) {
        super(message);
        this.dataType = dataType;
        this.duplicateField = duplicateField;
        this.duplicateValue = duplicateValue;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param cause 原因异常
     */
    public DuplicateDataException(String message, Throwable cause) {
        super(message, cause);
        this.dataType = null;
        this.duplicateField = null;
        this.duplicateValue = null;
    }

    /**
     * 获取数据类型
     *
     * @return 数据类型
     */
    public String getDataType() {
        return dataType;
    }

    /**
     * 获取重复字段
     *
     * @return 重复字段
     */
    public String getDuplicateField() {
        return duplicateField;
    }

    /**
     * 获取重复值
     *
     * @return 重复值
     */
    public Object getDuplicateValue() {
        return duplicateValue;
    }
}
