package com.haoys.user.project.controller;


import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.annotation.NoRepeatSubmit;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.bussiness.RedisKeyContants;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.domain.vo.project.ProjectOrgExcelVo;
import com.haoys.user.domain.vo.project.ProjectOrgExportVo;
import com.haoys.user.domain.vo.project.ProjectOrgIdentCodeVo;
import com.haoys.user.domain.vo.project.ProjectOrgVo;
import com.haoys.user.domain.vo.project.ProjectVo;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.common.excel.ExcelUtil;
import com.haoys.user.domain.dto.ProjectOrgParam;
import com.haoys.user.domain.vo.system.OrganizationVo;
import com.haoys.user.model.Organization;
import com.haoys.user.service.OrganizationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@RestController
@Api(tags = "项目研究中心管理")
@RequestMapping("/org")
public class ProjectOrgManageController extends BaseController {

    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private RedisTemplateService redisTemplateService;

    @ApiOperation("查询项目研究中心-下拉框使用")
    @GetMapping("/getOrganizationListDropownox")
    public CommonResult<List<OrganizationVo>> getOrganizationListForCombobox(String projectId, String orgName, String showProjectOrgCode, String showSelectAllOption){
        if (StringUtils.isBlank(projectId)) {
            return CommonResult.failed("项目id不能为空");
        }
        List<OrganizationVo> organizationData = organizationService.getOrganizationListForCombobox(projectId, orgName, getUserId(), showProjectOrgCode, showSelectAllOption);
        return CommonResult.success(organizationData);
    }


    @ApiOperation("查询项目研究中心分页列表")
    @GetMapping("/getProjectOrgListForPage")
    public CommonResult<CommonPage> getProjectOrgListForPage(@RequestParam(value="name",required = false) String name,
                                                                           @RequestParam(value="orgId",required = false) String orgId,
                                                                           @RequestParam(value="officer",required = false) String officer,
                                                                           @RequestParam(value="projectId") String projectId,
                                                                           @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                           @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize){
        List<ProjectOrgVo> organizationList = organizationService.getProjectOrgListForPage(projectId, orgId, name, officer, pageNum, pageSize);
        return returnPage(organizationList);
    }


    @ApiOperation(value = "查询项目中心基本信息")
    @RequestMapping(value = "/getProjectOrgInfo", method = RequestMethod.GET)
    public CommonResult<Object> getProjectOrgInfo(String orgId) {
        Organization organizationInfo = organizationService.getSystemOrganizationInfo(orgId);
        return CommonResult.success(organizationInfo);
    }


    @ApiOperation(value = "查询用户子中心列表")
    @RequestMapping(value = "/getUserOwnerOrgList", method = RequestMethod.GET)
    public CommonResult<List<OrganizationVo>> getUserOwnerOrgList(String projectId) {
        List<OrganizationVo> projectUserOrgList = organizationService.getProjectUserOrgList(projectId, getUserId());
        return CommonResult.success(projectUserOrgList);
    }

    @NoRepeatSubmit
    @Log(title = "添加/编辑项目研究中心", businessType = BusinessType.INSERT, projectRecordLog = true)
    @ApiOperation(value = "添加/编辑项目研究中心")
    @RequestMapping(value = "/saveProjectOrgInfo", method = RequestMethod.POST)
    public CommonResult<Object> saveProjectOrgInfo(@Validated @RequestBody ProjectOrgParam projectOrgParam) {
        projectOrgParam.setCreateUserId(getUserId());
        CustomResult customResult = organizationService.saveProjectOrgInfo(projectOrgParam);
        return CommonResult.success(customResult);
    }

    @NoRepeatSubmit
    @Log(title = "编辑项目研究中心", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "编辑项目研究中心-已废弃")
    @RequestMapping(value = "/modifyProjectOrgInfo", method = RequestMethod.POST)
    public CommonResult<Object> updateProjectOrgInfo(String projectId, String orgId, String code, String officer, String expands) {
        CustomResult customResult = organizationService.updateProjectOrgInfo(projectId, orgId, code, officer, expands, getUserId());
        return CommonResult.success(customResult);
    }

    @ApiOperation("下载项目中心模版")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ProjectOrgExcelVo> excelUtil = new ExcelUtil<>(ProjectOrgExcelVo.class);
        excelUtil.importTemplateExcel(response,"项目研究中心");
    }

    @NoRepeatSubmit
    @Log(title = "批量添加项目中心", businessType = BusinessType.IMPORT)
    @ApiOperation(value = "批量添加项目中心")
    @RequestMapping(value = "/saveBatchProjectOrg", method = RequestMethod.POST,headers = "content-type=multipart/form-data")
    public CommonResult<Object> saveBatchProjectOrg(HttpServletResponse response, @RequestParam MultipartFile file, String projectId) throws Exception {
        List<ProjectOrgExportVo> errorList = new ArrayList<>();
        HashMap<Object, Object> map = new HashMap<>();
        String message = organizationService.saveBatchProjectOrg(file, projectId, errorList, getUserId());
        map.put("message",message);
        if(errorList.size() >0){
            ExcelUtil<ProjectOrgExportVo> excelUtil = new ExcelUtil<>(ProjectOrgExportVo.class);
            CommonResult commonResult = excelUtil.exportExcel(errorList, "异常数据查看");
            map.put("url",commonResult.getData());
            return CommonResult.success(map, ResultCode.SYSTEM_IMPORT_EXCEPTION_MESSAGE);
        }
        return CommonResult.success(map);
    }


    @NoRepeatSubmit
    @Log(title = "删除项目研究中心", businessType = BusinessType.DELETE, projectRecordLog = true)
    @ApiOperation(value = "删除项目研究中心")
    @RequestMapping(value = "/removeProjectOrgByOrgId", method = RequestMethod.POST)
    public CommonResult<Object> deleteProjectOrgByOrgId(String projectId, String projectOrgId) {
        CustomResult customResult = organizationService.deleteProjectOrgByOrgId(projectId, projectOrgId);
        return CommonResult.success(customResult);
    }


    @ApiOperation(value = "设置最后选中项目研究中心id")
    @RequestMapping(value = "/modifyProjectOrgToCache", method = RequestMethod.GET)
    public CommonResult<Object> modifyProjectOrgToCache(String projectId, String projectOrgId) {
        redisTemplateService.set(RedisKeyContants.SEARCH_PROJECT_ORG_KEY + projectId + getUserId(), projectOrgId);
        return CommonResult.success(null);
    }

    @ApiOperation(value = "通过项目唯一机构识别码获取项目信息和机构信息")
    @GetMapping(value = "/getByIdentCode")
    public CommonResult<Object> getByIdentCode(String projectId, String identCode) {
        ProjectVo projectVo = organizationService.getProjectByOrgIdentCode(projectId,identCode);
        return CommonResult.success(projectVo);
    }

    @ApiOperation(value = "通过项目中心id获取中心识别码")
    @GetMapping(value = "/getIdentCode")
    public CommonResult<ProjectOrgIdentCodeVo> getIdentCode(String orgId) {
        ProjectOrgIdentCodeVo identCode = organizationService.getProjectOrgInfoByOrgIdentCode(orgId);
        return CommonResult.success(identCode);
    }

}
