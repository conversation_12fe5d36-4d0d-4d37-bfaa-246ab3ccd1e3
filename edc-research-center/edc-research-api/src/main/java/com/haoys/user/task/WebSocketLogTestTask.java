package com.haoys.user.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.concurrent.atomic.AtomicLong;
import java.util.Random;

/**
 * WebSocket日志测试任务
 * 专门用于测试SpringLogWebSocketHandler的实时日志推送功能
 * 每1秒输出一个完整的info日志信息，每次日志信息不重复
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-23
 */
@Component
@Slf4j
public class WebSocketLogTestTask {

    // WebSocket日志测试任务开关配置
    @Value("${websocket.log.test-task-enabled}")
    private boolean testTaskEnabled;

    private final AtomicLong logCounter = new AtomicLong(0);
    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
    private final Random random = new Random();
    
    // 测试消息模板
    private final String[] messageTemplates = {
        "用户登录成功，用户ID: {}",
        "数据库连接池状态正常，活跃连接: {}",
        "缓存更新完成，缓存键: cache-{}",
        "API请求处理完成，耗时: {}ms",
        "定时任务执行完成，任务ID: task-{}",
        "文件上传成功，文件大小: {}KB",
        "邮件发送成功，收件人数量: {}",
        "系统监控检查完成，CPU使用率: {}%",
        "数据备份完成，备份文件: backup-{}.sql",
        "WebSocket连接建立，客户端ID: client-{}"
    };
    
    /**
     * 每1秒输出一个不重复的INFO级别日志信息
     */
    @Scheduled(cron = "0/1 * * * * ?")
    public void logInfoMessage() {
        // 检查任务开关
        if (!testTaskEnabled) {
            return;
        }

        long currentCount = logCounter.incrementAndGet();
        String currentTime = sdf.format(System.currentTimeMillis());
        String template = messageTemplates[(int) (currentCount % messageTemplates.length)];

        // 生成随机参数
        Object param = generateRandomParam(currentCount);

        log.info("🔄 " + template + " - 时间: {} - 序号: #{}",
                param, currentTime, currentCount);
    }
    
    /**
     * 每5秒输出一个WARN级别的日志
     */
    @Scheduled(cron = "0/5 * * * * ?")
    public void logWarningMessage() {
        // 检查任务开关
        if (!testTaskEnabled) {
            return;
        }

        long currentCount = logCounter.get();
        log.warn("⚠️ 系统警告 - 内存使用率较高: {}% - 建议检查内存泄漏 - 序号: #{}",
                75 + random.nextInt(20), currentCount);
    }
    
    /**
     * 每15秒输出一个ERROR级别的日志
     */
    @Scheduled(cron = "0/15 * * * * ?")
    public void logErrorMessage() {
        // 检查任务开关
        if (!testTaskEnabled) {
            return;
        }

        long currentCount = logCounter.get();
        log.error("❌ 模拟错误 - 数据库连接超时，重试次数: {} - 错误代码: ERR-{} - 序号: #{}",
                random.nextInt(3) + 1, 1000 + random.nextInt(999), currentCount);
    }
    
    /**
     * 每30秒输出一个DEBUG级别的日志
     */
    @Scheduled(cron = "0/30 * * * * ?")
    public void logDebugMessage() {
        // 检查任务开关
        if (!testTaskEnabled) {
            return;
        }

        long currentCount = logCounter.get();
        log.debug("🔍 调试信息 - 方法执行跟踪: processData() - 参数: [id={}, type={}] - 序号: #{}",
                random.nextInt(1000), "TYPE-" + random.nextInt(10), currentCount);
    }
    
    /**
     * 每60秒输出系统状态汇总日志
     */
    @Scheduled(cron = "0 * * * * ?")
    public void logSystemStatus() {
        // 检查任务开关
        if (!testTaskEnabled) {
            return;
        }

        long currentCount = logCounter.get();
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory() / 1024 / 1024;
        long freeMemory = runtime.freeMemory() / 1024 / 1024;
        long usedMemory = totalMemory - freeMemory;

        log.info("📊 系统状态汇总 - 总内存: {}MB, 已用: {}MB, 空闲: {}MB - 日志计数器: {} - 时间: {}",
                totalMemory, usedMemory, freeMemory, currentCount, sdf.format(System.currentTimeMillis()));
    }
    
    /**
     * 生成随机参数用于日志消息
     */
    private Object generateRandomParam(long count) {
        int type = (int) (count % 5);
        switch (type) {
            case 0: return "USER-" + (1000 + random.nextInt(9000));
            case 1: return random.nextInt(50) + 1;
            case 2: return "CACHE-" + System.nanoTime();
            case 3: return random.nextInt(500) + 50;
            case 4: return "TASK-" + count;
            default: return count;
        }
    }
    
    /**
     * 获取当前日志计数器值
     */
    public long getLogCounter() {
        return logCounter.get();
    }
    
    /**
     * 重置日志计数器
     */
    public void resetLogCounter() {
        logCounter.set(0);
        if (testTaskEnabled) {
            log.info("🔄 日志计数器已重置");
        }
    }

    /**
     * 获取任务开关状态
     */
    public boolean isTestTaskEnabled() {
        return testTaskEnabled;
    }

    /**
     * 获取任务状态信息
     */
    public String getTaskStatus() {
        return String.format("WebSocket日志测试任务状态: %s, 日志计数器: %d",
                testTaskEnabled ? "启用" : "禁用", logCounter.get());
    }
}
