package com.haoys.user.config;

import com.haoys.user.service.TransactionTestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * 事务测试运行器
 * 
 * <p>在应用启动时自动运行事务测试，验证全局事务配置是否正确</p>
 * 
 * <p>启用条件：</p>
 * <ul>
 *   <li>transaction.test.enabled=true - 启用事务测试功能</li>
 *   <li>transaction.test.auto-run-on-startup=true - 启动时自动运行</li>
 * </ul>
 * 
 * <p>配置说明：</p>
 * <ul>
 *   <li>transaction.test.log-enabled - 控制是否输出测试日志</li>
 *   <li>transaction.test.log-level - 控制日志级别</li>
 *   <li>transaction.test.output-format - 控制输出格式（SIMPLE/DETAILED）</li>
 * </ul>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-03
 */
@Slf4j
@Component
@ConditionalOnProperty(
    name = {"transaction.test.enabled", "transaction.test.auto-run-on-startup"}, 
    havingValue = "true"
)
public class TransactionTestRunner implements CommandLineRunner {

    @Autowired
    private TransactionTestService transactionTestService;

    @Value("${transaction.test.log-enabled:true}")
    private boolean logEnabled;

    @Value("${transaction.test.log-level:INFO}")
    private String logLevel;

    @Value("${transaction.test.output-format:DETAILED}")
    private String outputFormat;

    private static final String SEPARATOR = createRepeatedString("=", 80);
    private static final String SUB_SEPARATOR = createRepeatedString("-", 60);

    /**
     * 创建重复字符串（Java 8 兼容方法）
     */
    private static String createRepeatedString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    @Override
    public void run(String... args) throws Exception {
        if (logEnabled) {
            log.info("\n{}", SEPARATOR);
            log.info("🚀 启动时自动运行全局事务配置测试");
            log.info("{}", SEPARATOR);
            log.info("📋 测试配置:");
            log.info("  - 日志启用: {}", logEnabled);
            log.info("  - 日志级别: {}", logLevel);
            log.info("  - 输出格式: {}", outputFormat);
            log.info("{}", SUB_SEPARATOR);
        }

        try {
            // 运行事务测试
            runTransactionTests();
            
            if (logEnabled) {
                log.info("{}", SUB_SEPARATOR);
                log.info("✅ 全局事务配置测试完成，所有测试通过！");
                log.info("{}", SEPARATOR);
            }
        } catch (Exception e) {
            if (logEnabled) {
                log.error("{}", SUB_SEPARATOR);
                log.error("❌ 全局事务配置测试失败: {}", e.getMessage(), e);
                log.error("{}", SEPARATOR);
            }
            throw e;
        }
    }

    /**
     * 运行事务测试
     */
    private void runTransactionTests() {
        // 测试写事务方法
        testWriteTransactionMethods();
        
        // 测试读事务方法
        testReadTransactionMethods();
        
        // 测试无事务方法
        testNoTransactionMethods();
        
        // 测试事务回滚
        testTransactionRollback();
        
        // 验证事务状态
        validateTransactionStatus();
    }

    /**
     * 测试写事务方法
     */
    private void testWriteTransactionMethods() {
        if (logEnabled) {
            log.info("🔧 测试写事务方法（应该有写事务，ReadOnly=false）");
        }

        String[] methods = {"insert", "save", "create", "add", "update", "delete"};
        String testData = "startup-test-data";
        
        for (String method : methods) {
            String result = null;
            switch (method) {
                case "insert":
                    result = transactionTestService.insertTestData(testData);
                    break;
                case "save":
                    result = transactionTestService.saveTestEntity(testData);
                    break;
                case "create":
                    result = transactionTestService.createTestRecord(testData);
                    break;
                case "add":
                    result = transactionTestService.addTestItem(testData);
                    break;
                case "update":
                    result = transactionTestService.updateTestRecord(testData);
                    break;
                case "delete":
                    result = transactionTestService.deleteTestItem(testData);
                    break;
            }
            
            if (result != null) {
                logTestResult(method + "TestData", result, true, false);
                validateResult(result, true, false, method + "方法");
            }
        }
        
        if (logEnabled) {
            log.info("✅ 写事务方法测试完成");
        }
    }

    /**
     * 测试读事务方法
     */
    private void testReadTransactionMethods() {
        if (logEnabled) {
            log.info("🔍 测试读事务方法（应该有只读事务，ReadOnly=true）");
        }

        String[] methods = {"select", "get", "query", "find", "search", "count"};
        String testCriteria = "startup-test-criteria";
        
        for (String method : methods) {
            String result = null;
            switch (method) {
                case "select":
                    result = transactionTestService.selectTestData(testCriteria);
                    break;
                case "get":
                    result = transactionTestService.getTestRecord(testCriteria);
                    break;
                case "query":
                    result = transactionTestService.queryTestItems(testCriteria);
                    break;
                case "find":
                    result = transactionTestService.findTestUsers(testCriteria);
                    break;
                case "search":
                    result = transactionTestService.searchTestEntities(testCriteria);
                    break;
                case "count":
                    result = transactionTestService.countTestRecords(testCriteria);
                    break;
            }
            
            if (result != null) {
                logTestResult(method + "TestData", result, true, true);
                validateResult(result, true, true, method + "方法");
            }
        }
        
        if (logEnabled) {
            log.info("✅ 读事务方法测试完成");
        }
    }

    /**
     * 测试无事务方法
     */
    private void testNoTransactionMethods() {
        if (logEnabled) {
            log.info("🚫 测试无事务方法（应该没有事务，Transaction=false）");
        }

        String testData = "no-transaction-data";
        String result = transactionTestService.processWithoutTransaction(testData);
        
        logTestResult("processWithoutTransaction", result, false, false);
        validateResult(result, false, false, "无事务方法");
        
        if (logEnabled) {
            log.info("✅ 无事务方法测试完成");
        }
    }

    /**
     * 测试事务回滚
     */
    private void testTransactionRollback() {
        if (logEnabled) {
            log.info("🔄 测试事务回滚机制");
        }

        // 测试正常情况
        String normalResult = transactionTestService.saveTestDataWithException("normal");
        logTestResult("saveTestDataWithException(normal)", normalResult, true, false);
        validateResult(normalResult, true, false, "正常保存方法");
        
        // 测试异常情况
        try {
            transactionTestService.saveTestDataWithException("exception");
            throw new RuntimeException("事务回滚测试失败：应该抛出RuntimeException");
        } catch (RuntimeException e) {
            if ("测试事务回滚".equals(e.getMessage())) {
                if (logEnabled) {
                    log.info("✅ 事务回滚测试成功，捕获到预期异常: {}", e.getMessage());
                }
            } else {
                throw new RuntimeException("事务回滚测试失败：异常消息不符合预期", e);
            }
        }
        
        if (logEnabled) {
            log.info("✅ 事务回滚测试完成");
        }
    }

    /**
     * 验证事务状态
     */
    private void validateTransactionStatus() {
        if (logEnabled) {
            log.info("🔍 验证事务状态的准确性");
        }

        // 在当前方法中，应该没有事务
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        if (isTransactionActive) {
            throw new RuntimeException("启动器方法中不应该有事务");
        }
        
        if (logEnabled) {
            log.info("✅ 启动器方法事务状态验证通过: Transaction={}", isTransactionActive);
            log.info("✅ 事务状态验证完成");
        }
    }

    /**
     * 记录测试结果
     */
    private void logTestResult(String methodName, String result, boolean expectedTransaction, boolean expectedReadOnly) {
        if (!logEnabled) {
            return;
        }

        boolean actualTransaction = result.contains("Transaction=true");
        boolean actualReadOnly = result.contains("ReadOnly=true");
        
        String status = "✅";
        if (actualTransaction != expectedTransaction || actualReadOnly != expectedReadOnly) {
            status = "❌";
        }
        
        if ("DETAILED".equals(outputFormat)) {
            log.info("{} {} - 期望[事务:{}, 只读:{}] 实际[事务:{}, 只读:{}]", 
                    status, methodName, expectedTransaction, expectedReadOnly, actualTransaction, actualReadOnly);
            log.info("   完整结果: {}", result);
        } else {
            log.info("{} {} - Transaction:{}, ReadOnly:{}", 
                    status, methodName, actualTransaction, actualReadOnly);
        }
    }

    /**
     * 验证结果
     */
    private void validateResult(String result, boolean expectedTransaction, boolean expectedReadOnly, String methodType) {
        boolean actualTransaction = result.contains("Transaction=true");
        boolean actualReadOnly = result.contains("ReadOnly=true");
        
        if (actualTransaction != expectedTransaction) {
            throw new RuntimeException(String.format("%s 事务状态不符合预期，期望: %s, 实际: %s", 
                    methodType, expectedTransaction, actualTransaction));
        }
        
        if (actualReadOnly != expectedReadOnly) {
            throw new RuntimeException(String.format("%s 只读状态不符合预期，期望: %s, 实际: %s", 
                    methodType, expectedReadOnly, actualReadOnly));
        }
    }
}
