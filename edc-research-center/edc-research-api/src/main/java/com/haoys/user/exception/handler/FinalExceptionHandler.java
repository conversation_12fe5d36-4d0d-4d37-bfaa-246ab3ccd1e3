package com.haoys.user.exception.handler;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.trace.TraceIdUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 最终异常处理器（兜底处理器）
 *
 * <p>作为异常处理的最后一道防线，确保所有未被特定处理器捕获的异常都能得到安全处理</p>
 * <p>对于敏感异常，不会将详细信息返回给客户端，只记录到日志中</p>
 * <p>提供异常统计和监控功能，支持异常频率告警</p>
 * <p>保留原始GlobalExceptionHandler的所有统计和监控功能</p>
 * <p>所有异常处理都包含TraceId，便于问题追踪和日志关联</p>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
@Slf4j
@RestControllerAdvice
@Order(Integer.MAX_VALUE) // 最低优先级，作为最后的兜底处理
public class FinalExceptionHandler extends BaseExceptionHandler {

    /**
     * 异常处理统计计数器
     */
    private static final AtomicLong EXCEPTION_COUNTER = new AtomicLong(0);

    /**
     * 异常频率检查间隔（毫秒）
     */
    private static final long EXCEPTION_CHECK_INTERVAL = 60000; // 1分钟

    /**
     * 异常频率告警阈值（每分钟）
     */
    private static final long EXCEPTION_ALERT_THRESHOLD = 10;

    /**
     * 上次异常频率检查时间
     */
    private static volatile long lastExceptionCheckTime = System.currentTimeMillis();

    /**
     * 上次检查时的异常计数
     */
    private static volatile long lastExceptionCount = 0;

    /**
     * 处理通用异常（最后的兜底处理）
     *
     * <p>确保所有异常都包含TraceId，便于问题追踪</p>
     *
     * @param exception 异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(Exception.class)
    public CommonResult<String> handleException(Exception exception, HttpServletRequest request) {
        // 详细检查异常类型和处理状态
        String exceptionType = exception.getClass().getName();
        String requestUri = request.getRequestURI();
        Object handledFlag = request.getAttribute("FILE_UPLOAD_EXCEPTION_HANDLED");

        log.info("FinalExceptionHandler 处理异常 - 异常类型: {}, URI: {}, 已处理标记: {}, 线程: {}",
                exceptionType, requestUri, handledFlag, Thread.currentThread().getName());

        // 检查是否是文件上传异常，如果是则不在此处理，让FileExceptionHandler处理
        if (exception instanceof org.springframework.web.multipart.MaxUploadSizeExceededException ||
            (exception.getCause() instanceof org.springframework.web.multipart.MaxUploadSizeExceededException) ||
            exceptionType.contains("MaxUploadSizeExceeded")) {

            log.info("FinalExceptionHandler 检测到文件上传异常，委托给FileExceptionHandler处理 - 异常类型: {}",
                    exceptionType);

            // 重新抛出异常，让FileExceptionHandler处理
            if (exception instanceof org.springframework.web.multipart.MaxUploadSizeExceededException) {
                throw (org.springframework.web.multipart.MaxUploadSizeExceededException) exception;
            } else {
                // 如果是包装异常，抛出原始异常
                Throwable cause = exception.getCause();
                if (cause instanceof org.springframework.web.multipart.MaxUploadSizeExceededException) {
                    throw (org.springframework.web.multipart.MaxUploadSizeExceededException) cause;
                }
            }

            log.error("严重问题：无法重新抛出文件上传异常！异常类型: {}, 原因: {}",
                    exceptionType, exception.getMessage());
        }

        // 增加异常计数器，用于监控
        long exceptionCount = EXCEPTION_COUNTER.incrementAndGet();

        // 检查异常频率并触发告警
        checkExceptionRateAndTriggerAlert(exceptionCount);

        // 记录详细的异常信息到日志，包含TraceId
        String traceId = logExceptionWithTraceId(request, exception, "ERROR", 
            String.format("系统异常[%d]: 异常类型=%s", exceptionCount, exception.getClass().getSimpleName()));

        // 在日志中输出TraceId相关信息
        log.error("最终异常处理器捕获异常 [TraceId: {}]: 异常序号={}, 请求路径={}, 用户={}, 客户端IP={}", 
                 traceId, exceptionCount, request.getRequestURI(), safeGetUserName(), 
                 com.haoys.user.common.ip.RequestIpUtils.getIpAddress(request));

        // 检查是否为敏感异常，决定返回信息的详细程度
        String safeMessage = isSensitiveException(exception) ? 
            "系统正在处理中，请稍后再试" : truncateMessage(exception.getMessage(), "系统遇到问题，请稍后重试");

        // 返回安全的错误响应，包含TraceId
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, safeMessage);
    }

    /**
     * 处理Error类型的严重错误
     *
     * <p>确保严重错误也包含TraceId，便于紧急问题追踪</p>
     *
     * @param error 错误对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(Error.class)
    public CommonResult<String> handleError(Error error, HttpServletRequest request) {
        // 增加异常计数器
        long exceptionCount = EXCEPTION_COUNTER.incrementAndGet();

        // 记录严重错误，包含TraceId
        String traceId = logExceptionWithTraceId(request, error, "ERROR", 
            String.format("系统严重错误[%d]: 错误类型=%s", exceptionCount, error.getClass().getSimpleName()));

        // 在日志中特别标记严重错误，包含TraceId
        log.error("严重系统错误 [TraceId: {}]: 错误序号={}, 错误类型={}, 请求路径={}, 用户={}, 客户端IP={}", 
                 traceId, exceptionCount, error.getClass().getSimpleName(), request.getRequestURI(), 
                 safeGetUserName(), com.haoys.user.common.ip.RequestIpUtils.getIpAddress(request));

        // 严重错误需要立即告警，告警消息包含TraceId
        if (exceptionAlertService != null) {
            try {
                String alertMessage = String.format("系统严重错误: 错误类型=%s, 请求路径=%s, TraceId=%s, 用户=%s", 
                                 error.getClass().getSimpleName(), request.getRequestURI(), traceId, safeGetUserName());
                exceptionAlertService.sendSmsNotification(new String[]{"admin"}, alertMessage);
                
                log.info("严重错误告警已发送 [TraceId: {}]: {}", traceId, alertMessage);
            } catch (Exception alertException) {
                log.error("发送严重错误告警失败 [TraceId: {}]: {}", traceId, alertException.getMessage(), alertException);
            }
        }

        // 对于Error类型，统一返回系统繁忙信息，包含TraceId
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, 
                                             "系统正在处理中，请稍后再试");
    }

    /**
     * 处理Throwable类型的所有异常
     *
     * <p>最终的异常捕获，确保所有异常都有TraceId</p>
     *
     * @param throwable 异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(Throwable.class)
    public CommonResult<String> handleThrowable(Throwable throwable, HttpServletRequest request) {
        // 增加异常计数器
        long exceptionCount = EXCEPTION_COUNTER.incrementAndGet();

        // 记录所有类型的异常，包含TraceId
        String traceId = logExceptionWithTraceId(request, throwable, "ERROR", 
            String.format("系统异常[%d]: 异常类型=%s", exceptionCount, throwable.getClass().getSimpleName()));

        // 在日志中输出详细的异常信息，包含TraceId
        log.error("Throwable异常处理 [TraceId: {}]: 异常序号={}, 异常类型={}, 请求路径={}, 用户={}, 客户端IP={}", 
                 traceId, exceptionCount, throwable.getClass().getSimpleName(), request.getRequestURI(), 
                 safeGetUserName(), com.haoys.user.common.ip.RequestIpUtils.getIpAddress(request));

        // 对于未知类型的异常，统一返回系统错误信息，包含TraceId
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, 
                                             "系统遇到问题，请稍后重试");
    }

    /**
     * 检查异常频率并触发告警
     *
     * <p>当异常频率过高时，发送告警消息，告警消息包含统计信息</p>
     *
     * @param currentExceptionCount 当前异常计数
     */
    private void checkExceptionRateAndTriggerAlert(long currentExceptionCount) {
        long currentTime = System.currentTimeMillis();
        
        // 检查是否到了检查间隔
        if (currentTime - lastExceptionCheckTime >= EXCEPTION_CHECK_INTERVAL) {
            synchronized (this) {
                // 双重检查锁定
                if (currentTime - lastExceptionCheckTime >= EXCEPTION_CHECK_INTERVAL) {
                    // 计算异常频率
                    long exceptionRate = currentExceptionCount - lastExceptionCount;
                    
                    // 记录异常频率统计，包含时间戳信息
                    log.info("异常频率统计: 过去1分钟发生异常 {} 次，累计异常 {} 次，检查时间: {}", 
                            exceptionRate, currentExceptionCount, new java.util.Date(currentTime));
                    
                    // 检查是否超过告警阈值
                    if (exceptionRate >= EXCEPTION_ALERT_THRESHOLD && exceptionAlertService != null) {
                        try {
                            String alertMessage = String.format("异常频率告警: 过去1分钟内发生异常 %d 次，超过阈值 %d 次，累计异常 %d 次，检查时间: %s", 
                                             exceptionRate, EXCEPTION_ALERT_THRESHOLD, currentExceptionCount, 
                                             new java.util.Date(currentTime));
                            exceptionAlertService.sendSmsNotification(new String[]{"admin"}, alertMessage);
                            
                            log.warn("异常频率告警已发送: {}", alertMessage);
                        } catch (Exception alertException) {
                            log.error("发送异常频率告警失败: {}", alertException.getMessage(), alertException);
                        }
                    }
                    
                    // 更新检查时间和计数
                    lastExceptionCheckTime = currentTime;
                    lastExceptionCount = currentExceptionCount;
                }
            }
        }
    }

    /**
     * 获取异常统计信息
     *
     * @return 异常统计信息字符串
     */
    public static String getExceptionStatistics() {
        long totalExceptions = EXCEPTION_COUNTER.get();
        long currentTime = System.currentTimeMillis();
        long timeSinceLastCheck = currentTime - lastExceptionCheckTime;
        long recentExceptions = totalExceptions - lastExceptionCount;
        
        return String.format("异常统计 - 总计: %d, 最近: %d (过去 %d 秒), 最后检查: %s", 
                           totalExceptions, recentExceptions, timeSinceLastCheck / 1000,
                           new java.util.Date(lastExceptionCheckTime));
    }

    /**
     * 获取异常计数
     *
     * @return 异常计数
     */
    public static long getExceptionCount() {
        return EXCEPTION_COUNTER.get();
    }

    /**
     * 重置异常统计
     */
    public static void resetExceptionCounter() {
        EXCEPTION_COUNTER.set(0);
        lastExceptionCount = 0;
        lastExceptionCheckTime = System.currentTimeMillis();
        log.info("异常统计已重置，重置时间: {}", new java.util.Date());
    }

    /**
     * 记录异常日志并生成TraceId的重写方法
     *
     * <p>重写基类方法，增强TraceId相关的日志输出</p>
     *
     * @param httpRequest HTTP请求对象
     * @param throwable 异常对象
     * @param level 日志级别
     * @param message 自定义消息
     * @return 生成的TraceId
     */
    protected String logExceptionWithTraceId(HttpServletRequest httpRequest, Throwable throwable,
                                          String level, String message) {
        // 调用父类方法记录基本日志
        String traceId = super.logExceptionWithTraceId(httpRequest, (Exception) throwable, level, message);
        
        // 添加额外的统计信息和TraceId关联日志
        long totalExceptions = EXCEPTION_COUNTER.get();
        log.info("异常处理统计 [TraceId: {}]: 当前异常序号={}, 异常类型={}, 请求路径={}, 处理时间={}", 
                traceId, totalExceptions, throwable.getClass().getSimpleName(), httpRequest.getRequestURI(),
                new java.util.Date());
        
        return traceId;
    }
}
