package com.haoys.user.flow.controller;

import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.domain.param.flow.FlowEditPerParam;
import com.haoys.user.domain.vo.flow.FlowOrderFormVo;
import com.haoys.user.model.FlowFormSet;
import com.haoys.user.service.FlowFormSetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "流程配置-表单排序")
@RestController
@RequestMapping("flowFormOrder")
public class FlowFormOrderController {

    @Autowired
    private FlowFormSetService formSetService;

    @ApiOperation("表单排序-列表")
    @Log(title = "表单排序-列表", businessType = BusinessType.OTHER)
    @GetMapping("listByFlowId")
    public CommonResult<FlowOrderFormVo> listByFlowId(Long flowId) {
        return formSetService.listByFlowId(flowId);
    }


    @ApiOperation("表单排序-获取绑定的表单")
    @Log(title = "表单排序-列表", businessType = BusinessType.OTHER)
    @GetMapping("getFormByFlowId")
    public CommonResult<List<FlowFormSet>> getFormByFlowId(Long flowId) {
        return formSetService.getFormByFlowId(flowId);
    }


    @ApiOperation("表单排序-修改权限")
    @Log(title = "研究流程-表单排序-修改权限", businessType = BusinessType.UPDATE)
    @PostMapping("editPer")
    public CommonResult<Object> editPer (@RequestBody FlowFormSet formSet) {
        return formSetService.updateFlowFormAuth(formSet);
    }

    @ApiOperation("表单排序-排序")
    @Log(title = "表单排序-保存", businessType = BusinessType.UPDATE)
    @PostMapping("order")
    public CommonResult<Object> order (@RequestBody List<String> ids) {
        return formSetService.saveOrderForm(ids);
    }


    @ApiOperation("表单排序-全部权限修改")
    @Log(title = "表单排序-全部", businessType = BusinessType.UPDATE)
    @PostMapping("editAllPer")
    public CommonResult<Object> editAllPer (@RequestBody FlowEditPerParam param) {
        return formSetService.editAllPer(param);
    }

}
