package com.haoys.user.participant.controller;


import cn.hutool.core.util.NumberUtil;
import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.util.SecureTokenUtil;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.model.ProjectTesteeSign;
import com.haoys.user.service.ProjectTesteeSignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(tags = "研究管理-参与者管理-之情同意书签字")
@RequestMapping("testeeSign")
public class ProjectTesteeSignController extends BaseController {

    @Autowired
    private ProjectTesteeSignService projectTesteeSignService;

    @Autowired
    private SecureTokenUtil secureTokenUtil;
    
    
    @PostMapping("/sign")
    @ApiOperation("确认签署")
    @Log(title = "参与者知情同意书签名", businessType = BusinessType.INSERT)
    public CommonResult<Object> sign(@RequestBody ProjectTesteeSign projectTesteeSignParam) {
        ProjectTesteeSign projectTesteeSign = projectTesteeSignService.create(projectTesteeSignParam);
        return CommonResult.success(projectTesteeSign);
    }
    
    @PostMapping("/modify-signInfo")
    @ApiOperation("更新签署同意书签名信息")
    @Log(title = "更新签署同意书签名信息", businessType = BusinessType.UPDATE)
    public CommonResult<Object> modifySignInfo(@RequestBody ProjectTesteeSign projectTesteeSignParam) {
        if(projectTesteeSignParam.getId() == null){
            return CommonResult.validateFailed("未查询到对应的签名记录");
        }
        projectTesteeSignService.update(projectTesteeSignParam);
        return CommonResult.success(null);
    }
    
    @GetMapping("/getProjectTesteeSignInfo")
    @ApiOperation("查询研究者签名")
    public CommonResult<Object> getProjectTesteeSign(@RequestParam(value = "projectId") String projectId, @RequestParam(value = "testeeCode") String testeeCode) {
        ProjectTesteeSign projectTesteeSignInfo = projectTesteeSignService.getProjectTesteeSignInfo(projectId, testeeCode);
        return CommonResult.success(projectTesteeSignInfo);
    }
    
    @PostMapping("/removeProjectTesteeSign")
    @ApiOperation("删除研究者签名")
    @Log(title = "删除研究者签名", businessType = BusinessType.DELETE)
    public CommonResult<Object> removeProjectTesteeSign(@RequestParam(value = "signId") String signId,@RequestParam(value="accessToken") String accessToken) {
        if(!secureTokenUtil.isValidAccessToken(accessToken)){
            return CommonResult.failed(ResultCode.VALIDATE_FAILED.getMessage());
        }
        int delete = projectTesteeSignService.delete(NumberUtil.parseLong(signId));
        return CommonResult.success(delete);
    }

}
