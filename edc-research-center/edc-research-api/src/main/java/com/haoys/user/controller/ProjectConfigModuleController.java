package com.haoys.user.controller;

import com.haoys.user.model.ProjectConfigModule;
import com.haoys.user.service.ProjectConfigModuleService;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Map;

/**
 * 项目配置分组表Controller
 * 提供RESTful API接口
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 * @date 2025-07-11 00:31:50
 */
@Slf4j
@RestController
@RequestMapping("/projectConfigModule")
@Api(tags = "项目配置分组表管理")
public class ProjectConfigModuleController extends BaseController {

    @Autowired
    private ProjectConfigModuleService projectConfigModuleService;

    @ApiOperation(value = "创建项目配置分组表", notes = "创建新的项目配置分组表记录")
    @PostMapping
    public CommonResult<ProjectConfigModule> create(
            @ApiParam(value = "项目配置分组表信息", required = true) @RequestBody ProjectConfigModule projectConfigModule) {
        return projectConfigModuleService.create(projectConfigModule);
    }

    @ApiOperation(value = "删除项目配置分组表", notes = "根据ID删除项目配置分组表")
    @DeleteMapping("/{moduleId}")
    public CommonResult<Void> delete(
            @ApiParam(value = "项目配置分组表ID", required = true) @PathVariable Long moduleId) {
        return projectConfigModuleService.deleteById(moduleId);
    }

    @ApiOperation(value = "更新项目配置分组表", notes = "更新项目配置分组表信息")
    @PutMapping
    public CommonResult<ProjectConfigModule> update(
            @ApiParam(value = "项目配置分组表信息", required = true) @RequestBody ProjectConfigModule projectConfigModule) {
        return projectConfigModuleService.update(projectConfigModule);
    }

    @ApiOperation(value = "查询项目配置分组表", notes = "根据ID查询项目配置分组表")
    @GetMapping("/{moduleId}")
    public CommonResult<ProjectConfigModule> getById(
            @ApiParam(value = "项目配置分组表ID", required = true) @PathVariable Long moduleId) {
        return projectConfigModuleService.getById(moduleId);
    }

    @ApiOperation(value = "查询项目配置分组表列表", notes = "根据条件查询项目配置分组表列表")
    @GetMapping
    public CommonResult<List<ProjectConfigModule>> list(
            @ApiParam(value = "查询参数") @RequestParam Map<String, Object> params) {
        return projectConfigModuleService.listByCondition(params);
    }

    @ApiOperation(value = "分页查询项目配置分组表", notes = "分页查询项目配置分组表列表")
    @GetMapping("/page")
    public CommonResult<CommonPage<ProjectConfigModule>> listByPage(
            @ApiParam(value = "查询参数") @RequestParam Map<String, Object> params,
            @ApiParam(value = "页码", defaultValue = "1") @RequestParam(defaultValue = "1") int pageNum,
            @ApiParam(value = "页大小", defaultValue = "10") @RequestParam(defaultValue = "10") int pageSize) {
        return projectConfigModuleService.pageByCondition(params, pageNum, pageSize);
    }

    @ApiOperation(value = "批量删除项目配置分组表", notes = "根据ID列表批量删除项目配置分组表")
    @DeleteMapping("/batch")
    public CommonResult<Void> batchDelete(
            @ApiParam(value = "ID列表", required = true) @RequestBody List<Long> ids) {
        return projectConfigModuleService.batchDeleteByIds(ids);
    }

}
