package com.haoys.user.mobile;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.domain.vo.project.ProjectAnnouncementVo;
import com.haoys.user.domain.vo.project.ProjectTesteeOrgVo;
import com.haoys.user.domain.vo.project.ProjectVo;
import com.haoys.user.model.ProjectAnnouncement;
import com.haoys.user.service.ProjectAnnouncementService;
import com.haoys.user.service.ProjectBaseManageService;
import com.haoys.user.service.ProjectTesteeInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Api(tags = "SRC-ePRO个人中心")
@RequestMapping("/mobileIndividualCenter")
public class MobileUserCenterController extends BaseController {
    
    @Autowired
    private ProjectBaseManageService projectBaseManageService;
    @Autowired
    private ProjectTesteeInfoService projectTesteeInfoService;
    @Autowired
    private ProjectAnnouncementService projectAnnouncementService;

    @ApiOperation("患者端获取项目列表")
    @GetMapping(value = "/getProjectList")
    public CommonResult<List<ProjectVo>> getOwnerProjectListForH5() {
        List<ProjectVo> projectList = projectBaseManageService.getOwnerProjectListForH5(getUserId());
        return CommonResult.success(projectList);
    }

    @ApiOperation("查询项目研究中心列表")
    @GetMapping(value = "/getProjectTesteeOrgVo")
    public CommonResult<List<ProjectTesteeOrgVo>> getProjectTesteeOrgVo(String projectId) {
        List<ProjectTesteeOrgVo> projectTesteeBaseInfoList = projectTesteeInfoService.getTesteeJoinOrgListByUserId(projectId, getUserId());
        return CommonResult.success(projectTesteeBaseInfoList);
    }

    @ApiOperation("EPR-获取公告列表列表")
    @GetMapping("/eprList")
    public CommonResult list(@RequestParam(value = "projectId") String projectId,
                             @RequestParam(value = "projectOrgId") String projectOrgId,
                             @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                             @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ProjectAnnouncement projectAnnouncement = new ProjectAnnouncement();
        projectAnnouncement.setProjectId(Long.valueOf(projectId));
        projectAnnouncement.setProjectOrgId(Long.valueOf(projectOrgId));
        CommonPage<ProjectAnnouncementVo> list = projectAnnouncementService.selectProjectAnnouncementEprList(projectAnnouncement, pageNum, pageSize);
        return CommonResult.success(list);
    }

}
