package com.haoys.user.controller;


import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.annotation.NoRepeatSubmit;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.domain.param.project.ProjectTesteeViewConfigParam;
import com.haoys.user.domain.vo.testee.ProjectTesteeConfigVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeViewConfigVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeVo;
import com.haoys.user.service.ProjectTesteeConfigService;
import com.haoys.user.service.ProjectTesteeInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Api(tags = "项目参与者审核")
@RequestMapping("/testee_review")
public class ProjectTesteeReviewController extends BaseController {

    @Autowired
    private ProjectTesteeInfoService projectTesteeInfoService;
    @Resource
    private ProjectTesteeConfigService projectTesteeConfigService;

    @ApiOperation("查询参与者审核分页列表")
    @RequestMapping(value = "/getTesteeReviewList", method = RequestMethod.GET)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "string",required = true),
            @ApiImplicitParam(name = "code", value = "参与者编号", dataType = "string"),
            @ApiImplicitParam(name = "realName", value = "参与者姓名", dataType = "string"),
            @ApiImplicitParam(name = "orgId", value = "所属中心id", dataType = "string"),
            @ApiImplicitParam(name = "ownerDoctor", value = "主管医生id-默认查询自己录入的数据 项目负责人查询全部参与者", dataType = "string"),
            @ApiImplicitParam(name = "reviewStatus", value = "绑定审核状态参数说明 013001-待审核 013002-已完成 013003-已拒绝", dataType = "string")
    })
    public CommonResult<CommonPage<ProjectTesteeViewConfigVo>> getTesteeReviewList(String projectId, String code, String realName, String orgId,
                                                                   String ownerDoctor, String reviewStatus,
                                                                   @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                   @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ProjectTesteeConfigVo projectTesteeConfigVo = projectTesteeConfigService.getProjectTesteeConfig(projectId);
        if(projectTesteeConfigVo == null){
            return CommonResult.failed(ResultCode.BUSINESS_PROJECT_TESTEE_CONFIG_NOT_FOUND.getCode(), BusinessConfig.PROJECT_TESTEE_CONFIG_NOT_FOUND);
        }
        CommonPage<ProjectTesteeViewConfigVo> projectTesteeList = projectTesteeInfoService.getTesteeReviewListForPage(projectId, code, realName, orgId, ownerDoctor, reviewStatus, pageNum, pageSize);
        return CommonResult.success(projectTesteeList);
    }


    @ApiOperation(value = "查询参与者审核详情")
    @RequestMapping(value = "/getTesteeReviewInfo", method = RequestMethod.GET)
    public CommonResult<ProjectTesteeViewConfigVo> getTesteeReviewInfo(String projectId, String id) {
        ProjectTesteeViewConfigVo projectTesteeViewConfigVo = new ProjectTesteeViewConfigVo();
        ProjectTesteeVo projectTesteeVo = projectTesteeInfoService.getProjectTesteeBaseInfo(projectId, id);
        BeanUtils.copyProperties(projectTesteeVo, projectTesteeViewConfigVo);
        return CommonResult.success(projectTesteeViewConfigVo);
    }

    @NoRepeatSubmit
    @Log(title = "参与者绑定审核", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "参与者绑定审核")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "string"),
            @ApiImplicitParam(name = "testeeId", value = "参与者id", dataType = "string"),
            @ApiImplicitParam(name = "testeeCode", value = "参与者编码code", dataType = "string"),
            @ApiImplicitParam(name = "status", value = "审核状态-参照字典项013", dataType = "string"),
    })
    @RequestMapping(value = "/modifyTesteeReviewStatus", method = RequestMethod.POST)
    public CommonResult<Object> updateTesteeReviewStatus(String projectId, String testeeId, String testeeCode, String status) {
        CustomResult data = projectTesteeInfoService.updateTesteeReviewStatus(projectId, testeeId, testeeCode, status, getUserId());
        if (!BusinessConfig.RETURN_MESSAGE_DEFAULT.equals(data.getMessage())) {
            return CommonResult.failed(data.getMessage());
        }
        return CommonResult.success(data.getData());
    }

    @NoRepeatSubmit
    @Log(title = "保存参与者基本信息绑定配置", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "保存参与者基本信息绑定配置")
    @RequestMapping(value = "/saveTesteeReviewConfig", method = RequestMethod.POST)
    public CommonResult<Object> saveTesteeReviewConfig(@RequestBody ProjectTesteeViewConfigParam projectTesteeViewConfigParam) {
        projectTesteeViewConfigParam.setCreateUserId(getUserId());
        CustomResult data = projectTesteeInfoService.saveTesteeReviewConfig(projectTesteeViewConfigParam);
        if (!BusinessConfig.RETURN_MESSAGE_DEFAULT.equals(data.getMessage())) {
            return CommonResult.failed(data.getMessage());
        }
        return CommonResult.success(data.getData());
    }







}
