package com.haoys.user.controller;

import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.model.FlowPlan;
import com.haoys.user.service.FlowPlanService;
import com.haoys.user.service.impl.DataAnalysisPlatformService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@Api(tags = "参与者数据同步管理")
@RequestMapping("/analysisPlatformApi")
public class DataAnalysisPlatformController extends BaseController {

    @Autowired
    private DataAnalysisPlatformService dataAnalysisPlatformService;
    @Autowired
    private FlowPlanService flowPlanService;

    @Log(title = "同步平台账号-数据分析平台", businessType = BusinessType.INSERT, projectRecordLog = true)
    @ApiOperation(value = "同步平台账号")
    @RequestMapping(value = "/modifySyncLoginUser", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<Object> updateSyncLoginUser() {
        CustomResult data = dataAnalysisPlatformService.updateSyncPlatformUserAccount(getUserId());
        return CommonResult.success(null);
    }

    @Log(title = "同步患者样本数据-数据分析平台", businessType = BusinessType.INSERT, projectRecordLog = true)
    @ApiOperation(value = "同步参与者样本数据-用于科研数据分析")
    @RequestMapping(value = "/syncProjectTesteeSimpleData", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<Object> syncProjectTesteeSimpleData(@RequestParam(value = "projectId") String projectId, String planId,
                                                            @RequestParam(value = "projectOrgId", required = false) String projectOrgId) {
        FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectId);
        if(flowPlanInfo != null){planId = flowPlanInfo.getId().toString();}
        CustomResult data = dataAnalysisPlatformService.updateSyncResearchData(projectId, projectOrgId, planId, getUserId());
        //CustomResult data = dataAnalysisPlatformService.updateSyncResearchExcelData(projectId, projectOrgId, planId, getUserId());
        return CommonResult.success(data);
    }

    @ApiOperation(value = "查询科研统计分析平台URL")
    @RequestMapping(value = "/redirectUrl", method = RequestMethod.GET)
    @ResponseBody
    public CommonResult<Object> redirectUrl() {
        String url = dataAnalysisPlatformService.getResearchRedirectUrl(getUserName());
        return CommonResult.success(url);
    }

    @ApiOperation(value = "查询chatGPT计算结果")
    @RequestMapping(value = "/getCalculatorContent", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<Object> getCalculatorContent(String question) {
        CustomResult data = dataAnalysisPlatformService.getCalculatorContent(question);
        return CommonResult.success(data);
    }
}
