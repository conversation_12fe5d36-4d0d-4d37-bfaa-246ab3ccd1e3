package com.haoys.user.exception.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.base.Throwables;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.ip.RequestIpUtils;
import com.haoys.user.common.trace.TraceIdUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.exception.CustomerException;
import com.haoys.user.exception.ServiceException;
import com.haoys.user.exception.alert.ExceptionAlertService;
import com.haoys.user.exception.file.FileSizeLimitExceededException;
import com.haoys.user.security.audit.SecurityAuditService;
import static com.haoys.user.exception.handler.ExceptionMessages.*;
import com.haoys.user.exception.user.UserPasswordNotMatchException;
import com.haoys.user.manager.AsyncTaskManager;
import com.haoys.user.manager.factory.AsyncTaskFactory;
import com.haoys.user.model.SystemExceptionLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.TypeMismatchException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.AuthenticationCredentialsNotFoundException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.io.IOException;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 *
 * <p>统一处理系统中的各种异常，提供一致的错误响应格式</p>
 * <p>按照异常类型分类处理，确保安全性和可维护性</p>
 * <p>支持异常监控、日志记录、性能统计等高级功能</p>
 *
 * <h3>异常分类处理：</h3>
 * <ul>
 *   <li><b>系统级异常</b>：运行时异常、状态异常等</li>
 *   <li><b>Web层异常</b>：HTTP相关、请求处理异常</li>
 *   <li><b>数据层异常</b>：数据库访问、数据完整性异常</li>
 *   <li><b>安全认证异常</b>：权限、认证相关异常</li>
 *   <li><b>业务逻辑异常</b>：自定义业务异常</li>
 *   <li><b>参数验证异常</b>：输入验证、绑定异常</li>
 *   <li><b>文件处理异常</b>：上传、下载相关异常</li>
 *   <li><b>序列化异常</b>：JSON、IO处理异常</li>
 * </ul>
 *
 * <h3>安全特性：</h3>
 * <ul>
 *   <li>敏感信息自动过滤，防止信息泄露</li>
 *   <li>异常堆栈信息仅记录到日志，不返回给客户端</li>
 *   <li>统一的错误响应格式，避免暴露系统内部结构</li>
 *   <li>异步日志记录，不影响主业务流程性能</li>
 *   <li>异常频率监控，支持异常告警机制</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
@Slf4j
//@RestControllerAdvice  // 已禁用，使用新的分类异常处理器
public class GlobalExceptionHandler {

    // ================================ 常量定义 ================================

    /**
     * 异常处理统计计数器
     * 用于监控系统异常发生频率，支持异常告警
     */
    private static final java.util.concurrent.atomic.AtomicLong EXCEPTION_COUNTER = new java.util.concurrent.atomic.AtomicLong(0);

    /**
     * 异常告警服务
     * 用于异常频率监控和告警通知
     */
    @Autowired
    private ExceptionAlertService exceptionAlertService;

    /**
     * 安全审计服务
     * 用于记录安全相关事件和风险评估
     */
    @Autowired
    private SecurityAuditService securityAuditService;

    /**
     * 最大错误消息长度限制
     * 防止过长的错误消息影响响应性能和安全性
     */
    private static final int MAX_ERROR_MESSAGE_LENGTH = 200;

    /**
     * 异常日志记录开关
     * 可通过配置控制是否记录异常到数据库
     */
    private static final boolean ENABLE_EXCEPTION_LOGGING = true;

    /**
     * 敏感异常类型列表
     * 这些异常不应该将详细信息返回给客户端
     */
    private static final Set<Class<? extends Exception>> SENSITIVE_EXCEPTIONS = new HashSet<>(Arrays.asList(
        SQLException.class,
        DataAccessException.class,
        NullPointerException.class,
        ClassCastException.class
    ));

    /**
     * 异常频率监控阈值（每分钟）
     * <p>当系统异常频率超过此阈值时将触发告警通知</p>
     * <p>包括短信和邮件通知，帮助运维人员及时发现和处理系统问题</p>
     */
    private static final long EXCEPTION_THRESHOLD_PER_MINUTE = 100;

    // ================================ 系统级异常处理 ================================

    /**
     * 处理通用异常（最后的兜底处理）
     *
     * <p>这是异常处理的最后一道防线，确保所有未被特定处理器捕获的异常都能得到安全处理</p>
     * <p>对于敏感异常，不会将详细信息返回给客户端，只记录到日志中</p>
     *
     * @param httpRequest HTTP请求对象，用于获取请求上下文信息
     * @param exceptionObject 异常对象，包含异常的详细信息
     * @return 统一响应结果，包含安全的错误信息
     */
    @ExceptionHandler(Exception.class)
    public CommonResult<String> handleException(HttpServletRequest httpRequest, Exception exceptionObject) {
        // 增加异常计数器，用于监控
        long exceptionCount = EXCEPTION_COUNTER.incrementAndGet();

        // 检查异常频率并触发告警
        checkExceptionRateAndTriggerAlert(exceptionCount);

        // 记录详细的异常信息到日志
        log.error("系统异常[{}]: 请求路径={}, 异常类型={}, 异常消息={}", exceptionCount, httpRequest.getRequestURI(), exceptionObject.getClass().getSimpleName(), exceptionObject.getMessage(), exceptionObject);

        // 异步记录异常日志到数据库（不影响响应性能）
        if (ENABLE_EXCEPTION_LOGGING) {
            recordExceptionLogSafely(httpRequest, exceptionObject);
        }

        // 检查是否为敏感异常，决定返回信息的详细程度
        String safeMessage = isSensitiveException(exceptionObject) ? SYSTEM_BUSY : truncateMessage(exceptionObject.getMessage(), SYSTEM_ERROR);

        // 生成追踪ID用于问题排查
        String traceId = TraceIdUtils.getOrGenerateTraceId(httpRequest);

        // 记录追踪ID到日志上下文，便于问题排查
        log.error("系统异常处理 [TraceId: {}]: 请求路径={}, 异常类型={}, 异常消息={}", traceId, httpRequest.getRequestURI(), exceptionObject.getClass().getSimpleName(), exceptionObject.getMessage(), exceptionObject);

        // 返回安全的错误响应
        return CommonResult.failed(ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, safeMessage);
    }

    /**
     * 处理运行时异常
     *
     * <p>专门处理RuntimeException及其子类，提供更精确的错误处理</p>
     *
     * @param runtimeException 运行时异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(RuntimeException.class)
    public CommonResult<String> handleSystemRuntimeException(RuntimeException runtimeException, HttpServletRequest httpRequest) {
        String traceId = TraceIdUtils.getOrGenerateTraceId(httpRequest);
        log.error("运行时异常 [TraceId: {}]: 请求路径={}, 异常信息={}", traceId, httpRequest.getRequestURI(), runtimeException.getMessage(), runtimeException);
        recordExceptionLogSafely(httpRequest, runtimeException);
        return CommonResult.failed(ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, SYSTEM_PROCESS_ERROR);
    }

    /**
     * 处理非法状态异常
     *
     * <p>当对象状态不符合方法调用要求时抛出此异常</p>
     *
     * @param illegalStateException 非法状态异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(IllegalStateException.class)
    public CommonResult<String> handleSystemIllegalStateException(IllegalStateException illegalStateException, HttpServletRequest httpRequest) {
        log.warn("非法状态异常: 请求路径={}, 异常信息={}", httpRequest.getRequestURI(), illegalStateException.getMessage());
        return CommonResult.failed(ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, REQUEST_STATE_ERROR);
    }

    /**
     * 处理空指针异常
     *
     * <p>空指针异常通常表示程序逻辑错误，不应将详细信息暴露给客户端</p>
     *
     * @param nullPointerException 空指针异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(NullPointerException.class)
    public CommonResult<String> handleSystemNullPointerException(NullPointerException nullPointerException, HttpServletRequest httpRequest) {
        log.error("空指针异常: 请求路径={}, 异常位置={}", httpRequest.getRequestURI(), nullPointerException.getStackTrace()[0], nullPointerException);
        recordExceptionLogSafely(httpRequest, nullPointerException);
        return CommonResult.failed(ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, SYSTEM_PROCESS_ERROR_CONTACT_ADMIN);
    }

    /**
     * 处理超时异常
     *
     * <p>处理各种超时相关的异常，如网络超时、数据库连接超时等</p>
     *
     * @param timeoutException 超时异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(TimeoutException.class)
    public CommonResult<String> handleSystemTimeoutException(TimeoutException timeoutException, HttpServletRequest httpRequest) {
        log.warn("超时异常: 请求路径={}, 超时信息={}", httpRequest.getRequestURI(), timeoutException.getMessage());
        return CommonResult.failed(ResultCode.SYSTEM_FREQUENT_REQUEST, SYSTEM_TIMEOUT);
    }

    // ================================ Web层异常处理 ================================
    /**
     * 处理HTTP请求方法不支持异常
     *
     * <p>当客户端使用了不支持的HTTP方法时抛出此异常</p>
     *
     * @param methodNotSupportedException HTTP请求方法不支持异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public CommonResult<String> handleWebMethodNotSupportedException(HttpRequestMethodNotSupportedException methodNotSupportedException, HttpServletRequest httpRequest) {
        log.warn("不支持的HTTP方法: 请求路径={}, 请求方法={}, 支持的方法={}", httpRequest.getRequestURI(), httpRequest.getMethod(), methodNotSupportedException.getSupportedMethods());

        String supportedMethods = methodNotSupportedException.getSupportedMethods() != null ? String.join(", ", methodNotSupportedException.getSupportedMethods()) : "未知";
        return CommonResult.failed(ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, String.format("不支持的请求方法，支持的方法: %s", supportedMethods));
    }

    /**
     * 处理HTTP媒体类型不支持异常
     *
     * <p>当客户端发送的Content-Type不被支持时抛出此异常</p>
     *
     * @param mediaTypeNotSupportedException HTTP媒体类型不支持异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public CommonResult<String> handleWebMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException mediaTypeNotSupportedException, HttpServletRequest httpRequest) {
        log.warn("不支持的媒体类型: 请求路径={}, Content-Type={}", httpRequest.getRequestURI(), httpRequest.getContentType());
        return CommonResult.failed(ResultCode.REQUEST_BUSSINESS_PARAM_FAIL, "不支持的媒体类型，请检查Content-Type");
    }

    /**
     * 处理HTTP消息不可读异常
     *
     * <p>当请求体无法解析时抛出此异常，通常是JSON格式错误</p>
     *
     * @param messageNotReadableException HTTP消息不可读异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public CommonResult<String> handleWebMessageNotReadableException(HttpMessageNotReadableException messageNotReadableException, HttpServletRequest httpRequest) {
        log.warn("HTTP消息不可读: 请求路径={}, 异常信息={}", httpRequest.getRequestURI(), messageNotReadableException.getMessage());
        String errorMessage = "请求数据格式错误";
        if (messageNotReadableException.getMessage() != null && messageNotReadableException.getMessage().contains("JSON")) {
            errorMessage = "JSON格式错误，请检查请求数据";
        }

        return CommonResult.failed(ResultCode.REQUEST_BUSSINESS_PARAM_FAIL, errorMessage);
    }

    /**
     * 处理缺少请求参数异常
     *
     * <p>当必需的请求参数缺失时抛出此异常</p>
     *
     * @param missingParameterException 缺少请求参数异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public CommonResult<String> handleWebMissingParameterException(MissingServletRequestParameterException missingParameterException, HttpServletRequest httpRequest) {
        log.warn("缺少请求参数: 请求路径={}, 参数名={}, 参数类型={}", httpRequest.getRequestURI(), missingParameterException.getParameterName(), missingParameterException.getParameterType());
        return CommonResult.failed(ResultCode.REQUEST_BUSSINESS_PARAM_FAIL, String.format("缺少必需参数: %s", missingParameterException.getParameterName()));
    }

    /**
     * 处理类型不匹配异常
     *
     * <p>当请求参数类型与期望类型不匹配时抛出此异常</p>
     *
     * @param typeMismatchException 类型不匹配异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler({TypeMismatchException.class, MethodArgumentTypeMismatchException.class})
    public CommonResult<String> handleWebTypeMismatchException(TypeMismatchException typeMismatchException, HttpServletRequest httpRequest) {
        log.warn("参数类型不匹配: 请求路径={}, 参数值={}, 期望类型={}", httpRequest.getRequestURI(), typeMismatchException.getValue(), typeMismatchException.getRequiredType());
        String parameterName = typeMismatchException instanceof MethodArgumentTypeMismatchException ? ((MethodArgumentTypeMismatchException) typeMismatchException).getName() : "未知参数";
        return CommonResult.failed(ResultCode.REQUEST_BUSSINESS_PARAM_FAIL, String.format("参数 %s 类型错误", parameterName));
    }

    /**
     * 处理404异常（资源未找到）
     *
     * <p>当请求的资源不存在时抛出此异常</p>
     *
     * @param noHandlerFoundException 资源未找到异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public CommonResult<String> handleWebNoHandlerFoundException(NoHandlerFoundException noHandlerFoundException, HttpServletRequest httpRequest) {
        log.warn("资源未找到: 请求路径={}, HTTP方法={}", httpRequest.getRequestURI(), httpRequest.getMethod());

        return CommonResult.failed(ResultCode.RETURN_MESSAGE_RECORD_NOT_FOUND, "请求的资源不存在");
    }

    // ================================ 数据层异常处理 ================================

    /**
     * 处理数据访问异常
     *
     * <p>处理所有数据库访问相关的异常，包括连接异常、SQL异常等</p>
     * <p>这类异常通常包含敏感的数据库信息，不应暴露给客户端</p>
     *
     * @param dataAccessException 数据访问异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(DataAccessException.class)
    public CommonResult<String> handleDatabaseAccessException(DataAccessException dataAccessException, HttpServletRequest httpRequest) {
        log.error("数据访问异常: 请求路径={}, 异常类型={}, 异常信息={}", httpRequest.getRequestURI(), dataAccessException.getClass().getSimpleName(), dataAccessException.getMessage(), dataAccessException);
        recordExceptionLogSafely(httpRequest, dataAccessException);
        return CommonResult.failed(ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, "数据处理异常，请稍后重试");
    }

    /**
     * 处理SQL异常
     *
     * <p>处理底层SQL执行异常，这类异常包含敏感的数据库信息</p>
     *
     * @param sqlException SQL异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(SQLException.class)
    public CommonResult<String> handleDatabaseSQLException(SQLException sqlException, HttpServletRequest httpRequest) {
        log.error("SQL异常: 请求路径={}, SQL状态={}, 错误代码={}, 异常信息={}", httpRequest.getRequestURI(), sqlException.getSQLState(), sqlException.getErrorCode(), sqlException.getMessage(), sqlException);
        recordExceptionLogSafely(httpRequest, sqlException);
        return CommonResult.failed(ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, "数据库操作异常，请联系管理员");
    }

    /**
     * 处理数据完整性违反异常
     *
     * <p>当数据库约束被违反时抛出此异常，如唯一约束、外键约束等</p>
     *
     * @param integrityViolationException 数据完整性违反异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(DataIntegrityViolationException.class)
    public CommonResult<String> handleDatabaseIntegrityViolationException(DataIntegrityViolationException integrityViolationException, HttpServletRequest httpRequest) {
        log.warn("数据完整性违反: 请求路径={}, 异常信息={}", httpRequest.getRequestURI(), integrityViolationException.getMessage());
        String userMessage = analyzeDataIntegrityError(integrityViolationException.getMessage());
        return CommonResult.failed(ResultCode.REQUEST_BUSSINESS_PARAM_FAIL, userMessage);
    }

    /**
     * 处理重复键异常
     *
     * <p>当插入重复的唯一键值时抛出此异常</p>
     *
     * @param duplicateKeyException 重复键异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(DuplicateKeyException.class)
    public CommonResult<String> handleDatabaseDuplicateKeyException(DuplicateKeyException duplicateKeyException, HttpServletRequest httpRequest) {
        log.warn("重复键异常: 请求路径={}, 异常信息={}", httpRequest.getRequestURI(), duplicateKeyException.getMessage());
        return CommonResult.failed(ResultCode.SYSTEM_USER_NAME_EXIST, "数据已存在，请检查后重试");
    }

    // ================================ 序列化和IO异常处理 ================================

    /**
     * 处理JSON序列化异常和IO异常
     *
     * @param serializationException 序列化或IO异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(value = {JsonProcessingException.class, IOException.class})
    public CommonResult<String> handleSerializationException(Exception serializationException, HttpServletRequest httpRequest) {
        log.error("序列化异常: 请求路径={}, 异常类型={}, 异常信息={}",
                 httpRequest.getRequestURI(), serializationException.getClass().getSimpleName(), serializationException.getMessage(), serializationException);

        String errorMessage = "数据处理异常";
        if (serializationException instanceof JsonProcessingException) {
            errorMessage = "JSON数据格式错误，请检查请求数据";
        } else if (serializationException instanceof IOException) {
            errorMessage = "数据读取异常，请重试";
        }

        return CommonResult.failed(ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, errorMessage);
    }

    // ================================ 安全相关异常处理 ================================

    /**
     * 处理访问拒绝异常
     *
     * <p>当用户没有足够权限访问资源时抛出此异常</p>
     * <p>需要记录安全审计日志，但不暴露权限配置信息</p>
     *
     * @param accessDeniedException 访问拒绝异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(AccessDeniedException.class)
    public CommonResult<String> handleSecurityAccessDeniedException(AccessDeniedException accessDeniedException, HttpServletRequest httpRequest) {
        String userName = SecurityUtils.getUserName();
        String userAgent = httpRequest.getHeader("User-Agent");
        String clientIp = RequestIpUtils.getIpAddress(httpRequest);
        log.warn("访问拒绝: 用户[{}] 从IP[{}] 使用[{}] 访问[{}] 被拒绝", userName, clientIp, userAgent, httpRequest.getRequestURI());
        recordSecurityEvent(httpRequest, "ACCESS_DENIED", userName, clientIp);
        return CommonResult.failed(ResultCode.FORBIDDEN, "权限不足，无法访问该资源");
    }

    /**
     * 处理认证异常
     *
     * <p>处理各种认证相关的异常</p>
     *
     * @param authenticationException 认证异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(AuthenticationException.class)
    public CommonResult<String> handleSecurityAuthenticationException(AuthenticationException authenticationException, HttpServletRequest httpRequest) {
        String clientIp = RequestIpUtils.getIpAddress(httpRequest);
        log.warn("认证异常: IP[{}] 访问[{}] 认证失败, 异常类型={}, 异常信息={}", clientIp, httpRequest.getRequestURI(), authenticationException.getClass().getSimpleName(), authenticationException.getMessage());
        recordSecurityEvent(httpRequest, "AUTHENTICATION_FAILED", "anonymous", clientIp);
        return CommonResult.failed(ResultCode.UNAUTHORIZED, "认证失败，请重新登录");
    }

    /**
     * 处理凭据错误异常
     *
     * <p>当用户名或密码错误时抛出此异常</p>
     *
     * @param badCredentialsException 凭据错误异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(BadCredentialsException.class)
    public CommonResult<String> handleSecurityBadCredentialsException(BadCredentialsException badCredentialsException, HttpServletRequest httpRequest) {
        String clientIp = RequestIpUtils.getIpAddress(httpRequest);
        log.warn("凭据错误: IP[{}] 登录凭据错误", clientIp);
        recordSecurityEvent(httpRequest, "BAD_CREDENTIALS", "unknown", clientIp);
        return CommonResult.failed(ResultCode.UNAUTHORIZED, "用户名或密码错误");
    }

    /**
     * 处理认证不足异常
     *
     * <p>当用户认证信息不足时抛出此异常</p>
     *
     * @param insufficientAuthException 认证不足异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(InsufficientAuthenticationException.class)
    public CommonResult<String> handleSecurityInsufficientAuthenticationException(InsufficientAuthenticationException insufficientAuthException, HttpServletRequest httpRequest) {
        String clientIp = RequestIpUtils.getIpAddress(httpRequest);
        log.warn("认证不足: IP[{}] 访问[{}] 认证信息不足", clientIp, httpRequest.getRequestURI());

        return CommonResult.failed(ResultCode.UNAUTHORIZED, "认证信息不足，请完整登录");
    }

    /**
     * 处理认证凭据未找到异常
     *
     * <p>当请求中缺少认证凭据时抛出此异常</p>
     *
     * @param credentialsNotFoundException 认证凭据未找到异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(AuthenticationCredentialsNotFoundException.class)
    public CommonResult<String> handleSecurityCredentialsNotFoundException(AuthenticationCredentialsNotFoundException credentialsNotFoundException, HttpServletRequest httpRequest) {
        String clientIp = RequestIpUtils.getIpAddress(httpRequest);
        log.warn("认证凭据未找到: IP[{}] 访问[{}] 缺少认证凭据", clientIp, httpRequest.getRequestURI());

        return CommonResult.failed(ResultCode.UNAUTHORIZED, "请提供有效的认证凭据");
    }

    // ================================ 业务异常处理 ================================

    /**
     * 处理API异常
     *
     * <p>处理自定义的API异常，通常是业务逻辑相关的异常</p>
     *
     * @param apiException API异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(value = ApiException.class)
    public CommonResult<String> handleBusinessApiException(ApiException apiException, HttpServletRequest httpRequest) {
        log.warn("API异常: 请求路径={}, 异常信息={}", httpRequest.getRequestURI(), apiException.getMessage());
        if (apiException.getErrorCode() != null) {
            return CommonResult.failed(apiException.getErrorCode());
        }
        return CommonResult.failed(apiException.getMessage());
    }

    /**
     * 处理服务异常
     *
     * <p>处理业务服务层抛出的异常，通常是业务逻辑错误</p>
     *
     * @param serviceException 服务异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(value = ServiceException.class)
    public CommonResult<String> handleBusinessServiceException(ServiceException serviceException, HttpServletRequest httpRequest) {
        log.warn("服务异常: 请求路径={}, 异常信息={}", httpRequest.getRequestURI(), serviceException.getMessage());
        return CommonResult.failed(serviceException.getMessage());
    }

    /**
     * 处理客户异常
     *
     * <p>处理客户相关的业务异常，包含错误码和错误信息</p>
     *
     * @param customerException 客户异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(value = CustomerException.class)
    public CommonResult<String> handleBusinessCustomerException(CustomerException customerException, HttpServletRequest httpRequest) {
        log.warn("客户异常: 请求路径={}, 错误码=[{}], 异常信息={}", httpRequest.getRequestURI(), customerException.getCode(), customerException.getMessage());
        return CommonResult.failed(customerException.getCode(), customerException.getMessage());
    }

    /**
     * 处理用户密码不匹配异常
     *
     * <p>处理用户密码验证失败的异常，出于安全考虑不返回详细信息</p>
     *
     * @param passwordNotMatchException 用户密码不匹配异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(value = UserPasswordNotMatchException.class)
    public CommonResult<String> handleUserPasswordNotMatchException(UserPasswordNotMatchException passwordNotMatchException, HttpServletRequest httpRequest) {
        String clientIp = RequestIpUtils.getIpAddress(httpRequest);
        log.warn("用户密码验证失败: 请求路径={}, 客户端IP={}, 异常信息={}", httpRequest.getRequestURI(), clientIp, passwordNotMatchException.getMessage());

        // 记录安全事件
        recordSecurityEvent(httpRequest, "PASSWORD_MISMATCH", safeGetUserName(), clientIp);

        return CommonResult.failed(ResultCode.REQUEST_BUSSINESS_PARAM_FAIL, "密码验证失败");
    }

    // ================================ 参数验证异常处理 ================================

    /**
     * 处理方法参数验证异常
     *
     * <p>当使用@Valid注解的参数验证失败时抛出此异常</p>
     *
     * @param methodArgumentNotValidException 方法参数验证异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public CommonResult<String> handleValidationMethodArgumentNotValidException(MethodArgumentNotValidException methodArgumentNotValidException, HttpServletRequest httpRequest) {
        log.warn("参数验证失败: 请求路径={}, 验证错误={}", httpRequest.getRequestURI(), methodArgumentNotValidException.getBindingResult().getErrorCount());

        String message = extractValidationErrorMessage(methodArgumentNotValidException.getBindingResult());
        return CommonResult.failed(ResultCode.REQUEST_BUSSINESS_PARAM_FAIL, "参数验证失败: " + message);
    }

    /**
     * 处理约束验证异常
     *
     * <p>当Bean Validation约束验证失败时抛出此异常</p>
     *
     * @param constraintViolationException 约束验证异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public CommonResult<String> handleValidationConstraintViolationException(ConstraintViolationException constraintViolationException, HttpServletRequest httpRequest) {
        log.warn("约束验证失败: 请求路径={}, 违反约束数量={}",
                httpRequest.getRequestURI(), constraintViolationException.getConstraintViolations().size());

        Set<ConstraintViolation<?>> violations = constraintViolationException.getConstraintViolations();
        String message = violations.stream()
                .limit(3) // 最多显示3个错误
                .map(violation -> violation.getPropertyPath() + ": " + violation.getMessage())
                .collect(Collectors.joining("; "));

        return CommonResult.failed(ResultCode.REQUEST_BUSSINESS_PARAM_FAIL, "约束验证失败: " + message);
    }

    /**
     * 处理参数绑定异常
     *
     * <p>当请求参数无法正确绑定到方法参数时抛出此异常</p>
     *
     * @param bindException 参数绑定异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(value = BindException.class)
    public CommonResult<String> handleValidationBindException(BindException bindException, HttpServletRequest httpRequest) {
        log.warn("参数绑定失败: 请求路径={}, 绑定错误={}", httpRequest.getRequestURI(), bindException.getBindingResult().getErrorCount());
        String message = extractValidationErrorMessage(bindException.getBindingResult());
        return CommonResult.failed(ResultCode.REQUEST_BUSSINESS_PARAM_BIND_FAIL, "参数绑定失败: " + message);
    }

    // ================================ 文件相关异常处理 ================================

    /**
     * 处理文件大小超限异常
     *
     * <p>当上传的文件大小超过限制时抛出此异常</p>
     *
     * @param fileSizeLimitExceededException 文件大小超限异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(value = FileSizeLimitExceededException.class)
    public CommonResult<String> handleFileSizeLimitExceededException(FileSizeLimitExceededException fileSizeLimitExceededException, HttpServletRequest httpRequest) {
        log.warn("文件大小超限: 请求路径={}, 异常信息={}", httpRequest.getRequestURI(), fileSizeLimitExceededException.getMessage());

        return CommonResult.failed(ResultCode.RETURN_MESSAGE_FILE_SIZE_LIMIT_EXCEEDED, "文件大小超限，请检查文件大小");
    }

    /**
     * 处理最大上传大小超限异常
     *
     * <p>当上传的文件总大小超过Spring Boot配置的最大值时抛出此异常</p>
     *
     * @param maxUploadSizeExceededException 最大上传大小超限异常对象
     * @param httpRequest HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public CommonResult<String> handleFileMaxUploadSizeExceededException(MaxUploadSizeExceededException maxUploadSizeExceededException, HttpServletRequest httpRequest) {
        log.warn("上传大小超限: 请求路径={}, 异常信息={}", httpRequest.getRequestURI(), maxUploadSizeExceededException.getMessage());
        return CommonResult.failed(ResultCode.RETURN_MESSAGE_FILE_SIZE_LIMIT_EXCEEDED, "上传文件总大小超限，请检查文件大小");
    }

    // ================================ 私有工具方法 ================================

    /**
     * 提取验证错误信息
     *
     * <p>从BindingResult中提取字段验证错误信息，格式化为用户友好的消息</p>
     *
     * @param bindingResult 绑定结果，包含验证错误信息
     * @return 格式化的错误信息字符串
     */
    private String extractValidationErrorMessage(BindingResult bindingResult) {
        if (bindingResult == null || !bindingResult.hasErrors()) {
            return "参数验证失败";
        }

        List<FieldError> fieldErrors = bindingResult.getFieldErrors();
        if (fieldErrors.isEmpty()) {
            return "参数验证失败";
        }

        // 收集所有字段错误信息，最多显示3个，避免错误信息过长
        return fieldErrors.stream()
                .limit(3)
                .map(error -> {
                    String field = error.getField();
                    String message = error.getDefaultMessage();
                    // 过滤敏感字段名
                    field = sanitizeFieldName(field);
                    return String.format("%s: %s", field, message);
                })
                .collect(Collectors.joining("; "));
    }

    /**
     * 安全地记录异常日志到数据库
     *
     * <p>异步记录异常日志，确保日志记录失败不会影响主业务流程</p>
     * <p>包含完整的异常上下文信息，用于问题排查和系统监控</p>
     *
     * @param httpRequest HTTP请求对象，包含请求上下文信息
     * @param exceptionObject 异常对象，包含异常详细信息
     */
    private void recordExceptionLogSafely(HttpServletRequest httpRequest, Exception exceptionObject) {
        if (!ENABLE_EXCEPTION_LOGGING) {
            return;
        }

        try {
            // 获取异常基本信息
            String stackTraceException = Throwables.getStackTraceAsString(exceptionObject);
            String exceptionMessage = truncateMessage(exceptionObject.getMessage(), "异常信息为空");
            String requestUrl = httpRequest.getRequestURI();

            // 获取用户信息（安全处理，避免获取用户信息时出错）
            String systemUserId = safeGetUserId();
            String userName = safeGetUserName();
            String ipAddress = RequestIpUtils.getIpAddress(httpRequest);

            // 获取请求详细信息
            String userAgent = httpRequest.getHeader("User-Agent");
            String requestMethod = httpRequest.getMethod();

            // 构建描述信息，包含关键上下文
            String description = String.format(
                "异常类型: %s, 请求方法: %s, 请求路径: %s, 用户代理: %s",
                exceptionObject.getClass().getSimpleName(), requestMethod, requestUrl,
                truncateMessage(userAgent, "未知"));

            // 创建异常日志对象
            SystemExceptionLog systemExceptionLog = new SystemExceptionLog(
                    systemUserId, userName, description, requestUrl,
                    ipAddress, stackTraceException, exceptionMessage);

            // 异步记录异常日志，不阻塞主流程
            AsyncTaskManager.ownerTask().execute(
                    AsyncTaskFactory.insertSystemExceptionLog(systemExceptionLog));

        } catch (Exception logException) {
            // 记录日志失败时，只记录到应用日志，避免影响主流程
            log.error("记录异常日志失败: 原始异常={}, 日志异常={}",
                     exceptionObject.getClass().getSimpleName(), logException.getMessage(), logException);
        }
    }

    /**
     * 记录安全事件
     *
     * <p>记录安全相关的事件，用于安全审计和监控</p>
     *
     * @param httpRequest HTTP请求对象
     * @param eventType 事件类型
     * @param userName 用户名
     * @param clientIp 客户端IP
     */
    private void recordSecurityEvent(HttpServletRequest httpRequest, String eventType, String userName, String clientIp) {
        try {
            String requestPath = httpRequest != null ? httpRequest.getRequestURI() : "unknown";
            String userAgent = httpRequest != null ? httpRequest.getHeader("User-Agent") : "unknown";
            String description = String.format("安全事件: %s", eventType);

            // 使用安全审计服务记录事件
            if (securityAuditService != null) {
                securityAuditService.recordEvent(eventType, userName, clientIp, requestPath, description);
            }

            // 同时记录到日志
            log.warn("安全事件记录: 事件类型={}, 用户={}, IP={}, 路径={}, UserAgent={}, 时间={}", eventType, userName, clientIp, requestPath, userAgent, java.time.LocalDateTime.now());

        } catch (Exception securityException) {
            log.error("记录安全事件失败: eventType={}, userName={}", eventType, userName, securityException);
        }
    }

    /**
     * 判断是否为敏感异常
     *
     * <p>敏感异常不应该将详细信息返回给客户端</p>
     *
     * @param exceptionObject 异常对象
     * @return true表示敏感异常，false表示普通异常
     */
    private boolean isSensitiveException(Exception exceptionObject) {
        if (exceptionObject == null) {
            return true;
        }

        // 检查异常类型是否在敏感列表中
        return SENSITIVE_EXCEPTIONS.stream()
                .anyMatch(sensitiveType -> sensitiveType.isAssignableFrom(exceptionObject.getClass()));
    }

    /**
     * 截断消息长度
     *
     * <p>防止过长的错误消息影响响应性能和安全性</p>
     *
     * @param message 原始消息
     * @param defaultMessage 默认消息（当原始消息为空时使用）
     * @return 截断后的消息
     */
    private String truncateMessage(String message, String defaultMessage) {
        if (message == null || message.trim().isEmpty()) {
            return defaultMessage;
        }

        if (message.length() > MAX_ERROR_MESSAGE_LENGTH) {
            return message.substring(0, MAX_ERROR_MESSAGE_LENGTH - 3) + "...";
        }

        return message;
    }



    /**
     * 安全地获取用户ID
     *
     * <p>安全地获取当前用户ID，避免在获取过程中出现异常</p>
     *
     * @return 用户ID，获取失败时返回"unknown"
     */
    private String safeGetUserId() {
        try {
            String userId = SecurityUtils.getUserIdValue();
            return userId != null ? userId : "unknown";
        } catch (Exception e) {
            log.debug("获取用户ID失败", e);
            return "unknown";
        }
    }

    /**
     * 安全地获取用户名
     *
     * <p>安全地获取当前用户名，避免在获取过程中出现异常</p>
     *
     * @return 用户名，获取失败时返回"anonymous"
     */
    private String safeGetUserName() {
        try {
            String userName = SecurityUtils.getUserName();
            return userName != null ? userName : "anonymous";
        } catch (Exception e) {
            log.debug("获取用户名失败", e);
            return "anonymous";
        }
    }

    /**
     * 过滤敏感字段名
     *
     * <p>过滤可能包含敏感信息的字段名</p>
     *
     * @param fieldName 原始字段名
     * @return 过滤后的字段名
     */
    private String sanitizeFieldName(String fieldName) {
        if (fieldName == null) {
            return "unknown";
        }

        // 过滤敏感字段名
        String lowerFieldName = fieldName.toLowerCase();
        if (lowerFieldName.contains("password") ||
            lowerFieldName.contains("secret") ||
            lowerFieldName.contains("token") ||
            lowerFieldName.contains("key")) {
            return "***";
        }

        return fieldName;
    }

    /**
     * 分析数据完整性错误
     *
     * <p>分析数据库约束违反的具体类型，提供用户友好的错误信息</p>
     *
     * @param errorMessage 原始错误信息
     * @return 用户友好的错误信息
     */
    private String analyzeDataIntegrityError(String errorMessage) {
        if (errorMessage == null) {
            return "数据操作失败";
        }

        String lowerMessage = errorMessage.toLowerCase();

        if (lowerMessage.contains("duplicate entry") || lowerMessage.contains("unique")) {
            return "数据已存在，请检查后重试";
        } else if (lowerMessage.contains("foreign key")) {
            return "数据关联错误，请检查相关数据";
        } else if (lowerMessage.contains("not null")) {
            return "必填字段不能为空";
        } else if (lowerMessage.contains("check constraint")) {
            return "数据格式不符合要求";
        } else if (lowerMessage.contains("length")) {
            return "数据长度超出限制";
        }

        return "数据操作失败，请检查输入数据";
    }



    /**
     * 获取异常统计信息
     *
     * <p>获取当前的异常统计信息，用于监控和告警</p>
     *
     * @return 异常统计信息
     */
    public static long getExceptionCount() {
        return EXCEPTION_COUNTER.get();
    }

    /**
     * 重置异常计数器
     *
     * <p>重置异常计数器，通常在定期统计后调用</p>
     */
    public static void resetExceptionCounter() {
        EXCEPTION_COUNTER.set(0);
    }

    // ================================ 链路追踪辅助方法 ================================

    /**
     * 记录异常日志并生成TraceId
     *
     * <p>统一处理异常日志记录和TraceId生成，便于问题排查</p>
     * <p>在集群环境中，TraceId是定位问题的关键信息</p>
     *
     * @param httpRequest HTTP请求对象
     * @param exceptionObject 异常对象
     * @param level 日志级别 (ERROR, WARN, INFO)
     * @param message 自定义消息
     * @return 生成的TraceId
     */
    private String logExceptionWithTraceId(HttpServletRequest httpRequest, Exception exceptionObject,
                                          String level, String message) {
        // 生成或获取TraceId
        String traceId = TraceIdUtils.getOrGenerateTraceId(httpRequest);

        // 构建日志消息
        String logMessage = String.format("%s [TraceId: %s]: 请求路径=%s, 异常类型=%s, 异常信息=%s",
                                         message, traceId, httpRequest.getRequestURI(),
                                         exceptionObject.getClass().getSimpleName(), exceptionObject.getMessage());

        // 根据日志级别记录日志
        switch (level.toUpperCase()) {
            case "ERROR":
                log.error(logMessage, exceptionObject);
                break;
            case "WARN":
                log.warn(logMessage, exceptionObject);
                break;
            case "INFO":
                log.info(logMessage, exceptionObject);
                break;
            default:
                log.error(logMessage, exceptionObject);
        }

        return traceId;
    }

    /**
     * 记录ERROR级别异常日志
     *
     * @param httpRequest HTTP请求对象
     * @param exception 异常对象
     * @param message 自定义消息
     * @return 生成的TraceId
     */
    private String logError(HttpServletRequest httpRequest, Exception exception, String message) {
        return logExceptionWithTraceId(httpRequest, exception, "ERROR", message);
    }

    /**
     * 记录WARN级别异常日志
     *
     * @param httpRequest HTTP请求对象
     * @param exceptionObject 异常对象
     * @param customMessage 自定义消息
     * @return 生成的TraceId
     */
    private String logWarn(HttpServletRequest httpRequest, Exception exceptionObject, String customMessage) {
        return logExceptionWithTraceId(httpRequest, exceptionObject, "WARN", customMessage);
    }

    // ================================ 异常监控告警方法 ================================

    /**
     * 检查异常频率并触发告警
     *
     * <p>当异常频率超过阈值时，自动发送短信和邮件告警通知</p>
     * <p>包含异常统计信息、系统状态、处理建议等详细内容</p>
     *
     * @param currentExceptionCount 当前异常总数
     */
    private void checkExceptionRateAndTriggerAlert(long currentExceptionCount) {
        try {
            if (exceptionAlertService != null) {
                exceptionAlertService.checkExceptionRateAndAlert(currentExceptionCount, 1, EXCEPTION_THRESHOLD_PER_MINUTE);
            }
        } catch (Exception alertException) {
            log.error("异常告警检查失败: currentCount={}", currentExceptionCount, alertException);
        }
    }

    /**
     * 获取异常统计信息
     *
     * <p>提供异常频率、总数等统计数据，用于监控和分析</p>
     *
     * @return 异常统计信息
     */
    public ExceptionAlertService.ExceptionStatistics getExceptionStatistics() {
        try {
            if (exceptionAlertService != null) {
                return exceptionAlertService.getExceptionStatistics(5);
            }
        } catch (Exception statisticsException) {
            log.error("获取异常统计信息失败", statisticsException);
        }
        return new ExceptionAlertService.ExceptionStatistics(EXCEPTION_COUNTER.get(), 0, "Unknown", "5分钟");
    }

    /**
     * 手动触发异常告警测试
     *
     * <p>用于测试告警功能是否正常工作</p>
     * <p>仅在开发和测试环境中使用</p>
     *
     * @param testMessage 测试消息
     * @return 告警发送结果
     */
    public boolean triggerTestAlert(String testMessage) {
        try {
            if (exceptionAlertService != null) {
                long currentCount = EXCEPTION_COUNTER.get();
                exceptionAlertService.checkExceptionRateAndAlert(currentCount + EXCEPTION_THRESHOLD_PER_MINUTE + 1, 1, EXCEPTION_THRESHOLD_PER_MINUTE);
                log.info("手动触发测试告警: message={}, currentCount={}", testMessage, currentCount);
                return true;
            }
        } catch (Exception testException) {
            log.error("触发测试告警失败: message={}", testMessage, testException);
        }
        return false;
    }
}
