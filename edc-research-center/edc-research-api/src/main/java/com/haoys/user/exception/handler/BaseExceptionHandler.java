package com.haoys.user.exception.handler;

import com.google.common.base.Throwables;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.ip.RequestIpUtils;
import com.haoys.user.common.trace.TraceIdUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.exception.alert.ExceptionAlertService;
import com.haoys.user.manager.AsyncTaskManager;
import com.haoys.user.manager.factory.AsyncTaskFactory;
import com.haoys.user.model.SystemExceptionLog;
import com.haoys.user.security.audit.SecurityAuditService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 异常处理器基类
 *
 * <p>提供异常处理的通用功能和工具方法</p>
 * <p>包含异常日志记录、安全审计、追踪ID生成等核心功能</p>
 * <p>所有具体的异常处理器都应继承此基类</p>
 * <p>保留原始GlobalExceptionHandler的所有审计日志功能</p>
 *
 * <h3>核心功能：</h3>
 * <ul>
 *   <li><b>异常日志记录</b>：统一记录异常信息到数据库和日志文件</li>
 *   <li><b>安全审计</b>：记录安全相关事件，支持风险评估</li>
 *   <li><b>追踪ID管理</b>：生成和管理请求追踪ID，便于问题排查</li>
 *   <li><b>敏感信息过滤</b>：自动过滤敏感异常信息，防止信息泄露</li>
 *   <li><b>异常告警</b>：支持异常频率监控和告警通知</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
@Slf4j
public abstract class BaseExceptionHandler {

    // ================================ 常量定义 ================================

    /**
     * 最大错误消息长度限制
     * 防止过长的错误消息影响响应性能和安全性
     */
    protected static final int MAX_ERROR_MESSAGE_LENGTH = 200;

    /**
     * 异常日志记录开关
     * 可通过配置控制是否记录异常到数据库
     */
    protected static final boolean ENABLE_EXCEPTION_LOGGING = true;

    /**
     * 敏感异常类型集合
     * 这些异常类型不应将详细信息返回给客户端
     */
    protected static final Set<Class<? extends Exception>> SENSITIVE_EXCEPTIONS = new HashSet<>(Arrays.asList(
            NullPointerException.class,
            ClassCastException.class,
            IllegalStateException.class,
            SecurityException.class,
            java.sql.SQLException.class,
            org.springframework.dao.DataAccessException.class,
            java.lang.reflect.InvocationTargetException.class,
            java.lang.NoSuchMethodException.class,
            java.lang.ClassNotFoundException.class
    ));

    // ================================ 依赖注入 ================================

    /**
     * 异常告警服务
     * 用于异常频率监控和告警通知
     */
    @Autowired(required = false)
    protected ExceptionAlertService exceptionAlertService;

    /**
     * 安全审计服务
     * 用于记录安全相关事件和风险评估
     */
    @Autowired(required = false)
    protected SecurityAuditService securityAuditService;

    // ================================ 核心工具方法 ================================

    /**
     * 安全地记录异常日志
     *
     * <p>异步记录异常信息到数据库，不影响主业务流程性能</p>
     * <p>包含完整的请求上下文信息，便于问题排查</p>
     * <p>保留原始GlobalExceptionHandler的审计日志功能</p>
     *
     * @param httpRequest HTTP请求对象，用于获取请求上下文
     * @param exceptionObject 异常对象，包含异常详细信息
     */
    protected void recordExceptionLogSafely(HttpServletRequest httpRequest, Exception exceptionObject) {
        if (!ENABLE_EXCEPTION_LOGGING || httpRequest == null || exceptionObject == null) {
            return;
        }

        try {
            // 获取异常基本信息
            String stackTraceException = Throwables.getStackTraceAsString(exceptionObject);
            String exceptionMessage = truncateMessage(exceptionObject.getMessage(), "异常信息为空");
            String requestUrl = httpRequest.getRequestURI();

            // 获取用户信息（安全处理，避免获取用户信息时出错）
            String systemUserId = safeGetUserId();
            String userName = safeGetUserName();
            String ipAddress = RequestIpUtils.getIpAddress(httpRequest);

            // 获取请求详细信息
            String userAgent = httpRequest.getHeader("User-Agent");
            String requestMethod = httpRequest.getMethod();

            // 构建描述信息，包含关键上下文
            String description = String.format(
                "异常类型: %s, 请求方法: %s, 请求路径: %s, 用户代理: %s",
                exceptionObject.getClass().getSimpleName(), requestMethod, requestUrl,
                truncateMessage(userAgent, "未知"));

            // 创建异常日志对象
            SystemExceptionLog systemExceptionLog = new SystemExceptionLog(
                    systemUserId, userName, description, requestUrl,
                    ipAddress, stackTraceException, exceptionMessage);

            // 异步记录异常日志，不阻塞主流程
            AsyncTaskManager.ownerTask().execute(
                    AsyncTaskFactory.insertSystemExceptionLog(systemExceptionLog));

        } catch (Exception logException) {
            // 记录日志失败时，只记录到应用日志，避免影响主流程
            log.error("记录异常日志失败: 原始异常={}, 日志异常={}",
                     exceptionObject.getClass().getSimpleName(), logException.getMessage(), logException);
        }
    }

    /**
     * 记录安全事件
     *
     * <p>记录安全相关的事件，用于安全审计和监控</p>
     *
     * @param httpRequest HTTP请求对象
     * @param eventType 事件类型
     * @param userName 用户名
     * @param clientIp 客户端IP
     */
    protected void recordSecurityEvent(HttpServletRequest httpRequest, String eventType, String userName, String clientIp) {
        try {
            String requestPath = httpRequest != null ? httpRequest.getRequestURI() : "unknown";
            String userAgent = httpRequest != null ? httpRequest.getHeader("User-Agent") : "unknown";
            String description = String.format("安全事件: %s", eventType);

            // 使用安全审计服务记录事件
            if (securityAuditService != null) {
                securityAuditService.recordEvent(eventType, userName, clientIp, requestPath, description);
            }

            // 同时记录到日志
            log.warn("安全事件记录: 事件类型={}, 用户={}, IP={}, 路径={}, UserAgent={}, 时间={}", 
                    eventType, userName, clientIp, requestPath, userAgent, LocalDateTime.now());

        } catch (Exception securityException) {
            log.error("记录安全事件失败: eventType={}, userName={}", eventType, userName, securityException);
        }
    }

    /**
     * 判断是否为敏感异常
     *
     * <p>敏感异常不应该将详细信息返回给客户端</p>
     *
     * @param exceptionObject 异常对象
     * @return true表示敏感异常，false表示普通异常
     */
    protected boolean isSensitiveException(Exception exceptionObject) {
        if (exceptionObject == null) {
            return true;
        }

        // 检查异常类型是否在敏感列表中
        return SENSITIVE_EXCEPTIONS.stream()
                .anyMatch(sensitiveType -> sensitiveType.isAssignableFrom(exceptionObject.getClass()));
    }

    /**
     * 截断消息长度
     *
     * <p>防止过长的错误消息影响响应性能</p>
     *
     * @param message 原始消息
     * @param defaultMessage 默认消息（当原始消息为空时使用）
     * @return 截断后的消息
     */
    protected String truncateMessage(String message, String defaultMessage) {
        if (message == null || message.trim().isEmpty()) {
            return defaultMessage != null ? defaultMessage : "未知错误";
        }

        String trimmedMessage = message.trim();
        if (trimmedMessage.length() <= MAX_ERROR_MESSAGE_LENGTH) {
            return trimmedMessage;
        }

        return trimmedMessage.substring(0, MAX_ERROR_MESSAGE_LENGTH - 3) + "...";
    }

    /**
     * 安全地获取用户ID
     *
     * <p>避免在获取用户信息时出现异常</p>
     *
     * @return 用户ID，获取失败时返回"unknown"
     */
    protected String safeGetUserId() {
        try {
            Long userId = SecurityUtils.getUserId();
            return userId != null ? userId.toString() : "unknown";
        } catch (Exception e) {
            return "unknown";
        }
    }

    /**
     * 安全地获取用户名
     *
     * <p>避免在获取用户信息时出现异常</p>
     *
     * @return 用户名，获取失败时返回"anonymous"
     */
    protected String safeGetUserName() {
        try {
            return SecurityUtils.getUserName();
        } catch (Exception e) {
            return "anonymous";
        }
    }

    /**
     * 记录异常日志并生成TraceId
     *
     * <p>统一处理异常日志记录和TraceId生成，便于问题排查</p>
     * <p>在集群环境中，TraceId是定位问题的关键信息</p>
     *
     * @param httpRequest HTTP请求对象
     * @param exceptionObject 异常对象
     * @param level 日志级别 (ERROR, WARN, INFO)
     * @param message 自定义消息
     * @return 生成的TraceId
     */
    protected String logExceptionWithTraceId(HttpServletRequest httpRequest, Exception exceptionObject,
                                          String level, String message) {
        // 生成或获取TraceId
        String traceId = TraceIdUtils.getOrGenerateTraceId(httpRequest);

        // 构建日志消息
        String logMessage = String.format("%s [TraceId: %s]: 请求路径=%s, 异常类型=%s, 异常信息=%s",
                                         message, traceId, httpRequest.getRequestURI(),
                                         exceptionObject.getClass().getSimpleName(), exceptionObject.getMessage());

        // 根据级别记录日志
        switch (level.toUpperCase()) {
            case "ERROR":
                log.error(logMessage, exceptionObject);
                break;
            case "WARN":
                log.warn(logMessage, exceptionObject);
                break;
            case "INFO":
                log.info(logMessage);
                break;
            default:
                log.error(logMessage, exceptionObject);
        }

        // 记录异常日志到数据库
        recordExceptionLogSafely(httpRequest, exceptionObject);

        return traceId;
    }

    /**
     * 创建带TraceId的失败响应
     *
     * <p>统一创建包含TraceId的错误响应，便于问题追踪</p>
     *
     * @param httpRequest HTTP请求对象
     * @param resultCode 结果码
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 包含TraceId的失败响应
     */
    protected <T> CommonResult<T> createFailedResponseWithTraceId(HttpServletRequest httpRequest, 
                                                                 ResultCode resultCode, String message) {
        String traceId = TraceIdUtils.getOrGenerateTraceId(httpRequest);
        CommonResult<T> result = CommonResult.failed(resultCode, message);
        return result.withTraceId(traceId);
    }

    /**
     * 创建带TraceId的失败响应（仅消息）
     *
     * @param httpRequest HTTP请求对象
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 包含TraceId的失败响应
     */
    protected <T> CommonResult<T> createFailedResponseWithTraceId(HttpServletRequest httpRequest, String message) {
        return createFailedResponseWithTraceId(httpRequest, ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, message);
    }
}
