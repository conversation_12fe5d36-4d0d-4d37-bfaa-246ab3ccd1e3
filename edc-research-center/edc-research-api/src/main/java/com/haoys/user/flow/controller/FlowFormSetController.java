package com.haoys.user.flow.controller;

import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.domain.param.flow.FlowFormExpandParam;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.domain.param.flow.FlowFormSetParam;
import com.haoys.user.domain.vo.FlowFormSetVo;
import com.haoys.user.service.FlowFormSetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "流程配置-配置研究流程")
@RestController
@RequestMapping("flowFormSet")
public class FlowFormSetController {

    @Autowired
    private FlowFormSetService formSetService;

//
//    @ApiOperation("研究流程-保存")
//    @Log(title = "研究流程-保存", businessType = BusinessType.INSERT)
//    @PostMapping("sv")
//    public CommonResult<Object> sv(@RequestBody List<FlowFormSetParam> params) {
//        return formSetService.save(params);
//    }


    @ApiOperation("研究流程-列表")
    @GetMapping("list")
    public CommonResult<List<FlowFormSetVo>> list(Long projectId,Long planId) {
        return formSetService.list(projectId,planId);
    }

    @ApiOperation("研究流程-选中")
    @Log(title = "研究流程-选中", businessType = BusinessType.INSERT)
    @PostMapping("checkOne")
    public CommonResult<Object> checkOne(@RequestBody FlowFormSetParam param) {
        if (param.getIsCheck()){
            return formSetService.saveOne(param);
        }else {
            return formSetService.delete(param);
        }
    }
    
    @ApiOperation("患者端-新增不良事件和合并用药表单")
    @Log(title = "患者端-研究流程-新增不良事件和合并用药表单", businessType = BusinessType.INSERT)
    @PostMapping("saveFormExpand")
    public CommonResult<Object> saveFormExpand(@RequestBody FlowFormExpandParam flowFormExpandParam) {
        if (!flowFormExpandParam.getRemoveFormExpand()){
            return formSetService.saveFormExpand(flowFormExpandParam);
        }else {
            return formSetService.deleteFormExpand(flowFormExpandParam);
        }
    }

}
