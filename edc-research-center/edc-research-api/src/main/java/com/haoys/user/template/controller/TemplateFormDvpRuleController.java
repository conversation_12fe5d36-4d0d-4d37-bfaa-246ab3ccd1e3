package com.haoys.user.template.controller;

import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.model.TemplateFormDvpRule;
import com.haoys.user.service.TemplateFormDvpRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "逻辑配置")
@RequestMapping("/dvpRule")
public class TemplateFormDvpRuleController {

    @Resource
    private TemplateFormDvpRuleService templateFormDvpRuleService;

    @ApiOperation("逻辑配置-新增逻辑")
    @Log(title = "逻辑配置-新增逻辑", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public CommonResult<Object> add(@RequestBody TemplateFormDvpRule dvpRule) {
        if (dvpRule.getId()!=null){
            return templateFormDvpRuleService.modify(dvpRule);
        }else {
            return templateFormDvpRuleService.add(dvpRule);
        }
    }

    @ApiOperation("逻辑配置-编辑逻辑")
    @Log(title = "逻辑配置-编辑逻辑", businessType = BusinessType.UPDATE)
    @PostMapping("modify")
    public CommonResult<Object> modify(@RequestBody TemplateFormDvpRule dvpRule) {
        return templateFormDvpRuleService.modify(dvpRule);
    }

    @ApiOperation("逻辑配置-启用停用")
    @Log(title = "逻辑配置-启用停用", businessType = BusinessType.UPDATE)
    @GetMapping("enable")
    public CommonResult<Object> enable(Long ruleId) {
        return templateFormDvpRuleService.enable(ruleId);
    }

    @ApiOperation("逻辑配置-列表")
    @Log(title = "逻辑配置-列表", businessType = BusinessType.OTHER)
    @GetMapping("list")
    public CommonPage<Object> list(Integer pageNum, Integer pageSize, String ruleType, String checkName, String projectId) {
        return templateFormDvpRuleService.list(pageNum,pageSize,ruleType,checkName,projectId);
    }

    @ApiOperation("逻辑配置-删除")
    @Log(title = "逻辑配置-删除", businessType = BusinessType.DELETE)
    @GetMapping("remove")
    public CommonResult<Object> remove(Long ruleId) {
        return templateFormDvpRuleService.remove(ruleId);
    }


    @ApiOperation("逻辑配置-表单字段规则查询")
    @Log(title = "逻辑配置-表单字段规则查询", businessType = BusinessType.OTHER)
    @GetMapping("search")
    public List<TemplateFormDvpRule> search(@RequestBody TemplateFormDvpRule dvpRule) {
        return templateFormDvpRuleService.searchRules(dvpRule);
    }

}
