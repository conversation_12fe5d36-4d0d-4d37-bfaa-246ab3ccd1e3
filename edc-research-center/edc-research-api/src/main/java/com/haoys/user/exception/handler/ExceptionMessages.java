package com.haoys.user.exception.handler;

/**
 * 异常处理错误信息常量类
 * 
 * <p>统一管理异常处理中使用的错误信息，提高可维护性和一致性</p>
 * <p>按照异常类型分类组织，便于查找和修改</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0
 */
public final class ExceptionMessages {

    private ExceptionMessages() {
        // 工具类，禁止实例化
    }

    // ================================ 系统级异常信息 ================================

    /**
     * 系统繁忙信息
     */
    public static final String SYSTEM_BUSY = "系统繁忙，请稍后重试";

    /**
     * 系统异常信息
     */
    public static final String SYSTEM_ERROR = "系统异常，请联系管理员";

    /**
     * 系统处理异常信息
     */
    public static final String SYSTEM_PROCESS_ERROR = "系统处理异常，请稍后重试";

    /**
     * 系统处理异常（需要联系管理员）
     */
    public static final String SYSTEM_PROCESS_ERROR_CONTACT_ADMIN = "系统处理异常，请联系管理员";

    /**
     * 系统处理超时信息
     */
    public static final String SYSTEM_TIMEOUT = "系统处理超时，请稍后重试";

    // ================================ 请求相关异常信息 ================================

    /**
     * 请求状态异常信息
     */
    public static final String REQUEST_STATE_ERROR = "请求状态异常，请重新操作";

    /**
     * 请求数据格式错误信息
     */
    public static final String REQUEST_DATA_FORMAT_ERROR = "请求数据格式错误";

    /**
     * JSON格式错误信息
     */
    public static final String JSON_FORMAT_ERROR = "JSON格式错误，请检查请求数据";

    /**
     * 不支持的媒体类型信息
     */
    public static final String UNSUPPORTED_MEDIA_TYPE = "不支持的媒体类型，请检查Content-Type";

    /**
     * 请求资源不存在信息
     */
    public static final String RESOURCE_NOT_FOUND = "请求的资源不存在";

    // ================================ 参数相关异常信息 ================================

    /**
     * 参数验证失败信息模板
     */
    public static final String PARAM_VALIDATION_FAILED_TEMPLATE = "参数验证失败: %s";

    /**
     * 约束验证失败信息模板
     */
    public static final String CONSTRAINT_VALIDATION_FAILED_TEMPLATE = "约束验证失败: %s";

    /**
     * 参数绑定失败信息模板
     */
    public static final String PARAM_BINDING_FAILED_TEMPLATE = "参数绑定失败: %s";

    /**
     * 缺少必需参数信息模板
     */
    public static final String MISSING_REQUIRED_PARAM_TEMPLATE = "缺少必需参数: %s";

    /**
     * 参数类型错误信息模板
     */
    public static final String PARAM_TYPE_ERROR_TEMPLATE = "参数 %s 类型错误";

    /**
     * 不支持的请求方法信息模板
     */
    public static final String UNSUPPORTED_METHOD_TEMPLATE = "不支持的请求方法，支持的方法: %s";

    // ================================ 数据库相关异常信息 ================================

    /**
     * 数据处理异常信息
     */
    public static final String DATA_PROCESS_ERROR = "数据处理异常，请稍后重试";

    /**
     * 数据库操作异常信息
     */
    public static final String DATABASE_OPERATION_ERROR = "数据库操作异常，请联系管理员";

    /**
     * 数据已存在信息
     */
    public static final String DATA_ALREADY_EXISTS = "数据已存在，请检查后重试";

    /**
     * 数据读取异常信息
     */
    public static final String DATA_READ_ERROR = "数据读取异常，请重试";

    // ================================ 安全相关异常信息 ================================

    /**
     * 权限不足信息
     */
    public static final String INSUFFICIENT_PERMISSIONS = "权限不足，无法访问该资源";

    /**
     * 认证失败信息
     */
    public static final String AUTHENTICATION_FAILED = "认证失败，请重新登录";

    /**
     * 用户名或密码错误信息
     */
    public static final String INVALID_CREDENTIALS = "用户名或密码错误";

    /**
     * 认证信息不足信息
     */
    public static final String INSUFFICIENT_AUTHENTICATION = "认证信息不足，请完整登录";

    /**
     * 请提供有效认证凭据信息
     */
    public static final String PROVIDE_VALID_CREDENTIALS = "请提供有效的认证凭据";

    /**
     * 密码验证失败信息
     */
    public static final String PASSWORD_VALIDATION_FAILED = "密码验证失败";

    // ================================ 文件相关异常信息 ================================

    /**
     * 文件大小超限信息
     */
    public static final String FILE_SIZE_EXCEEDED = "文件大小超限，请检查文件大小";

    /**
     * 上传文件总大小超限信息
     */
    public static final String UPLOAD_SIZE_EXCEEDED = "上传文件总大小超限，请检查文件大小";

    // ================================ 业务相关异常信息 ================================

    /**
     * 业务处理异常信息（通用）
     */
    public static final String BUSINESS_PROCESS_ERROR = "业务处理异常，请稍后重试";

    /**
     * 服务暂时不可用信息
     */
    public static final String SERVICE_UNAVAILABLE = "服务暂时不可用，请稍后重试";

    // ================================ 序列化相关异常信息 ================================

    /**
     * JSON数据格式错误信息（详细）
     */
    public static final String JSON_DATA_FORMAT_ERROR = "JSON数据格式错误，请检查请求数据";

    /**
     * 数据序列化异常信息
     */
    public static final String DATA_SERIALIZATION_ERROR = "数据序列化异常，请重试";

    // ================================ 工具方法 ================================

    /**
     * 格式化参数验证失败信息
     *
     * @param details 验证失败详情
     * @return 格式化后的错误信息
     */
    public static String formatParamValidationFailed(String details) {
        return String.format(PARAM_VALIDATION_FAILED_TEMPLATE, details);
    }

    /**
     * 格式化约束验证失败信息
     *
     * @param details 验证失败详情
     * @return 格式化后的错误信息
     */
    public static String formatConstraintValidationFailed(String details) {
        return String.format(CONSTRAINT_VALIDATION_FAILED_TEMPLATE, details);
    }

    /**
     * 格式化参数绑定失败信息
     *
     * @param details 绑定失败详情
     * @return 格式化后的错误信息
     */
    public static String formatParamBindingFailed(String details) {
        return String.format(PARAM_BINDING_FAILED_TEMPLATE, details);
    }

    /**
     * 格式化缺少必需参数信息
     *
     * @param paramName 参数名称
     * @return 格式化后的错误信息
     */
    public static String formatMissingRequiredParam(String paramName) {
        return String.format(MISSING_REQUIRED_PARAM_TEMPLATE, paramName);
    }

    /**
     * 格式化参数类型错误信息
     *
     * @param paramName 参数名称
     * @return 格式化后的错误信息
     */
    public static String formatParamTypeError(String paramName) {
        return String.format(PARAM_TYPE_ERROR_TEMPLATE, paramName);
    }

    /**
     * 格式化不支持的请求方法信息
     *
     * @param supportedMethods 支持的方法列表
     * @return 格式化后的错误信息
     */
    public static String formatUnsupportedMethod(String supportedMethods) {
        return String.format(UNSUPPORTED_METHOD_TEMPLATE, supportedMethods);
    }
}
