package com.haoys.user.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.common.core.domain.model.LoginUserInfo;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.domain.dto.AiChatRequestDto;
import com.haoys.user.domain.entity.AiChatSession;
import com.haoys.user.domain.entity.AiModelConfig;
import com.haoys.user.domain.vo.AiChatResponseVo;
import com.haoys.user.domain.vo.AiChatSessionVo;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.model.SystemUserInfo;
import com.haoys.user.service.AiChatService;
import com.haoys.user.service.SecureTokenService;
import com.haoys.user.service.SystemUserInfoService;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.util.JwtTokenHelper;
import com.haoys.user.domain.vo.SecureTokenVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Map;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * AI聊天控制器
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Slf4j
@Api(tags = "AI智能对话管理")
@RestController
@RequestMapping("/ai/chat")
public class AiChatController extends BaseController {

    /**
     * 匿名用户每日使用限制
     */
    private static final int ANONYMOUS_DAILY_LIMIT = 20;

    @Autowired
    private AiChatService aiChatService;
    
    @Autowired
    private JwtTokenHelper jwtTokenHelper;
    
    @Autowired
    private RedisTemplateService redisTemplateService;
    
    @Autowired
    private SecureTokenService secureTokenService;
    
    @Autowired
    private SystemUserInfoService systemUserInfoService;
    
    @ApiOperation(value = "发送聊天消息(一次性响应)")
    @Log(title = "AI聊天", businessType = BusinessType.OTHER)
    @PostMapping("/send")
    public CommonResult<AiChatResponseVo> sendMessage(@Valid @RequestBody AiChatRequestDto request, HttpServletRequest httpRequest) {
        // 直接调用Service层，限制检查已在Service层实现
        return aiChatService.sendMessage(request);
    }
    
    @ApiOperation(value = "发送聊天消息(流式响应)")
    @Log(title = "AI流式聊天", businessType = BusinessType.OTHER)
    @PostMapping("/stream")
    public SseEmitter sendMessageStream(@Valid @RequestBody AiChatRequestDto request, HttpServletRequest httpRequest) {
        // 直接调用Service层，限制检查已在Service层实现
        return aiChatService.sendMessageStream(request);
    }

    @ApiOperation(value = "发送聊天消息(流式响应-GET)")
    @Log(title = "AI流式聊天", businessType = BusinessType.OTHER)
    @GetMapping("/stream")
    public SseEmitter sendMessageStreamGet(
            @ApiParam(value = "会话ID") @RequestParam(required = false) String sessionId,
            @ApiParam(value = "消息内容", required = true) @RequestParam String content,
            @ApiParam(value = "模型类型") @RequestParam(required = false) String modelType,
            @ApiParam(value = "模型名称") @RequestParam(required = false) String modelName,
            @ApiParam(value = "是否流式") @RequestParam(defaultValue = "true") Boolean stream,
            HttpServletRequest httpRequest) {

        // 构建请求对象
        AiChatRequestDto request = new AiChatRequestDto();
        request.setSessionId(sessionId);
        request.setContent(content);
        request.setModelType(modelType);
        request.setModelName(modelName);
        request.setStream(stream);

        // 直接调用Service层，限制检查已在Service层实现
        return aiChatService.sendMessageStream(request);
    }
    
    @ApiOperation(value = "上传文件并解析")
    @Log(title = "AI文档解析", businessType = BusinessType.OTHER)
    @PostMapping("/upload")
    public CommonResult<AiChatResponseVo> uploadFile(
            @ApiParam(value = "会话ID", required = false) @RequestParam(required = false) String sessionId,
            @ApiParam(value = "文件", required = true) @RequestParam("file") MultipartFile file,
            @ApiParam(value = "问题描述", required = false) @RequestParam(required = false) String question) {
        return aiChatService.uploadAndParseFile(sessionId, file, question);
    }
    
    @ApiOperation(value = "创建新会话")
    @Log(title = "创建AI会话", businessType = BusinessType.INSERT)
    @PostMapping("/session/create")
    public CommonResult<AiChatSession> createSession(
            @ApiParam(value = "会话标题") @RequestParam(required = false) String title,
            @ApiParam(value = "模型类型") @RequestParam(required = false) String modelType,
            @ApiParam(value = "模型名称") @RequestParam(required = false) String modelName) {
        return aiChatService.createSession(title, modelType, modelName);
    }
    
    @ApiOperation(value = "获取会话详情")
    @GetMapping("/session/{sessionId}")
    public CommonResult<AiChatSessionVo> getSession(
            @ApiParam(value = "会话ID", required = true) @PathVariable String sessionId) {
        return aiChatService.getSession(sessionId);
    }
    
    @ApiOperation(value = "获取用户会话列表")
    @GetMapping("/sessions")
    public CommonResult<CommonPage<AiChatSessionVo>> getUserSessions(
            @ApiParam(value = "用户ID") @RequestParam(required = false) String userId,
            @ApiParam(value = "页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam(value = "页大小") @RequestParam(defaultValue = "10") Integer pageSize) {
        return aiChatService.getUserSessions(userId, pageNum, pageSize);
    }
    
    @ApiOperation(value = "获取会话消息历史")
    @GetMapping("/session/{sessionId}/messages")
    public CommonResult<List<AiChatResponseVo.MessageInfo>> getSessionMessages(
            @ApiParam(value = "会话ID", required = true) @PathVariable String sessionId,
            @ApiParam(value = "页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam(value = "页大小") @RequestParam(defaultValue = "20") Integer pageSize) {
        return aiChatService.getSessionMessages(sessionId, pageNum, pageSize);
    }
    
    @ApiOperation(value = "结束会话")
    @Log(title = "结束AI会话", businessType = BusinessType.UPDATE)
    @PutMapping("/session/{sessionId}/end")
    public CommonResult<Void> endSession(
            @ApiParam(value = "会话ID", required = true) @PathVariable String sessionId) {
        return aiChatService.endSession(sessionId);
    }
    
    @ApiOperation(value = "删除会话")
    @Log(title = "删除AI会话", businessType = BusinessType.DELETE)
    @DeleteMapping("/session/{sessionId}")
    public CommonResult<Void> deleteSession(
            @ApiParam(value = "会话ID", required = true) @PathVariable String sessionId) {
        return aiChatService.deleteSession(sessionId);
    }
    
    @ApiOperation(value = "获取可用模型列表")
    @GetMapping("/models")
    public CommonResult<List<AiModelConfig>> getAvailableModels() {
        return aiChatService.getAvailableModels();
    }
    
    @ApiOperation(value = "获取用户Token使用统计")
    @GetMapping("/usage/user")
    public CommonResult<Map<String, Object>> getUserTokenUsage(
            @ApiParam(value = "用户ID") @RequestParam(required = false) String userId,
            @ApiParam(value = "统计周期(today/month/week)") @RequestParam(defaultValue = "week") String period) {
        return aiChatService.getUserTokenUsage(userId, period);
    }
    
    @ApiOperation(value = "获取系统使用统计")
    @GetMapping("/usage/system")
    public CommonResult<Map<String, Object>> getSystemUsageStats(
            @ApiParam(value = "统计周期(today/week/month)") @RequestParam(defaultValue = "week") String period) {
        return aiChatService.getSystemUsageStats(period);
    }
    
    @ApiOperation(value = "清理超时会话")
    @Log(title = "清理超时会话", businessType = BusinessType.DELETE)
    @PostMapping("/cleanup")
    public CommonResult<Void> cleanupTimeoutSessions() {
        try {
            aiChatService.cleanupTimeoutSessions();
            return CommonResult.success(null, "清理完成");
        } catch (Exception e) {
            log.error("清理超时会话失败", e);
            return CommonResult.failed("清理失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前用户认证状态
     * 用于前端页面检测用户是否已登录
     * 使用HttpServletRequest从请求对象中获取用户的真实信息
     */
    @ApiOperation("获取当前用户认证状态")
    @GetMapping("/auth/status")
    public CommonResult<Map<String, Object>> getAuthStatus(HttpServletRequest request) {
        try {
            Map<String, Object> authInfo = new HashMap<>();
            
            // 从请求中获取用户信息
            String userId = getUserIdFromRequest(request);
            String userName = getUserNameFromRequest(request);
            String realName = getRealNameFromRequest(request);
            
            // 判断是否为真正的登录用户
            boolean isAuthenticated = StrUtil.isNotBlank(userId) && !"anonymousUser".equals(userId);
            
            // 如果未登录，使用匿名用户信息
            if (!isAuthenticated) {
                userId = "anonymousUser";
                userName = "匿名用户";
                realName = "匿名用户";
            }
            
            authInfo.put("isAuthenticated", isAuthenticated);
            authInfo.put("userId", userId);
            authInfo.put("userName", userName);
            authInfo.put("realName", realName);
            authInfo.put("userType", isAuthenticated ? "登录用户" : "匿名用户");
            authInfo.put("hasUsageLimit", !isAuthenticated); // 只有匿名用户有使用限制
            authInfo.put("dailyLimit", isAuthenticated ? -1 : 20); // 登录用户无限制(-1)，匿名用户20次
            
            // 记录认证状态检测日志
            log.debug("用户认证状态检测 - userId: {}, userName: {}, isAuthenticated: {}",
                    userId, userName, isAuthenticated);
            
            return CommonResult.success(authInfo);
        } catch (Exception e) {
            log.error("获取用户认证状态失败", e);
            return CommonResult.failed("获取认证状态失败");
        }
    }

    /**
     * 从HttpServletRequest中获取用户ID
     * 按优先级尝试多种方式获取用户ID
     */
    protected String getUserIdFromRequest(HttpServletRequest request) {
        // 1. 优先从Spring Security上下文获取
        try {
            LoginUserInfo loginUserInfo = SecurityUtils.getLoginUser();
            if (loginUserInfo != null && loginUserInfo.getId() != null) {
                return loginUserInfo.getId().toString();
            }
        } catch (Exception e) {
            // 忽略异常，继续其他方式获取
            log.debug("从Spring Security上下文获取用户ID失败: {}", e.getMessage());
        }
        
        // 2. 从JWT Token中解析用户ID
        try {
            String tokenKey = jwtTokenHelper.getTokenKeyFromRequest(request);
            if (StrUtil.isNotBlank(tokenKey)) {
                LoginUserInfo tokenUserInfo = (LoginUserInfo) redisTemplateService.get(tokenKey);
                if (tokenUserInfo != null && tokenUserInfo.getId() != null) {
                    return tokenUserInfo.getId().toString();
                }
            }
        } catch (Exception e) {
            // 忽略异常，继续其他方式获取
            log.debug("从JWT Token中获取用户ID失败: {}", e.getMessage());
        }
        
        // 3. 从Session中获取用户ID
        try {
            HttpSession session = request.getSession(false);
            if (session != null) {
                Object userId = session.getAttribute("userId");
                if (userId != null) {
                    return userId.toString();
                }
            }
        } catch (Exception e) {
            log.debug("从Session中获取用户ID失败: {}", e.getMessage());
        }
        
        // 4. 从请求头中获取用户ID
        try {
            String userId = request.getHeader("X-User-Id");
            if (StrUtil.isNotBlank(userId)) {
                return userId;
            }
        } catch (Exception e) {
            log.debug("从请求头中获取用户ID失败: {}", e.getMessage());
        }

        // 4.5. 从请求头中获取X-Access-Token并查询用户信息
        try {
            String accessToken = request.getHeader("X-Access-Token");
            if (StrUtil.isNotBlank(accessToken)) {
                String userId = getUserIdFromAccessToken(accessToken);
                if (StrUtil.isNotBlank(userId)) {
                    log.debug("从请求头X-Access-Token获取到用户ID: {}", userId);
                    return userId;
                }
            }
        } catch (Exception e) {
            log.debug("从请求头X-Access-Token获取用户ID失败: {}", e.getMessage());
        }
        
        // 5. 从Cookie中获取access_token并查询用户信息
        try {
            String accessToken = getAccessTokenFromCookie(request);
            if (StrUtil.isNotBlank(accessToken)) {
                String userId = getUserIdFromAccessToken(accessToken);
                if (StrUtil.isNotBlank(userId)) {
                    log.debug("从Cookie中的access_token获取到用户ID: {}", userId);
                    return userId;
                }
            }
        } catch (Exception e) {
            log.debug("从Cookie中的access_token获取用户ID失败: {}", e.getMessage());
        }
        
        // 6. 如果都获取不到，返回匿名用户标识
        return "anonymousUser";
    }
    
    /**
     * 从HttpServletRequest中获取用户名
     * 按优先级尝试多种方式获取用户名
     */
    protected String getUserNameFromRequest(HttpServletRequest request) {
        // 1. 优先从Spring Security上下文获取
        try {
            LoginUserInfo loginUserInfo = SecurityUtils.getLoginUser();
            if (loginUserInfo != null && StrUtil.isNotBlank(loginUserInfo.getUsername())) {
                return loginUserInfo.getUsername();
            }
        } catch (Exception e) {
            // 忽略异常，继续其他方式获取
            log.debug("从Spring Security上下文获取用户名失败: {}", e.getMessage());
        }
        
        // 2. 从JWT Token中解析用户名
        try {
            String tokenKey = jwtTokenHelper.getTokenKeyFromRequest(request);
            if (StrUtil.isNotBlank(tokenKey)) {
                LoginUserInfo tokenUserInfo = (LoginUserInfo) redisTemplateService.get(tokenKey);
                if (tokenUserInfo != null && StrUtil.isNotBlank(tokenUserInfo.getUsername())) {
                    return tokenUserInfo.getUsername();
                }
            }
        } catch (Exception e) {
            // 忽略异常，继续其他方式获取
            log.debug("从JWT Token中获取用户名失败: {}", e.getMessage());
        }
        
        // 3. 从Session中获取用户名
        try {
            HttpSession session = request.getSession(false);
            if (session != null) {
                Object userName = session.getAttribute("userName");
                if (userName != null) {
                    return userName.toString();
                }
            }
        } catch (Exception e) {
            log.debug("从Session中获取用户名失败: {}", e.getMessage());
        }
        
        // 4. 从请求头中获取用户名
        try {
            String userName = request.getHeader("X-User-Name");
            if (StrUtil.isNotBlank(userName)) {
                return userName;
            }
        } catch (Exception e) {
            log.debug("从请求头中获取用户名失败: {}", e.getMessage());
        }

        // 4.5. 从请求头中获取X-Access-Token并查询用户名
        try {
            String accessToken = request.getHeader("X-Access-Token");
            if (StrUtil.isNotBlank(accessToken)) {
                String userName = getUserNameFromAccessToken(accessToken);
                if (StrUtil.isNotBlank(userName)) {
                    log.debug("从请求头X-Access-Token获取到用户名: {}", userName);
                    return userName;
                }
            }
        } catch (Exception e) {
            log.debug("从请求头X-Access-Token获取用户名失败: {}", e.getMessage());
        }
        
        // 5. 从Cookie中获取access_token并查询用户名
        try {
            String accessToken = getAccessTokenFromCookie(request);
            if (StrUtil.isNotBlank(accessToken)) {
                String userName = getUserNameFromAccessToken(accessToken);
                if (StrUtil.isNotBlank(userName)) {
                    log.debug("从Cookie中的access_token获取到用户名: {}", userName);
                    return userName;
                }
            }
        } catch (Exception e) {
            log.debug("从Cookie中的access_token获取用户名失败: {}", e.getMessage());
        }
        
        // 6. 如果都获取不到，返回匿名用户标识
        return "匿名用户";
    }
    
    /**
     * 从HttpServletRequest中获取用户真实姓名
     * 按优先级尝试多种方式获取用户真实姓名
     */
    protected String getRealNameFromRequest(HttpServletRequest request) {
        // 1. 优先从Spring Security上下文获取
        try {
            LoginUserInfo loginUserInfo = SecurityUtils.getLoginUser();
            if (loginUserInfo != null && StrUtil.isNotBlank(loginUserInfo.getRealName())) {
                return loginUserInfo.getRealName();
            }
        } catch (Exception e) {
            // 忽略异常，继续其他方式获取
            log.debug("从Spring Security上下文获取真实姓名失败: {}", e.getMessage());
        }
        
        // 2. 从JWT Token中解析真实姓名
        try {
            String tokenKey = jwtTokenHelper.getTokenKeyFromRequest(request);
            if (StrUtil.isNotBlank(tokenKey)) {
                LoginUserInfo tokenUserInfo = (LoginUserInfo) redisTemplateService.get(tokenKey);
                if (tokenUserInfo != null && StrUtil.isNotBlank(tokenUserInfo.getRealName())) {
                    return tokenUserInfo.getRealName();
                }
            }
        } catch (Exception e) {
            // 忽略异常，继续其他方式获取
            log.debug("从JWT Token中获取真实姓名失败: {}", e.getMessage());
        }
        
        // 3. 从Session中获取真实姓名
        try {
            HttpSession session = request.getSession(false);
            if (session != null) {
                Object realName = session.getAttribute("realName");
                if (realName != null) {
                    return realName.toString();
                }
            }
        } catch (Exception e) {
            log.debug("从Session中获取真实姓名失败: {}", e.getMessage());
        }
        
        // 4. 从请求头中获取真实姓名
        try {
            String realName = request.getHeader("X-Real-Name");
            if (StrUtil.isNotBlank(realName)) {
                return realName;
            }
        } catch (Exception e) {
            log.debug("从请求头中获取真实姓名失败: {}", e.getMessage());
        }

        // 4.5. 从请求头中获取X-Access-Token并查询真实姓名
        try {
            String accessToken = request.getHeader("X-Access-Token");
            if (StrUtil.isNotBlank(accessToken)) {
                String realName = getRealNameFromAccessToken(accessToken);
                if (StrUtil.isNotBlank(realName)) {
                    log.debug("从请求头X-Access-Token获取到真实姓名: {}", realName);
                    return realName;
                }
            }
        } catch (Exception e) {
            log.debug("从请求头X-Access-Token获取真实姓名失败: {}", e.getMessage());
        }
        
        // 5. 从Cookie中获取access_token并查询真实姓名
        try {
            String accessToken = getAccessTokenFromCookie(request);
            if (StrUtil.isNotBlank(accessToken)) {
                String realName = getRealNameFromAccessToken(accessToken);
                if (StrUtil.isNotBlank(realName)) {
                    log.debug("从Cookie中的access_token获取到真实姓名: {}", realName);
                    return realName;
                }
            }
        } catch (Exception e) {
            log.debug("从Cookie中的access_token获取真实姓名失败: {}", e.getMessage());
        }
        
        // 6. 如果都获取不到，返回匿名用户标识
        return "匿名用户";
    }
    


    /**
     * 从Cookie中获取access_token
     *
     * @param request HTTP请求对象
     * @return access_token值，如果不存在返回null
     */
    private String getAccessTokenFromCookie(HttpServletRequest request) {
        if (request.getCookies() == null) {
            return null;
        }
        
        for (javax.servlet.http.Cookie cookie : request.getCookies()) {
            if ("access_token".equals(cookie.getName())) {
                String accessToken = cookie.getValue();
                log.debug("从Cookie中获取到access_token: {}", accessToken);
                return accessToken;
            }
        }
        
        return null;
    }
    
    /**
     * 根据access_token获取用户ID
     * @param accessToken 访问令牌
     * @return 用户ID，如果获取失败返回null
     */
    private String getUserIdFromAccessToken(String accessToken) {
        if (StrUtil.isBlank(accessToken)) {
            return null;
        }
        
        try {
            // 1. 首先尝试使用SecureTokenService验证和获取用户信息
            SecureTokenVo.ValidateResponse validateResponse = secureTokenService.validateAccessToken(accessToken);
            if (validateResponse != null && Boolean.TRUE.equals(validateResponse.getValid())) {
                String userId = validateResponse.getUserId();
                if (StrUtil.isNotBlank(userId)) {
                    log.debug("通过SecureTokenService从access_token获取到用户ID: {}", userId);
                    return userId;
                }
            }
        } catch (Exception e) {
            log.debug("通过SecureTokenService验证access_token失败: {}", e.getMessage());
        }
        
        try {
            // 2. 尝试使用JwtTokenHelper解析JWT格式的token
            LoginUserInfo loginUserInfo = jwtTokenHelper.getSystemLoginUserByToken(accessToken);
            if (loginUserInfo != null && loginUserInfo.getId() != null) {
                String userId = loginUserInfo.getId().toString();
                log.debug("通过JwtTokenHelper从access_token获取到用户ID: {}", userId);
                return userId;
            }
        } catch (Exception e) {
            log.debug("通过JwtTokenHelper解析access_token失败: {}", e.getMessage());
        }

        try {
            // 3. 尝试从Redis中直接查询（如果是传统的token存储方式）
            String tokenKey = "act_token:" + accessToken;
            Object userInfo = redisTemplateService.get(tokenKey);
            if (userInfo != null) {
                if (userInfo instanceof SystemUserInfo) {
                    SystemUserInfo systemUser = (SystemUserInfo) userInfo;
                    String userId = systemUser.getId().toString();
                    log.debug("从Redis直接查询access_token获取到用户ID: {}", userId);
                    return userId;
                } else if (userInfo instanceof String) {
                    // 如果存储的是JSON字符串，尝试解析
                    try {
                        SystemUserInfo systemUser = JSON.parseObject(userInfo.toString(), SystemUserInfo.class);
                        if (systemUser != null && systemUser.getId() != null) {
                            String userId = systemUser.getId().toString();
                            log.debug("从Redis JSON解析access_token获取到用户ID: {}", userId);
                            return userId;
                        }
                    } catch (Exception parseException) {
                        log.debug("解析Redis中的用户信息JSON失败: {}", parseException.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            log.debug("从Redis查询access_token失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 根据access_token获取用户名
     *
     * @param accessToken 访问令牌
     * @return 用户名，如果获取失败返回null
     */
    private String getUserNameFromAccessToken(String accessToken) {
        String userId = getUserIdFromAccessToken(accessToken);
        if (StrUtil.isBlank(userId)) {
            return null;
        }

        try {
            // 1. 首先尝试使用SecureTokenService获取扩展信息
            SecureTokenVo.ValidateResponse validateResponse = secureTokenService.validateAccessToken(accessToken);
            if (validateResponse != null && Boolean.TRUE.equals(validateResponse.getValid())) {
                String extraInfo = validateResponse.getExtraInfo();
                if (StrUtil.isNotBlank(extraInfo)) {
                    try {
                        // 尝试从扩展信息中解析用户名
                        SystemUserInfo userFromExtra = JSON.parseObject(extraInfo, SystemUserInfo.class);
                        if (userFromExtra != null && StrUtil.isNotBlank(userFromExtra.getUsername())) {
                            log.debug("从SecureToken扩展信息获取到用户名: {}", userFromExtra.getUsername());
                            return userFromExtra.getUsername();
                        }
                    } catch (Exception e) {
                        log.debug("解析SecureToken扩展信息失败: {}", e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            log.debug("通过SecureTokenService获取用户名失败: {}", e.getMessage());
        }

        try {
            // 2. 尝试从JWT Token中获取用户信息
            LoginUserInfo loginUserInfo = jwtTokenHelper.getSystemLoginUserByToken(accessToken);
            if (loginUserInfo != null && StrUtil.isNotBlank(loginUserInfo.getUsername())) {
                log.debug("从JWT Token获取到用户名: {}", loginUserInfo.getUsername());
                return loginUserInfo.getUsername();
            }
        } catch (Exception e) {
            log.debug("从JWT Token获取用户名失败: {}", e.getMessage());
        }

        try {
            // 3. 根据用户ID查询数据库获取用户信息
            SystemUserInfo userInfo = systemUserInfoService.getUserBaseInfo(Long.valueOf(userId));
            if (userInfo != null && StrUtil.isNotBlank(userInfo.getUsername())) {
                log.debug("从数据库查询获取到用户名: {}", userInfo.getUsername());
                return userInfo.getUsername();
            }
        } catch (Exception e) {
            log.debug("根据用户ID查询数据库获取用户名失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 根据access_token获取真实姓名
     *
     * @param accessToken 访问令牌
     * @return 真实姓名，如果获取失败返回null
     */
    private String getRealNameFromAccessToken(String accessToken) {
        String userId = getUserIdFromAccessToken(accessToken);
        if (StrUtil.isBlank(userId)) {
            return null;
        }

        try {
            // 1. 首先尝试使用SecureTokenService获取扩展信息
            SecureTokenVo.ValidateResponse validateResponse = secureTokenService.validateAccessToken(accessToken);
            if (validateResponse != null && Boolean.TRUE.equals(validateResponse.getValid())) {
                String extraInfo = validateResponse.getExtraInfo();
                if (StrUtil.isNotBlank(extraInfo)) {
                    try {
                        // 尝试从扩展信息中解析真实姓名
                        SystemUserInfo userFromExtra = JSON.parseObject(extraInfo, SystemUserInfo.class);
                        if (userFromExtra != null) {
                            String realName = userFromExtra.getRealName();
                            if (StrUtil.isBlank(realName)) {
                                realName = userFromExtra.getUsername(); // 如果没有真实姓名，使用用户名
                            }
                            log.debug("从SecureToken扩展信息获取到真实姓名: {}", realName);
                            return realName;
                        }
                    } catch (Exception e) {
                        log.debug("解析SecureToken扩展信息失败: {}", e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            log.debug("通过SecureTokenService获取真实姓名失败: {}", e.getMessage());
        }

        try {
            // 2. 尝试从JWT Token中获取用户信息
            LoginUserInfo loginUserInfo = jwtTokenHelper.getSystemLoginUserByToken(accessToken);
            if (loginUserInfo != null) {
                String realName = loginUserInfo.getRealName();
                if (StrUtil.isBlank(realName)) {
                    realName = loginUserInfo.getUsername(); // 如果没有真实姓名，使用用户名
                }
                log.debug("从JWT Token获取到真实姓名: {}", realName);
                return realName;
            }
        } catch (Exception e) {
            log.debug("从JWT Token获取真实姓名失败: {}", e.getMessage());
        }

        try {
            // 3. 根据用户ID查询数据库获取用户信息
            SystemUserInfo userInfo = systemUserInfoService.getUserBaseInfo(Long.valueOf(userId));
            if (userInfo != null) {
                String realName = userInfo.getRealName();
                if (StrUtil.isBlank(realName)) {
                    realName = userInfo.getUsername(); // 如果没有真实姓名，使用用户名
                }
                log.debug("从数据库查询获取到真实姓名: {}", realName);
                return realName;
            }
        } catch (Exception e) {
            log.debug("根据用户ID查询数据库获取真实姓名失败: {}", e.getMessage());
        }

        return null;
    }
}
