package com.haoys.user.task;


import com.haoys.user.model.SystemTenantUser;
import com.haoys.user.model.SystemTenantUserExample;
import com.haoys.user.service.SystemTenantUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 自动任务自动解除用户因为密码输入错误5次的锁定，判断30分钟自动解除
 */
@Slf4j
@Component
public class UserUnLockTask {


    @Autowired
    private SystemTenantUserService tenantUserService;


    /**
     * cron表达式：Seconds Minutes Hours DayofMonth Month DayofWeek [Year]
     * 定期更新任务方案
     */
    @Scheduled(cron="0 */1 * * * ?  ")
    private void userUnLock(){
        SystemTenantUserExample example = new SystemTenantUserExample();
        SystemTenantUserExample.Criteria criteria = example.createCriteria();
        criteria.andLockStatusEqualTo(true);
        List<SystemTenantUser> users = tenantUserService.getSystemTenantUserList(example);
        if (CollectionUtils.isNotEmpty(users)){
            long time = new Date().getTime();
            for (SystemTenantUser user : users) {
                // 半个小时自动解除 半个小时是30*60*1000
                if (user.getLockTime() != null && time-user.getLockTime()>=30*60*1000){
                    user.setLockTime(null);
                    user.setLockStatus(false);
                    tenantUserService.updateSystemTenantUser(user);
                }
            }
        }
    }
}


