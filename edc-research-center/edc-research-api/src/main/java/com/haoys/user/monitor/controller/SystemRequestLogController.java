package com.haoys.user.monitor.controller;

import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.domain.entity.SystemRequestLogQuery;
import com.haoys.user.service.SystemRequestLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@Api(tags = "操作日志记录")
@RestController
@RequestMapping("/request/operlog")
public class SystemRequestLogController extends BaseController {
    @Autowired
    private SystemRequestLogService systemRequestLogService;

    @ApiOperation("获取操作日志记录信息列表")
//    @PreAuthorize("@ss.hasPermi('monitor:operlog:list')")
    @GetMapping("/list")
    public CommonResult sselectRequestLogListForPage(SystemRequestLogQuery systemRequestLogQuery,
                                                     @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                     @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        CommonPage<SystemRequestLogQuery> list = systemRequestLogService.selectRequestLogListForPage(systemRequestLogQuery, pageNum, pageSize);
        return CommonResult.success(list);
    }

    @ApiOperation("删除日志")
    @Log(title = "删除日志", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('monitor:operlog:remove')")
    @DeleteMapping("/{operIds}")
    public CommonResult remove(@PathVariable Long[] operIds) {
        return returnResult(systemRequestLogService.deleteOperLogByIds(operIds));
    }

    @ApiOperation("清空操作日志记录信息")
    @Log(title = "清空操作日志记录信息", businessType = BusinessType.CLEAN)
    @PreAuthorize("@ss.hasPermi('monitor:operlog:remove')")
    @DeleteMapping("/clean")
    public CommonResult clean() {
        systemRequestLogService.cleanOperLog();
        return returnSuccess(null);
    }

    @ApiOperation("导出操作日志记录")
    @GetMapping("/exportSystemRequestLog")
    public CommonResult exportSystemRequestLog(HttpServletResponse response,SystemRequestLogQuery operLog) {
        systemRequestLogService.exportSystemOperLog(response, operLog);
        return CommonResult.success(null);
    }
}
