package com.haoys.user.exception.handler;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeoutException;

/**
 * 系统级异常处理器
 *
 * <p>专门处理系统运行时产生的各种异常</p>
 * <p>包括运行时异常、空指针异常、状态异常等系统级错误</p>
 * <p>这类异常通常包含敏感的系统信息，不应暴露给客户端</p>
 * <p>保留原始GlobalExceptionHandler的审计日志功能</p>
 *
 * <h3>处理的异常类型：</h3>
 * <ul>
 *   <li><b>运行时异常</b>：RuntimeException及其子类</li>
 *   <li><b>空指针异常</b>：NullPointerException</li>
 *   <li><b>类型转换异常</b>：ClassCastException</li>
 *   <li><b>数组越界异常</b>：IndexOutOfBoundsException</li>
 *   <li><b>非法状态异常</b>：IllegalStateException</li>
 *   <li><b>非法参数异常</b>：IllegalArgumentException</li>
 *   <li><b>超时异常</b>：TimeoutException</li>
 *   <li><b>内存不足异常</b>：OutOfMemoryError</li>
 * </ul>
 *
 * <h3>安全特性：</h3>
 * <ul>
 *   <li>敏感系统信息自动过滤，防止信息泄露</li>
 *   <li>异常堆栈信息仅记录到日志，不返回给客户端</li>
 *   <li>统一的错误响应格式，避免暴露系统内部结构</li>
 *   <li>异常频率监控，支持异常告警机制</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
@Slf4j
@RestControllerAdvice
@Order(800) // 较低优先级，作为系统级异常的兜底处理
public class SystemExceptionHandler extends BaseExceptionHandler {

    // ================================ 运行时异常处理 ================================

    /**
     * 处理空指针异常
     *
     * <p>当代码中出现空指针访问时触发</p>
     * <p>这类异常通常表示程序逻辑错误，不应暴露给客户端</p>
     *
     * @param exception 空指针异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(NullPointerException.class)
    public CommonResult<String> handleNullPointerException(NullPointerException exception, 
                                                          HttpServletRequest request) {
        String traceId = logExceptionWithTraceId(request, exception, "ERROR", 
            "空指针异常 - 系统内部错误");
        
        // 空指针异常是敏感异常，不返回详细信息给客户端
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, 
                                             "系统处理异常，请稍后重试");
    }

    /**
     * 处理类型转换异常
     *
     * <p>当类型强制转换失败时触发</p>
     * <p>通常表示程序逻辑错误或数据类型不匹配</p>
     *
     * @param exception 类型转换异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(ClassCastException.class)
    public CommonResult<String> handleClassCastException(ClassCastException exception, 
                                                        HttpServletRequest request) {
        String traceId = logExceptionWithTraceId(request, exception, "ERROR", 
            "类型转换异常 - 数据类型不匹配");
        
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, 
                                             "数据处理异常，请检查数据格式");
    }

    /**
     * 处理数组越界异常
     *
     * <p>当访问数组或集合超出边界时触发</p>
     * <p>通常表示程序逻辑错误或数据异常</p>
     *
     * @param exception 数组越界异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(IndexOutOfBoundsException.class)
    public CommonResult<String> handleIndexOutOfBoundsException(IndexOutOfBoundsException exception, 
                                                               HttpServletRequest request) {
        String traceId = logExceptionWithTraceId(request, exception, "ERROR", 
            "数组越界异常 - 数据访问超出范围");
        
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, 
                                             "数据处理异常，请稍后重试");
    }

    /**
     * 处理非法状态异常
     *
     * <p>当对象状态不符合方法调用要求时触发</p>
     * <p>通常表示业务状态错误或程序逻辑问题</p>
     *
     * @param exception 非法状态异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(IllegalStateException.class)
    public CommonResult<String> handleIllegalStateException(IllegalStateException exception, 
                                                           HttpServletRequest request) {
        String traceId = logExceptionWithTraceId(request, exception, "ERROR", 
            "非法状态异常 - 对象状态不正确");
        
        // 检查是否包含用户友好的错误信息
        String message = exception.getMessage();
        if (message != null && !isSensitiveException(exception)) {
            return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, 
                                                 truncateMessage(message, "系统状态异常"));
        }
        
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, 
                                             "系统状态异常，请稍后重试");
    }

    /**
     * 处理非法参数异常
     *
     * <p>当方法接收到非法参数时触发</p>
     * <p>可能包含用户友好的错误信息</p>
     *
     * @param exception 非法参数异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public CommonResult<String> handleIllegalArgumentException(IllegalArgumentException exception, 
                                                              HttpServletRequest request) {
        String traceId = logExceptionWithTraceId(request, exception, "WARN", 
            "非法参数异常 - 参数值不正确");
        
        // 非法参数异常可能包含用户友好的信息
        String message = exception.getMessage();
        if (message != null && !message.trim().isEmpty()) {
            return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_PARAM_FAIL, 
                                                 truncateMessage(message, "参数错误"));
        }
        
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_PARAM_FAIL, 
                                             "参数错误，请检查输入");
    }

    // ================================ 超时和资源异常处理 ================================

    /**
     * 处理超时异常
     *
     * <p>当操作执行超时时触发</p>
     * <p>通常表示系统负载过高或外部依赖响应慢</p>
     *
     * @param exception 超时异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(TimeoutException.class)
    public CommonResult<String> handleTimeoutException(TimeoutException exception, 
                                                      HttpServletRequest request) {
        String traceId = logExceptionWithTraceId(request, exception, "WARN", 
            "操作超时异常 - 处理时间过长");
        
        return createFailedResponseWithTraceId(request, ResultCode.SYSTEM_FREQUENT_REQUEST, 
                                             "操作超时，请稍后重试");
    }

    /**
     * 处理内存不足错误
     *
     * <p>当JVM内存不足时触发</p>
     * <p>这是严重的系统错误，需要立即关注</p>
     *
     * @param error 内存不足错误对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(OutOfMemoryError.class)
    public CommonResult<String> handleOutOfMemoryError(OutOfMemoryError error, 
                                                      HttpServletRequest request) {
        String traceId = logExceptionWithTraceId(request, error, "ERROR", 
            "内存不足错误 - 系统资源耗尽");
        
        // 内存不足是严重错误，需要告警
        if (exceptionAlertService != null) {
            try {
                String alertMessage = String.format("系统内存不足: 请求路径=%s, TraceId=%s", 
                                                   request.getRequestURI(), traceId);
                exceptionAlertService.sendSmsNotification(new String[]{"admin"}, alertMessage);
            } catch (Exception alertException) {
                log.error("发送内存不足告警失败", alertException);
            }
        }
        
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, 
                                             "系统资源不足，请稍后重试");
    }

    /**
     * 处理栈溢出错误
     *
     * <p>当方法调用栈溢出时触发</p>
     * <p>通常表示递归调用过深或程序逻辑错误</p>
     *
     * @param error 栈溢出错误对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(StackOverflowError.class)
    public CommonResult<String> handleStackOverflowError(StackOverflowError error, 
                                                        HttpServletRequest request) {
        String traceId = logExceptionWithTraceId(request, error, "ERROR", 
            "栈溢出错误 - 方法调用过深");
        
        // 栈溢出是严重错误，需要告警
        if (exceptionAlertService != null) {
            try {
                String alertMessage = String.format("系统栈溢出: 请求路径=%s, TraceId=%s", 
                                                   request.getRequestURI(), traceId);
                exceptionAlertService.sendSmsNotification(new String[]{"admin"}, alertMessage);
            } catch (Exception alertException) {
                log.error("发送栈溢出告警失败", alertException);
            }
        }
        
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, 
                                             "系统处理异常，请稍后重试");
    }

    /**
     * 处理通用运行时异常
     *
     * <p>处理其他未被具体处理器捕获的运行时异常</p>
     * <p>作为运行时异常的兜底处理</p>
     *
     * @param exception 运行时异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(RuntimeException.class)
    public CommonResult<String> handleRuntimeException(RuntimeException exception, 
                                                      HttpServletRequest request) {
        String traceId = logExceptionWithTraceId(request, exception, "ERROR", 
            String.format("运行时异常: 异常类型=%s", exception.getClass().getSimpleName()));
        
        // 检查是否为敏感异常
        if (isSensitiveException(exception)) {
            return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, 
                                                 "系统处理异常，请稍后重试");
        }
        
        // 非敏感异常可以返回部分信息
        String message = exception.getMessage();
        if (message != null && !message.trim().isEmpty()) {
            return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, 
                                                 truncateMessage(message, "系统处理异常"));
        }
        
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, 
                                             "系统处理异常，请稍后重试");
    }

    /**
     * 记录异常日志并生成TraceId的重写方法
     *
     * <p>重写基类方法，处理Error类型的异常</p>
     *
     * @param httpRequest HTTP请求对象
     * @param throwable 异常对象
     * @param level 日志级别
     * @param message 自定义消息
     * @return 生成的TraceId
     */
    protected String logExceptionWithTraceId(HttpServletRequest httpRequest, Throwable throwable,
                                          String level, String message) {
        // 对于Error类型，需要特殊处理
        if (throwable instanceof Error) {
            return super.logExceptionWithTraceId(httpRequest, new RuntimeException(throwable), level, message);
        } else {
            return super.logExceptionWithTraceId(httpRequest, (Exception) throwable, level, message);
        }
    }
}
