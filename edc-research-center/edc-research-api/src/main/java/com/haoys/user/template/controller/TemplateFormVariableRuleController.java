package com.haoys.user.template.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.domain.param.RuleComputeParam;
import com.haoys.user.domain.param.TemplateFormVariableRuleComputeParam;
import com.haoys.user.domain.vo.ecrf.TemplateFormVariableViewVo;
import com.haoys.user.model.TemplateFormVariableFun;
import com.haoys.user.model.TemplateFormVariableRule;
import com.haoys.user.service.TemplateConfigService;
import com.haoys.user.service.TemplateFormVariableRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api(tags = "表单配置-公式计算")
@RestController
@RequestMapping("templateFormVariableRule")
public class TemplateFormVariableRuleController {

    @Autowired
    private TemplateFormVariableRuleService ruleService;

    @Autowired
    private TemplateConfigService templateConfigService;

    @ApiOperation("获取公式配置信息")
    @GetMapping(value = "/getFormVariableRule")
    public CommonResult<TemplateFormVariableRule> getFormVariableRule(String projectId,String groupId, String formId, String variableId) {
        TemplateFormVariableRule rule = ruleService.getFormVariableRule(projectId,groupId,formId,variableId);
        return CommonResult.success(rule);
    }

    @ApiOperation("保存公式配置信息")
    @PostMapping(value = "/saveFormVariableRule")
    public CommonResult<TemplateFormVariableRule> saveFormVariableRule(@RequestBody TemplateFormVariableRule rule) {
        return ruleService.saveFormVariableRule(rule);

    }

    @ApiOperation(value = "查询表单公式配置变量集合")
    @GetMapping(value = "/formVariableRulList")
    public CommonResult<List<TemplateFormVariableViewVo>> formVariableRulList(@RequestParam(value = "projectId") String projectId,
                                                                                             @RequestParam(value = "formId") String formId) {
        List<TemplateFormVariableViewVo> dataList = templateConfigService.getFormVariableAndRuleList(projectId, formId);
        return CommonResult.success(dataList);
    }



    @ApiOperation("获取函数列表")
    @GetMapping(value = "/getFormVariableFun")
    public CommonResult<Map<Integer,List<TemplateFormVariableFun>>> getFormVariableFun() {
        return CommonResult.success(ruleService.getFormVariableFun());
    }



    @ApiOperation("公式计算")
    @PostMapping(value = "compute")
    public CommonResult<Object> compute(@RequestBody TemplateFormVariableRuleComputeParam templateFormVariableRuleComputeParam) {
        Expression compiledExp = AviatorEvaluator.compile(templateFormVariableRuleComputeParam.getRuleDetails());
        List<RuleComputeParam> variableData = templateFormVariableRuleComputeParam.getVariableData();
        Map<String, Object> params = new HashMap<>();
        if (!variableData.isEmpty()) {
            for (RuleComputeParam variableDatum : variableData) {
                String key = "$" + variableDatum.getVarName() + "$";
                Object value = variableDatum.getVarValue();
                if (BusinessConfig.PROJECT_VISIT_CRF_FORM_NUMBER.equals(variableDatum.getVarType())) {// 数值类型
                    params.put(key, Double.valueOf(value.toString()));
                } else if (BusinessConfig.PROJECT_VISIT_CRF_FORM_DATE.equals(variableDatum.getVarType())) {// 日期类型
                    params.put(key, DateUtil.parse(value.toString()));
                }else if (NumberUtil.isNumber(value.toString())) {// 判断是否是数字，如果是的话转换为double
                    params.put(key, Double.parseDouble(value.toString()));
                } else {
                    params.put(key, String.valueOf(value));
                }
            }
            Object execute = compiledExp.execute(params);
            if (execute instanceof Double) {
                BigDecimal bd = new BigDecimal(String.valueOf(execute));
                return CommonResult.success(bd.setScale(4, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
            }
            if (execute instanceof String) {
                try {
                    double v1 = Double.parseDouble(execute.toString());
                    BigDecimal bd = new BigDecimal(v1);
                    return CommonResult.success(bd.setScale(4, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
                } catch (Exception e) {
                    return CommonResult.success(execute);
                }
            }
            return CommonResult.success(execute);
        }
        return CommonResult.success("");
    }

}
