package com.haoys.user.flow.controller;

import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.model.FlowPlan;
import com.haoys.user.service.FlowPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 流程配置-创建计划控制类
 */
@Api(tags = "流程配置-创建计划")
@RestController
@RequestMapping("flowPlan")
public class FlowPlanController extends BaseController {

    @Autowired
    private FlowPlanService flowPlanService;

    @ApiOperation("创建计划")
    @Log(title = "创建计划", businessType = BusinessType.INSERT)
    @PostMapping("create")
    public CommonResult<Object> create(@RequestBody FlowPlan plan) {
        plan.setCreateTime(new Date());
        plan.setUpdateTime(new Date());
        plan.setCreateUser(getUserId());
        plan.setUpdateUser(getUserId());
        return flowPlanService.create(plan);
    }

    @ApiOperation("修改计划")
    @Log(title = "修改计划", businessType = BusinessType.UPDATE)
    @PostMapping("editPlan")
    public CommonResult<Object> editPlan(@RequestBody FlowPlan plan) {
        plan.setUpdateTime(new Date());
        plan.setUpdateUser(getUserId());
        return flowPlanService.update(plan);
    }

    @ApiOperation("删除")
    @Log(title = "删除", businessType = BusinessType.DELETE)
    @GetMapping("remove")
    public CommonResult<Object> remove(String planId) {
        return flowPlanService.delete(planId);
    }


    @ApiOperation("发布计划")
    @PostMapping("/pus")
    public CommonResult<Object> pus(@RequestBody FlowPlan plan) {
        return flowPlanService.publish(plan);
    }


    @ApiOperation("撤销发布计划")
    @GetMapping("/unPus")
    public CommonResult<Object> unPus(Long planId) {
        return flowPlanService.unPublish(planId);
    }


    @ApiOperation("计划列表")
    @PostMapping("/list")
    public CommonResult<List<FlowPlan>> getProjectFlowPlanList(Long projectId) {
        return CommonResult.success(flowPlanService.getProjectFlowPlanList(projectId));
    }


    @ApiOperation("校验是否有已经发布的流程")
    @GetMapping("/isHavPlan")
    public CommonResult<Object> isHavPlan(Long projectId) {
        return flowPlanService.isHavPlan(projectId);
    }
}
