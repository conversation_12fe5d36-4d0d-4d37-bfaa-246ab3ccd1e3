package com.haoys.user.project.controller;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.model.ProjectApplyUser;
import com.haoys.user.service.ProjectApplyUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "我的申请/我的审批")
@RestController
@RequestMapping("projectApplyUser")
public class ProjectApplyUserController {

    @Autowired
    private ProjectApplyUserService applyUserService;

    /**
     * 申请加入项目
     * @param projectId 项目id
     * @return 申请加入结果
     */
    @ApiOperation("申请加入项目")
    @GetMapping("apply")
    public CommonResult<Object> apply(Long projectId){
        return  applyUserService.apply(projectId);
    }

    /**
     * 获取申请列表
     * @return 申请列表
     */
    @ApiOperation("获取申请列表")
    @GetMapping("applyList")
    public CommonResult<Object> applyList(){
        return  applyUserService.applyList();
    }

    /**
     * 获取审批列表
     * @return 审批列表
     */
    @ApiOperation("获取审批列表")
    @GetMapping("auditList")
    public CommonResult<List<ProjectApplyUser>> auditList(){
        return  applyUserService.auditList();
    }

    /**
     * 审批通过
     * @param applyId 申请记录id
     * @return 审批通过
     */
    @ApiOperation("审批通过")
    @GetMapping("agree")
    @ApiImplicitParam(name = "applyId", value = "申请记录id", dataType = "Long",required = true)
    public CommonResult<Object> agree(Long applyId){
        return  applyUserService.audit(applyId, BusinessConfig.PROJECT_APPLY_STATUS_2);
    }

    /**
     * 审批不通过
     * @param applyId 申请记录id
     * @return 审批不通过
     */
    @ApiOperation("审批不通过")
    @ApiImplicitParam(name = "applyId", value = "申请记录id", dataType = "Long",required = true)
    @GetMapping("disagree")
    public CommonResult<Object> disagree(Long applyId){
        return  applyUserService.audit(applyId, BusinessConfig.PROJECT_APPLY_STATUS_3);
    }

    /**
     * 撤销申请
     * @param applyId 申请记录id
     * @return 撤销申请
     */
    @ApiOperation("撤销申请")
    @ApiImplicitParam(name = "applyId", value = "申请记录id", dataType = "Long",required = true)
    @GetMapping("revoke")
    public CommonResult<Object> revoke(Long applyId){
        return  applyUserService.audit(applyId, BusinessConfig.PROJECT_APPLY_STATUS_0);
    }

}
