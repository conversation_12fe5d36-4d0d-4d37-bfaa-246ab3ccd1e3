package com.haoys.user.project.controller;

import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.domain.vo.project.ProjectConfigVo;
import com.haoys.user.model.ProjectConfigBase;
import com.haoys.user.model.ProjectConfigValue;
import com.haoys.user.service.ProjectConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@Api(tags = "项目功能配置项管理")
@RequestMapping("/projectConfig")
public class ProjectConfigController extends BaseController {


    @Autowired
    private ProjectConfigService projectConfigService;

    @ApiOperation("项目配置列表")
    @GetMapping(value = "/getConfigByProjectId")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "string"),
            @ApiImplicitParam(name = "moduleName", value = "模块名称", dataType = "string"),
    })
    public CommonResult<List<ProjectConfigVo>> getConfigByProjectId(String projectId, String moduleName){
        return projectConfigService.getProjectConfigByProjectId(projectId,moduleName);
    }


    @Log(title = "保存项目配置项的值", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "保存项目配置项的值")
    @PostMapping(value = "/saveProjectConfigValue")
    public CommonResult<Object> saveProjectConfigValue(@RequestBody List<ProjectConfigValue> projectConfigValues) {
        return projectConfigService.saveProjectConfigValue(projectConfigValues);
    }


    @Log(title = "保存项目配置项", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "保存项目配置项")
    @PostMapping(value = "/saveProjectConfig")
    public CommonResult<Object> saveProjectConfig(@RequestBody ProjectConfigBase configBase) {
        return projectConfigService.saveProjectConfig(configBase);
    }




}
