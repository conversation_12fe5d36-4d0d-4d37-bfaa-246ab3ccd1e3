package com.haoys.user.exception;

/**
 * 数据不存在异常
 *
 * <p>当查询数据不存在时抛出此异常</p>
 * <p>例如：根据ID查询记录不存在、关联数据缺失等</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0
 */
public class DataNotFoundException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 数据类型
     */
    private final String dataType;

    /**
     * 查询条件
     */
    private final String queryCondition;

    /**
     * 构造函数
     *
     * @param message 异常消息
     */
    public DataNotFoundException(String message) {
        super(message);
        this.dataType = null;
        this.queryCondition = null;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param dataType 数据类型
     */
    public DataNotFoundException(String message, String dataType) {
        super(message);
        this.dataType = dataType;
        this.queryCondition = null;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param dataType 数据类型
     * @param queryCondition 查询条件
     */
    public DataNotFoundException(String message, String dataType, String queryCondition) {
        super(message);
        this.dataType = dataType;
        this.queryCondition = queryCondition;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param cause 原因异常
     */
    public DataNotFoundException(String message, Throwable cause) {
        super(message, cause);
        this.dataType = null;
        this.queryCondition = null;
    }

    /**
     * 获取数据类型
     *
     * @return 数据类型
     */
    public String getDataType() {
        return dataType;
    }

    /**
     * 获取查询条件
     *
     * @return 查询条件
     */
    public String getQueryCondition() {
        return queryCondition;
    }
}
