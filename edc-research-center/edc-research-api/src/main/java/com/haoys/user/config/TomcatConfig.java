package com.haoys.user.config;

import org.apache.catalina.connector.Connector;
import org.springframework.boot.web.embedded.tomcat.TomcatConnectorCustomizer;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Tomcat配置类
 *
 * <p>优化Tomcat连接器配置，特别针对文件上传场景</p>
 *
 * <AUTHOR>
 */
@Configuration
public class TomcatConfig {

    @Bean
    public ConfigurableServletWebServerFactory webServerFactory(){
        TomcatServletWebServerFactory factory = new TomcatServletWebServerFactory();
        factory.addConnectorCustomizers(new TomcatConnectorCustomizer() {
            @Override
            public void customize(Connector connector) {
                // 允许特殊字符在查询参数中
                connector.setProperty("relaxedQueryChars", "|[]_");

                // 文件上传优化配置
                // 设置最大POST请求大小（100MB + 1MB缓冲）
                connector.setMaxPostSize(101 * 1024 * 1024);

                // 设置最大保存POST请求大小
                connector.setMaxSavePostSize(101 * 1024 * 1024);

                // 设置连接超时时间（30秒）
                connector.setProperty("connectionTimeout", "30000");

                // 设置异步请求超时时间（5分钟，适用于大文件上传）
                connector.setAsyncTimeout(300000);

                // 启用压缩
                connector.setProperty("compression", "on");
                connector.setProperty("compressableMimeType",
                    "text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json");

                // 设置最大HTTP头大小（8KB）
                connector.setProperty("maxHttpHeaderSize", "8192");
                
                // 设置最大跟踪数
                connector.setProperty("maxTrailerSize", "8192");
                
                // 设置最大参数数量
                connector.setProperty("maxParameterCount", "10000");
                
                // 设置套接字缓冲区大小
                connector.setProperty("socketBuffer", "1024000");
                
                // 启用反向DNS查找
                connector.setProperty("enableLookups", "false");
                
                // 设置TCP无延迟
                connector.setProperty("tcpNoDelay", "true");
                
                // 设置套接字超时
                connector.setProperty("soTimeout", "30000");

                // 禁用服务器信息泄露
                connector.setProperty("server", "");
                
                // 设置拒绝连接时的错误报告值
                connector.setProperty("rejectIllegalHeader", "true");
            }
        });
        return factory;
    }
}