package com.haoys.user.participant.controller;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.domain.param.project.ProjectChallengeStatisticsParam;
import com.haoys.user.domain.vo.testee.ProjectChallengeStatisticsVo;
import com.haoys.user.service.ProjectChallengeStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(tags = "质疑统计")
@RequestMapping("/challengeStatistics")
public class ProjectChallengeStatController extends BaseController {

    @Autowired
    private ProjectChallengeStatisticsService challengeStatisticsService;

    @ApiOperation("质疑统计列表")
    @GetMapping("list")
    public CommonResult<CommonPage<ProjectChallengeStatisticsVo>> list(ProjectChallengeStatisticsParam param ){
        param.setUserId(getUserId());
        return CommonResult.success(challengeStatisticsService.list(param));
    }

    @ApiOperation("质疑统计柱状图")
    @GetMapping("challengeChart")
    public CommonResult<ProjectChallengeStatisticsVo> challengeChart(ProjectChallengeStatisticsParam param ){
        param.setUserId(getUserId());
        return CommonResult.success(challengeStatisticsService.challengeChart(param));
    }

    @ApiOperation("角色质疑统计柱状图")
    @GetMapping("challengeRoleChart")
    public CommonResult<ProjectChallengeStatisticsVo> challengeRoleChart(ProjectChallengeStatisticsParam param ){
        param.setUserId(getUserId());
        return CommonResult.success(challengeStatisticsService.challengeRoleChart(param));
    }
}
