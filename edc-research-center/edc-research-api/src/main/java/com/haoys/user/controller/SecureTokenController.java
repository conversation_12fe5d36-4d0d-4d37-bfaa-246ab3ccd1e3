package com.haoys.user.controller;

import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.domain.param.SecureTokenParam;
import com.haoys.user.domain.vo.SecureTokenVo;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.service.SecureTokenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 安全Token管理控制器
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
@Slf4j
@Api(tags = "安全Token管理")
@RestController
@RequestMapping("/secure/token")
public class SecureTokenController extends BaseController {
    
    @Autowired
    private SecureTokenService secureTokenService;
    
    @ApiOperation(value = "生成Code和RefreshCode", notes = "使用AppId和AppSecret生成验证码和刷新码")
    @Log(title = "生成Code", businessType = BusinessType.OTHER)
    @PostMapping("/generateCode")
    public CommonResult<SecureTokenVo.CodeResponse> generateCode(
            @Validated @RequestBody SecureTokenParam.GenerateCodeParam param) {
        try {
            SecureTokenVo.CodeResponse response = secureTokenService.generateCode(param);
            return CommonResult.success(response);
        } catch (Exception e) {
            log.error("生成Code失败: appId={}, environment={}, error={}",
                    param != null ? param.getAppId() : "null",
                    param != null ? param.getEnvironment() : "null",
                    e.getMessage());
            return CommonResult.failed("生成Code失败: " + e.getMessage());
        }
    }
    
    @ApiOperation(value = "根据Code和RefreshCode获取AccessToken", notes = "验证Code和RefreshCode的合法性后生成AccessToken")
    @Log(title = "获取AccessToken", businessType = BusinessType.OTHER)
    @PostMapping("/getAccessToken")
    public CommonResult<SecureTokenVo.AccessTokenResponse> getAccessToken(
            @Validated @RequestBody SecureTokenParam.GetAccessTokenParam param) {
        try {
            SecureTokenVo.AccessTokenResponse response = secureTokenService.getAccessToken(param);
            return CommonResult.success(response);
        } catch (Exception e) {
            log.error("获取AccessToken失败: code={}, refreshCode={}, error={}",
                    param != null ? param.getCode() : "null",
                    param != null ? param.getRefreshCode() : "null",
                    e.getMessage());
            return CommonResult.failed("获取AccessToken失败: " + e.getMessage());
        }
    }
    
    @ApiOperation(value = "验证AccessToken")
    @Log(title = "验证AccessToken", businessType = BusinessType.OTHER)
    @GetMapping("/validate")
    public CommonResult<SecureTokenVo.ValidateResponse> validateAccessToken(
            @RequestParam("accessToken") String accessToken) {
        try {
            SecureTokenVo.ValidateResponse response = secureTokenService.validateAccessToken(accessToken);
            return CommonResult.success(response);
        } catch (Exception e) {
            log.error("验证AccessToken失败", e);
            return CommonResult.failed("验证AccessToken失败: " + e.getMessage());
        }
    }
    
    @ApiOperation(value = "刷新AccessToken")
    @Log(title = "刷新AccessToken", businessType = BusinessType.OTHER)
    @PostMapping("/refresh")
    public CommonResult<SecureTokenVo.AccessTokenResponse> refreshAccessToken(
            @RequestParam("accessToken") String accessToken) {
        try {
            SecureTokenVo.AccessTokenResponse response = secureTokenService.refreshAccessToken(accessToken);
            return CommonResult.success(response);
        } catch (Exception e) {
            log.error("刷新AccessToken失败", e);
            return CommonResult.failed("刷新AccessToken失败: " + e.getMessage());
        }
    }
    
    @ApiOperation(value = "撤销AccessToken")
    @Log(title = "撤销AccessToken", businessType = BusinessType.OTHER)
    @PostMapping("/revoke")
    public CommonResult<Boolean> revokeAccessToken(
            @RequestParam("accessToken") String accessToken) {
        try {
            Boolean result = secureTokenService.revokeAccessToken(accessToken);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("撤销AccessToken失败", e);
            return CommonResult.failed("撤销AccessToken失败: " + e.getMessage());
        }
    }
    
    @ApiOperation(value = "清理过期Token")
    @Log(title = "清理过期Token", businessType = BusinessType.OTHER)
    @PostMapping("/cleanExpired")
    public CommonResult<Long> cleanExpiredTokens() {
        try {
            Long count = secureTokenService.cleanExpiredTokens();
            return CommonResult.success(count);
        } catch (Exception e) {
            log.error("清理过期Token失败", e);
            return CommonResult.failed("清理过期Token失败: " + e.getMessage());
        }
    }
}
