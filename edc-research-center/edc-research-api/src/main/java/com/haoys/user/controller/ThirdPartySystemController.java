package com.haoys.user.controller;


import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.service.impl.ThirdPartySystemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;


@Slf4j
@RestController
@Api(tags = "第三方系统接口管理")
@RequestMapping("/thirdPartyApi")
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class ThirdPartySystemController {
    
    
    private final ThirdPartySystemService thirdPartySystemService;
    
    @ApiOperation(value = "查询ajreport表单系统token")
    @RequestMapping(value = "/getAjreportLoginToken", method = RequestMethod.POST)
    public CommonResult<Object> getAjreportLoginToken() {
        CustomResult data = thirdPartySystemService.getAjreportLoginToken();
        return CommonResult.success(data);
    }
    
    @ApiOperation(value = "根据token查询用户信息")
    @RequestMapping(value = "/getUserLoginInfo", method = RequestMethod.POST)
    public CommonResult<Object> getUserLoginInfo(HttpServletRequest request, String token) {
        String authorization = request.getHeader("Authorization");
        if(StringUtils.isEmpty(token)){
            log.info("authorization: {}", authorization, "token: {}", token);
            token = authorization;
        }
        CustomResult data = thirdPartySystemService.getUserLoginInfo(token);
        return CommonResult.success(data);
    }
    
    @ApiOperation(value = "查询文献搜索建议词")
    @RequestMapping(value = "/getBCBISearchSuggestions", method = RequestMethod.GET)
    public CommonResult<Object> get(String searchValue) {
        CustomResult data = thirdPartySystemService.getBCBISearchSuggestions(searchValue);
        return CommonResult.success(data);
    }
    
    @ApiOperation(value = "查询文献列表")
    @RequestMapping(value = "/getBCBISearchResult", method = RequestMethod.GET)
    @ApiImplicitParams({
        @ApiImplicitParam(name = "searchValue", value = "搜索词", dataType = "string"),
        @ApiImplicitParam(name = "year", value = "搜索年份", dataType = "string"),
        @ApiImplicitParam(name = "reldate", value = "近几天", dataType = "string"),
        @ApiImplicitParam(name = "mindate", value = "搜索开始年份", dataType = "string"),
        @ApiImplicitParam(name = "maxdate", value = "搜索截止年份", dataType = "string"),
    })
    public CommonResult<Object> get(String searchValue, String year, String reldate, String mindate, String maxdate) {
        CustomResult data = thirdPartySystemService.getNCBISearchResultList(searchValue, year, reldate, mindate, maxdate);
        return CommonResult.success(data);
    }
    
    @ApiOperation(value = "查询指定文献")
    @RequestMapping(value = "/getBCBISearchSummaryResult", method = RequestMethod.GET)
    public CommonResult<Object> getBCBISearchSummaryResult(String id) {
        CustomResult data = thirdPartySystemService.getBCBISearchSummaryResult(id);
        return CommonResult.success(data);
    }
    
    @ApiOperation(value = "下载指定文献")
    @RequestMapping(value = "/getBCBISearchSummaryDownloadUrl", method = RequestMethod.GET)
    public CommonResult<Object> getBCBISearchSummaryDownloadUrl(String eLocationID) {
        CustomResult data = thirdPartySystemService.getBCBISearchSummaryDownloadUrl(eLocationID);
        return CommonResult.success(data);
    }
    
    @ApiOperation(value = "根据userId查询用户信息")
    @RequestMapping(value = "/getUserInfoByUserId", method = RequestMethod.GET)
    public CommonResult<Object> getUserInfoByUserId(String ticket) {
        CustomResult data = thirdPartySystemService.getUserInfoByUserId(ticket);
        return CommonResult.success(data);
    }

}
