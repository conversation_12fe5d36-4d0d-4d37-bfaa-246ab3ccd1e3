package com.haoys.user.participant.controller;


import com.alibaba.fastjson.JSON;
import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.annotation.NoRepeatSubmit;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.common.excel.ExcelUtil;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.crf.ProjectTesteeFormProcessParam;
import com.haoys.user.domain.param.crf.TemplateFormGroupParam;
import com.haoys.user.domain.param.project.ProjectTesteeBatchUploadParam;
import com.haoys.user.domain.param.project.ProjectTesteeCustomTableParam;
import com.haoys.user.domain.param.project.ProjectTesteeParam;
import com.haoys.user.domain.param.project.ProjectTesteeResultParam;
import com.haoys.user.domain.param.project.ProjectTesteeTableColumnParam;
import com.haoys.user.domain.param.project.ProjectTesteeTableParam;
import com.haoys.user.domain.param.testee.ProjectTesteeFillInfoParam;
import com.haoys.user.domain.vo.ecrf.ProjectFormResultVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeFormTableConfigVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeTableVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormConfigVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDetailVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormGroupVariableVo;
import com.haoys.user.domain.vo.ocr.OCRWordsResult;
import com.haoys.user.domain.vo.ocr.ReportStructuralObject;
import com.haoys.user.domain.vo.ocr.ReportStructuralProObject;
import com.haoys.user.domain.vo.participant.ProjectTesteeTableConfigWrapperVo;
import com.haoys.user.domain.vo.project.ProjectApplyOrgVo;
import com.haoys.user.domain.vo.project.ProjectTesteeOrgVo;
import com.haoys.user.domain.vo.system.OrganizationVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeExportExceptionVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeExportViewVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeFormImageVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeImportVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeWrapperVo;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.model.FlowPlan;
import com.haoys.user.model.Project;
import com.haoys.user.model.ProjectApplyUser;
import com.haoys.user.service.FlowPlanService;
import com.haoys.user.service.OrganizationService;
import com.haoys.user.service.ProjectApplyUserService;
import com.haoys.user.service.ProjectBaseManageService;
import com.haoys.user.service.ProjectFormAuditService;
import com.haoys.user.service.ProjectTesteeFileService;
import com.haoys.user.service.ProjectTesteeInfoService;
import com.haoys.user.service.ProjectTesteeTableService;
import com.haoys.user.service.TemplateFormGroupService;
import com.haoys.user.storge.cloud.OssStorageConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@Api(tags = "研究管理-参与者管理-录入数据")
@RequestMapping("/project-testee")
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class ProjectTesteeManageController extends BaseController {

    private final OssStorageConfig storageConfig;
    private final ProjectBaseManageService projectBaseManageService;
    private final ProjectApplyUserService projectApplyUserService;
    private final OrganizationService organizationService;
    private final ProjectTesteeInfoService projectTesteeInfoService;
    private final ProjectTesteeFileService projectTesteeFileService;
    private final ProjectTesteeTableService projectTesteeTableService;
    private final TemplateFormGroupService templateFormGroupService;
    private final FlowPlanService flowPlanService;
    private final ProjectFormAuditService projectFormAuditService;

    @ApiOperation(value = "查询机构列表-医学助理")
    @RequestMapping(value = "/getCustomOrgList", method = RequestMethod.GET)
    public CommonResult<List<ProjectApplyOrgVo>> getCustomOrgList(){
        List<ProjectApplyOrgVo> dataList = new ArrayList<>();
        List<OrganizationVo> userOrgList = organizationService.getUserOrgList(getUserId());
        for (OrganizationVo organizationVo : userOrgList) {
            ProjectApplyOrgVo projectApplyOrgVo = new ProjectApplyOrgVo();
            List<ProjectApplyUser> projectList = projectApplyUserService.getQueryProjectList(organizationVo.getId(), getUserId());
            if(projectList !=null && projectList.size() >0){
                ProjectApplyUser projectApplyUser = projectList.get(0);
                projectApplyOrgVo.setUserId(projectApplyUser.getUserId());
                projectApplyOrgVo.setOrgId(organizationVo.getId());
                projectApplyOrgVo.setOrgName(organizationVo.getName());
                projectApplyOrgVo.setProjectId(projectApplyUser.getProjectId());
                dataList.add(projectApplyOrgVo);
            }
        }
        return CommonResult.success(dataList);
    }

    @ApiOperation(value = "查询项目二维码 1-项目二维码 2-中心二维码")
    @RequestMapping(value = "/getProjectQRCode", method = RequestMethod.GET)
    public CommonResult<Map<String,Object>> getProjectQRCode(String projectId, String orgId, String QRCodeType){
        Map<String,Object> dataMap = new HashMap<>();
        dataMap.put("projectQRCode_url",storageConfig.getViewUrl()+ "testee/saveBindTesteeQRCode?projectId=" + projectId + "&userId=" + getUserId() + "&QRCodeType=" + QRCodeType);
        dataMap.put("projectOrg_url",storageConfig.getViewUrl()+ "testee/saveBindTesteeQRCode?projectId=" + projectId + "&orgId=" + orgId + "&userId=" + getUserId() + "&QRCodeType=" + QRCodeType);
        return CommonResult.success(dataMap);
    }


    @NoRepeatSubmit
    @Log(title = "H5患者端扫码绑定", businessType = BusinessType.INSERT)
    @ApiOperation(value = "H5患者端扫码绑定-患者自建和医生创建绑定操作")
    @RequestMapping(value = "/saveBindTesteeQRCode", method = RequestMethod.POST)
    public CommonResult<Object> saveBindTesteeQRCode(String projectId, String userId, String testeeId, String realName, String ownerOrgId, String ownerDoctorId, String selfRecord) {
        CustomResult data = projectTesteeInfoService.saveBindTesteeQRCode(projectId, userId, testeeId, realName, ownerOrgId, ownerDoctorId, selfRecord);
        return CommonResult.success(data);
    }

    @ApiOperation(value = "根据手机号查询参与者基本信息")
    @RequestMapping(value = "/getProjectTesteeByMobile", method = RequestMethod.GET)
    public CommonResult<ProjectTesteeVo> getProjectTesteeByMobile(String projectId, String mobile) {
        ProjectTesteeVo projectTesteeVo = projectTesteeInfoService.getProjectTesteeBaseInfoByMobile(projectId, mobile);
        return CommonResult.success(projectTesteeVo);
    }

    @NoRepeatSubmit
    @Log(title = "新增参与者基本信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增参与者基本信息")
    @PostMapping(value = "/saveProjectTesteeBaseInfo")
    public CommonResult<Object> saveProjectTesteeBaseInfo(@Validated @RequestBody ProjectTesteeParam projectTesteeParam) {
        projectTesteeParam.setOperator(getUserId());
        CustomResult data = projectTesteeInfoService.saveProjectTesteeBaseInfo(projectTesteeParam);
        return CommonResult.success(data);
    }


    @ApiOperation("下载参与者导入模版")
    @PostMapping("/downloadTesteeTemplate")
    public CommonResult<String> downloadTesteeTemplate(){
        String templateFile = "导入参与者模板.xlsx";
        String originalPath = storageConfig.getUploadFolder();
        downloadTemplate(originalPath, templateFile);
        String downloadTemplate = storageConfig.getViewUrl() + Constants.SYSTEM_VIEW_PATH + templateFile;
        return CommonResult.success(downloadTemplate);
    }

    @NoRepeatSubmit
    @Log(title = "批量导入参与者", businessType = BusinessType.IMPORT)
    @ApiOperation("批量导入参与者")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "string"),
    })
    @PostMapping("/saveBatchProjectTestee")
    public CommonResult saveBatchProjectTestee(MultipartFile file, String projectId) throws Exception {
        List<ProjectTesteeExportExceptionVo> errorList = new ArrayList<>();
        HashMap<Object, Object> map = new HashMap<>();
        ExcelUtil<ProjectTesteeImportVo> excelUtil = new ExcelUtil<>(ProjectTesteeImportVo.class);
        List<ProjectTesteeImportVo> dataList = excelUtil.importExcel(file.getInputStream());
        String message = projectTesteeInfoService.saveBatchProjectTesteeInfo(dataList, projectId, getUserId(), errorList);
        map.put("message",message);
        if(errorList.size() > 0){
            ExcelUtil<ProjectTesteeExportExceptionVo> exportexcelUtil = new ExcelUtil<>(ProjectTesteeExportExceptionVo.class);
            CommonResult commonResult = exportexcelUtil.exportExcel(errorList, "异常数据查看");
            map.put("url",commonResult.getData());
            return CommonResult.success(map, ResultCode.SYSTEM_IMPORT_EXCEPTION_MESSAGE);
        }
        return returnSuccess(map);
    }


    @ApiOperation(value = "查询参与者基本或扩展信息")
    @RequestMapping(value = "/getTesteeInfo", method = RequestMethod.GET)
    public CommonResult<ProjectTesteeVo> getTesteeInfo(String projectId, String id) {
        ProjectTesteeVo projectTesteeVo = projectTesteeInfoService.getProjectTesteeBaseInfo(projectId, id);
        if(StringUtils.isNotEmpty(projectId)){
            Project project = projectBaseManageService.getProjectBaseInfo(projectId);
            projectTesteeVo.setProjectName(project.getName());
        }
        return CommonResult.success(projectTesteeVo);
    }


    @ApiOperation(value = "查询参与者绑定配置信息")
    @RequestMapping(value = "/getTesteeConfig", method = RequestMethod.GET)
    public CommonResult<ProjectTesteeVo> getTesteeConfig(String projectId, String id) {
        ProjectTesteeVo projectTesteeVo = projectTesteeInfoService.getProjectTesteeBaseInfo(projectId, id);
        if(StringUtils.isNotEmpty(projectId)){
            Project project = projectBaseManageService.getProjectBaseInfo(projectId);
            projectTesteeVo.setProjectName(project.getName());
        }
        return CommonResult.success(projectTesteeVo);
    }


    @NoRepeatSubmit
    @Log(title = "修改参与者基本信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改参与者基本信息")
    @RequestMapping(value = "/modifyProjectTesteeBaseInfo", method = RequestMethod.POST)
    public CommonResult<Object> modifyProjectTesteeBaseInfo(@Validated @RequestBody ProjectTesteeParam projectTesteeParam) {
        projectTesteeParam.setOperator(getUserId());
        CustomResult data = projectTesteeInfoService.saveProjectTesteeBaseInfo(projectTesteeParam);
        return CommonResult.success(data);
    }

    @NoRepeatSubmit
    @Log(title = "更新参与者研究状态", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "更新参与者研究状态:参与者状态参照产品规则说明")
    @RequestMapping(value = "/modifyTesteeResearchStatus", method = RequestMethod.POST)
    public CommonResult<Object> modifyTesteeResearchStatus(String projectId, String testeeId, String projectOrgId, String status) {
        CustomResult data = projectTesteeInfoService.updateProjectTesteeStatus(projectId, projectOrgId, testeeId, status);
        return CommonResult.success(data);
    }

    @NoRepeatSubmit
    @Log(title = "更新参与者入组状态", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "更新参与者入组状态")
    @RequestMapping(value = "/modifyTesteeJoinGroupStatus", method = RequestMethod.POST)
    public CommonResult<Object> modifyTesteeJoinGroupStatus(String projectId, String projectOrgId, String testeeId) {
        CustomResult data = projectTesteeInfoService.updateProjectTesteeJoinGroupStatus(projectId, projectOrgId, testeeId);
        return CommonResult.success(data);
    }

    @ApiOperation(value = "查询参与者入组情况-返回参与者入组信息")
    @RequestMapping(value = "/getTesteeJoinGroupInfo", method = RequestMethod.GET)
    public CommonResult<Object> getTesteeJoinGroupInfo(String projectId, String projectOrgId, String testeeId) {
        CustomResult data = projectTesteeInfoService.getTesteeJoinGroupInfo(projectId, projectOrgId, testeeId);
        return CommonResult.success(data);
    }

    @NoRepeatSubmit
    @Log(title = "删除项目参与者", businessType = BusinessType.DELETE, projectRecordLog = true)
    @ApiOperation(value = "删除项目参与者")
    @RequestMapping(value = "/removeTesteeBaseInfo", method = RequestMethod.POST)
    public CommonResult<Object> removeTesteeBaseInfo(String projectId, String testeeId) {
        CustomResult data = projectTesteeInfoService.removeTesteeBaseInfo(projectId, testeeId);
        return CommonResult.success(data);
    }

    @NoRepeatSubmit
    @Log(title = "恢复项目参与者", businessType = BusinessType.UPDATE, projectRecordLog = true)
    @ApiOperation(value = "恢复项目参与者")
    @RequestMapping(value = "/modifyTesteeStatus", method = RequestMethod.POST)
    public CommonResult<Object> updateTesteeStatus(String projectId, String projectOrgId, String testeeIds, String status) {
        CustomResult data = projectTesteeInfoService.updateProjectTesteeStatus(projectId, projectOrgId, testeeIds, status);
        return CommonResult.success(data);
    }

    @ApiOperation(value = "查询参与者表单完成情况")
    @RequestMapping(value = "/getTesteeProjectFormAndTableResult", method = RequestMethod.GET)
    public CommonResult<Object> getTesteeProjectFormAndTableResult(String projectId, String testeeId) {
        CustomResult data = projectTesteeInfoService.getTesteeProjectFormAndTableResult(projectId, testeeId);
        return CommonResult.success(data.getData());
    }

    @ApiOperation("查询参与者分页列表")
    @RequestMapping(value = "/getProjectTesteeListForPage", method = RequestMethod.GET)
    @ApiImplicitParams({
        @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "string",required = true),
        @ApiImplicitParam(name = "code", value = "参与者编号", dataType = "string"),
        @ApiImplicitParam(name = "realName", value = "参与者姓名", dataType = "string"),
        @ApiImplicitParam(name = "orgId", value = "所属中心id", dataType = "string"),
        @ApiImplicitParam(name = "ownerDoctor", value = "主管医生id-默认查询自己录入的数据 项目负责人查询全部参与者", dataType = "string"),
        @ApiImplicitParam(name = "status", value = "研究状态参数说明 003001-已入组 003002-筛选中 003003-已退出 003004-已完成", dataType = "string"),
        @ApiImplicitParam(name = "reviewStatus", value = "绑定审核状态参数说明 013001-待审核 013002-已完成 013003-已拒绝", dataType = "string"),
        @ApiImplicitParam(name = "sortField", value = "排序字段 testeecode real_name ownerOrgName ownerDoctor create_time visit_create_time", dataType = "string"),
        @ApiImplicitParam(name = "sortType", value = "排序类型 asc desc", dataType = "string")
    })
    public CommonResult<CommonPage<ProjectTesteeVo>> getProjectTesteeListForPage(String projectId, String code, String realName, String orgId,
                                                                   String ownerDoctor, String status, String reviewStatus,String sortField, String sortType,
                                                                   @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                   @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        CommonPage<ProjectTesteeVo> projectTesteeList = projectTesteeInfoService.getProjectTesteeListForPage(projectId, code, realName, orgId, ownerDoctor, status, reviewStatus, sortField, sortType, pageNum, pageSize);
        return CommonResult.success(projectTesteeList);
    }


    @ApiOperation("查询参与者基本信息列表")
    @RequestMapping(value = "/getProjectTesteeBaseInfoList", method = RequestMethod.GET)
    @ApiImplicitParams({
        @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "string",required = true),
    })
    public CommonResult<List<ProjectTesteeWrapperVo>> getProjectTesteeBaseInfoList(String projectId, String testeeCode, String projectOrgId) {
        List<ProjectTesteeWrapperVo> projectTesteeList = projectTesteeInfoService.getProjectTesteeBaseInfoList(projectId, testeeCode, projectOrgId);
        return CommonResult.success(projectTesteeList);
    }

    @ApiOperation("查询参与者基本信息分页列表")
    @RequestMapping(value = "/getProjectTesteeBaseInfoListForPage", method = RequestMethod.GET)
    @ApiImplicitParams({
        @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "string",required = true),
        @ApiImplicitParam(name = "projectOrgId", value = "项目id", dataType = "string"),
        @ApiImplicitParam(name = "testeeCode", value = "项目id", dataType = "string"),
        @ApiImplicitParam(name = "pageNum", value = "当前页", dataType = "string",required = true),
        @ApiImplicitParam(name = "pageSize", value = "每页显示条数", dataType = "string",required = true),
    })
    public CommonResult<CommonPage<ProjectTesteeVo>> getProjectTesteeBaseInfoListForPage(String projectId, String projectOrgId, String testeeCode,
                                                                                   @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                                   @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        CommonPage<ProjectTesteeVo> projectTesteeList = projectTesteeInfoService.getProjectTesteeBaseInfoListForPage(projectId, projectOrgId, testeeCode, pageNum, pageSize);
        return CommonResult.success(projectTesteeList);
    }

    @ApiOperation(value = "查询参与者访视表单项集合信息-修订")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "string",required = true),
            @ApiImplicitParam(name = "planId", value = "计划方案id", dataType = "string",required = true),
            @ApiImplicitParam(name = "visitId", value = "访视id", dataType = "string",required = true),
    })
    @RequestMapping(value = "/getTesteeVisitList", method = RequestMethod.GET)
    public CommonResult<List<TemplateFormConfigVo>> getTesteeVisitList(String projectId, String planId, String visitId) {
        List<TemplateFormConfigVo> templateFormConfigs = projectTesteeInfoService.getTesteeVisitFormList("", projectId, planId, visitId);
        return CommonResult.success(templateFormConfigs);
    }

    @ApiOperation(value = "查询项目表单研究中心详情")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "string",required = true),
        @ApiImplicitParam(name = "projectOrgId", value = "项目研究中心id", dataType = "string",required = true),
        @ApiImplicitParam(name = "testeeId", value = "参与者id", dataType = "string",required = true),
    })
    @GetMapping(value = "/getTesteeProjectOrgDetail")
    public CommonResult<ProjectTesteeOrgVo> getTesteeProjectOrgDetail(String projectId, String projectOrgId, String testeeId) {
        ProjectTesteeOrgVo projectTesteeOrgVo = projectTesteeInfoService.getTesteeProjectOrgDetail(projectId, projectOrgId, testeeId);
        return CommonResult.success(projectTesteeOrgVo);
    }

    @ApiOperation(value = "查询参与者访视表单详情信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "string",required = true),
            @ApiImplicitParam(name = "projectOrgId", value = "研究中心id", dataType = "string",required = true),
            @ApiImplicitParam(name = "planId", value = "方案id", dataType = "string"),
            @ApiImplicitParam(name = "visitId", value = "访视(流程)id", dataType = "string",required = true),
            @ApiImplicitParam(name = "formId", value = "表单id", dataType = "string",required = true),
            @ApiImplicitParam(name = "formExpandId", value = "扩展表单id", dataType = "string"),
            @ApiImplicitParam(name = "variableTableId", value = "表格id", dataType = "string"),
            @ApiImplicitParam(name = "testeeId", value = "参与者id", dataType = "string",required = true),
            @ApiImplicitParam(name = "enableAppDevice", value = "是否APP端或者H5", dataType = "string"),
            @ApiImplicitParam(name = "tableSort", value = "表格排序方式 1-时间倒序 2-时间正序 3-sort倒序 4-sort正序", dataType = "string",required = true),
    })
    @RequestMapping(value = "/getTesteeVisitFormDetail", method = RequestMethod.GET)
    public CommonResult<List<TemplateFormDetailVo>> getTesteeVisitFormDetail(String projectId, String projectOrgId,String planId, String visitId, String formId, String formExpandId, String variableTableId, String testeeId, String enableAppDevice, String tableSort) {
        if(StringUtils.isEmpty(planId)){
            FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectId);
            if(flowPlanInfo != null){planId = flowPlanInfo.getId().toString();}
        }
        List<TemplateFormDetailVo> templateFormDetailVoList = projectTesteeInfoService.getTesteeVisitFormDetail(projectId, planId, visitId, formId, formExpandId, variableTableId, projectOrgId, testeeId, tableSort, enableAppDevice);
        return CommonResult.success(templateFormDetailVoList);
    }

    @ApiOperation(value = "查询参与者访视表单字段组和表格记录")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "string",required = true),
        @ApiImplicitParam(name = "projectOrgId", value = "研究中心id", dataType = "string",required = true),
        @ApiImplicitParam(name = "planId", value = "方案id", dataType = "string"),
        @ApiImplicitParam(name = "visitId", value = "访视id", dataType = "string",required = true),
        @ApiImplicitParam(name = "formId", value = "表单id", dataType = "string",required = true),
        @ApiImplicitParam(name = "formExpandId", value = "扩展表单id", dataType = "string"),
        @ApiImplicitParam(name = "variableId", value = "模版字段组id或者表格id", dataType = "string"),
        @ApiImplicitParam(name = "variableGroupTableId", value = "字段组模版表格id", dataType = "string"),
        @ApiImplicitParam(name = "variableTableId", value = "字段组中指定的表格id", dataType = "string"),
        @ApiImplicitParam(name = "testeeId", value = "参与者id", dataType = "string",required = true),
        @ApiImplicitParam(name = "enableAppDevice", value = "是否APP端或者H5", dataType = "string"),
        @ApiImplicitParam(name = "tableSort", value = "表格排序方式 1-时间倒序 2-时间正序 3-sort倒序 4-sort正序", dataType = "string",required = true),
    })
    @GetMapping(value = "/getTesteeVisitFormTableDetail")
    public CommonResult<List<TemplateFormDetailVo>> getTesteeVisitFormTableDetail(String projectId, String projectOrgId, String planId, String visitId, String formId, String formExpandId, String variableId,
                                                                                  String variableGroupTableId, String variableTableId, String testeeId, String enableAppDevice, String tableSort) {
        FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectId);
        if(flowPlanInfo != null){planId = flowPlanInfo.getId().toString();}
        List<TemplateFormDetailVo> templateFormDetailVoList = projectTesteeInfoService.getTesteeVisitFormTableDetailForSync(projectId, projectOrgId, planId, visitId, formId, formExpandId, variableId, variableGroupTableId, variableTableId, testeeId, enableAppDevice, tableSort);
        return CommonResult.success(templateFormDetailVoList);
    }

    @ApiOperation(value = "查询参与者表格记录详情信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "string",required = true),
            @ApiImplicitParam(name = "visitId", value = "访视id", dataType = "string",required = true),
            @ApiImplicitParam(name = "formId", value = "表单id", dataType = "string",required = true),
            @ApiImplicitParam(name = "testeeId", value = "参与者id", dataType = "string",required = true),
            @ApiImplicitParam(name = "queryRuleMethod", value = "查询dvp规则类型", dataType = "string"),
            @ApiImplicitParam(name = "openOCR", value = "是否传OCR图片", dataType = "string"),
            @ApiImplicitParam(name = "medicalType", value = "是否查询分类OCR图片-例如通用文字识别", dataType = "string"),
            @ApiImplicitParam(name = "rowNumber", value = "表格记录id", dataType = "string",required = true),
            @ApiImplicitParam(name = "testeeGroupId", value = "参与者字段组id-动态字段组必须设置", dataType = "string"),
    })
    @RequestMapping(value = "/getProjectTesteeTableRowRecord", method = RequestMethod.GET)
    public CommonResult<ProjectTesteeTableConfigWrapperVo> getProjectTesteeTableRowRecord(String projectId, String planId, String visitId, String formId, String formDetailId, String testeeId, String queryRuleMethod, String rowNumber, String openOCR, String medicalType, String testeeGroupId) {
        if(StringUtils.isEmpty(planId)){
            FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectId);
            if(flowPlanInfo != null){planId = flowPlanInfo.getId().toString();}
        }
        ProjectTesteeTableConfigWrapperVo projectTesteeTableWrapperVo = new ProjectTesteeTableConfigWrapperVo();
        ProjectTesteeFormTableConfigVo projectTesteeFormTableConfigVo = projectTesteeTableService.getProjectTesteeFormTableConfig(projectId, planId, visitId, formId, formDetailId, testeeGroupId, testeeId);
        projectTesteeFormTableConfigVo.setFormConfig(null);
        projectTesteeTableWrapperVo.setProjectTesteeFormTableConfigVo(projectTesteeFormTableConfigVo);
        if(StringUtils.isNotEmpty(rowNumber)){
            List<ProjectTesteeTableVo> projectTesteeTableRowRecord = projectTesteeTableService.getProjectTesteeTableRowRecord(projectId, planId, visitId, formId, testeeId, queryRuleMethod, rowNumber, openOCR, medicalType, testeeGroupId);
            projectTesteeTableWrapperVo.setProjectTesteeTableRowRecord(projectTesteeTableRowRecord);
        }
        return CommonResult.success(projectTesteeTableWrapperVo);
    }

    @ApiOperation(value = "查询添加参与者表单和表格配置项说明=已经废弃")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "string",required = true),
            @ApiImplicitParam(name = "visitId", value = "访视id", dataType = "string",required = true),
            @ApiImplicitParam(name = "formId", value = "表单id", dataType = "string",required = true),
            @ApiImplicitParam(name = "formDetailId", value = "表单详情id-如果表单数据中含有表格类型请设置", dataType = "string"),
            @ApiImplicitParam(name = "testeeGroupId", value = "参与者字段组分组id", dataType = "string"),
            @ApiImplicitParam(name = "testeeId", value = "参与者id", dataType = "string"),
    })
    @RequestMapping(value = "/getProjectTesteeFormTableConfig", method = RequestMethod.GET)
    public CommonResult<ProjectTesteeFormTableConfigVo> getProjectTesteeFormTableConfig(String projectId, String visitId, String formId, String formDetailId, String testeeGroupId, String testeeId) {
        ProjectTesteeFormTableConfigVo projectTesteeFormTableConfigVo = projectTesteeTableService.getProjectTesteeFormTableConfig(projectId, "", visitId, formId, formDetailId, testeeGroupId, testeeId);
        return CommonResult.success(projectTesteeFormTableConfigVo);
    }

    @NoRepeatSubmit
    @Log(title = "保存参与者表单详情信息", businessType = BusinessType.INSERT, projectRecordLog = true)
    @ApiOperation(value = "保存参与者表单详情信息")
    @RequestMapping(value = "/saveTesteeVisitFormDetail", method = RequestMethod.POST)
    public CommonResult<Object> saveTesteeVisitFormDetail(@Validated @RequestBody ProjectTesteeResultParam projectTesteeResultParam) {
        projectTesteeResultParam.setOperator(getUserId());
        CustomResult customResult = projectTesteeInfoService.saveTesteeVisitFormDetail(projectTesteeResultParam);
        return CommonResult.success(customResult);
    }

    @NoRepeatSubmit
    @Log(title = "保存参与者Table表格记录详情信息", businessType = BusinessType.INSERT, projectRecordLog = true)
    @ApiOperation(value = "保存参与者Table表格记录详情信息")
    @RequestMapping(value = "/saveProjectTesteeTableRowRecord", method = RequestMethod.POST)
    public CommonResult<Object> saveProjectTesteeTableRowRecord(@RequestBody ProjectTesteeTableParam projectTesteeTableParam) {
        projectTesteeTableParam.setCreateUserId(getUserId());
        CustomResult customResult = projectTesteeInfoService.saveProjectTesteeTableRowRecord(projectTesteeTableParam);
        return CommonResult.success(customResult);
    }

    @NoRepeatSubmit
    @Log(title = "初始化(批量)保存参与者Table表格多条记录", businessType = BusinessType.INSERT, projectRecordLog = true)
    @ApiOperation(value = "初始化(批量)保存参与者Table表格多条记录")
    @RequestMapping(value = "/saveProjectTesteeCustomTableRowRecord", method = RequestMethod.POST)
    public CommonResult<Object> saveProjectTesteeCustomTableRowRecord(@RequestBody ProjectTesteeCustomTableParam projectTesteeCustomTableParam) {
        projectTesteeCustomTableParam.setCreateUserId(getUserId());
        CustomResult customResult = projectTesteeInfoService.saveProjectTesteeCustomTableRowRecord(projectTesteeCustomTableParam);
        return CommonResult.success(customResult);
    }

    @NoRepeatSubmit
    @Log(title = "保存参与者Table表格记录列信息", businessType = BusinessType.INSERT, projectRecordLog = true)
    @ApiOperation(value = "保存参与者Table表格记录列信息")
    @RequestMapping(value = "/saveProjectTesteeTableColumnRecord", method = RequestMethod.POST)
    public CommonResult<Object> saveProjectTesteeTableColumnRecord(@RequestBody ProjectTesteeTableColumnParam projectTesteeTableColumnParam) {
        if(projectTesteeTableColumnParam.getPlanId() == null){
            FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectTesteeTableColumnParam.getProjectId().toString());
            if(flowPlanInfo != null){projectTesteeTableColumnParam.setPlanId(flowPlanInfo.getId());}
        }
        CustomResult customResult = projectTesteeInfoService.saveProjectTesteeTableColumnRecord(projectTesteeTableColumnParam);
        return CommonResult.success(customResult);
    }

    @NoRepeatSubmit
    @Log(title = "更新参与者表单录入进度", businessType = BusinessType.UPDATE, projectRecordLog = true)
    @ApiOperation(value = "更新参与者表单录入进度")
    @RequestMapping(value = "/modifyTesteeVisitFormProcess", method = RequestMethod.POST)
    public CommonResult<Object> modifyTesteeVisitFormProcess(@Validated @RequestBody ProjectTesteeFormProcessParam projectTesteeFormProcessParam) {
        projectTesteeFormProcessParam.setOperator(getUserId());
        CustomResult customResult = projectTesteeInfoService.modifyTesteeVisitFormProcess(projectTesteeFormProcessParam);
        return CommonResult.success(customResult);
    }

    @NoRepeatSubmit
    @Log(title = "管理端-批量上传表单数据(图片)", businessType = BusinessType.INSERT)
    @ApiOperation(value = "管理端-批量上传表单数据(图片)")
    @RequestMapping(value = "/saveBatchTesteeVisitFormDetail", method = RequestMethod.POST)
    public CommonResult<Object> saveBatchTesteeVisitFormDetail(@RequestBody List<ProjectTesteeBatchUploadParam> projectTesteeResultParam) {
        CustomResult customResult = projectTesteeInfoService.saveBatchTesteeVisitFormDetail(projectTesteeResultParam, getUserId());
        return CommonResult.success(customResult);
    }


    @ApiOperation(value = "查询当前表单字段分组集合")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "string",required = true),
        @ApiImplicitParam(name = "visitId", value = "访视id", dataType = "string",required = true),
        @ApiImplicitParam(name = "formId", value = "表单id", dataType = "string",required = true),
        @ApiImplicitParam(name = "testeeId", value = "参与者id", dataType = "string",required = true),
    })
    @RequestMapping(value = "/getProjectTesteeFormGroupListByFormId", method = RequestMethod.GET)
    public CommonResult<List<TemplateFormGroupVariableVo>> getProjectTesteeFormGroupListByFormId(String projectId, String planId, String visitId, String formId, String testeeId) {
        if(StringUtils.isEmpty(planId)){
            FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectId);
            if(flowPlanInfo != null){planId = flowPlanInfo.getId().toString();}
        }
        List<TemplateFormGroupVariableVo> dataList = templateFormGroupService.getProjectTesteeFormGroupListByFormId(projectId, planId, visitId, formId, "", "", testeeId);
        return CommonResult.success(dataList);
    }


    @NoRepeatSubmit
    @Log(title = "创建参与者字段组", businessType = BusinessType.INSERT, projectRecordLog = true)
    @ApiOperation(value = "参与者新增字段组")
    @RequestMapping(value = "/saveTesteeFormGroupDetail", method = RequestMethod.POST)
    public CommonResult<Object> saveTesteeFormGroupDetail(@RequestBody TemplateFormGroupParam templateFormGroupParam) {
        if(templateFormGroupParam.getPlanId() == null){
            FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(templateFormGroupParam.getProjectId().toString());
            if(flowPlanInfo != null){templateFormGroupParam.setPlanId(flowPlanInfo.getId());}
        }
        return templateFormGroupService.saveTesteeFormGroupDetail(templateFormGroupParam, getUserId());
    }


    @NoRepeatSubmit
    @Log(title = "批量新增参与者字段组", businessType = BusinessType.INSERT, projectRecordLog = true)
    @ApiOperation(value = "批量新增参与者字段组")
    @RequestMapping(value = "/saveBatchTesteeFormGroupDetail", method = RequestMethod.POST)
    public CommonResult<Object> saveBatchTesteeFormGroupDetail(@RequestParam(value="projectId") String projectId, @RequestBody List<TemplateFormGroupParam> groupParamList) {
        List<String> formGroupDetail = templateFormGroupService.saveBatchTesteeFormGroupDetail(projectId, groupParamList, getUserId());
        return CommonResult.success(formGroupDetail);
    }

    @NoRepeatSubmit
    @Log(title = "根据groupId删除参与者字段组", businessType = BusinessType.DELETE, projectRecordLog = true)
    @ApiOperation(value = "根据groupId删除参与者字段组")
    @RequestMapping(value = "/removeProjectTesteeGroupByGroupId", method = RequestMethod.POST)
    public CommonResult<Object> removeProjectTesteeGroupByGroupId(@RequestParam(value="groupId") String groupId) {
        return templateFormGroupService.deleteProjectTesteeGroupInfoByGroupId(groupId,getUserId());
    }

    @NoRepeatSubmit
    //@Log(title = "参与者表单字段组初始化", businessType = BusinessType.INSERT, projectRecordLog = true)
    @ApiOperation(value = "参与者表单字段组初始化")
    @RequestMapping(value = "/saveTesteeBaseFormVariableGroup", method = RequestMethod.POST)
    public CommonResult<Object> saveTesteeBaseFormVariableGroup(@RequestParam(value="projectId") String projectId,
                                                                @RequestParam(value="visitId") String visitId,
                                                                @RequestParam(value="formId") String formId,
                                                                @RequestParam(value="testeeId") String testeeId) {
        return templateFormGroupService.saveTesteeBaseFormVariableGroup(projectId, visitId, formId, testeeId, getUserId());
    }


    @ApiOperation("导出参与者分页列表")
    @RequestMapping(value = "/getExportProjectTesteeListForPage", method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "String", paramType="body", required = true),
            @ApiImplicitParam(name = "code", value = "参与者编号", dataType = "string", paramType="body"),
            @ApiImplicitParam(name = "realName", value = "参与者姓名", dataType = "string", paramType="body"),
            @ApiImplicitParam(name = "orgIds", value = "中心id,多个使用逗号隔开", dataType = "String", paramType="body"),
            @ApiImplicitParam(name = "ownerDoctor", value = "主管医生id", dataType = "string", paramType="body"),
            @ApiImplicitParam(name = "status", value = "研究状态参照字典003", dataType = "string", paramType="body"),
            @ApiImplicitParam(name = "sortField", value = "排序字段", dataType = "string", paramType="body"),
            @ApiImplicitParam(name = "sortType", value = "排序访视 asc desc", dataType = "string", paramType="body"),
            @ApiImplicitParam(name = "conditionValue", value = "设置变量条件", dataType = "string", paramType="body"),
            @ApiImplicitParam(name = "pageNum", value = "当前页", dataType = "string", paramType="body"),
            @ApiImplicitParam(name = "pageSize", value = "每页显示条数", dataType = "string", paramType="body"),
    })
    public CommonResult<CommonPage<ProjectTesteeExportViewVo>> getExportProjectTesteeListForPage(String projectId, String code, String realName, String orgIds,
                                                                                                 String ownerDoctor, String status, String sortField, String sortType,
                                                                                                 String conditionValue,
                                                                                                 @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                                                 @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        CommonPage<ProjectTesteeExportViewVo> projectTesteeList = projectTesteeInfoService.getExportProjectTesteeListForPageVersion2(projectId, code, realName, orgIds, ownerDoctor, status, sortField, sortType, conditionValue, pageNum, pageSize);
        return CommonResult.success(projectTesteeList);
    }

    @ApiOperation("导出参与者分页列表---最新版导出患者列表")
    @RequestMapping(value = "/getElasticSearchProjectTesteeListForPage", method = RequestMethod.POST)
    @ApiImplicitParams({
        @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "String", paramType="body", required = true),
        @ApiImplicitParam(name = "orgIds", value = "中心id,多个使用逗号隔开", dataType = "String", paramType="body"),
        @ApiImplicitParam(name = "sortField", value = "排序字段", dataType = "string", paramType="body"),
        @ApiImplicitParam(name = "sortType", value = "排序访视 asc desc", dataType = "string", paramType="body"),
        @ApiImplicitParam(name = "conditionValue", value = "设置变量条件", dataType = "string", paramType="body"),
        @ApiImplicitParam(name = "pageNum", value = "当前页", dataType = "string", paramType="body"),
        @ApiImplicitParam(name = "pageSize", value = "每页显示条数", dataType = "string", paramType="body"),
    })
    public CommonResult<CommonPage<Map<String, Object>>> getElasticSearchProjectTesteeListForPage(String projectId, String sortField, String sortType, String conditionValue,
                                                                                                  @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                                                  @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) throws Exception {

        //CommonPage<Map<String, Object>> projectTesteeList = projectTesteeTestService.getExportProjectTesteeListForPage(projectId, "", conditionValue, sortField, sortType, pageNum, pageSize);
        return CommonResult.success(null);
    }

    @NoRepeatSubmit
    @Log(title = "根据变量记录id更新详情信息", businessType = BusinessType.UPDATE, projectRecordLog = true)
    @ApiOperation(value = "根据变量记录id更新详情信息")
    @RequestMapping(value = "/modifyTesteeFormDetailById", method = RequestMethod.POST)
    public CommonResult<Object> updateTesteeFormDetailById(String id, String fieldValue) {
        CustomResult data = projectTesteeInfoService.updateTesteeFormDetailById(id, fieldValue, getUserId());
        return CommonResult.success(data);
    }

    @NoRepeatSubmit
    @Log(title = "根据变量记录id删除录入值", businessType = BusinessType.DELETE, projectRecordLog = true)
    @ApiOperation(value = "根据变量记录id删除录入值")
    @RequestMapping(value = "/removeTesteeFormDetailById", method = RequestMethod.POST)
    public CommonResult<Object> removeTesteeFormDetailById(String ids) {
        CustomResult data = projectTesteeInfoService.deleteTesteeFormDetailById(ids, getUserId());
        return CommonResult.success(data);
    }

    @NoRepeatSubmit
    @Log(title = "根据编号删除行记录", businessType = BusinessType.DELETE, projectRecordLog = true)
    @ApiOperation(value = "根据编号删除行记录")
    @RequestMapping(value = "/removeTesteeTableRowRecord", method = RequestMethod.POST)
    public CommonResult<Object> removeTesteeTableRowRecord(String rowNumber, String projectId, String planId, String visitId, String formId, String testeeGroupId, String testeeId) {
        if(StringUtils.isEmpty(planId)){
            FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectId);
            if(flowPlanInfo != null){planId = flowPlanInfo.getId().toString();}
        }
        CustomResult data = projectTesteeTableService.deleteTesteeTableRowRecord(rowNumber, projectId, planId, visitId, formId, testeeGroupId, testeeId);
        return CommonResult.success(data);
    }


    @NoRepeatSubmit
    @Log(title = "根据表格id删除整个表格记录", businessType = BusinessType.DELETE, projectRecordLog = true)
    @ApiOperation(value = "根据表格id删除整个表格记录")
    @RequestMapping(value = "/removeTesteeTableRecord", method = RequestMethod.POST)
    public CommonResult<Object> removeTesteeTableRecord(String projectId, String visitId, String formId, String formDetailId, String testeeGroupId, String testeeId) {
        CustomResult data = projectTesteeTableService.deleteTesteeTableRecord(projectId, visitId, formId, formDetailId, testeeGroupId, testeeId);
        return CommonResult.success(data);
    }

    @ApiOperation(value = "查询参与者访视表单CRF图片文件信息-第一版需求已废弃")
    @RequestMapping(value = "/getProjectTesteeTableCRF", method = RequestMethod.GET)
    public CommonResult<String> getProjectTesteeTableCRF(String projectId, String visitId, String testeeId, String formDetailId) {
        String testeeTableCRFUrl = projectTesteeInfoService.getProjectTesteeTableCRF(projectId, visitId, testeeId, formDetailId);
        return CommonResult.success(testeeTableCRFUrl);
    }

    @ApiOperation(value = "查询表单、变量图片集合")
    @RequestMapping(value = "/getProjectTesteeFormImage", method = RequestMethod.GET)
    public CommonResult<List<ProjectTesteeFormImageVo>> getProjectTesteeFormImage(String projectId, String planId, String visitId, String formId, String resourceId, String tableId, String rowNumber, String testeeId,
                                                                                  String medicalType, String openOCR, String batchUpload, String taskDate, String groupName, String batchOpenOcr) {
        List<ProjectTesteeFormImageVo> dataList = projectTesteeFileService.getProjectTesteeFormImageList(projectId, planId, visitId, formId, testeeId, taskDate, resourceId, tableId, rowNumber, medicalType, openOCR, batchUpload, groupName, true, batchOpenOcr);
        return CommonResult.success(dataList);
    }


    @ApiOperation(value = "查询合成图片对应的缩略图列表")
    @RequestMapping(value = "/getProjectTesteeThumbnailImageByFileId", method = RequestMethod.GET)
    public CommonResult<List<ProjectTesteeFormImageVo>> getProjectTesteeThumbnailImageByFileId(String projectId, String fileId) {
        List<ProjectTesteeFormImageVo> dataList = projectTesteeFileService.getProjectTesteeThumbnailImageByFileId(projectId, fileId);
        return CommonResult.success(dataList);
    }

    @ApiOperation(value = "查询项目参与者待合成图片列表")
    @RequestMapping(value = "/getProjectTesteeUplaodThumbnailImages", method = RequestMethod.GET)
    public CommonResult<List<ProjectTesteeFormImageVo>> getProjectTesteeUplaodThumbnailImages(String projectId, String visitId, String formId, String testeeId) {
        List<ProjectTesteeFormImageVo> dataList = projectTesteeFileService.getProjectTesteeUplaodThumbnailImages(projectId, visitId, formId, testeeId);
        return CommonResult.success(dataList);
    }

    @ApiOperation(value = "查询文件及识别结果")
    @RequestMapping(value = "/getProjectTesteeFormImageByFileId", method = RequestMethod.GET)
    public CommonResult<ProjectTesteeFormImageVo> getProjectTesteeFormImageByFileId(String fileId, String viewSmartStructuralText) {
        ProjectTesteeFormImageVo projectTesteeFormImageVo = projectTesteeFileService.getProjectTesteeFormImageByFileId(fileId);
        String wordsResult = projectTesteeFormImageVo.getProjectTesteeOcr().getWordsResult();
        if(StringUtils.isEmpty(viewSmartStructuralText)){
            OCRWordsResult ocrWordsResult = JSON.parseObject(wordsResult, OCRWordsResult.class);
            projectTesteeFormImageVo.setOcrWordsResult(ocrWordsResult);
        }
        if("1".equals(viewSmartStructuralText)){
            ReportStructuralObject reportStructuralObject = JSON.parseObject(wordsResult, ReportStructuralObject.class);
            projectTesteeFormImageVo.setOcrWordsResult(null);
            projectTesteeFormImageVo.setReportStructuralObject(reportStructuralObject);
        }
        if("2".equals(viewSmartStructuralText)){
            ReportStructuralProObject reportStructuralProObject = JSON.parseObject(wordsResult, ReportStructuralProObject.class);
            projectTesteeFormImageVo.setOcrWordsResult(null);
            projectTesteeFormImageVo.setReportStructuralProObject(reportStructuralProObject);
        }
        projectTesteeFormImageVo.setProjectTesteeOcr(null);
        return CommonResult.success(projectTesteeFormImageVo);
    }

    @ApiOperation(value = "通过fileId查询下一张图片信息")
    @RequestMapping(value = "/getProjectTesteeNextImageByFileId", method = RequestMethod.GET)
    public CommonResult<ProjectTesteeFormImageVo> getProjectTesteeNextImageByFileId(String fileId) {
        ProjectTesteeFormImageVo projectTesteeFormImageVo = projectTesteeFileService.getProjectTesteeNextImageByFileId(fileId);
        return CommonResult.success(projectTesteeFormImageVo);
    }

    @ApiOperation(value = "获取参与者信息列表")
    @PostMapping(value = "/getProjectTesteeList")
    public CommonPage<ProjectTesteeWrapperVo> getProjectTesteeList(@RequestBody ProjectTesteeFillInfoParam param) {
        return projectTesteeInfoService.getProjectTesteeDataViewList(param);
    }

    @NoRepeatSubmit
    @Log(title = "保存参与者Table表格记录详情信息List", businessType = BusinessType.INSERT, projectRecordLog = true)
    @ApiOperation(value = "保存参与者Table表格记录详情信息List")
    @RequestMapping(value = "/saveProjectTesteeTableRowRecordList", method = RequestMethod.POST)
    public CommonResult<Object> saveProjectTesteeTableRowRecordList(@RequestBody List<ProjectTesteeTableParam> projectTesteeTableParamList) {
        CustomResult customResult = new CustomResult();
        List<ProjectFormResultVo> resultList = new ArrayList<>();
        for(ProjectTesteeTableParam projectTesteeTableParam : projectTesteeTableParamList){
            projectTesteeTableParam.setCreateUserId(getUserId());
            CustomResult customResult1 = projectTesteeInfoService.saveProjectTesteeTableRowRecord(projectTesteeTableParam);
            List<ProjectFormResultVo> resultList1 = (List<ProjectFormResultVo>) customResult1.getData();
            for(ProjectFormResultVo projectFormResultVo : resultList1){
                resultList.add(projectFormResultVo);
            }
        }
        customResult.setData(resultList);
        return CommonResult.success(customResult);
    }


    @NoRepeatSubmit
    @Log(title = "表单审核通过/驳回", businessType = BusinessType.UPDATE, projectRecordLog = true)
    @ApiOperation(value = "表单审核通过/驳回")
    @GetMapping(value = "/audit")
    public CommonResult<Object> audit(String auditId, String auditStatus) {
        if(StringUtils.isEmpty(auditId) || StringUtils.isEmpty(auditStatus)){
            return CommonResult.validateFailed("审核参数设置不正确");
        }
        CustomResult customResult = projectFormAuditService.updateAuditForm(auditId,auditStatus);
        return CommonResult.success(customResult);
    }
}
