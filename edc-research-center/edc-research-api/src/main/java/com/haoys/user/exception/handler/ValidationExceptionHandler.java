package com.haoys.user.exception.handler;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 参数验证异常处理器
 *
 * <p>专门处理参数验证相关的异常</p>
 * <p>包括Bean Validation注解验证、方法参数验证、数据绑定异常等</p>
 * <p>提供详细的验证错误信息和用户友好的错误提示</p>
 * <p>保留原始GlobalExceptionHandler的审计日志功能</p>
 *
 * <h3>处理的异常类型：</h3>
 * <ul>
 *   <li><b>方法参数验证异常</b>：MethodArgumentNotValidException</li>
 *   <li><b>数据绑定异常</b>：BindException</li>
 *   <li><b>约束违反异常</b>：ConstraintViolationException</li>
 *   <li><b>参数类型转换异常</b>：TypeMismatchException</li>
 *   <li><b>缺少参数异常</b>：MissingServletRequestParameterException</li>
 * </ul>
 *
 * <h3>验证特性：</h3>
 * <ul>
 *   <li>详细的字段级验证错误信息</li>
 *   <li>多个验证错误的聚合显示</li>
 *   <li>国际化错误消息支持</li>
 *   <li>验证规则和错误位置定位</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
@Slf4j
@RestControllerAdvice
@Order(150) // 较高优先级，优先处理验证异常
public class ValidationExceptionHandler extends BaseExceptionHandler {

    // ================================ Bean Validation异常处理 ================================

    /**
     * 处理方法参数验证异常
     *
     * <p>当使用@Valid或@Validated注解验证请求体参数失败时触发</p>
     * <p>通常用于POST/PUT请求的JSON请求体验证</p>
     *
     * @param exception 方法参数验证异常
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public CommonResult<String> handleMethodArgumentNotValid(MethodArgumentNotValidException exception, 
                                                            HttpServletRequest request) {
        List<FieldError> fieldErrors = exception.getBindingResult().getFieldErrors();
        
        String traceId = logExceptionWithTraceId(request, exception, "WARN", 
            String.format("方法参数验证失败: 错误字段数=%d", fieldErrors.size()));
        
        // 构建详细的验证错误信息
        StringBuilder errorMessage = new StringBuilder("参数验证失败: ");
        
        if (fieldErrors.size() == 1) {
            // 单个字段错误，提供具体信息
            FieldError fieldError = fieldErrors.get(0);
            errorMessage.append(String.format("字段 '%s' %s", 
                                             fieldError.getField(), 
                                             fieldError.getDefaultMessage()));
        } else if (fieldErrors.size() <= 3) {
            // 多个字段错误但不超过3个，列出所有错误
            String errors = fieldErrors.stream()
                    .map(error -> String.format("字段 '%s' %s", error.getField(), error.getDefaultMessage()))
                    .collect(Collectors.joining("; "));
            errorMessage.append(errors);
        } else {
            // 错误字段过多，只显示前3个并提示总数
            String errors = fieldErrors.stream()
                    .limit(3)
                    .map(error -> String.format("字段 '%s' %s", error.getField(), error.getDefaultMessage()))
                    .collect(Collectors.joining("; "));
            errorMessage.append(errors)
                      .append(String.format(" 等，共 %d 个字段验证失败", fieldErrors.size()));
        }
        
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_PARAM_FAIL, 
                                             truncateMessage(errorMessage.toString(), "参数验证失败"));
    }

    /**
     * 处理数据绑定异常
     *
     * <p>当表单数据绑定到对象失败时触发</p>
     * <p>通常用于表单提交的数据验证</p>
     *
     * @param exception 数据绑定异常
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(BindException.class)
    public CommonResult<String> handleBindException(BindException exception, HttpServletRequest request) {
        List<FieldError> fieldErrors = exception.getBindingResult().getFieldErrors();
        
        String traceId = logExceptionWithTraceId(request, exception, "WARN", 
            String.format("数据绑定失败: 错误字段数=%d", fieldErrors.size()));
        
        // 构建验证错误信息
        StringBuilder errorMessage = new StringBuilder("数据绑定失败: ");
        
        if (fieldErrors.isEmpty()) {
            errorMessage.append("数据格式不正确");
        } else if (fieldErrors.size() == 1) {
            FieldError fieldError = fieldErrors.get(0);
            errorMessage.append(String.format("字段 '%s' %s", 
                                             fieldError.getField(), 
                                             fieldError.getDefaultMessage()));
        } else {
            String errors = fieldErrors.stream()
                    .limit(3)
                    .map(error -> String.format("字段 '%s' %s", error.getField(), error.getDefaultMessage()))
                    .collect(Collectors.joining("; "));
            errorMessage.append(errors);
            
            if (fieldErrors.size() > 3) {
                errorMessage.append(String.format(" 等，共 %d 个字段错误", fieldErrors.size()));
            }
        }
        
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_PARAM_BIND_FAIL, 
                                             truncateMessage(errorMessage.toString(), "数据绑定失败"));
    }

    /**
     * 处理约束违反异常
     *
     * <p>当方法级别的参数验证失败时触发</p>
     * <p>通常用于@Validated注解在类级别的验证</p>
     *
     * @param exception 约束违反异常
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public CommonResult<String> handleConstraintViolation(ConstraintViolationException exception, 
                                                         HttpServletRequest request) {
        Set<ConstraintViolation<?>> violations = exception.getConstraintViolations();
        
        String traceId = logExceptionWithTraceId(request, exception, "WARN", 
            String.format("约束验证失败: 违反约束数=%d", violations.size()));
        
        // 构建约束违反错误信息
        StringBuilder errorMessage = new StringBuilder("参数约束验证失败: ");
        
        if (violations.isEmpty()) {
            errorMessage.append("参数不符合约束条件");
        } else if (violations.size() == 1) {
            ConstraintViolation<?> violation = violations.iterator().next();
            String propertyPath = violation.getPropertyPath().toString();
            String message = violation.getMessage();
            errorMessage.append(String.format("参数 '%s' %s", propertyPath, message));
        } else {
            String errors = violations.stream()
                    .limit(3)
                    .map(violation -> {
                        String propertyPath = violation.getPropertyPath().toString();
                        String message = violation.getMessage();
                        return String.format("参数 '%s' %s", propertyPath, message);
                    })
                    .collect(Collectors.joining("; "));
            errorMessage.append(errors);
            
            if (violations.size() > 3) {
                errorMessage.append(String.format(" 等，共 %d 个约束违反", violations.size()));
            }
        }
        
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_PARAM_FAIL, 
                                             truncateMessage(errorMessage.toString(), "参数约束验证失败"));
    }

    // ================================ 参数类型和格式异常处理 ================================

    /**
     * 处理参数类型转换异常
     *
     * <p>当参数类型转换失败时触发</p>
     * <p>例如：期望数字但传入字符串、日期格式错误等</p>
     *
     * @param exception 类型不匹配异常
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(org.springframework.beans.TypeMismatchException.class)
    public CommonResult<String> handleTypeMismatch(org.springframework.beans.TypeMismatchException exception, 
                                                  HttpServletRequest request) {
        String propertyName = exception.getPropertyName();
        Object value = exception.getValue();
        Class<?> requiredType = exception.getRequiredType();
        
        String traceId = logExceptionWithTraceId(request, exception, "WARN", 
            String.format("参数类型转换失败: 参数名=%s, 参数值=%s, 期望类型=%s", 
                         propertyName, value, requiredType != null ? requiredType.getSimpleName() : "未知"));
        
        String message;
        if (propertyName != null && requiredType != null) {
            String typeName = getTypeFriendlyName(requiredType);
            message = String.format("参数 '%s' 的值 '%s' 格式不正确，期望格式: %s", 
                                   propertyName, value, typeName);
        } else {
            message = "参数类型格式不正确";
        }
        
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_PARAM_FAIL, 
                                             truncateMessage(message, "参数格式不正确"));
    }

    /**
     * 处理自定义验证异常
     *
     * <p>处理业务层自定义的参数验证异常</p>
     *
     * @param exception 自定义验证异常
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    // 注释掉，因为CustomValidationException类不存在
    // @ExceptionHandler(com.haoys.user.exception.CustomValidationException.class)
    // public CommonResult<String> handleCustomValidation(com.haoys.user.exception.CustomValidationException exception, 
    //                                                   HttpServletRequest request) {
    //     String traceId = logExceptionWithTraceId(request, exception, "WARN", 
    //         String.format("自定义验证失败: 验证规则=%s", exception.getValidationRule()));
    //     
    //     String message = exception.getMessage();
    //     if (message == null || message.trim().isEmpty()) {
    //         message = "参数验证失败";
    //     }
    //     
    //     return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_PARAM_FAIL, 
    //                                          truncateMessage(message, "参数验证失败"));
    // }

    // ================================ 工具方法 ================================

    /**
     * 获取类型的友好名称
     *
     * <p>将Java类型转换为用户友好的描述</p>
     *
     * @param type Java类型
     * @return 友好的类型名称
     */
    private String getTypeFriendlyName(Class<?> type) {
        if (type == null) {
            return "未知类型";
        }
        
        if (type == Integer.class || type == int.class) {
            return "整数";
        } else if (type == Long.class || type == long.class) {
            return "长整数";
        } else if (type == Double.class || type == double.class) {
            return "小数";
        } else if (type == Float.class || type == float.class) {
            return "浮点数";
        } else if (type == Boolean.class || type == boolean.class) {
            return "布尔值(true/false)";
        } else if (type == java.util.Date.class) {
            return "日期时间";
        } else if (type == java.time.LocalDate.class) {
            return "日期(yyyy-MM-dd)";
        } else if (type == java.time.LocalDateTime.class) {
            return "日期时间(yyyy-MM-dd HH:mm:ss)";
        } else if (type == String.class) {
            return "字符串";
        } else if (type.isEnum()) {
            return "枚举值";
        } else {
            return type.getSimpleName();
        }
    }
}
