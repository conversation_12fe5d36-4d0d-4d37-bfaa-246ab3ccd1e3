package com.haoys.user.project.controller;

import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.annotation.NoRepeatSubmit;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.common.excel.ExcelUtil;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.dto.ProjectUserExportDto;
import com.haoys.user.domain.dto.ProjectUserParam;
import com.haoys.user.domain.param.auth.ProjectUserAuthParam;
import com.haoys.user.domain.param.project.ProjectUserQueryParam;
import com.haoys.user.domain.vo.project.ProjectUserVo;
import com.haoys.user.domain.vo.system.SystemUserInfoVo;
import com.haoys.user.domain.wrapper.ProjectUserInfoWrapper;
import com.haoys.user.domain.wrapper.ProjectUserQueryWrapper;
import com.haoys.user.model.ProjectUserInfo;
import com.haoys.user.service.OrganizationService;
import com.haoys.user.service.ProjectUserService;
import com.haoys.user.service.SystemUserInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api(tags = "项目用户管理")
@RestController
@RequestMapping("/project/user")
public class ProjectUserManageController extends BaseController {

    @Autowired
    private SystemUserInfoService systemUserInfoService;
    @Autowired
    private ProjectUserService projectUserService;
    @Autowired
    private OrganizationService organizationService;

    @ApiOperation("查询项目用户分页列表")
    @GetMapping("/getProjectUserListForPage")
    public CommonResult getProjectUserListForPage(ProjectUserQueryParam projectUserQueryParam){
        List<ProjectUserVo> projectUserList = projectUserService.selectProjectUserListForPage(projectUserQueryParam);
        return returnPage(projectUserList);
    }

    @ApiOperation("查询项目用户列表-不分页")
    @GetMapping("/getProjectUserList")
    public CommonResult getProjectUserList(String projectId, String orgId, String realName, String mobile, String departmentId, String roleIds, String projectLeader){
        List<ProjectUserVo> projectUserList = projectUserService.selectProjectUserList(projectId, orgId, departmentId, realName, mobile, roleIds, projectLeader);
        return CommonResult.success(projectUserList);
    }

    @NoRepeatSubmit
    @Log(title = "邀请项目用户", businessType = BusinessType.INSERT, projectRecordLog = true)
    @ApiOperation("邀请项目用户")
    @PostMapping("/saveProjectUser")
    public CommonResult saveProjectUser(@RequestBody ProjectUserAuthParam projectUserAuthParam){
        projectUserAuthParam.setCreateUserId(getUserId());
        CustomResult customResult = projectUserService.saveProjectUserInfo(projectUserAuthParam);
        return CommonResult.success(customResult);
    }


    @ApiOperation("查询项目用户详情")
    @GetMapping("/getProjectUserInfo")
    public CommonResult getProjectUserInfo(String projectId, String userId){
        ProjectUserVo projectUserVo = projectUserService.getProjectUserInfo(projectId, userId);
        return CommonResult.success(projectUserVo);
    }

    @ApiOperation("查询项目角色和研究中心列表")
    @GetMapping("/getProjectRoleListAndOrgList")
    public CommonResult getProjectRoleListAndOrgList(@RequestParam List<Long> projectIds){
        Map<String,Object> dataMap = projectUserService.getProjectRoleListAndOrgList(projectIds);
        return CommonResult.success(dataMap);
    }

    @ApiOperation("根据账户名查询项目用户信息")
    @GetMapping("/getProjectUserInfoByAccountName")
    public CommonResult<ProjectUserQueryWrapper> getProjectUserInfoByAccountName(@RequestParam(value="projectId") String projectId,
                                                                                 @RequestParam(value="accountName") String accountName) {
        if(StringUtils.isEmpty(accountName)){return CommonResult.success(null);}
        ProjectUserQueryWrapper projectUserQueryWrapper = new ProjectUserQueryWrapper();
        SystemUserInfoVo systemUserInfo = systemUserInfoService.getSystemUserInfoByAccountName(accountName);
        if(systemUserInfo == null){return CommonResult.success(projectUserQueryWrapper);}
        projectUserQueryWrapper.setSystemUserInfo(systemUserInfo);
        systemUserInfo.setLoginCode(SnowflakeIdWorker.getUuidValue());
        ProjectUserInfo projectUserInfo = projectUserService.getProjectUserInfoByUserId(projectId, systemUserInfo.getId().toString());
        projectUserQueryWrapper.setProjectUserInfo(projectUserInfo);
        return CommonResult.success(projectUserQueryWrapper);
    }

    @NoRepeatSubmit
    @Log(title = "删除项目用户", businessType = BusinessType.DELETE, projectRecordLog = true)
    @ApiOperation("删除项目用户")
    @GetMapping("remove/{projectId}/{userId}")
    public CommonResult deleteProjectUser(@PathVariable("projectId")String projectId, @PathVariable("userId") String userId, String forceDelete){
        String message = projectUserService.deleteProjectUser(projectId, userId, "1".equals(forceDelete));
        if(!BusinessConfig.RETURN_MESSAGE_DEFAULT.equals(message)){
            return CommonResult.failed(message);
        }
        return CommonResult.success(message);
    }

    @NoRepeatSubmit
    @Log(title = "删除项目成员研究者等-根据code", businessType = BusinessType.DELETE, projectRecordLog = true)
    @ApiOperation("删除项目成员研究者等-根据code")
    @GetMapping("deleteProjectUserByCode/{projectId}/{userId}")
    public CommonResult deleteProjectUserByCode(@PathVariable("projectId")String projectId, @PathVariable("userId") String userId){
        String message = projectUserService.deleteProjectUserByRoleCode(projectId, userId, "");
        if(!BusinessConfig.RETURN_MESSAGE_DEFAULT.equals(message)){
            return CommonResult.failed(message);
        }
        return CommonResult.success(message);
    }

    @NoRepeatSubmit
    @Log(title = "修改项目用户", businessType = BusinessType.UPDATE, projectRecordLog = true)
    @ApiOperation("修改项目用户")
    @PostMapping("/modify")
    public CommonResult update(ProjectUserParam projectUserParam){
        CustomResult customResult = projectUserService.updateProjectUserInfo(projectUserParam);
        return CommonResult.success(customResult);
    }


    @ApiOperation("下载成员管理模版")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response){
        ExcelUtil<ProjectUserParam> excelUtil = new ExcelUtil<>(ProjectUserParam.class);
        excelUtil.importTemplateExcel(response,"成员管理");
    }

    @NoRepeatSubmit
    @Log(title = "批量导入项目用户", businessType = BusinessType.IMPORT)
    @ApiOperation("批量导入项目用户")
    @PostMapping("/importData")
    public CommonResult importData(MultipartFile file,Long projectId) throws Exception {
        List<ProjectUserExportDto> errorList = new ArrayList<>();
        HashMap<Object, Object> map = new HashMap<>();
        ExcelUtil<ProjectUserParam> excelUtil = new ExcelUtil<>(ProjectUserParam.class);
        List<ProjectUserParam> projectUserParams = excelUtil.importExcel(file.getInputStream());
        String message = projectUserService.saveBatchProjectUser(projectUserParams, projectId,errorList);
        map.put("message",message);
        if(errorList.size() > 0){
            ExcelUtil<ProjectUserExportDto> exportexcelUtil = new ExcelUtil<>(ProjectUserExportDto.class);
            CommonResult commonResult = exportexcelUtil.exportExcel(errorList, "异常数据查看");
            map.put("url",commonResult.getData());
            return CommonResult.success(map, ResultCode.SYSTEM_IMPORT_EXCEPTION_MESSAGE);
        }
        return returnSuccess(map);
    }

    @NoRepeatSubmit
    @Log(title = "添加系统用户所属中心", businessType = BusinessType.INSERT)
    @ApiOperation(value = "添加系统用户所属中心")
    @RequestMapping(value = "/saveSystemUserUserPrimaryOrg", method = RequestMethod.POST)
    public CommonResult<Object> saveSystemUserUserPrimaryOrg(String projectId, String userId, String orgId) {
        String message = organizationService.saveProjectUserOrgInfo(projectId, userId, orgId, "");
        return CommonResult.success(message);
    }

    @NoRepeatSubmit
    @Log(title = "添加用户所属项目研究中心", businessType = BusinessType.INSERT)
    @ApiOperation("添加用户所属项目研究中心")
    @GetMapping("/saveProjectUserOrgInfo")
    public CommonResult saveProjectUserOrgInfo(@RequestParam(value="projectId") String projectId,
                                               @RequestParam(value="orgId") String orgId,
                                               @RequestParam(value="projectOrgCode") String projectOrgCode,
                                               @RequestParam(value="userId") String userId){
        String message = organizationService.saveProjectUserOrgInfo(projectId, userId, orgId, projectOrgCode);
        return CommonResult.success(message);
    }


    @ApiOperation("根据userId查询项目研究中心角色")
    @GetMapping("getProjectUserRoleInfoByUserId")
    public CommonResult getProjectUserRoleInfoByUserId(String projectId, String orgId, String userId){
        ProjectUserInfoWrapper projectUserInfoWrapper = projectUserService.getProjectUserRoleInfoByUserId(projectId, orgId, userId);
        return CommonResult.success(projectUserInfoWrapper);
    }

}
