package com.haoys.user.config;

import com.haoys.user.service.TransactionTestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 事务配置验证器
 * 
 * <p>用于验证全局事务配置是否生效</p>
 * <p>在应用启动时自动运行验证</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-25
 */
@Component  // 重新启用，验证最终结果
public class TransactionConfigValidator implements CommandLineRunner {

    @Autowired
    private TransactionTestService transactionTestService;

    @Override
    public void run(String... args) throws Exception {
        System.out.println("\n" + createRepeatedString("=", 80));
        System.out.println("🔍 开始验证全局事务配置");
        System.out.println(createRepeatedString("=", 80));
        
        validateOriginalRequiredPrefixes();
        validateReadOnlyPrefixes();
        validateNoTransactionMethods();
        validateExceptionRollback();
        
        System.out.println(createRepeatedString("=", 80));
        System.out.println("🎉 全局事务配置验证完成！");
        System.out.println(createRepeatedString("=", 80) + "\n");
    }

    /**
     * 验证原有REQUIRED_RULE_TRANSACTION前缀
     */
    private void validateOriginalRequiredPrefixes() {
        System.out.println("\n📝 验证原有REQUIRED_RULE_TRANSACTION前缀:");
        System.out.println("原有前缀: {\"insert*\", \"create*\", \"add*\", \"save*\", \"register*\", \"batchSave*\", \"update*\", \"delete*\", \"remove*\", \"check*\", \"join*\", \"init*\", \"accredit*\"}");
        
        String[] originalPrefixes = {
            "insert", "create", "add", "save", "register", "batchSave", 
            "update", "delete", "remove", "check", "join", "init", "accredit"
        };
        
        int passedCount = 0;
        int totalCount = originalPrefixes.length;
        
        for (String prefix : originalPrefixes) {
            try {
                String result = callMethodByPrefix(prefix);
                boolean hasTransaction = result.contains("Transaction=true");
                boolean isWriteTransaction = result.contains("ReadOnly=false");

                if (hasTransaction && isWriteTransaction) {
                    System.out.println("✅ " + prefix + "* 前缀: 事务配置正确 - " + result);
                    passedCount++;
                } else {
                    System.out.println("❌ " + prefix + "* 前缀: 事务配置错误 - " + result);
                    // 对于失败的前缀，输出详细的调试信息
                    System.out.println("   🔍 调试信息: 方法名=" + getMethodNameByPrefix(prefix) +
                                     ", 期望=写事务(Transaction=true, ReadOnly=false)" +
                                     ", 实际=" + result);
                }
            } catch (Exception e) {
                System.out.println("❌ " + prefix + "* 前缀: 测试失败 - " + e.getMessage());
            }
        }
        
        System.out.println(String.format("\n📊 原有前缀验证结果: %d/%d 个前缀配置正确", passedCount, totalCount));
        
        if (passedCount == totalCount) {
            System.out.println("🎉 所有原有前缀都配置正确！");
        } else {
            System.out.println("⚠️ 有部分前缀配置不正确，请检查TransactionConfig配置");
        }
    }

    /**
     * 验证只读事务前缀
     */
    private void validateReadOnlyPrefixes() {
        System.out.println("\n📖 验证只读事务前缀:");
        
        String[] readPrefixes = {"select", "get", "query", "search", "count", "find"};
        
        int passedCount = 0;
        int totalCount = readPrefixes.length;
        
        for (String prefix : readPrefixes) {
            try {
                String result = callReadMethodByPrefix(prefix);

                // 修复后的REQUIRED传播行为总是创建事务
                if (result.contains("Transaction=true") && result.contains("ReadOnly=true")) {
                    System.out.println("✅ " + prefix + "* 前缀: 只读事务配置正确 - " + result);
                    passedCount++;
                } else {
                    System.out.println("❌ " + prefix + "* 前缀: 只读事务配置错误 - " + result);
                }
            } catch (Exception e) {
                System.out.println("❌ " + prefix + "* 前缀: 测试失败 - " + e.getMessage());
            }
        }
        
        System.out.println(String.format("\n📊 只读前缀验证结果: %d/%d 个前缀配置正确", passedCount, totalCount));
    }

    /**
     * 验证无事务方法
     */
    private void validateNoTransactionMethods() {
        System.out.println("\n❌ 验证无事务方法:");
        
        try {
            String result = transactionTestService.processWithoutTransaction("test-no-tx");
            
            if (result.contains("Transaction=false")) {
                System.out.println("✅ 无匹配前缀的方法: 正确无事务 - " + result);
            } else {
                System.out.println("❌ 无匹配前缀的方法: 意外有事务 - " + result);
            }
        } catch (Exception e) {
            System.out.println("❌ 无事务方法测试失败: " + e.getMessage());
        }
    }

    /**
     * 验证异常回滚机制
     */
    private void validateExceptionRollback() {
        System.out.println("\n🔄 验证异常回滚机制:");
        
        try {
            // 正常情况
            String normalResult = transactionTestService.saveTestDataWithException("normal");
            System.out.println("✅ 正常保存: " + normalResult);
            
            // 异常情况
            try {
                transactionTestService.saveTestDataWithException("exception");
                System.out.println("❌ 异常回滚测试失败: 应该抛出异常但没有抛出");
            } catch (RuntimeException e) {
                System.out.println("✅ 异常回滚测试通过: 正确抛出RuntimeException - " + e.getMessage());
            }
        } catch (Exception e) {
            System.out.println("❌ 异常回滚测试失败: " + e.getMessage());
        }
    }

    /**
     * 根据前缀调用对应的写事务测试方法
     */
    private String callMethodByPrefix(String prefix) {
        switch (prefix) {
            case "insert": return transactionTestService.insertTestData("test");
            case "create": return transactionTestService.createTestRecord("test");
            case "add": return transactionTestService.addTestItem("test");
            case "save": return transactionTestService.saveTestEntity("test");
            case "register": return transactionTestService.registerTestUser("test");
            case "batchSave": return transactionTestService.batchSaveTestData("test");
            case "update": return transactionTestService.updateTestRecord("test");
            case "delete": return transactionTestService.deleteTestItem("test");
            case "remove": return transactionTestService.removeTestEntity("test");
            case "check": return transactionTestService.checkTestStatus("test");
            case "join": return transactionTestService.joinTestGroup("test");
            case "init": return transactionTestService.initTestSystem("test");
            case "accredit": return transactionTestService.accreditTestPermission("test");
            default: throw new IllegalArgumentException("未知前缀: " + prefix);
        }
    }

    /**
     * 根据前缀调用对应的读事务测试方法
     */
    private String callReadMethodByPrefix(String prefix) {
        switch (prefix) {
            case "select": return transactionTestService.selectTestData("test");
            case "get": return transactionTestService.getTestRecord("test");
            case "query": return transactionTestService.queryTestItems("test");
            case "search": return transactionTestService.searchTestEntities("test");
            case "count": return transactionTestService.countTestRecords("test");
            case "find": return transactionTestService.findTestUsers("test");
            default: throw new IllegalArgumentException("未知前缀: " + prefix);
        }
    }

    /**
     * 根据前缀获取方法名
     */
    private String getMethodNameByPrefix(String prefix) {
        switch (prefix) {
            case "insert": return "insertTestData";
            case "create": return "createTestRecord";
            case "add": return "addTestItem";
            case "save": return "saveTestEntity";
            case "register": return "registerTestUser";
            case "batchSave": return "batchSaveTestData";
            case "update": return "updateTestRecord";
            case "delete": return "deleteTestItem";
            case "remove": return "removeTestEntity";
            case "check": return "checkTestStatus";
            case "join": return "joinTestGroup";
            case "init": return "initTestSystem";
            case "accredit": return "accreditTestPermission";
            default: return "unknown";
        }
    }

    /**
     * 创建重复字符串的辅助方法（兼容Java 8）
     */
    private String createRepeatedString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
}
