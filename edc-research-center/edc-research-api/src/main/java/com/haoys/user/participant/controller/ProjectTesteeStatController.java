package com.haoys.user.participant.controller;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.domain.param.project.ProjectTesteeStatisticsParam;
import com.haoys.user.domain.vo.testee.ProjectTesteeStatisticsVo;
import com.haoys.user.service.ProjectTesteeStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 访视统计接口
 */
@RestController
@Api(tags = "访视统计接口")
@RequestMapping("/testeeStatistics")
public class ProjectTesteeStatController extends BaseController {

    @Autowired
    private ProjectTesteeStatisticsService statisticsService;

    @ApiOperation("访视统计列表")
    @GetMapping("list")
    public CommonResult<CommonPage<ProjectTesteeStatisticsVo>> list(ProjectTesteeStatisticsParam param){
        param.setUserId(getUserId());
        return CommonResult.success(statisticsService.list(param));
    }
}
