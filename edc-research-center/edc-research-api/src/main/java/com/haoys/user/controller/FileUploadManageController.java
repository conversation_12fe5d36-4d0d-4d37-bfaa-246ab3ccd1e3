package com.haoys.user.controller;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.annotation.NoRepeatSubmit;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.common.file.FileUtils;
import com.haoys.user.domain.param.file.UploadProjectFileParam;
import com.haoys.user.domain.vo.UploadFileResultVo;
import com.haoys.user.domain.vo.project.ProjectTesteeFileVo;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.exception.ServiceException;
import com.haoys.user.model.ProjectTesteeFile;
import com.haoys.user.service.ProjectTesteeFileService;
import com.haoys.user.storge.cloud.OssStorageConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;


@Controller
@Api(tags = "文件上传下载管理", value = "FileManageController")
@RequestMapping("/fileManage")
public class FileUploadManageController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(FileUploadManageController.class);

    @Autowired
    private ProjectTesteeFileService projectTesteeFileService;
    @Autowired
    private OssStorageConfig ossStorageConfig;

    @ApiOperation("上传参与者文件")
    @Log(title = "上传参与者文件", businessType = BusinessType.INSERT)
    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    /*@ApiImplicitParams({
        //@ApiImplicitParam(name = "file", value = "上传的文件", required = true,allowMultiple = true, dataType = "MultipartFile"),
        @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "string"),
        @ApiImplicitParam(name = "visitId", value = "访视id", dataType = "string"),
        @ApiImplicitParam(name = "formId", value = "表单id", dataType = "string"),
        @ApiImplicitParam(name = "resourceId", value = "变量id或者表格id", dataType = "string"),
        @ApiImplicitParam(name = "tableId", value = "表格单元格（列）id", dataType = "string"),
        @ApiImplicitParam(name = "rowNumber", value = "表格行记录编号", dataType = "string"),
        @ApiImplicitParam(name = "testeeId", value = "参与者id", dataType = "string"),
        @ApiImplicitParam(name = "collect", value = "是否收藏 0/1", dataType = "string"),
        @ApiImplicitParam(name = "taskDate", value = "患者端提交任务日期", dataType = "string"),
        @ApiImplicitParam(name = "openOCR", value = "是否启用OCR识别 0/1", dataType = "string"),
        @ApiImplicitParam(name = "batchUpload", value = "是否批量上传 0/1", dataType = "string"),
        @ApiImplicitParam(name = "medicalType", value = "识别类型，如果是普通变量设置testee", dataType = "string"),
        @ApiImplicitParam(name = "templateId", value = "OCR定制模版id", dataType = "string"),
        @ApiImplicitParam(name = "prePageNo", value = "上一个单据编号", dataType = "string"),
        @ApiImplicitParam(name = "groupId", value = "字段组分组id", dataType = "string"),
        @ApiImplicitParam(name = "groupName", value = "多张化验单分组名称", dataType = "string"),
        @ApiImplicitParam(name = "imageType", value = "图片类型 原图-original 裁剪-crop 拼接-montage", dataType = "string"),
        @ApiImplicitParam(name = "extendStruct", value = "使用结构化服务识别表单部分变量 0/1", dataType = "string"),
        @ApiImplicitParam(name = "generalAccurate", value = "是否使用通用文字识别 0/1", dataType = "string"),
        @ApiImplicitParam(name = "mergeImage", value = "是否合并裁剪图片 0/1", dataType = "string"),
        @ApiImplicitParam(name = "mergeMethod", value = "合并裁剪图片方式 横向horizonta、纵向vertical", dataType = "string"),
        @ApiImplicitParam(name = "ifMontage", value = "是否参与图片合并 0/1", dataType = "string"),
        @ApiImplicitParam(name = "batchOpenOcr", value = "是否批量OCR识别 0/1", dataType = "string"),
        @ApiImplicitParam(name = "needMergeFileParam", value = "待合并文件列表", paramType = "body", dataType = "string"),
    })*/
    @ResponseBody
    public CommonResult uploadProjectFile(@RequestParam(value = "file")  MultipartFile[] file, String projectId, String visitId, String formId,
                                          String resourceId, String tableId, String rowNumber, String testeeId, String collect,
                                          String taskDate, String openOCR, String batchUpload, String medicalType, String templateId,
                                          String prePageNo, String groupId, String groupName, String imageType, String extendStruct,
                                          String generalAccurate, String mergeImage, String mergeMethod, String ifMontage,
                                          String needMergeFileParam, String batchOpenOcr) {
        try {
            List<UploadFileResultVo> uploadFileResultVo = projectTesteeFileService.saveUploadProjectFile(file, getUserId(), projectId, visitId, formId, resourceId, tableId,
                                                                                                rowNumber, testeeId, collect, taskDate, openOCR, batchUpload,
                                                                                                medicalType, templateId, prePageNo, groupId, groupName, imageType, extendStruct,
                                                                                                generalAccurate, mergeImage, mergeMethod, ifMontage, needMergeFileParam, batchOpenOcr);
            return CommonResult.success(uploadFileResultVo);
        } catch (IOException e) {
            // 内部处理IO异常，避免与异常处理器冲突
            log.error("文件上传IO异常: {}", e.getMessage(), e);
            return CommonResult.failed("文件上传失败，请重试");
        } catch (Exception e) {
            // 处理其他异常
            log.error("文件上传异常: {}", e.getMessage(), e);
            return CommonResult.failed("文件上传失败: " + e.getMessage());
        }
    }

    @ResponseBody
    @ApiOperation("上传项目表单文件")
    @Log(title = "上传项目表单文件", businessType = BusinessType.INSERT)
    @PostMapping(value = "/uploadProjectFormFile")
    public CommonResult uploadProjectFormFile(@RequestParam("file") MultipartFile[] file, String projectId) throws IOException {
        List<UploadFileResultVo> uploadFileResultVo = projectTesteeFileService.saveUploadCrfFile(file, projectId);
        return CommonResult.success(uploadFileResultVo);
    }
    
    
    @ResponseBody
    @ApiOperation("项目文件管理列表")
    @RequestMapping(value = "/getProjectFileListForPage", method = RequestMethod.GET)
    @ApiImplicitParams({
        @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "string",required = true),
        @ApiImplicitParam(name = "searchValue", value = "文件名称或者编号", dataType = "string"),
        @ApiImplicitParam(name = "resourceType", value = "资源类型", dataType = "string"),
        @ApiImplicitParam(name = "sortField", value = "排序字段 version", dataType = "string"),
        @ApiImplicitParam(name = "sortType", value = "排序类型 asc desc", dataType = "string")
    })
    public CommonResult<CommonPage<ProjectTesteeFileVo>> getProjectFileListForPage(String projectId, String resourceType, String searchValue, String sortField, String sortType,
                                                                                   @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                                   @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        
        CommonPage<ProjectTesteeFileVo> projectFileList = projectTesteeFileService.getProjectFileListForPage(projectId, resourceType, searchValue, sortField, sortType, pageNum, pageSize);
        return CommonResult.success(projectFileList);
    }
    
    @NoRepeatSubmit
    @ResponseBody
    @ApiOperation("上传项目文件")
    @Log(title = "上传项目文件", businessType = BusinessType.INSERT)
    @PostMapping(value = "/uploadProjectFile")
    public CommonResult uploadProjectFile(@RequestParam(value = "file", required = false) MultipartFile[] file, @RequestParam String uploadProjectFileParam) {
        try {
            UploadProjectFileParam uploadProjectFile = JSON.parseObject(uploadProjectFileParam, UploadProjectFileParam.class);
            // 验证文件记录是否重复
            ProjectTesteeFile projectTesteeExistFile = projectTesteeFileService.checkFileRecordRepeat(uploadProjectFile.getProjectId(), uploadProjectFile.getFileName(), uploadProjectFile.getFileNumber(), uploadProjectFile.getVersion(), getUserId());
            if(projectTesteeExistFile != null && !projectTesteeExistFile.getId().toString().equals(uploadProjectFile.getFileId())){
                return CommonResult.failed("文件记录已存在");
            }
            List<UploadFileResultVo> uploadFileResultVo = projectTesteeFileService.uploadProjectFile(file, uploadProjectFile);
            return CommonResult.success(uploadFileResultVo);
        } catch (IOException e) {
            // 内部处理IO异常，避免与异常处理器冲突
            log.error("文件上传IO异常: {}", e.getMessage(), e);
            return CommonResult.failed("文件上传失败，请重试");
        } catch (Exception e) {
            // 处理其他异常
            log.error("文件上传异常: {}", e.getMessage(), e);
            return CommonResult.failed("文件上传失败: " + e.getMessage());
        }
    }
    
    @ResponseBody
    @ApiOperation("系统文件上传-图片、文档ppt、word文件、音视频等")
    @Log(title = "上传系统文件", businessType = BusinessType.INSERT)
    @PostMapping(value = "/uploadSystemFile")
    public CommonResult uploadSystemFile(@RequestParam("file") MultipartFile file, @RequestParam String folderName) {
        try {
            List<UploadFileResultVo> uploadFileResultVo = projectTesteeFileService.uploadSystemFile(file, folderName);
            return CommonResult.success(uploadFileResultVo);
        } catch (IOException e) {
            // 内部处理IO异常，避免与异常处理器冲突
            log.error("系统文件上传IO异常: {}", e.getMessage(), e);
            return CommonResult.failed("文件上传失败，请重试");
        } catch (Exception e) {
            // 处理其他异常
            log.error("系统文件上传异常: {}", e.getMessage(), e);
            return CommonResult.failed("文件上传失败: " + e.getMessage());
        }
    }

    @ApiOperation("标记表单与OCR图片关联")
    @RequestMapping(value = "/modifyFileOCRRemark", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult modifyFileOCRRemark(String fileId, String projectId, String visitId, String formId, String groupId, String collect) {
        CustomResult customResult = projectTesteeFileService.updateProjectTesteeOCRFile(fileId, visitId, formId, getUserId(), collect);
        return CommonResult.success(customResult);
    }


    @ApiOperation("裁剪图片合并")
    @Log(title = "裁剪图片合并", businessType = BusinessType.INSERT)
    @RequestMapping(value = "/mergeImages", method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "string"),
            @ApiImplicitParam(name = "visitId", value = "访视id", dataType = "string"),
            @ApiImplicitParam(name = "formId", value = "表单id", dataType = "string"),
            @ApiImplicitParam(name = "resourceId", value = "变量id或者表格id", dataType = "string"),
            @ApiImplicitParam(name = "groupId", value = "字段组分组id", dataType = "string"),
            @ApiImplicitParam(name = "testeeId", value = "参与者id", dataType = "string"),
            @ApiImplicitParam(name = "openOCR", value = "是否启用OCR识别 0/1", dataType = "string"),
            @ApiImplicitParam(name = "medicalType", value = "识别类型，如果是普通变量设置testee", dataType = "string"),
            @ApiImplicitParam(name = "extendStruct", value = "使用结构化服务识别表单部分变量 0/1", dataType = "string"),
            @ApiImplicitParam(name = "generalAccurate", value = "是否使用通用文字识别 0/1", dataType = "string"),
            @ApiImplicitParam(name = "mergeImage", value = "是否合并裁剪图片 0/1", dataType = "string"),
            @ApiImplicitParam(name = "mergeMethod", value = "合并裁剪图片方式 横向horizonta、纵向vertical", dataType = "string"),
    })
    @ResponseBody
    public CommonResult mergeImages(String projectId, String visitId, String formId, String resourceId, String testeeId,
                                          String groupId, String fileIds, String mergeImage, String mergeMethod,
                                          String openOCR, String medicalType, String extendStruct, String generalAccurate) throws FileNotFoundException {
        String message = projectTesteeFileService.updateMergeImages(projectId, visitId, formId, resourceId, groupId, testeeId, fileIds, mergeImage, mergeMethod,
                                                                    openOCR, medicalType, extendStruct, generalAccurate, getUserId());
        if(!BusinessConfig.RETURN_MESSAGE_DEFAULT.equals(message)){
            return CommonResult.failed(message);
        }
        return CommonResult.success(message);
    }
    
    @ResponseBody
    @ApiOperation("删除文件")
    @RequestMapping(value = "/removeProjectTesteeFileById", method = RequestMethod.POST)
    @Log(title = "通过id删除关联文件", businessType = BusinessType.DELETE)
    public CommonResult removeProjectTesteeFileById(String id) {
        CustomResult customResult = projectTesteeFileService.deleteProjectTesteeFileById(id, getUserId());
        return CommonResult.success(customResult);
    }
    @ApiOperation("下载文件")
    @GetMapping("/download")
    public void download(HttpServletResponse response, HttpServletRequest request, @RequestParam("fileId") String fileId) {
        ProjectTesteeFile projectTesteeFile = projectTesteeFileService.getProjectTesteeFileByFileId(NumberUtil.parseLong(fileId));
        if (projectTesteeFile == null) {
            throw new ServiceException("文件不存在");
        }
        try {
            String fileName = projectTesteeFile.getFileName();
            /*if (!FileUtils.checkAllowDownload(fileName)) {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }*/
            String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            String filePath = projectTesteeFile.getUploadPath();
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }

}
