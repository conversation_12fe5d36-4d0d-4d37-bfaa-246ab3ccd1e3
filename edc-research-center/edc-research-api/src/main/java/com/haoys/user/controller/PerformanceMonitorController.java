package com.haoys.user.controller;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.aspect.PerformanceMonitorAspect;
import com.haoys.user.common.interceptor.SqlPerformanceInterceptor;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 性能监控控制器
 * 提供系统性能监控数据查询和管理功能
 * 
 * <AUTHOR>
 * @since 2025-01-11
 */
@Slf4j
@RestController
@RequestMapping("/api/performance")
@Api(tags = "性能监控管理", description = "系统性能监控数据查询和管理")
public class PerformanceMonitorController {

    @Autowired
    private PerformanceMonitorAspect performanceMonitorAspect;
    
    @Autowired
    private SqlPerformanceInterceptor sqlPerformanceInterceptor;

    @ApiOperation(value = "获取方法性能统计", notes = "获取Controller和Service方法的性能统计信息")
    @GetMapping("/method-stats")
    public CommonResult<Map<String, Object>> getMethodPerformanceStats() {
        try {
            ConcurrentHashMap<String, PerformanceMonitorAspect.MethodStats> methodStats = 
                performanceMonitorAspect.getPerformanceStats();
            
            Map<String, Object> result = new HashMap<>();
            Map<String, Object> controllerStats = new HashMap<>();
            Map<String, Object> serviceStats = new HashMap<>();
            
            // 分离Controller和Service统计
            methodStats.forEach((methodName, stats) -> {
                Map<String, Object> statMap = new HashMap<>();
                statMap.put("totalCalls", stats.getTotalCalls());
                statMap.put("successCalls", stats.getSuccessCalls());
                statMap.put("errorCalls", stats.getErrorCalls());
                statMap.put("averageDuration", stats.getAverageDuration());
                statMap.put("maxDuration", stats.getMaxDuration());
                statMap.put("minDuration", stats.getMinDuration());
                statMap.put("successRate", String.format("%.2f%%", stats.getSuccessRate()));
                
                if (methodName.startsWith("SERVICE_")) {
                    serviceStats.put(methodName.substring(8), statMap);
                } else {
                    controllerStats.put(methodName, statMap);
                }
            });
            
            result.put("controllerStats", controllerStats);
            result.put("serviceStats", serviceStats);
            result.put("totalMethods", methodStats.size());
            
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("获取方法性能统计失败", e);
            return CommonResult.failed("获取方法性能统计失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取SQL性能统计", notes = "获取SQL查询的性能统计信息")
    @GetMapping("/sql-stats")
    public CommonResult<Map<String, Object>> getSqlPerformanceStats() {
        try {
            ConcurrentHashMap<String, SqlPerformanceInterceptor.SqlStats> sqlStats = 
                sqlPerformanceInterceptor.getSqlPerformanceStats();
            
            Map<String, Object> result = new HashMap<>();
            Map<String, Object> sqlStatsMap = new HashMap<>();
            
            long totalSlowQueries = 0;
            long totalExecutions = 0;
            
            for (Map.Entry<String, SqlPerformanceInterceptor.SqlStats> entry : sqlStats.entrySet()) {
                SqlPerformanceInterceptor.SqlStats stats = entry.getValue();
                
                Map<String, Object> statMap = new HashMap<>();
                statMap.put("totalExecutions", stats.getTotalExecutions());
                statMap.put("successExecutions", stats.getSuccessExecutions());
                statMap.put("errorExecutions", stats.getErrorExecutions());
                statMap.put("slowQueryCount", stats.getSlowQueryCount());
                statMap.put("averageDuration", stats.getAverageDuration());
                statMap.put("maxDuration", stats.getMaxDuration());
                statMap.put("minDuration", stats.getMinDuration());
                statMap.put("successRate", String.format("%.2f%%", stats.getSuccessRate()));
                statMap.put("slowQueryRate", String.format("%.2f%%", stats.getSlowQueryRate()));
                
                sqlStatsMap.put(entry.getKey(), statMap);
                
                totalSlowQueries += stats.getSlowQueryCount();
                totalExecutions += stats.getTotalExecutions();
            }
            
            result.put("sqlStats", sqlStatsMap);
            result.put("totalSqlMethods", sqlStats.size());
            result.put("totalSlowQueries", totalSlowQueries);
            result.put("totalExecutions", totalExecutions);
            result.put("overallSlowQueryRate", totalExecutions > 0 ? 
                String.format("%.2f%%", (double) totalSlowQueries / totalExecutions * 100) : "0.00%");
            
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("获取SQL性能统计失败", e);
            return CommonResult.failed("获取SQL性能统计失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取性能概览", notes = "获取系统整体性能概览信息")
    @GetMapping("/overview")
    public CommonResult<Map<String, Object>> getPerformanceOverview() {
        try {
            Map<String, Object> overview = new HashMap<>();
            
            // 方法性能概览
            ConcurrentHashMap<String, PerformanceMonitorAspect.MethodStats> methodStats = 
                performanceMonitorAspect.getPerformanceStats();
            
            long totalMethodCalls = 0;
            long totalMethodErrors = 0;
            long slowMethods = 0;
            
            for (PerformanceMonitorAspect.MethodStats stats : methodStats.values()) {
                totalMethodCalls += stats.getTotalCalls();
                totalMethodErrors += stats.getErrorCalls();
                if (stats.getAverageDuration() > 1000) { // 平均耗时超过1秒的方法
                    slowMethods++;
                }
            }
            
            // SQL性能概览
            ConcurrentHashMap<String, SqlPerformanceInterceptor.SqlStats> sqlStats = 
                sqlPerformanceInterceptor.getSqlPerformanceStats();
            
            long totalSqlExecutions = 0;
            long totalSqlErrors = 0;
            long totalSlowQueries = 0;
            
            for (SqlPerformanceInterceptor.SqlStats stats : sqlStats.values()) {
                totalSqlExecutions += stats.getTotalExecutions();
                totalSqlErrors += stats.getErrorExecutions();
                totalSlowQueries += stats.getSlowQueryCount();
            }
            
            Map<String, Object> methodOverview = new HashMap<>();
            methodOverview.put("totalMethods", methodStats.size());
            methodOverview.put("totalCalls", totalMethodCalls);
            methodOverview.put("totalErrors", totalMethodErrors);
            methodOverview.put("slowMethods", slowMethods);
            methodOverview.put("errorRate", totalMethodCalls > 0 ? 
                String.format("%.2f%%", (double) totalMethodErrors / totalMethodCalls * 100) : "0.00%");
            overview.put("methodOverview", methodOverview);
            
            Map<String, Object> sqlOverview = new HashMap<>();
            sqlOverview.put("totalSqlMethods", sqlStats.size());
            sqlOverview.put("totalExecutions", totalSqlExecutions);
            sqlOverview.put("totalErrors", totalSqlErrors);
            sqlOverview.put("totalSlowQueries", totalSlowQueries);
            sqlOverview.put("errorRate", totalSqlExecutions > 0 ? 
                String.format("%.2f%%", (double) totalSqlErrors / totalSqlExecutions * 100) : "0.00%");
            sqlOverview.put("slowQueryRate", totalSqlExecutions > 0 ? 
                String.format("%.2f%%", (double) totalSlowQueries / totalSqlExecutions * 100) : "0.00%");
            overview.put("sqlOverview", sqlOverview);
            
            return CommonResult.success(overview);
        } catch (Exception e) {
            log.error("获取性能概览失败", e);
            return CommonResult.failed("获取性能概览失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "清空性能统计", notes = "清空所有性能监控统计数据")
    @PostMapping("/clear-stats")
    public CommonResult<String> clearPerformanceStats(
            @ApiParam(value = "清空类型：all-全部, method-方法统计, sql-SQL统计", required = true)
            @RequestParam String type) {
        try {
            switch (type.toLowerCase()) {
                case "all":
                    performanceMonitorAspect.clearStats();
                    sqlPerformanceInterceptor.clearSqlStats();
                    log.info("已清空所有性能统计数据");
                    return CommonResult.success("已清空所有性能统计数据");
                    
                case "method":
                    performanceMonitorAspect.clearStats();
                    log.info("已清空方法性能统计数据");
                    return CommonResult.success("已清空方法性能统计数据");
                    
                case "sql":
                    sqlPerformanceInterceptor.clearSqlStats();
                    log.info("已清空SQL性能统计数据");
                    return CommonResult.success("已清空SQL性能统计数据");
                    
                default:
                    return CommonResult.failed("无效的清空类型，支持：all, method, sql");
            }
        } catch (Exception e) {
            log.error("清空性能统计失败", e);
            return CommonResult.failed("清空性能统计失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取慢查询TOP10", notes = "获取平均耗时最高的前10个SQL查询")
    @GetMapping("/slow-queries/top10")
    public CommonResult<Map<String, Object>> getTop10SlowQueries() {
        try {
            ConcurrentHashMap<String, SqlPerformanceInterceptor.SqlStats> sqlStats = 
                sqlPerformanceInterceptor.getSqlPerformanceStats();
            
            Map<String, Object> result = sqlStats.entrySet().stream()
                .filter(entry -> entry.getValue().getSlowQueryCount() > 0)
                .sorted((e1, e2) -> Long.compare(e2.getValue().getAverageDuration(), e1.getValue().getAverageDuration()))
                .limit(10)
                .collect(HashMap::new, 
                    (map, entry) -> {
                        SqlPerformanceInterceptor.SqlStats stats = entry.getValue();
                        Map<String, Object> statMap = new HashMap<>();
                        statMap.put("averageDuration", stats.getAverageDuration());
                        statMap.put("maxDuration", stats.getMaxDuration());
                        statMap.put("slowQueryCount", stats.getSlowQueryCount());
                        statMap.put("totalExecutions", stats.getTotalExecutions());
                        statMap.put("slowQueryRate", String.format("%.2f%%", stats.getSlowQueryRate()));
                        map.put(entry.getKey(), statMap);
                    },
                    HashMap::putAll);
            
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("获取慢查询TOP10失败", e);
            return CommonResult.failed("获取慢查询TOP10失败: " + e.getMessage());
        }
    }
}
