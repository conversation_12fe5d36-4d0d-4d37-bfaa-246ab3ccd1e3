package com.haoys.user.wechat.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.common.util.SendHttpRequestHelper;
import com.haoys.user.common.uuid.UUID;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@Api(tags = "公众号管理")
@RequestMapping("/wechat_config")
public class WechatConfigController extends BaseController {

    private final static String APP_ID = "wx75654a182064d47f";
    private final static String APP_SECRET = "8da343cab15f9f49af1ab83287c91e83";


    @RequestMapping("getConfigInfo")
    public CommonResult<Object> getConfigInfo(HttpServletRequest request, HttpServletResponse response, String url) {
        Map<String, String> map =  new HashMap<>();
        String access_token = "";
        //拼接地址获取token
        String tokenStr  = SendHttpRequestHelper.sendGet("https://api.weixin.qq.com/cgi-bin/token","grant_type=client_credential&appid="+APP_ID+"&secret="+APP_SECRET);
        log.info("tokenStr: {}", tokenStr);
        JSONObject token = JSON.parseObject(tokenStr);
        access_token = token.getString("access_token");
        //获取jsapi
        String jsapiStr  = SendHttpRequestHelper.sendGet("https://api.weixin.qq.com/cgi-bin/ticket/getticket","access_token="+access_token+"&type=jsapi");
        JSONObject jsapi = JSON.parseObject(jsapiStr);
        String jsapi_ticket = jsapi.getString("ticket");
        log.info("jsapi_ticket: {}", jsapi_ticket);
        /*****************获取签名signature********************/
        //生成随机字符串  noncestr
        String nonceStr = UUID.randomUUID().toString();
        //时间戳 timestamp，精确到秒
        String timestamp = Long.toString(System.currentTimeMillis() / 1000);
        //url
        //String Url = url;
        String Stitching = "jsapi_ticket="+jsapi_ticket+"&noncestr="+nonceStr+"&timestamp="+timestamp+"&url="+url;
        System.out.println("Stitching："+Stitching);
        //SHA1 加密
        java.security.MessageDigest digest = null;
        try {
            digest = java.security.MessageDigest.getInstance("SHA-1");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        digest.update(Stitching.getBytes());
        byte messageDigest[] = digest.digest();
        StringBuffer hexStr = new StringBuffer();
        // 字节数组转换为 十六进制 数
        for (int i = 0; i < messageDigest.length; i++) {
            String shaHex = Integer.toHexString(messageDigest[i] & 0xFF);
            if (shaHex.length() < 2) {
                hexStr.append(0);
            }
            hexStr.append(shaHex);
        }
        map.put("appid", APP_ID);
        map.put("timestamp", timestamp);
        map.put("nonceStr", nonceStr);
        map.put("jsapi_ticket", jsapi_ticket);
        map.put("signature", hexStr.toString());
        return CommonResult.success(map);
    }

}
