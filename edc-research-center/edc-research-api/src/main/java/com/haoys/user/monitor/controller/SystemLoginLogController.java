package com.haoys.user.monitor.controller;

import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.common.core.domain.model.LoginUserInfo;
import com.haoys.user.domain.vo.system.SystemLoginLogVo;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.domain.entity.SystemLoginLogQuery;
import com.haoys.user.service.SystemLoginLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;


@Api(tags = "系统访问记录", value = "SysLogininforController")
@RestController
@RequestMapping("/login-log/logging")
public class SystemLoginLogController extends BaseController
{
    @Autowired
    private SystemLoginLogService systemLoginLogService;

    @ApiOperation("获取系统访问记录列表")
    //@PreAuthorize("@ss.hasPermi('monitor:logininfor:list')")
    @GetMapping("/list")
    public CommonResult list(SystemLoginLogQuery logininfor,
                             @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                             @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize)
    {
        CommonPage<SystemLoginLogVo> list = systemLoginLogService.selectLoginRecordList(logininfor,pageNum,pageSize);
        return CommonResult.success(list);
    }


    @ApiOperation("删除系统访问记录列表")
    //@PreAuthorize("@ss.hasPermi('monitor:logininfor:remove')")
    @Log(title = "登录日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{infoIds}")
    public CommonResult remove(@PathVariable Long[] infoIds)
    {
        return returnResult(systemLoginLogService.deleteLogininforByIds(infoIds));
    }

    @ApiOperation("清空系统访问记录列表")
    //@PreAuthorize("@ss.hasPermi('monitor:logininfor:remove')")
    @Log(title = "登录日志", businessType = BusinessType.CLEAN)
    @DeleteMapping("/clean")
    public CommonResult clean()
    {
        LoginUserInfo loginUserInfo = SecurityUtils.getLoginUser();
        /*if(loginUserInfo.getBaseSystemUser().isAdmin()){
            systemLoginLogService.cleanLogininfor();
        }*/
        return CommonResult.success(null);
    }

    @ApiOperation("导出登录日志记录信息")
    @GetMapping("/importSystemLoginLog")
    public void importSystemLoginLog(HttpServletResponse response, SystemLoginLogQuery logininfor) {
        systemLoginLogService.importSystemLoginLog(response, logininfor);
//        return CommonResult.success(null);

    }
}
