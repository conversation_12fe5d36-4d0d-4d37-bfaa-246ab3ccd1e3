package com.haoys.user.exception.handler;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.servlet.http.HttpServletRequest;

/**
 * Web层异常处理器
 *
 * <p>专门处理HTTP请求处理过程中产生的各种异常</p>
 * <p>包括请求方法不支持、媒体类型不支持、请求体解析失败等Web层错误</p>
 * <p>提供标准的HTTP状态码和用户友好的错误信息</p>
 * <p>保留原始GlobalExceptionHandler的审计日志功能</p>
 *
 * <h3>处理的异常类型：</h3>
 * <ul>
 *   <li><b>请求方法不支持</b>：HttpRequestMethodNotSupportedException</li>
 *   <li><b>媒体类型不支持</b>：HttpMediaTypeNotSupportedException</li>
 *   <li><b>请求体不可读</b>：HttpMessageNotReadableException</li>
 *   <li><b>方法参数类型不匹配</b>：MethodArgumentTypeMismatchException</li>
 *   <li><b>找不到处理器</b>：NoHandlerFoundException</li>
 *   <li><b>缺少请求参数</b>：MissingServletRequestParameterException</li>
 * </ul>
 *
 * <h3>安全特性：</h3>
 * <ul>
 *   <li>标准化的HTTP状态码响应</li>
 *   <li>用户友好的错误提示信息</li>
 *   <li>详细的开发调试日志</li>
 *   <li>请求上下文信息记录</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
@Slf4j
@RestControllerAdvice
@Order(200) // 中等优先级
public class WebExceptionHandler extends BaseExceptionHandler {

    // ================================ HTTP请求异常处理 ================================

    /**
     * 处理请求方法不支持异常
     *
     * <p>当客户端使用了不被支持的HTTP方法时触发</p>
     * <p>例如：接口只支持POST，但客户端发送了GET请求</p>
     *
     * @param exception 请求方法不支持异常
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public CommonResult<String> handleMethodNotSupported(HttpRequestMethodNotSupportedException exception, 
                                                        HttpServletRequest request) {
        String supportedMethods = String.join(", ", exception.getSupportedMethods());
        
        String traceId = logExceptionWithTraceId(request, exception, "WARN", 
            String.format("请求方法不支持: 当前方法=%s, 支持的方法=%s", request.getMethod(), supportedMethods));
        
        String message = String.format("请求方法 %s 不被支持，支持的方法: %s", 
                                     request.getMethod(), supportedMethods);
        
        return createFailedResponseWithTraceId(request, ResultCode.BAD_REQUEST, message);
    }

    /**
     * 处理媒体类型不支持异常
     *
     * <p>当客户端发送的Content-Type不被支持时触发</p>
     * <p>例如：接口只支持application/json，但客户端发送了text/plain</p>
     *
     * @param exception 媒体类型不支持异常
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public CommonResult<String> handleMediaTypeNotSupported(HttpMediaTypeNotSupportedException exception, 
                                                           HttpServletRequest request) {
        String contentType = request.getContentType();
        String supportedTypes = exception.getSupportedMediaTypes().toString();
        
        String traceId = logExceptionWithTraceId(request, exception, "WARN", 
            String.format("媒体类型不支持: 当前类型=%s, 支持的类型=%s", contentType, supportedTypes));
        
        String message = String.format("不支持的媒体类型: %s，支持的类型: %s", 
                                     contentType, supportedTypes);
        
        return createFailedResponseWithTraceId(request, ResultCode.BAD_REQUEST, message);
    }

    /**
     * 处理请求体不可读异常
     *
     * <p>当请求体格式错误或无法解析时触发</p>
     * <p>例如：JSON格式错误、字段类型不匹配等</p>
     *
     * @param exception 请求体不可读异常
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public CommonResult<String> handleMessageNotReadable(HttpMessageNotReadableException exception, 
                                                        HttpServletRequest request) {
        String traceId = logExceptionWithTraceId(request, exception, "WARN", "请求体解析失败");
        
        String message = "请求体格式错误";
        String exceptionMessage = exception.getMessage();
        
        // 根据具体错误提供更友好的提示
        if (exceptionMessage != null) {
            if (exceptionMessage.contains("JSON parse error")) {
                message = "JSON格式错误，请检查请求数据格式";
            } else if (exceptionMessage.contains("Required request body is missing")) {
                message = "缺少请求体，请提供必要的请求数据";
            } else if (exceptionMessage.contains("Cannot deserialize")) {
                message = "数据类型不匹配，请检查字段类型";
            }
        }
        
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_PARAM_FAIL, message);
    }

    /**
     * 处理方法参数类型不匹配异常
     *
     * <p>当URL参数或查询参数类型转换失败时触发</p>
     * <p>例如：期望数字类型，但传入了字符串</p>
     *
     * @param exception 方法参数类型不匹配异常
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public CommonResult<String> handleTypeMismatch(MethodArgumentTypeMismatchException exception, 
                                                  HttpServletRequest request) {
        String parameterName = exception.getName();
        String parameterValue = String.valueOf(exception.getValue());
        String requiredType = exception.getRequiredType() != null ? 
            exception.getRequiredType().getSimpleName() : "未知";
        
        String traceId = logExceptionWithTraceId(request, exception, "WARN", 
            String.format("参数类型不匹配: 参数名=%s, 参数值=%s, 期望类型=%s", 
                         parameterName, parameterValue, requiredType));
        
        String message = String.format("参数 '%s' 的值 '%s' 类型不正确，期望类型: %s", 
                                     parameterName, parameterValue, requiredType);
        
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_PARAM_FAIL, message);
    }

    /**
     * 处理找不到处理器异常
     *
     * <p>当请求的URL没有对应的处理器时触发</p>
     * <p>通常表示404错误</p>
     *
     * @param exception 找不到处理器异常
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public CommonResult<String> handleNoHandlerFound(NoHandlerFoundException exception, 
                                                    HttpServletRequest request) {
        String traceId = logExceptionWithTraceId(request, exception, "WARN", 
            String.format("找不到处理器: 请求方法=%s, 请求URL=%s", 
                         exception.getHttpMethod(), exception.getRequestURL()));
        
        String message = String.format("请求的资源不存在: %s %s", 
                                     exception.getHttpMethod(), exception.getRequestURL());
        
        return createFailedResponseWithTraceId(request, ResultCode.VALIDATE_FAILED, message);
    }

    /**
     * 处理缺少请求参数异常
     *
     * <p>当必需的请求参数缺失时触发</p>
     *
     * @param exception 缺少请求参数异常
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public CommonResult<String> handleMissingParameter(MissingServletRequestParameterException exception, 
                                                      HttpServletRequest request) {
        String parameterName = exception.getParameterName();
        String parameterType = exception.getParameterType();
        
        String traceId = logExceptionWithTraceId(request, exception, "WARN", 
            String.format("缺少请求参数: 参数名=%s, 参数类型=%s", parameterName, parameterType));
        
        String message = String.format("缺少必需的请求参数: %s (%s)", parameterName, parameterType);
        
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_PARAM_FAIL, message);
    }

    /**
     * 处理异步请求超时异常
     *
     * <p>当异步请求处理超时时触发</p>
     *
     * @param exception 异步请求超时异常
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(org.springframework.web.context.request.async.AsyncRequestTimeoutException.class)
    public CommonResult<String> handleAsyncTimeout(org.springframework.web.context.request.async.AsyncRequestTimeoutException exception, 
                                                  HttpServletRequest request) {
        String traceId = logExceptionWithTraceId(request, exception, "WARN", "异步请求超时");
        
        return createFailedResponseWithTraceId(request, ResultCode.SYSTEM_FREQUENT_REQUEST, 
                                             "请求处理超时，请稍后重试");
    }

    /**
     * 处理请求绑定异常
     *
     * <p>当请求参数绑定到对象失败时触发</p>
     *
     * @param exception 请求绑定异常
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(org.springframework.web.bind.ServletRequestBindingException.class)
    public CommonResult<String> handleServletRequestBinding(org.springframework.web.bind.ServletRequestBindingException exception, 
                                                           HttpServletRequest request) {
        String traceId = logExceptionWithTraceId(request, exception, "WARN", "请求参数绑定失败");
        
        String message = "请求参数绑定失败";
        String exceptionMessage = exception.getMessage();
        
        if (exceptionMessage != null) {
            if (exceptionMessage.contains("Missing header")) {
                message = "缺少必需的请求头";
            } else if (exceptionMessage.contains("Missing cookie")) {
                message = "缺少必需的Cookie";
            } else if (exceptionMessage.contains("Missing request")) {
                message = "缺少必需的请求参数";
            }
        }
        
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_PARAM_FAIL, message);
    }
}
