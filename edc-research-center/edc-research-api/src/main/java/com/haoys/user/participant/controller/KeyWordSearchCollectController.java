package com.haoys.user.participant.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.bussiness.RedisKeyContants;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.export.ProjectSearchCollectParam;
import com.haoys.user.domain.vo.SearchLogVo;
import com.haoys.user.domain.vo.export.ProjectSearchCollectVo;
import com.haoys.user.service.KeyWordSearchCollectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@RestController
@Api(tags = "搜索条件和搜索历史管理")
@RequestMapping("/keywordSearch")
public class KeyWordSearchCollectController {

    @Autowired
    private RedisTemplateService redisTemplateService;
    @Autowired
    private KeyWordSearchCollectService keyWordSearchCollectService;

    @Log(title = "保存搜索条件记录", businessType = BusinessType.INSERT)
    @ApiOperation("保存搜索条件记录")
    @PostMapping("/saveSearchLog")
    public CommonResult<String> saveSearchLog(@RequestBody SearchLogVo vo){
        String key = RedisKeyContants.EXPORT_SEARCH_LOG + "_"+vo.getProjectId()+"-"+ SecurityUtils.getUserId();
        String searchLogStr = vo.getSearchKeyword();
        if (StringUtils.isNotEmpty(searchLogStr)) {
            Set<Object> set = new HashSet<>();
            List<HashMap> searchLog = new ArrayList<>();
            List<HashMap> hashMaps = JSON.parseArray(searchLogStr, HashMap.class);
            hashMaps.forEach(map -> {
                Object formDetailId = map.get("formDetailId");
                if (!set.contains(formDetailId)) {
                    searchLog.add(map);
                    set.add(formDetailId);
                }
            });
            if (CollectionUtil.isNotEmpty(searchLog)) {
                int size = searchLog.size();
                if (size < 20) {
                    Object dataStrObj = redisTemplateService.get(key);
                    if (dataStrObj != null && dataStrObj != "") {
                        List<HashMap> data = (List<HashMap>) dataStrObj;
                        int i2 = 0;
                        int oldSize = data.size();
                        for (int i = (size-1); i < 20; i++) {
                            if (i2<oldSize){
                                HashMap search = data.get(i2);
                                if (search != null) {
                                    Object formDetailId = search.get("formDetailId");
                                    if (!set.contains(formDetailId)) {
                                        searchLog.add(search);
                                        i2++;
                                    }
                                }
                            }
                        }
                    }
                }
                redisTemplateService.set(key, searchLog);
            }
        }
        return CommonResult.success("保存成功");
    }

    @ApiOperation("查询搜索历史记录")
    @GetMapping("/getSearchLog")
    public CommonResult<Object> getSearchLog(Long projectId){
        String key = RedisKeyContants.EXPORT_SEARCH_LOG + "_"+projectId+"-"+SecurityUtils.getUserId();
        Object dataStrObj = redisTemplateService.get(key);
        if (dataStrObj!=null){
            return CommonResult.success(dataStrObj);
        }
        return CommonResult.success(new ArrayList<>());
    }



    @Log(title = "删除搜索历史记录", businessType = BusinessType.DELETE)
    @ApiOperation("删除搜索历史记录")
    @GetMapping("/removeSearchLog")
    public CommonResult<Object> removeSearchLog(String diseaseType){
        String key = RedisKeyContants.EXPORT_SEARCH_LOG + "_" + diseaseType + "-" +SecurityUtils.getUserId();
        redisTemplateService.del(key);
        return CommonResult.success(null);
    }

    @Log(title = "保存搜藏条件", businessType = BusinessType.INSERT)
    @ApiOperation("保存搜藏条件")
    @RequestMapping(value = "/saveProjectExportCollectRecord", method = RequestMethod.POST)
    public CommonResult<Object> saveProjectExportCollectRecord(@RequestBody ProjectSearchCollectParam projectSearchCollectParam){
        CustomResult data = keyWordSearchCollectService.saveDiseaseSearchCollectRecord(projectSearchCollectParam);
        return CommonResult.success(data);
    }

    @Log(title = "删除搜藏条件", businessType = BusinessType.DELETE)
    @ApiOperation("删除搜藏条件")
    @RequestMapping(value = "/removeProjectExportCollectRecord", method = RequestMethod.POST)
    public CommonResult<Object> removeProjectExportCollectRecord(String id){
        CustomResult data = keyWordSearchCollectService.deleteDiseaseSearchCollectRecord(id);
        return CommonResult.success(data.getData());
    }

    @ApiOperation("查询搜藏条件分页列表")
    @RequestMapping(value = "/getDiseaseSearchCollectListForPage", method = RequestMethod.GET)
    public CommonResult<CommonPage<ProjectSearchCollectVo>> getDiseaseSearchCollectListForPage(@RequestParam(value = "dataBaseId", required = false) String dataBaseId,
                                                                                               @RequestParam(value = "diseaseType", required = false) String diseaseType,
                                                                                               @RequestParam(value = "systemUseType") String systemUseType,
                                                                                               @RequestParam(value = "searchType") String searchType,
                                                                                               @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                                               @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        CommonPage<ProjectSearchCollectVo> projectTesteeExportList = keyWordSearchCollectService.getDiseaseSearchCollectListForPage(dataBaseId, diseaseType, systemUseType, searchType, pageNum, pageSize);
        return CommonResult.success(projectTesteeExportList);
    }
}
