package com.haoys.user.security.audit;

import com.haoys.user.security.audit.model.SecurityAuditEvent;

/**
 * 安全审计服务接口
 * 
 * <p>提供安全事件记录、查询、分析等功能</p>
 * <p>用于监控系统安全状况，记录安全相关的操作和异常</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0
 */
public interface SecurityAuditService {

    /**
     * 记录安全事件
     * 
     * @param auditEvent 安全审计事件对象
     */
    void recordEvent(SecurityAuditEvent auditEvent);

    /**
     * 记录安全事件（简化版）
     * 
     * @param eventType 事件类型
     * @param userName 用户名
     * @param clientIp 客户端IP
     * @param requestPath 请求路径
     * @param description 事件描述
     */
    void recordEvent(String eventType, String userName, String clientIp, String requestPath, String description);

    /**
     * 记录登录事件
     * 
     * @param userName 用户名
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @param success 是否成功
     * @param failureReason 失败原因（成功时为null）
     */
    void recordLoginEvent(String userName, String clientIp, String userAgent, boolean success, String failureReason);

    /**
     * 记录权限访问事件
     * 
     * @param userName 用户名
     * @param clientIp 客户端IP
     * @param requestPath 请求路径
     * @param action 操作类型
     * @param success 是否成功
     * @param failureReason 失败原因（成功时为null）
     */
    void recordAccessEvent(String userName, String clientIp, String requestPath, String action, boolean success, String failureReason);

    /**
     * 记录数据操作事件
     * 
     * @param userName 用户名
     * @param clientIp 客户端IP
     * @param operation 操作类型（CREATE、UPDATE、DELETE、READ）
     * @param resourceType 资源类型
     * @param resourceId 资源ID
     * @param success 是否成功
     */
    void recordDataOperationEvent(String userName, String clientIp, String operation, String resourceType, String resourceId, boolean success);

    /**
     * 记录异常安全事件
     * 
     * @param userName 用户名
     * @param clientIp 客户端IP
     * @param requestPath 请求路径
     * @param exceptionType 异常类型
     * @param exceptionMessage 异常信息
     */
    void recordExceptionEvent(String userName, String clientIp, String requestPath, String exceptionType, String exceptionMessage);

    /**
     * 检查是否存在安全风险
     * 
     * @param clientIp 客户端IP
     * @param timeWindowMinutes 时间窗口（分钟）
     * @return 安全风险评估结果
     */
    SecurityRiskAssessment assessSecurityRisk(String clientIp, int timeWindowMinutes);

    /**
     * 获取用户的安全事件统计
     * 
     * @param userName 用户名
     * @param timeWindowHours 时间窗口（小时）
     * @return 安全事件统计信息
     */
    SecurityEventStatistics getUserSecurityStatistics(String userName, int timeWindowHours);

    /**
     * 获取IP的安全事件统计
     * 
     * @param clientIp 客户端IP
     * @param timeWindowHours 时间窗口（小时）
     * @return 安全事件统计信息
     */
    SecurityEventStatistics getIpSecurityStatistics(String clientIp, int timeWindowHours);

    /**
     * 安全风险评估结果
     */
    class SecurityRiskAssessment {
        private final String riskLevel;
        private final int riskScore;
        private final String description;
        private final boolean shouldBlock;

        public SecurityRiskAssessment(String riskLevel, int riskScore, String description, boolean shouldBlock) {
            this.riskLevel = riskLevel;
            this.riskScore = riskScore;
            this.description = description;
            this.shouldBlock = shouldBlock;
        }

        public String getRiskLevel() { return riskLevel; }
        public int getRiskScore() { return riskScore; }
        public String getDescription() { return description; }
        public boolean shouldBlock() { return shouldBlock; }
    }

    /**
     * 安全事件统计信息
     */
    class SecurityEventStatistics {
        private final long totalEvents;
        private final long failedLoginAttempts;
        private final long accessDeniedEvents;
        private final long exceptionEvents;
        private final String mostFrequentEventType;

        public SecurityEventStatistics(long totalEvents, long failedLoginAttempts, long accessDeniedEvents, long exceptionEvents, String mostFrequentEventType) {
            this.totalEvents = totalEvents;
            this.failedLoginAttempts = failedLoginAttempts;
            this.accessDeniedEvents = accessDeniedEvents;
            this.exceptionEvents = exceptionEvents;
            this.mostFrequentEventType = mostFrequentEventType;
        }

        public long getTotalEvents() { return totalEvents; }
        public long getFailedLoginAttempts() { return failedLoginAttempts; }
        public long getAccessDeniedEvents() { return accessDeniedEvents; }
        public long getExceptionEvents() { return exceptionEvents; }
        public String getMostFrequentEventType() { return mostFrequentEventType; }
    }
}
