package com.haoys.user.service;

import com.haoys.user.model.ProjectConfigModule;
import com.haoys.user.common.api.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 项目配置分组表Service测试类
 * 测试完整的CRUD操作、批量操作、复杂查询等功能
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 * @date 2025-07-11 00:31:50
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class ProjectConfigModuleServiceTest {

    @Autowired
    private ProjectConfigModuleService projectConfigModuleService;

    private ProjectConfigModule testProjectConfigModule;
    private Long testModuleId;

    @BeforeEach
    void setUp() {
        // 创建测试数据
        testProjectConfigModule = new ProjectConfigModule();
        testProjectConfigModule.setModuleId(1L);
        testProjectConfigModule.setModuleName("test_moduleName");
        testProjectConfigModule.setSourceFrom("test_sourceFrom");
        testProjectConfigModule.setSort(1);
        testProjectConfigModule.setCreateUserId("test_createUserId");
        testProjectConfigModule.setCreateTime(new Date());
        testProjectConfigModule.setTenantId("test_tenantId");
        testProjectConfigModule.setPlatformId("test_platformId");
    }

    @Test
    @DisplayName("测试创建项目配置分组表")
    void testCreate() {
        log.info("开始测试创建项目配置分组表");
        
        CommonResult<ProjectConfigModule> result = projectConfigModuleService.create(testProjectConfigModule);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertNotNull(result.getData().getModuleId());
        
        testModuleId = result.getData().getModuleId();
        log.info("创建项目配置分组表成功，ID: {}", testModuleId);
    }

    @Test
    @DisplayName("测试查询项目配置分组表")
    void testGetById() {
        log.info("开始测试查询项目配置分组表");
        
        // 先创建数据
        CommonResult<ProjectConfigModule> createResult = projectConfigModuleService.create(testProjectConfigModule);
        assertTrue(createResult.isSuccess());
        testModuleId = createResult.getData().getModuleId();
        
        // 查询数据
        CommonResult<ProjectConfigModule> result = projectConfigModuleService.getById(testModuleId);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(testModuleId, result.getData().getModuleId());
        
        log.info("查询项目配置分组表成功: {}", result.getData());
    }

    @Test
    @DisplayName("测试更新项目配置分组表")
    void testUpdate() {
        log.info("开始测试更新项目配置分组表");
        
        // 先创建数据
        CommonResult<ProjectConfigModule> createResult = projectConfigModuleService.create(testProjectConfigModule);
        assertTrue(createResult.isSuccess());
        ProjectConfigModule created = createResult.getData();
        
        // 修改数据（只有当表中存在updateTime字段时才设置）
        created.setModuleName("test_moduleName");
        
        // 更新数据
        CommonResult<ProjectConfigModule> result = projectConfigModuleService.update(created);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        
        log.info("更新项目配置分组表成功: {}", result.getData());
    }

    @Test
    @DisplayName("测试删除项目配置分组表")
    void testDelete() {
        log.info("开始测试删除项目配置分组表");
        
        // 先创建数据
        CommonResult<ProjectConfigModule> createResult = projectConfigModuleService.create(testProjectConfigModule);
        assertTrue(createResult.isSuccess());
        testModuleId = createResult.getData().getModuleId();
        
        // 删除数据
        CommonResult<Void> result = projectConfigModuleService.deleteById(testModuleId);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        
        // 验证删除
        CommonResult<ProjectConfigModule> getResult = projectConfigModuleService.getById(testModuleId);
        assertFalse(getResult.isSuccess());
        
        log.info("删除项目配置分组表成功");
    }

    @Test
    @DisplayName("测试条件查询项目配置分组表")
    void testListByCondition() {
        log.info("开始测试条件查询项目配置分组表");
        
        // 先创建数据
        CommonResult<ProjectConfigModule> createResult = projectConfigModuleService.create(testProjectConfigModule);
        assertTrue(createResult.isSuccess());
        
        // 构造查询条件
        Map<String, Object> params = new HashMap<>();
        // params.put("字段名", "查询值");
        
        // 条件查询
        CommonResult<List<ProjectConfigModule>> result = projectConfigModuleService.listByCondition(params);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        
        log.info("条件查询项目配置分组表成功，结果数量: {}", result.getData().size());
    }

}
