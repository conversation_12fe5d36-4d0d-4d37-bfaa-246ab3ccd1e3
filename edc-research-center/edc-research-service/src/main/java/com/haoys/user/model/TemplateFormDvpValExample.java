package com.haoys.user.model;

import java.util.ArrayList;
import java.util.List;

public class TemplateFormDvpValExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TemplateFormDvpValExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdIsNull() {
            addCriterion("dvp_rule_id is null");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdIsNotNull() {
            addCriterion("dvp_rule_id is not null");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdEqualTo(Long value) {
            addCriterion("dvp_rule_id =", value, "dvpRuleId");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdNotEqualTo(Long value) {
            addCriterion("dvp_rule_id <>", value, "dvpRuleId");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdGreaterThan(Long value) {
            addCriterion("dvp_rule_id >", value, "dvpRuleId");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdGreaterThanOrEqualTo(Long value) {
            addCriterion("dvp_rule_id >=", value, "dvpRuleId");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdLessThan(Long value) {
            addCriterion("dvp_rule_id <", value, "dvpRuleId");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdLessThanOrEqualTo(Long value) {
            addCriterion("dvp_rule_id <=", value, "dvpRuleId");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdIn(List<Long> values) {
            addCriterion("dvp_rule_id in", values, "dvpRuleId");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdNotIn(List<Long> values) {
            addCriterion("dvp_rule_id not in", values, "dvpRuleId");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdBetween(Long value1, Long value2) {
            addCriterion("dvp_rule_id between", value1, value2, "dvpRuleId");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdNotBetween(Long value1, Long value2) {
            addCriterion("dvp_rule_id not between", value1, value2, "dvpRuleId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andVisitIdIsNull() {
            addCriterion("visit_id is null");
            return (Criteria) this;
        }

        public Criteria andVisitIdIsNotNull() {
            addCriterion("visit_id is not null");
            return (Criteria) this;
        }

        public Criteria andVisitIdEqualTo(Long value) {
            addCriterion("visit_id =", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdNotEqualTo(Long value) {
            addCriterion("visit_id <>", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdGreaterThan(Long value) {
            addCriterion("visit_id >", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("visit_id >=", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdLessThan(Long value) {
            addCriterion("visit_id <", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdLessThanOrEqualTo(Long value) {
            addCriterion("visit_id <=", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdIn(List<Long> values) {
            addCriterion("visit_id in", values, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdNotIn(List<Long> values) {
            addCriterion("visit_id not in", values, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdBetween(Long value1, Long value2) {
            addCriterion("visit_id between", value1, value2, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdNotBetween(Long value1, Long value2) {
            addCriterion("visit_id not between", value1, value2, "visitId");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNull() {
            addCriterion("form_id is null");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNotNull() {
            addCriterion("form_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormIdEqualTo(Long value) {
            addCriterion("form_id =", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotEqualTo(Long value) {
            addCriterion("form_id <>", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThan(Long value) {
            addCriterion("form_id >", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_id >=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThan(Long value) {
            addCriterion("form_id <", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThanOrEqualTo(Long value) {
            addCriterion("form_id <=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdIn(List<Long> values) {
            addCriterion("form_id in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotIn(List<Long> values) {
            addCriterion("form_id not in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdBetween(Long value1, Long value2) {
            addCriterion("form_id between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotBetween(Long value1, Long value2) {
            addCriterion("form_id not between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNull() {
            addCriterion("group_id is null");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNotNull() {
            addCriterion("group_id is not null");
            return (Criteria) this;
        }

        public Criteria andGroupIdEqualTo(Long value) {
            addCriterion("group_id =", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotEqualTo(Long value) {
            addCriterion("group_id <>", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThan(Long value) {
            addCriterion("group_id >", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThanOrEqualTo(Long value) {
            addCriterion("group_id >=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThan(Long value) {
            addCriterion("group_id <", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThanOrEqualTo(Long value) {
            addCriterion("group_id <=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdIn(List<Long> values) {
            addCriterion("group_id in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotIn(List<Long> values) {
            addCriterion("group_id not in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdBetween(Long value1, Long value2) {
            addCriterion("group_id between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotBetween(Long value1, Long value2) {
            addCriterion("group_id not between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdIsNull() {
            addCriterion("form_detail_id is null");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdIsNotNull() {
            addCriterion("form_detail_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdEqualTo(Long value) {
            addCriterion("form_detail_id =", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdNotEqualTo(Long value) {
            addCriterion("form_detail_id <>", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdGreaterThan(Long value) {
            addCriterion("form_detail_id >", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_detail_id >=", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdLessThan(Long value) {
            addCriterion("form_detail_id <", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdLessThanOrEqualTo(Long value) {
            addCriterion("form_detail_id <=", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdIn(List<Long> values) {
            addCriterion("form_detail_id in", values, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdNotIn(List<Long> values) {
            addCriterion("form_detail_id not in", values, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdBetween(Long value1, Long value2) {
            addCriterion("form_detail_id between", value1, value2, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdNotBetween(Long value1, Long value2) {
            addCriterion("form_detail_id not between", value1, value2, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdIsNull() {
            addCriterion("form_table_id is null");
            return (Criteria) this;
        }

        public Criteria andFormTableIdIsNotNull() {
            addCriterion("form_table_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormTableIdEqualTo(Long value) {
            addCriterion("form_table_id =", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdNotEqualTo(Long value) {
            addCriterion("form_table_id <>", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdGreaterThan(Long value) {
            addCriterion("form_table_id >", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_table_id >=", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdLessThan(Long value) {
            addCriterion("form_table_id <", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdLessThanOrEqualTo(Long value) {
            addCriterion("form_table_id <=", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdIn(List<Long> values) {
            addCriterion("form_table_id in", values, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdNotIn(List<Long> values) {
            addCriterion("form_table_id not in", values, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdBetween(Long value1, Long value2) {
            addCriterion("form_table_id between", value1, value2, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdNotBetween(Long value1, Long value2) {
            addCriterion("form_table_id not between", value1, value2, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFieldTypeIsNull() {
            addCriterion("field_type is null");
            return (Criteria) this;
        }

        public Criteria andFieldTypeIsNotNull() {
            addCriterion("field_type is not null");
            return (Criteria) this;
        }

        public Criteria andFieldTypeEqualTo(String value) {
            addCriterion("field_type =", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeNotEqualTo(String value) {
            addCriterion("field_type <>", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeGreaterThan(String value) {
            addCriterion("field_type >", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeGreaterThanOrEqualTo(String value) {
            addCriterion("field_type >=", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeLessThan(String value) {
            addCriterion("field_type <", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeLessThanOrEqualTo(String value) {
            addCriterion("field_type <=", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeLike(String value) {
            addCriterion("field_type like", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeNotLike(String value) {
            addCriterion("field_type not like", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeIn(List<String> values) {
            addCriterion("field_type in", values, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeNotIn(List<String> values) {
            addCriterion("field_type not in", values, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeBetween(String value1, String value2) {
            addCriterion("field_type between", value1, value2, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeNotBetween(String value1, String value2) {
            addCriterion("field_type not between", value1, value2, "fieldType");
            return (Criteria) this;
        }

        public Criteria andDicResourceIsNull() {
            addCriterion("dic_resource is null");
            return (Criteria) this;
        }

        public Criteria andDicResourceIsNotNull() {
            addCriterion("dic_resource is not null");
            return (Criteria) this;
        }

        public Criteria andDicResourceEqualTo(String value) {
            addCriterion("dic_resource =", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceNotEqualTo(String value) {
            addCriterion("dic_resource <>", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceGreaterThan(String value) {
            addCriterion("dic_resource >", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceGreaterThanOrEqualTo(String value) {
            addCriterion("dic_resource >=", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceLessThan(String value) {
            addCriterion("dic_resource <", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceLessThanOrEqualTo(String value) {
            addCriterion("dic_resource <=", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceLike(String value) {
            addCriterion("dic_resource like", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceNotLike(String value) {
            addCriterion("dic_resource not like", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceIn(List<String> values) {
            addCriterion("dic_resource in", values, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceNotIn(List<String> values) {
            addCriterion("dic_resource not in", values, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceBetween(String value1, String value2) {
            addCriterion("dic_resource between", value1, value2, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceNotBetween(String value1, String value2) {
            addCriterion("dic_resource not between", value1, value2, "dicResource");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
