package com.haoys.user.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class FlowFormSetVo {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "表单id")
    private Long formId;

    @ApiModelProperty(value = "表单名称")
    private String formName;

    @ApiModelProperty(value = "流程集合")
    private List<FlowFormVo> flowFormVos;

}
