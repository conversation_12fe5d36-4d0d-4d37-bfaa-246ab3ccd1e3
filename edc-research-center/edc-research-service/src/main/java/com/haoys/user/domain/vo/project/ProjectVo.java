package com.haoys.user.domain.vo.project;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.haoys.user.domain.vo.system.OrganizationVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class ProjectVo implements Serializable {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "项目id")
    private Long id;

    @ApiModelProperty(value = "项目名称")
    private String name;

    @ApiModelProperty(value = "项目编号")
    private String code;

    @ApiModelProperty(value = "组长单位")
    private String groupUnit;

    @ApiModelProperty(value = "组长单位名称")
    private String groupUnitName;

    @ApiModelProperty(value = "研究描述")
    private String description;

    @ApiModelProperty(value = "项目申请code 1-申请加入项目 2-已提交申请 3-已申请通过 4-已拒绝申请 5-禁止访问")
    private String status;

    @ApiModelProperty(value = "项目申请状态视图值")
    private String statusView;

    @ApiModelProperty(value = "是否公开项目 0-不公开 1-公开")
    private Boolean ifPublic;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "项目开始时间")
    private Date startDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "项目结束时间")
    private Date endDate;

    @ApiModelProperty(value = "研究领域ID-字典")
    private String researchArea;

    @ApiModelProperty(value = "研究领域视图值")
    private String researchAreaValue;

    @ApiModelProperty(value = "发起单位")
    private String nitiator;

    @ApiModelProperty(value = "发起单位名称")
    private String nitiatorName;

    @ApiModelProperty(value = "发起人")
    private String sponsor;

    @ApiModelProperty(value = "是否显示申请列表按钮")
    private Boolean showApplyListButton = false;

    @ApiModelProperty(value = "是否显示项目配置按钮")
    private Boolean showProjectConfigButton = false;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "申请人id")
    private String applyUserId;

    @ApiModelProperty(value = "申请机构id")
    private String applyOrgId;

    @ApiModelProperty(value = "是否启用药方分析")
    private Boolean enablePrescriptionAnalysis = false;

    @ApiModelProperty(value = "项目性质-前瞻性和回顾性")
    private String projectNature;

    @ApiModelProperty(value = "注册号")
    private String registeNumber;

    @ApiModelProperty(value = "备案号")
    private String recordNumber;

    @ApiModelProperty(value = "参与者录入总量")
    private Integer totalCount;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

    @ApiModelProperty(value = "计划开始时间")
    private Date planStartDate;

    @ApiModelProperty(value = "计划入组数量")
    private Integer planGroupNum;

    @ApiModelProperty(value = "申办单位联系方式")
    private String nitiatorContact;

    @ApiModelProperty(value = "计划参与研究中心数量")
    private Integer involvedOrgNum;

    @ApiModelProperty(value = "组长单位主要研究者")
    private String guResearcher;

    @ApiModelProperty(value = "组长单位主要研究者联系方式")
    private String guContact;

    @ApiModelProperty(value = "项目类型")
    private String projectType;

    @ApiModelProperty(value = "项目研究中心")
    private List<OrganizationVo> orgList = new ArrayList<>();
    
    @ApiModelProperty(value = "已经加入的项目研究中心")
    private List<ProjectTesteeOrgVo> joinOrgList = new ArrayList<>();

    @ApiModelProperty(value = "角色开关")
    private  Integer projectSwitch;

    @ApiModelProperty(value = "项目中心id")
    private String ownerOrgId;

    @ApiModelProperty(value = "数据库id")
    private String databaseId;

    @ApiModelProperty(value = "数据库来源")
    private String databaseFrom;

    @ApiModelProperty(value = "研究方式 1-单中心 2-多中心")
    private String researchMethod;

}
