package com.haoys.user.domain.param.project;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ProjectTesteeConfigParam {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "绑定配置来源 [{'visitId':'1','formId':'1','variableId':'1', 'label':'基本信息','value':'输入值', 'extands':'扩展信息'}]")
    private Object configData;

    @ApiModelProperty(value = "绑定类型 1-参与者自建病历 2-医生创建病历")
    private String bindType;

    @ApiModelProperty(value = "绑定配置说明")
    private Object bindConfig;

    @ApiModelProperty(value = "必选配置项说明")
    private Object bindBaseConfig;

    @ApiModelProperty(value = "数据状态")
    private String status;

    @ApiModelProperty(value = "扩展字段")
    private String expand;

    @ApiModelProperty(value = "创建人")
    private String createUserId;

}
