//package com.haoys.mis.service.impl;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.haoys.mis.common.api.CommonPage;
//import com.haoys.mis.common.constants.DefineConstant;
//import com.haoys.mis.common.util.BeanUtils;
//import com.haoys.mis.common.util.JsonUtils;
//import com.haoys.mis.common.util.StringUtils;
//import com.haoys.mis.domain.mongodbvo.ProjectTesteeVariableValueVo;
//import com.haoys.mis.domain.ecrf.vo.ProjectTesteeVariableESResultVo;
//import com.haoys.mis.domain.ecrf.vo.ProjectTesteeVariableResultVo;
//import com.haoys.mis.elasticsearch.ElasticSearchUtil;
//import com.haoys.mis.service.ProjectTesteeResultService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.mongodb.core.aggregation.Aggregation;
//import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
//import org.springframework.data.mongodb.core.aggregation.MatchOperation;
//import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
//import org.springframework.data.mongodb.core.aggregation.UnwindOperation;
//import org.springframework.data.mongodb.core.mapreduce.GroupBy;
//import org.springframework.data.mongodb.core.query.Criteria;
//import org.springframework.stereotype.Service;
//
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.HashMap;
//import java.util.LinkedHashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
//@Slf4j
//@Service
//public class ProjectTesteeTestService {
//
//    @Autowired
//    private ProjectTesteeResultService projectTesteeResultService;
//
//    public Map<String, Object> testQueryEs(Map<String, Object> requestContext) throws Exception {
//        Map<String, Object> equalCondition = new HashMap<>();
//        equalCondition.put("cust_no_s",requestContext.get("custNo"));
//        Map<String, Object> rangeCondition = new HashMap<>();
//        rangeCondition.put("txn_amt_d","(,"+requestContext.get("rangeAmt") + "]");
//        List<String> fieldNullTest = Arrays.asList("txn_comment_s");
//        List<String> fieldsNotBlank = null;
//        List<String> fieldsNotNull = null;
//        List<String> fieldsIsNull = null;
//        List<String> fieldsIsBlank = null;
//        if ("1".equals(requestContext.get("nilFlag"))) {
//            fieldsNotBlank = fieldNullTest;
//        }
//        if ("2".equals(requestContext.get("nilFlag"))) {
//            fieldsNotNull = fieldNullTest;
//        }
//        if ("3".equals(requestContext.get("nilFlag"))) {
//            fieldsIsNull = fieldNullTest;
//        }
//        if ("4".equals(requestContext.get("nilFlag"))) {
//            fieldsIsBlank = fieldNullTest;
//        }
//        List<String> orderby = Arrays.asList("-txn_date_s");
//        return ElasticSearchUtil.queryForElasticSearchTemplate("income_expense_analysis",equalCondition, null, rangeCondition,
//                                        fieldsNotBlank,fieldsNotNull, fieldsIsNull, fieldsIsBlank, orderby, false, 1,10);
//    }
//
//    public Map<String, Object> testEsInsert(Map<String, Object> requestContext){
//        Map<String, Object> insertMap = new HashMap<>();
//        insertMap.put("id_s",requestContext.get("id"));
//        insertMap.put("card_no_s",requestContext.get("cardNo"));
//        insertMap.put("card_type_s",requestContext.get("cardType"));
//        insertMap.put("acct_no_s",requestContext.get("acctNo"));
//        insertMap.put("cust_no_s",requestContext.get("custNo"));
//        insertMap.put("cust_name_s",requestContext.get("custName"));
//        insertMap.put("txn_month_s",requestContext.get("txnMonth"));
//        insertMap.put("txn_seqno_s",requestContext.get("txnSeqno"));
//        insertMap.put("txn_date_s",requestContext.get("txnDate"));
//        insertMap.put("txn_time_s",requestContext.get("txnTime"));
//        insertMap.put("txn_re_type_s",requestContext.get("txnReType"));
//        insertMap.put("txn_type_s",requestContext.get("txnType"));
//        insertMap.put("txn_amt_d",requestContext.get("txnAmt"));
//        insertMap.put("avl_bal_d",requestContext.get("avlBal"));
//        insertMap.put("txn_chnl_s",requestContext.get("txnChnl"));
//        insertMap.put("txn_comment_s",requestContext.get("txnComment"));
//        insertMap.put("txn_remark_s",requestContext.get("txnRemark"));
//        return ElasticSearchUtil.singleInsert("income_expense_analysis",insertMap);
//    }
//
//    public Map<String, Object> testEsUpdate(Map<String, Object> requestContext){
//        Map<String, Object> updateMap = new HashMap<>();
//        updateMap.put("id_s",requestContext.get("id"));
//        updateMap.put("txn_remark_s",requestContext.get("txnRemark"));
//        return ElasticSearchUtil.updateEsRecord("income_expense_analysis",updateMap);
//    }
//
//    public Map<String, Object> testEsDelete(String id){
//        return ElasticSearchUtil.deleteEsRecord("income_expense_analysis",id);
//    }
//
//    public Map<String, Object> testEsBulk(){
//        return ElasticSearchUtil.batchInsert("income_expense_analysis",getBulkData());
//    }
//
//    private List<Map<String, Object>> getBulkData(){
//        List<Map<String, Object>> bulkData = new ArrayList<>();
//        Map<String,Object> data1 = new HashMap<>();
//        data1.put("id_s","2022-03-31318648");
//        data1.put("card_no_s","6231232131123");
//        data1.put("card_type_s","01");
//        data1.put("acct_no_s","101010101010");
//        data1.put("cust_no_s","111666");
//        data1.put("cust_name_s","顾娟");
//        data1.put("txn_month_s","2022-03");
//        data1.put("txn_seqno_s","318648");
//        data1.put("txn_date_s","2022-03-31");
//        data1.put("txn_time_s","13:14:26");
//        data1.put("txn_re_type_s","R");
//        data1.put("txn_type_s","收入");
//        data1.put("txn_amt_d",210);
//        data1.put("avl_bal_d",54206.29);
//        data1.put("txn_chnl_s","73");
//        data1.put("txn_comment_s","03月30日商户入账");
//        data1.put("txn_remark_s","03月30日商户入账");
//        Map<String,Object> data2 = new HashMap<>();
//        data2.put("id_s","2022-03-31584152");
//        data2.put("card_no_s","6231232131123");
//        data2.put("card_type_s","01");
//        data2.put("acct_no_s","101010101010");
//        data2.put("cust_no_s","111666");
//        data2.put("cust_name_s","顾娟");
//        data2.put("txn_month_s","2022-03");
//        data2.put("txn_seqno_s","584152");
//        data2.put("txn_date_s","2022-03-31");
//        data2.put("txn_time_s","19:23:57");
//        data2.put("txn_re_type_s","E");
//        data2.put("txn_type_s","支出");
//        data2.put("txn_amt_d",55);
//        data2.put("avl_bal_d",54013.29);
//        data2.put("txn_chnl_s","NA");
//        data2.put("txn_comment_s","财付通支付");
//        data2.put("txn_remark_s","财付通支付");
//        Map<String,Object> data3 = new HashMap<>();
//        data3.put("id_s","2022-03-31585274");
//        data3.put("card_no_s","6231232131123");
//        data3.put("card_type_s","01");
//        data3.put("acct_no_s","101010101010");
//        data3.put("cust_no_s","111666");
//        data3.put("cust_name_s","顾娟");
//        data3.put("txn_month_s","2022-03");
//        data3.put("txn_seqno_s","585274");
//        data3.put("txn_date_s","2022-03-31");
//        data3.put("txn_time_s","19:26:43");
//        data3.put("txn_re_type_s","E");
//        data3.put("txn_type_s","支出");
//        data3.put("txn_amt_d",12);
//        data3.put("avl_bal_d",54001.29);
//        data3.put("txn_chnl_s","NB");
//        data3.put("txn_comment_s","财付通B支付");
//        data3.put("txn_remark_s","财付通B支付");
//        Map<String,Object> data4 = new HashMap<>();
//        data4.put("id_s","2022-03-31489947");
//        data4.put("card_no_s","6231232131123");
//        data4.put("card_type_s","01");
//        data4.put("acct_no_s","101010101010");
//        data4.put("cust_no_s","111666");
//        data4.put("cust_name_s","顾娟");
//        data4.put("txn_month_s","2022-03");
//        data4.put("txn_seqno_s","489947");
//        data4.put("txn_date_s","2022-03-31");
//        data4.put("txn_time_s","13:14:26");
//        data4.put("txn_re_type_s","E");
//        data4.put("txn_type_s","支出");
//        data4.put("txn_amt_d",138);
//        data4.put("avl_bal_d",54068.29);
//        data4.put("txn_chnl_s","NA");
//        data4.put("txn_comment_s",null);
//        data4.put("txn_remark_s","财付通支付");
//        Map<String,Object> data5 = new HashMap<>();
//        data5.put("id_s","2022-04-28489953");
//        data5.put("card_no_s","6231232131123");
//        data5.put("card_type_s","01");
//        data5.put("acct_no_s","101010101010");
//        data5.put("cust_no_s","111666");
//        data5.put("cust_name_s","顾娟");
//        data5.put("txn_month_s","2022-04");
//        data5.put("txn_seqno_s","489953");
//        data5.put("txn_date_s","2022-04-28");
//        data5.put("txn_time_s","10:24:26");
//        data5.put("txn_re_type_s","R");
//        data5.put("txn_type_s","收入");
//        data5.put("txn_amt_d",520);
//        data5.put("avl_bal_d",61368.29);
//        data5.put("txn_chnl_s","73");
//        data5.put("txn_comment_s",null);
//        data5.put("txn_remark_s","04月28日商户入账");
//        Map<String,Object> data6 = new HashMap<>();
//        data6.put("id_s","2020-05-20789324");
//        data6.put("card_no_s","6231232131123");
//        data6.put("card_type_s","01");
//        data6.put("acct_no_s","101010101010");
//        data6.put("cust_no_s","111666");
//        data6.put("cust_name_s","顾娟");
//        data6.put("txn_month_s","2022-05");
//        data6.put("txn_seqno_s","789324");
//        data6.put("txn_date_s","2022-05-20");
//        data6.put("txn_time_s","10:24:50");
//        data6.put("txn_re_type_s","E");
//        data6.put("txn_type_s","支出");
//        data6.put("txn_amt_d",520);
//        data6.put("avl_bal_d",60848.29);
//        data6.put("txn_chnl_s","23");
//        data6.put("txn_comment_s","");
//        data6.put("txn_remark_s","转出到微信");
//        Map<String,Object> data7 = new HashMap<>();
//        data7.put("id_s","2020-05-20523129");
//        data7.put("card_no_s","6201232133423");
//        data7.put("card_type_s","01");
//        data7.put("acct_no_s","131010189010");
//        data7.put("cust_no_s","112666");
//        data7.put("cust_name_s","王刚");
//        data7.put("txn_month_s","2022-05");
//        data7.put("txn_seqno_s","523129");
//        data7.put("txn_date_s","2022-05-17");
//        data7.put("txn_time_s","18:24:50");
//        data7.put("txn_re_type_s","E");
//        data7.put("txn_type_s","支出");
//        data7.put("txn_amt_d",5000);
//        data7.put("avl_bal_d",6848.29);
//        data7.put("txn_chnl_s","23");
//        data7.put("txn_comment_s","");
//        data7.put("txn_remark_s","购买大件");
//        bulkData.add(data1);
//        bulkData.add(data2);
//        bulkData.add(data3);
//        bulkData.add(data4);
//        bulkData.add(data5);
//        bulkData.add(data6);
//        bulkData.add(data7);
//        return bulkData;
//    }
//
//    public Map<String, Object> testEsAggs(Map<String, Object> requestContext) throws Exception {
//        Map<String,Object> equalCondition = new HashMap<>();
//        equalCondition.put("cust_no_s",requestContext.get("custNo"));
//        List<String> txnMonths = Arrays.asList(((String) requestContext.get("txnMonth")).split(","));
//        equalCondition.put("txn_month_s",txnMonths);
//        List<String> aggsFields = Arrays.asList("txn_amt_d");
//        return ElasticSearchUtil.aggregationQuery("income_expense_analysis",equalCondition,null,aggsFields);
//    }
//
//    public Map<String, Object> testEsGroupAggs(Map<String, Object> requestContext) throws Exception {
//        Map<String,Object> equalCondition = new HashMap<>();
//        equalCondition.put("cust_no_s",requestContext.get("custNo"));
//        String groupFileds = "txn_month_s,txn_re_type_s,txn_chnl_s";
//        List<String> txnMonths = Arrays.asList(groupFileds.split(","));
//        return ElasticSearchUtil.groupAggregationQuery("income_expense_analysis",equalCondition,null,"txn_amt_d",txnMonths);
//    }
//
//    /**
//     * 批量同步患者数据
//     * @param projectId
//     * @param testeeId
//     * @return
//     */
//    public Map<String, Object> batchSaveProjectTesteeVariableResultFromMysqlDataSource(String projectId, String testeeId) {
//        Map<String, Object> research_testee_analysis_test = new HashMap<>();
//        List<ProjectTesteeVariableResultVo> projectTesteeFormAndTableResultList = projectTesteeResultService.getProjectTesteeVariableResultFromMysqlDataSource(projectId, testeeId);
//            ProjectTesteeVariableESResultVo projectTesteeVariableESResultVo = new ProjectTesteeVariableESResultVo();
//            // 按照访视表单id进行分组提取数据
//            LinkedHashMap<String, List<ProjectTesteeVariableResultVo>> projectTesteeFormMap = projectTesteeFormAndTableResultList.stream().collect(Collectors.groupingBy(data ->data.getForm_id(), LinkedHashMap::new, Collectors.toList()));
//            projectTesteeFormMap.forEach((formId, formVariableResultList)->{
//                // 表单变量集合
//                List<ProjectTesteeVariableESResultVo.FormVariableResultInfo> variable_info_list = new ArrayList<>();
//                // 查询普通表单提交记录集合
//                List<ProjectTesteeVariableResultVo> projectTesteeFormVariableResult = formVariableResultList.stream().filter(formVariable -> formVariable.getForm_id().equals(formId)).collect(Collectors.toList());
//                for (ProjectTesteeVariableResultVo projectTesteeVariableResultVo : projectTesteeFormVariableResult) {
//                    // 设置基本信息
//                    BeanUtils.copyProperties(projectTesteeVariableResultVo, projectTesteeVariableESResultVo);
//
//                    //先组装普通表单数据
//                    if(projectTesteeVariableResultVo.getForm_id().equals(formId)){
//                        ProjectTesteeVariableESResultVo.FormVariableResultInfo variable_info = new ProjectTesteeVariableESResultVo.FormVariableResultInfo();
//                        BeanUtils.copyProperties(projectTesteeVariableResultVo, variable_info);
//
//                        // 查询表格提交记录集合
//                        List<ProjectTesteeVariableResultVo> projectTesteeTableVariableResult = formVariableResultList.stream().filter(formVariable -> StringUtils.isNotEmpty(formVariable.getTable_id()) && formVariable.getForm_id().equals(formId.split(",")[0])).collect(Collectors.toList());
//
//                        /*LinkedHashMap<String, List<ProjectTesteeVariableResultVo>> projectTesteeTableMap = projectTesteeTableVariableResult.stream().collect(Collectors.groupingBy(data ->data.getTable_id(), LinkedHashMap::new, Collectors.toList()));
//                        projectTesteeTableMap.forEach((tableId, tableVariableResultList)->{
//                            List<ProjectTesteeVariableESResultVo.TableVariavleResultInfo> table_info_list = new ArrayList<>();
//                            //先组装普通表单数据
//                            List<ProjectTesteeVariableResultVo> projectTesteeTableVariableResultList = tableVariableResultList.stream().collect(Collectors.toList());
//                            projectTesteeTableVariableResultList.forEach(tableRowData -> {
//                                if(tableRowData.getTable_id().equals(tableId) && tableRowData.getTable_id().equals(projectTesteeVariableResultVo)){
//                                    ProjectTesteeVariableESResultVo.TableVariavleResultInfo tableInfo = new ProjectTesteeVariableESResultVo.TableVariavleResultInfo();
//                                    BeanUtils.copyProperties(tableRowData, tableInfo);
//                                    table_info_list.add(tableInfo);
//                                }
//                            });
//                            if(tableId.equals(variable_info.getVariable_id())){
//                                variable_info.setTable_info(table_info_list);
//                            }
//                        });*/
//                        variable_info_list.add(variable_info);
//                    }
//                }
//
//                projectTesteeVariableESResultVo.setVariable_info(variable_info_list);
//
//                // insert into es data
//                 Map<String, Object> testeeVariableResultMap = JsonUtils.jsonToMap(JSON.toJSONString(projectTesteeVariableESResultVo));
//                 log.info("testeeVariableResultMap:{}", JSON.toJSONString(projectTesteeVariableESResultVo));
//                ElasticSearchUtil.singleInsert(DefineConstant.SEARCH_REQUEST_INDEX, testeeVariableResultMap);
//            });
//
//
//        return research_testee_analysis_test;
//    }
//
//
//
//
//    public Map<String, Object> batchSaveProjectTesteeVariableResultToESFromMysqlDataSource(String projectId, String testeeId) {
//
//        List<ProjectTesteeVariableResultVo> projectTesteeFormAndTableResultList = projectTesteeResultService.getProjectTesteeVariableResultFromMysqlDataSource(projectId, testeeId);
//        ProjectTesteeVariableESResultVo projectTesteeVariableESResultVo = new ProjectTesteeVariableESResultVo();
//        // 按照访视表单id进行分组提取数据
//        Map<String, Object> research_testee_analysis_test = new HashMap<>();
//        LinkedHashMap<String, List<ProjectTesteeVariableResultVo>> projectTesteeFormMap = projectTesteeFormAndTableResultList.stream().collect(Collectors.groupingBy(data ->data.getForm_id(), LinkedHashMap::new, Collectors.toList()));
//        projectTesteeFormMap.forEach((formId, formVariableResultList)->{
//            // 表单变量集合
//            // 查询普通表单提交记录集合
//            List<ProjectTesteeVariableResultVo> projectTesteeFormVariableResult = formVariableResultList.stream().filter(formVariable -> formVariable.getForm_id().equals(formId)).collect(Collectors.toList());
//            for (ProjectTesteeVariableResultVo projectTesteeVariableResultVo : projectTesteeFormVariableResult) {
//                // 设置基本信息
//                BeanUtils.copyProperties(projectTesteeVariableResultVo, projectTesteeVariableESResultVo);
//                research_testee_analysis_test.put("testee_id", projectTesteeVariableResultVo.getTestee_id());
//                research_testee_analysis_test.put("testee_code", projectTesteeVariableResultVo.getTestee_code());
//                research_testee_analysis_test.put("real_name", projectTesteeVariableResultVo.getReal_name());
//
//                if(StringUtils.isEmpty(projectTesteeVariableResultVo.getField_name())){
//                    continue;
//                }
//                research_testee_analysis_test.put(projectTesteeVariableResultVo.getVariable_id(), projectTesteeVariableResultVo.getVariable_id());
//                research_testee_analysis_test.put(projectTesteeVariableResultVo.getField_name(), projectTesteeVariableResultVo.getField_value());
//
//            }
//            Map<String, Object> testeeVariableResultMap = JsonUtils.jsonToMap(JSON.toJSONString(research_testee_analysis_test));
//            log.info("testeeVariableResultMap:{}", JSON.toJSONString(testeeVariableResultMap));
//            ElasticSearchUtil.singleInsert(DefineConstant.SEARCH_REQUEST_INDEX, testeeVariableResultMap);
//
//
//
//        });
//        return research_testee_analysis_test;
//    }
//
//
//
//    public CommonPage<Map<String, Object>> getExportProjectTesteeListForPage(String projectId, String orgIds, String conditionValueInfo, String sortField, String sortType, Integer pageNum, Integer pageSize) throws Exception {
//        CommonPage<Map<String, Object>> commonPage = new CommonPage<>();
//        List<Map<String, Object>> dataList = new ArrayList<>();
//        Map<String, Object> project_testee_variable_result = ElasticSearchUtil.queryProjectTesteeForElasticSearchTemplate(projectId, DefineConstant.SEARCH_REQUEST_INDEX, conditionValueInfo, sortField, sortType, pageNum, pageSize);
//        // Map<String, Object>  project_testee_variable_result = ElasticSearchUtil.queryProjectTesteeForElasticSearchTemplate1(pageNum, pageSize);
//        dataList.add(project_testee_variable_result);
//        commonPage.setList(dataList);
//        return commonPage;
//    }
//
//    public Map<String, Object> batchSaveProjectTesteeVariableResultToMongoDBFromMysqlDataSource(String projectId, String testeeId) {
//        Map<String, Object> research_testee_analysis_test = new HashMap<>();
//        List<ProjectTesteeVariableResultVo> projectTesteeFormAndTableResultList = projectTesteeResultService.getProjectTesteeVariableResultFromMysqlDataSource(projectId, testeeId);
//        ProjectTesteeVariableESResultVo projectTesteeVariableESResultVo = new ProjectTesteeVariableESResultVo();
//        // 按照访视表单id进行分组提取数据
//        LinkedHashMap<String, List<ProjectTesteeVariableResultVo>> projectTesteeFormMap = projectTesteeFormAndTableResultList.stream().collect(Collectors.groupingBy(data ->data.getForm_id(), LinkedHashMap::new, Collectors.toList()));
//        projectTesteeFormMap.forEach((formId, formVariableResultList)->{
//            // 表单变量集合
//            List<ProjectTesteeVariableESResultVo.FormVariableResultInfo> variable_info_list = new ArrayList<>();
//            // 查询普通表单提交记录集合
//            List<ProjectTesteeVariableResultVo> projectTesteeFormVariableResult = formVariableResultList.stream().filter(formVariable -> formVariable.getForm_id().equals(formId)).collect(Collectors.toList());
//            for (ProjectTesteeVariableResultVo projectTesteeVariableResultVo : projectTesteeFormVariableResult) {
//                // 设置基本信息
//                BeanUtils.copyProperties(projectTesteeVariableResultVo, projectTesteeVariableESResultVo);
//                //先组装普通表单数据
//                if(projectTesteeVariableResultVo.getForm_id().equals(formId)){
//                    ProjectTesteeVariableESResultVo.FormVariableResultInfo variable_info = new ProjectTesteeVariableESResultVo.FormVariableResultInfo();
//                    BeanUtils.copyProperties(projectTesteeVariableResultVo, variable_info);
//                    // 查询表格提交记录集合
//                    //List<ProjectTesteeVariableResultVo> projectTesteeTableVariableResult = formVariableResultList.stream().filter(formVariable -> StringUtils.isNotEmpty(formVariable.getTable_id()) && formVariable.getForm_id().equals(formId.split(",")[0])).collect(Collectors.toList());
//                    variable_info_list.add(variable_info);
//                }
//            }
//
//            projectTesteeVariableESResultVo.setVariable_info(variable_info_list);
//            ProjectTesteeVariableValueVo projectTesteeVariableValueVo = new ProjectTesteeVariableValueVo();
//            BeanUtils.copyProperties(projectTesteeVariableESResultVo, projectTesteeVariableValueVo);
//             //MongoTemplateUtil.saveOne("ProjectTesteeVariableValueVo", ProjectTesteeVariableValueVo.class);
//            // mongoTemplate.createCollection("ProjectTesteeVariableValueVo");
//
//            //mongoTemplate.save(projectTesteeVariableValueVo);
//
//            // insert into es data
//            // Couldn't find PersistentEntity for type class java.lang.Class
//        });
//        return research_testee_analysis_test;
//    }
//
//    public Map<String, Object> getMongoDBRecord(String projectId) {
//        //
//        JSONObject queryResultInfo = null;
//        //封装对象列表查询条件
//        List<AggregationOperation> commonOperations = new ArrayList<>();
//        //1. 指定查询主文档
//        MatchOperation match = Aggregation.match(Criteria.where("project_id").is(projectId));
//        commonOperations.add(match);
//        //2. 指定投影，返回哪些字段
//        ProjectionOperation project = Aggregation.project("variable_info");
//        commonOperations.add(project);
//        //3. 拆分内嵌文档
//        UnwindOperation unwind = Aggregation.unwind("variable_info");
//        commonOperations.add(unwind);
//
//        /*propertyValues1.put("variable_info.variable_id", "806166728089407488");
//        propertyValues1.put("variable_info.field_value", "梁启健");
//
//        propertyValues2.put("variable_info.variable_id", "806367294417670144");
//        propertyValues2.put("variable_info.field_value", "21/03");*/
//
//
//        //4. 指定查询子文档
//        MatchOperation match1 = Aggregation.match(Criteria.where("variable_info.variable_id").is("806166728089407488"));
//        commonOperations.add(match1);
//
//        MatchOperation match2 = Aggregation.match(Criteria.where("variable_info.field_value").is("梁启健"));
//        commonOperations.add(match2);
//
//
////        MatchOperation match3 = Aggregation.match(Criteria.where("variable_info.variable_id").is("806367294417670144"));
////        commonOperations.add(match3);
////
////        MatchOperation match4 = Aggregation.match(Criteria.where("variable_info.field_value").is("21/03"));
////        commonOperations.add(match4);
//
//
//
//        //创建管道查询对象
//        //Aggregation aggregation = Aggregation.newAggregation(commonOperations);
//        //AggregationResults<JSONObject> reminds = mongoTemplate.aggregate(aggregation, "ProjectTesteeVariableValueVo", JSONObject.class);
////        List<JSONObject> mappedResults = reminds.getMappedResults();
////        if (mappedResults != null && mappedResults.size() > 0) {
////            queryResultInfo = JSONObject.parseObject(mappedResults.get(0).getJSONObject("variable_info").toJSONString(), JSONObject.class);
////            log.info("queryResultInfo:{}", JSON.toJSONString(queryResultInfo));
////        }
//        return queryResultInfo;
//
//    }
//
//    public Map<String, Object> getMongoDBRecord1(String projectId) {
//
//        // 类似结构{"_id":"12345","listKey": [{"property1": "XXX1","property2": "XXX2"},{"property1": "YYY1","property2": "YYY2"}]}
//        Criteria findModelCriteria = Criteria.where("project_id").is(projectId);
//
//        Criteria criteria1 = Criteria.where("variable_info").elemMatch(Criteria.where("variable_id").is("806166728089407488").andOperator(Criteria.where("field_value").is("梁启健")));
//        Criteria criteria2 = Criteria.where("variable_info").elemMatch(Criteria.where("variable_id").is("806367294417670144").andOperator(Criteria.where("field_value").is("21/03")));
//
//
//        List<Criteria> criteriaList = new ArrayList<>();
//        criteriaList.add(findModelCriteria);
//        criteriaList.add(criteria1);
//        criteriaList.add(criteria2);
//
//        //Criteria criteria = findModelCriteria.andOperator(criteria1).andOperator(criteria2);
//
//        //Criteria criteria = criteria1.andOperator(criteria2);
//        //criteria.andOperator(Criteria.where("project_id").is(projectId));
//        //使用elemMatch 过滤查询出来的元素
//
//
//        GroupBy groupBy = new GroupBy("testee_id")
//                .initialDocument("{ count: 0 }")
//                .reduceFunction("function (doc,pre){pre.count +=1 ;}");
//
////        BasicQuery basicQuery = new BasicQuery(findModelCriteria.getCriteriaObject());
////        ProjectTesteeVariableValueVo document  = mongoTemplate.findOne(basicQuery, ProjectTesteeVariableValueVo.class);
////        Query query = new Query();
////        //query.addCriteria(findModelCriteria).criteria1, criteria2);
////        List<ProjectTesteeVariableValueVo> dataList = mongoTemplate.find(query, ProjectTesteeVariableValueVo.class,"ProjectTesteeVariableValueVo");
//
//        return null;
//
//    }
//
//    public void saveTesteeVariableRecord() throws IOException {
//        projectTesteeResultService.saveTesteeVariableRecord();
//    }
//
//}
