package com.haoys.user.domain.vo.project;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ProjectApplyOrgVo {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "申请人或者负责人id")
    private String userId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "所属中心id")
    private Long orgId;

    @ApiModelProperty(value = "所属中心名称")
    private String orgName;
}
