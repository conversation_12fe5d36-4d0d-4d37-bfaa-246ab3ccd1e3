package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.DateUtil;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.enums.ProjectFlowReturnEnums;
import com.haoys.user.domain.param.testee.ProjectTesteeFillInfoParam;
import com.haoys.user.domain.param.testee.ProjectTesteeStatistVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeFillFlowVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeFillVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeImageInfoVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeWrapperVo;
import com.haoys.user.mapper.ProjectTesteeProcessMapper;
import com.haoys.user.model.*;
import com.haoys.user.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
public class ProjectTesteeInfoFillServiceImpl extends BaseService implements ProjectTesteeInfoFillService {

    @Autowired
    private ProjectTesteeInfoService projectTesteeInfoService;
    @Autowired
    private ProjectVisitConfigService projectVisitConfigService;
    @Autowired
    private FlowFormSetService flowFormSetService;

    @Autowired
    private ProjectTesteeProcessMapper projectTesteeProcessMapper;

    @Autowired
    private ProjectVisitUserService projectVisitUserService;

    @Override
    public CommonResult<ProjectTesteeImageInfoVo> getFillInformationList(ProjectTesteeFillInfoParam param) {
        ProjectTesteeImageInfoVo imageInfoVo = new ProjectTesteeImageInfoVo();
        List<ProjectTesteeFillVo> data = new ArrayList<>();
        // 获取参与者
        Page<Object> page = PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<ProjectTesteeWrapperVo> list = projectTesteeInfoService.getProjectTesteeBaseDataViewList(param.getProjectId().toString(), param.getTesteeCode(),
                param.getProjectOrgId(), param.getStatus(),param.getResearchStatus(),
                "create_time", "desc");
        List<ProjectVisitConfig> flowLost = projectVisitConfigService.listByProjectId(param.getProjectId());
        // 如果参与者不为空获取流程信息
        if (CollectionUtil.isNotEmpty(list)) {
            if (CollectionUtil.isNotEmpty(flowLost)) {
                ProjectVisitConfig config = flowLost.get(0);
                // 计划id
                Long planId = config.getPlanId();
                // 根据计划id获取所有绑定的表单。
                FlowFormSetExample example = new FlowFormSetExample();
                FlowFormSetExample.Criteria criteria = example.createCriteria();
                criteria.andProjectIdEqualTo(param.getProjectId());
                criteria.andPlanIdEqualTo(planId);
                List<FlowFormSet> flowFormSets = flowFormSetService.selectByExample(example);
                if (CollectionUtil.isNotEmpty(flowFormSets)) {

                    // 根据计划id获取所有绑定的表单。根据流程id进行分组
                    Map<Long, List<FlowFormSet>> flowMap = flowFormSets.stream().collect(Collectors.groupingBy(FlowFormSet::getVisitId));
                    // 循环每一位参与者
                    for (ProjectTesteeWrapperVo testee : list) {
                        // 设置参与者信息
                        ProjectTesteeFillVo vo = new ProjectTesteeFillVo();
                        vo.setTesteeId(testee.getId());
                        vo.setTesteeName(testee.getRealName());
                        vo.setTesteeCode(testee.getCode());
                        vo.setStatus(testee.getStatus());
                        vo.setResearchStatus(testee.getResearchStatus());
                        // 表单完成信息集合
                        List<ProjectTesteeProcess> processes = getProcesses(param.getProjectId(),param.getProjectOrgId(), planId, testee.getId());
                        Map<Long, List<ProjectTesteeProcess>> processMap = new HashMap<>();
                        if (CollectionUtil.isNotEmpty(processes)) {
                            // 表单完成信息集合，根据流程id进行分组
                            processMap = processes.stream().collect(Collectors.groupingBy(ProjectTesteeProcess::getVisitId));
                        }
                        List<ProjectTesteeFillFlowVo> listVo = new ArrayList<>();
                        for (ProjectVisitConfig conf : flowLost) {
                            ProjectTesteeFillFlowVo flowVo = new ProjectTesteeFillFlowVo();
                            flowVo.setFlowId(conf.getId().toString());
                            flowVo.setFlowName(conf.getVisitName());
                            List<FlowFormSet> set = flowMap.get(conf.getId());
                            List<ProjectTesteeProcess> processList = processMap.get(conf.getId());
                            int fillFormNum = CollectionUtil.isNotEmpty(processList) ? processList.size() : 0;
                            int allFormNum = CollectionUtil.isNotEmpty(set) ? set.size() : 0;
                            if (allFormNum!=0 && fillFormNum!=0){
                                double b= new BigDecimal(fillFormNum/ (double) allFormNum).doubleValue();
                                flowVo.setFillProportion(Integer.valueOf(String.format("%.0f",b*100)));
                            }
                            flowVo.setAllFormNum(allFormNum);
                            flowVo.setFillFormNum(fillFormNum);
                            flowVo.setSignFormNum(0);
                            flowVo.setChallengeFormNum(0);
                            flowVo.setNotCloseFormNum(0);
                            listVo.add(flowVo);
                        }
                        vo.setList(listVo);
                        data.add(vo);
                    }
                }
            }else {
                return CommonResult.failed(ProjectFlowReturnEnums.E30003);
            }
        }else {
            return CommonResult.failed(ProjectFlowReturnEnums.E30003);
        }
        // 设置数据
        CommonPage commonPage = commonPageListWrapper(param.getPageNum(), param.getPageSize(), page, data);
        imageInfoVo.setData(commonPage);
        imageInfoVo.setTitles(flowLost);
        // 设置表头
        return CommonResult.success(imageInfoVo);
    }

    @Override
    public CommonResult<ProjectTesteeStatistVo> testeeStatist(Long projectId, Long orgId) {
        //  统计筛选中，筛选未通过，研究中，研究结束。
        ProjectTesteeStatistVo vo = projectVisitUserService.testeeStatist(projectId,orgId);
        if (vo!=null){
            vo.setTotal(vo.getScreenNum()+vo.getScreenNotNum()+vo.getDiscussCloseNum()+vo.getDiscussNum()+vo.getSuspendNum());
            // 今日的数量
            Integer todayNum = projectVisitUserService.testeeStatistByDate(projectId,orgId, DateUtil.getCurrentDate());
            vo.setToDayNum(todayNum);
            // 昨日的数量
            Integer yeaDayNum = projectVisitUserService.testeeStatistByDate(projectId,orgId, DateUtil.getYesterday());
            vo.setYeDayNum(yeaDayNum);
            // 计算入选率：入选率=( 研究中+研究结束+中止脱落)/( 筛选未通过+研究中+研究结束+中止脱落)
            int d0 = vo.getScreenNotNum() + vo.getDiscussNum() + vo.getDiscussCloseNum() + vo.getSuspendNum();
            int d1 = vo.getDiscussNum() + vo.getDiscussCloseNum() + vo.getSuspendNum();
            double selection = 0;
            if (d0!=0 && d1!=0){
                selection = (d1) * 1.0 / (d0)*100;
            }
            // 对入选率进行保留一位小数
            BigDecimal bd = new BigDecimal(selection);
            BigDecimal selectionRate = bd.setScale(0, RoundingMode.HALF_DOWN);
            vo.setSelectionRate(selectionRate+"");
        }else {
            vo = new ProjectTesteeStatistVo();
        }

        return CommonResult.success(vo);
    }

    /**
     * @param projectId 项目id
     * @param planId    计划id
     * @param testeeId  参与者id
     * @param orgId     研究中心id
     * @return 参与者填写表单的进度信息集合
     */
    private List<ProjectTesteeProcess> getProcesses(Long projectId, String orgId, Long planId, Long testeeId) {
        ProjectTesteeProcessExample px = new ProjectTesteeProcessExample();
        ProjectTesteeProcessExample.Criteria pxc = px.createCriteria();
        pxc.andProjectIdEqualTo(projectId);
        pxc.andPlanIdEqualTo(planId);
        pxc.andTesteeIdEqualTo(testeeId);
        if (StringUtils.isNotBlank(orgId)){
            pxc.andOwnerOrgIdEqualTo(orgId);
        }
        pxc.andComplateStatusNotEqualTo(BusinessConfig.FORM_FILL_NOT);
        return projectTesteeProcessMapper.selectByExample(px);
    }
}

