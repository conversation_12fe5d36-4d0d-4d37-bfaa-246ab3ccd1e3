package com.haoys.user.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProjectTesteeVariableSyncExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectTesteeVariableSyncExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andBaseVariableIdIsNull() {
            addCriterion("base_variable_id is null");
            return (Criteria) this;
        }

        public Criteria andBaseVariableIdIsNotNull() {
            addCriterion("base_variable_id is not null");
            return (Criteria) this;
        }

        public Criteria andBaseVariableIdEqualTo(Long value) {
            addCriterion("base_variable_id =", value, "baseVariableId");
            return (Criteria) this;
        }

        public Criteria andBaseVariableIdNotEqualTo(Long value) {
            addCriterion("base_variable_id <>", value, "baseVariableId");
            return (Criteria) this;
        }

        public Criteria andBaseVariableIdGreaterThan(Long value) {
            addCriterion("base_variable_id >", value, "baseVariableId");
            return (Criteria) this;
        }

        public Criteria andBaseVariableIdGreaterThanOrEqualTo(Long value) {
            addCriterion("base_variable_id >=", value, "baseVariableId");
            return (Criteria) this;
        }

        public Criteria andBaseVariableIdLessThan(Long value) {
            addCriterion("base_variable_id <", value, "baseVariableId");
            return (Criteria) this;
        }

        public Criteria andBaseVariableIdLessThanOrEqualTo(Long value) {
            addCriterion("base_variable_id <=", value, "baseVariableId");
            return (Criteria) this;
        }

        public Criteria andBaseVariableIdIn(List<Long> values) {
            addCriterion("base_variable_id in", values, "baseVariableId");
            return (Criteria) this;
        }

        public Criteria andBaseVariableIdNotIn(List<Long> values) {
            addCriterion("base_variable_id not in", values, "baseVariableId");
            return (Criteria) this;
        }

        public Criteria andBaseVariableIdBetween(Long value1, Long value2) {
            addCriterion("base_variable_id between", value1, value2, "baseVariableId");
            return (Criteria) this;
        }

        public Criteria andBaseVariableIdNotBetween(Long value1, Long value2) {
            addCriterion("base_variable_id not between", value1, value2, "baseVariableId");
            return (Criteria) this;
        }

        public Criteria andBaseFieldLabelIsNull() {
            addCriterion("base_field_label is null");
            return (Criteria) this;
        }

        public Criteria andBaseFieldLabelIsNotNull() {
            addCriterion("base_field_label is not null");
            return (Criteria) this;
        }

        public Criteria andBaseFieldLabelEqualTo(String value) {
            addCriterion("base_field_label =", value, "baseFieldLabel");
            return (Criteria) this;
        }

        public Criteria andBaseFieldLabelNotEqualTo(String value) {
            addCriterion("base_field_label <>", value, "baseFieldLabel");
            return (Criteria) this;
        }

        public Criteria andBaseFieldLabelGreaterThan(String value) {
            addCriterion("base_field_label >", value, "baseFieldLabel");
            return (Criteria) this;
        }

        public Criteria andBaseFieldLabelGreaterThanOrEqualTo(String value) {
            addCriterion("base_field_label >=", value, "baseFieldLabel");
            return (Criteria) this;
        }

        public Criteria andBaseFieldLabelLessThan(String value) {
            addCriterion("base_field_label <", value, "baseFieldLabel");
            return (Criteria) this;
        }

        public Criteria andBaseFieldLabelLessThanOrEqualTo(String value) {
            addCriterion("base_field_label <=", value, "baseFieldLabel");
            return (Criteria) this;
        }

        public Criteria andBaseFieldLabelLike(String value) {
            addCriterion("base_field_label like", value, "baseFieldLabel");
            return (Criteria) this;
        }

        public Criteria andBaseFieldLabelNotLike(String value) {
            addCriterion("base_field_label not like", value, "baseFieldLabel");
            return (Criteria) this;
        }

        public Criteria andBaseFieldLabelIn(List<String> values) {
            addCriterion("base_field_label in", values, "baseFieldLabel");
            return (Criteria) this;
        }

        public Criteria andBaseFieldLabelNotIn(List<String> values) {
            addCriterion("base_field_label not in", values, "baseFieldLabel");
            return (Criteria) this;
        }

        public Criteria andBaseFieldLabelBetween(String value1, String value2) {
            addCriterion("base_field_label between", value1, value2, "baseFieldLabel");
            return (Criteria) this;
        }

        public Criteria andBaseFieldLabelNotBetween(String value1, String value2) {
            addCriterion("base_field_label not between", value1, value2, "baseFieldLabel");
            return (Criteria) this;
        }

        public Criteria andSourceVisitIdIsNull() {
            addCriterion("source_visit_id is null");
            return (Criteria) this;
        }

        public Criteria andSourceVisitIdIsNotNull() {
            addCriterion("source_visit_id is not null");
            return (Criteria) this;
        }

        public Criteria andSourceVisitIdEqualTo(Long value) {
            addCriterion("source_visit_id =", value, "sourceVisitId");
            return (Criteria) this;
        }

        public Criteria andSourceVisitIdNotEqualTo(Long value) {
            addCriterion("source_visit_id <>", value, "sourceVisitId");
            return (Criteria) this;
        }

        public Criteria andSourceVisitIdGreaterThan(Long value) {
            addCriterion("source_visit_id >", value, "sourceVisitId");
            return (Criteria) this;
        }

        public Criteria andSourceVisitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("source_visit_id >=", value, "sourceVisitId");
            return (Criteria) this;
        }

        public Criteria andSourceVisitIdLessThan(Long value) {
            addCriterion("source_visit_id <", value, "sourceVisitId");
            return (Criteria) this;
        }

        public Criteria andSourceVisitIdLessThanOrEqualTo(Long value) {
            addCriterion("source_visit_id <=", value, "sourceVisitId");
            return (Criteria) this;
        }

        public Criteria andSourceVisitIdIn(List<Long> values) {
            addCriterion("source_visit_id in", values, "sourceVisitId");
            return (Criteria) this;
        }

        public Criteria andSourceVisitIdNotIn(List<Long> values) {
            addCriterion("source_visit_id not in", values, "sourceVisitId");
            return (Criteria) this;
        }

        public Criteria andSourceVisitIdBetween(Long value1, Long value2) {
            addCriterion("source_visit_id between", value1, value2, "sourceVisitId");
            return (Criteria) this;
        }

        public Criteria andSourceVisitIdNotBetween(Long value1, Long value2) {
            addCriterion("source_visit_id not between", value1, value2, "sourceVisitId");
            return (Criteria) this;
        }

        public Criteria andSourceFormIdIsNull() {
            addCriterion("source_form_id is null");
            return (Criteria) this;
        }

        public Criteria andSourceFormIdIsNotNull() {
            addCriterion("source_form_id is not null");
            return (Criteria) this;
        }

        public Criteria andSourceFormIdEqualTo(Long value) {
            addCriterion("source_form_id =", value, "sourceFormId");
            return (Criteria) this;
        }

        public Criteria andSourceFormIdNotEqualTo(Long value) {
            addCriterion("source_form_id <>", value, "sourceFormId");
            return (Criteria) this;
        }

        public Criteria andSourceFormIdGreaterThan(Long value) {
            addCriterion("source_form_id >", value, "sourceFormId");
            return (Criteria) this;
        }

        public Criteria andSourceFormIdGreaterThanOrEqualTo(Long value) {
            addCriterion("source_form_id >=", value, "sourceFormId");
            return (Criteria) this;
        }

        public Criteria andSourceFormIdLessThan(Long value) {
            addCriterion("source_form_id <", value, "sourceFormId");
            return (Criteria) this;
        }

        public Criteria andSourceFormIdLessThanOrEqualTo(Long value) {
            addCriterion("source_form_id <=", value, "sourceFormId");
            return (Criteria) this;
        }

        public Criteria andSourceFormIdIn(List<Long> values) {
            addCriterion("source_form_id in", values, "sourceFormId");
            return (Criteria) this;
        }

        public Criteria andSourceFormIdNotIn(List<Long> values) {
            addCriterion("source_form_id not in", values, "sourceFormId");
            return (Criteria) this;
        }

        public Criteria andSourceFormIdBetween(Long value1, Long value2) {
            addCriterion("source_form_id between", value1, value2, "sourceFormId");
            return (Criteria) this;
        }

        public Criteria andSourceFormIdNotBetween(Long value1, Long value2) {
            addCriterion("source_form_id not between", value1, value2, "sourceFormId");
            return (Criteria) this;
        }

        public Criteria andSourceVariableIdIsNull() {
            addCriterion("source_variable_id is null");
            return (Criteria) this;
        }

        public Criteria andSourceVariableIdIsNotNull() {
            addCriterion("source_variable_id is not null");
            return (Criteria) this;
        }

        public Criteria andSourceVariableIdEqualTo(Long value) {
            addCriterion("source_variable_id =", value, "sourceVariableId");
            return (Criteria) this;
        }

        public Criteria andSourceVariableIdNotEqualTo(Long value) {
            addCriterion("source_variable_id <>", value, "sourceVariableId");
            return (Criteria) this;
        }

        public Criteria andSourceVariableIdGreaterThan(Long value) {
            addCriterion("source_variable_id >", value, "sourceVariableId");
            return (Criteria) this;
        }

        public Criteria andSourceVariableIdGreaterThanOrEqualTo(Long value) {
            addCriterion("source_variable_id >=", value, "sourceVariableId");
            return (Criteria) this;
        }

        public Criteria andSourceVariableIdLessThan(Long value) {
            addCriterion("source_variable_id <", value, "sourceVariableId");
            return (Criteria) this;
        }

        public Criteria andSourceVariableIdLessThanOrEqualTo(Long value) {
            addCriterion("source_variable_id <=", value, "sourceVariableId");
            return (Criteria) this;
        }

        public Criteria andSourceVariableIdIn(List<Long> values) {
            addCriterion("source_variable_id in", values, "sourceVariableId");
            return (Criteria) this;
        }

        public Criteria andSourceVariableIdNotIn(List<Long> values) {
            addCriterion("source_variable_id not in", values, "sourceVariableId");
            return (Criteria) this;
        }

        public Criteria andSourceVariableIdBetween(Long value1, Long value2) {
            addCriterion("source_variable_id between", value1, value2, "sourceVariableId");
            return (Criteria) this;
        }

        public Criteria andSourceVariableIdNotBetween(Long value1, Long value2) {
            addCriterion("source_variable_id not between", value1, value2, "sourceVariableId");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(String value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(String value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(String value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(String value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLike(String value) {
            addCriterion("create_user_id like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotLike(String value) {
            addCriterion("create_user_id not like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<String> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<String> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(String value1, String value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(String value1, String value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNull() {
            addCriterion("update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNotNull() {
            addCriterion("update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdEqualTo(String value) {
            addCriterion("update_user_id =", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotEqualTo(String value) {
            addCriterion("update_user_id <>", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThan(String value) {
            addCriterion("update_user_id >", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("update_user_id >=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThan(String value) {
            addCriterion("update_user_id <", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThanOrEqualTo(String value) {
            addCriterion("update_user_id <=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLike(String value) {
            addCriterion("update_user_id like", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotLike(String value) {
            addCriterion("update_user_id not like", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIn(List<String> values) {
            addCriterion("update_user_id in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotIn(List<String> values) {
            addCriterion("update_user_id not in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdBetween(String value1, String value2) {
            addCriterion("update_user_id between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotBetween(String value1, String value2) {
            addCriterion("update_user_id not between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNull() {
            addCriterion("platform_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNotNull() {
            addCriterion("platform_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdEqualTo(String value) {
            addCriterion("platform_id =", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotEqualTo(String value) {
            addCriterion("platform_id <>", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThan(String value) {
            addCriterion("platform_id >", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThanOrEqualTo(String value) {
            addCriterion("platform_id >=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThan(String value) {
            addCriterion("platform_id <", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThanOrEqualTo(String value) {
            addCriterion("platform_id <=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLike(String value) {
            addCriterion("platform_id like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotLike(String value) {
            addCriterion("platform_id not like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIn(List<String> values) {
            addCriterion("platform_id in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotIn(List<String> values) {
            addCriterion("platform_id not in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdBetween(String value1, String value2) {
            addCriterion("platform_id between", value1, value2, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotBetween(String value1, String value2) {
            addCriterion("platform_id not between", value1, value2, "platformId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}