package com.haoys.user.mapper;

import com.haoys.user.domain.vo.system.SystemExceptionLogVo;
import com.haoys.user.model.SystemExceptionLog;
import com.haoys.user.model.SystemExceptionLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SystemExceptionLogMapper {
    long countByExample(SystemExceptionLogExample example);

    int deleteByExample(SystemExceptionLogExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SystemExceptionLog record);

    int insertSelective(SystemExceptionLog record);

    List<SystemExceptionLog> selectByExample(SystemExceptionLogExample example);

    SystemExceptionLog selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SystemExceptionLog record, @Param("example") SystemExceptionLogExample example);

    int updateByExample(@Param("record") SystemExceptionLog record, @Param("example") SystemExceptionLogExample example);

    int updateByPrimaryKeySelective(SystemExceptionLog record);

    int updateByPrimaryKey(SystemExceptionLog record);
    
    List<SystemExceptionLogVo> getSystemExceptionLogList(String userName, Integer pageNum, Integer pageSize);
}