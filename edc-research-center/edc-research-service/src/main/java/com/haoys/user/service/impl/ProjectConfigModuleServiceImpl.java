package com.haoys.user.service.impl;

import com.haoys.user.model.ProjectConfigModule;
import com.haoys.user.service.ProjectConfigModuleService;
import com.haoys.user.mapper.ProjectConfigModuleMapper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.util.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CachePut;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import com.github.pagehelper.PageHelper;

/**
 * 项目配置分组表Service实现类
 * 提供完整的CRUD操作、批量操作、复杂查询、缓存管理等功能
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 * @date 2025-07-11 00:31:50
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class ProjectConfigModuleServiceImpl implements ProjectConfigModuleService {

    private static final String CACHE_NAME = "projectConfigModuleCache";
    private static final String CACHE_KEY = "'projectConfigModule:' + #id";
    private static final String CACHE_KEY_ALL = "'projectConfigModule:all'";

    @Autowired
    private ProjectConfigModuleMapper projectConfigModuleMapper;

    @Override
    @CacheEvict(value = CACHE_NAME, key = CACHE_KEY_ALL)
    public CommonResult<ProjectConfigModule> create(ProjectConfigModule record) {
        try {
            // 设置创建信息
            record.setCreateTime(new Date());
            String userId = SecurityUtils.getUserIdValue();
            record.setCreateUserId(userId);
            record.setTenantId(SecurityUtils.getSystemTenantId());
            record.setPlatformId(SecurityUtils.getSystemPlatformId());

            int result = projectConfigModuleMapper.insertSelective(record);
            if (result > 0) {
                log.info("创建ProjectConfigModule成功: {}", record.getModuleId());
                return CommonResult.success(record);
            } else {
                log.warn("创建ProjectConfigModule失败");
                return CommonResult.failed("创建失败");
            }
        } catch (Exception e) {
            log.error("创建ProjectConfigModule异常: {}", e.getMessage(), e);
            return CommonResult.failed("创建异常: " + e.getMessage());
        }
    }

    @Override
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public CommonResult<Void> deleteById(Long id) {
        try {
            int result = projectConfigModuleMapper.deleteByPrimaryKey(id);
            if (result > 0) {
                log.info("删除ProjectConfigModule成功: {}", id);
                return CommonResult.success(null);
            } else {
                log.warn("删除ProjectConfigModule失败，记录不存在: {}", id);
                return CommonResult.failed("记录不存在");
            }
        } catch (Exception e) {
            log.error("删除ProjectConfigModule异常: {}", e.getMessage(), e);
            return CommonResult.failed("删除异常: " + e.getMessage());
        }
    }

    @Override
    @CachePut(value = CACHE_NAME, key = CACHE_KEY)
    public CommonResult<ProjectConfigModule> update(ProjectConfigModule record) {
        try {
            // 设置更新信息

            int result = projectConfigModuleMapper.updateByPrimaryKeySelective(record);
            if (result > 0) {
                log.info("更新ProjectConfigModule成功: {}", record.getModuleId());
                return CommonResult.success(record);
            } else {
                log.warn("更新ProjectConfigModule失败，记录不存在: {}", record.getModuleId());
                return CommonResult.failed("记录不存在");
            }
        } catch (Exception e) {
            log.error("更新ProjectConfigModule异常: {}", e.getMessage(), e);
            return CommonResult.failed("更新异常: " + e.getMessage());
        }
    }

    @Override
    @Cacheable(value = CACHE_NAME, key = CACHE_KEY)
    public CommonResult<ProjectConfigModule> getById(Long id) {
        try {
            ProjectConfigModule record = projectConfigModuleMapper.selectByPrimaryKey(id);
            if (record != null) {
                return CommonResult.success(record);
            } else {
                return CommonResult.failed("记录不存在");
            }
        } catch (Exception e) {
            log.error("查询ProjectConfigModule异常: {}", e.getMessage(), e);
            return CommonResult.failed("查询异常: " + e.getMessage());
        }
    }

    @Override
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public CommonResult<List<ProjectConfigModule>> batchCreate(List<ProjectConfigModule> records) {
        try {
            if (records == null || records.isEmpty()) {
                return CommonResult.failed("批量创建数据不能为空");
            }

            String userId = SecurityUtils.getUserIdValue();
            String tenantId = SecurityUtils.getSystemTenantId();
            String platformId = SecurityUtils.getSystemPlatformId();
            Date now = new Date();

            // 设置公共字段
            for (ProjectConfigModule record : records) {
                record.setCreateTime(now);
                record.setCreateUserId(userId);
                record.setTenantId(tenantId);
                record.setPlatformId(platformId);
            }

            int result = projectConfigModuleMapper.batchInsert(records);
            if (result > 0) {
                log.info("批量创建ProjectConfigModule成功，共{}条记录", records.size());
                return CommonResult.success(records);
            } else {
                log.warn("批量创建ProjectConfigModule失败");
                return CommonResult.failed("批量创建失败");
            }
        } catch (Exception e) {
            log.error("批量创建ProjectConfigModule异常: {}", e.getMessage(), e);
            return CommonResult.failed("批量创建异常: " + e.getMessage());
        }
    }

    @Override
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public CommonResult<Void> batchDeleteByIds(List<Long> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return CommonResult.failed("批量删除ID列表不能为空");
            }

            int result = projectConfigModuleMapper.batchDeleteByIds(ids);
            if (result > 0) {
                log.info("批量删除ProjectConfigModule成功，共{}条记录", result);
                return CommonResult.success(null);
            } else {
                log.warn("批量删除ProjectConfigModule失败，没有找到匹配的记录");
                return CommonResult.failed("没有找到匹配的记录");
            }
        } catch (Exception e) {
            log.error("批量删除ProjectConfigModule异常: {}", e.getMessage(), e);
            return CommonResult.failed("批量删除异常: " + e.getMessage());
        }
    }

    @Override
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public CommonResult<List<ProjectConfigModule>> batchUpdate(List<ProjectConfigModule> records) {
        try {
            if (records == null || records.isEmpty()) {
                return CommonResult.failed("批量更新数据不能为空");
            }

            String userId = SecurityUtils.getUserIdValue();
            Date now = new Date();

            // 设置更新字段
            for (ProjectConfigModule record : records) {
            }

            int result = projectConfigModuleMapper.batchUpdate(records);
            if (result > 0) {
                log.info("批量更新ProjectConfigModule成功，共{}条记录", result);
                return CommonResult.success(records);
            } else {
                log.warn("批量更新ProjectConfigModule失败");
                return CommonResult.failed("批量更新失败");
            }
        } catch (Exception e) {
            log.error("批量更新ProjectConfigModule异常: {}", e.getMessage(), e);
            return CommonResult.failed("批量更新异常: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<List<ProjectConfigModule>> listByCondition(Map<String, Object> params) {
        try {
            List<ProjectConfigModule> records = projectConfigModuleMapper.selectByCondition(params);
            return CommonResult.success(records);
        } catch (Exception e) {
            log.error("条件查询ProjectConfigModule列表异常: {}", e.getMessage(), e);
            return CommonResult.failed("查询异常: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<ProjectConfigModule> getOneByCondition(Map<String, Object> params) {
        try {
            ProjectConfigModule record = projectConfigModuleMapper.selectOneByCondition(params);
            if (record != null) {
                return CommonResult.success(record);
            } else {
                return CommonResult.failed("未找到符合条件的记录");
            }
        } catch (Exception e) {
            log.error("条件查询单个ProjectConfigModule异常: {}", e.getMessage(), e);
            return CommonResult.failed("查询异常: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<CommonPage<ProjectConfigModule>> pageByCondition(Map<String, Object> params, Integer pageNum, Integer pageSize) {
        try {
            // 设置分页参数
            if (pageNum == null || pageNum < 1) pageNum = 1;
            if (pageSize == null || pageSize < 1) pageSize = 10;
            
            // 这里需要集成分页插件，如PageHelper
            // PageHelper.startPage(pageNum, pageSize);
            List<ProjectConfigModule> records = projectConfigModuleMapper.selectByCondition(params);
            
            // 构建分页结果
            CommonPage<ProjectConfigModule> page = CommonPage.restPage(records);
            return CommonResult.success(page);
        } catch (Exception e) {
            log.error("分页查询ProjectConfigModule异常: {}", e.getMessage(), e);
            return CommonResult.failed("分页查询异常: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<Long> countByCondition(Map<String, Object> params) {
        try {
            long count = projectConfigModuleMapper.countByCondition(params);
            return CommonResult.success(count);
        } catch (Exception e) {
            log.error("条件统计ProjectConfigModule异常: {}", e.getMessage(), e);
            return CommonResult.failed("统计异常: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<List<ProjectConfigModule>> listByMultipleConditions(Map<String, Object> conditions) {
        try {
            List<ProjectConfigModule> records = projectConfigModuleMapper.selectByMultipleConditions(conditions);
            return CommonResult.success(records);
        } catch (Exception e) {
            log.error("多条件查询ProjectConfigModule异常: {}", e.getMessage(), e);
            return CommonResult.failed("查询异常: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<List<ProjectConfigModule>> listByRange(String field, Object startValue, Object endValue) {
        try {
            List<ProjectConfigModule> records = projectConfigModuleMapper.selectByRange(field, startValue, endValue);
            return CommonResult.success(records);
        } catch (Exception e) {
            log.error("范围查询ProjectConfigModule异常: {}", e.getMessage(), e);
            return CommonResult.failed("查询异常: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<List<ProjectConfigModule>> listByLike(String field, String keyword) {
        try {
            List<ProjectConfigModule> records = projectConfigModuleMapper.selectByLike(field, keyword);
            return CommonResult.success(records);
        } catch (Exception e) {
            log.error("模糊查询ProjectConfigModule异常: {}", e.getMessage(), e);
            return CommonResult.failed("查询异常: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<List<ProjectConfigModule>> listByIn(String field, List<Object> values) {
        try {
            List<ProjectConfigModule> records = projectConfigModuleMapper.selectByIn(field, values);
            return CommonResult.success(records);
        } catch (Exception e) {
            log.error("IN查询ProjectConfigModule异常: {}", e.getMessage(), e);
            return CommonResult.failed("查询异常: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<Long> countTotal(Map<String, Object> params) {
        try {
            long count = projectConfigModuleMapper.countTotal(params);
            return CommonResult.success(count);
        } catch (Exception e) {
            log.error("统计ProjectConfigModule总数异常: {}", e.getMessage(), e);
            return CommonResult.failed("统计异常: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<List<ProjectConfigModule>> complexQuery(Map<String, Object> queryParams) {
        try {
            // 支持多种查询条件的组合
            List<ProjectConfigModule> records = new ArrayList<>();
            
            // 如果有精确匹配条件
            if (queryParams.containsKey("exactConditions")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> exactConditions = (Map<String, Object>) queryParams.get("exactConditions");
                records = projectConfigModuleMapper.selectByMultipleConditions(exactConditions);
            }
            // 如果有范围查询条件
            else if (queryParams.containsKey("rangeField") && queryParams.containsKey("startValue") && queryParams.containsKey("endValue")) {
                String field = (String) queryParams.get("rangeField");
                Object startValue = queryParams.get("startValue");
                Object endValue = queryParams.get("endValue");
                records = projectConfigModuleMapper.selectByRange(field, startValue, endValue);
            }
            // 如果有模糊查询条件
            else if (queryParams.containsKey("likeField") && queryParams.containsKey("keyword")) {
                String field = (String) queryParams.get("likeField");
                String keyword = (String) queryParams.get("keyword");
                records = projectConfigModuleMapper.selectByLike(field, keyword);
            }
            // 如果有IN查询条件
            else if (queryParams.containsKey("inField") && queryParams.containsKey("values")) {
                String field = (String) queryParams.get("inField");
                @SuppressWarnings("unchecked")
                List<Object> values = (List<Object>) queryParams.get("values");
                records = projectConfigModuleMapper.selectByIn(field, values);
            }
            // 默认条件查询
            else {
                records = projectConfigModuleMapper.selectByCondition(queryParams);
            }
            
            return CommonResult.success(records);
        } catch (Exception e) {
            log.error("复杂查询ProjectConfigModule异常: {}", e.getMessage(), e);
            return CommonResult.failed("查询异常: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<CommonPage<ProjectConfigModule>> complexPageQuery(Map<String, Object> queryParams, Integer pageNum, Integer pageSize) {
        try {
            PageHelper.startPage(pageNum, pageSize);
            CommonResult<List<ProjectConfigModule>> result = complexQuery(queryParams);
            
            if (result.isSuccess()) {
                List<ProjectConfigModule> list = result.getData();
                return CommonResult.success(CommonPage.restPage(list));
            } else {
                return CommonResult.failed(result.getMessage());
            }
        } catch (Exception e) {
            log.error("分页复杂查询ProjectConfigModule异常: {}", e.getMessage(), e);
            return CommonResult.failed("查询异常: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<Void> validateData(ProjectConfigModule record) {
        try {
            if (record == null) {
                return CommonResult.failed("数据不能为空");
            }

            // 基础字段校验

            // 业务逻辑校验
            // 可以在这里添加具体的业务校验逻辑
            
            return CommonResult.success(null);
        } catch (Exception e) {
            log.error("数据校验异常: {}", e.getMessage(), e);
            return CommonResult.failed("数据校验异常: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<Boolean> existsById(Long id) {
        try {
            if (id == null) {
                return CommonResult.success(false);
            }
            ProjectConfigModule record = projectConfigModuleMapper.selectByPrimaryKey(id);
            return CommonResult.success(record != null);
        } catch (Exception e) {
            log.error("检查ProjectConfigModule存在性异常: {}", e.getMessage(), e);
            return CommonResult.failed("检查异常: " + e.getMessage());
        }
    }

}
