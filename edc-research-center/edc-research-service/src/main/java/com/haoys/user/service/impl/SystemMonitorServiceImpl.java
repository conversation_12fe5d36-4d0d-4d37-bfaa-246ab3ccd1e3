package com.haoys.user.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.haoys.user.common.api.PageResult;
import com.haoys.user.common.ip.IpLocationUtils;
import com.haoys.user.common.ip.UserAgentUtils;
import com.haoys.user.domain.param.monitor.SystemMonitorParam;
import com.haoys.user.domain.vo.monitor.SystemAccessLogVo;
import com.haoys.user.domain.vo.monitor.SystemAccessStatisticsVo;
import com.haoys.user.domain.vo.monitor.SystemOnlineUserVo;
import com.haoys.user.domain.vo.system.SystemExceptionLogVo;
import com.haoys.user.service.SystemMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 系统监控服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Slf4j
@Service
public class SystemMonitorServiceImpl implements SystemMonitorService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    @Transactional
    public void recordAccessLog(String userId, String userName, String realName, String sessionId,
                               String requestUrl, String requestMethod, String requestIp,
                               String userAgent, Long responseTime, Integer responseStatus) {
        try {
            // 确保IP地址不为null
            if (requestIp == null || requestIp.trim().isEmpty()) {
                requestIp = "127.0.0.1"; // 默认本地IP
                log.warn("请求IP为空，使用默认值: {}", requestIp);
            }

            // 解析用户代理信息
            Map<String, String> uaInfo = UserAgentUtils.parseUserAgent(userAgent);
            String browser = uaInfo.get("browser");
            String os = uaInfo.get("os");
            String deviceType = uaInfo.get("deviceType");

            // 获取IP位置信息
            String location = IpLocationUtils.getLocationByIp(requestIp);

            // 确定访问类型
            String accessType = determineAccessType(requestUrl, userAgent);

            String sql = "INSERT INTO system_access_log " +
                        "(user_id, user_name, real_name, session_id, request_url, request_method, " +
                        "request_ip, location, user_agent, browser, os, device_type, access_time, " +
                        "response_time, response_status, access_type) " +
                        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?, ?)";

            jdbcTemplate.update(sql,
                userId, userName, realName, sessionId, requestUrl, requestMethod,
                requestIp, location, userAgent, browser, os, deviceType,
                responseTime, responseStatus, accessType);

            log.debug("记录访问日志成功: user={}, url={}, ip={}", userName, requestUrl, requestIp);
        } catch (Exception e) {
            log.error("记录访问日志失败: user={}, url={}, error={}", userName, requestUrl, e.getMessage(), e);
        }
    }

    @Override
    @Async
    public void recordLoginLog(String userId, String userName, String realName, String sessionId,
                              String requestUrl, String requestMethod, String requestIp,
                              String userAgent, Long responseTime, Integer responseStatus,
                              String loginType, String phoneNumber, Boolean loginSuccess, String failureReason) {
        try {
            // 确保IP地址不为null
            if (requestIp == null || requestIp.trim().isEmpty()) {
                requestIp = "127.0.0.1"; // 默认本地IP
                log.warn("登录请求IP为空，使用默认值: {}", requestIp);
            }

            // 解析用户代理信息
            Map<String, String> uaInfo = UserAgentUtils.parseUserAgent(userAgent);
            String browser = uaInfo.get("browser");
            String os = uaInfo.get("os");
            String deviceType = uaInfo.get("deviceType");

            // 获取IP位置信息
            String location = IpLocationUtils.getLocationByIp(requestIp);

            // 确定访问类型
            String accessType = determineAccessType(requestUrl, userAgent);

            // 插入登录日志
            String sql = "INSERT INTO system_access_log " +
                        "(user_id, user_name, real_name, session_id, request_url, request_method, " +
                        "request_ip, location, user_agent, browser, os, device_type, access_time, " +
                        "response_time, response_status, access_type, is_login_request, login_type, " +
                        "phone_number, login_success, login_failure_reason) " +
                        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?, ?, 1, ?, ?, ?, ?)";

            jdbcTemplate.update(sql, userId, userName, realName, sessionId, requestUrl, requestMethod,
                               requestIp, location, userAgent, browser, os, deviceType,
                               responseTime, responseStatus, accessType, loginType, phoneNumber,
                               loginSuccess, failureReason);

            log.debug("记录登录日志成功: user={}, loginType={}, success={}", userName, loginType, loginSuccess);
        } catch (Exception e) {
            log.error("记录登录日志失败: user={}, loginType={}, error={}", userName, loginType, e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void updateOnlineUser(String userId, String userName, String realName, String sessionId,
                                String loginIp, String userAgent, String token) {
        try {
            // 确保IP地址不为null
            if (loginIp == null || loginIp.trim().isEmpty()) {
                loginIp = "127.0.0.1"; // 默认本地IP
                log.warn("在线用户IP为空，使用默认值: {}", loginIp);
            }

            // 解析用户代理信息
            Map<String, String> uaInfo = UserAgentUtils.parseUserAgent(userAgent);
            String browser = uaInfo.get("browser");
            String os = uaInfo.get("os");
            String deviceType = uaInfo.get("deviceType");

            // 获取IP位置信息
            String location = IpLocationUtils.getLocationByIp(loginIp);
            
            // 检查用户是否已在线
            String checkSql = "SELECT COUNT(*) FROM system_online_user WHERE session_id = ?";
            Integer count = jdbcTemplate.queryForObject(checkSql, Integer.class, sessionId);
            
            if (count != null && count > 0) {
                // 更新现有记录
                String updateSql = "UPDATE system_online_user SET " +
                                  "last_access_time = NOW(), access_count = access_count + 1, " +
                                  "status = 'ONLINE', token = ? " +
                                  "WHERE session_id = ?";
                jdbcTemplate.update(updateSql, token, sessionId);
            } else {
                // 插入新记录
                String insertSql = "INSERT INTO system_online_user " +
                                  "(user_id, user_name, real_name, session_id, login_ip, login_location, " +
                                  "user_agent, browser, os, device_type, login_time, last_access_time, " +
                                  "access_count, status, token, expire_time) " +
                                  "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), 1, 'ONLINE', ?, " +
                                  "DATE_ADD(NOW(), INTERVAL 2 HOUR))";
                jdbcTemplate.update(insertSql,
                    userId, userName, realName, sessionId, loginIp, location,
                    userAgent, browser, os, deviceType, token);
            }
            
            log.debug("更新在线用户成功: user={}, session={}", userName, sessionId);
        } catch (Exception e) {
            log.error("更新在线用户失败: user={}, session={}, error={}", userName, sessionId, e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void removeOfflineUser(String sessionId) {
        try {
            String sql = "UPDATE system_online_user SET status = 'OFFLINE' WHERE session_id = ?";
            int updated = jdbcTemplate.update(sql, sessionId);
            
            if (updated > 0) {
                log.debug("用户下线成功: session={}", sessionId);
            }
        } catch (Exception e) {
            log.error("移除离线用户失败: session={}, error={}", sessionId, e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public int cleanExpiredOnlineUsers() {
        try {
            // 清理超过2小时未活动的用户
            String sql = "UPDATE system_online_user SET status = 'TIMEOUT' " +
                        "WHERE status = 'ONLINE' AND last_access_time < DATE_SUB(NOW(), INTERVAL 2 HOUR)";
            int updated = jdbcTemplate.update(sql);
            
            log.info("清理过期在线用户: {} 个", updated);
            return updated;
        } catch (Exception e) {
            log.error("清理过期在线用户失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    @Transactional
    public void recordUserLogin(String userId, String userName, String realName, String sessionId,
                               String loginIp, String userAgent, String token, String loginType, String phoneNumber) {
        try {
            // 确保IP地址不为null
            if (loginIp == null || loginIp.trim().isEmpty()) {
                loginIp = "127.0.0.1"; // 默认本地IP
                log.warn("用户登录IP为空，使用默认值: {}", loginIp);
            }

            // 解析用户代理信息
            Map<String, String> uaInfo = UserAgentUtils.parseUserAgent(userAgent);
            String browser = uaInfo.get("browser");
            String os = uaInfo.get("os");
            String deviceType = uaInfo.get("deviceType");

            // 获取IP位置信息
            String location = IpLocationUtils.getLocationByIp(loginIp);

            // 检查用户是否已在线
            String checkSql = "SELECT COUNT(*) FROM system_online_user WHERE session_id = ?";
            Integer count = jdbcTemplate.queryForObject(checkSql, Integer.class, sessionId);

            if (count != null && count > 0) {
                // 更新现有记录
                String updateSql = "UPDATE system_online_user SET " +
                                  "last_access_time = NOW(), last_login_time = NOW(), " +
                                  "login_success_count = login_success_count + 1, " +
                                  "access_count = access_count + 1, status = 'ONLINE', " +
                                  "token = ?, login_type = ?, phone_number = ? " +
                                  "WHERE session_id = ?";
                jdbcTemplate.update(updateSql, token, loginType, phoneNumber, sessionId);
            } else {
                // 插入新记录
                String insertSql = "INSERT INTO system_online_user " +
                                  "(user_id, user_name, real_name, session_id, login_ip, login_location, " +
                                  "user_agent, browser, os, device_type, login_time, last_access_time, " +
                                  "last_login_time, access_count, login_success_count, status, token, " +
                                  "login_type, phone_number, expire_time) " +
                                  "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW(), 1, 1, 'ONLINE', ?, ?, ?, " +
                                  "DATE_ADD(NOW(), INTERVAL 2 HOUR))";
                jdbcTemplate.update(insertSql,
                    userId, userName, realName, sessionId, loginIp, location,
                    userAgent, browser, os, deviceType, token, loginType, phoneNumber);
            }

            log.debug("记录用户登录成功: user={}, loginType={}, session={}", userName, loginType, sessionId);
        } catch (Exception e) {
            log.error("记录用户登录失败: user={}, loginType={}, session={}, error={}", userName, loginType, sessionId, e.getMessage(), e);
        }
    }

    @Override
    public PageResult<SystemAccessLogVo> getAccessLogList(SystemMonitorParam.AccessLogQueryParam param) {
        try {
            // 构建查询条件
            StringBuilder whereSql = new StringBuilder(" WHERE 1=1 ");
            List<Object> params = new ArrayList<>();
            
            if (StrUtil.isNotBlank(param.getUserId())) {
                whereSql.append(" AND user_id = ? ");
                params.add(param.getUserId());
            }
            if (StrUtil.isNotBlank(param.getUserName())) {
                whereSql.append(" AND user_name LIKE ? ");
                params.add("%" + param.getUserName() + "%");
            }
            if (StrUtil.isNotBlank(param.getRequestIp())) {
                whereSql.append(" AND request_ip = ? ");
                params.add(param.getRequestIp());
            }
            if (StrUtil.isNotBlank(param.getRequestUrl())) {
                whereSql.append(" AND request_url LIKE ? ");
                params.add("%" + param.getRequestUrl() + "%");
            }
            if (StrUtil.isNotBlank(param.getRequestMethod())) {
                whereSql.append(" AND request_method = ? ");
                params.add(param.getRequestMethod());
            }
            if (StrUtil.isNotBlank(param.getAccessType())) {
                whereSql.append(" AND access_type = ? ");
                params.add(param.getAccessType());
            }
            if (param.getStartTime() != null) {
                whereSql.append(" AND access_time >= ? ");
                params.add(param.getStartTime());
            }
            if (param.getEndTime() != null) {
                whereSql.append(" AND access_time <= ? ");
                params.add(param.getEndTime());
            }
            
            // 查询总数
            String countSql = "SELECT COUNT(*) FROM system_access_log " + whereSql.toString();
            Long total = jdbcTemplate.queryForObject(countSql, Long.class, params.toArray());
            
            if (total == null || total == 0) {
                return PageResult.empty();
            }
            
            // 构建排序
            String orderBy = buildOrderBy(param.getOrderBy(), param.getOrderDirection());
            
            // 分页查询
            int offset = (param.getPageNum() - 1) * param.getPageSize();
            String dataSql = "SELECT * FROM system_access_log " + whereSql.toString() + 
                           orderBy + " LIMIT ? OFFSET ?";
            params.add(param.getPageSize());
            params.add(offset);
            
            List<SystemAccessLogVo> list = jdbcTemplate.query(dataSql, params.toArray(), 
                (rs, rowNum) -> {
                    SystemAccessLogVo vo = new SystemAccessLogVo();
                    vo.setId(rs.getLong("id"));
                    vo.setUserId(rs.getString("user_id"));
                    vo.setUserName(rs.getString("user_name"));
                    vo.setRealName(rs.getString("real_name"));
                    vo.setSessionId(rs.getString("session_id"));
                    vo.setRequestUrl(rs.getString("request_url"));
                    vo.setRequestMethod(rs.getString("request_method"));
                    vo.setRequestIp(rs.getString("request_ip"));
                    vo.setLocation(rs.getString("location"));
                    vo.setUserAgent(rs.getString("user_agent"));
                    vo.setBrowser(rs.getString("browser"));
                    vo.setOs(rs.getString("os"));
                    vo.setDeviceType(rs.getString("device_type"));
                    vo.setAccessTime(rs.getTimestamp("access_time"));
                    vo.setResponseTime(rs.getLong("response_time"));
                    vo.setResponseStatus(rs.getInt("response_status"));
                    vo.setRequestParams(rs.getString("request_params"));
                    vo.setResponseSize(rs.getLong("response_size"));
                    vo.setReferer(rs.getString("referer"));
                    vo.setAccessType(rs.getString("access_type"));
                    vo.setCreateTime(rs.getTimestamp("create_time"));
                    vo.setUpdateTime(rs.getTimestamp("update_time"));
                    return vo;
                });
            
            return PageResult.success(list, total, param.getPageNum(), param.getPageSize());
        } catch (Exception e) {
            log.error("查询访问日志失败: {}", e.getMessage(), e);
            return PageResult.empty();
        }
    }

    /**
     * 确定访问类型
     */
    private String determineAccessType(String requestUrl, String userAgent) {
        if (StrUtil.isBlank(requestUrl)) {
            return "WEB";
        }
        
        if (requestUrl.contains("/api/")) {
            return "API";
        }
        
        if (userAgent != null && (userAgent.contains("Mobile") || userAgent.contains("Android") || userAgent.contains("iPhone"))) {
            return "MOBILE";
        }
        
        return "WEB";
    }

    @Override
    public PageResult<SystemOnlineUserVo> getOnlineUserList(SystemMonitorParam.OnlineUserQueryParam param) {
        try {
            // 构建查询条件
            StringBuilder whereSql = new StringBuilder(" WHERE status = 'ONLINE' ");
            List<Object> params = new ArrayList<>();

            if (StrUtil.isNotBlank(param.getUserId())) {
                whereSql.append(" AND user_id = ? ");
                params.add(param.getUserId());
            }
            if (StrUtil.isNotBlank(param.getUserName())) {
                whereSql.append(" AND user_name LIKE ? ");
                params.add("%" + param.getUserName() + "%");
            }
            if (StrUtil.isNotBlank(param.getLoginIp())) {
                whereSql.append(" AND login_ip = ? ");
                params.add(param.getLoginIp());
            }
            if (StrUtil.isNotBlank(param.getDeviceType())) {
                whereSql.append(" AND device_type = ? ");
                params.add(param.getDeviceType());
            }

            // 查询总数
            String countSql = "SELECT COUNT(*) FROM system_online_user " + whereSql.toString();
            Long total = jdbcTemplate.queryForObject(countSql, Long.class, params.toArray());

            if (total == null || total == 0) {
                return PageResult.empty();
            }

            // 构建排序
            String orderBy = buildOrderBy(param.getOrderBy(), param.getOrderDirection());

            // 分页查询
            int offset = (param.getPageNum() - 1) * param.getPageSize();
            String dataSql = "SELECT *, TIMESTAMPDIFF(MINUTE, login_time, NOW()) as online_duration " +
                           "FROM system_online_user " + whereSql.toString() +
                           orderBy + " LIMIT ? OFFSET ?";
            params.add(param.getPageSize());
            params.add(offset);

            List<SystemOnlineUserVo> list = jdbcTemplate.query(dataSql, params.toArray(),
                (rs, rowNum) -> {
                    SystemOnlineUserVo vo = new SystemOnlineUserVo();
                    vo.setId(rs.getLong("id"));
                    vo.setUserId(rs.getString("user_id"));
                    vo.setUserName(rs.getString("user_name"));
                    vo.setRealName(rs.getString("real_name"));
                    vo.setSessionId(rs.getString("session_id"));
                    vo.setLoginIp(rs.getString("login_ip"));
                    vo.setLoginLocation(rs.getString("login_location"));
                    vo.setUserAgent(rs.getString("user_agent"));
                    vo.setBrowser(rs.getString("browser"));
                    vo.setOs(rs.getString("os"));
                    vo.setDeviceType(rs.getString("device_type"));
                    vo.setLoginTime(rs.getTimestamp("login_time"));
                    vo.setLastAccessTime(rs.getTimestamp("last_access_time"));
                    vo.setLastAccessUrl(rs.getString("last_access_url"));
                    vo.setAccessCount(rs.getInt("access_count"));
                    vo.setStatus(rs.getString("status"));
                    vo.setToken(rs.getString("token"));
                    vo.setExpireTime(rs.getTimestamp("expire_time"));
                    vo.setOnlineDuration(rs.getLong("online_duration"));
                    vo.setCreateTime(rs.getTimestamp("create_time"));
                    vo.setUpdateTime(rs.getTimestamp("update_time"));
                    return vo;
                });

            return PageResult.success(list, total, param.getPageNum(), param.getPageSize());
        } catch (Exception e) {
            log.error("查询在线用户失败: {}", e.getMessage(), e);
            return PageResult.empty();
        }
    }

    @Override
    public PageResult<SystemExceptionLogVo> getExceptionLogList(SystemMonitorParam.ExceptionLogQueryParam param) {
        try {
            // 构建查询条件
            StringBuilder whereSql = new StringBuilder(" WHERE 1=1 ");
            List<Object> params = new ArrayList<>();

            if (StrUtil.isNotBlank(param.getUserId())) {
                whereSql.append(" AND user_id = ? ");
                params.add(param.getUserId());
            }
            if (StrUtil.isNotBlank(param.getUserName())) {
                whereSql.append(" AND (user_name LIKE ? OR user_name = 'Anonymous users') ");
                params.add("%" + param.getUserName() + "%");
            }
            if (StrUtil.isNotBlank(param.getRequestIp())) {
                whereSql.append(" AND request_ip = ? ");
                params.add(param.getRequestIp());
            }
            if (StrUtil.isNotBlank(param.getRequestUrl())) {
                whereSql.append(" AND request_url LIKE ? ");
                params.add("%" + param.getRequestUrl() + "%");
            }
            if (StrUtil.isNotBlank(param.getExceptionMessage())) {
                whereSql.append(" AND exception_message LIKE ? ");
                params.add("%" + param.getExceptionMessage() + "%");
            }
            if (param.getStartTime() != null) {
                whereSql.append(" AND create_time >= ? ");
                params.add(param.getStartTime());
            }
            if (param.getEndTime() != null) {
                whereSql.append(" AND create_time <= ? ");
                params.add(param.getEndTime());
            }

            // 查询总数
            String countSql = "SELECT COUNT(*) FROM system_exception_log " + whereSql.toString();
            Long total = jdbcTemplate.queryForObject(countSql, Long.class, params.toArray());

            if (total == null || total == 0) {
                return PageResult.empty();
            }

            // 构建排序
            String orderBy = buildOrderBy(param.getOrderBy(), param.getOrderDirection());

            // 分页查询
            int offset = (param.getPageNum() - 1) * param.getPageSize();
            String dataSql = "SELECT * FROM system_exception_log " + whereSql.toString() +
                           orderBy + " LIMIT ? OFFSET ?";
            params.add(param.getPageSize());
            params.add(offset);

            List<SystemExceptionLogVo> list = jdbcTemplate.query(dataSql, params.toArray(),
                (rs, rowNum) -> {
                    SystemExceptionLogVo vo = new SystemExceptionLogVo();
                    Long id = rs.getLong("id");
                    vo.setId(id);
                    vo.setIdStr(String.valueOf(id)); // 设置字符串形式的ID，避免JavaScript精度丢失
                    vo.setUserId(rs.getString("user_id"));
                    vo.setUserName(StrUtil.isBlank(rs.getString("user_name")) ? "Anonymous users" : rs.getString("user_name"));
                    vo.setRequestUrl(rs.getString("request_url"));
                    vo.setRequestIp(rs.getString("request_ip"));
                    vo.setExceptionMessage(rs.getString("exception_message"));
                    vo.setStackTraceMessage(rs.getString("stack_trace_message"));
                    vo.setDescription(rs.getString("description"));
                    vo.setCreateTime(rs.getTimestamp("create_time"));

                    // 设置前端期望的字段名
                    vo.setExceptionType(rs.getString("description")); // 使用description作为异常类型
                    vo.setIpAddress(rs.getString("request_ip")); // 映射为ipAddress
                    vo.setLogLevel("ERROR"); // 默认设置为ERROR级别
                    vo.setStackTrace(rs.getString("stack_trace_message")); // 映射为stackTrace

                    return vo;
                });

            return PageResult.success(list, total, param.getPageNum(), param.getPageSize());
        } catch (Exception e) {
            log.error("查询异常日志失败: {}", e.getMessage(), e);
            return PageResult.empty();
        }
    }

    /**
     * 构建排序SQL
     */
    private String buildOrderBy(String orderBy, String orderDirection) {
        if (StrUtil.isBlank(orderBy)) {
            return " ORDER BY create_time DESC ";
        }

        // 安全检查，防止SQL注入
        String[] allowedFields = {"id", "user_name", "request_ip", "access_time", "create_time", "response_time", "last_access_time", "login_time"};
        boolean isValidField = Arrays.asList(allowedFields).contains(orderBy);

        if (!isValidField) {
            orderBy = "create_time";
        }

        if (!"ASC".equalsIgnoreCase(orderDirection) && !"DESC".equalsIgnoreCase(orderDirection)) {
            orderDirection = "DESC";
        }

        return " ORDER BY " + orderBy + " " + orderDirection + " ";
    }

    @Override
    public SystemAccessStatisticsVo.StatisticsOverview getStatisticsOverview() {
        try {
            SystemAccessStatisticsVo.StatisticsOverview overview = new SystemAccessStatisticsVo.StatisticsOverview();

            // 今日统计
            String todaySql = "SELECT " +
                            "COUNT(*) as today_visits, " +
                            "COUNT(DISTINCT request_ip) as today_unique_visitors, " +
                            "COUNT(DISTINCT CASE WHEN request_url LIKE '%login%' THEN user_id END) as today_logins " +
                            "FROM system_access_log WHERE DATE(access_time) = CURDATE()";

            jdbcTemplate.queryForObject(todaySql, (rs, rowNum) -> {
                overview.setTodayVisits(rs.getLong("today_visits"));
                overview.setTodayUniqueVisitors(rs.getLong("today_unique_visitors"));
                overview.setTodayLogins(rs.getLong("today_logins"));
                return null;
            });

            // 当前在线用户数
            String onlineSql = "SELECT COUNT(*) FROM system_online_user WHERE status = 'ONLINE'";
            Long currentOnline = jdbcTemplate.queryForObject(onlineSql, Long.class);
            overview.setCurrentOnlineUsers(currentOnline != null ? currentOnline : 0L);

            // 总访问次数
            String totalSql = "SELECT COUNT(*) FROM system_access_log";
            Long totalVisits = jdbcTemplate.queryForObject(totalSql, Long.class);
            overview.setTotalVisits(totalVisits != null ? totalVisits : 0L);

            // 本周访问次数
            String weekSql = "SELECT COUNT(*) FROM system_access_log WHERE access_time >= DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)";
            Long weekVisits = jdbcTemplate.queryForObject(weekSql, Long.class);
            overview.setWeekVisits(weekVisits != null ? weekVisits : 0L);

            // 本月访问次数
            String monthSql = "SELECT COUNT(*) FROM system_access_log WHERE YEAR(access_time) = YEAR(CURDATE()) AND MONTH(access_time) = MONTH(CURDATE())";
            Long monthVisits = jdbcTemplate.queryForObject(monthSql, Long.class);
            overview.setMonthVisits(monthVisits != null ? monthVisits : 0L);

            // 平均响应时间
            String avgSql = "SELECT AVG(response_time) FROM system_access_log WHERE response_time IS NOT NULL AND DATE(access_time) = CURDATE()";
            BigDecimal avgResponseTime = jdbcTemplate.queryForObject(avgSql, BigDecimal.class);
            overview.setAvgResponseTime(avgResponseTime != null ? avgResponseTime : BigDecimal.ZERO);

            // 系统健康状态
            overview.setSystemHealth(determineSystemHealth(overview));

            return overview;
        } catch (Exception e) {
            log.error("获取统计概览失败: {}", e.getMessage(), e);
            return new SystemAccessStatisticsVo.StatisticsOverview();
        }
    }

    @Override
    public PageResult<SystemAccessStatisticsVo> getAccessStatisticsList(SystemMonitorParam.StatisticsQueryParam param) {
        try {
            // 构建查询条件
            StringBuilder whereSql = new StringBuilder(" WHERE 1=1 ");
            List<Object> params = new ArrayList<>();

            if (param.getStartDate() != null) {
                whereSql.append(" AND stat_date >= ? ");
                params.add(param.getStartDate());
            }
            if (param.getEndDate() != null) {
                whereSql.append(" AND stat_date <= ? ");
                params.add(param.getEndDate());
            }

            // 查询总数
            String countSql = "SELECT COUNT(*) FROM system_access_statistics " + whereSql.toString();
            Long total = jdbcTemplate.queryForObject(countSql, Long.class, params.toArray());

            if (total == null || total == 0) {
                return PageResult.empty();
            }

            // 分页查询
            int offset = (param.getPageNum() - 1) * param.getPageSize();
            String dataSql = "SELECT * FROM system_access_statistics " + whereSql.toString() +
                           " ORDER BY stat_date DESC LIMIT ? OFFSET ?";
            params.add(param.getPageSize());
            params.add(offset);

            List<SystemAccessStatisticsVo> list = jdbcTemplate.query(dataSql, params.toArray(),
                (rs, rowNum) -> {
                    SystemAccessStatisticsVo vo = new SystemAccessStatisticsVo();
                    vo.setId(rs.getLong("id"));
                    vo.setStatDate(rs.getDate("stat_date"));
                    vo.setTotalVisits(rs.getLong("total_visits"));
                    vo.setUniqueVisitors(rs.getLong("unique_visitors"));
                    vo.setLoginCount(rs.getLong("login_count"));
                    vo.setUniqueLoginUsers(rs.getLong("unique_login_users"));
                    vo.setPageViews(rs.getLong("page_views"));
                    vo.setApiCalls(rs.getLong("api_calls"));
                    vo.setAvgResponseTime(rs.getBigDecimal("avg_response_time"));
                    vo.setMaxOnlineUsers(rs.getInt("max_online_users"));
                    vo.setErrorCount(rs.getLong("error_count"));
                    vo.setCreateTime(rs.getTimestamp("create_time"));
                    vo.setUpdateTime(rs.getTimestamp("update_time"));
                    return vo;
                });

            return PageResult.success(list, total, param.getPageNum(), param.getPageSize());
        } catch (Exception e) {
            log.error("查询访问统计失败: {}", e.getMessage(), e);
            return PageResult.empty();
        }
    }

    @Override
    @Transactional
    public boolean generateDailyStatistics(String statDate) {
        try {
            Date date = DateUtil.parseDate(statDate);

            // 检查是否已存在统计数据
            String checkSql = "SELECT COUNT(*) FROM system_access_statistics WHERE stat_date = ?";
            Integer count = jdbcTemplate.queryForObject(checkSql, Integer.class, date);

            if (count != null && count > 0) {
                log.info("统计数据已存在: {}", statDate);
                return true;
            }

            // 生成统计数据
            String insertSql = "INSERT INTO system_access_statistics " +
                             "(stat_date, total_visits, unique_visitors, login_count, unique_login_users, " +
                             "page_views, api_calls, avg_response_time, max_online_users, error_count) " +
                             "SELECT ?, " +
                             "COUNT(*) as total_visits, " +
                             "COUNT(DISTINCT request_ip) as unique_visitors, " +
                             "COUNT(DISTINCT CASE WHEN request_url LIKE '%login%' THEN user_id END) as login_count, " +
                             "COUNT(DISTINCT CASE WHEN request_url LIKE '%login%' THEN user_id END) as unique_login_users, " +
                             "COUNT(CASE WHEN access_type = 'WEB' THEN 1 END) as page_views, " +
                             "COUNT(CASE WHEN access_type = 'API' THEN 1 END) as api_calls, " +
                             "AVG(CASE WHEN response_time IS NOT NULL THEN response_time END) as avg_response_time, " +
                             "0 as max_online_users, " +
                             "COUNT(CASE WHEN response_status >= 400 THEN 1 END) as error_count " +
                             "FROM system_access_log WHERE DATE(access_time) = ?";

            int inserted = jdbcTemplate.update(insertSql, date, date);

            log.info("生成每日统计数据成功: date={}, inserted={}", statDate, inserted);
            return inserted > 0;
        } catch (Exception e) {
            log.error("生成每日统计数据失败: date={}, error={}", statDate, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 确定系统健康状态
     */
    private String determineSystemHealth(SystemAccessStatisticsVo.StatisticsOverview overview) {
        try {
            // 检查错误率
            String errorSql = "SELECT COUNT(CASE WHEN response_status >= 400 THEN 1 END) * 100.0 / COUNT(*) as error_rate " +
                            "FROM system_access_log WHERE DATE(access_time) = CURDATE()";
            BigDecimal errorRate = jdbcTemplate.queryForObject(errorSql, BigDecimal.class);

            if (errorRate != null && errorRate.compareTo(new BigDecimal("10")) > 0) {
                return "CRITICAL";
            } else if (errorRate != null && errorRate.compareTo(new BigDecimal("5")) > 0) {
                return "WARNING";
            } else if (overview.getAvgResponseTime().compareTo(new BigDecimal("2000")) > 0) {
                return "WARNING";
            } else {
                return "HEALTHY";
            }
        } catch (Exception e) {
            log.error("确定系统健康状态失败: {}", e.getMessage(), e);
            return "UNKNOWN";
        }
    }

    @Override
    public Map<String, Object> getRealTimeStatistics() {
        Map<String, Object> result = new HashMap<>();
        try {
            // 当前在线用户数
            String onlineSql = "SELECT COUNT(*) FROM system_online_user WHERE status = 'ONLINE'";
            Long onlineUsers = jdbcTemplate.queryForObject(onlineSql, Long.class);
            result.put("onlineUsers", onlineUsers != null ? onlineUsers : 0L);

            // 今日访问次数
            String todayVisitsSql = "SELECT COUNT(*) FROM system_access_log WHERE DATE(access_time) = CURDATE()";
            Long todayVisits = jdbcTemplate.queryForObject(todayVisitsSql, Long.class);
            result.put("todayVisits", todayVisits != null ? todayVisits : 0L);

            // 今日新增用户
            String newUsersSql = "SELECT COUNT(DISTINCT user_id) FROM system_access_log WHERE DATE(access_time) = CURDATE() AND user_id IS NOT NULL";
            Long newUsers = jdbcTemplate.queryForObject(newUsersSql, Long.class);
            result.put("newUsers", newUsers != null ? newUsers : 0L);

            // 系统响应时间
            String avgResponseSql = "SELECT AVG(response_time) FROM system_access_log WHERE response_time IS NOT NULL AND access_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)";
            BigDecimal avgResponse = jdbcTemplate.queryForObject(avgResponseSql, BigDecimal.class);
            result.put("avgResponseTime", avgResponse != null ? avgResponse : BigDecimal.ZERO);

            // 错误率
            String errorRateSql = "SELECT COUNT(CASE WHEN response_status >= 400 THEN 1 END) * 100.0 / COUNT(*) " +
                                "FROM system_access_log WHERE access_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)";
            BigDecimal errorRate = jdbcTemplate.queryForObject(errorRateSql, BigDecimal.class);
            result.put("errorRate", errorRate != null ? errorRate : BigDecimal.ZERO);

        } catch (Exception e) {
            log.error("获取实时统计数据失败: {}", e.getMessage(), e);
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> getAccessTrend(int days) {
        try {
            String sql = "SELECT DATE(access_time) as date, COUNT(*) as visits, COUNT(DISTINCT request_ip) as unique_visitors " +
                        "FROM system_access_log WHERE access_time >= DATE_SUB(CURDATE(), INTERVAL ? DAY) " +
                        "GROUP BY DATE(access_time) ORDER BY date";

            return jdbcTemplate.queryForList(sql, days);
        } catch (Exception e) {
            log.error("获取访问趋势数据失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getPopularPages(int limit) {
        try {
            String sql = "SELECT request_url, COUNT(*) as visit_count " +
                        "FROM system_access_log WHERE DATE(access_time) = CURDATE() " +
                        "GROUP BY request_url ORDER BY visit_count DESC LIMIT ?";

            return jdbcTemplate.queryForList(sql, limit);
        } catch (Exception e) {
            log.error("获取热门页面数据失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getLocationDistribution(int limit) {
        try {
            String sql = "SELECT location, COUNT(*) as visit_count " +
                        "FROM system_access_log WHERE location IS NOT NULL AND DATE(access_time) = CURDATE() " +
                        "GROUP BY location ORDER BY visit_count DESC LIMIT ?";

            return jdbcTemplate.queryForList(sql, limit);
        } catch (Exception e) {
            log.error("获取地域分布数据失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional
    public boolean forceOfflineUser(String sessionId) {
        try {
            String sql = "UPDATE system_online_user SET status = 'OFFLINE' WHERE session_id = ?";
            int updated = jdbcTemplate.update(sql, sessionId);

            log.info("强制下线用户: session={}, result={}", sessionId, updated > 0);
            return updated > 0;
        } catch (Exception e) {
            log.error("强制下线用户失败: session={}, error={}", sessionId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public int batchForceOfflineUsers(List<String> sessionIds) {
        if (sessionIds == null || sessionIds.isEmpty()) {
            return 0;
        }

        try {
            String sql = "UPDATE system_online_user SET status = 'OFFLINE' WHERE session_id = ?";

            int successCount = 0;
            for (String sessionId : sessionIds) {
                try {
                    int updated = jdbcTemplate.update(sql, sessionId);
                    if (updated > 0) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("批量下线用户失败: session={}, error={}", sessionId, e.getMessage());
                }
            }

            log.info("批量强制下线用户: total={}, success={}", sessionIds.size(), successCount);
            return successCount;
        } catch (Exception e) {
            log.error("批量强制下线用户失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public Map<String, Object> getLoginStatistics(String startDate, String endDate) {
        try {
            Map<String, Object> result = new HashMap<>();

            // 总登录次数
            String totalLoginSql = "SELECT COUNT(*) FROM system_access_log " +
                                  "WHERE is_login_request = 1 AND login_success = 1 " +
                                  "AND DATE(access_time) BETWEEN ? AND ?";
            Integer totalLogins = jdbcTemplate.queryForObject(totalLoginSql, Integer.class, startDate, endDate);

            // 独立登录用户数
            String uniqueUserSql = "SELECT COUNT(DISTINCT user_id) FROM system_access_log " +
                                  "WHERE is_login_request = 1 AND login_success = 1 " +
                                  "AND DATE(access_time) BETWEEN ? AND ?";
            Integer uniqueUsers = jdbcTemplate.queryForObject(uniqueUserSql, Integer.class, startDate, endDate);

            // 登录失败次数
            String failedLoginSql = "SELECT COUNT(*) FROM system_access_log " +
                                   "WHERE is_login_request = 1 AND login_success = 0 " +
                                   "AND DATE(access_time) BETWEEN ? AND ?";
            Integer failedLogins = jdbcTemplate.queryForObject(failedLoginSql, Integer.class, startDate, endDate);

            result.put("totalLogins", totalLogins != null ? totalLogins : 0);
            result.put("uniqueUsers", uniqueUsers != null ? uniqueUsers : 0);
            result.put("failedLogins", failedLogins != null ? failedLogins : 0);
            result.put("successRate", totalLogins != null && totalLogins > 0 ?
                      String.format("%.2f", (double) totalLogins / (totalLogins + (failedLogins != null ? failedLogins : 0)) * 100) : "0.00");

            return result;
        } catch (Exception e) {
            log.error("获取登录统计失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    @Override
    public List<Map<String, Object>> getLoginTypeStatistics(String startDate, String endDate) {
        try {
            String sql = "SELECT login_type, COUNT(*) as count, " +
                        "COUNT(DISTINCT user_id) as unique_users " +
                        "FROM system_access_log " +
                        "WHERE is_login_request = 1 AND login_success = 1 " +
                        "AND DATE(access_time) BETWEEN ? AND ? " +
                        "GROUP BY login_type " +
                        "ORDER BY count DESC";

            return jdbcTemplate.queryForList(sql, startDate, endDate);
        } catch (Exception e) {
            log.error("获取登录类型统计失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> getTodayLoginStatistics() {
        try {
            String today = DateUtil.formatDate(new Date());
            return getLoginStatistics(today, today);
        } catch (Exception e) {
            log.error("获取今日登录统计失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    @Override
    public List<Map<String, Object>> getUserLoginHistory(String userId, int limit) {
        try {
            String sql = "SELECT access_time as login_time, login_type, phone_number, " +
                        "request_ip as login_ip, location as login_location, " +
                        "browser, os, device_type " +
                        "FROM system_access_log " +
                        "WHERE user_id = ? AND is_login_request = 1 AND login_success = 1 " +
                        "ORDER BY access_time DESC " +
                        "LIMIT ?";

            return jdbcTemplate.queryForList(sql, userId, limit);
        } catch (Exception e) {
            log.error("获取用户登录历史失败: userId={}, error={}", userId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public SystemAccessLogVo getAccessLogDetail(String id) {
        try {
            String sql = "SELECT * FROM system_access_log WHERE id = ?";
            return jdbcTemplate.queryForObject(sql, (rs, rowNum) -> {
                SystemAccessLogVo vo = new SystemAccessLogVo();
                vo.setId(rs.getLong("id"));
                vo.setUserId(rs.getString("user_id"));
                vo.setUserName(rs.getString("user_name"));
                vo.setRequestIp(rs.getString("request_ip"));
                vo.setRequestUrl(rs.getString("request_url"));
                vo.setRequestMethod(rs.getString("request_method"));
                vo.setRequestParams(rs.getString("request_params"));
                vo.setResponseStatus(rs.getInt("response_status"));
                vo.setResponseTime(rs.getLong("response_time"));
                vo.setUserAgent(rs.getString("user_agent"));
                vo.setReferer(rs.getString("referer"));
                vo.setAccessTime(rs.getTimestamp("access_time"));
                vo.setLocation(rs.getString("location"));
                vo.setBrowser(rs.getString("browser"));
                vo.setOs(rs.getString("os"));
                vo.setDeviceType(rs.getString("device_type"));
                vo.setAccessType(rs.getString("access_type"));
                vo.setSessionId(rs.getString("session_id"));
                return vo;
            }, id);
        } catch (Exception e) {
            log.error("获取访问日志详情失败: id={}, error={}", id, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public SystemOnlineUserVo getOnlineUserDetail(String sessionId) {
        try {
            String sql = "SELECT *, TIMESTAMPDIFF(MINUTE, login_time, NOW()) as online_duration " +
                        "FROM system_online_user WHERE session_id = ?";
            return jdbcTemplate.queryForObject(sql, (rs, rowNum) -> {
                SystemOnlineUserVo vo = new SystemOnlineUserVo();
                vo.setUserId(rs.getString("user_id"));
                vo.setUserName(rs.getString("user_name"));
                vo.setRealName(rs.getString("real_name"));
                vo.setSessionId(rs.getString("session_id"));
                vo.setLoginIp(rs.getString("login_ip"));
                vo.setLoginLocation(rs.getString("login_location"));
                vo.setUserAgent(rs.getString("user_agent"));
                vo.setBrowser(rs.getString("browser"));
                vo.setOs(rs.getString("os"));
                vo.setDeviceType(rs.getString("device_type"));
                vo.setLoginTime(rs.getTimestamp("login_time"));
                vo.setLastAccessTime(rs.getTimestamp("last_access_time"));
                vo.setLastLoginTime(rs.getTimestamp("last_login_time"));
                vo.setAccessCount(rs.getInt("access_count"));
                vo.setLoginSuccessCount(rs.getInt("login_success_count"));
                vo.setStatus(rs.getString("status"));
                vo.setLoginType(rs.getString("login_type"));
                vo.setPhoneNumber(rs.getString("phone_number"));
                vo.setOnlineDuration((long) rs.getInt("online_duration"));
                return vo;
            }, sessionId);
        } catch (Exception e) {
            log.error("获取在线用户详情失败: sessionId={}, error={}", sessionId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public SystemExceptionLogVo getExceptionLogDetail(String id) {
        try {
            log.info("开始查询异常日志详情: id={}", id);

            // 首先检查记录是否存在
            String countSql = "SELECT COUNT(*) FROM system_exception_log WHERE id = ?";
            Integer count = jdbcTemplate.queryForObject(countSql, Integer.class, id);

            log.info("异常日志记录数量: id={}, count={}", id, count);

            if (count == null || count == 0) {
                log.warn("异常日志不存在: id={}", id);
                return null;
            }

            String sql = "SELECT * FROM system_exception_log WHERE id = ?";
            List<SystemExceptionLogVo> results = jdbcTemplate.query(sql, (rs, rowNum) -> {
                SystemExceptionLogVo vo = new SystemExceptionLogVo();
                Long resultId = rs.getLong("id");
                vo.setId(resultId);
                vo.setIdStr(String.valueOf(resultId)); // 设置字符串形式的ID，避免JavaScript精度丢失
                vo.setUserId(rs.getString("user_id"));
                vo.setUserName(rs.getString("user_name"));
                vo.setDescription(rs.getString("description"));
                vo.setExceptionMessage(rs.getString("exception_message"));
                vo.setStackTraceMessage(rs.getString("stack_trace_message"));
                vo.setRequestUrl(rs.getString("request_url"));
                vo.setRequestIp(rs.getString("request_ip"));
                vo.setCreateTime(rs.getTimestamp("create_time"));

                // 设置前端期望的字段名
                vo.setExceptionType(rs.getString("description")); // 使用description作为异常类型
                vo.setIpAddress(rs.getString("request_ip")); // 映射为ipAddress
                vo.setLogLevel("ERROR"); // 默认设置为ERROR级别
                vo.setStackTrace(rs.getString("stack_trace_message")); // 映射为stackTrace

                return vo;
            }, id);

            log.info("异常日志查询结果数量: id={}, resultSize={}", id, results.size());

            if (results.isEmpty()) {
                log.warn("异常日志查询结果为空: id={}", id);
                return null;
            }

            SystemExceptionLogVo result = results.get(0);
            log.info("成功获取异常日志详情: id={}, userId={}, userName={}", id, result.getUserId(), result.getUserName());
            return result;
        } catch (Exception e) {
            log.error("获取异常日志详情失败: id={}, error={}", id, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Map<String, Object> getOnlineUserStatistics() {
        Map<String, Object> result = new HashMap<>();
        try {
            // 在线用户数
            String onlineSql = "SELECT COUNT(*) FROM system_online_user WHERE status = 'ONLINE'";
            Long onlineUsers = jdbcTemplate.queryForObject(onlineSql, Long.class);
            result.put("totalOnline", onlineUsers != null ? onlineUsers : 0L);

            // 空闲用户数（超过30分钟未活动但未超时）
            String idleSql = "SELECT COUNT(*) FROM system_online_user WHERE status = 'ONLINE' " +
                           "AND last_access_time < DATE_SUB(NOW(), INTERVAL 30 MINUTE) " +
                           "AND last_access_time >= DATE_SUB(NOW(), INTERVAL 2 HOUR)";
            Long idleUsers = jdbcTemplate.queryForObject(idleSql, Long.class);
            result.put("totalIdle", idleUsers != null ? idleUsers : 0L);

            // 今日登录数
            String todayLoginSql = "SELECT COUNT(DISTINCT user_id) FROM system_online_user " +
                                 "WHERE DATE(login_time) = CURDATE()";
            Long todayLogins = jdbcTemplate.queryForObject(todayLoginSql, Long.class);
            result.put("todayLogins", todayLogins != null ? todayLogins : 0L);

            // 平均会话时长（分钟）
            String avgSessionSql = "SELECT AVG(TIMESTAMPDIFF(MINUTE, login_time, COALESCE(last_access_time, NOW()))) " +
                                 "FROM system_online_user WHERE status = 'ONLINE'";
            BigDecimal avgSessionTime = jdbcTemplate.queryForObject(avgSessionSql, BigDecimal.class);
            result.put("avgSessionTime", avgSessionTime != null ? avgSessionTime.intValue() : 0);

        } catch (Exception e) {
            log.error("获取在线用户统计失败: {}", e.getMessage(), e);
            result.put("totalOnline", 0L);
            result.put("totalIdle", 0L);
            result.put("todayLogins", 0L);
            result.put("avgSessionTime", 0);
        }
        return result;
    }

    @Override
    public Map<String, Object> getExceptionLogStatistics() {
        Map<String, Object> result = new HashMap<>();
        try {
            // 今日错误数（由于表中没有log_level字段，统计所有异常记录）
            String todayErrorSql = "SELECT COUNT(*) FROM system_exception_log " +
                                 "WHERE DATE(create_time) = CURDATE()";
            Long todayErrors = jdbcTemplate.queryForObject(todayErrorSql, Long.class);
            result.put("todayErrors", todayErrors != null ? todayErrors : 0L);

            // 今日警告数（由于表中没有log_level字段，暂时设为0）
            // String todayWarnSql = "SELECT COUNT(*) FROM system_exception_log " +
            //                     "WHERE DATE(create_time) = CURDATE() AND log_level = 'WARN'";
            // Long todayWarnings = jdbcTemplate.queryForObject(todayWarnSql, Long.class);
            result.put("todayWarnings", 0L);

            // 最近1小时错误数（由于表中没有log_level字段，统计所有异常记录）
            String recentErrorSql = "SELECT COUNT(*) FROM system_exception_log " +
                                  "WHERE create_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)";
            Long recentErrors = jdbcTemplate.queryForObject(recentErrorSql, Long.class);
            result.put("recentErrors", recentErrors != null ? recentErrors : 0L);

            // 错误率（基于访问日志中的错误响应）
            String errorRateSql = "SELECT " +
                                "COUNT(CASE WHEN response_status >= 400 THEN 1 END) * 100.0 / COUNT(*) as error_rate " +
                                "FROM system_access_log WHERE DATE(access_time) = CURDATE()";
            BigDecimal errorRate = jdbcTemplate.queryForObject(errorRateSql, BigDecimal.class);
            result.put("errorRate", errorRate != null ? errorRate.setScale(2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);

        } catch (Exception e) {
            log.error("获取异常日志统计失败: {}", e.getMessage(), e);
            result.put("todayErrors", 0L);
            result.put("todayWarnings", 0L);
            result.put("recentErrors", 0L);
            result.put("errorRate", BigDecimal.ZERO);
        }
        return result;
    }
}
