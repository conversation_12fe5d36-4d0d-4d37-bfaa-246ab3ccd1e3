package com.haoys.user.domain.vo.participant;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ProjectTesteeVisitPercentVo {
    
    @ApiModelProperty(value = "访视完成状态 1-未录入 2-录入中 3-已完成")
    private String complateVisitStatus = "1";
    
    @ApiModelProperty(value = "访视完成百分比")
    private String complateVisitPercent = "1";
    
    @ApiModelProperty(value = "表单完成状态 1-未录入 2-录入中 3-已完成")
    private String complateFormStatus = "1";
    
}
