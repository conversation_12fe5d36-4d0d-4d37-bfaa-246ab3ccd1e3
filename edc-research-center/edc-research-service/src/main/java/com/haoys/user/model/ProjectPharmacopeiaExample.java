package com.haoys.user.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProjectPharmacopeiaExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectPharmacopeiaExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andAliasIsNull() {
            addCriterion("alias is null");
            return (Criteria) this;
        }

        public Criteria andAliasIsNotNull() {
            addCriterion("alias is not null");
            return (Criteria) this;
        }

        public Criteria andAliasEqualTo(String value) {
            addCriterion("alias =", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasNotEqualTo(String value) {
            addCriterion("alias <>", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasGreaterThan(String value) {
            addCriterion("alias >", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasGreaterThanOrEqualTo(String value) {
            addCriterion("alias >=", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasLessThan(String value) {
            addCriterion("alias <", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasLessThanOrEqualTo(String value) {
            addCriterion("alias <=", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasLike(String value) {
            addCriterion("alias like", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasNotLike(String value) {
            addCriterion("alias not like", value, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasIn(List<String> values) {
            addCriterion("alias in", values, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasNotIn(List<String> values) {
            addCriterion("alias not in", values, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasBetween(String value1, String value2) {
            addCriterion("alias between", value1, value2, "alias");
            return (Criteria) this;
        }

        public Criteria andAliasNotBetween(String value1, String value2) {
            addCriterion("alias not between", value1, value2, "alias");
            return (Criteria) this;
        }

        public Criteria andNatureFlavIsNull() {
            addCriterion("nature_flav is null");
            return (Criteria) this;
        }

        public Criteria andNatureFlavIsNotNull() {
            addCriterion("nature_flav is not null");
            return (Criteria) this;
        }

        public Criteria andNatureFlavEqualTo(String value) {
            addCriterion("nature_flav =", value, "natureFlav");
            return (Criteria) this;
        }

        public Criteria andNatureFlavNotEqualTo(String value) {
            addCriterion("nature_flav <>", value, "natureFlav");
            return (Criteria) this;
        }

        public Criteria andNatureFlavGreaterThan(String value) {
            addCriterion("nature_flav >", value, "natureFlav");
            return (Criteria) this;
        }

        public Criteria andNatureFlavGreaterThanOrEqualTo(String value) {
            addCriterion("nature_flav >=", value, "natureFlav");
            return (Criteria) this;
        }

        public Criteria andNatureFlavLessThan(String value) {
            addCriterion("nature_flav <", value, "natureFlav");
            return (Criteria) this;
        }

        public Criteria andNatureFlavLessThanOrEqualTo(String value) {
            addCriterion("nature_flav <=", value, "natureFlav");
            return (Criteria) this;
        }

        public Criteria andNatureFlavLike(String value) {
            addCriterion("nature_flav like", value, "natureFlav");
            return (Criteria) this;
        }

        public Criteria andNatureFlavNotLike(String value) {
            addCriterion("nature_flav not like", value, "natureFlav");
            return (Criteria) this;
        }

        public Criteria andNatureFlavIn(List<String> values) {
            addCriterion("nature_flav in", values, "natureFlav");
            return (Criteria) this;
        }

        public Criteria andNatureFlavNotIn(List<String> values) {
            addCriterion("nature_flav not in", values, "natureFlav");
            return (Criteria) this;
        }

        public Criteria andNatureFlavBetween(String value1, String value2) {
            addCriterion("nature_flav between", value1, value2, "natureFlav");
            return (Criteria) this;
        }

        public Criteria andNatureFlavNotBetween(String value1, String value2) {
            addCriterion("nature_flav not between", value1, value2, "natureFlav");
            return (Criteria) this;
        }

        public Criteria andReturnEssenceIsNull() {
            addCriterion("return_essence is null");
            return (Criteria) this;
        }

        public Criteria andReturnEssenceIsNotNull() {
            addCriterion("return_essence is not null");
            return (Criteria) this;
        }

        public Criteria andReturnEssenceEqualTo(String value) {
            addCriterion("return_essence =", value, "returnEssence");
            return (Criteria) this;
        }

        public Criteria andReturnEssenceNotEqualTo(String value) {
            addCriterion("return_essence <>", value, "returnEssence");
            return (Criteria) this;
        }

        public Criteria andReturnEssenceGreaterThan(String value) {
            addCriterion("return_essence >", value, "returnEssence");
            return (Criteria) this;
        }

        public Criteria andReturnEssenceGreaterThanOrEqualTo(String value) {
            addCriterion("return_essence >=", value, "returnEssence");
            return (Criteria) this;
        }

        public Criteria andReturnEssenceLessThan(String value) {
            addCriterion("return_essence <", value, "returnEssence");
            return (Criteria) this;
        }

        public Criteria andReturnEssenceLessThanOrEqualTo(String value) {
            addCriterion("return_essence <=", value, "returnEssence");
            return (Criteria) this;
        }

        public Criteria andReturnEssenceLike(String value) {
            addCriterion("return_essence like", value, "returnEssence");
            return (Criteria) this;
        }

        public Criteria andReturnEssenceNotLike(String value) {
            addCriterion("return_essence not like", value, "returnEssence");
            return (Criteria) this;
        }

        public Criteria andReturnEssenceIn(List<String> values) {
            addCriterion("return_essence in", values, "returnEssence");
            return (Criteria) this;
        }

        public Criteria andReturnEssenceNotIn(List<String> values) {
            addCriterion("return_essence not in", values, "returnEssence");
            return (Criteria) this;
        }

        public Criteria andReturnEssenceBetween(String value1, String value2) {
            addCriterion("return_essence between", value1, value2, "returnEssence");
            return (Criteria) this;
        }

        public Criteria andReturnEssenceNotBetween(String value1, String value2) {
            addCriterion("return_essence not between", value1, value2, "returnEssence");
            return (Criteria) this;
        }

        public Criteria andEfficacyIsNull() {
            addCriterion("efficacy is null");
            return (Criteria) this;
        }

        public Criteria andEfficacyIsNotNull() {
            addCriterion("efficacy is not null");
            return (Criteria) this;
        }

        public Criteria andEfficacyEqualTo(String value) {
            addCriterion("efficacy =", value, "efficacy");
            return (Criteria) this;
        }

        public Criteria andEfficacyNotEqualTo(String value) {
            addCriterion("efficacy <>", value, "efficacy");
            return (Criteria) this;
        }

        public Criteria andEfficacyGreaterThan(String value) {
            addCriterion("efficacy >", value, "efficacy");
            return (Criteria) this;
        }

        public Criteria andEfficacyGreaterThanOrEqualTo(String value) {
            addCriterion("efficacy >=", value, "efficacy");
            return (Criteria) this;
        }

        public Criteria andEfficacyLessThan(String value) {
            addCriterion("efficacy <", value, "efficacy");
            return (Criteria) this;
        }

        public Criteria andEfficacyLessThanOrEqualTo(String value) {
            addCriterion("efficacy <=", value, "efficacy");
            return (Criteria) this;
        }

        public Criteria andEfficacyLike(String value) {
            addCriterion("efficacy like", value, "efficacy");
            return (Criteria) this;
        }

        public Criteria andEfficacyNotLike(String value) {
            addCriterion("efficacy not like", value, "efficacy");
            return (Criteria) this;
        }

        public Criteria andEfficacyIn(List<String> values) {
            addCriterion("efficacy in", values, "efficacy");
            return (Criteria) this;
        }

        public Criteria andEfficacyNotIn(List<String> values) {
            addCriterion("efficacy not in", values, "efficacy");
            return (Criteria) this;
        }

        public Criteria andEfficacyBetween(String value1, String value2) {
            addCriterion("efficacy between", value1, value2, "efficacy");
            return (Criteria) this;
        }

        public Criteria andEfficacyNotBetween(String value1, String value2) {
            addCriterion("efficacy not between", value1, value2, "efficacy");
            return (Criteria) this;
        }

        public Criteria andIndicationIsNull() {
            addCriterion("indication is null");
            return (Criteria) this;
        }

        public Criteria andIndicationIsNotNull() {
            addCriterion("indication is not null");
            return (Criteria) this;
        }

        public Criteria andIndicationEqualTo(String value) {
            addCriterion("indication =", value, "indication");
            return (Criteria) this;
        }

        public Criteria andIndicationNotEqualTo(String value) {
            addCriterion("indication <>", value, "indication");
            return (Criteria) this;
        }

        public Criteria andIndicationGreaterThan(String value) {
            addCriterion("indication >", value, "indication");
            return (Criteria) this;
        }

        public Criteria andIndicationGreaterThanOrEqualTo(String value) {
            addCriterion("indication >=", value, "indication");
            return (Criteria) this;
        }

        public Criteria andIndicationLessThan(String value) {
            addCriterion("indication <", value, "indication");
            return (Criteria) this;
        }

        public Criteria andIndicationLessThanOrEqualTo(String value) {
            addCriterion("indication <=", value, "indication");
            return (Criteria) this;
        }

        public Criteria andIndicationLike(String value) {
            addCriterion("indication like", value, "indication");
            return (Criteria) this;
        }

        public Criteria andIndicationNotLike(String value) {
            addCriterion("indication not like", value, "indication");
            return (Criteria) this;
        }

        public Criteria andIndicationIn(List<String> values) {
            addCriterion("indication in", values, "indication");
            return (Criteria) this;
        }

        public Criteria andIndicationNotIn(List<String> values) {
            addCriterion("indication not in", values, "indication");
            return (Criteria) this;
        }

        public Criteria andIndicationBetween(String value1, String value2) {
            addCriterion("indication between", value1, value2, "indication");
            return (Criteria) this;
        }

        public Criteria andIndicationNotBetween(String value1, String value2) {
            addCriterion("indication not between", value1, value2, "indication");
            return (Criteria) this;
        }

        public Criteria andContentIsNull() {
            addCriterion("content is null");
            return (Criteria) this;
        }

        public Criteria andContentIsNotNull() {
            addCriterion("content is not null");
            return (Criteria) this;
        }

        public Criteria andContentEqualTo(String value) {
            addCriterion("content =", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotEqualTo(String value) {
            addCriterion("content <>", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentGreaterThan(String value) {
            addCriterion("content >", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentGreaterThanOrEqualTo(String value) {
            addCriterion("content >=", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLessThan(String value) {
            addCriterion("content <", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLessThanOrEqualTo(String value) {
            addCriterion("content <=", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLike(String value) {
            addCriterion("content like", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotLike(String value) {
            addCriterion("content not like", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentIn(List<String> values) {
            addCriterion("content in", values, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotIn(List<String> values) {
            addCriterion("content not in", values, "content");
            return (Criteria) this;
        }

        public Criteria andContentBetween(String value1, String value2) {
            addCriterion("content between", value1, value2, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotBetween(String value1, String value2) {
            addCriterion("content not between", value1, value2, "content");
            return (Criteria) this;
        }

        public Criteria andAttentionIsNull() {
            addCriterion("attention is null");
            return (Criteria) this;
        }

        public Criteria andAttentionIsNotNull() {
            addCriterion("attention is not null");
            return (Criteria) this;
        }

        public Criteria andAttentionEqualTo(String value) {
            addCriterion("attention =", value, "attention");
            return (Criteria) this;
        }

        public Criteria andAttentionNotEqualTo(String value) {
            addCriterion("attention <>", value, "attention");
            return (Criteria) this;
        }

        public Criteria andAttentionGreaterThan(String value) {
            addCriterion("attention >", value, "attention");
            return (Criteria) this;
        }

        public Criteria andAttentionGreaterThanOrEqualTo(String value) {
            addCriterion("attention >=", value, "attention");
            return (Criteria) this;
        }

        public Criteria andAttentionLessThan(String value) {
            addCriterion("attention <", value, "attention");
            return (Criteria) this;
        }

        public Criteria andAttentionLessThanOrEqualTo(String value) {
            addCriterion("attention <=", value, "attention");
            return (Criteria) this;
        }

        public Criteria andAttentionLike(String value) {
            addCriterion("attention like", value, "attention");
            return (Criteria) this;
        }

        public Criteria andAttentionNotLike(String value) {
            addCriterion("attention not like", value, "attention");
            return (Criteria) this;
        }

        public Criteria andAttentionIn(List<String> values) {
            addCriterion("attention in", values, "attention");
            return (Criteria) this;
        }

        public Criteria andAttentionNotIn(List<String> values) {
            addCriterion("attention not in", values, "attention");
            return (Criteria) this;
        }

        public Criteria andAttentionBetween(String value1, String value2) {
            addCriterion("attention between", value1, value2, "attention");
            return (Criteria) this;
        }

        public Criteria andAttentionNotBetween(String value1, String value2) {
            addCriterion("attention not between", value1, value2, "attention");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andMedicinalPropertyIsNull() {
            addCriterion("medicinal_property is null");
            return (Criteria) this;
        }

        public Criteria andMedicinalPropertyIsNotNull() {
            addCriterion("medicinal_property is not null");
            return (Criteria) this;
        }

        public Criteria andMedicinalPropertyEqualTo(String value) {
            addCriterion("medicinal_property =", value, "medicinalProperty");
            return (Criteria) this;
        }

        public Criteria andMedicinalPropertyNotEqualTo(String value) {
            addCriterion("medicinal_property <>", value, "medicinalProperty");
            return (Criteria) this;
        }

        public Criteria andMedicinalPropertyGreaterThan(String value) {
            addCriterion("medicinal_property >", value, "medicinalProperty");
            return (Criteria) this;
        }

        public Criteria andMedicinalPropertyGreaterThanOrEqualTo(String value) {
            addCriterion("medicinal_property >=", value, "medicinalProperty");
            return (Criteria) this;
        }

        public Criteria andMedicinalPropertyLessThan(String value) {
            addCriterion("medicinal_property <", value, "medicinalProperty");
            return (Criteria) this;
        }

        public Criteria andMedicinalPropertyLessThanOrEqualTo(String value) {
            addCriterion("medicinal_property <=", value, "medicinalProperty");
            return (Criteria) this;
        }

        public Criteria andMedicinalPropertyLike(String value) {
            addCriterion("medicinal_property like", value, "medicinalProperty");
            return (Criteria) this;
        }

        public Criteria andMedicinalPropertyNotLike(String value) {
            addCriterion("medicinal_property not like", value, "medicinalProperty");
            return (Criteria) this;
        }

        public Criteria andMedicinalPropertyIn(List<String> values) {
            addCriterion("medicinal_property in", values, "medicinalProperty");
            return (Criteria) this;
        }

        public Criteria andMedicinalPropertyNotIn(List<String> values) {
            addCriterion("medicinal_property not in", values, "medicinalProperty");
            return (Criteria) this;
        }

        public Criteria andMedicinalPropertyBetween(String value1, String value2) {
            addCriterion("medicinal_property between", value1, value2, "medicinalProperty");
            return (Criteria) this;
        }

        public Criteria andMedicinalPropertyNotBetween(String value1, String value2) {
            addCriterion("medicinal_property not between", value1, value2, "medicinalProperty");
            return (Criteria) this;
        }

        public Criteria andMedicinalTasteIsNull() {
            addCriterion("medicinal_taste is null");
            return (Criteria) this;
        }

        public Criteria andMedicinalTasteIsNotNull() {
            addCriterion("medicinal_taste is not null");
            return (Criteria) this;
        }

        public Criteria andMedicinalTasteEqualTo(String value) {
            addCriterion("medicinal_taste =", value, "medicinalTaste");
            return (Criteria) this;
        }

        public Criteria andMedicinalTasteNotEqualTo(String value) {
            addCriterion("medicinal_taste <>", value, "medicinalTaste");
            return (Criteria) this;
        }

        public Criteria andMedicinalTasteGreaterThan(String value) {
            addCriterion("medicinal_taste >", value, "medicinalTaste");
            return (Criteria) this;
        }

        public Criteria andMedicinalTasteGreaterThanOrEqualTo(String value) {
            addCriterion("medicinal_taste >=", value, "medicinalTaste");
            return (Criteria) this;
        }

        public Criteria andMedicinalTasteLessThan(String value) {
            addCriterion("medicinal_taste <", value, "medicinalTaste");
            return (Criteria) this;
        }

        public Criteria andMedicinalTasteLessThanOrEqualTo(String value) {
            addCriterion("medicinal_taste <=", value, "medicinalTaste");
            return (Criteria) this;
        }

        public Criteria andMedicinalTasteLike(String value) {
            addCriterion("medicinal_taste like", value, "medicinalTaste");
            return (Criteria) this;
        }

        public Criteria andMedicinalTasteNotLike(String value) {
            addCriterion("medicinal_taste not like", value, "medicinalTaste");
            return (Criteria) this;
        }

        public Criteria andMedicinalTasteIn(List<String> values) {
            addCriterion("medicinal_taste in", values, "medicinalTaste");
            return (Criteria) this;
        }

        public Criteria andMedicinalTasteNotIn(List<String> values) {
            addCriterion("medicinal_taste not in", values, "medicinalTaste");
            return (Criteria) this;
        }

        public Criteria andMedicinalTasteBetween(String value1, String value2) {
            addCriterion("medicinal_taste between", value1, value2, "medicinalTaste");
            return (Criteria) this;
        }

        public Criteria andMedicinalTasteNotBetween(String value1, String value2) {
            addCriterion("medicinal_taste not between", value1, value2, "medicinalTaste");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}