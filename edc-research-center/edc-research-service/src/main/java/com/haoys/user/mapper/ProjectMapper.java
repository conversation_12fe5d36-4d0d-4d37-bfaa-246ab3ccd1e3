package com.haoys.user.mapper;

import com.haoys.user.domain.vo.project.ProjectVo;
import com.haoys.user.model.Project;
import com.haoys.user.model.ProjectExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ProjectMapper {
    long countByExample(ProjectExample example);

    int deleteByExample(ProjectExample example);

    int deleteByPrimaryKey(Long id);

    int insert(Project record);

    int insertSelective(Project record);

    List<Project> selectByExample(ProjectExample example);

    Project selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") Project record, @Param("example") ProjectExample example);

    int updateByExample(@Param("record") Project record, @Param("example") ProjectExample example);

    int updateByPrimaryKeySelective(Project record);

    int updateByPrimaryKey(Project record);

    /**
     * 项目广场列表
     * @param params
     * @return
     */
    List<ProjectVo> getPublicProjectListForPage(Map<String, Object> params);

    /**
     * 工作台-我的项目列表
     * @param params
     * @return
     */
    List<ProjectVo> getOwnerProjectListForPage(Map<String, Object> params);

    /**
     * 后台项目管理列表
     * @param params
     * @return
     */
    List<ProjectVo> getProjectManageListForPage(Map<String, Object> params);

    Project getProjectBaseInfoByName(String name, String systemTenantId, String systemPlatformId);

    Project getProjectBaseInfoByCode(String code, String systemTenantId, String systemPlatformId);

    List<ProjectVo> getOwnerProjectListForH5(String userId);
    
    List<ProjectVo> getProjectList(Map<String, Object> params);
    
    Project getEnableProjectBaseInfo(String loginTenantId, String loginPlatformId);
}