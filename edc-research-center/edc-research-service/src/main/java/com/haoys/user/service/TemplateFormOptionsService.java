package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.param.crf.TemplateFormGroupFieldOptionParam;
import com.haoys.user.domain.vo.ecrf.TemplateFormFieldOptionVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormGroupFieldOptionVo;

import java.util.List;

public interface TemplateFormOptionsService {


    CommonPage<TemplateFormGroupFieldOptionVo> getTemplateFormGroupFieldOptionListForPage(String groupName, Integer pageNum, Integer pageSize);


    CustomResult saveTemplateFormGroupFieldOption(TemplateFormGroupFieldOptionParam templateFormGroupFieldOptionParam);

    CustomResult deleteFormGroupFieldOption(String parentId, String id, String updateUserId);

    List<TemplateFormGroupFieldOptionVo> getFormGroupFieldOptionByParentId(String parentId);

    /**
     * 根据父级id查询表单字段选项集合-CRF设置变量时使用
     * @param parentId
     * @return
     */
    TemplateFormFieldOptionVo getFormFieldOptionByParentId(String parentId);
}
