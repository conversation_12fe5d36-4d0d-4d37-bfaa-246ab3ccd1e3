package com.haoys.user.mapper;

import com.haoys.user.model.TemplateFormDetailImage;
import com.haoys.user.model.TemplateFormDetailImageExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TemplateFormDetailImageMapper {
    long countByExample(TemplateFormDetailImageExample example);

    int deleteByExample(TemplateFormDetailImageExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TemplateFormDetailImage record);

    int insertSelective(TemplateFormDetailImage record);

    List<TemplateFormDetailImage> selectByExample(TemplateFormDetailImageExample example);

    TemplateFormDetailImage selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TemplateFormDetailImage record, @Param("example") TemplateFormDetailImageExample example);

    int updateByExample(@Param("record") TemplateFormDetailImage record, @Param("example") TemplateFormDetailImageExample example);

    int updateByPrimaryKeySelective(TemplateFormDetailImage record);

    int updateByPrimaryKey(TemplateFormDetailImage record);
}