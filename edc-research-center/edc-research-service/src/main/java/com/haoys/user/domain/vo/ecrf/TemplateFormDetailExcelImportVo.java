package com.haoys.user.domain.vo.ecrf;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
public class TemplateFormDetailExcelImportVo implements Serializable {

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @ApiModelProperty(value = "表单id")
    private Long formId;

    @ApiModelProperty(value = "表单详情id")
    private Long formDetailId;

    @ApiModelProperty(value = "表单组件类型")
    private String type;

    @ApiModelProperty(value = "表单标签名称")
    private String label;

    @ApiModelProperty(value = "组件是否隐藏")
    private Boolean hidden;

    @ApiModelProperty(value = "组件是否必填")
    private Boolean required;

    @ApiModelProperty(value = "输入提示文字")
    private String placeholder;

    @ApiModelProperty(value = "变量图标")
    private String icon;

    @ApiModelProperty(value = "字段key")
    private String key;

    @ApiModelProperty(value = "验证规则")
    private String rules;

    @ApiModelProperty(value = "变量下拉框内容")
    private String options;

    @ApiModelProperty(value = "是否为定制表格")
    private Boolean customTable;

    @ApiModelProperty(value = "组件默认值")
    private String defaultValue;

    @ApiModelProperty(value = "量表打分设置")
    private BigDecimal scoreValue;

    @ApiModelProperty(value = "计量单位")
    private String unitValue;

    @ApiModelProperty(value = "字段样式")
    private String styleData;

    @ApiModelProperty(value = "扩展属性1")
    private String extData1;

    @ApiModelProperty(value = "扩展属性2")
    private String extData2;

    @ApiModelProperty(value = "扩展属性3")
    private String extData3;

    @ApiModelProperty(value = "扩展属性4")
    private String extData4;

    @ApiModelProperty(value = "扩展属性5")
    private String extData5;

    @ApiModelProperty(value = "扩展字段")
    private String expand;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "数据状态")
    private String status;
}
