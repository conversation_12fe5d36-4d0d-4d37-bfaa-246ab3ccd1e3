package com.haoys.user.domain.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;


@Data
public class PrescriptionAnalysisParam {


    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "分析批次名称")
    private String title;

    @ApiModelProperty(value = "分析批次记录code-系统设置")
    private String batchCode;

    @ApiModelProperty(value = "操作人")
    private String createUserId;

    @ApiModelProperty(value = "诊断分析结果")
    private String diagnosticResult;

    @ApiModelProperty(value = "药方分析结果")
    private String prescriptionResult;

    @ApiModelProperty(value = "数据状态 0-正常 1-封存")
    private Boolean sealFlag;

    @ApiModelProperty(value = "平台id-指向项目id")
    private String platformId;

    @ApiModelProperty(value = "样本数据集合")
    private List<ProjectAnalysisRecordParam> sampleList = new ArrayList<>();

    @Data
    public static class ProjectAnalysisRecordParam {

        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "主键")
        private Long id;

        @ApiModelProperty(value = "数据分析批次号code-系统设置")
        private String batchCode;

        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "样本数据id")
        private Long sampleId;

        @ApiModelProperty(value = "患者姓名")
        private String sampleName;

    }


}
