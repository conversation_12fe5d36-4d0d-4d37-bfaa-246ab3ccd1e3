package com.haoys.user.mapper;

import com.haoys.user.domain.entity.AiChatSession;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI聊天会话Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Mapper
public interface AiChatSessionMapper {
    
    /**
     * 插入会话记录
     */
    int insert(AiChatSession session);
    
    /**
     * 根据ID查询会话
     */
    AiChatSession selectById(@Param("id") Long id);
    
    /**
     * 根据会话ID查询会话
     */
    AiChatSession selectBySessionId(@Param("sessionId") String sessionId);
    
    /**
     * 更新会话信息
     */
    int updateBySessionId(AiChatSession session);
    
    /**
     * 根据用户ID查询会话列表
     */
    List<AiChatSession> selectByUserId(@Param("userId") String userId, 
                                       @Param("status") Integer status,
                                       @Param("offset") Integer offset, 
                                       @Param("limit") Integer limit);
    
    /**
     * 统计用户会话数量
     */
    int countByUserId(@Param("userId") String userId, @Param("status") Integer status);
    
    /**
     * 更新会话状态
     */
    int updateStatus(@Param("sessionId") String sessionId, @Param("status") Integer status);
    
    /**
     * 更新会话Token和成本统计
     */
    int updateTokensAndCost(@Param("sessionId") String sessionId, 
                           @Param("tokens") Long tokens, 
                           @Param("cost") java.math.BigDecimal cost);
    
    /**
     * 更新消息数量和最后消息时间
     */
    int updateMessageCountAndTime(@Param("sessionId") String sessionId, 
                                 @Param("messageCount") Integer messageCount,
                                 @Param("lastMessageTime") java.util.Date lastMessageTime);
    
    /**
     * 删除会话
     */
    int deleteBySessionId(@Param("sessionId") String sessionId);
    
    /**
     * 批量删除超时会话
     */
    int deleteTimeoutSessions(@Param("timeoutMinutes") Integer timeoutMinutes);
}
