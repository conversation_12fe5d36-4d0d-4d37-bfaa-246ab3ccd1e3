package com.haoys.user.mapper;

import com.haoys.user.model.SystemFileInfo;
import com.haoys.user.model.SystemFileInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SystemFileInfoMapper {
    long countByExample(SystemFileInfoExample example);

    int deleteByExample(SystemFileInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SystemFileInfo record);

    int insertSelective(SystemFileInfo record);

    List<SystemFileInfo> selectByExample(SystemFileInfoExample example);

    SystemFileInfo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SystemFileInfo record, @Param("example") SystemFileInfoExample example);

    int updateByExample(@Param("record") SystemFileInfo record, @Param("example") SystemFileInfoExample example);

    int updateByPrimaryKeySelective(SystemFileInfo record);

    int updateByPrimaryKey(SystemFileInfo record);
}