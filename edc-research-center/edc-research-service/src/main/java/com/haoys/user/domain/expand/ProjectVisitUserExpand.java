package com.haoys.user.domain.expand;

import com.haoys.user.model.ProjectResearchersInfo;
import com.haoys.user.model.ProjectVisitUser;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectVisitUserExpand extends ProjectResearchersInfo {
    
    
    @ApiModelProperty(value = "项目id")
    private Long projectId;
    
    @ApiModelProperty(value = "参与者id")
    private Long testeeId;
    
    @ApiModelProperty(value = "参与者配置信息")
    private String testeeConfig;
    
    @ApiModelProperty(value = "参与者编号")
    private String testeeCode;
    
    @ApiModelProperty(value = "就诊卡号")
    private String visitCardNo;
    
    @ApiModelProperty(value = "所属研究中心id")
    private String ownerOrgId;
    
    @ApiModelProperty(value = "所属研究中心名称")
    private String ownerOrgName;
    
    @ApiModelProperty(value = "主管医生id")
    private String ownerDoctorId;
    
    @ApiModelProperty(value = "知情日期")
    private Date informedDate;
    
    @ApiModelProperty(value = "所属表单")
    private Long resourceFormId;
    
    @ApiModelProperty(value = "是否为医生自建病历 1-医生创建 0-患者端创建")
    private Boolean selfRecord;
    
    @ApiModelProperty(value = "项目绑定状态 1-绑定 0-未绑定")
    private Boolean bindResult;
    
    @ApiModelProperty(value = "患者绑定时间")
    private Date bindTime;
    
    @ApiModelProperty(value = "参与者是否需要审核 1-是 0-否")
    private Boolean reviewFlag;
    
    @ApiModelProperty(value = "审核状态")
    private String reviewStatus;
    
    @ApiModelProperty(value = "审核人")
    private String reviewUserId;
    
    @ApiModelProperty(value = "审核时间")
    private Date reviewTime;
    
    @ApiModelProperty(value = "项目患者来源 1-医生端创建 2-患者自建 3-其他来源")
    private String bindResource;
    
    @ApiModelProperty(value = "参与者研究状态 参照字典说明")
    private String researchStatus;
    
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    @ApiModelProperty(value = "创建人")
    private String createUserId;
    
    @ApiModelProperty(value = "姓名")
    private String realName;
    
    private String acronym;
    
    private String projectResearchUserId;
    
}
