package com.haoys.user.domain.vo.patient;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


@Data
public class PatientTaskFormValueVo implements Serializable {

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "方案id")
    private String planId;

    @ApiModelProperty(value = "方案名称")
    private String planName;

    @ApiModelProperty(value = "列表返回结果说明")
    private String viewTaskResult = "0";

    @ApiModelProperty(value = "所有任务表单录入是否完成")
    private Boolean finishAllFormState = false;

    private List<PatientTaskFormVo> dataList = new ArrayList<>();

    @Data
    public static class PatientTaskFormVo {

        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "任务id")
        private Long taskId;
        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "访视id")
        private Long visitId;
        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "表单id")
        private Long formId;

        @ApiModelProperty(value = "任务名称")
        private String taskName;
        @ApiModelProperty(value = "任务类型Id")
        private String taskTypeId;
        @ApiModelProperty(value = "任务类型名称")
        private String taskTypeViewName;
        @ApiModelProperty(value = "任务备注信息")
        private String remark;
        @ApiModelProperty(value = "特殊说明标识")
        private String identification = "";
        @ApiModelProperty(value = "是否完成表单录入")
        private Boolean finishFormState = false;

        @ApiModelProperty(value = "表单完成状态")
        private String complateStatus = "";

    }

}
