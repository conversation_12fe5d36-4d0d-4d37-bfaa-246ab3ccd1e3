package com.haoys.user.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TemplateFormDvpRule implements Serializable {
    @ApiModelProperty(value = "主键")
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    private Long id;
    @ApiModelProperty(value = "逻辑核查编号")
    private String code;

    @ApiModelProperty(value = "逻辑核查名称")
    private String checkName;

    @ApiModelProperty(value = "项目id")
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    private Long projectId;

    @ApiModelProperty(value = "方案id")
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    private Long planId;

    @ApiModelProperty(value = "访视id")
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    private Long visitId;

    @ApiModelProperty(value = "表单id")
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    private Long formId;

    @ApiModelProperty(value = "字段组id")
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    private Long groupId;

    @ApiModelProperty(value = "变量id")
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    private Long formDetailId;

    @ApiModelProperty(value = "表格变量id")
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    private Long formTableId;

    @ApiModelProperty(value = "触发位置")
    private String variableName;

    @ApiModelProperty(value = "相关变量")
    private String formRelationId;

    @ApiModelProperty(value = "是否为定制表格 1-是 0-否")
    private Boolean customTable;

    @ApiModelProperty(value = "定制表格行标记")
    private String tableRowIndex;

    @ApiModelProperty(value = "质疑类型参照字典")
    private String queryType;

    @ApiModelProperty(value = "质疑访视 required、date等")
    private String queryMethod;

    @ApiModelProperty(value = "函数表达式")
    private String functionValue;

    @ApiModelProperty(value = "运算表达式")
    private String arithmeticValue;

    @ApiModelProperty(value = "逻辑表达式")
    private String conditionValue;

    @ApiModelProperty(value = "目标值")
    private String targetValue;

    @ApiModelProperty(value = "触发类型：1.质疑 2.计算")
    private String ruleType;

    @ApiModelProperty(value = "质疑内容")
    private String content;

    @ApiModelProperty(value = "启用/停用")
    private Boolean enabled;

    @ApiModelProperty(value = "数据状态")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建人")
    private String createUserId;

    @ApiModelProperty(value = "修改人")
    private String updateUserId;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

    @ApiModelProperty(value = "触发位置前端位置回显")
    private String selectedOptions;



}
