package com.haoys.user.service;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.domain.vo.project.ProjectApplyUserVo;
import com.haoys.user.model.ProjectApplyUser;
import com.haoys.user.model.ProjectApplyUserExample;

import java.util.List;

public interface ProjectApplyUserService {

    List<ProjectApplyUser> getQueryProjectList(Long orgId, String userId);

    boolean getProjectUserResult(String projectId, String userId);

    void insert(ProjectApplyUser projectApplyUser);

    ProjectApplyUser selectByPrimaryKey(long id);

    List<ProjectApplyUser> selectByExample(ProjectApplyUserExample example);

    List<ProjectApplyUserVo> getUserApplyListForPage(String projectId, String applyUserName, String projectCreateUser, String orgId, String startDate, String endDate, String status);

    void updateByPrimaryKeySelective(ProjectApplyUser projectApplyUser);

    /**
     * 申请加入项目
     * @param projectId 项目id
     * @return 申请加入结果
     */
    CommonResult<Object> apply(Long projectId);

    /**
     * 获取申请列表
     * @return 申请列表
     */
    CommonResult<Object> applyList();

    /**
     * 获取审批列表
     * @return 审批列表
     */
    CommonResult<List<ProjectApplyUser>> auditList();

    /**
     * 审批
     * @param applyId 申请记录id
     * @param applyStatus 审批状态
     * @return 审批结果
     */
    CommonResult<Object> audit(Long applyId, String applyStatus);
}
