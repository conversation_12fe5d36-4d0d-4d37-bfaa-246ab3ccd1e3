package com.haoys.user.service.impl;


import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.enums.PatientTaskTypeEnum;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.domain.param.project.ProjectPatientTaskParam;
import com.haoys.user.domain.vo.project.ProjectTaskVo;
import com.haoys.user.domain.vo.project.SystemUserInfoExtendVo;
import com.haoys.user.mapper.ProjectPatientTaskMapper;
import com.haoys.user.mapper.ProjectPatientTaskVariableMapper;
import com.haoys.user.model.ProjectPatientPlanDetail;
import com.haoys.user.model.ProjectPatientTask;
import com.haoys.user.model.ProjectPatientTaskExample;
import com.haoys.user.model.ProjectPatientTaskVariable;
import com.haoys.user.model.ProjectPatientTaskVariableExample;
import com.haoys.user.service.ProjectPlanManageService;
import com.haoys.user.service.ProjectTaskManageService;
import com.haoys.user.service.SystemUserInfoService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class ProjectTaskManageServiceImpl extends BaseService implements ProjectTaskManageService{

    private final ProjectPatientTaskMapper projectPatientTaskMapper;
    private final ProjectPatientTaskVariableMapper projectPatientTaskVariableMapper;
    private final SystemUserInfoService systemUserInfoService;
    private final ProjectPlanManageService projectPlanManageService;

    @Override
    public CommonPage<ProjectTaskVo> getProjectTaskListForPage(String projectId, String taskName, String createUserId, Integer pageSize, Integer pageNum) {
        List<ProjectTaskVo> dataList = new ArrayList<>();
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        ProjectPatientTaskExample example = new ProjectPatientTaskExample();
        ProjectPatientTaskExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        //criteria.andCreateUserEqualTo(createUserId);
        criteria.andStatusEqualTo("0");
        example.setOrderByClause("create_time desc");
        List<ProjectPatientTask> projectPatientTaskList = projectPatientTaskMapper.selectByExample(example);
        for (ProjectPatientTask projectPatientTask : projectPatientTaskList) {
            ProjectTaskVo projectTaskVo = new ProjectTaskVo();
            BeanUtils.copyProperties(projectPatientTask, projectTaskVo);
            SystemUserInfoExtendVo userInfo = systemUserInfoService.getSystemUserInfoByUserId(projectPatientTask.getCreateUser());
            if(userInfo != null){
                projectTaskVo.setCreateUserName(userInfo.getRealName());
            }
            List<ProjectTaskVo.ProjectPatientTaskVariableVo> varDataList = new ArrayList<>();
            List<ProjectPatientTaskVariable> projectPatientTaskVariableList = getProjectPatientTaskVariables(projectPatientTask);
            for (ProjectPatientTaskVariable projectPatientTaskVariable : projectPatientTaskVariableList) {
                ProjectTaskVo.ProjectPatientTaskVariableVo projectPatientTaskVariableVo = new ProjectTaskVo.ProjectPatientTaskVariableVo();
                BeanUtils.copyProperties(projectPatientTaskVariable, projectPatientTaskVariableVo);
                varDataList.add(projectPatientTaskVariableVo);
            }
            projectTaskVo.setDataList(varDataList);
            if(PatientTaskTypeEnum.PROJECT_TASK_01.getName().equals(projectPatientTask.getType())){
                projectTaskVo.setTypeViewName(PatientTaskTypeEnum.PROJECT_TASK_01.getValue());
            }
            if(PatientTaskTypeEnum.PROJECT_TASK_02.getName().equals(projectPatientTask.getType())){
                projectTaskVo.setTypeViewName(PatientTaskTypeEnum.PROJECT_TASK_02.getValue());
            }
            if(PatientTaskTypeEnum.PROJECT_TASK_03.getName().equals(projectPatientTask.getType())){
                projectTaskVo.setTypeViewName(PatientTaskTypeEnum.PROJECT_TASK_03.getValue());
            }
            if(PatientTaskTypeEnum.PROJECT_TASK_04.getName().equals(projectPatientTask.getType())){
                projectTaskVo.setTypeViewName(PatientTaskTypeEnum.PROJECT_TASK_04.getValue());
            }
            if(PatientTaskTypeEnum.PROJECT_TASK_05.getName().equals(projectPatientTask.getType())){
                projectTaskVo.setTypeViewName(PatientTaskTypeEnum.PROJECT_TASK_05.getValue());
            }

            dataList.add(projectTaskVo);
        }
        return commonPageListWrapper(pageNum, pageSize, page, dataList);
    }

    @Override
    public CustomResult saveProjectTask(ProjectPatientTaskParam projectPatientTaskParam) {
        CustomResult customResult = new CustomResult();
        ProjectPatientTask projectPatientTask = new ProjectPatientTask();
        if(projectPatientTaskParam.getId() == null){
            BeanUtils.copyProperties(projectPatientTaskParam, projectPatientTask);
            projectPatientTask.setId(SnowflakeIdWorker.getUuid());
            projectPatientTask.setCreateTime(new Date());
            projectPatientTask.setCreateUser(projectPatientTaskParam.getCreateUserId());
            projectPatientTaskMapper.insertSelective(projectPatientTask);
            List<ProjectPatientTaskParam.ProjectPatientTaskVariableParam> projectPatientTaskVariableParamList = projectPatientTaskParam.getDataList();
            for (ProjectPatientTaskParam.ProjectPatientTaskVariableParam projectPatientTaskVariableParam : projectPatientTaskVariableParamList) {
                ProjectPatientTaskVariable projectPatientTaskVariable = new ProjectPatientTaskVariable();
                BeanUtils.copyProperties(projectPatientTaskVariableParam, projectPatientTaskVariable);
                projectPatientTaskVariable.setId(SnowflakeIdWorker.getUuid());
                projectPatientTaskVariable.setStatus("0");
                projectPatientTaskVariable.setTaskId(projectPatientTask.getId());
                projectPatientTaskVariable.setCreateTime(new Date());
                projectPatientTaskVariable.setCreateUser(projectPatientTaskParam.getCreateUserId());
                projectPatientTaskVariableMapper.insertSelective(projectPatientTaskVariable);
            }
        }else{
            ProjectPatientTask projectPatientTaskVo = projectPatientTaskMapper.selectByPrimaryKey(projectPatientTaskParam.getId());
            if(projectPatientTaskVo == null){
                customResult.setMessage(BusinessConfig.RETURN_MESSAGE_RECORD_NOT_FOUND);
                return customResult;
            }

            if(projectPatientTaskParam.getFormId() != projectPatientTaskVo.getFormId()){
                List<ProjectPatientTaskVariable> projectPatientTaskVariableList = getProjectPatientTaskVariables(projectPatientTaskVo);
                for (ProjectPatientTaskVariable projectPatientTaskVariable : projectPatientTaskVariableList) {
                    Map<String,Object> dataMap = new HashMap<>();
                    projectPatientTaskVariable.setStatus("1");
                    dataMap.put("visitId", projectPatientTaskVo.getVisitId());
                    dataMap.put("formId", projectPatientTaskVo.getFormId());
                    projectPatientTaskVariable.setModifyContent(JSON.toJSONString(dataMap));
                    projectPatientTaskVariable.setModifyTime(new Date());
                    projectPatientTaskVariable.setUpdateTime(new Date());
                    projectPatientTaskVariable.setUpdateUser(projectPatientTaskParam.getCreateUserId());
                    projectPatientTaskVariableMapper.updateByPrimaryKeySelective(projectPatientTaskVariable);
                }

                List<ProjectPatientTaskParam.ProjectPatientTaskVariableParam> projectPatientTaskVariableParamList = projectPatientTaskParam.getDataList();
                for (ProjectPatientTaskParam.ProjectPatientTaskVariableParam projectPatientTaskVariableParam : projectPatientTaskVariableParamList) {
                    ProjectPatientTaskVariable projectPatientTaskVariable = new ProjectPatientTaskVariable();
                    BeanUtils.copyProperties(projectPatientTaskVariableParam, projectPatientTaskVariable);
                    projectPatientTaskVariable.setId(SnowflakeIdWorker.getUuid());
                    projectPatientTaskVariable.setTaskId(projectPatientTaskVo.getId());
                    projectPatientTaskVariable.setStatus("0");
                    projectPatientTaskVariable.setCreateTime(new Date());
                    projectPatientTaskVariable.setCreateUser(projectPatientTaskParam.getCreateUserId());
                    projectPatientTaskVariableMapper.insertSelective(projectPatientTaskVariable);
                }
            }
            BeanUtils.copyProperties(projectPatientTaskParam, projectPatientTaskVo);
            projectPatientTaskVo.setUpdateTime(new Date());
            projectPatientTaskVo.setUpdateUser(projectPatientTaskParam.getCreateUserId());
            projectPatientTaskMapper.updateByPrimaryKeySelective(projectPatientTaskVo);
        }
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }

    @Override
    public ProjectTaskVo getProjectTaskInfo(String taskId) {
        List<ProjectTaskVo.ProjectPatientTaskVariableVo> dataList = new ArrayList<>();
        ProjectPatientTask projectPatientTaskVo = projectPatientTaskMapper.selectByPrimaryKey(Long.parseLong(taskId));
        if(projectPatientTaskVo != null){
            ProjectTaskVo projectTaskVo = new ProjectTaskVo();
            BeanUtils.copyProperties(projectPatientTaskVo, projectTaskVo);
            List<ProjectPatientTaskVariable> projectPatientTaskVariableList = getProjectPatientTaskVariables(projectPatientTaskVo);
            for (ProjectPatientTaskVariable projectPatientTaskVariable : projectPatientTaskVariableList) {
                ProjectTaskVo.ProjectPatientTaskVariableVo projectPatientTaskVariableVo = new ProjectTaskVo.ProjectPatientTaskVariableVo();
                BeanUtils.copyProperties(projectPatientTaskVariable, projectPatientTaskVariableVo);
                dataList.add(projectPatientTaskVariableVo);
            }
            projectTaskVo.setDataList(dataList);
            return projectTaskVo;
        }
        return null;
    }

    @Override
    public CustomResult deleteProjectTask(String taskId, String operatorUserId) {
        CustomResult customResult = new CustomResult();
        ProjectPatientTask projectPatientTaskVo = projectPatientTaskMapper.selectByPrimaryKey(Long.parseLong(taskId));
        if(projectPatientTaskVo == null){
            customResult.setMessage(BusinessConfig.RETURN_MESSAGE_RECORD_NOT_FOUND);
            return customResult;
        }
        //验证方案是否已使用
        List<ProjectPatientPlanDetail> projectPatientPlanDetailList = projectPlanManageService.getProjectPlanListByCondition(taskId);
        if(CollectionUtils.isNotEmpty(projectPatientPlanDetailList)){
            customResult.setMessage(BusinessConfig.PROJECT_TASK_RECORD_FOUND);
            return customResult;
        }

        projectPatientTaskVo.setUpdateTime(new Date());
        projectPatientTaskVo.setUpdateUser(operatorUserId);
        projectPatientTaskVo.setStatus("1");
        projectPatientTaskMapper.updateByPrimaryKey(projectPatientTaskVo);
        List<ProjectPatientTaskVariable> projectPatientTaskVariableList = getProjectPatientTaskVariables(projectPatientTaskVo);
        for (ProjectPatientTaskVariable projectPatientTaskVariable : projectPatientTaskVariableList) {
            projectPatientTaskVariable.setStatus("1");
            projectPatientTaskVariable.setUpdateTime(new Date());
            projectPatientTaskVariable.setUpdateUser(operatorUserId);
            projectPatientTaskVariableMapper.updateByPrimaryKey(projectPatientTaskVariable);
        }
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }

    @Override
    public List<ProjectTaskVo> getProjectTaskList(String projectId) {
        return null;
    }

    @Override
    public ProjectTaskVo.ProjectPatientTaskVariableVo getProjectTaskFormDetailEnabled(String projectId, String formDetailId) {
        ProjectPatientTaskVariableExample example = new ProjectPatientTaskVariableExample();
        ProjectPatientTaskVariableExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andFormDetailIdEqualTo(Long.parseLong(formDetailId));
        criteria.andStatusEqualTo("0");
        List<ProjectPatientTaskVariable> projectPatientTaskVariables = projectPatientTaskVariableMapper.selectByExample(example);
        if(CollectionUtils.isNotEmpty(projectPatientTaskVariables)){
            ProjectPatientTaskVariable projectPatientTaskVariable = projectPatientTaskVariables.get(0);
            ProjectTaskVo.ProjectPatientTaskVariableVo projectPatientTaskVariableVo = new ProjectTaskVo.ProjectPatientTaskVariableVo();
            BeanUtils.copyProperties(projectPatientTaskVariable, projectPatientTaskVariableVo);
            return projectPatientTaskVariableVo;
        }
        return null;
    }

    private List<ProjectPatientTaskVariable> getProjectPatientTaskVariables(ProjectPatientTask projectPatientTaskVo) {
        ProjectPatientTaskVariableExample example = new ProjectPatientTaskVariableExample();
        ProjectPatientTaskVariableExample.Criteria criteria1 = example.createCriteria();
        criteria1.andTaskIdEqualTo(projectPatientTaskVo.getId());
        criteria1.andStatusEqualTo("0");
        return projectPatientTaskVariableMapper.selectByExample(example);
    }
}
