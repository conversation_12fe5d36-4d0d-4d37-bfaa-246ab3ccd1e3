package com.haoys.user.config;

import com.haoys.user.common.spring.SpringUtils;
import com.haoys.user.manager.AsyncTaskManager;
import com.haoys.user.manager.factory.AsyncTaskFactory;
import com.haoys.user.service.SystemRequestRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.Map;

/**
 * 系统日志定时任务配置
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
@Configuration
@EnableScheduling
@ConditionalOnProperty(prefix = "request.access.log", name = "enabled", havingValue = "true", matchIfMissing = true)
public class SystemLogScheduleConfig {
    
    private static final Logger log = LoggerFactory.getLogger(SystemLogScheduleConfig.class);
    
    @Autowired
    private SystemLogProperties logProperties;
    
    /**
     * 数据清理任务
     * 每天凌晨2点执行，清理过期的日志数据
     */
    @Scheduled(cron = "${scheduled.data-cleanup.cron:0 0 2 * * ?}")
    @ConditionalOnProperty(prefix = "scheduled.data-cleanup", name = "enabled", havingValue = "true", matchIfMissing = true)
    public void cleanupExpiredData() {
        if (!logProperties.isAutoCleanupEnabled()) {
            log.debug("数据自动清理功能已禁用，跳过清理任务");
            return;
        }
        
        try {
            log.info("开始执行数据清理任务");
            
            // 使用异步任务执行清理
            AsyncTaskManager.ownerTask().execute(
                AsyncTaskFactory.cleanupExpiredSystemRequestRecords(logProperties.getDataRetentionDays())
            );
            
            log.info("数据清理任务已提交到异步队列");
        } catch (Exception e) {
            log.error("执行数据清理任务失败", e);
        }
    }
    
    /**
     * 表优化任务
     * 每周日凌晨3点执行，优化数据库表
     */
    @Scheduled(cron = "${scheduled.table-optimize.cron:0 0 3 * * SUN}")
    @ConditionalOnProperty(prefix = "scheduled.table-optimize", name = "enabled", havingValue = "true", matchIfMissing = true)
    public void optimizeTable() {
        try {
            log.info("开始执行表优化任务");
            
            // 使用异步任务执行优化
            AsyncTaskManager.ownerTask().execute(
                AsyncTaskFactory.optimizeSystemRequestRecordTable()
            );
            
            log.info("表优化任务已提交到异步队列");
        } catch (Exception e) {
            log.error("执行表优化任务失败", e);
        }
    }
    
    /**
     * 监控报告任务
     * 每天早上8点执行，生成监控报告
     */
    @Scheduled(cron = "${scheduled.monitoring-report.cron:0 0 8 * * ?}")
    @ConditionalOnProperty(prefix = "scheduled.monitoring-report", name = "enabled", havingValue = "true", matchIfMissing = false)
    public void generateMonitoringReport() {
        try {
            log.info("开始生成监控报告");
            
            // 使用异步任务生成报告
            AsyncTaskManager.ownerTask().execute(
                AsyncTaskFactory.generateSystemRequestRecordReport()
            );
            
            log.info("监控报告生成任务已提交到异步队列");
        } catch (Exception e) {
            log.error("生成监控报告失败", e);
        }
    }
    
    /**
     * 健康检查任务
     * 每5分钟执行一次，检查系统健康状态
     */
    @Scheduled(cron = "0 */5 * * * ?")
    @ConditionalOnProperty(prefix = "scheduled.health-check", name = "enabled", havingValue = "true", matchIfMissing = false)
    public void healthCheck() {
        try {
            // 直接通过类型获取服务，避免CGLIB代理问题
            SystemRequestRecordService recordService = SpringUtils.getBean(SystemRequestRecordService.class);
            if (recordService == null) {
                log.warn("SystemRequestRecordService 服务不可用");
                return;
            }

            // 执行简单的健康检查
            boolean healthStatus = recordService.checkHealth();

            if (!healthStatus) {
                log.warn("系统访问记录服务健康检查失败");
            } else {
                log.debug("系统访问记录服务健康检查正常");
            }

        } catch (Exception e) {
            log.error("健康检查失败", e);
        }
    }
    
    /**
     * 性能统计任务
     * 每小时执行一次，统计性能指标
     */
    @Scheduled(cron = "0 0 * * * ?")
    @ConditionalOnProperty(prefix = "scheduled.performance-stats", name = "enabled", havingValue = "true", matchIfMissing = false)
    public void collectPerformanceStats() {
        try {
            log.info("开始收集性能统计数据");
            
            // 使用异步任务收集统计数据
            AsyncTaskManager.ownerTask().execute(
                AsyncTaskFactory.collectSystemRequestRecordStats()
            );
            
            log.info("性能统计任务已提交到异步队列");
        } catch (Exception e) {
            log.error("收集性能统计数据失败", e);
        }
    }
    
    /**
     * 告警检查任务
     * 每10分钟执行一次，检查是否需要发送告警
     */
    @Scheduled(cron = "0 */10 * * * ?")
    @ConditionalOnProperty(prefix = "scheduled.alert-check", name = "enabled", havingValue = "true", matchIfMissing = false)
    public void checkAlerts() {
        try {
            // 直接通过类型获取服务，避免CGLIB代理问题
            SystemRequestRecordService recordService = SpringUtils.getBean(SystemRequestRecordService.class);
            if (recordService == null) {
                log.warn("SystemRequestRecordService 服务不可用");
                return;
            }

            // 检查最近10小时的错误率
            Map<String, Object> errorRateStats = recordService.getErrorRateStats(10);

            if (errorRateStats != null && !errorRateStats.containsKey("error")) {
                Object errorRateObj = errorRateStats.get("errorRate");

                if (errorRateObj instanceof Number) {
                    double errorRate = ((Number) errorRateObj).doubleValue();

                    if (errorRate > 10.0) { // 错误率超过10%
                        log.warn("系统错误率过高: {}%", String.format("%.2f", errorRate));

                        // 发送告警
                        AsyncTaskManager.ownerTask().execute(
                            AsyncTaskFactory.sendSystemRequestRecordAlert("高错误率告警",
                                String.format("系统错误率达到 %.2f%%，请及时处理。统计数据：总请求数=%s，错误请求数=%s",
                                    errorRate,
                                    errorRateStats.get("totalRequests"),
                                    errorRateStats.get("errorRequests")))
                        );
                    } else {
                        log.debug("系统错误率正常: {}%", String.format("%.2f", errorRate));
                    }
                }
            } else {
                log.warn("获取错误率统计失败: {}", errorRateStats != null ? errorRateStats.get("error") : "返回数据为空");
            }

        } catch (Exception e) {
            log.error("告警检查失败", e);
        }
    }
    
    /**
     * 数据归档任务
     * 每月1号凌晨1点执行，归档历史数据
     */
    @Scheduled(cron = "0 0 1 1 * ?")
    @ConditionalOnProperty(prefix = "scheduled.data-archive", name = "enabled", havingValue = "true", matchIfMissing = false)
    public void archiveHistoricalData() {
        try {
            log.info("开始执行数据归档任务");
            
            // 使用异步任务执行归档
            AsyncTaskManager.ownerTask().execute(
                AsyncTaskFactory.archiveSystemRequestRecords(logProperties.getDataRetentionDays())
            );
            
            log.info("数据归档任务已提交到异步队列");
        } catch (Exception e) {
            log.error("执行数据归档任务失败", e);
        }
    }
    
    /**
     * 索引优化任务
     * 每周六凌晨4点执行，优化数据库索引
     */
    @Scheduled(cron = "0 0 4 * * SAT")
    @ConditionalOnProperty(prefix = "scheduled.index-optimize", name = "enabled", havingValue = "true", matchIfMissing = false)
    public void optimizeIndexes() {
        try {
            log.info("开始执行索引优化任务");
            
            // 使用异步任务执行索引优化
            AsyncTaskManager.ownerTask().execute(
                AsyncTaskFactory.optimizeSystemRequestRecordIndexes()
            );
            
            log.info("索引优化任务已提交到异步队列");
        } catch (Exception e) {
            log.error("执行索引优化任务失败", e);
        }
    }
    
    /**
     * 缓存清理任务
     * 每天凌晨4点执行，清理过期缓存
     */
    @Scheduled(cron = "0 0 4 * * ?")
    @ConditionalOnProperty(prefix = "scheduled.cache-cleanup", name = "enabled", havingValue = "true", matchIfMissing = false)
    public void cleanupCache() {
        try {
            log.info("开始执行缓存清理任务");
            
            // 使用异步任务执行缓存清理
            AsyncTaskManager.ownerTask().execute(
                AsyncTaskFactory.cleanupSystemRequestRecordCache()
            );
            
            log.info("缓存清理任务已提交到异步队列");
        } catch (Exception e) {
            log.error("执行缓存清理任务失败", e);
        }
    }
    
    /**
     * 数据库连接监控任务
     * 每30分钟执行一次，监控数据库连接状态
     */
    @Scheduled(cron = "0 */30 * * * ?")
    @ConditionalOnProperty(prefix = "scheduled.database-monitor", name = "enabled", havingValue = "true", matchIfMissing = false)
    public void monitorDatabaseConnection() {
        try {
            Object recordService = SpringUtils.getBean("systemRequestRecordService");
            if (recordService == null) {
                return;
            }
            
            // 执行简单查询测试数据库连接
            Object tableStats = recordService.getClass().getMethod("getTableStatistics").invoke(recordService);
            
            if (tableStats instanceof Map && !((Map<?, ?>) tableStats).isEmpty()) {
                log.debug("数据库连接正常，表统计信息: {}", tableStats);
            } else {
                log.warn("数据库连接可能存在问题，无法获取表统计信息");
            }
            
        } catch (Exception e) {
            log.error("数据库连接监控失败", e);
        }
    }
}
