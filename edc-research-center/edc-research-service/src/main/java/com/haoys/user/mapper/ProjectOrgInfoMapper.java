package com.haoys.user.mapper;

import com.haoys.user.domain.vo.project.ProjectOrgVo;
import com.haoys.user.model.ProjectOrgInfo;
import com.haoys.user.model.ProjectOrgInfoExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
public interface ProjectOrgInfoMapper {

    long countByExample(ProjectOrgInfoExample example);

    int deleteByExample(ProjectOrgInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectOrgInfo record);

    int insertSelective(ProjectOrgInfo record);

    List<ProjectOrgInfo> selectByExample(ProjectOrgInfoExample example);

    ProjectOrgInfo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectOrgInfo record, @Param("example") ProjectOrgInfoExample example);

    int updateByExample(@Param("record") ProjectOrgInfo record, @Param("example") ProjectOrgInfoExample example);

    int updateByPrimaryKeySelective(ProjectOrgInfo record);

    int updateByPrimaryKey(ProjectOrgInfo record);

    /**
     * 项目中心分页列表
     * @param projectId
     * @param name
     * @param officer
     * @param orgIds
     * @return
     */
    List<ProjectOrgVo> getProjectOrgListForPage(@Param("projectId")String projectId, @Param("name")String name, @Param("officer") String officer, @Param("orgIds")String orgIds);

    /**
     * 根据projectId和orgId查询项目研究中心
     * @param projectId
     * @param orgId
     * @param code
     * @return
     */
    ProjectOrgInfo getProjectOrgListByProjectIdAndOrgId(String projectId, String orgId, String code);

    /**
     * 查询项目研究中心code 是否存在
     * @param projectId
     * @param code
     * @return
     */
    ProjectOrgInfo getProjectOrgInfoByProjectIdAndCode(String projectId, String code);

    /**
     * 删除项目研究中心
     * @param projectId
     * @param projectOrgId
     */
    void deleteProjectOrgByOrgId(String projectId, String projectOrgId);

    /**
     * 获取最大的项目中心的识别码
     * @return 最大的项目中心的识别码
     */
    String getMaxIdentCodeCode();

    /**
     * 根据识别码查询项目信息
     * @param identCode
     * @return
     */
    List<ProjectOrgInfo> selectProjectByIdentCode(String identCode);

    Long getDefaultProjectOrgId(String tenantId, String platformId);
    
    ProjectOrgInfo getProjectOrganizationInfo(String projectId);
}
