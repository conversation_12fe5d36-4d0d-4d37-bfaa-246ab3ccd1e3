package com.haoys.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.entity.ProjectMenuQuery;
import com.haoys.user.domain.entity.ProjectRoleMenuQuery;
import com.haoys.user.domain.entity.ProjectRoleQuery;
import com.haoys.user.domain.entity.ProjectUserRoleQuery;
import com.haoys.user.domain.enums.ProjectRoleResultEnums;
import com.haoys.user.domain.vo.auth.ProjectMenuVo;
import com.haoys.user.domain.vo.auth.ProjectRoleVo;
import com.haoys.user.domain.vo.auth.ProjectUserRoleVo;
import com.haoys.user.enums.ProjectRoleEnum;
import com.haoys.user.exception.ServiceException;
import com.haoys.user.mapper.ProjectMenuMapper;
import com.haoys.user.mapper.ProjectOrgRoleMapper;
import com.haoys.user.mapper.ProjectOrgUserRoleMapper;
import com.haoys.user.mapper.ProjectRoleMapper;
import com.haoys.user.mapper.ProjectRoleMenuMapper;
import com.haoys.user.mapper.ProjectUserRoleMapper;
import com.haoys.user.model.ProjectOrgRole;
import com.haoys.user.model.ProjectOrgRoleExample;
import com.haoys.user.model.ProjectOrgUserRoleExample;
import com.haoys.user.model.ProjectRole;
import com.haoys.user.model.ProjectRoleExample;
import com.haoys.user.service.ProjectRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ProjectRoleServiceImpl implements ProjectRoleService {

    private final ProjectRoleMapper projectRoleMapper;
    private final ProjectMenuMapper projectMenuMapper;
    private final ProjectRoleMenuMapper projectRoleMenuMapper;
    private final ProjectUserRoleMapper projectUserRoleMapper;
    private final ProjectOrgRoleMapper projectOrgRoleMapper;

    private final ProjectOrgUserRoleMapper projectOrgUserRoleMapper;


    @Override
    public List<ProjectRoleQuery> selectProjectRoleList(ProjectRoleQuery projectRoleQuery) {
        PageHelper.startPage(projectRoleQuery.getPageNum(), projectRoleQuery.getPageSize());
        return projectRoleMapper.selectProjectRoleListByQueryCondition(projectRoleQuery);
    }

    @Override
    public List<ProjectRoleVo> getProjectRoleListForCombobox(ProjectRoleQuery projectRoleQuery) {
        List<ProjectRoleVo> projectRoleQueryList = new ArrayList<>();
        List<ProjectRoleQuery> projectRoleList = projectRoleMapper.selectProjectRoleListByQueryCondition(projectRoleQuery);
        if(projectRoleQuery.getShowSelectAllOption()){
            ProjectRoleVo projectRoleParam = new ProjectRoleVo();
            projectRoleParam.setName("全部研究角色");
            projectRoleParam.setId("");
            projectRoleQueryList.add(projectRoleParam);
        }
        for (ProjectRoleQuery roleQuery : projectRoleList) {
            ProjectRoleVo projectRoleVo = new ProjectRoleVo();
            projectRoleVo.setId(roleQuery.getId().toString());
            projectRoleVo.setName(roleQuery.getName());
            projectRoleVo.setEnname(roleQuery.getEnname());
            projectRoleQueryList.add(projectRoleVo);
        }
        return projectRoleQueryList;
    }

    @Override
    public void insertProjectUserRole(String projectId, String userId, String roleId, String systemTenantId, String systemPlatformId){
        if(StringUtils.isEmpty(projectId)){
            Assert.notEmpty(projectId,"projectId不能为空");
        }
        if(StringUtils.isEmpty(roleId)){
            Assert.notEmpty(projectId,"roleId不能为空");
        }
        if(StringUtils.isEmpty(systemTenantId)){systemTenantId = SecurityUtils.getSystemTenantId();}
        if(StringUtils.isEmpty(systemPlatformId)){systemPlatformId = SecurityUtils.getSystemPlatformId();}
        ProjectUserRoleVo projectUserRoleVo = projectUserRoleMapper.getProjectRoleListByProjectIdAndUserId(projectId, userId, roleId);
        if(projectUserRoleVo == null){
            ArrayList<ProjectUserRoleQuery> list = new ArrayList<>();
            ProjectUserRoleQuery projectUserRoleQuery = new ProjectUserRoleQuery();
            projectUserRoleQuery.setProjectId(NumberUtil.parseLong(projectId));
            projectUserRoleQuery.setUserId(NumberUtil.parseLong(userId));
            projectUserRoleQuery.setRoleId(NumberUtil.parseLong(roleId));
            projectUserRoleQuery.setTenantId(systemTenantId);
            projectUserRoleQuery.setPlatformId(systemPlatformId);
            list.add(projectUserRoleQuery);
            if(list.size() > 0){
                projectUserRoleMapper.batchProjectUserRole(list);
            }
        }
    }

    @Override
    public void insertProjectRoleMenu(Long roleId){
        ProjectRoleMenuQuery entity = new ProjectRoleMenuQuery();
        entity.setRoleId(1L);
        List<ProjectRoleMenuQuery> projectRoleMenuQueries = projectRoleMenuMapper.selectList(entity);
        for (ProjectRoleMenuQuery projectRoleMenuQuery : projectRoleMenuQueries) {
            ProjectRoleMenuQuery roleMenu = new ProjectRoleMenuQuery();
            roleMenu.setRoleId(roleId);
            roleMenu.setMenuId(projectRoleMenuQuery.getMenuId());
            roleMenu.setTenantId(SecurityUtils.getSystemTenantId());
            roleMenu.setPlatformId(SecurityUtils.getSystemPlatformId());
            projectRoleMenuMapper.insert(roleMenu);
        }
    }

    @Override
    public CommonResult insertProjectRole(ProjectRoleQuery projectRoleQuery) {
        projectRoleQuery.setId(null);
        // 项目管理员创建的角色名称校验
        ProjectRole projectRoleInfo = this.checkProjectRoleNameUnique(projectRoleQuery);
        if (projectRoleInfo != null){
            return CommonResult.failed(ProjectRoleResultEnums.B40006.getCode(),"角色名称已存在");
        }
        projectRoleInfo = this.checkProjectRoleEnNameUnique(projectRoleQuery);
        if (projectRoleInfo != null){
            return CommonResult.failed(ProjectRoleResultEnums.B40006.getCode(),"角色英文名称已存在");
        }

        // 新增角色
        ProjectRole projectRole = new ProjectRole();
        projectRole.setId(SnowflakeIdWorker.getUuid());
        projectRole.setProjectId(projectRoleQuery.getProjectId());
        projectRole.setName(projectRoleQuery.getName());
        projectRole.setEnname(projectRoleQuery.getEnname());
        projectRole.setProjectTemplate(false);
        projectRole.setOrgTemplate(false);
        projectRole.setSystemDefault(projectRoleQuery.getSystemDefault());
        projectRole.setCreateUser(SecurityUtils.getUserId().toString());
        projectRole.setStatus(BusinessConfig.ENABLED_STATUS);
        projectRole.setCreateTime(new Date());
        projectRole.setDescription(projectRoleQuery.getDescription());
        projectRole.setTenantId(SecurityUtils.getSystemTenantId());
        projectRole.setPlatformId(SecurityUtils.getSystemPlatformId());
        projectRoleMapper.insert(projectRole);

        // 新增用户与角色管理
        List<String> menuIds = projectRoleQuery.getMenuIds();
        if(CollectionUtil.isEmpty(menuIds)){
            throw new ServiceException(ResultCode.PROJECT_ROLE_MENU_NOT_FOUND);
        }
        log.info("batchSaveProjectRoleMenuList init projectId:{} roleId:{} menuIds:{}", projectRoleQuery.getProjectId().toString(), projectRole.getId().toString(), JSON.toJSONString(menuIds));
        String tenantId = SecurityUtils.getSystemTenantId();
        String platformId = SecurityUtils.getSystemPlatformId();
        projectRoleMenuMapper.batchSaveProjectRoleMenuList(projectRoleQuery.getProjectId().toString(), projectRole.getId().toString(), menuIds, tenantId, platformId);
        if(projectRoleQuery.getSystemDefault() & projectRoleQuery.getSaveDefaultDefault()){
            insertProjectUserRole(projectRoleQuery.getProjectId().toString(), projectRoleQuery.getCreateUser(), projectRole.getId().toString(), "", "");
        }
        return CommonResult.success(null);
    }

    @Override
    public CommonResult updateProjectRole(ProjectRoleQuery projectRoleQuery) {

        // 项目管理员创建的角色名称校验
        ProjectRole role0 = this.checkProjectRoleNameUnique(projectRoleQuery);
        if (role0!=null){
            return CommonResult.failed(ProjectRoleResultEnums.B40006.getCode(),"角色名称已存在");
        }
        ProjectRole role1 = this.checkProjectRoleEnNameUnique(projectRoleQuery);
        if (role1!=null){
            return CommonResult.failed(ProjectRoleResultEnums.B40006.getCode(),"角色英文名称已存在");
        }

        projectRoleQuery.setUpdateUser(SecurityUtils.getUserId().toString());
        projectRoleQuery.setUpdateTime(new Date());
        projectRoleMapper.update(projectRoleQuery);
        projectRoleMenuMapper.deleteProjectRoleMenuByRoleId(projectRoleQuery.getId());
        // 新增用户与角色管理
        List<String> menuIds = projectRoleQuery.getMenuIds();
        if(CollectionUtil.isEmpty(menuIds)){
            throw new ServiceException(ResultCode.PROJECT_ROLE_MENU_NOT_FOUND);
        }
        projectRoleMenuMapper.batchSaveProjectRoleMenuList(projectRoleQuery.getProjectId().toString(), projectRoleQuery.getId().toString(), menuIds, "", "");
        return CommonResult.success(null);
    }

    @Override
    public CommonResult deleteProjectRoleById(Long roleId) {
        ProjectRole projectRole = projectRoleMapper.selectByPrimaryKey(roleId);
        if (projectRole!=null){
            if (projectRole.getProjectTemplate()){
                return CommonResult.failed("系统模板无法删除");
            }
            Long projectOrgUserRoles = getProjectOrgUserRoles(roleId, projectRole.getProjectId());
            if (projectOrgUserRoles>0){
                return CommonResult.failed(ProjectRoleResultEnums.J40004);
            }
            projectRoleMenuMapper.deleteProjectRoleMenuByRoleId(roleId);
            projectRoleMapper.deleteByPrimaryKey(roleId);
            return CommonResult.success(null);
        }
        return CommonResult.failed();
    }

    /**
     *  查询该角色是否被引用
      */
    private Long getProjectOrgUserRoles(Long roleId, Long projectId) {
        // 查询该角色是否被引用
        ProjectOrgRoleExample example = new ProjectOrgRoleExample();
        ProjectOrgRoleExample.Criteria criteria = example.createCriteria();
        criteria.andResourceRoleIdEqualTo(roleId);
        criteria.andProjectIdEqualTo(projectId);
        List<ProjectOrgRole> roles = projectOrgRoleMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(roles)){
            ProjectOrgUserRoleExample example2 = new ProjectOrgUserRoleExample();
            ProjectOrgUserRoleExample.Criteria criteria2 = example2.createCriteria();
            criteria2.andRoleIdEqualTo(roles.get(0).getId());
            criteria2.andProjectIdEqualTo(projectId);
            return projectOrgUserRoleMapper.countByExample(example2);
        }
        return 0L;
    }

    /**
     * 校验名称
     * @param role
     * @return
     */
    @Override
    public ProjectRole checkProjectRoleNameUnique(ProjectRoleQuery role) {
        String roleId = role.getId() == null ?  "-1": role.getId().toString();
        // 根据项目id和名称检查是否已经存在，并且id也不相同，因为如果是编辑查出的是相同的
        ProjectRole projectRole = projectRoleMapper.getProjectRoleInfoByRoleName(role.getProjectId().toString(), role.getName());
        if(StringUtils.isNotNull(projectRole) && !projectRole.getId().toString().equals(roleId)){
            return projectRole;
        }
        return null;
    }

    /**
     * 校验英文名称
     * @param role
     * @return
     */
    private ProjectRole checkProjectRoleEnNameUnique(ProjectRoleQuery role) {
        String roleId = role.getId() == null ?  "-1": role.getId().toString();
        ProjectRole projectRole = projectRoleMapper.getProjectRoleInfoByEnName(role.getProjectId().toString(), role.getEnname());
        if(StringUtils.isNotNull(projectRole) && !projectRole.getId().toString().equals(roleId)){
            return projectRole;
        }
        return null;
    }

    @Override
    public int countProjectRoleByRoleId(Long roleId) {
        return projectRoleMapper.countProjectUserRoleByRoleId(roleId);
    }

    @Override
    public List<ProjectRoleVo> getProjectRoleListByUserId(String projectId, String userId) {
        return projectRoleMapper.getProjectRoleListByUserId(projectId, userId);
    }

    @Override
    public List<ProjectRoleVo> getProjectOrgRoleListByUserId(String projectId, String orgId, String userId) {
        return projectOrgRoleMapper.getProjectOrgRoleListByUserId(projectId, orgId, userId);
    }

    @Override
    public CustomResult initSystemProjectRoleInfo(String projectId, String templateStatus, String createUserId) {
        CustomResult customResult = new CustomResult();
        ProjectRoleQuery projectRoleQuery = new ProjectRoleQuery();
        projectRoleQuery.setProjectId(Long.parseLong(projectId));
        List<String> menuIds = new ArrayList<>();

        List<ProjectMenuQuery> projectMenuQueryList = projectMenuMapper.getProjectMenuListForTemplate(Constants.SYSTEM_PROJECT_MENN_TEMPLATE_ID);
        for (ProjectMenuQuery projectMenuQuery : projectMenuQueryList) {
            projectMenuQuery.setProjectId(Long.parseLong(projectId));
            projectMenuQuery.setTenantId(SecurityUtils.getSystemTenantId());
            projectMenuQuery.setPlatformId(SecurityUtils.getSystemPlatformId());
        }
        projectMenuMapper.batchSaveProjectMenuList(projectMenuQueryList);

        //查询字典角色列表
        List<String> roleCodeList = Arrays.asList(ProjectRoleEnum.PROJECT_PM.getCode(), ProjectRoleEnum.PROJECT_PI.getCode(),
                                          ProjectRoleEnum.PROJECT_SUB_PI.getCode(),ProjectRoleEnum.PROJECT_CRA.getCode(),ProjectRoleEnum.PROJECT_CRC.getCode(),
                                          ProjectRoleEnum.PROJECT_PH.getCode(),ProjectRoleEnum.PROJECT_QA.getCode(),ProjectRoleEnum.PROJECT_MEDICAL.getCode(),
                                          ProjectRoleEnum.PROJECT_DM.getCode(),ProjectRoleEnum.PROJECT_PA.getCode());


        roleCodeList.forEach(projectRoleCode ->{
            // 查询角色模版状态
            ProjectRole projectTemplateRole = projectRoleMapper.getProjectTemplateRoleInfoByRoleCode(projectRoleCode);
            if(projectTemplateRole != null && projectTemplateRole.getStatus() == 1){
                return;
            }
            //根据项目ID获取菜单ID
            List<ProjectMenuVo> projectMenuList = projectMenuMapper.selectProjectTemplateMenuInfo(Constants.SYSTEM_PROJECT_MENN_TEMPLATE_ID, projectRoleCode);
            for (ProjectMenuVo item : projectMenuList) {
                menuIds.add(item.getMenuId().toString());
            }
            projectRoleQuery.setMenuIds(menuIds);
            ProjectRoleEnum projectRoleEnum = ProjectRoleEnum.getDictionaryInfoByCode(projectRoleCode);
            projectRoleQuery.setName(projectRoleEnum.getValue());
            projectRoleQuery.setEnname(projectRoleEnum.getCode());
            projectRoleQuery.setSystemDefault(true);
            if(ProjectRoleEnum.PROJECT_PA.getCode().equals(projectRoleCode)){
                projectRoleQuery.setSaveDefaultDefault(true);
            }else{
                projectRoleQuery.setSaveDefaultDefault(false);
            }
            projectRoleQuery.setCreateUser(createUserId);
            insertProjectRole(projectRoleQuery);
            menuIds.clear();
        });
        return customResult;
    }

    @Override
    public ProjectRoleVo getProjectLeaderRoleInfo(Long projectId) {
        ProjectRoleQuery projectRoleQuery = new ProjectRoleQuery();
        projectRoleQuery.setName("项目负责人");
        projectRoleQuery.setProjectId(projectId);
        projectRoleQuery.setEnname(ProjectRoleEnum.PROJECT_PA.getCode());
        projectRoleQuery.setStatus("1");
        ProjectRole projectRoleQueryData = projectRoleMapper.selectOne(projectRoleQuery);
        if(projectRoleQueryData != null){
            ProjectRoleVo projectRoleVo = new ProjectRoleVo();
            BeanUtils.copyProperties(projectRoleQueryData, projectRoleVo);
            projectRoleVo.setRoleId(projectRoleQuery.getId().toString());
            return projectRoleVo;
        }
        return null;
    }

    @Override
    public CommonResult updateProjectRoleStatus(ProjectRoleQuery projectRoleQuery) {
        ProjectRole role = projectRoleMapper.selectByPrimaryKey(projectRoleQuery.getId());
        if (role!=null){
            // 校验是否被使用，如果被使用不能禁用
            if (BusinessConfig.DISABLED_STATUS.toString().equals(projectRoleQuery.getStatus())){

                Long projectOrgUserRoles = getProjectOrgUserRoles(projectRoleQuery.getId(), role.getProjectId());
                if (projectOrgUserRoles>0){
                    return CommonResult.failed(ProjectRoleResultEnums.J40004);
                }
            }
            role.setStatus(Integer.parseInt(projectRoleQuery.getStatus()));
            int update = projectRoleMapper.update(projectRoleQuery);
            if (update>0){
                if (BusinessConfig.ENABLED_STATUS.toString().equals(projectRoleQuery.getStatus())){
                    return CommonResult.success(ProjectRoleResultEnums.Q40005);
                }else {
                    return CommonResult.success(ProjectRoleResultEnums.S40003);
                }
            }else {
                return CommonResult.failed();
            }
        }
        return CommonResult.failed();
    }

    /**
     * 根据id获取
     * @param roleId 角色id
     * @return
     */
    @Override
    public ProjectRoleQuery getProjectRoleByRoleId(Long roleId) {
        ProjectRoleQuery projectRoleQuery = new ProjectRoleQuery();
        ProjectRole projectRole = projectRoleMapper.selectByPrimaryKey(roleId);
        if (projectRole != null){
            BeanUtil.copyProperties(projectRole, projectRoleQuery, false);
            List<String> menuIds = projectRoleMenuMapper.selectProjectMenuIdsByProjectRoleId(roleId.toString());
            projectRoleQuery.setMenuIds(menuIds);
        }
        return projectRoleQuery;
    }

    @Override
    public ProjectUserRoleVo getProjectRoleListByProjectIdAndUserId(String projectId, String systemUserId, String roleId) {
        return projectUserRoleMapper.getProjectRoleListByProjectIdAndUserId(projectId, systemUserId, roleId);
    }

    @Override
    public void deleteProjectUserRole(String projectId, String userId) {
        projectUserRoleMapper.deleteProjectUserRoleByProjectIdAndUserId(projectId, userId);
    }

    @Override
    public List<ProjectMenuVo> selectProjectTemplateMenuInfo(String systemProjectMenuTemplateId, String projectRoleCode) {
        return projectMenuMapper.selectProjectTemplateMenuInfo(systemProjectMenuTemplateId, projectRoleCode);
    }

    @Override
    public ProjectRole getProjectTemplateRoleInfoByRoleCode(String projectRoleCode) {
        return projectRoleMapper.getProjectTemplateRoleInfoByRoleCode(projectRoleCode);
    }
    
    @Override
    public ProjectRole getProjectManageRoleInfoByProjectRoleCode(String projectId, String code, String tenantId) {
        return projectRoleMapper.getProjectManageRoleInfoByProjectRoleCode(projectId, code, tenantId);
    }
    
    @Override
    public List<ProjectRole> selectByExample(ProjectRoleExample example) {
        return projectRoleMapper.selectByExample(example);
    }

    @Override
    public int insertSelective(ProjectRole record){
        return projectRoleMapper.insertSelective(record);

    }

    @Override
    public int updateByPrimaryKeySelective(ProjectRole record){
        return projectRoleMapper.updateByPrimaryKeySelective(record);
    }
    @Override
    public int deleteByExample(ProjectRoleExample example){
        return projectRoleMapper.deleteByExample(example);
    }

    @Override
    public ProjectRole selectByPrimaryKey(Long id){
        return projectRoleMapper.selectByPrimaryKey(id);
    }

}
