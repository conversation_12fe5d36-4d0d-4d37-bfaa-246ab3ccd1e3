package com.haoys.user.domain.vo.auth;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ProjectRoleVo implements Serializable {


    @ApiModelProperty(value = "项目角色id")
    private String id;

    @ApiModelProperty(value = "项目角色id")
    private String roleId;

    @ApiModelProperty(value = "项目角色名称")
    private String name;

    @ApiModelProperty(value = "项目角色code")
    private String enname;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目中心授权id")
    private String orgId;

    @ApiModelProperty(value = "研究中心角色名称")
    private String orgRoleName;

    @ApiModelProperty(value = "研究中心角色Code")
    private String orgRoleCode;

    @ApiModelProperty(value = "启用状态0/1")
    private Integer status;

    @ApiModelProperty(value = "排序字段")
    private Integer sort;

    @ApiModelProperty(value = "全局标识-禁止编辑")
    private Boolean globalDefault;

    @ApiModelProperty(value = "是否系统默认角色0/1")
    private Boolean systemDefault;

    @ApiModelProperty(value = "项目模板0/1")
    private Boolean projectTemplate;

    @ApiModelProperty(value = "项目中心模板0/1")
    private Boolean orgTemplate;

    private Boolean defaultProjectRole = false;

    @ApiModelProperty(value = "创建者")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    private String updateUser;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

    private Long[] menuIds;

}
