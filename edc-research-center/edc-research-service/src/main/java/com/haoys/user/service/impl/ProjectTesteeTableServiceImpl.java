package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.haoys.user.common.annotation.ProjectVariableRecordFlag;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.enums.FormVariableComplateStatus;
import com.haoys.user.enums.ProjectChallengeButtonEnum;
import com.haoys.user.enums.ProjectChallengeEnum;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeFormDetailTableVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeFormTableConfigVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeTableRowDataVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeTableVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeTableWrapperVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDetailVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDictionaryVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDvpRuleVo;
import com.haoys.user.domain.vo.ecrf.TemplateTableVo;
import com.haoys.user.domain.vo.project.ProjectChallengeVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeFormAndTableCountVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeFormAndTableResultExportVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeFormImageVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeTableResultExportVo;
import com.haoys.user.domain.vo.testee.TesteeChallengeVo;
import com.haoys.user.mapper.ProjectTesteeTableMapper;
import com.haoys.user.mapper.TemplateFormDetailMapper;
import com.haoys.user.mapper.TemplateFormTableMapper;
import com.haoys.user.model.*;
import com.haoys.user.service.*;
import com.haoys.user.model.Dictionary;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class ProjectTesteeTableServiceImpl implements ProjectTesteeTableService {

    private final ProjectTesteeTableMapper projectTesteeTableMapper;
    private final TemplateFormTableMapper templateFormTableMapper;
    private final TemplateFormDetailMapper templateFormDetailMapper;
    private final ProjectTesteeChallengeService projectTesteeChallengeService;
    private final ProjectTesteeDvpService projectTesteeDvpService;
    private final ProjectTesteeFileService projectTesteeFileService;
    private final TemplateFormGroupService templateFormGroupService;
    private final ProjectDictionaryService projectDictionaryService;
    private final DictionaryService dictionaryService;
    private final TemplateConfigService templateConfigService;
    private final TemplateFormVariableRuleService variableRuleService;

    @Override
    public List<ProjectTesteeTableVo> getProjectTesteeTableRowRecord(String projectId, String planId, String visitId, String formId, String testeeId, String queryMethod, String rowNumber, String openOCR, String medicalType, String testeeGroupId) {
        List<ProjectTesteeTableVo> dataList = new ArrayList<>();
        Map<String,Object> params = new HashMap<>();
        params.put("projectId",projectId);
        params.put("planId",planId);
        params.put("visitId",visitId);
        params.put("formId",formId);
        params.put("testeeId",testeeId);
        params.put("rowNumber",rowNumber);
        params.put("status","0");
        if(StringUtils.isNotEmpty(queryMethod)){
            params.put("queryMethod",queryMethod);
        }
        List<ProjectTesteeTableVo> projectTesteeTables;
        if(StringUtils.isEmpty(testeeGroupId)){
            projectTesteeTables = projectTesteeTableMapper.getProjectTesteeTableRowRecord(params);
        }else{
            projectTesteeTables = projectTesteeTableMapper.getProjectTesteeGroupTableRowRecord(params);
        }
        for (ProjectTesteeTableVo projectTesteeTable : projectTesteeTables) {
            Long formTableId = projectTesteeTable.getFormTableId();

            if(projectTesteeTable.getGroupId() != null){
                formTableId = projectTesteeTable.getResourceTableId();
            }
            TemplateFormTable templateFormTableType = getTemplateFormTableType(formTableId);
            ProjectTesteeTableVo projectTesteeTableVo = new ProjectTesteeTableVo();
            BeanUtils.copyProperties(projectTesteeTable, projectTesteeTableVo);
            projectTesteeTableVo.setTesteeResultId(projectTesteeTable.getId().toString());
            projectTesteeTableVo.setRowNumber(projectTesteeTable.getRowNumber());
            projectTesteeTableVo.setType(templateFormTableType.getType());
            projectTesteeTableVo.setExpand(templateFormTableType.getExpand());
            projectTesteeTable.setRefDicId(templateFormTableType.getRefDicId());
            if(StringUtils.isNotEmpty(templateFormTableType.getExpand())){
                Map<String, Object> dataMap = JSON.parseObject(templateFormTableType.getExpand(), new TypeReference<HashMap<String, Object>>(){}.getType());
                projectTesteeTableVo.setExpandValue(dataMap);
            }
            projectTesteeTableVo.setFieldName(projectTesteeTable.getFieldName());
            projectTesteeTableVo.setFieldValue(projectTesteeTable.getFieldValue());
            projectTesteeTableVo.setFieldText(projectTesteeTable.getFieldText());
            projectTesteeTableVo.setUnitValue(projectTesteeTable.getUnitValue());
            projectTesteeTableVo.setUnitText(projectTesteeTable.getUnitText());

            //查询表格列的计算规则
            TemplateFormVariableRule tRule = variableRuleService.getFormVariableRule(projectId,null, formId, projectTesteeTable.getFormTableId().toString());
            projectTesteeTableVo.setVariableRule(tRule);

            //如果存在多张图片则全部展示
            List<String> stringList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_IMAGE, BusinessConfig.PROJECT_VISIT_CRF_FORM_FILE);
            if(stringList.contains(templateFormTableType.getType())){
                List<ProjectTesteeFormImageVo> variableImageList = projectTesteeFileService.getProjectTesteeFormImageList(projectId, planId, visitId, projectTesteeTable.getFormId().toString(), testeeId,"", templateFormTableType.getFormDetailId().toString(), projectTesteeTable.getFormTableId().toString(), rowNumber, medicalType, openOCR, "", "", true, "");
                projectTesteeTableVo.setVariableImageList(variableImageList);
            }
            // 字段组 返回resourceVariableId resourceTableId
            if(projectTesteeTable.getResourceVariableId() != null){
                projectTesteeTableVo.setResourceVariableId(projectTesteeTable.getResourceVariableId());
                projectTesteeTableVo.setResourceTableId(projectTesteeTable.getResourceTableId());
            }

            projectTesteeTableVo.setEnableAssociate(templateFormTableType.getEnableAssociate());
            projectTesteeTableVo.setConditionExpression(templateFormTableType.getConditionExpression());
            projectTesteeTableVo.setPointVariableId(templateFormTableType.getPointVariableId());
            projectTesteeTableVo.setExtData3(templateFormTableType.getExtData3());
            projectTesteeTableVo.setExtData4(templateFormTableType.getExtData4());
            projectTesteeTableVo.setComplateStatus(projectTesteeTable.getComplateStatus());

            List<TemplateFormDictionaryVo> templateFormDictionaryList = new ArrayList<>();
            if (BusinessConfig.PROJECT_VISIT_CRF_SYSTEM_FORM.equals(templateFormTableType.getDicResource())) {
                List<Dictionary> dictionaryList = dictionaryService.getDictionaryListByParentId(templateFormTableType.getRefDicId(), "", "");
                dictionaryList.forEach(dictionary -> {
                    TemplateFormDictionaryVo templateFormDictionaryVo = new TemplateFormDictionaryVo();
                    templateFormDictionaryVo.setId(dictionary.getId().toString());
                    templateFormDictionaryVo.setName(dictionary.getName());
                    templateFormDictionaryVo.setOptionValue(dictionary.getValue());
                    templateFormDictionaryList.add(templateFormDictionaryVo);
                });
            }
            if (BusinessConfig.PROJECT_VISIT_CRF_PRO_FORM.equals(templateFormTableType.getDicResource())) {
                List<Dictionary> dictionaryList = projectDictionaryService.getDictionaryListByParentId(templateFormTableType.getProjectId().toString(), projectTesteeTable.getRefDicId(), "", Constants.PROJECT_DIC_TYPE);
                dictionaryList.forEach(dictionary -> {
                    TemplateFormDictionaryVo templateFormDictionaryVo = new TemplateFormDictionaryVo();
                    templateFormDictionaryVo.setId(dictionary.getId().toString());
                    templateFormDictionaryVo.setName(dictionary.getName());
                    templateFormDictionaryVo.setOptionValue(dictionary.getValue());
                    templateFormDictionaryList.add(templateFormDictionaryVo);
                });
            }
            if (BusinessConfig.PROJECT_VISIT_CRF_NUMBER_FORM.equals(templateFormTableType.getDicResource())) {
                List<Dictionary> dictionaryList = projectDictionaryService.getDictionaryListByParentId(templateFormTableType.getProjectId().toString(), projectTesteeTable.getRefDicId(), "", Constants.UNIT_DIC_TYPE);
                dictionaryList.forEach(dictionary -> {
                    TemplateFormDictionaryVo templateFormDictionaryVo = new TemplateFormDictionaryVo();
                    templateFormDictionaryVo.setId(dictionary.getId().toString());
                    templateFormDictionaryVo.setName(dictionary.getName());
                    templateFormDictionaryList.add(templateFormDictionaryVo);
                });
            }
            projectTesteeTableVo.setTemplateFormDictionaryList(templateFormDictionaryList);

            //查询质疑标识
            TesteeChallengeVo testeeChallengeVo = getProjectTableResultIdChallengeStatus(projectId, visitId, projectTesteeTable.getFormId().toString(), projectTesteeTable.getId().toString(), testeeId);
            projectTesteeTableVo.setChallengeStatus(testeeChallengeVo.getChallengeStatus());
            projectTesteeTableVo.setChallengeButtonStatus(testeeChallengeVo.getChallengeButtonStatus());

            //查询逻辑核查规则（未关闭的系统质疑)
            String project_challenge_status = ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_01.getName();
            List<ProjectChallengeVo> projectChallengeList = projectTesteeChallengeService.getProjectSystemChallageData(projectId, visitId, projectTesteeTable.getFormId().toString(), testeeId, projectTesteeTable.getFormDetailId().toString(), projectTesteeTable.getFormTableId().toString(), rowNumber, queryMethod, project_challenge_status);
            if(CollectionUtil.isNotEmpty(projectChallengeList)){
                //projectTesteeTableVo.setProjectChallengeList(projectChallengeList);
            }
            dataList.add(projectTesteeTableVo);
        }
        return dataList;
    }
    
    @Override
    public List<ProjectTesteeTableVo> getProjectTesteeTableRowRecordForRowNumber(String projectId, String planId, String visitId, String formId, String testeeId, String rowNumber, String testeeGroupId) {
        return getProjectTesteeTableRowRecord(projectId, planId, visitId, formId, testeeId, null, rowNumber, "", "", testeeGroupId);
    }
    
    @Override
    public List<ProjectTesteeTableVo> getProjectTesteeTableColumnRecordList(String projectId, String planId, String visitId, String formId, String formExpandId, String formDetailId, String formTableColumnId, String testeeGroupId, String testeeId) {
        List<ProjectTesteeTableVo> dataList = new ArrayList<>();
        Map<String,Object> params = new HashMap<>();
        params.put("projectId",projectId);
        params.put("planId",planId);
        params.put("visitId",visitId);
        params.put("formId",formId);
        params.put("formExpandId",formExpandId);
        params.put("formDetailId",formDetailId);
        params.put("formTableId",formTableColumnId);
        params.put("testeeId",testeeId);
        params.put("status",BusinessConfig.VALID_STATUS);
        List<ProjectTesteeTableVo> projectTesteeTables;
        if(StringUtils.isEmpty(testeeGroupId)){
            projectTesteeTables = projectTesteeTableMapper.getProjectTesteeTableRowRecord(params);
        }else{
            projectTesteeTables = projectTesteeTableMapper.getProjectTesteeGroupTableRowRecord(params);
        }
        for (ProjectTesteeTableVo projectTesteeTable : projectTesteeTables) {
            Long formTableId = projectTesteeTable.getFormTableId();
            if(projectTesteeTable.getGroupId() != null){
                formTableId = projectTesteeTable.getResourceTableId();
            }
            TemplateFormTable templateFormTableType = getTemplateFormTableType(formTableId);
            ProjectTesteeTableVo projectTesteeTableVo = new ProjectTesteeTableVo();
            BeanUtils.copyProperties(projectTesteeTable, projectTesteeTableVo);
            projectTesteeTableVo.setTesteeResultId(projectTesteeTable.getId().toString());
            projectTesteeTableVo.setRowNumber(projectTesteeTable.getRowNumber());
            projectTesteeTableVo.setType(templateFormTableType.getType());
            projectTesteeTableVo.setExpand(templateFormTableType.getExpand());
            projectTesteeTable.setRefDicId(templateFormTableType.getRefDicId());
            if(StringUtils.isNotEmpty(templateFormTableType.getExpand())){
                Map<String, Object> dataMap = JSON.parseObject(templateFormTableType.getExpand(), new TypeReference<HashMap<String, Object>>(){}.getType());
                projectTesteeTableVo.setExpandValue(dataMap);
            }
            projectTesteeTableVo.setFieldName(projectTesteeTable.getFieldName());
            projectTesteeTableVo.setFieldValue(projectTesteeTable.getFieldValue());
            projectTesteeTableVo.setUnitValue(projectTesteeTable.getUnitValue());
            // 字段组 返回resourceVariableId resourceTableId
            if(projectTesteeTable.getResourceVariableId() != null){
                projectTesteeTableVo.setResourceVariableId(projectTesteeTable.getResourceVariableId());
                projectTesteeTableVo.setResourceTableId(projectTesteeTable.getResourceTableId());
            }
            projectTesteeTableVo.setEnableAssociate(templateFormTableType.getEnableAssociate());
            projectTesteeTableVo.setConditionExpression(templateFormTableType.getConditionExpression());
            projectTesteeTableVo.setPointVariableId(templateFormTableType.getPointVariableId());
            projectTesteeTableVo.setExtData3(templateFormTableType.getExtData3());
            projectTesteeTableVo.setExtData4(templateFormTableType.getExtData4());
            projectTesteeTableVo.setComplateStatus(projectTesteeTable.getComplateStatus());
            dataList.add(projectTesteeTableVo);
        }
        return dataList;
    }

    @Override
    public List<ProjectTesteeFormDetailTableVo> getProjectTesteeFormDetailTableRow(String projectId, String visitId, String formId, String formDetailId, String testeeId, String testeeGroupId, String tableSort) {
        List<Long> rowNumberList = this.getProjectTesteeTableRowNumber(projectId, "", visitId, formId, "", formDetailId, testeeId, "0", null, tableSort, "");
        List<ProjectTesteeFormDetailTableVo> dataList = new ArrayList<>();
        for (Long rowNumber : rowNumberList) {
            ProjectTesteeFormDetailTableVo projectTesteeFormDetailTableVo = new ProjectTesteeFormDetailTableVo();
            List<ProjectTesteeTableWrapperVo> projectTesteeTableList = this.getProjectTesteeTableListForPage(projectId, "", visitId, formId, "", formDetailId, testeeId, rowNumber);
            projectTesteeFormDetailTableVo.setRowNumber(rowNumber.toString());
            List<ProjectTesteeTableVo> tableRowList = new ArrayList<>();
            for (ProjectTesteeTableWrapperVo projectTesteeTable : projectTesteeTableList) {
                ProjectTesteeTableVo projectTesteeTableVo = new ProjectTesteeTableVo();
                BeanUtils.copyProperties(projectTesteeTable, projectTesteeTableVo);
                projectTesteeTableVo.setUnitValue(projectTesteeTable.getUnitValue());
                projectTesteeTableVo.setFieldName(projectTesteeTable.getFieldName());
                projectTesteeTableVo.setFieldValue(projectTesteeTable.getFieldValue());
                tableRowList.add(projectTesteeTableVo);
            }
            projectTesteeFormDetailTableVo.setTableRowList(tableRowList);
            dataList.add(projectTesteeFormDetailTableVo);
        }
        return dataList;
    }

    private TemplateFormTable getTemplateFormTableType(Long templateFormTableId){
        return templateFormTableMapper.selectByPrimaryKey(templateFormTableId);
    }

    @Override
    public int saveProjectTesteeTableRowRecord(ProjectTesteeTable projectTesteeTable) {
        return projectTesteeTableMapper.insert(projectTesteeTable);
    }

    @Override
    public int saveBatchProjectTesteeTableRowRecord(List<ProjectTesteeTable> tableRowList) {
        return projectTesteeTableMapper.saveBatchProjectTesteeTableRowRecord(tableRowList);
    }

    @Override
    public List<ProjectTesteeTableWrapperVo> getProjectTesteeTableListForPage(String projectId, String planId, String visitId, String formId, String formExpandId, String formDetailId, String testeeId, Long rowNumber) {
        Map<String, Object> params = new HashMap<>();
        params.put("projectId", projectId);
        params.put("planId", planId);
        params.put("visitId", visitId);
        params.put("formId", formId);
        if(StringUtils.isNotEmpty(formExpandId)){params.put("formExpandId", formExpandId);}
        if(StringUtils.isNotEmpty(formDetailId)){params.put("formDetailId", formDetailId);}
        params.put("testeeId", testeeId);
        if (rowNumber != null) {params.put("rowNumber", rowNumber);}
        return projectTesteeTableMapper.getProjectTesteeTableListForPage(params);
    }

    @Override
    public List<ProjectTesteeTableWrapperVo> getProjectTesteeGroupTableListForPage(String projectId, String visitId, String formId, String groupDetailId, String testeeId, Long rowNumber) {
        Map<String, Object> params = new HashMap<>();
        params.put("projectId", projectId);
        params.put("visitId", visitId);
        params.put("formId", formId);
        params.put("formDetailId", groupDetailId);
        params.put("testeeId", testeeId);
        params.put("rowNumber", rowNumber);
        return projectTesteeTableMapper.getProjectTesteeGroupTableListForPage(params);
    }

    @Override
    public List<Long> getProjectTesteeTableRowNumber(String projectId, String planId, String visitId, String formId, String formExpandId, String formDetailId, String testeeId, String queryFormGroup, String taskDate, String tableSort, String groupId) {
        if(StringUtils.isEmpty(tableSort)){tableSort="1";}
        if(StringUtils.isEmpty(queryFormGroup)){queryFormGroup="0";}
        return projectTesteeTableMapper.getProjectTesteeTableRowNumber(projectId, planId, visitId, formId, formExpandId, formDetailId, testeeId, queryFormGroup, taskDate, tableSort, groupId);
    }

    @Override
    public void updateProjectTesteeTableRowRecord(ProjectTesteeTable projectTesteeTable) {
        projectTesteeTableMapper.updateByPrimaryKeySelective(projectTesteeTable);
    }

    @Override
    public ProjectTesteeFormTableConfigVo getProjectTesteeFormTableConfig(String projectId, String planId, String visitId, String formId, String formDetailId, String testeeGroupId, String testeeId) {
        ProjectTesteeFormTableConfigVo projectTesteeFormTableConfigVo = new ProjectTesteeFormTableConfigVo();
        List<ProjectTesteeTableVo> tableRowConfigList = new ArrayList<>();
        List<TemplateFormDetailVo> formConfigList = new ArrayList<>();
        if(StringUtils.isEmpty(testeeGroupId)){
            TemplateFormTableExample example = new TemplateFormTableExample();
            TemplateFormTableExample.Criteria criteria = example.createCriteria();
            criteria.andFormIdEqualTo(Long.parseLong(formId));
            criteria.andFormDetailIdEqualTo(Long.parseLong(formDetailId));
            criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
            example.setOrderByClause("sort asc, create_time asc");
            List<TemplateFormTable> templateFormTables = templateFormTableMapper.selectByExample(example);
            for (TemplateFormTable templateFormTable : templateFormTables) {
                ProjectTesteeTableVo projectTesteeTableVo = new ProjectTesteeTableVo();
                BeanUtils.copyProperties(templateFormTable, projectTesteeTableVo);
                projectTesteeTableVo.setOptions(templateFormTable.getExpand());
                projectTesteeTableVo.setFormDetailId(Long.parseLong(formDetailId));

                //查询表格列的计算规则
                TemplateFormVariableRule tRule = variableRuleService.getFormVariableRule(projectId,null, formId, templateFormTable.getId().toString());
                projectTesteeTableVo.setVariableRule(tRule);

                // 解析扩展属性
                if(StringUtils.isNotEmpty(templateFormTable.getExpand())){
                    Map<String, Object> dataMap = JSON.parseObject(templateFormTable.getExpand(), new TypeReference<HashMap<String, Object>>(){}.getType());
                    projectTesteeTableVo.setExpandValue(dataMap);
                }
                if(StringUtils.isNotEmpty(templateFormTable.getDicResource())) {
                    List<TemplateFormDictionaryVo> templateFormDictionaryList = new ArrayList<>();
                    if (BusinessConfig.PROJECT_VISIT_CRF_SYSTEM_FORM.equals(templateFormTable.getDicResource())) {
                        List<Dictionary> dictionaryList = dictionaryService.getDictionaryListByParentId(templateFormTable.getRefDicId(), "", "");
                        dictionaryList.forEach(dictionary -> {
                            TemplateFormDictionaryVo templateFormDictionaryVo = new TemplateFormDictionaryVo();
                            templateFormDictionaryVo.setId(dictionary.getId().toString());
                            templateFormDictionaryVo.setName(dictionary.getName());
                            templateFormDictionaryVo.setOptionValue(dictionary.getValue());
                            templateFormDictionaryList.add(templateFormDictionaryVo);
                        });

                    }
                    if (BusinessConfig.PROJECT_VISIT_CRF_PRO_FORM.equals(templateFormTable.getDicResource())) {
                        List<Dictionary> dictionaryList = projectDictionaryService.getDictionaryListByParentId(templateFormTable.getProjectId().toString(), templateFormTable.getRefDicId(), "", Constants.PROJECT_DIC_TYPE);
                        dictionaryList.forEach(dictionary -> {
                            TemplateFormDictionaryVo templateFormDictionaryVo = new TemplateFormDictionaryVo();
                            templateFormDictionaryVo.setId(dictionary.getId().toString());
                            templateFormDictionaryVo.setName(dictionary.getName());
                            templateFormDictionaryVo.setOptionValue(dictionary.getValue());
                            templateFormDictionaryList.add(templateFormDictionaryVo);
                        });

                        //设置默认值
                        List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT);
                        if(variableTypeList.contains(templateFormTable.getType())){
                            String defaultDicValue = templateFormTable.getDefaultDicValue();
                            if(StringUtils.isNotEmpty(defaultDicValue)){
                                ProjectDictionary dictionaryInfo = projectDictionaryService.getDictionaryInfo(defaultDicValue);
                                if(dictionaryInfo != null){
                                    projectTesteeTableVo.setUnitValue(dictionaryInfo.getName());
                                }
                            }
                        }
                    }
                    if (BusinessConfig.PROJECT_VISIT_CRF_NUMBER_FORM.equals(templateFormTable.getDicResource())) {
                        List<Dictionary> dictionaryList = projectDictionaryService.getDictionaryListByParentId(templateFormTable.getProjectId().toString(), templateFormTable.getRefDicId(), "", Constants.UNIT_DIC_TYPE);
                        dictionaryList.forEach(dictionary -> {
                            TemplateFormDictionaryVo templateFormDictionaryVo = new TemplateFormDictionaryVo();
                            templateFormDictionaryVo.setId(dictionary.getId().toString());
                            templateFormDictionaryVo.setName(dictionary.getName());
                            templateFormDictionaryList.add(templateFormDictionaryVo);
                        });
                    }
                    projectTesteeTableVo.setTemplateFormDictionaryList(templateFormDictionaryList);
                }

                //设置表格变量逻辑核查条件
                List<TemplateFormDvpRuleVo> tableDetailDvpContentList = projectTesteeDvpService.getTableDetailDvpContent(projectId, visitId, formId, formDetailId, templateFormTable.getId().toString());
                if(CollectionUtil.isNotEmpty(tableDetailDvpContentList)){
                    projectTesteeTableVo.setTemplateFormDvpRuleList(tableDetailDvpContentList);
                }
                tableRowConfigList.add(projectTesteeTableVo);
            }
        }else{
            List<TemplateFormGroupTable> templateGroupTableRowList = templateFormGroupService.getTemplateGroupTableRowHeader(projectId, planId, visitId, formId, formDetailId, testeeGroupId, "", testeeId);
            for (TemplateFormGroupTable templateFormGroupTable : templateGroupTableRowList) {
                ProjectTesteeTableVo projectTesteeTableVo = new ProjectTesteeTableVo();
                TemplateFormTable templateFormTable = this.getTemplateFormTableType(templateFormGroupTable.getResourceTableId());
                BeanUtils.copyProperties(templateFormTable, projectTesteeTableVo);
                projectTesteeTableVo.setFormDetailId(templateFormGroupTable.getFormDetailId());
                projectTesteeTableVo.setResourceVariableId(templateFormGroupTable.getResourceVariableId());
                projectTesteeTableVo.setResourceTableId(templateFormGroupTable.getResourceTableId());
                projectTesteeTableVo.setId(templateFormGroupTable.getId());
                projectTesteeTableVo.setExpand(templateFormTable.getExpand());
                if(StringUtils.isNotEmpty(templateFormTable.getExpand())){
                    Map<String, Object> dataMap = JSON.parseObject(templateFormTable.getExpand(), new TypeReference<HashMap<String, Object>>(){}.getType());
                    projectTesteeTableVo.setExpandValue(dataMap);
                }

                //查询表格列的计算规则
                TemplateFormVariableRule tRule = variableRuleService.getFormVariableRule(projectId,null, formId, templateFormTable.getId().toString());
                projectTesteeTableVo.setVariableRule(tRule);

                if(StringUtils.isNotEmpty(templateFormTable.getDicResource())) {
                    List<TemplateFormDictionaryVo> templateFormDictionaryList = new ArrayList<>();
                    if (BusinessConfig.PROJECT_VISIT_CRF_SYSTEM_FORM.equals(templateFormTable.getDicResource())) {
                        List<Dictionary> dictionaryList = dictionaryService.getDictionaryListByParentId(templateFormTable.getRefDicId(), "", "");
                        dictionaryList.forEach(dictionary -> {
                            TemplateFormDictionaryVo templateFormDictionaryVo = new TemplateFormDictionaryVo();
                            templateFormDictionaryVo.setId(dictionary.getId().toString());
                            templateFormDictionaryVo.setName(dictionary.getName());
                            templateFormDictionaryList.add(templateFormDictionaryVo);
                        });

                    }
                    if (BusinessConfig.PROJECT_VISIT_CRF_PRO_FORM.equals(templateFormTable.getDicResource())) {
                        List<Dictionary> dictionaryList = projectDictionaryService.getDictionaryListByParentId(templateFormTable.getProjectId().toString(), templateFormTable.getRefDicId(), "", Constants.PROJECT_DIC_TYPE);
                        dictionaryList.forEach(dictionary -> {
                            TemplateFormDictionaryVo templateFormDictionaryVo = new TemplateFormDictionaryVo();
                            templateFormDictionaryVo.setId(dictionary.getId().toString());
                            templateFormDictionaryVo.setName(dictionary.getName());
                            templateFormDictionaryList.add(templateFormDictionaryVo);
                        });
                    }
                    if (BusinessConfig.PROJECT_VISIT_CRF_NUMBER_FORM.equals(templateFormTable.getDicResource())) {
                        List<Dictionary> dictionaryList = projectDictionaryService.getDictionaryListByParentId(templateFormTable.getProjectId().toString(), templateFormTable.getRefDicId(), "", Constants.UNIT_DIC_TYPE);
                        dictionaryList.forEach(dictionary -> {
                            TemplateFormDictionaryVo templateFormDictionaryVo = new TemplateFormDictionaryVo();
                            templateFormDictionaryVo.setId(dictionary.getId().toString());
                            templateFormDictionaryVo.setName(dictionary.getName());
                            templateFormDictionaryList.add(templateFormDictionaryVo);
                        });
                    }
                    projectTesteeTableVo.setTemplateFormDictionaryList(templateFormDictionaryList);
                }
                //设置表格变量逻辑核查条件
                List<TemplateFormDvpRuleVo> tableDetailDvpContentList = projectTesteeDvpService.getTableDetailDvpContent(projectId, visitId, formId, formDetailId, templateFormTable.getId().toString());
                if(CollectionUtil.isNotEmpty(tableDetailDvpContentList)){
                    projectTesteeTableVo.setTemplateFormDvpRuleList(tableDetailDvpContentList);
                }
                tableRowConfigList.add(projectTesteeTableVo);
            }
        }

        projectTesteeFormTableConfigVo.setTableConfigRow(tableRowConfigList);
        TemplateFormDetailExample formDetailExample = new TemplateFormDetailExample();
        TemplateFormDetailExample.Criteria criteria1 = formDetailExample.createCriteria();
        criteria1.andFormIdEqualTo(Long.parseLong(formId));
        criteria1.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        List<TemplateFormDetail> templateFormDetails = templateFormDetailMapper.selectByExample(formDetailExample);
        for (TemplateFormDetail templateFormDetail : templateFormDetails) {
            TemplateFormDetailVo templateFormDetailVo = new TemplateFormDetailVo();
            BeanUtils.copyProperties(templateFormDetail, templateFormDetailVo);
            formConfigList.add(templateFormDetailVo);
        }
        projectTesteeFormTableConfigVo.setFormConfig(formConfigList);
        return projectTesteeFormTableConfigVo;
    }

    @Override
    public List<ProjectTesteeTableVo> getProjectTesteeTableRowFieldRecord(String projectId, String tableId) {
        return projectTesteeTableMapper.getProjectTesteeTableRowFieldRecord(projectId, tableId);
    }

    @Override
    public List<TemplateTableVo> getProjectTesteeTableRowHead(String formId, String formDetailId, Boolean queryIgnoreStatus) {
        List<TemplateTableVo> dataList = new ArrayList<>();
        TemplateFormTableExample example = new TemplateFormTableExample();
        TemplateFormTableExample.Criteria criteria = example.createCriteria();
        if(StringUtils.isNotEmpty(formId)){
            criteria.andFormIdEqualTo(Long.parseLong(formId));
        }
        criteria.andFormDetailIdEqualTo(Long.parseLong(formDetailId));
        if(!queryIgnoreStatus){criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);}
        example.setOrderByClause("sort asc, create_time asc");
        List<TemplateFormTable> projectTesteeTableList = templateFormTableMapper.selectByExample(example);
        for (TemplateFormTable templateFormTable : projectTesteeTableList) {
            TemplateTableVo templateTableVo = new TemplateTableVo();
            BeanUtils.copyProperties(templateFormTable, templateTableVo);
            dataList.add(templateTableVo);
        }
        return dataList;
    }


    @Override
    public TesteeChallengeVo getProjectTableRowChallengeStatus(String projectId, String visitId, String formId, String tableRowNo, String testeeId){
        TesteeChallengeVo testeeChallengeVo = new TesteeChallengeVo();
        List<String> challengeNoApplyList = new ArrayList<>();
        List<String> challengeApplyedList = new ArrayList<>();
        List<String> challengeClosedList = new ArrayList<>();
        List<ProjectTesteeChallenge> challengelist = projectTesteeChallengeService.getProjectChallengeListByTableRowNo(projectId, visitId, formId, tableRowNo, testeeId);
        for (ProjectTesteeChallenge projectTesteeChallenge : challengelist) {
            if(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_01.getName().equals(projectTesteeChallenge.getReplyCloseStatus())){
                challengeNoApplyList.add(projectTesteeChallenge.getId().toString());
            }
            if(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName().equals(projectTesteeChallenge.getReplyCloseStatus())){
                challengeApplyedList.add(projectTesteeChallenge.getId().toString());
            }
            if(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_03.getName().equals(projectTesteeChallenge.getReplyCloseStatus())){
                challengeClosedList.add(projectTesteeChallenge.getId().toString());
            }
        }
        if(challengelist.size() == 0){
            testeeChallengeVo.setChallengeButtonStatus(ProjectChallengeButtonEnum.PROJECT_CHALLENGE_BUTTON_STATUS_01.getName());
        }
        if(challengelist.size() == 0  || challengeNoApplyList.size() > 0){
            testeeChallengeVo.setChallengeStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_01.getName());
        }
        if(challengeNoApplyList.size() == 0 && challengeApplyedList.size() >0){
            testeeChallengeVo.setChallengeStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName());
            testeeChallengeVo.setChallengeButtonStatus(ProjectChallengeButtonEnum.PROJECT_CHALLENGE_BUTTON_STATUS_02.getName());
        }
        if(challengelist.size() >0 && challengelist.size() == challengeClosedList.size()){
            testeeChallengeVo.setChallengeStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_03.getName());
            testeeChallengeVo.setChallengeButtonStatus(ProjectChallengeButtonEnum.PROJECT_CHALLENGE_BUTTON_STATUS_02.getName());
        }
        return testeeChallengeVo;
    }

    @Override
    public TesteeChallengeVo getProjectTableResultIdChallengeStatus(String projectId, String visitId, String formId, String tableResultId, String testeeId){
        TesteeChallengeVo testeeChallengeVo = new TesteeChallengeVo();
        List<String> challengeNoApplyList = new ArrayList<>();
        List<String> challengeApplyedList = new ArrayList<>();
        List<String> challengeClosedList = new ArrayList<>();
        List<ProjectChallengeVo> challengelist = projectTesteeChallengeService.getProjectChallengeListByTableId(projectId, visitId, formId, tableResultId, testeeId);
        for (ProjectChallengeVo projectTesteeChallenge : challengelist) {
            if(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_01.getName().equals(projectTesteeChallenge.getReplyCloseStatus())){
                challengeNoApplyList.add(projectTesteeChallenge.getId().toString());
            }
            if(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName().equals(projectTesteeChallenge.getReplyCloseStatus())){
                challengeApplyedList.add(projectTesteeChallenge.getId().toString());
            }
            if(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_03.getName().equals(projectTesteeChallenge.getReplyCloseStatus())){
                challengeClosedList.add(projectTesteeChallenge.getId().toString());
            }
        }
        if(challengelist.size() == 0){
            testeeChallengeVo.setChallengeButtonStatus(ProjectChallengeButtonEnum.PROJECT_CHALLENGE_BUTTON_STATUS_01.getName());
        }
        if(challengelist.size() == 0  || challengeNoApplyList.size() > 0){
            testeeChallengeVo.setChallengeStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_01.getName());
        }
        if(challengeNoApplyList.size() == 0 && challengeApplyedList.size() >0){
            testeeChallengeVo.setChallengeStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName());
            testeeChallengeVo.setChallengeButtonStatus(ProjectChallengeButtonEnum.PROJECT_CHALLENGE_BUTTON_STATUS_02.getName());
        }
        if(challengelist.size() >0 && challengelist.size() == challengeClosedList.size()){
            testeeChallengeVo.setChallengeStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_03.getName());
            testeeChallengeVo.setChallengeButtonStatus(ProjectChallengeButtonEnum.PROJECT_CHALLENGE_BUTTON_STATUS_02.getName());
        }
        return testeeChallengeVo;
    }

    @Override
    public List<ProjectTesteeTable> selectByExample(ProjectTesteeTableExample exampleTable) {
        return projectTesteeTableMapper.selectByExample(exampleTable);
    }

    @Override
    public ProjectTesteeTable getProjectTesteeTableRowRecordByTableId(Long id) {
        return projectTesteeTableMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<ProjectTesteeTableRowDataVo> getProjectTesteeTableRowDataList(String projectId, String planId, String visitId, String formId, String formDetailId, String testeeId) {
        List<ProjectTesteeTableRowDataVo> dataList = new ArrayList<>();
        List<Long> projectTesteeTableRowNumber = projectTesteeTableMapper.getProjectTesteeTableRowNumber(projectId, "", visitId, formId, "", formDetailId, testeeId, "", "", "1", "");
        for (Long rowNumber : projectTesteeTableRowNumber) {
            List<String> complateCount = new ArrayList<>();
            ProjectTesteeTableRowDataVo projectTesteeTableRowDataVo = new ProjectTesteeTableRowDataVo();
            Map<String, Object> params = getProjectTesteeTableRowRequestMap(projectId, planId, visitId, formId, formDetailId, testeeId, rowNumber);
            List<ProjectTesteeTableVo> projectTesteeTableList = projectTesteeTableMapper.getProjectTesteeTableRowRecord(params);
            List<ProjectTesteeTableRowDataVo.TesteeTableRow> rowList = new ArrayList<>();
            projectTesteeTableRowDataVo.setProjectId(Long.parseLong(projectId));
            projectTesteeTableRowDataVo.setVisitId(Long.parseLong(visitId));
            projectTesteeTableRowDataVo.setFormId(Long.parseLong(formId));
            projectTesteeTableRowDataVo.setFormDetailId(Long.parseLong(formDetailId));
            projectTesteeTableRowDataVo.setTesteeId(Long.parseLong(testeeId));
            projectTesteeTableRowDataVo.setRowNumber(rowNumber);
            for (ProjectTesteeTableVo projectTesteeTable : projectTesteeTableList) {
                ProjectTesteeTableRowDataVo.TesteeTableRow testeeTableRow = new ProjectTesteeTableRowDataVo.TesteeTableRow();
                BeanUtils.copyProperties(projectTesteeTable, testeeTableRow);
                testeeTableRow.setValue(projectTesteeTable.getFieldValue());
                rowList.add(testeeTableRow);
                if(projectTesteeTable.getComplateStatus().equals(FormVariableComplateStatus.FORM_VAR_FILL_SUCCESS.getCode())){
                    complateCount.add(projectTesteeTable.getComplateStatus());
                }
            }
            if(complateCount.size() == projectTesteeTableList.size()){
                projectTesteeTableRowDataVo.setComplateStatus(FormVariableComplateStatus.FORM_VAR_FILL_SUCCESS.getCode());
            }
            if(complateCount.size() > projectTesteeTableList.size()){
                projectTesteeTableRowDataVo.setComplateStatus(FormVariableComplateStatus.FORM_VAR_FILL.getCode());
            }
            projectTesteeTableRowDataVo.setRowList(rowList);
            dataList.add(projectTesteeTableRowDataVo);
        }
        return dataList;
    }

    @NotNull
    public Map<String, Object> getProjectTesteeTableRowRequestMap(String projectId, String planId, String visitId, String formId, String formDetailId, String testeeId, Long rowNumber) {
        Map<String,Object> params = new HashMap<>();
        params.put("projectId", projectId);
        if(StringUtils.isNotEmpty(planId)){params.put("planId", planId);}
        params.put("visitId", visitId);
        params.put("formId", formId);
        params.put("formDetailId", formDetailId);
        params.put("testeeId", testeeId);
        params.put("rowNumber", rowNumber);
        params.put("status",BusinessConfig.VALID_STATUS);
        return params;
    }

    @Override
    public List<ProjectTesteeTableResultExportVo> getProjectTesteeTableRecordsWithVariableIds(String projectId, String projectOrgId, String visitId, String formId, String formDetailId, String formTableId, String testeeId) {
        return projectTesteeTableMapper.getProjectTesteeTableRecordsWithVariableIds(projectId, projectOrgId, visitId, formId, formDetailId, formTableId, testeeId);
    }

    @Override
    public List<ProjectTesteeFormAndTableResultExportVo> getProjectTesteeFormAndTableResultByVariableIds(String projectId, String visitId, String formId, String formDetailId, String formTableId, String testeeId, String orgId) {
        return projectTesteeTableMapper.getProjectTesteeFormAndTableResultByVariableIds(projectId, visitId, formId, formDetailId, formTableId, testeeId, orgId);
    }

    @Override
    public List<ProjectTesteeFormAndTableCountVo> getProjectTesteeFormAndTableCountByVariableIds(String projectId, String visitId, String formId, String formDetailId, String testeeId, String orgId) {
        return projectTesteeTableMapper.getProjectTesteeFormAndTableCountByVariableIds(projectId, visitId, formId, formDetailId, testeeId, orgId);
    }

    /**
     * 获取题组的数据。
     *
     * @param projectId 项目id
     * @param visitId   访视id
     */
    @Override
    public List<ProjectTesteeTableResultExportVo> getProjectTesteeGroupVariableAndTable(String projectId, String projectOrgId, String visitId, String testeeId, String fromId) {
        return projectTesteeTableMapper.getProjectTesteeGroupVariableAndTable(projectId,visitId,testeeId,fromId, projectOrgId);
    }

    @Override
    public List<ProjectTesteeTableResultExportVo> getProjectTesteeGroupTableResult(String projectId, String visitId, String fromId, String formTableId, String testeeId, String orgId) {
        return projectTesteeTableMapper.getProjectTesteeGroupTableResult(projectId,visitId,fromId,formTableId,testeeId,orgId);

    }

    @Override
    @ProjectVariableRecordFlag(paramterType = BusinessConfig.PARAMTER_TYPE_01, formType = BusinessConfig.PROJECT_VISIT_CRF_TABLE)
    public CustomResult deleteTesteeTableRowRecord(String rowNumber, String projectId, String planId, String visitId, String formId, String testeeGroupId, String testeeId) {
        CustomResult customResult = new CustomResult();
        //根据编号删除行记录
        Map<String,Object> params = new HashMap<>(7);
        params.put("projectId",projectId);
        params.put("planId",planId);
        params.put("visitId",visitId);
        params.put("formId",formId);
        params.put("testeeId",testeeId);
        params.put("rowNumber",rowNumber);
        params.put("status",BusinessConfig.VALID_STATUS);
        List<ProjectTesteeTableVo> projectTesteeTables;
        if(StringUtils.isEmpty(testeeGroupId)){
            projectTesteeTables = projectTesteeTableMapper.getProjectTesteeTableRowRecord(params);
        }else{
            projectTesteeTables = projectTesteeTableMapper.getProjectTesteeGroupTableRowRecord(params);
        }
        for (ProjectTesteeTableVo projectTesteeTableVo : projectTesteeTables){
            ProjectTesteeTable projectTesteeTable = new ProjectTesteeTable();
            BeanUtils.copyProperties(projectTesteeTableVo, projectTesteeTable);
            projectTesteeTable.setStatus(BusinessConfig.NO_VALID_STATUS);
            projectTesteeTable.setUpdateTime(new Date());
            projectTesteeTableMapper.updateByPrimaryKeySelective(projectTesteeTable);
        }
        return customResult;
    }

    @Override
    @ProjectVariableRecordFlag(paramterType = BusinessConfig.PARAMTER_TYPE_01, formType = BusinessConfig.PROJECT_VISIT_CRF_TABLE)
    public CustomResult deleteTesteeTableRecord(String projectId, String visitId, String formId, String formDetailId, String testeeGroupId, String testeeId) {
        // 根据表格id删除整个表格记录
        CustomResult customResult = new CustomResult();
        String queryFormGroup = "0";
        if(StringUtils.isNotEmpty(testeeGroupId)){queryFormGroup = "1";}
        List<Long> projectTesteeTableRowNumber = projectTesteeTableMapper.getProjectTesteeTableRowNumber(projectId, "", visitId, formId, "", formDetailId, testeeId, queryFormGroup, "", "1", testeeGroupId);
        for (Long rowNumber : projectTesteeTableRowNumber) {
            deleteTesteeTableRowRecord(rowNumber.toString(), projectId, "", visitId, formId, testeeGroupId, testeeId);
        }
        return customResult;
    }

}
