package com.haoys.user.domain.vo.ecrf;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ProjectTesteeTableWrapperVo implements Serializable {

    @ApiModelProperty(value = "记录id")
    private Long id;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @ApiModelProperty(value = "表单项id")
    private Long formId;

    @ApiModelProperty(value = "表格id")
    private Long formDetailId;
    
    private Long resourceVariableId;

    @ApiModelProperty(value = "表格单元格所在列id")
    private Long formTableId;
    
    @ApiModelProperty(value = "字段组模板表格列所在id")
    private Long resourceTableId;

    @ApiModelProperty(value = "参与者id")
    private Long testeeId;

    @ApiModelProperty(value = "记录行号")
    private Long rowNo;

    @ApiModelProperty(value = "字段名称")
    private String label;

    private String type;

    @ApiModelProperty(value = "字段key")
    private String fieldName;

    @ApiModelProperty(value = "完成状态 1-未录入 2-录入中 3-已完成")
    private String complateStatus;

    @ApiModelProperty(value = "数据状态")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @ApiModelProperty(value = "表格行记录提交记录")
    private String fieldValue;
    
    @ApiModelProperty(value = "字段文本值")
    private String fieldText = "";

    @ApiModelProperty(value = "表单录入值计量单位")
    private String unitValue = "";
    
    @ApiModelProperty(value = "单位文本值")
    private String unitText = "";

    @ApiModelProperty(value = "字典来源(1-系统字典2-表单字典 3-数据单位)")
    private String dicResource;

    @ApiModelProperty(value = "引用字典id")
    private String refDicId;

    @ApiModelProperty(value = "字典默认值")
    private String defaultDicValue;

    @ApiModelProperty(value = "是否开启选项联动控制")
    private Boolean enableViewConfig = false;
    
    @ApiModelProperty(value = "实验室配置指定范围 visit-访视 form-表单")
    private String labConfigScope;

}
