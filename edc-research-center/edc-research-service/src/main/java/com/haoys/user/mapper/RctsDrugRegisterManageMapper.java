package com.haoys.user.mapper;

import com.haoys.user.domain.vo.rcts.DrugRegisterVo;
import com.haoys.user.model.RctsDrugRegisterManage;
import com.haoys.user.model.RctsDrugRegisterManageExample;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

public interface RctsDrugRegisterManageMapper {
    long countByExample(RctsDrugRegisterManageExample example);

    int deleteByExample(RctsDrugRegisterManageExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RctsDrugRegisterManage record);

    int insertSelective(RctsDrugRegisterManage record);

    List<RctsDrugRegisterManage> selectByExample(RctsDrugRegisterManageExample example);

    RctsDrugRegisterManage selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RctsDrugRegisterManage record, @Param("example") RctsDrugRegisterManageExample example);

    int updateByExample(@Param("record") RctsDrugRegisterManage record, @Param("example") RctsDrugRegisterManageExample example);

    int updateByPrimaryKeySelective(RctsDrugRegisterManage record);

    int updateByPrimaryKey(RctsDrugRegisterManage record);

    List<DrugRegisterVo> getDrugRegisterPage(Map<String, Object> params);

    @Update(" UPDATE rcts_drug_register_manage SET min_package_unit = #{nowMinPackageUnit} WHERE id = #{id} ")
    int updateDrugMinPackageUnit(@Param("nowMinPackageUnit") int nowMinPackageUnit, @Param("id") long id);

    @Select(" SELECT concat(id,'') id , material_code materialCode FROM rcts_drug_register_manage WHERE STATUS = '0' AND material_status = 's03' AND  project_id = #{projectId} ")
    List<Map<String, String>> getMaterialCode(@Param("projectId") String projectId);

    @Update(" UPDATE rcts_drug_register_manage dm SET warning_status = (CASE IFNULL(warning_status, '') WHEN '' THEN #{warningStatus} ELSE CONCAT(warning_status,',',#{warningStatus}) END ) WHERE dm.status = '0' AND dm.expiration_warning_value > 0 AND dm.expiration_end_date < ( DATE_ADD(CURDATE(), INTERVAL dm.expiration_warning_value DAY)) AND (dm.warning_status IS NULL OR dm.warning_status NOT LIKE CONCAT('%',#{warningStatus},'%')) ")
    int updateExpirationDrug(@Param("warningStatus") String warningStatus);

}