package com.haoys.user.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class ProjectExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andCodeIsNull() {
            addCriterion("code is null");
            return (Criteria) this;
        }

        public Criteria andCodeIsNotNull() {
            addCriterion("code is not null");
            return (Criteria) this;
        }

        public Criteria andCodeEqualTo(String value) {
            addCriterion("code =", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotEqualTo(String value) {
            addCriterion("code <>", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThan(String value) {
            addCriterion("code >", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThanOrEqualTo(String value) {
            addCriterion("code >=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThan(String value) {
            addCriterion("code <", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThanOrEqualTo(String value) {
            addCriterion("code <=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLike(String value) {
            addCriterion("code like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotLike(String value) {
            addCriterion("code not like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeIn(List<String> values) {
            addCriterion("code in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotIn(List<String> values) {
            addCriterion("code not in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeBetween(String value1, String value2) {
            addCriterion("code between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotBetween(String value1, String value2) {
            addCriterion("code not between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNull() {
            addCriterion("project_type is null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIsNotNull() {
            addCriterion("project_type is not null");
            return (Criteria) this;
        }

        public Criteria andProjectTypeEqualTo(String value) {
            addCriterion("project_type =", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotEqualTo(String value) {
            addCriterion("project_type <>", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThan(String value) {
            addCriterion("project_type >", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeGreaterThanOrEqualTo(String value) {
            addCriterion("project_type >=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThan(String value) {
            addCriterion("project_type <", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLessThanOrEqualTo(String value) {
            addCriterion("project_type <=", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeLike(String value) {
            addCriterion("project_type like", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotLike(String value) {
            addCriterion("project_type not like", value, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeIn(List<String> values) {
            addCriterion("project_type in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotIn(List<String> values) {
            addCriterion("project_type not in", values, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeBetween(String value1, String value2) {
            addCriterion("project_type between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andProjectTypeNotBetween(String value1, String value2) {
            addCriterion("project_type not between", value1, value2, "projectType");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNull() {
            addCriterion("start_date is null");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNotNull() {
            addCriterion("start_date is not null");
            return (Criteria) this;
        }

        public Criteria andStartDateEqualTo(Date value) {
            addCriterion("start_date =", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotEqualTo(Date value) {
            addCriterion("start_date <>", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThan(Date value) {
            addCriterion("start_date >", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThanOrEqualTo(Date value) {
            addCriterion("start_date >=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThan(Date value) {
            addCriterion("start_date <", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThanOrEqualTo(Date value) {
            addCriterion("start_date <=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateIn(List<Date> values) {
            addCriterion("start_date in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotIn(List<Date> values) {
            addCriterion("start_date not in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateBetween(Date value1, Date value2) {
            addCriterion("start_date between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotBetween(Date value1, Date value2) {
            addCriterion("start_date not between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNull() {
            addCriterion("end_date is null");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNotNull() {
            addCriterion("end_date is not null");
            return (Criteria) this;
        }

        public Criteria andEndDateEqualTo(Date value) {
            addCriterion("end_date =", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotEqualTo(Date value) {
            addCriterion("end_date <>", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThan(Date value) {
            addCriterion("end_date >", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThanOrEqualTo(Date value) {
            addCriterion("end_date >=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThan(Date value) {
            addCriterion("end_date <", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThanOrEqualTo(Date value) {
            addCriterion("end_date <=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIn(List<Date> values) {
            addCriterion("end_date in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotIn(List<Date> values) {
            addCriterion("end_date not in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateBetween(Date value1, Date value2) {
            addCriterion("end_date between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotBetween(Date value1, Date value2) {
            addCriterion("end_date not between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andIfPublicIsNull() {
            addCriterion("if_public is null");
            return (Criteria) this;
        }

        public Criteria andIfPublicIsNotNull() {
            addCriterion("if_public is not null");
            return (Criteria) this;
        }

        public Criteria andIfPublicEqualTo(Boolean value) {
            addCriterion("if_public =", value, "ifPublic");
            return (Criteria) this;
        }

        public Criteria andIfPublicNotEqualTo(Boolean value) {
            addCriterion("if_public <>", value, "ifPublic");
            return (Criteria) this;
        }

        public Criteria andIfPublicGreaterThan(Boolean value) {
            addCriterion("if_public >", value, "ifPublic");
            return (Criteria) this;
        }

        public Criteria andIfPublicGreaterThanOrEqualTo(Boolean value) {
            addCriterion("if_public >=", value, "ifPublic");
            return (Criteria) this;
        }

        public Criteria andIfPublicLessThan(Boolean value) {
            addCriterion("if_public <", value, "ifPublic");
            return (Criteria) this;
        }

        public Criteria andIfPublicLessThanOrEqualTo(Boolean value) {
            addCriterion("if_public <=", value, "ifPublic");
            return (Criteria) this;
        }

        public Criteria andIfPublicIn(List<Boolean> values) {
            addCriterion("if_public in", values, "ifPublic");
            return (Criteria) this;
        }

        public Criteria andIfPublicNotIn(List<Boolean> values) {
            addCriterion("if_public not in", values, "ifPublic");
            return (Criteria) this;
        }

        public Criteria andIfPublicBetween(Boolean value1, Boolean value2) {
            addCriterion("if_public between", value1, value2, "ifPublic");
            return (Criteria) this;
        }

        public Criteria andIfPublicNotBetween(Boolean value1, Boolean value2) {
            addCriterion("if_public not between", value1, value2, "ifPublic");
            return (Criteria) this;
        }

        public Criteria andIfPublishOrgIsNull() {
            addCriterion("if_publish_org is null");
            return (Criteria) this;
        }

        public Criteria andIfPublishOrgIsNotNull() {
            addCriterion("if_publish_org is not null");
            return (Criteria) this;
        }

        public Criteria andIfPublishOrgEqualTo(Boolean value) {
            addCriterion("if_publish_org =", value, "ifPublishOrg");
            return (Criteria) this;
        }

        public Criteria andIfPublishOrgNotEqualTo(Boolean value) {
            addCriterion("if_publish_org <>", value, "ifPublishOrg");
            return (Criteria) this;
        }

        public Criteria andIfPublishOrgGreaterThan(Boolean value) {
            addCriterion("if_publish_org >", value, "ifPublishOrg");
            return (Criteria) this;
        }

        public Criteria andIfPublishOrgGreaterThanOrEqualTo(Boolean value) {
            addCriterion("if_publish_org >=", value, "ifPublishOrg");
            return (Criteria) this;
        }

        public Criteria andIfPublishOrgLessThan(Boolean value) {
            addCriterion("if_publish_org <", value, "ifPublishOrg");
            return (Criteria) this;
        }

        public Criteria andIfPublishOrgLessThanOrEqualTo(Boolean value) {
            addCriterion("if_publish_org <=", value, "ifPublishOrg");
            return (Criteria) this;
        }

        public Criteria andIfPublishOrgIn(List<Boolean> values) {
            addCriterion("if_publish_org in", values, "ifPublishOrg");
            return (Criteria) this;
        }

        public Criteria andIfPublishOrgNotIn(List<Boolean> values) {
            addCriterion("if_publish_org not in", values, "ifPublishOrg");
            return (Criteria) this;
        }

        public Criteria andIfPublishOrgBetween(Boolean value1, Boolean value2) {
            addCriterion("if_publish_org between", value1, value2, "ifPublishOrg");
            return (Criteria) this;
        }

        public Criteria andIfPublishOrgNotBetween(Boolean value1, Boolean value2) {
            addCriterion("if_publish_org not between", value1, value2, "ifPublishOrg");
            return (Criteria) this;
        }

        public Criteria andProjectNatureIsNull() {
            addCriterion("project_nature is null");
            return (Criteria) this;
        }

        public Criteria andProjectNatureIsNotNull() {
            addCriterion("project_nature is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNatureEqualTo(String value) {
            addCriterion("project_nature =", value, "projectNature");
            return (Criteria) this;
        }

        public Criteria andProjectNatureNotEqualTo(String value) {
            addCriterion("project_nature <>", value, "projectNature");
            return (Criteria) this;
        }

        public Criteria andProjectNatureGreaterThan(String value) {
            addCriterion("project_nature >", value, "projectNature");
            return (Criteria) this;
        }

        public Criteria andProjectNatureGreaterThanOrEqualTo(String value) {
            addCriterion("project_nature >=", value, "projectNature");
            return (Criteria) this;
        }

        public Criteria andProjectNatureLessThan(String value) {
            addCriterion("project_nature <", value, "projectNature");
            return (Criteria) this;
        }

        public Criteria andProjectNatureLessThanOrEqualTo(String value) {
            addCriterion("project_nature <=", value, "projectNature");
            return (Criteria) this;
        }

        public Criteria andProjectNatureLike(String value) {
            addCriterion("project_nature like", value, "projectNature");
            return (Criteria) this;
        }

        public Criteria andProjectNatureNotLike(String value) {
            addCriterion("project_nature not like", value, "projectNature");
            return (Criteria) this;
        }

        public Criteria andProjectNatureIn(List<String> values) {
            addCriterion("project_nature in", values, "projectNature");
            return (Criteria) this;
        }

        public Criteria andProjectNatureNotIn(List<String> values) {
            addCriterion("project_nature not in", values, "projectNature");
            return (Criteria) this;
        }

        public Criteria andProjectNatureBetween(String value1, String value2) {
            addCriterion("project_nature between", value1, value2, "projectNature");
            return (Criteria) this;
        }

        public Criteria andProjectNatureNotBetween(String value1, String value2) {
            addCriterion("project_nature not between", value1, value2, "projectNature");
            return (Criteria) this;
        }

        public Criteria andResearchAreaIsNull() {
            addCriterion("research_area is null");
            return (Criteria) this;
        }

        public Criteria andResearchAreaIsNotNull() {
            addCriterion("research_area is not null");
            return (Criteria) this;
        }

        public Criteria andResearchAreaEqualTo(String value) {
            addCriterion("research_area =", value, "researchArea");
            return (Criteria) this;
        }

        public Criteria andResearchAreaNotEqualTo(String value) {
            addCriterion("research_area <>", value, "researchArea");
            return (Criteria) this;
        }

        public Criteria andResearchAreaGreaterThan(String value) {
            addCriterion("research_area >", value, "researchArea");
            return (Criteria) this;
        }

        public Criteria andResearchAreaGreaterThanOrEqualTo(String value) {
            addCriterion("research_area >=", value, "researchArea");
            return (Criteria) this;
        }

        public Criteria andResearchAreaLessThan(String value) {
            addCriterion("research_area <", value, "researchArea");
            return (Criteria) this;
        }

        public Criteria andResearchAreaLessThanOrEqualTo(String value) {
            addCriterion("research_area <=", value, "researchArea");
            return (Criteria) this;
        }

        public Criteria andResearchAreaLike(String value) {
            addCriterion("research_area like", value, "researchArea");
            return (Criteria) this;
        }

        public Criteria andResearchAreaNotLike(String value) {
            addCriterion("research_area not like", value, "researchArea");
            return (Criteria) this;
        }

        public Criteria andResearchAreaIn(List<String> values) {
            addCriterion("research_area in", values, "researchArea");
            return (Criteria) this;
        }

        public Criteria andResearchAreaNotIn(List<String> values) {
            addCriterion("research_area not in", values, "researchArea");
            return (Criteria) this;
        }

        public Criteria andResearchAreaBetween(String value1, String value2) {
            addCriterion("research_area between", value1, value2, "researchArea");
            return (Criteria) this;
        }

        public Criteria andResearchAreaNotBetween(String value1, String value2) {
            addCriterion("research_area not between", value1, value2, "researchArea");
            return (Criteria) this;
        }

        public Criteria andGroupUnitIsNull() {
            addCriterion("group_unit is null");
            return (Criteria) this;
        }

        public Criteria andGroupUnitIsNotNull() {
            addCriterion("group_unit is not null");
            return (Criteria) this;
        }

        public Criteria andGroupUnitEqualTo(String value) {
            addCriterion("group_unit =", value, "groupUnit");
            return (Criteria) this;
        }

        public Criteria andGroupUnitNotEqualTo(String value) {
            addCriterion("group_unit <>", value, "groupUnit");
            return (Criteria) this;
        }

        public Criteria andGroupUnitGreaterThan(String value) {
            addCriterion("group_unit >", value, "groupUnit");
            return (Criteria) this;
        }

        public Criteria andGroupUnitGreaterThanOrEqualTo(String value) {
            addCriterion("group_unit >=", value, "groupUnit");
            return (Criteria) this;
        }

        public Criteria andGroupUnitLessThan(String value) {
            addCriterion("group_unit <", value, "groupUnit");
            return (Criteria) this;
        }

        public Criteria andGroupUnitLessThanOrEqualTo(String value) {
            addCriterion("group_unit <=", value, "groupUnit");
            return (Criteria) this;
        }

        public Criteria andGroupUnitLike(String value) {
            addCriterion("group_unit like", value, "groupUnit");
            return (Criteria) this;
        }

        public Criteria andGroupUnitNotLike(String value) {
            addCriterion("group_unit not like", value, "groupUnit");
            return (Criteria) this;
        }

        public Criteria andGroupUnitIn(List<String> values) {
            addCriterion("group_unit in", values, "groupUnit");
            return (Criteria) this;
        }

        public Criteria andGroupUnitNotIn(List<String> values) {
            addCriterion("group_unit not in", values, "groupUnit");
            return (Criteria) this;
        }

        public Criteria andGroupUnitBetween(String value1, String value2) {
            addCriterion("group_unit between", value1, value2, "groupUnit");
            return (Criteria) this;
        }

        public Criteria andGroupUnitNotBetween(String value1, String value2) {
            addCriterion("group_unit not between", value1, value2, "groupUnit");
            return (Criteria) this;
        }

        public Criteria andGuResearcherIsNull() {
            addCriterion("gu_researcher is null");
            return (Criteria) this;
        }

        public Criteria andGuResearcherIsNotNull() {
            addCriterion("gu_researcher is not null");
            return (Criteria) this;
        }

        public Criteria andGuResearcherEqualTo(String value) {
            addCriterion("gu_researcher =", value, "guResearcher");
            return (Criteria) this;
        }

        public Criteria andGuResearcherNotEqualTo(String value) {
            addCriterion("gu_researcher <>", value, "guResearcher");
            return (Criteria) this;
        }

        public Criteria andGuResearcherGreaterThan(String value) {
            addCriterion("gu_researcher >", value, "guResearcher");
            return (Criteria) this;
        }

        public Criteria andGuResearcherGreaterThanOrEqualTo(String value) {
            addCriterion("gu_researcher >=", value, "guResearcher");
            return (Criteria) this;
        }

        public Criteria andGuResearcherLessThan(String value) {
            addCriterion("gu_researcher <", value, "guResearcher");
            return (Criteria) this;
        }

        public Criteria andGuResearcherLessThanOrEqualTo(String value) {
            addCriterion("gu_researcher <=", value, "guResearcher");
            return (Criteria) this;
        }

        public Criteria andGuResearcherLike(String value) {
            addCriterion("gu_researcher like", value, "guResearcher");
            return (Criteria) this;
        }

        public Criteria andGuResearcherNotLike(String value) {
            addCriterion("gu_researcher not like", value, "guResearcher");
            return (Criteria) this;
        }

        public Criteria andGuResearcherIn(List<String> values) {
            addCriterion("gu_researcher in", values, "guResearcher");
            return (Criteria) this;
        }

        public Criteria andGuResearcherNotIn(List<String> values) {
            addCriterion("gu_researcher not in", values, "guResearcher");
            return (Criteria) this;
        }

        public Criteria andGuResearcherBetween(String value1, String value2) {
            addCriterion("gu_researcher between", value1, value2, "guResearcher");
            return (Criteria) this;
        }

        public Criteria andGuResearcherNotBetween(String value1, String value2) {
            addCriterion("gu_researcher not between", value1, value2, "guResearcher");
            return (Criteria) this;
        }

        public Criteria andGuContactIsNull() {
            addCriterion("gu_contact is null");
            return (Criteria) this;
        }

        public Criteria andGuContactIsNotNull() {
            addCriterion("gu_contact is not null");
            return (Criteria) this;
        }

        public Criteria andGuContactEqualTo(String value) {
            addCriterion("gu_contact =", value, "guContact");
            return (Criteria) this;
        }

        public Criteria andGuContactNotEqualTo(String value) {
            addCriterion("gu_contact <>", value, "guContact");
            return (Criteria) this;
        }

        public Criteria andGuContactGreaterThan(String value) {
            addCriterion("gu_contact >", value, "guContact");
            return (Criteria) this;
        }

        public Criteria andGuContactGreaterThanOrEqualTo(String value) {
            addCriterion("gu_contact >=", value, "guContact");
            return (Criteria) this;
        }

        public Criteria andGuContactLessThan(String value) {
            addCriterion("gu_contact <", value, "guContact");
            return (Criteria) this;
        }

        public Criteria andGuContactLessThanOrEqualTo(String value) {
            addCriterion("gu_contact <=", value, "guContact");
            return (Criteria) this;
        }

        public Criteria andGuContactLike(String value) {
            addCriterion("gu_contact like", value, "guContact");
            return (Criteria) this;
        }

        public Criteria andGuContactNotLike(String value) {
            addCriterion("gu_contact not like", value, "guContact");
            return (Criteria) this;
        }

        public Criteria andGuContactIn(List<String> values) {
            addCriterion("gu_contact in", values, "guContact");
            return (Criteria) this;
        }

        public Criteria andGuContactNotIn(List<String> values) {
            addCriterion("gu_contact not in", values, "guContact");
            return (Criteria) this;
        }

        public Criteria andGuContactBetween(String value1, String value2) {
            addCriterion("gu_contact between", value1, value2, "guContact");
            return (Criteria) this;
        }

        public Criteria andGuContactNotBetween(String value1, String value2) {
            addCriterion("gu_contact not between", value1, value2, "guContact");
            return (Criteria) this;
        }

        public Criteria andNitiatorIsNull() {
            addCriterion("nitiator is null");
            return (Criteria) this;
        }

        public Criteria andNitiatorIsNotNull() {
            addCriterion("nitiator is not null");
            return (Criteria) this;
        }

        public Criteria andNitiatorEqualTo(String value) {
            addCriterion("nitiator =", value, "nitiator");
            return (Criteria) this;
        }

        public Criteria andNitiatorNotEqualTo(String value) {
            addCriterion("nitiator <>", value, "nitiator");
            return (Criteria) this;
        }

        public Criteria andNitiatorGreaterThan(String value) {
            addCriterion("nitiator >", value, "nitiator");
            return (Criteria) this;
        }

        public Criteria andNitiatorGreaterThanOrEqualTo(String value) {
            addCriterion("nitiator >=", value, "nitiator");
            return (Criteria) this;
        }

        public Criteria andNitiatorLessThan(String value) {
            addCriterion("nitiator <", value, "nitiator");
            return (Criteria) this;
        }

        public Criteria andNitiatorLessThanOrEqualTo(String value) {
            addCriterion("nitiator <=", value, "nitiator");
            return (Criteria) this;
        }

        public Criteria andNitiatorLike(String value) {
            addCriterion("nitiator like", value, "nitiator");
            return (Criteria) this;
        }

        public Criteria andNitiatorNotLike(String value) {
            addCriterion("nitiator not like", value, "nitiator");
            return (Criteria) this;
        }

        public Criteria andNitiatorIn(List<String> values) {
            addCriterion("nitiator in", values, "nitiator");
            return (Criteria) this;
        }

        public Criteria andNitiatorNotIn(List<String> values) {
            addCriterion("nitiator not in", values, "nitiator");
            return (Criteria) this;
        }

        public Criteria andNitiatorBetween(String value1, String value2) {
            addCriterion("nitiator between", value1, value2, "nitiator");
            return (Criteria) this;
        }

        public Criteria andNitiatorNotBetween(String value1, String value2) {
            addCriterion("nitiator not between", value1, value2, "nitiator");
            return (Criteria) this;
        }

        public Criteria andSponsorIsNull() {
            addCriterion("sponsor is null");
            return (Criteria) this;
        }

        public Criteria andSponsorIsNotNull() {
            addCriterion("sponsor is not null");
            return (Criteria) this;
        }

        public Criteria andSponsorEqualTo(String value) {
            addCriterion("sponsor =", value, "sponsor");
            return (Criteria) this;
        }

        public Criteria andSponsorNotEqualTo(String value) {
            addCriterion("sponsor <>", value, "sponsor");
            return (Criteria) this;
        }

        public Criteria andSponsorGreaterThan(String value) {
            addCriterion("sponsor >", value, "sponsor");
            return (Criteria) this;
        }

        public Criteria andSponsorGreaterThanOrEqualTo(String value) {
            addCriterion("sponsor >=", value, "sponsor");
            return (Criteria) this;
        }

        public Criteria andSponsorLessThan(String value) {
            addCriterion("sponsor <", value, "sponsor");
            return (Criteria) this;
        }

        public Criteria andSponsorLessThanOrEqualTo(String value) {
            addCriterion("sponsor <=", value, "sponsor");
            return (Criteria) this;
        }

        public Criteria andSponsorLike(String value) {
            addCriterion("sponsor like", value, "sponsor");
            return (Criteria) this;
        }

        public Criteria andSponsorNotLike(String value) {
            addCriterion("sponsor not like", value, "sponsor");
            return (Criteria) this;
        }

        public Criteria andSponsorIn(List<String> values) {
            addCriterion("sponsor in", values, "sponsor");
            return (Criteria) this;
        }

        public Criteria andSponsorNotIn(List<String> values) {
            addCriterion("sponsor not in", values, "sponsor");
            return (Criteria) this;
        }

        public Criteria andSponsorBetween(String value1, String value2) {
            addCriterion("sponsor between", value1, value2, "sponsor");
            return (Criteria) this;
        }

        public Criteria andSponsorNotBetween(String value1, String value2) {
            addCriterion("sponsor not between", value1, value2, "sponsor");
            return (Criteria) this;
        }

        public Criteria andNitiatorContactIsNull() {
            addCriterion("nitiator_contact is null");
            return (Criteria) this;
        }

        public Criteria andNitiatorContactIsNotNull() {
            addCriterion("nitiator_contact is not null");
            return (Criteria) this;
        }

        public Criteria andNitiatorContactEqualTo(String value) {
            addCriterion("nitiator_contact =", value, "nitiatorContact");
            return (Criteria) this;
        }

        public Criteria andNitiatorContactNotEqualTo(String value) {
            addCriterion("nitiator_contact <>", value, "nitiatorContact");
            return (Criteria) this;
        }

        public Criteria andNitiatorContactGreaterThan(String value) {
            addCriterion("nitiator_contact >", value, "nitiatorContact");
            return (Criteria) this;
        }

        public Criteria andNitiatorContactGreaterThanOrEqualTo(String value) {
            addCriterion("nitiator_contact >=", value, "nitiatorContact");
            return (Criteria) this;
        }

        public Criteria andNitiatorContactLessThan(String value) {
            addCriterion("nitiator_contact <", value, "nitiatorContact");
            return (Criteria) this;
        }

        public Criteria andNitiatorContactLessThanOrEqualTo(String value) {
            addCriterion("nitiator_contact <=", value, "nitiatorContact");
            return (Criteria) this;
        }

        public Criteria andNitiatorContactLike(String value) {
            addCriterion("nitiator_contact like", value, "nitiatorContact");
            return (Criteria) this;
        }

        public Criteria andNitiatorContactNotLike(String value) {
            addCriterion("nitiator_contact not like", value, "nitiatorContact");
            return (Criteria) this;
        }

        public Criteria andNitiatorContactIn(List<String> values) {
            addCriterion("nitiator_contact in", values, "nitiatorContact");
            return (Criteria) this;
        }

        public Criteria andNitiatorContactNotIn(List<String> values) {
            addCriterion("nitiator_contact not in", values, "nitiatorContact");
            return (Criteria) this;
        }

        public Criteria andNitiatorContactBetween(String value1, String value2) {
            addCriterion("nitiator_contact between", value1, value2, "nitiatorContact");
            return (Criteria) this;
        }

        public Criteria andNitiatorContactNotBetween(String value1, String value2) {
            addCriterion("nitiator_contact not between", value1, value2, "nitiatorContact");
            return (Criteria) this;
        }

        public Criteria andInvolvedOrgNumIsNull() {
            addCriterion("involved_org_num is null");
            return (Criteria) this;
        }

        public Criteria andInvolvedOrgNumIsNotNull() {
            addCriterion("involved_org_num is not null");
            return (Criteria) this;
        }

        public Criteria andInvolvedOrgNumEqualTo(Integer value) {
            addCriterion("involved_org_num =", value, "involvedOrgNum");
            return (Criteria) this;
        }

        public Criteria andInvolvedOrgNumNotEqualTo(Integer value) {
            addCriterion("involved_org_num <>", value, "involvedOrgNum");
            return (Criteria) this;
        }

        public Criteria andInvolvedOrgNumGreaterThan(Integer value) {
            addCriterion("involved_org_num >", value, "involvedOrgNum");
            return (Criteria) this;
        }

        public Criteria andInvolvedOrgNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("involved_org_num >=", value, "involvedOrgNum");
            return (Criteria) this;
        }

        public Criteria andInvolvedOrgNumLessThan(Integer value) {
            addCriterion("involved_org_num <", value, "involvedOrgNum");
            return (Criteria) this;
        }

        public Criteria andInvolvedOrgNumLessThanOrEqualTo(Integer value) {
            addCriterion("involved_org_num <=", value, "involvedOrgNum");
            return (Criteria) this;
        }

        public Criteria andInvolvedOrgNumIn(List<Integer> values) {
            addCriterion("involved_org_num in", values, "involvedOrgNum");
            return (Criteria) this;
        }

        public Criteria andInvolvedOrgNumNotIn(List<Integer> values) {
            addCriterion("involved_org_num not in", values, "involvedOrgNum");
            return (Criteria) this;
        }

        public Criteria andInvolvedOrgNumBetween(Integer value1, Integer value2) {
            addCriterion("involved_org_num between", value1, value2, "involvedOrgNum");
            return (Criteria) this;
        }

        public Criteria andInvolvedOrgNumNotBetween(Integer value1, Integer value2) {
            addCriterion("involved_org_num not between", value1, value2, "involvedOrgNum");
            return (Criteria) this;
        }

        public Criteria andRegisteNumberIsNull() {
            addCriterion("registe_number is null");
            return (Criteria) this;
        }

        public Criteria andRegisteNumberIsNotNull() {
            addCriterion("registe_number is not null");
            return (Criteria) this;
        }

        public Criteria andRegisteNumberEqualTo(String value) {
            addCriterion("registe_number =", value, "registeNumber");
            return (Criteria) this;
        }

        public Criteria andRegisteNumberNotEqualTo(String value) {
            addCriterion("registe_number <>", value, "registeNumber");
            return (Criteria) this;
        }

        public Criteria andRegisteNumberGreaterThan(String value) {
            addCriterion("registe_number >", value, "registeNumber");
            return (Criteria) this;
        }

        public Criteria andRegisteNumberGreaterThanOrEqualTo(String value) {
            addCriterion("registe_number >=", value, "registeNumber");
            return (Criteria) this;
        }

        public Criteria andRegisteNumberLessThan(String value) {
            addCriterion("registe_number <", value, "registeNumber");
            return (Criteria) this;
        }

        public Criteria andRegisteNumberLessThanOrEqualTo(String value) {
            addCriterion("registe_number <=", value, "registeNumber");
            return (Criteria) this;
        }

        public Criteria andRegisteNumberLike(String value) {
            addCriterion("registe_number like", value, "registeNumber");
            return (Criteria) this;
        }

        public Criteria andRegisteNumberNotLike(String value) {
            addCriterion("registe_number not like", value, "registeNumber");
            return (Criteria) this;
        }

        public Criteria andRegisteNumberIn(List<String> values) {
            addCriterion("registe_number in", values, "registeNumber");
            return (Criteria) this;
        }

        public Criteria andRegisteNumberNotIn(List<String> values) {
            addCriterion("registe_number not in", values, "registeNumber");
            return (Criteria) this;
        }

        public Criteria andRegisteNumberBetween(String value1, String value2) {
            addCriterion("registe_number between", value1, value2, "registeNumber");
            return (Criteria) this;
        }

        public Criteria andRegisteNumberNotBetween(String value1, String value2) {
            addCriterion("registe_number not between", value1, value2, "registeNumber");
            return (Criteria) this;
        }

        public Criteria andPlanStartDateIsNull() {
            addCriterion("plan_start_date is null");
            return (Criteria) this;
        }

        public Criteria andPlanStartDateIsNotNull() {
            addCriterion("plan_start_date is not null");
            return (Criteria) this;
        }

        public Criteria andPlanStartDateEqualTo(Date value) {
            addCriterionForJDBCDate("plan_start_date =", value, "planStartDate");
            return (Criteria) this;
        }

        public Criteria andPlanStartDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("plan_start_date <>", value, "planStartDate");
            return (Criteria) this;
        }

        public Criteria andPlanStartDateGreaterThan(Date value) {
            addCriterionForJDBCDate("plan_start_date >", value, "planStartDate");
            return (Criteria) this;
        }

        public Criteria andPlanStartDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("plan_start_date >=", value, "planStartDate");
            return (Criteria) this;
        }

        public Criteria andPlanStartDateLessThan(Date value) {
            addCriterionForJDBCDate("plan_start_date <", value, "planStartDate");
            return (Criteria) this;
        }

        public Criteria andPlanStartDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("plan_start_date <=", value, "planStartDate");
            return (Criteria) this;
        }

        public Criteria andPlanStartDateIn(List<Date> values) {
            addCriterionForJDBCDate("plan_start_date in", values, "planStartDate");
            return (Criteria) this;
        }

        public Criteria andPlanStartDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("plan_start_date not in", values, "planStartDate");
            return (Criteria) this;
        }

        public Criteria andPlanStartDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("plan_start_date between", value1, value2, "planStartDate");
            return (Criteria) this;
        }

        public Criteria andPlanStartDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("plan_start_date not between", value1, value2, "planStartDate");
            return (Criteria) this;
        }

        public Criteria andPlanGroupNumIsNull() {
            addCriterion("plan_group_num is null");
            return (Criteria) this;
        }

        public Criteria andPlanGroupNumIsNotNull() {
            addCriterion("plan_group_num is not null");
            return (Criteria) this;
        }

        public Criteria andPlanGroupNumEqualTo(Integer value) {
            addCriterion("plan_group_num =", value, "planGroupNum");
            return (Criteria) this;
        }

        public Criteria andPlanGroupNumNotEqualTo(Integer value) {
            addCriterion("plan_group_num <>", value, "planGroupNum");
            return (Criteria) this;
        }

        public Criteria andPlanGroupNumGreaterThan(Integer value) {
            addCriterion("plan_group_num >", value, "planGroupNum");
            return (Criteria) this;
        }

        public Criteria andPlanGroupNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("plan_group_num >=", value, "planGroupNum");
            return (Criteria) this;
        }

        public Criteria andPlanGroupNumLessThan(Integer value) {
            addCriterion("plan_group_num <", value, "planGroupNum");
            return (Criteria) this;
        }

        public Criteria andPlanGroupNumLessThanOrEqualTo(Integer value) {
            addCriterion("plan_group_num <=", value, "planGroupNum");
            return (Criteria) this;
        }

        public Criteria andPlanGroupNumIn(List<Integer> values) {
            addCriterion("plan_group_num in", values, "planGroupNum");
            return (Criteria) this;
        }

        public Criteria andPlanGroupNumNotIn(List<Integer> values) {
            addCriterion("plan_group_num not in", values, "planGroupNum");
            return (Criteria) this;
        }

        public Criteria andPlanGroupNumBetween(Integer value1, Integer value2) {
            addCriterion("plan_group_num between", value1, value2, "planGroupNum");
            return (Criteria) this;
        }

        public Criteria andPlanGroupNumNotBetween(Integer value1, Integer value2) {
            addCriterion("plan_group_num not between", value1, value2, "planGroupNum");
            return (Criteria) this;
        }

        public Criteria andRecordNumberIsNull() {
            addCriterion("record_number is null");
            return (Criteria) this;
        }

        public Criteria andRecordNumberIsNotNull() {
            addCriterion("record_number is not null");
            return (Criteria) this;
        }

        public Criteria andRecordNumberEqualTo(String value) {
            addCriterion("record_number =", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberNotEqualTo(String value) {
            addCriterion("record_number <>", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberGreaterThan(String value) {
            addCriterion("record_number >", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberGreaterThanOrEqualTo(String value) {
            addCriterion("record_number >=", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberLessThan(String value) {
            addCriterion("record_number <", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberLessThanOrEqualTo(String value) {
            addCriterion("record_number <=", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberLike(String value) {
            addCriterion("record_number like", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberNotLike(String value) {
            addCriterion("record_number not like", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberIn(List<String> values) {
            addCriterion("record_number in", values, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberNotIn(List<String> values) {
            addCriterion("record_number not in", values, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberBetween(String value1, String value2) {
            addCriterion("record_number between", value1, value2, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberNotBetween(String value1, String value2) {
            addCriterion("record_number not between", value1, value2, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNull() {
            addCriterion("template_id is null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNotNull() {
            addCriterion("template_id is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdEqualTo(Long value) {
            addCriterion("template_id =", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotEqualTo(Long value) {
            addCriterion("template_id <>", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThan(Long value) {
            addCriterion("template_id >", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThanOrEqualTo(Long value) {
            addCriterion("template_id >=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThan(Long value) {
            addCriterion("template_id <", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThanOrEqualTo(Long value) {
            addCriterion("template_id <=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIn(List<Long> values) {
            addCriterion("template_id in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotIn(List<Long> values) {
            addCriterion("template_id not in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdBetween(Long value1, Long value2) {
            addCriterion("template_id between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotBetween(Long value1, Long value2) {
            addCriterion("template_id not between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andDatabaseIdIsNull() {
            addCriterion("database_id is null");
            return (Criteria) this;
        }

        public Criteria andDatabaseIdIsNotNull() {
            addCriterion("database_id is not null");
            return (Criteria) this;
        }

        public Criteria andDatabaseIdEqualTo(String value) {
            addCriterion("database_id =", value, "databaseId");
            return (Criteria) this;
        }

        public Criteria andDatabaseIdNotEqualTo(String value) {
            addCriterion("database_id <>", value, "databaseId");
            return (Criteria) this;
        }

        public Criteria andDatabaseIdGreaterThan(String value) {
            addCriterion("database_id >", value, "databaseId");
            return (Criteria) this;
        }

        public Criteria andDatabaseIdGreaterThanOrEqualTo(String value) {
            addCriterion("database_id >=", value, "databaseId");
            return (Criteria) this;
        }

        public Criteria andDatabaseIdLessThan(String value) {
            addCriterion("database_id <", value, "databaseId");
            return (Criteria) this;
        }

        public Criteria andDatabaseIdLessThanOrEqualTo(String value) {
            addCriterion("database_id <=", value, "databaseId");
            return (Criteria) this;
        }

        public Criteria andDatabaseIdLike(String value) {
            addCriterion("database_id like", value, "databaseId");
            return (Criteria) this;
        }

        public Criteria andDatabaseIdNotLike(String value) {
            addCriterion("database_id not like", value, "databaseId");
            return (Criteria) this;
        }

        public Criteria andDatabaseIdIn(List<String> values) {
            addCriterion("database_id in", values, "databaseId");
            return (Criteria) this;
        }

        public Criteria andDatabaseIdNotIn(List<String> values) {
            addCriterion("database_id not in", values, "databaseId");
            return (Criteria) this;
        }

        public Criteria andDatabaseIdBetween(String value1, String value2) {
            addCriterion("database_id between", value1, value2, "databaseId");
            return (Criteria) this;
        }

        public Criteria andDatabaseIdNotBetween(String value1, String value2) {
            addCriterion("database_id not between", value1, value2, "databaseId");
            return (Criteria) this;
        }

        public Criteria andDatabaseFromIsNull() {
            addCriterion("database_from is null");
            return (Criteria) this;
        }

        public Criteria andDatabaseFromIsNotNull() {
            addCriterion("database_from is not null");
            return (Criteria) this;
        }

        public Criteria andDatabaseFromEqualTo(String value) {
            addCriterion("database_from =", value, "databaseFrom");
            return (Criteria) this;
        }

        public Criteria andDatabaseFromNotEqualTo(String value) {
            addCriterion("database_from <>", value, "databaseFrom");
            return (Criteria) this;
        }

        public Criteria andDatabaseFromGreaterThan(String value) {
            addCriterion("database_from >", value, "databaseFrom");
            return (Criteria) this;
        }

        public Criteria andDatabaseFromGreaterThanOrEqualTo(String value) {
            addCriterion("database_from >=", value, "databaseFrom");
            return (Criteria) this;
        }

        public Criteria andDatabaseFromLessThan(String value) {
            addCriterion("database_from <", value, "databaseFrom");
            return (Criteria) this;
        }

        public Criteria andDatabaseFromLessThanOrEqualTo(String value) {
            addCriterion("database_from <=", value, "databaseFrom");
            return (Criteria) this;
        }

        public Criteria andDatabaseFromLike(String value) {
            addCriterion("database_from like", value, "databaseFrom");
            return (Criteria) this;
        }

        public Criteria andDatabaseFromNotLike(String value) {
            addCriterion("database_from not like", value, "databaseFrom");
            return (Criteria) this;
        }

        public Criteria andDatabaseFromIn(List<String> values) {
            addCriterion("database_from in", values, "databaseFrom");
            return (Criteria) this;
        }

        public Criteria andDatabaseFromNotIn(List<String> values) {
            addCriterion("database_from not in", values, "databaseFrom");
            return (Criteria) this;
        }

        public Criteria andDatabaseFromBetween(String value1, String value2) {
            addCriterion("database_from between", value1, value2, "databaseFrom");
            return (Criteria) this;
        }

        public Criteria andDatabaseFromNotBetween(String value1, String value2) {
            addCriterion("database_from not between", value1, value2, "databaseFrom");
            return (Criteria) this;
        }

        public Criteria andResearchMethodIsNull() {
            addCriterion("research_method is null");
            return (Criteria) this;
        }

        public Criteria andResearchMethodIsNotNull() {
            addCriterion("research_method is not null");
            return (Criteria) this;
        }

        public Criteria andResearchMethodEqualTo(String value) {
            addCriterion("research_method =", value, "researchMethod");
            return (Criteria) this;
        }

        public Criteria andResearchMethodNotEqualTo(String value) {
            addCriterion("research_method <>", value, "researchMethod");
            return (Criteria) this;
        }

        public Criteria andResearchMethodGreaterThan(String value) {
            addCriterion("research_method >", value, "researchMethod");
            return (Criteria) this;
        }

        public Criteria andResearchMethodGreaterThanOrEqualTo(String value) {
            addCriterion("research_method >=", value, "researchMethod");
            return (Criteria) this;
        }

        public Criteria andResearchMethodLessThan(String value) {
            addCriterion("research_method <", value, "researchMethod");
            return (Criteria) this;
        }

        public Criteria andResearchMethodLessThanOrEqualTo(String value) {
            addCriterion("research_method <=", value, "researchMethod");
            return (Criteria) this;
        }

        public Criteria andResearchMethodLike(String value) {
            addCriterion("research_method like", value, "researchMethod");
            return (Criteria) this;
        }

        public Criteria andResearchMethodNotLike(String value) {
            addCriterion("research_method not like", value, "researchMethod");
            return (Criteria) this;
        }

        public Criteria andResearchMethodIn(List<String> values) {
            addCriterion("research_method in", values, "researchMethod");
            return (Criteria) this;
        }

        public Criteria andResearchMethodNotIn(List<String> values) {
            addCriterion("research_method not in", values, "researchMethod");
            return (Criteria) this;
        }

        public Criteria andResearchMethodBetween(String value1, String value2) {
            addCriterion("research_method between", value1, value2, "researchMethod");
            return (Criteria) this;
        }

        public Criteria andResearchMethodNotBetween(String value1, String value2) {
            addCriterion("research_method not between", value1, value2, "researchMethod");
            return (Criteria) this;
        }

        public Criteria andCreateMethodIsNull() {
            addCriterion("create_method is null");
            return (Criteria) this;
        }

        public Criteria andCreateMethodIsNotNull() {
            addCriterion("create_method is not null");
            return (Criteria) this;
        }

        public Criteria andCreateMethodEqualTo(String value) {
            addCriterion("create_method =", value, "createMethod");
            return (Criteria) this;
        }

        public Criteria andCreateMethodNotEqualTo(String value) {
            addCriterion("create_method <>", value, "createMethod");
            return (Criteria) this;
        }

        public Criteria andCreateMethodGreaterThan(String value) {
            addCriterion("create_method >", value, "createMethod");
            return (Criteria) this;
        }

        public Criteria andCreateMethodGreaterThanOrEqualTo(String value) {
            addCriterion("create_method >=", value, "createMethod");
            return (Criteria) this;
        }

        public Criteria andCreateMethodLessThan(String value) {
            addCriterion("create_method <", value, "createMethod");
            return (Criteria) this;
        }

        public Criteria andCreateMethodLessThanOrEqualTo(String value) {
            addCriterion("create_method <=", value, "createMethod");
            return (Criteria) this;
        }

        public Criteria andCreateMethodLike(String value) {
            addCriterion("create_method like", value, "createMethod");
            return (Criteria) this;
        }

        public Criteria andCreateMethodNotLike(String value) {
            addCriterion("create_method not like", value, "createMethod");
            return (Criteria) this;
        }

        public Criteria andCreateMethodIn(List<String> values) {
            addCriterion("create_method in", values, "createMethod");
            return (Criteria) this;
        }

        public Criteria andCreateMethodNotIn(List<String> values) {
            addCriterion("create_method not in", values, "createMethod");
            return (Criteria) this;
        }

        public Criteria andCreateMethodBetween(String value1, String value2) {
            addCriterion("create_method between", value1, value2, "createMethod");
            return (Criteria) this;
        }

        public Criteria andCreateMethodNotBetween(String value1, String value2) {
            addCriterion("create_method not between", value1, value2, "createMethod");
            return (Criteria) this;
        }

        public Criteria andEnablePrescriptionAnalysisIsNull() {
            addCriterion("enable_prescription_analysis is null");
            return (Criteria) this;
        }

        public Criteria andEnablePrescriptionAnalysisIsNotNull() {
            addCriterion("enable_prescription_analysis is not null");
            return (Criteria) this;
        }

        public Criteria andEnablePrescriptionAnalysisEqualTo(Boolean value) {
            addCriterion("enable_prescription_analysis =", value, "enablePrescriptionAnalysis");
            return (Criteria) this;
        }

        public Criteria andEnablePrescriptionAnalysisNotEqualTo(Boolean value) {
            addCriterion("enable_prescription_analysis <>", value, "enablePrescriptionAnalysis");
            return (Criteria) this;
        }

        public Criteria andEnablePrescriptionAnalysisGreaterThan(Boolean value) {
            addCriterion("enable_prescription_analysis >", value, "enablePrescriptionAnalysis");
            return (Criteria) this;
        }

        public Criteria andEnablePrescriptionAnalysisGreaterThanOrEqualTo(Boolean value) {
            addCriterion("enable_prescription_analysis >=", value, "enablePrescriptionAnalysis");
            return (Criteria) this;
        }

        public Criteria andEnablePrescriptionAnalysisLessThan(Boolean value) {
            addCriterion("enable_prescription_analysis <", value, "enablePrescriptionAnalysis");
            return (Criteria) this;
        }

        public Criteria andEnablePrescriptionAnalysisLessThanOrEqualTo(Boolean value) {
            addCriterion("enable_prescription_analysis <=", value, "enablePrescriptionAnalysis");
            return (Criteria) this;
        }

        public Criteria andEnablePrescriptionAnalysisIn(List<Boolean> values) {
            addCriterion("enable_prescription_analysis in", values, "enablePrescriptionAnalysis");
            return (Criteria) this;
        }

        public Criteria andEnablePrescriptionAnalysisNotIn(List<Boolean> values) {
            addCriterion("enable_prescription_analysis not in", values, "enablePrescriptionAnalysis");
            return (Criteria) this;
        }

        public Criteria andEnablePrescriptionAnalysisBetween(Boolean value1, Boolean value2) {
            addCriterion("enable_prescription_analysis between", value1, value2, "enablePrescriptionAnalysis");
            return (Criteria) this;
        }

        public Criteria andEnablePrescriptionAnalysisNotBetween(Boolean value1, Boolean value2) {
            addCriterion("enable_prescription_analysis not between", value1, value2, "enablePrescriptionAnalysis");
            return (Criteria) this;
        }

        public Criteria andEnableRandomizedConfigIsNull() {
            addCriterion("enable_randomized_config is null");
            return (Criteria) this;
        }

        public Criteria andEnableRandomizedConfigIsNotNull() {
            addCriterion("enable_randomized_config is not null");
            return (Criteria) this;
        }

        public Criteria andEnableRandomizedConfigEqualTo(Boolean value) {
            addCriterion("enable_randomized_config =", value, "enableRandomizedConfig");
            return (Criteria) this;
        }

        public Criteria andEnableRandomizedConfigNotEqualTo(Boolean value) {
            addCriterion("enable_randomized_config <>", value, "enableRandomizedConfig");
            return (Criteria) this;
        }

        public Criteria andEnableRandomizedConfigGreaterThan(Boolean value) {
            addCriterion("enable_randomized_config >", value, "enableRandomizedConfig");
            return (Criteria) this;
        }

        public Criteria andEnableRandomizedConfigGreaterThanOrEqualTo(Boolean value) {
            addCriterion("enable_randomized_config >=", value, "enableRandomizedConfig");
            return (Criteria) this;
        }

        public Criteria andEnableRandomizedConfigLessThan(Boolean value) {
            addCriterion("enable_randomized_config <", value, "enableRandomizedConfig");
            return (Criteria) this;
        }

        public Criteria andEnableRandomizedConfigLessThanOrEqualTo(Boolean value) {
            addCriterion("enable_randomized_config <=", value, "enableRandomizedConfig");
            return (Criteria) this;
        }

        public Criteria andEnableRandomizedConfigIn(List<Boolean> values) {
            addCriterion("enable_randomized_config in", values, "enableRandomizedConfig");
            return (Criteria) this;
        }

        public Criteria andEnableRandomizedConfigNotIn(List<Boolean> values) {
            addCriterion("enable_randomized_config not in", values, "enableRandomizedConfig");
            return (Criteria) this;
        }

        public Criteria andEnableRandomizedConfigBetween(Boolean value1, Boolean value2) {
            addCriterion("enable_randomized_config between", value1, value2, "enableRandomizedConfig");
            return (Criteria) this;
        }

        public Criteria andEnableRandomizedConfigNotBetween(Boolean value1, Boolean value2) {
            addCriterion("enable_randomized_config not between", value1, value2, "enableRandomizedConfig");
            return (Criteria) this;
        }

        public Criteria andTotalCountIsNull() {
            addCriterion("total_count is null");
            return (Criteria) this;
        }

        public Criteria andTotalCountIsNotNull() {
            addCriterion("total_count is not null");
            return (Criteria) this;
        }

        public Criteria andTotalCountEqualTo(Integer value) {
            addCriterion("total_count =", value, "totalCount");
            return (Criteria) this;
        }

        public Criteria andTotalCountNotEqualTo(Integer value) {
            addCriterion("total_count <>", value, "totalCount");
            return (Criteria) this;
        }

        public Criteria andTotalCountGreaterThan(Integer value) {
            addCriterion("total_count >", value, "totalCount");
            return (Criteria) this;
        }

        public Criteria andTotalCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("total_count >=", value, "totalCount");
            return (Criteria) this;
        }

        public Criteria andTotalCountLessThan(Integer value) {
            addCriterion("total_count <", value, "totalCount");
            return (Criteria) this;
        }

        public Criteria andTotalCountLessThanOrEqualTo(Integer value) {
            addCriterion("total_count <=", value, "totalCount");
            return (Criteria) this;
        }

        public Criteria andTotalCountIn(List<Integer> values) {
            addCriterion("total_count in", values, "totalCount");
            return (Criteria) this;
        }

        public Criteria andTotalCountNotIn(List<Integer> values) {
            addCriterion("total_count not in", values, "totalCount");
            return (Criteria) this;
        }

        public Criteria andTotalCountBetween(Integer value1, Integer value2) {
            addCriterion("total_count between", value1, value2, "totalCount");
            return (Criteria) this;
        }

        public Criteria andTotalCountNotBetween(Integer value1, Integer value2) {
            addCriterion("total_count not between", value1, value2, "totalCount");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andProjectSwitchIsNull() {
            addCriterion("project_switch is null");
            return (Criteria) this;
        }

        public Criteria andProjectSwitchIsNotNull() {
            addCriterion("project_switch is not null");
            return (Criteria) this;
        }

        public Criteria andProjectSwitchEqualTo(Integer value) {
            addCriterion("project_switch =", value, "projectSwitch");
            return (Criteria) this;
        }

        public Criteria andProjectSwitchNotEqualTo(Integer value) {
            addCriterion("project_switch <>", value, "projectSwitch");
            return (Criteria) this;
        }

        public Criteria andProjectSwitchGreaterThan(Integer value) {
            addCriterion("project_switch >", value, "projectSwitch");
            return (Criteria) this;
        }

        public Criteria andProjectSwitchGreaterThanOrEqualTo(Integer value) {
            addCriterion("project_switch >=", value, "projectSwitch");
            return (Criteria) this;
        }

        public Criteria andProjectSwitchLessThan(Integer value) {
            addCriterion("project_switch <", value, "projectSwitch");
            return (Criteria) this;
        }

        public Criteria andProjectSwitchLessThanOrEqualTo(Integer value) {
            addCriterion("project_switch <=", value, "projectSwitch");
            return (Criteria) this;
        }

        public Criteria andProjectSwitchIn(List<Integer> values) {
            addCriterion("project_switch in", values, "projectSwitch");
            return (Criteria) this;
        }

        public Criteria andProjectSwitchNotIn(List<Integer> values) {
            addCriterion("project_switch not in", values, "projectSwitch");
            return (Criteria) this;
        }

        public Criteria andProjectSwitchBetween(Integer value1, Integer value2) {
            addCriterion("project_switch between", value1, value2, "projectSwitch");
            return (Criteria) this;
        }

        public Criteria andProjectSwitchNotBetween(Integer value1, Integer value2) {
            addCriterion("project_switch not between", value1, value2, "projectSwitch");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNull() {
            addCriterion("create_user is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNotNull() {
            addCriterion("create_user is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserEqualTo(String value) {
            addCriterion("create_user =", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotEqualTo(String value) {
            addCriterion("create_user <>", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThan(String value) {
            addCriterion("create_user >", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThanOrEqualTo(String value) {
            addCriterion("create_user >=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThan(String value) {
            addCriterion("create_user <", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThanOrEqualTo(String value) {
            addCriterion("create_user <=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLike(String value) {
            addCriterion("create_user like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotLike(String value) {
            addCriterion("create_user not like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserIn(List<String> values) {
            addCriterion("create_user in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotIn(List<String> values) {
            addCriterion("create_user not in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserBetween(String value1, String value2) {
            addCriterion("create_user between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotBetween(String value1, String value2) {
            addCriterion("create_user not between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNull() {
            addCriterion("update_user is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNotNull() {
            addCriterion("update_user is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserEqualTo(String value) {
            addCriterion("update_user =", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotEqualTo(String value) {
            addCriterion("update_user <>", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThan(String value) {
            addCriterion("update_user >", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThanOrEqualTo(String value) {
            addCriterion("update_user >=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThan(String value) {
            addCriterion("update_user <", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThanOrEqualTo(String value) {
            addCriterion("update_user <=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLike(String value) {
            addCriterion("update_user like", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotLike(String value) {
            addCriterion("update_user not like", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIn(List<String> values) {
            addCriterion("update_user in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotIn(List<String> values) {
            addCriterion("update_user not in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserBetween(String value1, String value2) {
            addCriterion("update_user between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotBetween(String value1, String value2) {
            addCriterion("update_user not between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNull() {
            addCriterion("platform_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNotNull() {
            addCriterion("platform_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdEqualTo(String value) {
            addCriterion("platform_id =", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotEqualTo(String value) {
            addCriterion("platform_id <>", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThan(String value) {
            addCriterion("platform_id >", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThanOrEqualTo(String value) {
            addCriterion("platform_id >=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThan(String value) {
            addCriterion("platform_id <", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThanOrEqualTo(String value) {
            addCriterion("platform_id <=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLike(String value) {
            addCriterion("platform_id like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotLike(String value) {
            addCriterion("platform_id not like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIn(List<String> values) {
            addCriterion("platform_id in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotIn(List<String> values) {
            addCriterion("platform_id not in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdBetween(String value1, String value2) {
            addCriterion("platform_id between", value1, value2, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotBetween(String value1, String value2) {
            addCriterion("platform_id not between", value1, value2, "platformId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}