package com.haoys.user.mapper;

import com.haoys.user.model.SendRequestRecord;
import com.haoys.user.model.SendRequestRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SendRequestRecordMapper {
    long countByExample(SendRequestRecordExample example);

    int deleteByExample(SendRequestRecordExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SendRequestRecord record);

    int insertSelective(SendRequestRecord record);

    List<SendRequestRecord> selectByExampleWithBLOBs(SendRequestRecordExample example);

    List<SendRequestRecord> selectByExample(SendRequestRecordExample example);

    SendRequestRecord selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SendRequestRecord record, @Param("example") SendRequestRecordExample example);

    int updateByExampleWithBLOBs(@Param("record") SendRequestRecord record, @Param("example") SendRequestRecordExample example);

    int updateByExample(@Param("record") SendRequestRecord record, @Param("example") SendRequestRecordExample example);

    int updateByPrimaryKeySelective(SendRequestRecord record);

    int updateByPrimaryKeyWithBLOBs(SendRequestRecord record);

    int updateByPrimaryKey(SendRequestRecord record);
}