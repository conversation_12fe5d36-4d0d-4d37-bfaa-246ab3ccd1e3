package com.haoys.user.service.impl;

import com.haoys.user.domain.param.file.SaveUploadProjectFileParam;
import com.haoys.user.domain.vo.UploadFileResultVo;
import com.haoys.user.service.ProjectTesteeFileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 重构后的文件上传服务使用示例
 * 展示如何使用新的参数对象和异步上传功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ProjectTesteeFileServiceExample {

    private final ProjectTesteeFileService projectTesteeFileService;

    /**
     * 使用重构后的方法上传文件的示例
     * 
     * @param files 上传的文件数组
     * @return 上传结果列表
     * @throws IOException IO异常
     */
    public List<UploadFileResultVo> uploadFilesExample(MultipartFile[] files) throws IOException {
        // 使用构建器模式创建参数对象，代码更清晰易读
        SaveUploadProjectFileParam param = SaveUploadProjectFileParam.builder()
                .createUserId("user123")
                .projectId("project456")
                .visitId("visit789")
                .formId("form101")
                .resourceId("resource202")
                .tableId("table303")
                .rowNumber("1")
                .testeeId("testee404")
                .collect("1")
                .taskDate("2024-01-01")
                .openOCR("1")
                .batchUpload("0")
                .medicalType("medical_type_01")
                .templateId("template505")
                .prePageNo("1")
                .groupId("group606")
                .groupName("测试组")
                .imageType("original")
                .extendStruct("1")
                .generalAccurate("1")
                .mergeImage("0")
                .mergeMethod("horizontal")
                .ifMontage("0")
                .needMergeFileParam("")
                .batchOpenOcr("1");

        // 调用重构后的方法
        return projectTesteeFileService.saveUploadProjectFileWithParam(files, param);
    }

    /**
     * 简化版本的文件上传示例
     * 只设置必要的参数
     * 
     * @param files 上传的文件数组
     * @param projectId 项目ID
     * @param testeeId 测试者ID
     * @param createUserId 创建用户ID
     * @return 上传结果列表
     * @throws IOException IO异常
     */
    public List<UploadFileResultVo> uploadFilesSimple(MultipartFile[] files, String projectId, 
                                                     String testeeId, String createUserId) throws IOException {
        // 只设置必要参数的简化版本
        SaveUploadProjectFileParam param = SaveUploadProjectFileParam.builder()
                .createUserId(createUserId)
                .projectId(projectId)
                .testeeId(testeeId)
                .medicalType("default")
                .openOCR("0")
                .batchUpload("0")
                .ifMontage("0")
                .batchOpenOcr("0");

        return projectTesteeFileService.saveUploadProjectFileWithParam(files, param);
    }

    /**
     * 带OCR功能的文件上传示例
     * 
     * @param files 上传的文件数组
     * @param projectId 项目ID
     * @param testeeId 测试者ID
     * @param createUserId 创建用户ID
     * @param medicalType 医疗类型
     * @return 上传结果列表
     * @throws IOException IO异常
     */
    public List<UploadFileResultVo> uploadFilesWithOcr(MultipartFile[] files, String projectId, 
                                                      String testeeId, String createUserId, 
                                                      String medicalType) throws IOException {
        SaveUploadProjectFileParam param = SaveUploadProjectFileParam.builder()
                .createUserId(createUserId)
                .projectId(projectId)
                .testeeId(testeeId)
                .medicalType(medicalType)
                .openOCR("1")  // 启用OCR
                .extendStruct("1")  // 启用扩展结构识别
                .generalAccurate("1")  // 启用通用精确识别
                .batchUpload("1")
                .ifMontage("0")
                .batchOpenOcr("1");

        return projectTesteeFileService.saveUploadProjectFileWithParam(files, param);
    }
}
