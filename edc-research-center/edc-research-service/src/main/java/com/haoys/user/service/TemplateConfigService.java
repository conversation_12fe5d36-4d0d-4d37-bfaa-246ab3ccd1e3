package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.expand.TemplateLabConfigExpand;
import com.haoys.user.domain.param.crf.TemplateCustomFormConfigParam;
import com.haoys.user.domain.param.crf.TemplateFormConfigBaseParam;
import com.haoys.user.domain.param.crf.TemplateFormConfigParam;
import com.haoys.user.domain.param.crf.TemplateFormDetailParam;
import com.haoys.user.domain.param.crf.TemplateFormLogicParam;
import com.haoys.user.domain.param.crf.TemplateTableConfigParam;
import com.haoys.user.domain.param.lab.TemplateLabConfigParam;
import com.haoys.user.domain.template.TemplateFormVariableWrapper;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeFormDetailAndTableResultVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormConfigVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDetailExcelImportVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDetailVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormLogicVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormTableExcelImportVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormVariableExportVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormVariableViewVo;
import com.haoys.user.domain.vo.ecrf.TemplateTableVo;
import com.haoys.user.domain.vo.ecrf.TemplateTesteeJoinGroupFormVo;
import com.haoys.user.domain.vo.ecrf.TemplateVariableVo;
import com.haoys.user.domain.vo.ecrf.TemplateVo;
import com.haoys.user.domain.vo.lab.TemplateLabConfigVo;
import com.haoys.user.model.Template;
import com.haoys.user.model.TemplateFormConfig;
import com.haoys.user.model.TemplateFormDetail;
import com.haoys.user.model.TemplateFormTable;

import java.util.List;


public interface TemplateConfigService {
    
    /**
     * 保存模版基本信息
     * @param template
     */
    void saveTemplateBaseInfo(Template template);
    
    /**
     * 根据templateId查询模版
     * @param templateId
     * @return
     */
    TemplateVo getProjectTemplateInfo(String templateId);
    
    /**
     * 修改模版基本信息
     * @param templateId
     * @param templateStatus
     * @param userId
     * @return
     */
    String modifyProjectTemplateStatus(String templateId, String templateStatus, String userId);

    /**
     * 发布项目模版
     * @param templateId
     * @param templateName
     * @param templateStatus
     * @param templateDesc
     * @param userId
     * @return
     */
    CustomResult saveTemplateConfig(String templateId, String templateName, String templateStatus, String templateDesc, String userId);


    /**
     * 表单配置-创建表单-批量保存表单
     * @param templateFormConfigParamList
     * @param userId
     * @return
     */
    CustomResult saveBatchTemplateFormConfigBaseInfo(List<TemplateFormConfigBaseParam> templateFormConfigParamList, String userId);

    /**
     * 保存修改模板及创建访视动态表单
     * @param templateFormConfigParam
     * @return
     */
    CustomResult saveTemplateFormConfig(TemplateFormConfigParam templateFormConfigParam);

    /**
     * 创建表单配置表格
     * @param templateTableConfigParam
     * @return
     */
    CustomResult saveTemplateTableConfig(TemplateTableConfigParam templateTableConfigParam);

    /**
     * 表单配置-创建表单-保存表单基本信息
     * @param templateFormConfigParam
     * @return
     */
    CustomResult saveTemplateFormConfigBaseInfo(TemplateFormConfigBaseParam templateFormConfigParam);


    /**
     * 保存表单字段变量
     * @param templateFormDetailParam
     * @return
     */
    CustomResult saveTemplateFormDetailConfig(TemplateFormDetailParam templateFormDetailParam);

    /**
     * 使用全局模板保存项目表单
     * @param projectId
     * @param templateId
     * @param updateCurrentLogic
     * @param userId
     * @return
     */
    String saveCustomFormConfigByTemplateId(String projectId, String templateId, String updateCurrentLogic, String userId);

    /**
     * 保存选中模板表单项集合 --- 复制模版到我的项目或者项目模版
     * @param templateId              模版id
     * @param projectId               项目id
     * @param configType              模版类型
     * @param templateFormId          访视模板表单id
     * @param targetFormId            引用模版-复制到目标表单
     * @param replaceFormName         设置表单配置名称
     * @param formCode                表单code
     * @param formType                表单类型
     * @param appendForm              创建表单 0/1
     * @param formDetailId            指定变量  复制
     * @param updateCurrentLogic      是否更新逻辑显示条件
     * @param operator                操作人
     * @return
     */
    String saveTemplateConfigFromCustomTemplate(String templateId, String projectId, String groupId, String configType, String templateFormId, String targetFormId, String replaceFormName, String formCode, String formType, boolean appendForm, String formDetailId, String updateCurrentLogic, String operator);
    /**
     * 初始化模板表单项集合 --- 复制企业模版到我的项目模版
     * @return
     */
    String initTemplateConfigFromTenantTemplate(String projectId, String copyTemplateId, String copyFormId, String operator);
    
    String initTemplateConfigFromReferenceTemplate(String string, String referenceProjectId, boolean copyPlanAndFlow, String operator);
    
    
    /**
     * 更新表单变量字段映射来源
     * @param variableId
     * @param BaseVariableId
     * @param operator
     */
    void updateTemplateFormDetailConfigByVaribaleId(String variableId, String BaseVariableId, String operator);

    void updateTemplateFormDetailConfigById(TemplateFormDetail templateFormDetail);

    void updateTemplateFormTableConfigById(TemplateFormTable templateFormTable);


    /**
     * 查询模版信息
     * @param templateId
     * @return
     */
    Template getTemplateBaseInfoById(Long templateId);

    /**
     * 查询项目模版分页列表
     * @param templateName
     * @param pageNum
     * @param pageSize
     * @return
     */
    CommonPage<TemplateVo> getProjectTemplateListForPage(String templateName, Integer pageNum, Integer pageSize);

    /**
     * 查询项目表单项目列表
     *
     * @param templateId                 模板id
     * @param projectId                  项目id
     * @param planId
     * @param ePro                       是否开启ePro 1-开启 0-关闭
     * @param formName                   表单名称
     * @param isPublish                  是否已发布
     * @param excludeJoinGroupFromConfig
     * @return
     */
    List<TemplateFormConfigVo> getTemplateFormConfigListByPlanId(String templateId, String projectId, String planId, String ePro, String formName, Boolean isPublish, Boolean excludeJoinGroupFromConfig);

    /**
     * 通过templateId和formId查询表单详情列表
     *
     * @param templateId          模版id
     * @param formId              表单id
     * @param variableTableId
     * @param queryTable
     * @param queryGroup          查询变量分组标识 0/1
     * @param queryWithoutGroupId 是否查询包含groupId数据记录 0/1
     * @return
     */
    List<TemplateFormDetailVo> getTemplateFormDetailConfigListByFormId(String templateId, String formId, String variableTableId, String queryTable, String queryGroup, String queryWithoutGroupId);

    /**
     * 通过详情id查询表单明细记录
     * @param formDetailId
     * @return
     */
    TemplateFormDetail getTemplateFormDetailConfigByVariableId(String formDetailId);

    /**
     * 删除模板或表单项配置
     * @param projectId 项目id
     * @param formId    表单项id
     * @param operator  操作人
     * @return
     */
    String deleteTemplateFormConfigById(String projectId, String formId, String operator);


    /**
     * 根据templateId删除数据字典系统模版
     * @param templateId
     * @param configType
     * @param userId
     * @return
     */
    String deleteCustomTemplateFormByTemplateId(String templateId, String configType, String userId);


    /**
     * 删除表单指定变量
     * @param projectId
     * @param formId
     * @param id
     * @param forceDelete
     * @return
     */
    String deleteTemplateFormDetailById(String projectId, String formId, String id, String forceDelete, String operator);

    /**
     * 根据projectId和VisitId查询表单明细组件列表
     * @param projectId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @param formTableIds
     * @return
     */
    List<TemplateFormVariableExportVo> getProjectFormAndTableLabelByProjectIdAndFormVariableIds(String projectId, String visitId, String formId, String formDetailId, String formTableIds);

    /**
     * 根据访视id查询表单集合
     *
     * @param projectId
     * @param planId
     * @param visitId
     * @param excludeJoinGroupFromConfig
     * @return
     */
    List<TemplateFormConfigVo> getFormConfigListByVisitId(String projectId, String planId, String visitId, String excludeJoinGroupFromConfig);

    /**
     * 保存编辑访视表单组件条件设置
     * @param templateFormLogicParam
     * @return
     */
    CustomResult saveTemplateFormVariableLogicConfig(TemplateFormLogicParam templateFormLogicParam);

    /**
     * 将当前表单字段集合保存为模板(全局模版、项目模板、系统模版)
     * @param templateCustomFormConfigParam
     * @return
     */
    CustomResult saveCustomTemplateFormConfig(TemplateCustomFormConfigParam templateCustomFormConfigParam);

    /**
     * 查询模板表单列表
     *
     * @param templateId     系统模版id
     * @param templateName   模版名称
     * @param projectId      如果是项目模版请设置此参数
     * @param configType     模版类型 参照说明BusinessConfig
     * @param templateStatus 模版状态
     * @param createUserId   操作人
     * @param tenantId
     * @return
     */
    List<TemplateFormConfigVo> getCustomTemplateFormConfigList(String templateId, String templateName, String projectId, String configType, String templateStatus, String createUserId, String tenantId);

    /**
     * 查询项目表单模板分页列表
     * @param projectId
     * @param templateName
     * @param templateStatus
     * @param pageNum
     * @param pageSize
     * @return
     */
    CommonPage<TemplateFormConfigVo> getCustomTemplateFormConfigForPage(String projectId, String templateName, String templateStatus, Integer pageNum, Integer pageSize);

    /**
     * 查询表单逻辑条件设置列表
     * @param templateId
     * @param projectId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @param testeeId
     * @return
     */
    List<TemplateFormLogicVo> getFormVariableLogicList(String templateId, String projectId, String visitId, String formId, String formDetailId, String testeeId);

    /**
     * 根据分类id查询表单项列表
     * @param projectId
     * @param groupName
     * @return
     */
    List<TemplateFormConfig> getTemplateFormConfigByGroupName(String projectId, String groupName);

    /**
     * 查询数据分析平台同步项目的所有表单和表格变量集合
     * @param projectId
     * @param planId
     * @return
     */
    List<TemplateFormDetail> getResearchTemplateFormVariableList(String projectId, String planId);

    /**
     * 根据项目id查询所有表单集合
     * @param projectId
     * @param queryTable  是否查询表格 0/1
     * @return
     */
    List<TemplateFormConfig> getProjectTemplateFormListByProjectId(String projectId, String queryTable);

    /**
     * 删除访视动态表格单元格变量
     * @param projectId
     * @param tableId
     * @param forceDelete
     * @param operator
     * @return
     */
    String deleteTemplateFormTableById(String projectId, String tableId, boolean forceDelete, String operator);

    /**
     * 查询访视表单基本信息
     * @param formId 表单id
     * @return
     */
    TemplateFormConfig getTemplateFormConfigById(Long formId);

    /**
     * 删除表单逻辑条件设置
     * @param id
     * @return
     */
    CustomResult deleteFormLogicData(String id);

    /**
     * 查询模板表单变量逻辑条件
     * @param templateFormId
     * @param userId
     * @return
     */
    List<TemplateFormLogicVo> getTemplateVariableLoginConfig(String templateFormId, String userId);

    /**
     * 根据表单项id获取表单指定类型变量列表
     * @param templateId
     * @param formId
     * @param variableType
     * @return
     */
    List<TemplateFormDetailVo> getTemplateFormDetailByFormIdAndVariableType(String templateId, String formId, String variableType);

    /**
     * 根据表单项id获取表单基本信息
     * @param formId
     * @return
     */
    TemplateFormConfigVo getTemplateFormConfigBaseInfoByFormId(String formId);

    /**
     * 查询表格变量信息
     * @param formTableId
     * @return
     */
    TemplateFormTable getTemplateFormTableConfigById(Long formTableId);

    /**
     * 查询访视时间所在表单id
     * @param projectId
     * @param preVisitId
     * @param projectLabelVisitName
     * @return
     */
    TemplateFormConfig getFormIdByCoustomVisitName(String projectId, String preVisitId, String projectLabelVisitName);


    /**
     * 查询表单变量是否以已经录入
     * @param projectId
     * @param planId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @param tableId
     * @return
     */
    List<ProjectTesteeFormDetailAndTableResultVo> getTemplateFormDetailAndTableResult(String projectId, String planId, String visitId, String formId, String formDetailId, String tableId);

    /**
     * 根据表格id查询变量集合
     * @param formDetailId
     * @return
     */
    List<TemplateTableVo> getTemplateFormTableByFormDetailId(String formDetailId);

    /**
     * 根据访视表单名称和variable查询详情
     * @param projectId
     * @param visitName
     * @param formName
     * @param variable
     * @return
     */
    TemplateFormDetailExcelImportVo getFormDetailByFormNameAndVariableName(String projectId, String visitName, String formName, String variable);

    /**
     * 根据访视表单名称和variable查询表格变量详情
     * @param projectId
     * @param visitName
     * @param formName
     * @param variable
     * @return
     */
    TemplateFormTableExcelImportVo getFormTableInfoByFormNameAndVariableName(String projectId, String visitName, String formName, String variable);


    /**
     * 查询项目模版名称是否存在
     * @param projectId
     * @param templateName
     * @param configType
     * @return
     */
    Boolean getProjectTemplateNameResult(String projectId, String templateName, String configType);


    /**
     * 查询表单配置名称是否存在
     * @param projectId
     * @param formConfigName
     * @return
     */
    Boolean getProjectTemplateFormConfigNameResult(String projectId, String formConfigName);

    /**
     * 根据表单id获取表单变量分页列表
     * @param templateId
     * @param formId
     * @param formDetailName
     * @param type
     * @param pageNum
     * @param pageSize
     * @return
     */
    List<TemplateFormVariableWrapper> getTemplateFormVariableListByFormIdForPage(String templateId, String formId, String formDetailName, String type, Integer pageNum, Integer pageSize);


    /**
     * 根据分组id查询对应变量数据
     * @param formId            表单id
     * @param groupId           参与者分组id
     * @param groupTableId
     * @param queryIgnoreStatus
     * @return
     */
    List<TemplateFormDetailVo> getTemplateFormDetailByGroupId(String formId, String groupId, String groupTableId, Boolean queryIgnoreStatus);

    /**
     * 根据fieldName查询字段信息
     * @param fieldName
     * @return
     */
    String getTemplateFormDetailConfigByFieldName(String fieldName);

    /**
     * 发布、取消表单配置
     * @param projectId
     * @param planId
     * @param formId
     * @param publishStatus
     * @param userId
     * @return
     */
    CustomResult savePublishTemplateFormConfig(String projectId, String planId, String formId, boolean publishStatus, String userId);

    /**
     * 批量发布表单
     * @param projectId
     * @param formIds
     * @param planId
     * @param userId
     * @return
     */
    CustomResult batchPublishFormTemplateFormConfig(String projectId, String formIds, String planId, String userId);

    /**
     * 根据项目案查询表单列表
     * @param templateId
     * @param projectId
     * @param formName
     * @param publishStatus
     * @return
     */
    List<TemplateFormConfigVo> getTemplateFormConfigListByProjectId(String templateId, String projectId, String formName, Boolean publishStatus);


    /**
     * 查询表单code是否存在
     * @param templateId
     * @param projectId
     * @param formCode
     * @return
     */
    Boolean getTemplateFormCode(String templateId, String projectId, String formCode);

    /**
     * 查询表单字段code是否存在
     * @param projectId
     * @param formId
     * @param formDetailId
     * @param fieldName
     * @return
     */
    Boolean getTemplateFormDetailCode(String projectId, String formId, String formDetailId, String fieldName);

    /**
     * 查询表格字段code是否存在
     * @param projectId
     * @param formDetailId
     * @param formTableId
     * @param fieldName
     * @return
     */
    Boolean getTemplateFormTableCodeResult(String projectId, String formDetailId, String formTableId, String fieldName);


    Boolean checkSystemDictionaryTypeReference(String dictionaryId);

    Boolean checkProjectDictionaryTypeReference(String projectId, String dictionaryId);

    Boolean checkSystemDictionaryOptionReference(String dictionaryId);

    Boolean checkProjectDictionaryOptionReference(String projectId, String dictionaryId);

    /**
     * 自定义标题列表
     * @param projectId
     * @param fieldName
     * @param showTitle
     * @return
     */
    List<TemplateFormDetailVo> getTesteeCustomTitleList(String projectId, String fieldName, String showTitle);

    /**
     * 启用、停用自定义标题
     * @param id
     * @param showTitle
     * @return
     */
    CustomResult modifyCustomTitleStatus(String id, String showTitle);

    /**
     * 自定义标题-创建表单-保存字段
     * @param templateFormDetailParam
     * @return
     */
    CustomResult saveTemplateTesteeFormDetailConfig(TemplateFormDetailParam templateFormDetailParam);

    /**
     * 查询参与者基本信息表单
     * @param projectId
     * @return
     */
    TemplateFormConfigVo getTemplateTesteeFormBaseInfo(String projectId);

    /**
     * 查询参与者表单自定义字段信息
     * @param projectId
     * @param formId
     * @param showTitle
     * @return
     */
    List<TemplateFormDetailVo> getTemplateTesteeFormDetailBaseInfo(String projectId, String formId, Boolean showTitle);

    /**
     * 查询字段详情
     * @param projectId
     * @param formId
     * @param fieldName
     * @return
     */
    TemplateFormDetailVo getTemplateTesteeFormDetailConfig(String projectId, String formId, String fieldName);

    /**
     * 排序保存
     * @param ids
     * @return
     */
     CustomResult editSortByIds(String[] ids);

    /**
     * 查询联动控制表单变量列表
     * @param projectId
     * @param formId
     * @param optionId
     * @return
     */
     List<TemplateFormVariableViewVo> getTemplateCurrentFormVariableList(String projectId, String formId, String optionId);

     /**
     * 查询公式计算表单变量列表
     * @param projectId
     * @param formId
     * @return
     */
     List<TemplateFormVariableViewVo> getFormVariableAndRuleList(String projectId, String formId);

    /**
     * 查询表单变量列表
     * @return
     */
    List<TemplateVariableVo> getTemplateVariableDetailConfig();
    
    TemplateLabConfigVo getProjectTemplateLabConfigDetail(String id);
    
    /**
     * 保存实验室配置
     * @param templateLabConfigParam
     * @return
     */
    CustomResult saveTemplateFormVariableLabConfig(TemplateLabConfigParam templateLabConfigParam);
    
    CustomResult deleteProjectTemplateLabConfig(String id);
    
    CustomResult saveProjectTemplateLabConfigForOrg(String labConfigId, String projectOrgId, boolean applyTotalOrg);
    
    TemplateLabConfigExpand getTemplateLabConfigByVariableId(String projectOrgId, String labConfigScope, String visitId, String formId, String variableId);
    
    TemplateLabConfigExpand getTemplateLabConfigByVariableIdAndTableId(String projectOrgId, String labConfigScope, String visitId, String formId, String variableId, String tableId);
    
    TemplateLabConfigExpand getTemplateLabConfigByGroupIdAndVariableId(String projectOrgId, String labConfigScope, String visitId, String formId, String groupId, String variableId);
    
    TemplateLabConfigExpand getTemplateLabConfigByGroupIdAndTableId(String projectOrgId, String labConfigScope, String visitId, String formId, String groupId, String variableId, String tableId);
    
    CommonPage<TemplateLabConfigVo> getProjectTemplateLabConfigListForPage(String projectId, String variableName, String labType, Integer pageNum, Integer pageSize);
    
    TemplateTesteeJoinGroupFormVo getTemplateTesteeJoinGroupFormConfig(String projectId, String planId);
    
    List<TemplateFormDetailVo> getTemplateFormVariableListByFormId(String projectId, String formId, boolean enableRandomizedConfig);
    
    TemplateFormConfig getProjectTemplateFormICF(String projectId, String formCode);
    
    TemplateFormDetail getTemplateVariableValueForICFDATE(String projectId, String formId, String variableCode);
}
