package com.haoys.user.domain.param.crf;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
public class TemplateFormDvpRuleParam implements Serializable {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "逻辑核查编号")
    private String code;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @ApiModelProperty(value = "表单id")
    private Long formId;

    @ApiModelProperty(value = "变量id")
    private Long formDetailId;

    @ApiModelProperty(value = "是否为定制表格")
    private Boolean customTable = false;

    @ApiModelProperty(value = "表格变量id")
    private Long formTableId;

    @ApiModelProperty(value = "定制表格行标记")
    private String tableRowIndex;

    @ApiModelProperty(value = "相关变量")
    private String formRelationId;

    @ApiModelProperty(value = "质疑类型")
    private String queryType;

    @ApiModelProperty(value = "质疑方式1-是否必填 2-日期判断 3-字符型. . . ")
    private String queryMethod;

    @ApiModelProperty(value = "逻辑表达式")
    private String conditionValue;

    @ApiModelProperty(value = "目标值")
    private String targetValue;

    @ApiModelProperty(value = "质疑内容")
    private String content;

    @ApiModelProperty(value = "启用-1 停用-0")
    private String enabled;

    @ApiModelProperty(value = "数据状态")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人id")
    private String createUserId;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;


}
