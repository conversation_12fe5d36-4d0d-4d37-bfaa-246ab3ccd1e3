package com.haoys.user.domain.param.auth;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ProjectUserAuthParam {

    private String systemUserId;

    private String projectId;

    private String userName;

    private String realName;

    private String mobile;

    private String email;

    private List<ProjectOrgRoleInfo> projectOrgRoleList = new ArrayList<>();

    private List<ProjectRoleParam> projectRoleList = new ArrayList<>();

    private String departmentId;

    private String departmentName;

    private String createUserId;

    @ApiModelProperty(value = "用户注册方式")
    private String registerType;

    @Data
    public static class ProjectOrgRoleInfo {

        private Long roleId;

        private String roleName;

        private String ename;

        private Boolean ownerTotalAuth = false;

        private String projectId;

        private List<ProjectOrgInfo> projectOrgList = new ArrayList<>();
    }

    @Data
    public static class ProjectOrgInfo {

        private Long orgId;

        private Long projectOrgId;

        private String orgName;

        private String projectOrgCode;

    }

}
