package com.haoys.user.model;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class TemplateVariableViewConfig implements Serializable {
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "变量选项id值")
    private Long variableOptionId;

    @ApiModelProperty(value = "模板id")
    private Long templateId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "表单项id")
    private Long formId;

    @ApiModelProperty(value = "表单/表格变量id")
    private Long formDetailId;

    @ApiModelProperty(value = "表格列变量id")
    private Long formTableId;

    @ApiModelProperty(value = "字段组id")
    private Long groupId;

    @ApiModelProperty(value = "变量名称")
    private String label;

    @ApiModelProperty(value = "是否显示 1-显示 0-不显示")
    private String viewConfigResult;

    @ApiModelProperty(value = "创建人")
    private String createUserId;

    @ApiModelProperty(value = "修改人")
    private String updateUserId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "扩展字段")
    private String extands;

    @ApiModelProperty(value = "数据状态0/1")
    private String status;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getVariableOptionId() {
        return variableOptionId;
    }

    public void setVariableOptionId(Long variableOptionId) {
        this.variableOptionId = variableOptionId;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getFormId() {
        return formId;
    }

    public void setFormId(Long formId) {
        this.formId = formId;
    }

    public Long getFormDetailId() {
        return formDetailId;
    }

    public void setFormDetailId(Long formDetailId) {
        this.formDetailId = formDetailId;
    }

    public Long getFormTableId() {
        return formTableId;
    }

    public void setFormTableId(Long formTableId) {
        this.formTableId = formTableId;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getViewConfigResult() {
        return viewConfigResult;
    }

    public void setViewConfigResult(String viewConfigResult) {
        this.viewConfigResult = viewConfigResult;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public String getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getExtands() {
        return extands;
    }

    public void setExtands(String extands) {
        this.extands = extands;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", variableOptionId=").append(variableOptionId);
        sb.append(", templateId=").append(templateId);
        sb.append(", projectId=").append(projectId);
        sb.append(", formId=").append(formId);
        sb.append(", formDetailId=").append(formDetailId);
        sb.append(", formTableId=").append(formTableId);
        sb.append(", groupId=").append(groupId);
        sb.append(", label=").append(label);
        sb.append(", viewConfigResult=").append(viewConfigResult);
        sb.append(", createUserId=").append(createUserId);
        sb.append(", updateUserId=").append(updateUserId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", extands=").append(extands);
        sb.append(", status=").append(status);
        sb.append(", tenantId=").append(tenantId);
        sb.append(", platformId=").append(platformId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}