package com.haoys.user.domain.vo.flow;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ProjectFlowVo {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    private Long id;

    @JsonFormat(shape= JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "计划id")
    private Long planId;

    @ApiModelProperty(value = "访视名称")
    private String visitName;

    @ApiModelProperty(value = "访视类型（1-单次录入 2-多次录入）")
    private String visitType;

    @ApiModelProperty(value = "访视归属阶段")
    private String ownerPeroid;

    @ApiModelProperty(value = "访视表单")
    private List<ProjectFlowFormVo> formList;


}
