package com.haoys.user.domain.param.project;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class ProjectPatientPlanParam implements Serializable {


    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "方案名称")
    private String planName;

    @ApiModelProperty(value = "是否开启前置条件 0-不开启 1-开启")
    private Boolean ifOpen;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @ApiModelProperty(value = "表单id")
    private Long formId;

    @ApiModelProperty(value = "变量id")
    private Long formDetailId;

    @ApiModelProperty(value = "变量值,多个值使用英文逗号隔开")
    private String formDetailValue;

    @ApiModelProperty(value = "数据状态 0-有效 1-无效")
    private String status;

    @ApiModelProperty(value = "创建人")
    private String createUserId;

    @ApiModelProperty(value = "任务详情变量集合")
    private List<ProjectPatientPlanParam.ProjectPatientPlanVariableParam> dataList = new ArrayList<>();

    @Data
    public static class ProjectPatientPlanVariableParam {
        @ApiModelProperty(value = "主键id")
        private Long id;

        @ApiModelProperty(value = "项目id")
        private Long projectId;

        @ApiModelProperty(value = "方案id")
        private Long planId;

        @ApiModelProperty(value = "任务id")
        private Long taskId;

        @ApiModelProperty(value = "是否开启访视窗口期")
        private Boolean ifVisitWindow;

        @ApiModelProperty(value = "窗口开始天数")
        private Integer startVisitValue;

        @ApiModelProperty(value = "窗口截止天数")
        private Integer endVisitValue;

        @ApiModelProperty(value = "任务触发频率")
        private String sendRate;

        @ApiModelProperty(value = "任务触发访视id")
        private Long baseVisitId;

        @ApiModelProperty(value = "任务触发表单id")
        private Long baseFormId;

        @ApiModelProperty(value = "任务触发变量-时间")
        private Long baseVariableId;

        @ApiModelProperty(value = "任务触发基础时间值")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date baseVisitValue;

        @ApiModelProperty(value = "基础时间间隔天数（单位：天）")
        private Integer baseVisitInterval;

        @ApiModelProperty(value = "单日频次")
        private Integer daySendRate;

        @ApiModelProperty(value = "扩展字段1")
        private String extData01;

        @ApiModelProperty(value = "扩展字段2")
        private String extData02;

        @ApiModelProperty(value = "扩展字段3")
        private String extData03;

        @ApiModelProperty(value = "扩展字段4")
        private String extData04;

        @ApiModelProperty(value = "扩展字段5")
        private String extData05;

        @ApiModelProperty(value = "数据状态")
        private String status;

        @ApiModelProperty(value = "排序")
        private Integer sort;

    }
}
