package com.haoys.user.model;

import java.io.Serializable;

public class ProjectTasteStat implements Serializable {
    private Long index;

    private String batchCode;

    private String dataJson;

    private static final long serialVersionUID = 1L;

    public Long getIndex() {
        return index;
    }

    public void setIndex(Long index) {
        this.index = index;
    }

    public String getBatchCode() {
        return batchCode;
    }

    public void setBatchCode(String batchCode) {
        this.batchCode = batchCode;
    }

    public String getDataJson() {
        return dataJson;
    }

    public void setDataJson(String dataJson) {
        this.dataJson = dataJson;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", index=").append(index);
        sb.append(", batchCode=").append(batchCode);
        sb.append(", dataJson=").append(dataJson);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}