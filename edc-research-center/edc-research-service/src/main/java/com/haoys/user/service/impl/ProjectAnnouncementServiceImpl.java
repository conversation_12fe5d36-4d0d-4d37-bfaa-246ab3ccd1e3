package com.haoys.user.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.IdUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.bean.BeanUtils;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.file.FileUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.project.ProjectAnnouncementParam;
import com.haoys.user.domain.vo.UploadFileResultVo;
import com.haoys.user.domain.vo.project.ProjectAnnouncementVo;
import com.haoys.user.domain.vo.system.OrganizationVo;
import com.haoys.user.domain.vo.testee.UploadRichTextFileVo;
import com.haoys.user.mapper.ProjectAnnouncementMapper;
import com.haoys.user.model.SystemFileInfo;
import com.haoys.user.model.ProjectAnnouncement;
import com.haoys.user.service.AsyncFileUploadService;
import com.haoys.user.service.OrganizationService;
import com.haoys.user.service.ProjectAnnouncementService;
import com.haoys.user.service.SystemFileInfoService;
import com.haoys.user.storge.cloud.OssStorageConfig;
import com.haoys.user.storge.cloud.OssStorageFactory;
import com.haoys.user.storge.cloud.localStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

@Service
public class ProjectAnnouncementServiceImpl extends BaseService implements ProjectAnnouncementService {

    @Autowired
    private ProjectAnnouncementMapper projectAnnouncementMapper;
    @Autowired
    private SystemFileInfoService systemFileInfoService;
    @Autowired
    private OssStorageConfig storageConfig;
    @Autowired
    private localStorageService torageService;
    @Autowired
    private AsyncFileUploadService asyncFileUploadService;
    @Autowired
    private OrganizationService organizationService;


    private static final String ROOT_PATH = "mis-research";
    private static final String TEMPLATE_FORM_CONFIG_PATH = "project-announcement";

    /**
     * 获取公告列表
     * @param projectAnnouncement
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    public CommonPage<ProjectAnnouncementVo> selectProjectAnnouncementList(ProjectAnnouncement projectAnnouncement, Integer pageNum, Integer pageSize){
        Page<Object> page = PageHelper.startPage(pageNum,pageSize);
        return commonPageListWrapper(pageNum, pageSize, page, projectAnnouncementMapper.selectProjectAnnouncementList(projectAnnouncement));
    }

    /**
     * 根据id获取公告
     * @param id
     * @return
     */
    @Override
    public ProjectAnnouncementVo selectProjectAnnouncementById(Long id){
        ProjectAnnouncement projectAnnouncement = projectAnnouncementMapper.selectByPrimaryKey(id);
        ProjectAnnouncementVo ProjectAnnouncementVo = new ProjectAnnouncementVo();
        BeanUtils.copyProperties(projectAnnouncement, ProjectAnnouncementVo);
        if(StringUtils.isNotEmpty(projectAnnouncement.getFileIds())){
            String[] fileIds = projectAnnouncement.getFileIds().split(",");
            BeanUtils.copyProperties(projectAnnouncement, ProjectAnnouncementVo);
            for(int i = 0 ; i < fileIds.length; i++){
                SystemFileInfo systemFileInfo = systemFileInfoService.selectByPrimaryKey(Long.valueOf(fileIds[i]));
                ProjectAnnouncementVo.getSystemFileInfoList().add(systemFileInfo);
            }
        }
        return ProjectAnnouncementVo;
    }

    /**
     * 获取项目中心列表
     * @param projectId
     * @return
     */
    @Override
    public List<OrganizationVo> getProjectOrgList(String projectId){
        return organizationService.getOrganizationListForCombobox(projectId, "", "", "", "");

    }

    /**
     * 添加公告
     * @param userId
     * @param projectAnnouncementParam
     * @return
     */
    @Override
    public int addProjectAnnouncement(String userId, ProjectAnnouncementParam projectAnnouncementParam){
        Long id = SnowflakeIdWorker.getUuid();
        List<ProjectAnnouncementParam.FileData> fileData = projectAnnouncementParam.getFileData();
        String fileIds = "";
        if(!fileData.isEmpty()){
            for(ProjectAnnouncementParam.FileData t : fileData){
                SystemFileInfo systemFileInfo = new SystemFileInfo();
                systemFileInfo.setFileName(t.getNewFileName());
                systemFileInfo.setOriginalName(t.getOriginalFilename());
                systemFileInfo.setUploadPath(t.getFileName());
                systemFileInfo.setFileUrl(t.getUrl());
                systemFileInfo.setTenantId(String.valueOf(SecurityUtils.getSystemTenantId()));
                systemFileInfo.setPlatformId(String.valueOf(SecurityUtils.getSystemTenantId()));
                systemFileInfo.setStatus("0");
                systemFileInfo.setCreateTime(new Date());
                systemFileInfo.setCreateUserId(userId);
                systemFileInfoService.insert(systemFileInfo);
                fileIds += systemFileInfo.getId() + ",";
            }

        }
        ProjectAnnouncement projectAnnouncement = new ProjectAnnouncement();
        BeanUtils.copyProperties(projectAnnouncementParam, projectAnnouncement);
        projectAnnouncement.setCreateUser(userId);
        projectAnnouncement.setCreateTime(new Date());
        projectAnnouncement.setTenantId(String.valueOf(SecurityUtils.getSystemTenantId()));
        projectAnnouncement.setPlatformId(String.valueOf(SecurityUtils.getSystemTenantId()));
        projectAnnouncement.setStatus("0");
        projectAnnouncement.setId(id);
        projectAnnouncement.setFileIds(fileIds);
        return projectAnnouncementMapper.insert(projectAnnouncement);
    }

    /**
     * 发布/撤销
     * @param param
     * @return
     */
    @Override
    public int editPublish(ProjectAnnouncement param){
        ProjectAnnouncement projectAnnouncement = projectAnnouncementMapper.selectByPrimaryKey(param.getId());
        if("0".equals(param.getStatus())){
            projectAnnouncement.setStatus(param.getStatus());
        }else {
            projectAnnouncement.setStatus(param.getStatus());
            projectAnnouncement.setType(param.getType());
            projectAnnouncement.setSystemType(param.getSystemType());
            projectAnnouncement.setProjectOrgId(param.getProjectOrgId());
            projectAnnouncement.setUpdateUser(param.getUpdateUser());
            projectAnnouncement.setUpdateTime(new Date());
        }
        return projectAnnouncementMapper.updateByPrimaryKey(projectAnnouncement);
    }

    /**
     * 编辑公告
     * @param userId
     * @param param
     * @return
     */
    @Override
    public int editProjectAnnouncement(String userId, ProjectAnnouncementParam param){
        ProjectAnnouncement projectAnnouncement = projectAnnouncementMapper.selectByPrimaryKey(param.getId());
        String fileIds = projectAnnouncement.getFileIds() == null ? "" : projectAnnouncement.getFileIds();//当前附件ids
        String delFileIds = param.getDelFileIds() == null ? "" : param.getDelFileIds();//已删除附件ids
        if(!"".equals(delFileIds)){
            String[] fileIdArr = fileIds.split(",");
            String[] delFileIdArr = delFileIds.split(",");
            ArrayList<String> fileIdList = new ArrayList<>(fileIdArr.length);
            Collections.addAll(fileIdList,fileIdArr);
            for(int i = 0 ; i< delFileIdArr.length ; i++){
                systemFileInfoService.deleteByPrimaryKey(Long.valueOf(delFileIdArr[i]));
                fileIdList.remove(delFileIdArr[i]);
            }
            String ids = "";
            for(String s :fileIdList){
                ids += s + ",";
            }
            fileIds = ids;
        }

        List<ProjectAnnouncementParam.FileData> fileData = param.getFileData();
        if(!fileData.isEmpty()){
            for(ProjectAnnouncementParam.FileData t : fileData){
                SystemFileInfo systemFileInfo = new SystemFileInfo();
                systemFileInfo.setFileName(t.getNewFileName());
                systemFileInfo.setOriginalName(t.getOriginalFilename());
                systemFileInfo.setUploadPath(t.getFileName());
                systemFileInfo.setFileUrl(t.getUrl());
                systemFileInfo.setTenantId(String.valueOf(SecurityUtils.getSystemTenantId()));
                systemFileInfo.setPlatformId(String.valueOf(SecurityUtils.getSystemTenantId()));
                systemFileInfo.setStatus("0");
                systemFileInfo.setCreateTime(new Date());
                systemFileInfo.setCreateUserId(userId);
                systemFileInfoService.insert(systemFileInfo);
                fileIds += systemFileInfo.getId() + ",";
            }

        }
        BeanUtils.copyProperties(param, projectAnnouncement);
        projectAnnouncement.setFileIds(fileIds);
        return projectAnnouncementMapper.updateByPrimaryKeySelective(projectAnnouncement);
    }

    /**
     * 删除公告
     * @param id
     * @return
     */
    @Override
    public int deleteProjectAnnouncement(Long id){
        ProjectAnnouncement projectAnnouncement = projectAnnouncementMapper.selectByPrimaryKey(id);
        if(!"".equals(projectAnnouncement.getFileIds())){
            String[] fileIds = projectAnnouncement.getFileIds().split(",");
            for(int i = 0 ; i < fileIds.length; i++){
                systemFileInfoService.deleteByPrimaryKey(Long.valueOf(fileIds[i]));
            }
        }
        return projectAnnouncementMapper.deleteByPrimaryKey(id);
    }

    /**
     * 上传附件（非阻塞IO版本）
     * @param file
     * @return
     * @throws IOException
     */
    @Override
    public List<UploadFileResultVo> saveUpload(MultipartFile file) throws IOException {
        List<UploadFileResultVo> dataList = new ArrayList();
        if(file != null){
            String path = new StringBuffer(ROOT_PATH + "/announcement/" + String.valueOf(SecurityUtils.getSystemTenantId()))
                    .append(CharUtil.SLASH)
                    .append(TEMPLATE_FORM_CONFIG_PATH)
                    .append(CharUtil.SLASH)
                    .append(IdUtil.simpleUUID())
                    .append(CharUtil.DOT)
                    .append(FileUtil.extName(file.getOriginalFilename())).toString();

            // 使用非阻塞IO上传
            String url = uploadFileNonBlocking(file, path);

            // 异步上传到本地存储作为备份
            asyncFileUploadService.uploadToLocalStorageAsync(file, path);

            UploadFileResultVo uploadFileResultVo = new UploadFileResultVo();
            uploadFileResultVo.setFileName(storageConfig.getUploadFolder() + path);
            uploadFileResultVo.setNewFileName(FileUtils.getName(path));
            uploadFileResultVo.setOriginalFilename(file.getOriginalFilename());
            uploadFileResultVo.setFileNameKey(FileUtils.getNameNotSuffix(path));
            uploadFileResultVo.setUrl(url);
            dataList.add(uploadFileResultVo);

        }
        return dataList;
    }

    /**
     * 移动端获取项目列表
     * @param projectAnnouncement
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    public CommonPage<ProjectAnnouncementVo> selectProjectAnnouncementEprList(ProjectAnnouncement projectAnnouncement, Integer pageNum, Integer pageSize){
        Page<Object> page = PageHelper.startPage(pageNum,pageSize);
        projectAnnouncement.setStatus("1");
        projectAnnouncement.setSystemType("2");
        projectAnnouncement.setType("2");
        List<ProjectAnnouncementVo> ProjectAnnouncementVoList = projectAnnouncementMapper.selectProjectAnnouncementList(projectAnnouncement);
        for(ProjectAnnouncementVo sv : ProjectAnnouncementVoList){
            if(StringUtils.isNotEmpty(sv.getFileIds())){
                String[] fileIds = sv.getFileIds().split(",");
                for(int i = 0 ; i < fileIds.length; i++){
                    SystemFileInfo systemFileInfo = systemFileInfoService.selectByPrimaryKey(Long.valueOf(fileIds[i]));
                    sv.getSystemFileInfoList().add(systemFileInfo);
                }
            }
        }
        return commonPageListWrapper(pageNum, pageSize, page, ProjectAnnouncementVoList);
    }

    /**
     * 富文本附件上传（非阻塞IO版本）
     * @param file
     * @return
     * @throws IOException
     */
    @Override
    public UploadRichTextFileVo uploadRichTextFile(MultipartFile file) throws IOException {
        UploadRichTextFileVo uploadRichTextFileVo = new UploadRichTextFileVo();
        if(file != null){
            String path = new StringBuffer(ROOT_PATH + "/announcement/" + String.valueOf(SecurityUtils.getSystemTenantId()))
                    .append(CharUtil.SLASH)
                    .append(TEMPLATE_FORM_CONFIG_PATH)
                    .append(CharUtil.SLASH)
                    .append(IdUtil.simpleUUID())
                    .append(CharUtil.DOT)
                    .append(FileUtil.extName(file.getOriginalFilename())).toString();

            // 使用非阻塞IO上传
            String url = uploadFileNonBlocking(file, path);

            // 异步上传到本地存储作为备份
            asyncFileUploadService.uploadToLocalStorageAsync(file, path);

            String fileName = url.substring(url.lastIndexOf("/") + 1);
            uploadRichTextFileVo.setUrl(url);
            uploadRichTextFileVo.setHref(url);
            uploadRichTextFileVo.setAlt(fileName);
        }
        return uploadRichTextFileVo;
    }

    /**
     * 非阻塞文件上传辅助方法
     *
     * @param file 文件
     * @param path 存储路径
     * @return 文件URL
     * @throws IOException IO异常
     */
    private String uploadFileNonBlocking(MultipartFile file, String path) throws IOException {
        try {
            // 使用CompletableFuture实现非阻塞上传
            CompletableFuture<String> uploadFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    InputStream inputStream = file.getInputStream();
                    return OssStorageFactory.build().upload(inputStream, path);
                } catch (Exception e) {
                    throw new RuntimeException("文件上传失败: " + file.getOriginalFilename(), e);
                }
            });

            // 根据文件大小计算超时时间
            long timeoutSeconds = calculateUploadTimeout(file.getSize());

            return uploadFuture.get(timeoutSeconds, TimeUnit.SECONDS);

        } catch (TimeoutException e) {
            throw new IOException("文件上传超时: " + file.getOriginalFilename(), e);
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof IOException) {
                throw (IOException) cause;
            } else {
                throw new IOException("文件上传失败: " + file.getOriginalFilename(), cause);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("文件上传被中断: " + file.getOriginalFilename(), e);
        }
    }

    /**
     * 根据文件大小计算上传超时时间
     *
     * @param fileSize 文件大小（字节）
     * @return 超时时间（秒）
     */
    private long calculateUploadTimeout(long fileSize) {
        // 基础超时时间：30秒
        long baseTimeout = 30;

        // 每MB增加5秒超时时间
        long additionalTimeout = (fileSize / (1024 * 1024)) * 5;

        // 最小30秒，最大300秒（5分钟）
        return Math.max(baseTimeout, Math.min(baseTimeout + additionalTimeout, 300));
    }
}
