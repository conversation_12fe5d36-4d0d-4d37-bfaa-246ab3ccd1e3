package com.haoys.user.domain.vo.testee;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.haoys.user.enums.FormVariableComplateStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 访视统计数据返回类
 */

@Data
public class ProjectTesteeStatisticsVo {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "参与者用户id")
    private Long id;

    @ApiModelProperty(value = "参与者姓名")
    private String realName;

    @ApiModelProperty(value = "参与者编号")
    private String code;

    @ApiModelProperty(value = "所属中心名称")
    private String ownerOrgName;

    @ApiModelProperty(value = "所属中心id")
    private String ownerOrgId;

    @ApiModelProperty(value = "主管医生姓名")
    private String ownerDoctorName;

    @ApiModelProperty(value = "主管医生Id")
    private String ownerDoctorId;

    @ApiModelProperty(value = "主管医生-当前登录人用户id")
    private String ownerDoctor;

    @ApiModelProperty(value = "访视名称")
    private String visitName;

    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @ApiModelProperty(value = "访视id")
    private Long projectId;

    @ApiModelProperty(value = "访视进度")
    private String viewPlan= FormVariableComplateStatus.FORM_VAR_NO_FILL.getValue();

    @ApiModelProperty(value = "计划访视时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planViewDate;
}
