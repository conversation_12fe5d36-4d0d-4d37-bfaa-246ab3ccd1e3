package com.haoys.user.mapper;

import com.haoys.user.domain.entity.AiTokenUsage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * AI Token使用统计Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Mapper
public interface AiTokenUsageMapper {
    
    /**
     * 插入使用记录
     */
    int insert(AiTokenUsage usage);
    
    /**
     * 根据ID查询记录
     */
    AiTokenUsage selectById(@Param("id") Long id);
    
    /**
     * 根据用户ID查询使用记录
     */
    List<AiTokenUsage> selectByUserId(@Param("userId") String userId,
                                     @Param("startDate") Date startDate,
                                     @Param("endDate") Date endDate,
                                     @Param("offset") Integer offset,
                                     @Param("limit") Integer limit);
    
    /**
     * 统计用户Token使用情况
     */
    Map<String, Object> selectUserUsageStats(@Param("userId") String userId,
                                            @Param("startDate") Date startDate,
                                            @Param("endDate") Date endDate);
    
    /**
     * 统计用户今日使用情况
     */
    Map<String, Object> selectUserTodayUsage(@Param("userId") String userId);
    
    /**
     * 统计用户本月使用情况
     */
    Map<String, Object> selectUserMonthUsage(@Param("userId") String userId);
    
    /**
     * 根据模型类型统计使用情况
     */
    List<Map<String, Object>> selectUsageByModel(@Param("startDate") Date startDate,
                                                @Param("endDate") Date endDate);
    
    /**
     * 统计每日使用趋势
     */
    List<Map<String, Object>> selectDailyUsageTrend(@Param("userId") String userId,
                                                   @Param("days") Integer days);
    
    /**
     * 统计每小时使用分布
     */
    List<Map<String, Object>> selectHourlyUsageDistribution(@Param("userId") String userId,
                                                           @Param("date") Date date);
    
    /**
     * 查询用户当日消费金额
     */
    BigDecimal selectUserDailyCost(@Param("userId") String userId, @Param("date") Date date);
    
    /**
     * 查询用户当月消费金额
     */
    BigDecimal selectUserMonthlyCost(@Param("userId") String userId, @Param("year") Integer year, @Param("month") Integer month);
    
    /**
     * 查询系统总体使用统计
     */
    Map<String, Object> selectSystemUsageStats(@Param("startDate") Date startDate,
                                              @Param("endDate") Date endDate);
    
    /**
     * 查询Top用户使用排行
     */
    List<Map<String, Object>> selectTopUsers(@Param("startDate") Date startDate,
                                            @Param("endDate") Date endDate,
                                            @Param("limit") Integer limit);
}
