package com.haoys.user.generator.config;

import lombok.Data;

/**
 * 代码生成器配置类
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
@Data
public class GeneratorConfig {
    
    // ========== 数据库配置 ==========
    /**
     * JDBC驱动类
     */
    private String jdbcDriver;
    
    /**
     * 数据库连接URL
     */
    private String jdbcUrl;
    
    /**
     * 数据库用户名
     */
    private String jdbcUsername;
    
    /**
     * 数据库密码
     */
    private String jdbcPassword;
    
    // ========== 包配置 ==========
    /**
     * 基础包名
     */
    private String basePackage;
    
    /**
     * 实体类包名
     */
    private String modelPackage;
    
    /**
     * Mapper接口包名
     */
    private String mapperPackage;
    
    /**
     * Service接口包名
     */
    private String servicePackage;
    
    /**
     * Service实现类包名
     */
    private String serviceImplPackage;
    
    /**
     * Controller包名
     */
    private String controllerPackage;

    /**
     * Controller文件路径（可能在不同模块）
     */
    private String controllerPath;
    
    // ========== 路径配置 ==========
    /**
     * Java源码路径
     */
    private String javaPath;
    
    /**
     * 资源文件路径
     */
    private String resourcePath;
    
    /**
     * 测试代码路径
     */
    private String testPath;
    
    // ========== 表配置 ==========
    /**
     * 数据库表名
     */
    private String tableName;
    
    /**
     * 实体类名
     */
    private String entityName;
    
    /**
     * 表前缀（用于去除生成类名时的前缀）
     */
    private String tablePrefix;
    
    // ========== 功能配置 ==========
    /**
     * 是否启用Swagger注解
     */
    private boolean enableSwagger = true;
    
    /**
     * 是否启用缓存功能
     */
    private boolean enableCache = true;
    
    /**
     * 是否启用批量操作
     */
    private boolean enableBatchOperations = true;
    
    /**
     * 是否启用复杂查询
     */
    private boolean enableComplexQuery = true;
    
    /**
     * 是否启用BLOB字段查询
     */
    private boolean enableBlobQuery = true;
    
    /**
     * 是否启用聚合查询
     */
    private boolean enableAggregateQuery = true;
    
    /**
     * 是否覆盖已存在的文件
     */
    private boolean overwriteFiles = false;
    
    // ========== 作者信息 ==========
    /**
     * 作者
     */
    private String author = "system";
    
    /**
     * 版本
     */
    private String version = "1.0.0";
    
    /**
     * 模块描述
     */
    private String moduleDescription;
    
    // ========== 模板配置 ==========
    /**
     * 实体类模板路径
     */
    private String entityTemplatePath = "templates/entity.ftl";
    
    /**
     * Mapper接口模板路径
     */
    private String mapperTemplatePath = "templates/mapper.ftl";
    
    /**
     * Mapper XML模板路径
     */
    private String mapperXmlTemplatePath = "templates/mapper-xml.ftl";
    
    /**
     * Service接口模板路径
     */
    private String serviceTemplatePath = "templates/service.ftl";
    
    /**
     * Service实现类模板路径
     */
    private String serviceImplTemplatePath = "templates/service-impl.ftl";
    
    /**
     * Controller模板路径
     */
    private String controllerTemplatePath = "templates/controller.ftl";
    
    /**
     * 测试类模板路径
     */
    private String testTemplatePath = "templates/test.ftl";
    
    // ========== 辅助方法 ==========
    
    /**
     * 获取Mapper XML文件路径
     */
    public String getMapperXmlPath() {
        return resourcePath + "/" + mapperPackage.replace(".", "/");
    }
    
    /**
     * 获取实体类文件路径
     */
    public String getModelPath() {
        return javaPath + "/" + modelPackage.replace(".", "/");
    }
    
    /**
     * 获取Mapper接口文件路径
     */
    public String getMapperPath() {
        return javaPath + "/" + mapperPackage.replace(".", "/");
    }
    
    /**
     * 获取Service接口文件路径
     */
    public String getServicePath() {
        return javaPath + "/" + servicePackage.replace(".", "/");
    }
    
    /**
     * 获取Service实现类文件路径
     */
    public String getServiceImplPath() {
        return javaPath + "/" + serviceImplPackage.replace(".", "/");
    }
    
    /**
     * 获取Controller文件路径
     */
    public String getControllerPath() {
        // 如果设置了自定义Controller路径，则使用自定义路径
        if (controllerPath != null && !controllerPath.trim().isEmpty()) {
            return controllerPath;
        }
        // 否则使用默认路径
        return javaPath + "/" + controllerPackage.replace(".", "/");
    }
    
    /**
     * 获取测试类文件路径
     */
    public String getTestClassPath() {
        return testPath + "/" + servicePackage.replace(".", "/");
    }
}
