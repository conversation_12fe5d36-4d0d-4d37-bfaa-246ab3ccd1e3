package com.haoys.user.mapper;

import com.haoys.user.domain.vo.flow.FlowPlanFormVo;
import com.haoys.user.model.FlowPlanFormInfo;
import com.haoys.user.model.FlowPlanFormInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FlowPlanFormInfoMapper {
    long countByExample(FlowPlanFormInfoExample example);

    int deleteByExample(FlowPlanFormInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(FlowPlanFormInfo record);

    int insertSelective(FlowPlanFormInfo record);

    List<FlowPlanFormInfo> selectByExample(FlowPlanFormInfoExample example);

    FlowPlanFormInfo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") FlowPlanFormInfo record, @Param("example") FlowPlanFormInfoExample example);

    int updateByExample(@Param("record") FlowPlanFormInfo record, @Param("example") FlowPlanFormInfoExample example);

    int updateByPrimaryKeySelective(FlowPlanFormInfo record);

    int updateByPrimaryKey(FlowPlanFormInfo record);

    FlowPlanFormInfo getFlowPlanFormBaseInfoByPlanIdAndFormId(String projectId, String planId, String formId);

    /**
     * 根据项目id和方案id查询表单列表
     *
     * @param projectId
     * @param planId
     * @param visitId
     * @param formId
     * @return
     */
    List<FlowPlanFormVo> getFormConfigListByPlanIdAndVisitId(@Param("projectId") String projectId, @Param("planId") String planId, @Param("visitId")  String visitId, @Param("formId") String formId);

    /**
     * 查询表单发布列表
     * @param projectId
     * @param formId
     * @return
     */
    List<FlowPlanFormInfo> getFormConfigListByProjectIdAndFormId(@Param("projectId") String projectId, @Param("formId") String formId);
    
    FlowPlanFormInfo getFormConfigListByProjectIdAndPlanIdAndFormId(@Param("projectId") String projectId, @Param("planId") String planId, @Param("formId") String formId);
}
