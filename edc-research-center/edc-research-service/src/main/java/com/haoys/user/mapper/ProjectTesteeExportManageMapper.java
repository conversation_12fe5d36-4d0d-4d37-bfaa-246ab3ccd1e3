package com.haoys.user.mapper;

import com.haoys.user.domain.param.testee.ProjectTesteeExportSelectParam;
import com.haoys.user.model.ProjectTesteeExportManage;
import com.haoys.user.model.ProjectTesteeExportManageExample;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface ProjectTesteeExportManageMapper {
    long countByExample(ProjectTesteeExportManageExample example);

    int deleteByExample(ProjectTesteeExportManageExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(ProjectTesteeExportManage record);

    int insertSelective(ProjectTesteeExportManage record);

    List<ProjectTesteeExportManage> selectByExample(ProjectTesteeExportManageExample example);

    ProjectTesteeExportManage selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") ProjectTesteeExportManage record, @Param("example") ProjectTesteeExportManageExample example);

    int updateByExample(@Param("record") ProjectTesteeExportManage record, @Param("example") ProjectTesteeExportManageExample example);

    int updateByPrimaryKeySelective(ProjectTesteeExportManage record);

    int updateByPrimaryKey(ProjectTesteeExportManage record);

    /**
     * 获取参与者导出数据
     * @param selectParam 导出参数
     * @return 导出列表
     */
    List<ProjectTesteeExportManage> selectList(ProjectTesteeExportSelectParam selectParam);

    /**
     * 获取当前用户下载的文件
     * @param list
     * @return
     */
    List<ProjectTesteeExportManage> getDownList(List<Object> list);

}
