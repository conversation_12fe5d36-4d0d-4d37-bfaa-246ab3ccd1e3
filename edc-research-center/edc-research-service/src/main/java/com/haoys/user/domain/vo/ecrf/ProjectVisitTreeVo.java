package com.haoys.user.domain.vo.ecrf;

import com.haoys.user.model.ProjectFormAudit;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ProjectVisitTreeVo {

    @ApiModelProperty(value = "访视id")
    private String visitId;
    @ApiModelProperty(value = "访视名称")
    private String visitName;
    @ApiModelProperty(value = "类型 1-表单项 2-访视时间")
    private String resourceType;
    
    @ApiModelProperty(value = "访视完成状态 1-未录入 2-录入中 3-已完成")
    private String complateVisitStatus = "1";
    private String complateVisitPercent;
    @ApiModelProperty(value = "访视提交状态 1-未提交 2-已提交")
    private String submitVisitStatus = "1";
    @ApiModelProperty(value = "访视审核状态 1-待审核 2-审核通过")
    private String auditVisitStatus = "1";
    @ApiModelProperty(value = "访视质疑状态")
    private String changleVisitStatus = "";
    @ApiModelProperty(value = "表单详情发起质疑按钮")
    private String challengeButtonStatus = "";
    @ApiModelProperty(value = "表单集合")
    private List<FormTreeVo> childrenNodes = new ArrayList<>();

    @Data
    public static class FormTreeVo{
        
        @ApiModelProperty(value = "扩展表单序号")
        private Integer xh;
        @ApiModelProperty(value = "表单id")
        private String formId;
        @ApiModelProperty(value = "流程表单主记录id")
        private String formSetId;
        @ApiModelProperty(value = "不良时间等扩展表单id")
        private String formExpandId;
        @ApiModelProperty(value = "表单名称")
        private String formName;
        @ApiModelProperty(value = "表单项配置")
        private String formConfig;
        @ApiModelProperty(value = "表单code")
        private String formCode;
        @ApiModelProperty(value = "pc端查看权限")
        private Boolean pcRoAuth = true;
        @ApiModelProperty(value = "pc端编辑权限")
        private Boolean pcRwAuth = false;
        @ApiModelProperty(value = "移动端查看权限")
        private Boolean moRoAuth = false;
        @ApiModelProperty(value = "移动端编辑权限")
        private Boolean moRwAuth = false;
        @ApiModelProperty(value = "是否不良事件和合并用药表单")
        private Boolean formSetExpand = false;
        @ApiModelProperty(value = "表单数据暂存和提交状态 0-提交状态 -1为暂存状态")
        private String status = "";
        @ApiModelProperty(value = "表单完成状态 1-未录入 2-录入中 3-已完成")
        private String complateStatus = "1";
        @ApiModelProperty(value = "表单项质疑标识")
        private String formChallengeStatus;
        @ApiModelProperty(value = "表单项是否发起质疑标识")
        private String formChallengeButtonStatus;
        @ApiModelProperty(value = "表单审核")
        private ProjectFormAudit projectFormAudit;
        @ApiModelProperty(value = "修改时间")
        private String updateTime;
    }
}
