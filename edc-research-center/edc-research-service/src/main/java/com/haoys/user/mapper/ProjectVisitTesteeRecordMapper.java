package com.haoys.user.mapper;

import com.haoys.user.model.ProjectVisitTesteeRecord;
import com.haoys.user.model.ProjectVisitTesteeRecordExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProjectVisitTesteeRecordMapper {
    long countByExample(ProjectVisitTesteeRecordExample example);

    int deleteByExample(ProjectVisitTesteeRecordExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectVisitTesteeRecord record);

    int insertSelective(ProjectVisitTesteeRecord record);

    List<ProjectVisitTesteeRecord> selectByExample(ProjectVisitTesteeRecordExample example);

    ProjectVisitTesteeRecord selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectVisitTesteeRecord record, @Param("example") ProjectVisitTesteeRecordExample example);

    int updateByExample(@Param("record") ProjectVisitTesteeRecord record, @Param("example") ProjectVisitTesteeRecordExample example);

    int updateByPrimaryKeySelective(ProjectVisitTesteeRecord record);

    int updateByPrimaryKey(ProjectVisitTesteeRecord record);

    List<ProjectVisitTesteeRecord> getProjectVisitFollowRealTimeList(@Param("projectId")String projectId, @Param("orgIds")String orgIds);

    List<ProjectVisitTesteeRecord> getProjectPlannedVisitList(@Param("projectId")String projectId, @Param("orgIds")String orgIds);

    List<ProjectVisitTesteeRecord> getProjectOverdueVisitFollowRealTimeNullCount(@Param("projectId")String projectId, @Param("orgIds")String orgIds, @Param("nextFollowRealNullValue") String nextFollowRealNullValue);

    List<ProjectVisitTesteeRecord> getProjectOverdueVisitFollowRealTimeNotNullCount(@Param("projectId")String projectId, @Param("orgIds")String orgIds, @Param("nextFollowRealNotNullValue") String nextFollowRealNotNullValue);
}
