package com.haoys.user.mapper;

import com.haoys.user.common.core.mapper.BaseMapper;
import com.haoys.user.domain.entity.ProjectRoleMenuQuery;
import com.haoys.user.model.*;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 项目角色和菜单数据层
 */
@Mapper
public interface ProjectRoleMenuMapper extends BaseMapper<ProjectRoleMenuQuery> {

    /**
     * 批量新增项目角色菜单信息
     *  @param projectId
     * @param roleId
     * @param menuIds
     * @param tenantId
     * @param platformId
     */
    void batchSaveProjectRoleMenuList(String projectId, String roleId, List<String> menuIds, String tenantId, String platformId);

    /**
     * 通过项目角色ID删除项目角色和菜单关联
     * @param roleId 角色ID
     * @return 结果
     */
    int deleteProjectRoleMenuByRoleId(Long roleId);

    /**
     * 根据角色id获取项目菜单id集合
     * @param projectRoleId 角色id
     * @return
     */
    List<String> selectProjectMenuIdsByProjectRoleId(String projectRoleId);

    int insert(ProjectRoleMenu record);

    int deleteByExample(ProjectRoleMenuExample example);

    List<ProjectRoleMenu> selectByExample(ProjectRoleMenuExample example);

    List<String> selectByRoleId(Long roleId);


}
