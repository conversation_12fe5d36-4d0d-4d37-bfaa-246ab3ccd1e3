package com.haoys.user.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.crf.TemplateGroupLableParam;
import com.haoys.user.domain.template.ProjectVisitSearchTreeVo;
import com.haoys.user.domain.vo.ecrf.ProjectFormVariableTreeVo;
import com.haoys.user.domain.vo.ecrf.ProjectTemplateFormVariableTreeVo;
import com.haoys.user.domain.vo.ecrf.ProjectVisitTreeVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormConfigVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDetailVo;
import com.haoys.user.domain.vo.ecrf.TemplateGroupLableVo;
import com.haoys.user.domain.vo.ecrf.TemplateTableVo;
import com.haoys.user.domain.vo.flow.FlowPlanFormVo;
import com.haoys.user.domain.vo.participant.ProjectTesteeVisitPercentVo;
import com.haoys.user.domain.vo.project.ProjectVisitVo;
import com.haoys.user.domain.vo.testee.TesteeChallengeVo;
import com.haoys.user.enums.FormVariableComplateStatus;
import com.haoys.user.mapper.TemplateGroupLableMapper;
import com.haoys.user.model.Dictionary;
import com.haoys.user.model.FlowFormSetExpand;
import com.haoys.user.model.FlowPlan;
import com.haoys.user.model.ProjectDictionary;
import com.haoys.user.model.ProjectFormAudit;
import com.haoys.user.model.ProjectTesteeProcess;
import com.haoys.user.model.ProjectVisitConfig;
import com.haoys.user.model.TemplateFormConfig;
import com.haoys.user.model.TemplateGroupLable;
import com.haoys.user.model.TemplateGroupLableExample;
import com.haoys.user.service.FlowFormSetService;
import com.haoys.user.service.FlowPlanFormService;
import com.haoys.user.service.FlowPlanService;
import com.haoys.user.service.ProjectDictionaryService;
import com.haoys.user.service.ProjectFormAuditService;
import com.haoys.user.service.ProjectTesteeInfoService;
import com.haoys.user.service.ProjectVisitConfigService;
import com.haoys.user.service.TemplateConfigService;
import com.haoys.user.service.TemplateGroupLableService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class TemplateGroupLableServiceImpl implements TemplateGroupLableService {

    private final TemplateGroupLableMapper templateGroupLableMapper;
    private final TemplateConfigService templateConfigService;
    private final ProjectDictionaryService projectDictionaryService;
    private final ProjectVisitConfigService projectVisitConfigService;
    private final ProjectTesteeInfoService projectTesteeInfoService;
    private final FlowPlanFormService flowPlanFormService;
    private final FlowPlanService flowPlanService;
    private final FlowFormSetService formSetService;
    private final ProjectFormAuditService projectFormAuditService;

    @Override
    public List<TemplateGroupLableVo> getTemplateGroupLableList(String projectId, String lableId, String visitId, String testeeId) {
        List<TemplateGroupLableVo> dataList = new ArrayList<>();
        TemplateGroupLableExample example = new TemplateGroupLableExample();
        TemplateGroupLableExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(lableId)) {
            criteria.andIdEqualTo(Long.parseLong(lableId));
        }
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        if (StringUtils.isNotBlank(visitId)) {
            criteria.andVisitIdEqualTo(Long.parseLong(visitId));
        }
        criteria.andParentIdEqualTo(0l);
        example.setOrderByClause("sort asc");
        List<TemplateGroupLable> templateGroupLableList = templateGroupLableMapper.selectByExample(example);
        for (TemplateGroupLable templateGroupLable : templateGroupLableList) {
            TemplateGroupLableVo templateGroupLableVo = new TemplateGroupLableVo();
            BeanUtils.copyProperties(templateGroupLable, templateGroupLableVo);
            if (BusinessConfig.PROJECT_LABEL_VISIT_TYPE.equals(templateGroupLable.getResourceType())) {
                templateGroupLableVo.setShowVisitTimeLink(true);
                templateGroupLableVo.setVisitId(Long.parseLong(visitId));
                templateGroupLableVo.setVisitName(BusinessConfig.PROJECT_LABEL_VISIT_NAME);
                ///ProjectVisitVo visitConfig = projectVisitConfigService.getProjectVisitBaseConfig(visitId);
            }
            example.clear();
            TemplateGroupLableExample.Criteria criteria_query = example.createCriteria();
            criteria_query.andParentIdEqualTo(templateGroupLable.getId());
            long count = templateGroupLableMapper.countByExample(example);
            if (count > 0) {
                templateGroupLableVo.setLeafNode(false);
            }
            if (StringUtils.isNotBlank(visitId)) {
                List<TemplateGroupLableVo.TemplateFormVo> childrenNodeList = new ArrayList<>();
                List<TemplateFormConfigVo> templateFormConfigVoList = templateConfigService.getTemplateFormConfigListByPlanId("", projectId, "", null, null, null, false);
                for (TemplateFormConfigVo templateFormConfigVo : templateFormConfigVoList) {
                    TemplateGroupLableVo.TemplateFormVo childrenNode = new TemplateGroupLableVo.TemplateFormVo();
                    childrenNode.setFormId(templateFormConfigVo.getFormId());
                    childrenNode.setFormName(templateFormConfigVo.getFormName());
                    childrenNode.setFormConfig(templateFormConfigVo.getFormConfig());
                    //查询表单项质疑标识
                    if (StringUtils.isNotBlank(testeeId)) {
                        TesteeChallengeVo projectFormChallenge = projectTesteeInfoService.getProjectFormChallengeStatus(projectId, visitId, templateFormConfigVo.getFormId(), testeeId);
                        childrenNode.setFormChallengeStatus(projectFormChallenge.getChallengeStatus());
                        childrenNode.setFormChallengeButtonStatus(projectFormChallenge.getChallengeButtonStatus());
                    }
                    if (templateFormConfigVo.getGroupName().equals(templateGroupLable.getId().toString())) {
                        childrenNodeList.add(childrenNode);
                    }
                }
                templateGroupLableVo.setChildrenNodes(childrenNodeList);
            }
            dataList.add(templateGroupLableVo);
        }
        return dataList;
    }


    /**
     * 新版访视列表展示 结构：访视-表单-变量
     */
    @Override
    public List<ProjectVisitTreeVo> getProjectFrontVisitTreeList(String projectId, String projectOrgId, String planId, String visitId, String formId, String testeeId, String hiddenReadForm, String complateStatus, String enableVisitPercent) {
        List<ProjectVisitTreeVo> dataList = new ArrayList<>();
        if (StringUtils.isEmpty(planId)) {
            FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectId);
            if (flowPlanInfo == null) {
                return null;
            }
            planId = flowPlanInfo.getId().toString();
        }
        List<ProjectVisitConfig> projectVisitConfigList = projectVisitConfigService.getProjectVisitListByPlanId(projectId, planId, visitId);
        for (ProjectVisitConfig projectVisitConfig : projectVisitConfigList) {
            ProjectVisitTreeVo projectVisitTreeVo = new ProjectVisitTreeVo();
            projectVisitTreeVo.setVisitId(projectVisitConfig.getId().toString());
            projectVisitTreeVo.setVisitName(projectVisitConfig.getVisitName());
            projectVisitTreeVo.setResourceType(projectVisitConfig.getVisitType());
            //计算访视质疑
            TesteeChallengeVo projectVisitChallenge = projectTesteeInfoService.getProjectVisitChallengeStatus(projectId, projectVisitConfig.getId().toString(), testeeId);
            projectVisitTreeVo.setChangleVisitStatus(projectVisitChallenge.getChallengeStatus());
            projectVisitTreeVo.setChallengeButtonStatus(projectVisitChallenge.getChallengeButtonStatus());
            //访视进度状态
            ProjectTesteeVisitPercentVo projectTesteeVisitPercent = projectTesteeInfoService.getProjectVisitTesteeComplateStatus(projectId, projectOrgId, planId, projectVisitConfig.getId().toString(), testeeId);
            projectVisitTreeVo.setComplateVisitStatus(projectTesteeVisitPercent.getComplateVisitStatus());
            projectVisitTreeVo.setComplateVisitPercent(projectTesteeVisitPercent.getComplateVisitPercent());
            // 访视提交状态
            List<String> visitStatusList = new ArrayList<>();
            // 访视审核状态
            List<String> visitAduitList = new ArrayList<>();
            List<ProjectVisitTreeVo.FormTreeVo> formConfigList = new ArrayList<>();
            List<FlowPlanFormVo> templateFormConfigVoList = flowPlanFormService.getFormConfigListByPlanIdAndVisitId(projectId, planId, projectVisitConfig.getId().toString(), formId);
            for (FlowPlanFormVo flowPlanFormVo : templateFormConfigVoList) {
                if(!flowPlanFormVo.getMoRoAuth()){
                    if("1".equals(hiddenReadForm)){continue;}
                }
                ProjectVisitTreeVo.FormTreeVo templateFormTreeVo = new ProjectVisitTreeVo.FormTreeVo();
                templateFormTreeVo.setFormId(flowPlanFormVo.getFormId());
                templateFormTreeVo.setFormName(flowPlanFormVo.getFormName());
                templateFormTreeVo.setFormCode(flowPlanFormVo.getFormCode());
                templateFormTreeVo.setPcRoAuth(flowPlanFormVo.getPcRoAuth());
                templateFormTreeVo.setPcRwAuth(flowPlanFormVo.getPcRwAuth());
                templateFormTreeVo.setMoRoAuth(flowPlanFormVo.getMoRoAuth());
                templateFormTreeVo.setMoRwAuth(flowPlanFormVo.getMoRwAuth());
                ProjectFormAudit projectFormAuditInfo = projectFormAuditService.getProjectFormAuditInfo(NumberUtil.parseLong(projectId), NumberUtil.parseLong(projectVisitConfig.getId().toString()), NumberUtil.parseLong(flowPlanFormVo.getFormId()), NumberUtil.parseLong(testeeId));
                if(projectFormAuditInfo != null){
                    templateFormTreeVo.setProjectFormAudit(projectFormAuditInfo);
                    if(projectFormAuditInfo.getAuditStatus().equals("1")){
                        visitAduitList.add(projectFormAuditInfo.getId().toString());
                    }
                }
                
                if(BusinessConfig.TEMPLATE_CONFIG_INPUT_TYPE_1.equals(flowPlanFormVo.getFormType())){
                    // 访视和质疑状态
                    TesteeChallengeVo projectFormChallenge = projectTesteeInfoService.getProjectFormChallengeStatus(projectId, projectVisitConfig.getId().toString(), flowPlanFormVo.getFormId(), testeeId);
                    templateFormTreeVo.setFormChallengeStatus(projectFormChallenge.getChallengeStatus());
                    templateFormTreeVo.setFormChallengeButtonStatus(projectFormChallenge.getChallengeButtonStatus());
                    // 查询表单录入进度和提交暂存状态
                    ProjectTesteeProcess projectTesteeProcess = projectTesteeInfoService.getProjectTesteeFormProcess(projectId, projectOrgId, planId, projectVisitConfig.getId().toString(), flowPlanFormVo.getFormId(), "", testeeId);
                    if (projectTesteeProcess != null) {
                        templateFormTreeVo.setStatus(projectTesteeProcess.getStatus());
                        templateFormTreeVo.setComplateStatus(StringUtils.isEmpty(projectTesteeProcess.getComplateStatus()) ? FormVariableComplateStatus.FORM_VAR_NO_FILL.getCode() : projectTesteeProcess.getComplateStatus());
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        String str = sdf.format(projectTesteeProcess.getUpdateTime() == null ? projectTesteeProcess.getCreateTime() : projectTesteeProcess.getUpdateTime());
                        templateFormTreeVo.setUpdateTime(str);
                        visitStatusList.add(projectTesteeProcess.getId().toString());
                    }
                    if (StringUtils.isNotEmpty(complateStatus)) {
                        String[] complateStatusArray = complateStatus.split(",");
                        for (String complateStatusStr : complateStatusArray) {
                            if (complateStatusStr.equals(templateFormTreeVo.getComplateStatus())) {
                                formConfigList.add(templateFormTreeVo);
                            }
                        }
                    } else {
                        formConfigList.add(templateFormTreeVo);
                    }
                }
                
                // 不良事件和合并用药查询扩展表单
                if(BusinessConfig.TEMPLATE_CONFIG_INPUT_TYPE_2.equals(flowPlanFormVo.getFormType())){
                    String systemTenantId = SecurityUtils.getSystemTenantId();
                    String systemPlatformId = SecurityUtils.getSystemPlatformId();
                    // 异步补偿formSerExpand
                    //AsyncTaskManager.ownerTask().execute(AsyncTaskFactory.insertflowFormSetExpandForTemplate(projectId, planId, projectVisitConfig.getId().toString(), flowPlanFormVo.getFormId(), testeeId, systemTenantId, systemPlatformId),500);
                    List<FlowFormSetExpand> flowFormSetExpandList = formSetService.getFormSetExpandList(projectId, planId, projectVisitConfig.getId().toString(), flowPlanFormVo.getFormId(), testeeId);
                    for (FlowFormSetExpand flowFormSetExpand : flowFormSetExpandList) {
                        // 查询表单录入进度和提交暂存状态
                        String formExpandId = flowFormSetExpand.getId().toString();
                        ProjectTesteeProcess projectTesteeProcess = projectTesteeInfoService.getProjectTesteeFormProcess(projectId, projectOrgId, planId, projectVisitConfig.getId().toString(), flowPlanFormVo.getFormId(), formExpandId, testeeId);
                        if (projectTesteeProcess != null) {
                            templateFormTreeVo.setStatus(projectTesteeProcess.getStatus());
                            templateFormTreeVo.setComplateStatus(StringUtils.isEmpty(projectTesteeProcess.getComplateStatus()) ? FormVariableComplateStatus.FORM_VAR_NO_FILL.getCode() : projectTesteeProcess.getComplateStatus());
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String str = sdf.format(projectTesteeProcess.getUpdateTime() == null ? projectTesteeProcess.getCreateTime() : projectTesteeProcess.getUpdateTime());
                            templateFormTreeVo.setUpdateTime(str);
                            visitStatusList.add(projectTesteeProcess.getId().toString());
                        }
                        if(flowFormSetExpand.getTemplateCopy()){
                            templateFormTreeVo.setXh(flowFormSetExpand.getXh());
                            templateFormTreeVo.setFormName("(" + flowFormSetExpand.getXh() + ") " + flowPlanFormVo.getFormName());
                            templateFormTreeVo.setFormSetId(flowPlanFormVo.getFormSetId());
                            templateFormTreeVo.setFormSetExpand(true);
                            templateFormTreeVo.setFormExpandId(flowFormSetExpand.getId().toString());
                            formConfigList.add(templateFormTreeVo);
                        }else{
                            ProjectVisitTreeVo.FormTreeVo templateFormExpandTreeVo = new ProjectVisitTreeVo.FormTreeVo();
                            templateFormExpandTreeVo.setXh(flowFormSetExpand.getXh());
                            templateFormExpandTreeVo.setFormId(flowPlanFormVo.getFormId());
                            templateFormExpandTreeVo.setFormSetId(flowPlanFormVo.getFormSetId());
                            templateFormExpandTreeVo.setFormExpandId(flowFormSetExpand.getId().toString());
                            templateFormExpandTreeVo.setFormName("(" + flowFormSetExpand.getXh() + ") " + flowPlanFormVo.getFormName());
                            templateFormExpandTreeVo.setFormCode(flowPlanFormVo.getFormCode());
                            templateFormExpandTreeVo.setPcRoAuth(flowPlanFormVo.getPcRoAuth());
                            templateFormExpandTreeVo.setPcRwAuth(flowPlanFormVo.getPcRwAuth());
                            templateFormExpandTreeVo.setMoRoAuth(flowPlanFormVo.getMoRoAuth());
                            templateFormExpandTreeVo.setMoRwAuth(flowPlanFormVo.getMoRwAuth());
                            if (projectTesteeProcess != null) {
                                templateFormExpandTreeVo.setStatus(projectTesteeProcess.getStatus());
                            }
                            formConfigList.add(templateFormExpandTreeVo);
                        }
                    }
                }
            }
            projectVisitTreeVo.setChildrenNodes(formConfigList);
            dataList.add(projectVisitTreeVo);
            if(formConfigList.size() == visitStatusList.size()){
                projectVisitTreeVo.setSubmitVisitStatus("2");
            }
            if(formConfigList.size() == visitAduitList.size()){
                projectVisitTreeVo.setAuditVisitStatus("2");
            }
        }
        return dataList;
    }

    @Override
    public List<ProjectVisitTreeVo> getTemplateVisitConfigList(String templateId) {
        List<ProjectVisitTreeVo> dataList = new ArrayList<>();
        List<ProjectVisitConfig> projectVisitConfigList = projectVisitConfigService.getProjectVisitListByTemplateId(templateId);
        for (ProjectVisitConfig projectVisitConfig : projectVisitConfigList) {
            ProjectVisitTreeVo projectVisitTreeVo = new ProjectVisitTreeVo();
            projectVisitTreeVo.setVisitId(projectVisitConfig.getId().toString());
            projectVisitTreeVo.setVisitName(projectVisitConfig.getVisitName());
            projectVisitTreeVo.setResourceType(projectVisitConfig.getVisitType());
            List<ProjectVisitTreeVo.FormTreeVo> formConfigList = new ArrayList<>();
            List<TemplateFormConfigVo> templateFormConfigVoList = templateConfigService.getTemplateFormConfigListByPlanId(templateId, "", "", "", "", null, false);
            for (TemplateFormConfigVo templateFormConfigVo : templateFormConfigVoList) {
                ProjectVisitTreeVo.FormTreeVo formTreeVo = new ProjectVisitTreeVo.FormTreeVo();
                formTreeVo.setFormId(templateFormConfigVo.getFormId());
                formTreeVo.setFormName(templateFormConfigVo.getFormName());
                formTreeVo.setFormConfig(templateFormConfigVo.getFormConfig());
                formConfigList.add(formTreeVo);
            }
            projectVisitTreeVo.setChildrenNodes(formConfigList);
            dataList.add(projectVisitTreeVo);
        }
        return dataList;
    }

    @Override
    public List<ProjectVisitTreeVo> getProjectBackVisitTreeList(String projectId) {
        List<ProjectVisitTreeVo> dataList = new ArrayList<>();
        FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectId);
        if (flowPlanInfo == null) {
            return null;
        }
        List<ProjectVisitConfig> projectVisitConfigList = projectVisitConfigService.getProjectVisitListByPlanId(projectId, flowPlanInfo.getId().toString(), "");
        for (ProjectVisitConfig projectVisitConfig : projectVisitConfigList) {
            ProjectVisitTreeVo projectVisitTreeVo = new ProjectVisitTreeVo();
            projectVisitTreeVo.setVisitId(projectVisitConfig.getId().toString());
            projectVisitTreeVo.setVisitName(projectVisitConfig.getVisitName());
            projectVisitTreeVo.setResourceType(projectVisitConfig.getVisitType());
            List<ProjectVisitTreeVo.FormTreeVo> formConfigList = new ArrayList<>();
            List<TemplateFormConfigVo> templateFormConfigList = templateConfigService.getFormConfigListByVisitId(projectId, flowPlanInfo.getId().toString(), projectVisitConfig.getId().toString(), "");
            for (TemplateFormConfigVo templateFormConfig : templateFormConfigList) {
                ProjectVisitTreeVo.FormTreeVo formTreeVo = new ProjectVisitTreeVo.FormTreeVo();
                formTreeVo.setFormId(templateFormConfig.getFormId());
                formTreeVo.setFormName(templateFormConfig.getFormName());
                formTreeVo.setFormConfig(templateFormConfig.getFormConfig());
                formConfigList.add(formTreeVo);
            }
            projectVisitTreeVo.setChildrenNodes(formConfigList);
            dataList.add(projectVisitTreeVo);
        }
        return dataList;
    }

    @Override
    public List<ProjectVisitSearchTreeVo> getProjectVisitTreeData(String projectId) {
        List<ProjectVisitSearchTreeVo> dataList = new ArrayList<>();
        List<ProjectVisitConfig> projectVisitConfigList = projectVisitConfigService.getProjectVisitListByPlanId(projectId, "", "");
        for (ProjectVisitConfig projectVisitConfig : projectVisitConfigList) {
            ProjectVisitSearchTreeVo projectVisitTreeVo = new ProjectVisitSearchTreeVo();
            projectVisitTreeVo.setValue(projectVisitConfig.getId().toString());
            projectVisitTreeVo.setLabel(projectVisitConfig.getVisitName());
            List<ProjectVisitSearchTreeVo> children = new ArrayList<>();
            List<TemplateFormConfigVo> templateFormConfigList = templateConfigService.getFormConfigListByVisitId(projectId, "", projectVisitConfig.getId().toString(), "");
            for (TemplateFormConfigVo templateFormConfig : templateFormConfigList) {
                ProjectVisitSearchTreeVo treeVo = new ProjectVisitSearchTreeVo();
                treeVo.setValue(templateFormConfig.getFormId());
                treeVo.setLabel(templateFormConfig.getFormName());
                children.add(treeVo);
            }
            projectVisitTreeVo.setChildren(children);
            dataList.add(projectVisitTreeVo);
        }
        return dataList;
    }

    @Override
    public List<ProjectFormVariableTreeVo> getProjectFormVariableTreeVo(String projectId, String planId) {
        List<ProjectFormVariableTreeVo> dataList = new ArrayList<>();
        List<ProjectVisitConfig> projectVisitConfigList = projectVisitConfigService.getProjectVisitListByPlanId(projectId, planId, "");
        for (ProjectVisitConfig projectVisitConfig : projectVisitConfigList) {
            ProjectFormVariableTreeVo projectVisitTreeVo = new ProjectFormVariableTreeVo();
            projectVisitTreeVo.setVarKey(projectVisitConfig.getId().toString());
            projectVisitTreeVo.setVisitId(projectVisitConfig.getId().toString());
            projectVisitTreeVo.setVariableId(projectVisitConfig.getId().toString());
            projectVisitTreeVo.setName(projectVisitConfig.getVisitName());
            List<ProjectFormVariableTreeVo.FormTreeVo> projectFormList = new ArrayList<>();
            List<FlowPlanFormVo> templateFormConfigList = flowPlanFormService.getFormConfigListByPlanIdAndVisitId(projectId, planId, projectVisitConfig.getId().toString(), "");
            for (FlowPlanFormVo templateFormConfig : templateFormConfigList) {
                ProjectFormVariableTreeVo.FormTreeVo formTreeVo = new ProjectFormVariableTreeVo.FormTreeVo();
                formTreeVo.setVarKey(templateFormConfig.getFormId());
                formTreeVo.setFormId(templateFormConfig.getFormId());
                formTreeVo.setVariableId(templateFormConfig.getFormId());
                formTreeVo.setName(templateFormConfig.getFormName());
                //查询表格变量
                List<TemplateFormDetailVo> templateFormDetailList = templateConfigService.getTemplateFormDetailConfigListByFormId("", templateFormConfig.getFormId(), "", "1", "0", "");
                List<ProjectFormVariableTreeVo.FormVariableVo> formVariableList = new ArrayList<>();
                for (TemplateFormDetailVo templateFormDetailVo : templateFormDetailList) {
                    if (BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(templateFormDetailVo.getType())) {
                        List<TemplateTableVo> templateFormTableList = templateConfigService.getTemplateFormTableByFormDetailId(templateFormDetailVo.getId().toString());
                        for (TemplateTableVo templateTableVo : templateFormTableList) {
                            ProjectFormVariableTreeVo.FormVariableVo formVariableVo = new ProjectFormVariableTreeVo.FormVariableVo();
                            formVariableVo.setVarKey(templateTableVo.getId().toString());
                            formVariableVo.setVisitId(projectVisitTreeVo.getVisitId());
                            formVariableVo.setFormId(formTreeVo.getFormId());
                            formVariableVo.setVariableId(templateTableVo.getId().toString());
                            formVariableVo.setTableId(templateFormDetailVo.getId().toString());
                            formVariableVo.setName(templateTableVo.getLabel());
                            formVariableVo.setFieldName(templateTableVo.getFieldName());
                            formVariableVo.setExpand(templateTableVo.getExtData2() + "");
                            formVariableVo.setPlaceholder(templateTableVo.getPlaceholder());
                            formVariableVo.setType(templateFormDetailVo.getType());
                            formVariableVo.setVarType(templateTableVo.getType());
                            formVariableList.add(formVariableVo);
                        }
                    } else if (BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP.equals(templateFormDetailVo.getType())) {
                        List<TemplateFormDetailVo> templateGroupFormDetailList = templateConfigService.getTemplateFormDetailByGroupId(templateFormDetailVo.getFormId().toString(), templateFormDetailVo.getId().toString(), "", false);
                        log.info("templateGroupFormDetailList:{}", JSON.toJSONString(templateGroupFormDetailList));
                        for (TemplateFormDetailVo templateFormDetail : templateGroupFormDetailList) {
                            if (BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(templateFormDetail.getType())) {
                                List<TemplateTableVo> templateFormTableList = templateConfigService.getTemplateFormTableByFormDetailId(templateFormDetail.getId().toString());
                                for (TemplateTableVo templateTableVo : templateFormTableList) {
                                    ProjectFormVariableTreeVo.FormVariableVo formVariableVo = new ProjectFormVariableTreeVo.FormVariableVo();
                                    formVariableVo.setVarKey(templateTableVo.getId().toString());
                                    formVariableVo.setVisitId(projectVisitTreeVo.getVisitId());
                                    formVariableVo.setFormId(formTreeVo.getFormId());
                                    formVariableVo.setVariableId(templateTableVo.getId().toString());
                                    formVariableVo.setTableId(templateFormDetail.getId().toString());
                                    formVariableVo.setGroupId(templateFormDetail.getGroupId() + "");
                                    formVariableVo.setName(templateTableVo.getLabel());
                                    formVariableVo.setFieldName(formVariableVo.getGroupId() + "_" + templateTableVo.getFieldName());
                                    formVariableVo.setExpand(templateTableVo.getExtData2());
                                    formVariableVo.setPlaceholder(templateTableVo.getPlaceholder());
                                    formVariableVo.setType(templateFormDetail.getType());
                                    formVariableVo.setVarType(templateTableVo.getType());
                                    formVariableVo.setFieldGroup(true);
                                    formVariableList.add(formVariableVo);
                                }
                            } else {
                                ProjectFormVariableTreeVo.FormVariableVo formVariableVo = new ProjectFormVariableTreeVo.FormVariableVo();
                                formVariableVo.setVarKey(templateFormDetail.getId().toString());
                                formVariableVo.setVisitId(projectVisitTreeVo.getVisitId());
                                formVariableVo.setFormId(formTreeVo.getFormId());
                                formVariableVo.setVariableId(templateFormDetail.getId().toString());
                                formVariableVo.setGroupId(templateFormDetail.getGroupId() + "");
                                formVariableVo.setName(templateFormDetail.getLabel());
                                formVariableVo.setFieldName(formVariableVo.getGroupId() + "_" + templateFormDetail.getFieldName());
                                formVariableVo.setExpand(templateFormDetail.getExpand());
                                formVariableVo.setPlaceholder(templateFormDetail.getPlaceholder());
                                formVariableVo.setType(templateFormDetail.getType());
                                formVariableVo.setVarType(templateFormDetail.getType());
                                formVariableVo.setFieldGroup(true);
                                formVariableList.add(formVariableVo);
                            }
                        }
                    } else {
                        ProjectFormVariableTreeVo.FormVariableVo formVariableVo = new ProjectFormVariableTreeVo.FormVariableVo();
                        formVariableVo.setVarKey(templateFormDetailVo.getId().toString());
                        formVariableVo.setVisitId(projectVisitTreeVo.getVisitId());
                        formVariableVo.setFormId(formTreeVo.getFormId());
                        formVariableVo.setVariableId(templateFormDetailVo.getId().toString());
                        formVariableVo.setName(templateFormDetailVo.getLabel());
                        formVariableVo.setFieldName(templateFormDetailVo.getFieldName());
                        formVariableVo.setExpand(templateFormDetailVo.getExpand());
                        formVariableVo.setPlaceholder(templateFormDetailVo.getPlaceholder());
                        formVariableVo.setType(templateFormDetailVo.getType());
                        formVariableVo.setVarType(templateFormDetailVo.getType());
                        formVariableList.add(formVariableVo);
                    }
                }
                formTreeVo.setFormVariableList(formVariableList);
                projectFormList.add(formTreeVo);
            }
            projectVisitTreeVo.setProjectFormList(projectFormList);
            dataList.add(projectVisitTreeVo);
        }
        return dataList;
    }


    public List<ProjectTemplateFormVariableTreeVo> getTemplateFormVariableTreeVo(String projectId, String labConfigType) {
        List<ProjectTemplateFormVariableTreeVo> projectTemplateFormVariableTreeVo = new ArrayList<>();
        List<TemplateFormConfig> templateFormConfigList = templateConfigService.getProjectTemplateFormListByProjectId(projectId, null);
        for (TemplateFormConfig templateFormConfig : templateFormConfigList) {
            ProjectTemplateFormVariableTreeVo formVo = new ProjectTemplateFormVariableTreeVo();
            formVo.setVariableId(templateFormConfig.getId().toString());
            formVo.setName(templateFormConfig.getFormName());
            List<ProjectTemplateFormVariableTreeVo> variableList = new ArrayList<>();
            List<TemplateFormDetailVo> templateFormDetailList = templateConfigService.getTemplateFormDetailConfigListByFormId("", templateFormConfig.getId().toString(), "", "1", "1", "0");
            for (TemplateFormDetailVo templateFormDetailVo : templateFormDetailList) {
                ProjectTemplateFormVariableTreeVo formVariableVo = new ProjectTemplateFormVariableTreeVo();
                List<String> templateVariableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_TABLE, BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP);
                if(!templateVariableTypeList.contains(templateFormDetailVo.getType())){
                    formVariableVo.setFormId(templateFormConfig.getId().toString());
                    formVariableVo.setFormDetaiId(templateFormDetailVo.getId().toString());
                    formVariableVo.setVariableId(templateFormDetailVo.getId().toString());
                    formVariableVo.setGroupId(templateFormDetailVo.getGroupId() == null ? "" : templateFormDetailVo.getGroupId().toString());
                    formVariableVo.setName(templateFormDetailVo.getLabel());
                    formVariableVo.setType(templateFormDetailVo.getType());
                    if(BusinessConfig.LAB_CONFIG_TYPE_01.equals(labConfigType)){
                        List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_INPUT, BusinessConfig.PROJECT_VISIT_CRF_FORM_NUMBER);
                        if(!variableTypeList.contains(templateFormDetailVo.getType())){
                            continue;
                        }
                    }
                    if(BusinessConfig.LAB_CONFIG_TYPE_02.equals(labConfigType)){
                        List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT);
                        if(!variableTypeList.contains(templateFormDetailVo.getType())){
                            continue;
                        }
                        formVariableVo.setDictionaryResourceType(templateFormDetailVo.getDicResource());
                        formVariableVo.setDictionaryResourceId(templateFormDetailVo.getRefDicId());
                        List<ProjectDictionary> projectDictionaryList = projectDictionaryService.getDictionaryList(projectId,  "1", null, null);
                        formVariableVo.setProjectDictionaryList(projectDictionaryList);
                        List<Dictionary> dictionaryList = projectDictionaryService.getDictionaryListByParentId(projectId, templateFormDetailVo.getRefDicId().toString(), "", "1");
                        formVariableVo.setProjectDictionaryOptionsList(dictionaryList);
                        formVariableVo.setProjectDictionaryId(templateFormDetailVo.getRefDicId().toString());
                    }
                    variableList.add(formVariableVo);
                }

                // 字段组
                if (BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP.equals(templateFormDetailVo.getType())) {
                    List<ProjectTemplateFormVariableTreeVo> formGroupVariableList = new ArrayList<>();
                    List<TemplateFormDetailVo> templateGroupFormDetailList = templateConfigService.getTemplateFormDetailByGroupId(templateFormDetailVo.getFormId().toString(), templateFormDetailVo.getId().toString(), "", false);
                    for (TemplateFormDetailVo templateFormDetail : templateGroupFormDetailList) {
                        ProjectTemplateFormVariableTreeVo formGroupVariableVo = new ProjectTemplateFormVariableTreeVo();
                        List<String> groupVariableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_TABLE, BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP);
                        if(!groupVariableTypeList.contains(templateFormDetail.getType())){
                            if(BusinessConfig.LAB_CONFIG_TYPE_01.equals(labConfigType)){
                                List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_INPUT, BusinessConfig.PROJECT_VISIT_CRF_FORM_NUMBER);
                                if(!variableTypeList.contains(templateFormDetail.getType())){
                                    continue;
                                }
                            }
                            if(BusinessConfig.LAB_CONFIG_TYPE_02.equals(labConfigType)){
                                List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT);
                                if(!variableTypeList.contains(templateFormDetail.getType())){
                                    continue;
                                }
                                formGroupVariableVo.setDictionaryResourceType(templateFormDetail.getDicResource());
                                formGroupVariableVo.setDictionaryResourceId(templateFormDetail.getRefDicId());
                                List<ProjectDictionary> projectDictionaryList = projectDictionaryService.getDictionaryList(projectId,  "1", null, null);
                                formGroupVariableVo.setProjectDictionaryList(projectDictionaryList);
                                List<Dictionary> dictionaryList = projectDictionaryService.getDictionaryListByParentId(projectId, templateFormDetail.getRefDicId().toString(), "", "1");
                                formGroupVariableVo.setProjectDictionaryOptionsList(dictionaryList);
                                formGroupVariableVo.setProjectDictionaryId(templateFormDetail.getRefDicId().toString());
                            }
                        }

                        if (BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(templateFormDetail.getType())) {
                            List<ProjectTemplateFormVariableTreeVo> formTableVariableList = new ArrayList<>();
                            List<TemplateTableVo> templateFormTableListForGroup = templateConfigService.getTemplateFormTableByFormDetailId(templateFormDetail.getId().toString());
                            for (TemplateTableVo templateTableVo : templateFormTableListForGroup) {
                                ProjectTemplateFormVariableTreeVo formGroupTableVariableVo = new ProjectTemplateFormVariableTreeVo();
                                formGroupTableVariableVo.setFormId(templateFormConfig.getId().toString());
                                formGroupTableVariableVo.setFormDetaiId(templateFormDetail.getId().toString());
                                formGroupTableVariableVo.setFormTableId(templateTableVo.getId().toString());
                                formGroupTableVariableVo.setVariableId(templateTableVo.getId().toString());
                                formGroupTableVariableVo.setGroupId(templateFormDetail.getGroupId() == null ? "" : templateFormDetail.getGroupId().toString());
                                formGroupTableVariableVo.setName(templateTableVo.getLabel());
                                formGroupTableVariableVo.setType(templateTableVo.getType());
                                if(BusinessConfig.LAB_CONFIG_TYPE_01.equals(labConfigType)){
                                    List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_INPUT, BusinessConfig.PROJECT_VISIT_CRF_FORM_NUMBER);
                                    if(!variableTypeList.contains(templateTableVo.getType())){
                                        continue;
                                    }
                                }
                                if(BusinessConfig.LAB_CONFIG_TYPE_02.equals(labConfigType)){
                                    List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT);
                                    if(!variableTypeList.contains(templateTableVo.getType())){
                                        continue;
                                    }
                                    formGroupTableVariableVo.setDictionaryResourceType(templateTableVo.getDicResource());
                                    formGroupTableVariableVo.setDictionaryResourceId(templateTableVo.getRefDicId());
                                    List<ProjectDictionary> projectDictionaryList = projectDictionaryService.getDictionaryList(projectId,  "1", null, null);
                                    formGroupTableVariableVo.setProjectDictionaryList(projectDictionaryList);
                                    List<Dictionary> dictionaryList = projectDictionaryService.getDictionaryListByParentId(projectId, templateTableVo.getRefDicId().toString(), "", "1");
                                    formGroupTableVariableVo.setProjectDictionaryOptionsList(dictionaryList);
                                    formGroupTableVariableVo.setProjectDictionaryId(templateTableVo.getRefDicId().toString());
                                }
                                formTableVariableList.add(formGroupTableVariableVo);
                            }
                            formGroupVariableVo.setChildren(formTableVariableList);
                        }
                        formGroupVariableVo.setFormId(templateFormDetail.getFormId().toString());
                        formGroupVariableVo.setFormDetaiId(templateFormDetail.getId().toString());
                        formGroupVariableVo.setVariableId(templateFormDetail.getId().toString());
                        formGroupVariableVo.setGroupId(templateFormDetail.getGroupId() == null ? "" : templateFormDetail.getGroupId().toString());
                        formGroupVariableVo.setName(templateFormDetail.getLabel());
                        formGroupVariableVo.setType(templateFormDetail.getType());
                        formGroupVariableList.add(formGroupVariableVo);
                    }
                    formVariableVo.setFormId(templateFormConfig.getId().toString());
                    formVariableVo.setGroupId(templateFormDetailVo.getGroupId() == null ? "" : templateFormDetailVo.getGroupId().toString());
                    formVariableVo.setFormDetaiId(templateFormDetailVo.getId().toString());
                    formVariableVo.setVariableId(templateFormDetailVo.getId().toString());
                    formVariableVo.setName(templateFormDetailVo.getLabel());
                    formVariableVo.setType(templateFormDetailVo.getType());
                    formVariableVo.setChildren(formGroupVariableList);
                    variableList.add(formVariableVo);
                }

                // 表格
                if (BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(templateFormDetailVo.getType())) {
                    List<TemplateTableVo> templateFormTableList = templateConfigService.getTemplateFormTableByFormDetailId(templateFormDetailVo.getId().toString());
                    List<ProjectTemplateFormVariableTreeVo> formTableVariableList = new ArrayList<>();
                    for (TemplateTableVo templateTableVo : templateFormTableList) {
                        ProjectTemplateFormVariableTreeVo formTableVariableVo = new ProjectTemplateFormVariableTreeVo();
                        formTableVariableVo.setFormId(templateFormConfig.getId().toString());
                        formTableVariableVo.setFormDetaiId(templateFormDetailVo.getId().toString());
                        formTableVariableVo.setFormTableId(templateTableVo.getId().toString());
                        formTableVariableVo.setVariableId(templateTableVo.getId().toString());
                        formTableVariableVo.setName(templateTableVo.getLabel());
                        formTableVariableVo.setType(templateTableVo.getType());
                        if(BusinessConfig.LAB_CONFIG_TYPE_01.equals(labConfigType)){
                            List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_INPUT, BusinessConfig.PROJECT_VISIT_CRF_FORM_NUMBER);
                            if(!variableTypeList.contains(templateTableVo.getType())){
                                continue;
                            }
                        }
                        if(BusinessConfig.LAB_CONFIG_TYPE_02.equals(labConfigType)){
                            List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT);
                            if(!variableTypeList.contains(templateTableVo.getType())){
                                continue;
                            }
                            formTableVariableVo.setDictionaryResourceType(templateTableVo.getDicResource());
                            formTableVariableVo.setDictionaryResourceId(templateTableVo.getRefDicId());
                            List<ProjectDictionary> projectDictionaryList = projectDictionaryService.getDictionaryList(projectId,  "1", null, null);
                            formTableVariableVo.setProjectDictionaryList(projectDictionaryList);
                            List<Dictionary> dictionaryList = projectDictionaryService.getDictionaryListByParentId(projectId, templateTableVo.getRefDicId().toString(), "", "1");
                            formTableVariableVo.setProjectDictionaryOptionsList(dictionaryList);
                            formTableVariableVo.setProjectDictionaryId(templateTableVo.getRefDicId().toString());
                        }
                        formTableVariableList.add(formTableVariableVo);
                    }
                    formVariableVo.setFormId(templateFormConfig.getId().toString());
                    formVariableVo.setFormDetaiId(templateFormDetailVo.getId().toString());
                    formVariableVo.setVariableId(templateFormDetailVo.getId().toString());
                    formVariableVo.setName(templateFormDetailVo.getLabel());
                    formVariableVo.setType(templateFormDetailVo.getType());
                    formVariableVo.setChildren(formTableVariableList);
                    variableList.add(formVariableVo);
                }
                formVo.setChildren(variableList);
            }
            projectTemplateFormVariableTreeVo.add(formVo);
        }
        return projectTemplateFormVariableTreeVo;
    }

    @Override
    public List<ProjectTemplateFormVariableTreeVo> getTemplateVisitFormVariableTreeVo(String projectId, String planId, String labConfigType, String queryGroup, String queryTable) {
        if(StringUtils.isEmpty(queryGroup)){queryGroup = "1";}
        if(StringUtils.isEmpty(queryTable)){queryTable = "1";}
        List<ProjectTemplateFormVariableTreeVo> projectTemplateFormVariableTreeVo = new ArrayList<>();
        List<ProjectVisitConfig> projectVisitConfigList = projectVisitConfigService.getProjectVisitListByPlanId(projectId, planId, "");
        for (ProjectVisitConfig projectVisitConfig : projectVisitConfigList) {
            List<ProjectTemplateFormVariableTreeVo> formVariableList = new ArrayList<>();
            ProjectTemplateFormVariableTreeVo visitVo = new ProjectTemplateFormVariableTreeVo();
            String visitId = projectVisitConfig.getId().toString();
            visitVo.setVariableId(projectVisitConfig.getId().toString());
            visitVo.setName(projectVisitConfig.getVisitName());


            List<FlowPlanFormVo> templateFormConfigList = flowPlanFormService.getFormConfigListByPlanIdAndVisitId(projectId, planId, visitId, "");
            for (FlowPlanFormVo templateFormConfig : templateFormConfigList) {
                ProjectTemplateFormVariableTreeVo formVo = new ProjectTemplateFormVariableTreeVo();
                formVo.setVariableId(templateFormConfig.getFormId());
                formVo.setName(templateFormConfig.getFormName());
                List<ProjectTemplateFormVariableTreeVo> variableList = new ArrayList<>();
                List<TemplateFormDetailVo> templateFormDetailList = templateConfigService.getTemplateFormDetailConfigListByFormId("", templateFormConfig.getFormId(), "", queryTable, queryGroup, "0");
                for (TemplateFormDetailVo templateFormDetailVo : templateFormDetailList) {
                    ProjectTemplateFormVariableTreeVo formVariableVo = new ProjectTemplateFormVariableTreeVo();
                    List<String> varTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_TABLE, BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP);
                    if(!varTypeList.contains(templateFormDetailVo.getType())){
                        if(BusinessConfig.LAB_CONFIG_TYPE_01.equals(labConfigType)){
                            List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_INPUT, BusinessConfig.PROJECT_VISIT_CRF_FORM_NUMBER);
                            if(!variableTypeList.contains(templateFormDetailVo.getType())){
                                continue;
                            }
                        }
                        if(BusinessConfig.LAB_CONFIG_TYPE_02.equals(labConfigType)){
                            List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT);
                            if(!variableTypeList.contains(templateFormDetailVo.getType())){
                                continue;
                            }
                            formVariableVo.setDictionaryResourceType(templateFormDetailVo.getDicResource());
                            formVariableVo.setDictionaryResourceId(templateFormDetailVo.getRefDicId());
                            List<ProjectDictionary> projectDictionaryList = projectDictionaryService.getDictionaryList(projectId,  "1", null, null);
                            formVariableVo.setProjectDictionaryList(projectDictionaryList);
                            List<Dictionary> dictionaryList = projectDictionaryService.getDictionaryListByParentId(projectId, templateFormDetailVo.getRefDicId().toString(), "", "1");
                            formVariableVo.setProjectDictionaryOptionsList(dictionaryList);
                            formVariableVo.setProjectDictionaryId(templateFormDetailVo.getRefDicId().toString());
                        }
                        formVariableVo.setVisitId(projectVisitConfig.getId().toString());
                        formVariableVo.setVisitName(projectVisitConfig.getVisitName());
                        formVariableVo.setFormId(templateFormConfig.getFormId());
                        formVariableVo.setFormName(templateFormConfig.getFormName());
                        formVariableVo.setFormDetaiId(templateFormDetailVo.getId().toString());
                        formVariableVo.setVariableId(templateFormDetailVo.getId().toString());
                        formVariableVo.setGroupId(templateFormDetailVo.getGroupId() == null ? "" : templateFormDetailVo.getGroupId().toString());
                        formVariableVo.setName(templateFormDetailVo.getLabel());
                        formVariableVo.setType(templateFormDetailVo.getType());
                        variableList.add(formVariableVo);
                    }
                    // 字段组
                    if (BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP.equals(templateFormDetailVo.getType())) {
                        List<ProjectTemplateFormVariableTreeVo> formGroupVariableList = new ArrayList<>();
                        List<TemplateFormDetailVo> templateGroupFormDetailList = templateConfigService.getTemplateFormDetailByGroupId(templateFormDetailVo.getFormId().toString(), templateFormDetailVo.getId().toString(), "", false);
                        for (TemplateFormDetailVo templateFormDetail : templateGroupFormDetailList) {
                            ProjectTemplateFormVariableTreeVo formGroupVariableVo = new ProjectTemplateFormVariableTreeVo();
                            List<String> groupVariableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_TABLE);
                            if(!groupVariableTypeList.contains(templateFormDetail.getType())){
                                if(BusinessConfig.LAB_CONFIG_TYPE_01.equals(labConfigType)){
                                    List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_INPUT, BusinessConfig.PROJECT_VISIT_CRF_FORM_NUMBER);
                                    if(!variableTypeList.contains(templateFormDetail.getType())){
                                        continue;
                                    }
                                }
                                if(BusinessConfig.LAB_CONFIG_TYPE_02.equals(labConfigType)){
                                    List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT);
                                    if(!variableTypeList.contains(templateFormDetail.getType())){
                                        continue;
                                    }
                                    formGroupVariableVo.setDictionaryResourceType(templateFormDetail.getDicResource());
                                    formGroupVariableVo.setDictionaryResourceId(templateFormDetail.getRefDicId());
                                    List<ProjectDictionary> projectDictionaryList = projectDictionaryService.getDictionaryList(projectId,  "1", null, null);
                                    formGroupVariableVo.setProjectDictionaryList(projectDictionaryList);
                                    List<Dictionary> dictionaryList = projectDictionaryService.getDictionaryListByParentId(projectId, templateFormDetail.getRefDicId().toString(), "", "1");
                                    formGroupVariableVo.setProjectDictionaryOptionsList(dictionaryList);
                                    formGroupVariableVo.setProjectDictionaryId(templateFormDetail.getRefDicId().toString());
                                }
                            }


                            if (BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(templateFormDetail.getType())) {
                                List<ProjectTemplateFormVariableTreeVo> formTableVariableList = new ArrayList<>();
                                List<TemplateTableVo> templateFormTableListForGroup = templateConfigService.getTemplateFormTableByFormDetailId(templateFormDetail.getId().toString());
                                for (TemplateTableVo templateTableVo : templateFormTableListForGroup) {
                                    ProjectTemplateFormVariableTreeVo formGroupTableVariableVo = new ProjectTemplateFormVariableTreeVo();
                                    formGroupTableVariableVo.setVisitId(projectVisitConfig.getId().toString());
                                    formGroupTableVariableVo.setVisitName(projectVisitConfig.getVisitName());
                                    formGroupTableVariableVo.setFormId(templateFormConfig.getFormId());
                                    formGroupTableVariableVo.setFormName(templateFormConfig.getFormName());
                                    formGroupTableVariableVo.setFormDetaiId(templateFormDetail.getId().toString());
                                    formGroupTableVariableVo.setFormTableId(templateTableVo.getId().toString());
                                    formGroupTableVariableVo.setVariableId(templateTableVo.getId().toString());
                                    formGroupTableVariableVo.setGroupId(templateFormDetail.getGroupId() == null ? "" : templateFormDetail.getGroupId().toString());
                                    formGroupTableVariableVo.setName(templateTableVo.getLabel());
                                    formGroupTableVariableVo.setType(templateTableVo.getType());
                                    if(BusinessConfig.LAB_CONFIG_TYPE_01.equals(labConfigType)){
                                        List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_INPUT, BusinessConfig.PROJECT_VISIT_CRF_FORM_NUMBER);
                                        if(!variableTypeList.contains(templateTableVo.getType())){
                                            continue;
                                        }
                                    }
                                    if(BusinessConfig.LAB_CONFIG_TYPE_02.equals(labConfigType)){
                                        List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT);
                                        if(!variableTypeList.contains(templateTableVo.getType())){
                                            continue;
                                        }
                                        formGroupTableVariableVo.setDictionaryResourceType(templateTableVo.getDicResource());
                                        formGroupTableVariableVo.setDictionaryResourceId(templateTableVo.getRefDicId());
                                        List<ProjectDictionary> projectDictionaryList = projectDictionaryService.getDictionaryList(projectId,  "1", null, null);
                                        formGroupTableVariableVo.setProjectDictionaryList(projectDictionaryList);
                                        List<Dictionary> dictionaryList = projectDictionaryService.getDictionaryListByParentId(projectId, templateTableVo.getRefDicId().toString(), "", "1");
                                        formGroupTableVariableVo.setProjectDictionaryOptionsList(dictionaryList);
                                        formGroupTableVariableVo.setProjectDictionaryId(templateTableVo.getRefDicId().toString());
                                    }

                                    formTableVariableList.add(formGroupTableVariableVo);
                                }
                                formGroupVariableVo.setChildren(formTableVariableList);
                            }
                            formGroupVariableVo.setVisitId(projectVisitConfig.getId().toString());
                            formGroupVariableVo.setVisitName(projectVisitConfig.getVisitName());
                            formGroupVariableVo.setFormId(templateFormDetail.getFormId().toString());
                            formGroupVariableVo.setFormName(templateFormConfig.getFormName());
                            formGroupVariableVo.setFormDetaiId(templateFormDetail.getId().toString());
                            formGroupVariableVo.setVariableId(templateFormDetail.getId().toString());
                            formGroupVariableVo.setGroupId(templateFormDetail.getGroupId() == null ? "" : templateFormDetail.getGroupId().toString());
                            formGroupVariableVo.setName(templateFormDetail.getLabel());
                            formGroupVariableVo.setType(templateFormDetail.getType());
                            formGroupVariableList.add(formGroupVariableVo);
                        }
                        formVariableVo.setVisitId(projectVisitConfig.getId().toString());
                        formVariableVo.setVisitName(projectVisitConfig.getVisitName());
                        formVariableVo.setFormId(templateFormConfig.getFormId());
                        formVariableVo.setFormName(templateFormConfig.getFormName());
                        formVariableVo.setGroupId(templateFormDetailVo.getGroupId() == null ? "" : templateFormDetailVo.getGroupId().toString());
                        formVariableVo.setFormDetaiId(templateFormDetailVo.getId().toString());
                        formVariableVo.setVariableId(templateFormDetailVo.getId().toString());
                        formVariableVo.setName(templateFormDetailVo.getLabel());
                        formVariableVo.setType(templateFormDetailVo.getType());
                        formVariableVo.setChildren(formGroupVariableList);
                        variableList.add(formVariableVo);
                    }
                    // 表格
                    if (BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(templateFormDetailVo.getType())) {
                        List<TemplateTableVo> templateFormTableList = templateConfigService.getTemplateFormTableByFormDetailId(templateFormDetailVo.getId().toString());
                        List<ProjectTemplateFormVariableTreeVo> formTableVariableList = new ArrayList<>();
                        for (TemplateTableVo templateTableVo : templateFormTableList) {
                            ProjectTemplateFormVariableTreeVo formTableVariableVo = new ProjectTemplateFormVariableTreeVo();
                            formTableVariableVo.setVisitId(projectVisitConfig.getId().toString());
                            formTableVariableVo.setVisitName(projectVisitConfig.getVisitName());
                            formTableVariableVo.setFormId(templateFormConfig.getFormId());
                            formTableVariableVo.setFormName(templateFormConfig.getFormName());
                            formTableVariableVo.setFormDetaiId(templateFormDetailVo.getId().toString());
                            formTableVariableVo.setFormTableId(templateTableVo.getId().toString());
                            formTableVariableVo.setVariableId(templateTableVo.getId().toString());
                            formTableVariableVo.setName(templateTableVo.getLabel());
                            formTableVariableVo.setType(templateTableVo.getType());
                            if(BusinessConfig.LAB_CONFIG_TYPE_01.equals(labConfigType)){
                                List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_INPUT, BusinessConfig.PROJECT_VISIT_CRF_FORM_NUMBER);
                                if(!variableTypeList.contains(templateTableVo.getType())){
                                    continue;
                                }
                            }
                            if(BusinessConfig.LAB_CONFIG_TYPE_02.equals(labConfigType)){
                                List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT);
                                if(!variableTypeList.contains(templateTableVo.getType())){
                                    continue;
                                }
                                formTableVariableVo.setDictionaryResourceType(templateTableVo.getDicResource());
                                formTableVariableVo.setDictionaryResourceId(templateTableVo.getRefDicId());
                                List<ProjectDictionary> projectDictionaryList = projectDictionaryService.getDictionaryList(projectId,  "1", null, null);
                                formTableVariableVo.setProjectDictionaryList(projectDictionaryList);
                                List<Dictionary> dictionaryList = projectDictionaryService.getDictionaryListByParentId(projectId, templateTableVo.getRefDicId().toString(), "", "1");
                                formTableVariableVo.setProjectDictionaryOptionsList(dictionaryList);
                                formTableVariableVo.setProjectDictionaryId(templateTableVo.getRefDicId().toString());
                            }

                            formTableVariableList.add(formTableVariableVo);
                        }
                        formVariableVo.setVisitId(projectVisitConfig.getId().toString());
                        formVariableVo.setVisitName(projectVisitConfig.getVisitName());
                        formVariableVo.setFormId(templateFormConfig.getFormId());
                        formVariableVo.setFormName(templateFormConfig.getFormName());
                        formVariableVo.setFormDetaiId(templateFormDetailVo.getId().toString());
                        formVariableVo.setVariableId(templateFormDetailVo.getId().toString());
                        formVariableVo.setName(templateFormDetailVo.getLabel());
                        formVariableVo.setType(templateFormDetailVo.getType());
                        formVariableVo.setChildren(formTableVariableList);
                        variableList.add(formVariableVo);
                    }
                    formVo.setChildren(variableList);
                }
                formVariableList.add(formVo);
            }
            visitVo.setChildren(formVariableList);
            projectTemplateFormVariableTreeVo.add(visitVo);
        }
        return projectTemplateFormVariableTreeVo;
    }

    @Override
    public CustomResult saveTemplateFormLabel(TemplateGroupLableParam templateGroupLableParam) {
        CustomResult customResult = new CustomResult();
        TemplateGroupLable templateGroupLable = new TemplateGroupLable();
        //核查访视时间
        ProjectVisitVo projectVisitVo = projectVisitConfigService.getProjectVisitBaseConfigByVisitId(templateGroupLableParam.getVisitId().toString());
        if (projectVisitVo == null) {
            customResult.setMessage(BusinessConfig.RETURN_MESSAGE_PROJECT_VISIT_RECORD_NOT_FOUND);
            return customResult;
        }
        if (Constants.PROJECT_VISIT_TYPE_01.equals(projectVisitVo.getVisitType())) {
            checkTemplateVisitLabelRecord(templateGroupLableParam);
        }
        if (templateGroupLableParam.getId() == null) {
            BeanUtils.copyProperties(templateGroupLableParam, templateGroupLable);
            templateGroupLable.setId(SnowflakeIdWorker.getUuid());
            templateGroupLable.setStatus("0");
            templateGroupLable.setCreateTime(new Date());
            templateGroupLable.setLeafNode(true);
            Integer sort = getTemplateFormLabelSort(templateGroupLableParam);
            templateGroupLable.setSort(sort);
            templateGroupLableMapper.insertSelective(templateGroupLable);
            customResult.setData(templateGroupLable.getId());
        } else {
            templateGroupLable = templateGroupLableMapper.selectByPrimaryKey(templateGroupLableParam.getId());
            if (templateGroupLable == null) {
                customResult.setMessage(BusinessConfig.RETURN_MESSAGE_RECORD_NOT_FOUND);
                return customResult;
            }
            BeanUtils.copyProperties(templateGroupLableParam, templateGroupLable);
            templateGroupLable.setUpdateTime(new Date());
            templateGroupLable.setUpdateUser(templateGroupLableParam.getCreateUser());
            templateGroupLableMapper.updateByPrimaryKeySelective(templateGroupLable);
        }
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }

    private Integer getTemplateFormLabelSort(TemplateGroupLableParam templateGroupLableParam) {
        TemplateGroupLableExample example = new TemplateGroupLableExample();
        TemplateGroupLableExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(templateGroupLableParam.getProjectId());
        criteria.andVisitIdEqualTo(templateGroupLableParam.getVisitId());
        return templateGroupLableMapper.selectByExample(example).size();
    }

    @Override
    public String deleteTemplateFormLabel(String projectId, String labelId) {
        //删除分类
        TemplateGroupLable templateGroupLable = templateGroupLableMapper.selectByPrimaryKey(Long.parseLong(labelId));
        if (templateGroupLable == null) {
            return BusinessConfig.RETURN_MESSAGE_RECORD_NOT_FOUND;
        }
        List<TemplateFormConfig> dataList = templateConfigService.getTemplateFormConfigByGroupName(projectId, labelId);
        if (dataList != null && dataList.size() > 0) {
            return BusinessConfig.RETURN_MESSAGE_FORM_LABEL_FORBIDDEN;
        }
        templateGroupLableMapper.deleteByPrimaryKey(Long.parseLong(labelId));
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }


    private void checkTemplateVisitLabelRecord(TemplateGroupLableParam templateGroupLableParam) {
        TemplateGroupLableExample example = new TemplateGroupLableExample();
        TemplateGroupLableExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(templateGroupLableParam.getProjectId());
        criteria.andVisitIdEqualTo(templateGroupLableParam.getVisitId());
        criteria.andLableNameEqualTo(BusinessConfig.PROJECT_LABEL_VISIT_NAME);
        List<TemplateGroupLable> templateGroupLables = templateGroupLableMapper.selectByExample(example);
        if (templateGroupLables == null || templateGroupLables.size() == 0) {
            TemplateGroupLable templateGroupLable = new TemplateGroupLable();
            templateGroupLable.setId(SnowflakeIdWorker.getUuid());
            templateGroupLable.setProjectId(templateGroupLableParam.getProjectId());
            templateGroupLable.setVisitId(templateGroupLableParam.getVisitId());
            templateGroupLable.setLableName(BusinessConfig.PROJECT_LABEL_VISIT_NAME);
            templateGroupLable.setParentId(0L);
            templateGroupLable.setResourceType("2");
            templateGroupLable.setStatus("0");
            templateGroupLable.setSort(0);
            templateGroupLable.setCreateTime(new Date());
            templateGroupLable.setCreateUser(templateGroupLableParam.getCreateUser());
            templateGroupLable.setLeafNode(true);
            templateGroupLableMapper.insertSelective(templateGroupLable);
        }
    }
}
