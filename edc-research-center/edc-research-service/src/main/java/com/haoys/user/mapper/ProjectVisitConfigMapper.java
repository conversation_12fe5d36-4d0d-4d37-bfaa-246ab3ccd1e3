package com.haoys.user.mapper;

import com.haoys.user.model.ProjectVisitConfig;
import com.haoys.user.model.ProjectVisitConfigExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProjectVisitConfigMapper {
    long countByExample(ProjectVisitConfigExample example);

    int deleteByExample(ProjectVisitConfigExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectVisitConfig record);

    int insertSelective(ProjectVisitConfig record);

    List<ProjectVisitConfig> selectByExample(ProjectVisitConfigExample example);

    ProjectVisitConfig selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectVisitConfig record, @Param("example") ProjectVisitConfigExample example);

    int updateByExample(@Param("record") ProjectVisitConfig record, @Param("example") ProjectVisitConfigExample example);

    int updateByPrimaryKeySelective(ProjectVisitConfig record);

    int updateByPrimaryKey(ProjectVisitConfig record);

    /**
     * 获取研究流程最大的排序号
     * @param planId 计划id
     * @return
     */
    Integer getMaxSort(Long planId);
}
