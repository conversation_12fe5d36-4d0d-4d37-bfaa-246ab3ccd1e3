package com.haoys.user.domain.vo.testee;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 参与者管理-图形化信息
 */

@Data
public class ProjectTesteeFillFlowVo {

    @ApiModelProperty(value = "流程id")
    private String flowId;

    @ApiModelProperty(value = "流程名称")
    private String flowName;

    @ApiModelProperty(value = "已录入的表单数量")
    private Integer fillFormNum=0;

    @ApiModelProperty(value = "已签名的表单数量")
    private Integer signFormNum=0;

    @ApiModelProperty(value = "质疑的表单数量")
    private Integer challengeFormNum=0;

    @ApiModelProperty(value = "未关闭的表单数量")
    private Integer notCloseFormNum=0;

    @ApiModelProperty(value = "总表单数量")
    private Integer allFormNum=0;

    @ApiModelProperty(value = "表单填写占比")
    private Integer fillProportion=0;


}
