package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.mapper.ProjectFormAuditMapper;
import com.haoys.user.model.ProjectFormAudit;
import com.haoys.user.model.ProjectFormAuditExample;
import com.haoys.user.service.ProjectFormAuditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 流程配置-创建计划服务实现类
 */
@Service
public class ProjectFormAuditServiceImpl extends BaseService implements ProjectFormAuditService {

    @Autowired
    private ProjectFormAuditMapper projectFormAuditMapper;


    @Override
    public int create(ProjectFormAudit audit) {
        return projectFormAuditMapper.insert(audit);
    }

    @Override
    public int update(ProjectFormAudit audit) {
        return projectFormAuditMapper.updateByPrimaryKey(audit);
    }

    @Override
    public int delete(Long auditId) {
        return projectFormAuditMapper.deleteByPrimaryKey(auditId);
    }

    @Override
    public List<ProjectFormAudit> getProjectFormAuditList(Long projectId, Long visitId, Long formId, Long testeeId) {
        ProjectFormAuditExample example = new ProjectFormAuditExample();
        ProjectFormAuditExample.Criteria criteria = example.createCriteria();
        if (projectId != null){
            criteria.andProjectIdEqualTo(projectId);
        }
        if (visitId != null){
            criteria.andVisitIdEqualTo(visitId);
        }
        if (formId != null){
            criteria.andFormIdEqualTo(formId);
        }
        if (testeeId != null){
            criteria.andTesteeIdEqualTo(testeeId);
        }
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        List<ProjectFormAudit> projectFormAudits = projectFormAuditMapper.selectByExample(example);
        return projectFormAudits;
    }

    @Override
    public Boolean isHavAudit(Long projectId) {
        return null;
    }

    @Override
    public ProjectFormAudit getProjectFormAuditInfo(Long projectId, Long visitId, Long formId, Long testeeId) {
        List<ProjectFormAudit> projectFormAuditList = getProjectFormAuditList(projectId, visitId, formId, testeeId);
        if (CollectionUtil.isNotEmpty(projectFormAuditList)){
            return projectFormAuditList.get(0);
        }
        return null;
    }

    @Override
    public CustomResult updateAuditForm(String auditId, String auditStatus) {
        CustomResult customResult = new CustomResult();
        ProjectFormAudit projectFormAudit = projectFormAuditMapper.selectByPrimaryKey(Long.valueOf(auditId));
        if(projectFormAudit == null){
            customResult.setCode(ResultCode.PROJECT_FORM_AUDIT_NOT_FOUND.getCode());
            customResult.setMessage(ResultCode.PROJECT_FORM_AUDIT_NOT_FOUND.getMessage());
            return customResult;
        }
        projectFormAudit.setAuditStatus(auditStatus);
        projectFormAudit.setAuditTime(new Date());
        projectFormAudit.setAuditUserId(SecurityUtils.getUserId());
        projectFormAuditMapper.updateByPrimaryKey(projectFormAudit);
        return customResult;
    }
}
