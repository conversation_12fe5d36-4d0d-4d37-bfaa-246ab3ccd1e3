package com.haoys.user.service.impl;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.domain.param.project.ProjectPatientPlanParam;
import com.haoys.user.domain.vo.project.ProjectPlanVo;
import com.haoys.user.mapper.ProjectPatientPlanDetailMapper;
import com.haoys.user.mapper.ProjectPatientPlanMapper;
import com.haoys.user.model.ProjectPatientPlan;
import com.haoys.user.model.ProjectPatientPlanDetail;
import com.haoys.user.model.ProjectPatientPlanDetailExample;
import com.haoys.user.model.ProjectPatientPlanExample;
import com.haoys.user.service.ProjectPlanManageService;
import com.haoys.user.service.ProjectTaskManageService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class ProjectPlanManageServiceImpl extends BaseService implements ProjectPlanManageService {

    private final ProjectPatientPlanMapper projectPatientPlanMapper;
    private final ProjectPatientPlanDetailMapper projectPatientPlanDetailMapper;
    private final ProjectTaskManageService projectTaskManageService;

    @Override
    public CommonPage<ProjectPlanVo> getProjectPlanListForPage(String projectId, String name, String userId, Integer pageSize, Integer pageNum) {
        List<ProjectPlanVo> dataList = new ArrayList<>();
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        ProjectPatientPlanExample example = new ProjectPatientPlanExample();
        ProjectPatientPlanExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andStatusNotEqualTo("-1");
        example.setOrderByClause("create_time desc");
        List<ProjectPatientPlan> projectPatientPlanList = projectPatientPlanMapper.selectByExample(example);
        for (ProjectPatientPlan projectPatientPlan : projectPatientPlanList) {
            ProjectPlanVo projectPlanVo = new ProjectPlanVo();
            BeanUtils.copyProperties(projectPatientPlan, projectPlanVo);
            dataList.add(projectPlanVo);
        }
        return commonPageListWrapper(pageNum, pageSize, page, dataList);
    }

    @Override
    public CustomResult saveProjectPlan(ProjectPatientPlanParam projectPatientPlanParam) {
        CustomResult customResult = new CustomResult();
        if(projectPatientPlanParam.getId() == null){
            ProjectPatientPlan record = new ProjectPatientPlan();
            BeanUtils.copyProperties(projectPatientPlanParam, record);
            record.setId(SnowflakeIdWorker.getUuid());
            record.setStatus("0");
            record.setCreateTime(new Date());
            record.setCreateUser(projectPatientPlanParam.getCreateUserId());
            projectPatientPlanMapper.insert(record);
            List<ProjectPatientPlanParam.ProjectPatientPlanVariableParam> dataList = projectPatientPlanParam.getDataList();
            for (ProjectPatientPlanParam.ProjectPatientPlanVariableParam projectPatientPlanVariableParam : dataList) {
                ProjectPatientPlanDetail projectPatientPlanDetail = new ProjectPatientPlanDetail();
                BeanUtils.copyProperties(projectPatientPlanVariableParam, projectPatientPlanDetail);
                projectPatientPlanDetail.setId(SnowflakeIdWorker.getUuid());
                projectPatientPlanDetail.setPlanId(record.getId());
                projectPatientPlanDetail.setStatus("0");
                projectPatientPlanDetail.setCreateTime(new Date());
                projectPatientPlanDetail.setCreateUser(projectPatientPlanParam.getCreateUserId());
                projectPatientPlanDetailMapper.insert(projectPatientPlanDetail);
            }
        }else{
            ProjectPatientPlan projectPatientPlan = projectPatientPlanMapper.selectByPrimaryKey(projectPatientPlanParam.getId());
            BeanUtils.copyProperties(projectPatientPlanParam, projectPatientPlan);
            projectPatientPlan.setUpdateTime(new Date());
            projectPatientPlan.setUpdateUser(projectPatientPlanParam.getCreateUserId());
            projectPatientPlanMapper.updateByPrimaryKeySelective(projectPatientPlan);
            ProjectPatientPlanDetailExample example = new ProjectPatientPlanDetailExample();
            ProjectPatientPlanDetailExample.Criteria criteria = example.createCriteria();
            criteria.andStatusEqualTo("0");
            criteria.andPlanIdEqualTo(projectPatientPlan.getId());
            List<ProjectPatientPlanDetail> projectPatientPlanDetailList = projectPatientPlanDetailMapper.selectByExample(example);
            for (ProjectPatientPlanDetail projectPatientPlanDetail : projectPatientPlanDetailList) {
                projectPatientPlanDetailMapper.deleteByPrimaryKey(projectPatientPlanDetail.getId());
            }
            List<ProjectPatientPlanParam.ProjectPatientPlanVariableParam> dataList = projectPatientPlanParam.getDataList();
            for (ProjectPatientPlanParam.ProjectPatientPlanVariableParam projectPatientPlanVariableParam : dataList) {
                ProjectPatientPlanDetail projectPatientPlanDetail = new ProjectPatientPlanDetail();
                BeanUtils.copyProperties(projectPatientPlanVariableParam, projectPatientPlanDetail);
                projectPatientPlanDetail.setId(SnowflakeIdWorker.getUuid());
                projectPatientPlanDetail.setPlanId(projectPatientPlan.getId());
                projectPatientPlanDetail.setStatus("0");
                projectPatientPlanDetail.setCreateTime(new Date());
                projectPatientPlanDetail.setCreateUser(projectPatientPlanParam.getCreateUserId());
                projectPatientPlanDetailMapper.insert(projectPatientPlanDetail);
            }
        }
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }

    @Override
    public ProjectPlanVo getProjectPlanInfo(String planId) {
        ProjectPlanVo projectPlanVo = new ProjectPlanVo();
        List<ProjectPlanVo.ProjectPatientPlanVariableVo> dataList = new ArrayList<>();
        ProjectPatientPlan projectPatientPlan = projectPatientPlanMapper.selectByPrimaryKey(Long.parseLong(planId));
        if(projectPatientPlan != null){
            BeanUtils.copyProperties(projectPatientPlan, projectPlanVo);
            ProjectPatientPlanDetailExample example = new ProjectPatientPlanDetailExample();
            ProjectPatientPlanDetailExample.Criteria criteria = example.createCriteria();
            criteria.andStatusEqualTo("0");
            criteria.andPlanIdEqualTo(Long.parseLong(planId));
            List<ProjectPatientPlanDetail> projectPatientPlanDetailList = projectPatientPlanDetailMapper.selectByExample(example);
            for (ProjectPatientPlanDetail projectPatientPlanDetail : projectPatientPlanDetailList) {
                ProjectPlanVo.ProjectPatientPlanVariableVo projectPatientPlanVariableVo = new ProjectPlanVo.ProjectPatientPlanVariableVo();
                BeanUtils.copyProperties(projectPatientPlanDetail, projectPatientPlanVariableVo);
                dataList.add(projectPatientPlanVariableVo);
            }
            projectPlanVo.setDataList(dataList);
        }
        return projectPlanVo;
    }

    @Override
    public CustomResult updateProjectPlanStatus(String planId, String status, String userId) {
        CustomResult customResult = new CustomResult();
        ProjectPatientPlan projectPatientPlan = projectPatientPlanMapper.selectByPrimaryKey(Long.parseLong(planId));
        if(projectPatientPlan == null){
            customResult.setMessage(BusinessConfig.RETURN_MESSAGE_RECORD_NOT_FOUND);
            return customResult;
        }
        projectPatientPlan.setStatus(status);
        projectPatientPlan.setUpdateUser(userId);
        projectPatientPlan.setUpdateTime(new Date());
        projectPatientPlanMapper.updateByPrimaryKey(projectPatientPlan);
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }

    @Override
    public ProjectPlanVo getRecentProjectPlan(String projectId) {
        ProjectPlanVo projectPlanVo = new ProjectPlanVo();
        ProjectPatientPlanExample example = new ProjectPatientPlanExample();
        ProjectPatientPlanExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andStatusEqualTo("0");
        example.setOrderByClause("create_time desc");
        List<ProjectPatientPlan> projectPatientPlanList = projectPatientPlanMapper.selectByExample(example);
        if(CollectionUtils.isNotEmpty(projectPatientPlanList)){
            ProjectPatientPlan projectPatientPlan = projectPatientPlanList.get(0);
            BeanUtils.copyProperties(projectPatientPlan, projectPlanVo);

            List<ProjectPlanVo.ProjectPatientPlanVariableVo> dataList = new ArrayList<>();
            ProjectPatientPlanDetailExample detailExample = new ProjectPatientPlanDetailExample();
            ProjectPatientPlanDetailExample.Criteria detailCriteria = detailExample.createCriteria();
            detailCriteria.andStatusEqualTo("0");
            detailCriteria.andPlanIdEqualTo(projectPatientPlan.getId());
            List<ProjectPatientPlanDetail> projectPatientPlanDetailList = projectPatientPlanDetailMapper.selectByExample(detailExample);
            for (ProjectPatientPlanDetail projectPatientPlanDetail : projectPatientPlanDetailList) {
                ProjectPlanVo.ProjectPatientPlanVariableVo projectPatientPlanVariableVo = new ProjectPlanVo.ProjectPatientPlanVariableVo();
                BeanUtils.copyProperties(projectPatientPlanDetail, projectPatientPlanVariableVo);
                dataList.add(projectPatientPlanVariableVo);
            }
            projectPlanVo.setDataList(dataList);
            return projectPlanVo;
        }
        return null;
    }

    @Override
    public List<ProjectPatientPlanDetail> getProjectPlanListByCondition(String taskId) {
        ProjectPatientPlanDetailExample example = new ProjectPatientPlanDetailExample();
        ProjectPatientPlanDetailExample.Criteria criteria = example.createCriteria();
        criteria.andStatusEqualTo("0");
        criteria.andTaskIdEqualTo(Long.parseLong(taskId));
        List<ProjectPatientPlanDetail> projectPatientPlanDetailList = projectPatientPlanDetailMapper.selectByExample(example);
        return projectPatientPlanDetailList;
    }

    @Override
    public List<ProjectPatientPlan> getProjectPlanListByProjectId(Long projectId) {
        ProjectPatientPlanExample example = new ProjectPatientPlanExample();
        ProjectPatientPlanExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(projectId);
        criteria.andStatusEqualTo("0");
        List<ProjectPatientPlan> projectPatientPlanList = projectPatientPlanMapper.selectByExample(example);
        return projectPatientPlanList;
    }

    @Override
    public List<ProjectPlanVo> getProjectPlanConditionList(String projectId) {
        List<ProjectPlanVo> projectPlanVoList = new ArrayList<>();
        ProjectPatientPlanExample example = new ProjectPatientPlanExample();
        ProjectPatientPlanExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andStatusEqualTo("0");
        example.setOrderByClause("create_time desc");
        List<ProjectPatientPlan> projectPatientPlanList = projectPatientPlanMapper.selectByExample(example);
        for (ProjectPatientPlan projectPatientPlan : projectPatientPlanList) {
            ProjectPlanVo projectPlanVo = new ProjectPlanVo();
            BeanUtils.copyProperties(projectPatientPlan, projectPlanVo);
            List<ProjectPlanVo.ProjectPatientPlanVariableVo> dataList = new ArrayList<>();
            ProjectPatientPlanDetailExample detailExample = new ProjectPatientPlanDetailExample();
            ProjectPatientPlanDetailExample.Criteria detailCriteria = detailExample.createCriteria();
            detailCriteria.andPlanIdEqualTo(projectPatientPlan.getId());
            detailCriteria.andStatusEqualTo("0");
            List<ProjectPatientPlanDetail> patientPlanDetailList = projectPatientPlanDetailMapper.selectByExample(detailExample);
            for (ProjectPatientPlanDetail projectPatientPlanDetail : patientPlanDetailList) {
                ProjectPlanVo.ProjectPatientPlanVariableVo projectPatientPlanVariableVo = new ProjectPlanVo.ProjectPatientPlanVariableVo();
                BeanUtils.copyProperties(projectPatientPlanDetail, projectPatientPlanVariableVo);
                dataList.add(projectPatientPlanVariableVo);
            }
            projectPlanVo.setDataList(dataList);
            projectPlanVoList.add(projectPlanVo);
        }
        return projectPlanVoList;
    }
}
