package com.haoys.user.domain.param.project;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectTesteeBatchUploadParam implements Serializable {

    @NotNull
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @NotNull
    @ApiModelProperty(value = "参与者id")
    private Long testeeId;

    @ApiModelProperty(value = "表单变量数据集合")
    List<TesteeFormResultValue> dataList = new ArrayList<>();

    @ApiModelProperty(value = "操作人")
    private String operator;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TesteeFormResultValue implements Serializable{

        @ApiModelProperty(value = "参与者提交结果记录id -编辑数据时必须设置")
        private Long id;

        @ApiModelProperty(value = "访视id")
        private Long visitId;

        @ApiModelProperty(value = "表单项目id")
        private Long formId;

        @ApiModelProperty(value = "表单项明细id")
        private Long formDetailId;

        @ApiModelProperty(value = "字段名称")
        private String label;

        @ApiModelProperty(value = "字段key")
        private String fieldName;

        @ApiModelProperty(value = "字段值")
        private Object fieldValue;

        @ApiModelProperty(value = "排序")
        private Integer sort;

        @ApiModelProperty(value = "数据状态")
        private String status;

        @ApiModelProperty(value = "计量单位")
        private String unitValue;

        @ApiModelProperty(value = "如果是表格类型 设置此状态 1-未录入 2-录入中 3-已完成")
        private String complateStatus;

    }

}

