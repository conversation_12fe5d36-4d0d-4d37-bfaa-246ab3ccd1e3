package com.haoys.user.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class RctsTesteeInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public RctsTesteeInfoExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdIsNull() {
            addCriterion("testee_id is null");
            return (Criteria) this;
        }

        public Criteria andTesteeIdIsNotNull() {
            addCriterion("testee_id is not null");
            return (Criteria) this;
        }

        public Criteria andTesteeIdEqualTo(Long value) {
            addCriterion("testee_id =", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdNotEqualTo(Long value) {
            addCriterion("testee_id <>", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdGreaterThan(Long value) {
            addCriterion("testee_id >", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("testee_id >=", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdLessThan(Long value) {
            addCriterion("testee_id <", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdLessThanOrEqualTo(Long value) {
            addCriterion("testee_id <=", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdIn(List<Long> values) {
            addCriterion("testee_id in", values, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdNotIn(List<Long> values) {
            addCriterion("testee_id not in", values, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdBetween(Long value1, Long value2) {
            addCriterion("testee_id between", value1, value2, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdNotBetween(Long value1, Long value2) {
            addCriterion("testee_id not between", value1, value2, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeIsNull() {
            addCriterion("testee_code is null");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeIsNotNull() {
            addCriterion("testee_code is not null");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeEqualTo(String value) {
            addCriterion("testee_code =", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeNotEqualTo(String value) {
            addCriterion("testee_code <>", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeGreaterThan(String value) {
            addCriterion("testee_code >", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("testee_code >=", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeLessThan(String value) {
            addCriterion("testee_code <", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeLessThanOrEqualTo(String value) {
            addCriterion("testee_code <=", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeLike(String value) {
            addCriterion("testee_code like", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeNotLike(String value) {
            addCriterion("testee_code not like", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeIn(List<String> values) {
            addCriterion("testee_code in", values, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeNotIn(List<String> values) {
            addCriterion("testee_code not in", values, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeBetween(String value1, String value2) {
            addCriterion("testee_code between", value1, value2, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeNotBetween(String value1, String value2) {
            addCriterion("testee_code not between", value1, value2, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andRealNameIsNull() {
            addCriterion("real_name is null");
            return (Criteria) this;
        }

        public Criteria andRealNameIsNotNull() {
            addCriterion("real_name is not null");
            return (Criteria) this;
        }

        public Criteria andRealNameEqualTo(String value) {
            addCriterion("real_name =", value, "realName");
            return (Criteria) this;
        }

        public Criteria andRealNameNotEqualTo(String value) {
            addCriterion("real_name <>", value, "realName");
            return (Criteria) this;
        }

        public Criteria andRealNameGreaterThan(String value) {
            addCriterion("real_name >", value, "realName");
            return (Criteria) this;
        }

        public Criteria andRealNameGreaterThanOrEqualTo(String value) {
            addCriterion("real_name >=", value, "realName");
            return (Criteria) this;
        }

        public Criteria andRealNameLessThan(String value) {
            addCriterion("real_name <", value, "realName");
            return (Criteria) this;
        }

        public Criteria andRealNameLessThanOrEqualTo(String value) {
            addCriterion("real_name <=", value, "realName");
            return (Criteria) this;
        }

        public Criteria andRealNameLike(String value) {
            addCriterion("real_name like", value, "realName");
            return (Criteria) this;
        }

        public Criteria andRealNameNotLike(String value) {
            addCriterion("real_name not like", value, "realName");
            return (Criteria) this;
        }

        public Criteria andRealNameIn(List<String> values) {
            addCriterion("real_name in", values, "realName");
            return (Criteria) this;
        }

        public Criteria andRealNameNotIn(List<String> values) {
            addCriterion("real_name not in", values, "realName");
            return (Criteria) this;
        }

        public Criteria andRealNameBetween(String value1, String value2) {
            addCriterion("real_name between", value1, value2, "realName");
            return (Criteria) this;
        }

        public Criteria andRealNameNotBetween(String value1, String value2) {
            addCriterion("real_name not between", value1, value2, "realName");
            return (Criteria) this;
        }

        public Criteria andAcronymIsNull() {
            addCriterion("acronym is null");
            return (Criteria) this;
        }

        public Criteria andAcronymIsNotNull() {
            addCriterion("acronym is not null");
            return (Criteria) this;
        }

        public Criteria andAcronymEqualTo(String value) {
            addCriterion("acronym =", value, "acronym");
            return (Criteria) this;
        }

        public Criteria andAcronymNotEqualTo(String value) {
            addCriterion("acronym <>", value, "acronym");
            return (Criteria) this;
        }

        public Criteria andAcronymGreaterThan(String value) {
            addCriterion("acronym >", value, "acronym");
            return (Criteria) this;
        }

        public Criteria andAcronymGreaterThanOrEqualTo(String value) {
            addCriterion("acronym >=", value, "acronym");
            return (Criteria) this;
        }

        public Criteria andAcronymLessThan(String value) {
            addCriterion("acronym <", value, "acronym");
            return (Criteria) this;
        }

        public Criteria andAcronymLessThanOrEqualTo(String value) {
            addCriterion("acronym <=", value, "acronym");
            return (Criteria) this;
        }

        public Criteria andAcronymLike(String value) {
            addCriterion("acronym like", value, "acronym");
            return (Criteria) this;
        }

        public Criteria andAcronymNotLike(String value) {
            addCriterion("acronym not like", value, "acronym");
            return (Criteria) this;
        }

        public Criteria andAcronymIn(List<String> values) {
            addCriterion("acronym in", values, "acronym");
            return (Criteria) this;
        }

        public Criteria andAcronymNotIn(List<String> values) {
            addCriterion("acronym not in", values, "acronym");
            return (Criteria) this;
        }

        public Criteria andAcronymBetween(String value1, String value2) {
            addCriterion("acronym between", value1, value2, "acronym");
            return (Criteria) this;
        }

        public Criteria andAcronymNotBetween(String value1, String value2) {
            addCriterion("acronym not between", value1, value2, "acronym");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdIsNull() {
            addCriterion("owner_org_id is null");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdIsNotNull() {
            addCriterion("owner_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdEqualTo(String value) {
            addCriterion("owner_org_id =", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdNotEqualTo(String value) {
            addCriterion("owner_org_id <>", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdGreaterThan(String value) {
            addCriterion("owner_org_id >", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdGreaterThanOrEqualTo(String value) {
            addCriterion("owner_org_id >=", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdLessThan(String value) {
            addCriterion("owner_org_id <", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdLessThanOrEqualTo(String value) {
            addCriterion("owner_org_id <=", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdLike(String value) {
            addCriterion("owner_org_id like", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdNotLike(String value) {
            addCriterion("owner_org_id not like", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdIn(List<String> values) {
            addCriterion("owner_org_id in", values, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdNotIn(List<String> values) {
            addCriterion("owner_org_id not in", values, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdBetween(String value1, String value2) {
            addCriterion("owner_org_id between", value1, value2, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdNotBetween(String value1, String value2) {
            addCriterion("owner_org_id not between", value1, value2, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameIsNull() {
            addCriterion("owner_org_name is null");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameIsNotNull() {
            addCriterion("owner_org_name is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameEqualTo(String value) {
            addCriterion("owner_org_name =", value, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameNotEqualTo(String value) {
            addCriterion("owner_org_name <>", value, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameGreaterThan(String value) {
            addCriterion("owner_org_name >", value, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("owner_org_name >=", value, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameLessThan(String value) {
            addCriterion("owner_org_name <", value, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameLessThanOrEqualTo(String value) {
            addCriterion("owner_org_name <=", value, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameLike(String value) {
            addCriterion("owner_org_name like", value, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameNotLike(String value) {
            addCriterion("owner_org_name not like", value, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameIn(List<String> values) {
            addCriterion("owner_org_name in", values, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameNotIn(List<String> values) {
            addCriterion("owner_org_name not in", values, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameBetween(String value1, String value2) {
            addCriterion("owner_org_name between", value1, value2, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameNotBetween(String value1, String value2) {
            addCriterion("owner_org_name not between", value1, value2, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andAgeIsNull() {
            addCriterion("age is null");
            return (Criteria) this;
        }

        public Criteria andAgeIsNotNull() {
            addCriterion("age is not null");
            return (Criteria) this;
        }

        public Criteria andAgeEqualTo(Integer value) {
            addCriterion("age =", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotEqualTo(Integer value) {
            addCriterion("age <>", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeGreaterThan(Integer value) {
            addCriterion("age >", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeGreaterThanOrEqualTo(Integer value) {
            addCriterion("age >=", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeLessThan(Integer value) {
            addCriterion("age <", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeLessThanOrEqualTo(Integer value) {
            addCriterion("age <=", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeIn(List<Integer> values) {
            addCriterion("age in", values, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotIn(List<Integer> values) {
            addCriterion("age not in", values, "age");
            return (Criteria) this;
        }

        public Criteria andAgeBetween(Integer value1, Integer value2) {
            addCriterion("age between", value1, value2, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotBetween(Integer value1, Integer value2) {
            addCriterion("age not between", value1, value2, "age");
            return (Criteria) this;
        }

        public Criteria andGenderIsNull() {
            addCriterion("gender is null");
            return (Criteria) this;
        }

        public Criteria andGenderIsNotNull() {
            addCriterion("gender is not null");
            return (Criteria) this;
        }

        public Criteria andGenderEqualTo(String value) {
            addCriterion("gender =", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotEqualTo(String value) {
            addCriterion("gender <>", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderGreaterThan(String value) {
            addCriterion("gender >", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderGreaterThanOrEqualTo(String value) {
            addCriterion("gender >=", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderLessThan(String value) {
            addCriterion("gender <", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderLessThanOrEqualTo(String value) {
            addCriterion("gender <=", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderLike(String value) {
            addCriterion("gender like", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotLike(String value) {
            addCriterion("gender not like", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderIn(List<String> values) {
            addCriterion("gender in", values, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotIn(List<String> values) {
            addCriterion("gender not in", values, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderBetween(String value1, String value2) {
            addCriterion("gender between", value1, value2, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotBetween(String value1, String value2) {
            addCriterion("gender not between", value1, value2, "gender");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberIsNull() {
            addCriterion("randomized_number is null");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberIsNotNull() {
            addCriterion("randomized_number is not null");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberEqualTo(String value) {
            addCriterion("randomized_number =", value, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberNotEqualTo(String value) {
            addCriterion("randomized_number <>", value, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberGreaterThan(String value) {
            addCriterion("randomized_number >", value, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberGreaterThanOrEqualTo(String value) {
            addCriterion("randomized_number >=", value, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberLessThan(String value) {
            addCriterion("randomized_number <", value, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberLessThanOrEqualTo(String value) {
            addCriterion("randomized_number <=", value, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberLike(String value) {
            addCriterion("randomized_number like", value, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberNotLike(String value) {
            addCriterion("randomized_number not like", value, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberIn(List<String> values) {
            addCriterion("randomized_number in", values, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberNotIn(List<String> values) {
            addCriterion("randomized_number not in", values, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberBetween(String value1, String value2) {
            addCriterion("randomized_number between", value1, value2, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberNotBetween(String value1, String value2) {
            addCriterion("randomized_number not between", value1, value2, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameIsNull() {
            addCriterion("join_group_name is null");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameIsNotNull() {
            addCriterion("join_group_name is not null");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameEqualTo(String value) {
            addCriterion("join_group_name =", value, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameNotEqualTo(String value) {
            addCriterion("join_group_name <>", value, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameGreaterThan(String value) {
            addCriterion("join_group_name >", value, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameGreaterThanOrEqualTo(String value) {
            addCriterion("join_group_name >=", value, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameLessThan(String value) {
            addCriterion("join_group_name <", value, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameLessThanOrEqualTo(String value) {
            addCriterion("join_group_name <=", value, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameLike(String value) {
            addCriterion("join_group_name like", value, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameNotLike(String value) {
            addCriterion("join_group_name not like", value, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameIn(List<String> values) {
            addCriterion("join_group_name in", values, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameNotIn(List<String> values) {
            addCriterion("join_group_name not in", values, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameBetween(String value1, String value2) {
            addCriterion("join_group_name between", value1, value2, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameNotBetween(String value1, String value2) {
            addCriterion("join_group_name not between", value1, value2, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeIsNull() {
            addCriterion("randomized_time is null");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeIsNotNull() {
            addCriterion("randomized_time is not null");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeEqualTo(Date value) {
            addCriterion("randomized_time =", value, "randomizedTime");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeNotEqualTo(Date value) {
            addCriterion("randomized_time <>", value, "randomizedTime");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeGreaterThan(Date value) {
            addCriterion("randomized_time >", value, "randomizedTime");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("randomized_time >=", value, "randomizedTime");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeLessThan(Date value) {
            addCriterion("randomized_time <", value, "randomizedTime");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeLessThanOrEqualTo(Date value) {
            addCriterion("randomized_time <=", value, "randomizedTime");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeIn(List<Date> values) {
            addCriterion("randomized_time in", values, "randomizedTime");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeNotIn(List<Date> values) {
            addCriterion("randomized_time not in", values, "randomizedTime");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeBetween(Date value1, Date value2) {
            addCriterion("randomized_time between", value1, value2, "randomizedTime");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeNotBetween(Date value1, Date value2) {
            addCriterion("randomized_time not between", value1, value2, "randomizedTime");
            return (Criteria) this;
        }

        public Criteria andResearchStatusIsNull() {
            addCriterion("research_status is null");
            return (Criteria) this;
        }

        public Criteria andResearchStatusIsNotNull() {
            addCriterion("research_status is not null");
            return (Criteria) this;
        }

        public Criteria andResearchStatusEqualTo(String value) {
            addCriterion("research_status =", value, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andResearchStatusNotEqualTo(String value) {
            addCriterion("research_status <>", value, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andResearchStatusGreaterThan(String value) {
            addCriterion("research_status >", value, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andResearchStatusGreaterThanOrEqualTo(String value) {
            addCriterion("research_status >=", value, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andResearchStatusLessThan(String value) {
            addCriterion("research_status <", value, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andResearchStatusLessThanOrEqualTo(String value) {
            addCriterion("research_status <=", value, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andResearchStatusLike(String value) {
            addCriterion("research_status like", value, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andResearchStatusNotLike(String value) {
            addCriterion("research_status not like", value, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andResearchStatusIn(List<String> values) {
            addCriterion("research_status in", values, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andResearchStatusNotIn(List<String> values) {
            addCriterion("research_status not in", values, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andResearchStatusBetween(String value1, String value2) {
            addCriterion("research_status between", value1, value2, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andResearchStatusNotBetween(String value1, String value2) {
            addCriterion("research_status not between", value1, value2, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andExpandIsNull() {
            addCriterion("expand is null");
            return (Criteria) this;
        }

        public Criteria andExpandIsNotNull() {
            addCriterion("expand is not null");
            return (Criteria) this;
        }

        public Criteria andExpandEqualTo(String value) {
            addCriterion("expand =", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotEqualTo(String value) {
            addCriterion("expand <>", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandGreaterThan(String value) {
            addCriterion("expand >", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandGreaterThanOrEqualTo(String value) {
            addCriterion("expand >=", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLessThan(String value) {
            addCriterion("expand <", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLessThanOrEqualTo(String value) {
            addCriterion("expand <=", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLike(String value) {
            addCriterion("expand like", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotLike(String value) {
            addCriterion("expand not like", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandIn(List<String> values) {
            addCriterion("expand in", values, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotIn(List<String> values) {
            addCriterion("expand not in", values, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandBetween(String value1, String value2) {
            addCriterion("expand between", value1, value2, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotBetween(String value1, String value2) {
            addCriterion("expand not between", value1, value2, "expand");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(String value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(String value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(String value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(String value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLike(String value) {
            addCriterion("create_user_id like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotLike(String value) {
            addCriterion("create_user_id not like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<String> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<String> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(String value1, String value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(String value1, String value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNull() {
            addCriterion("update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNotNull() {
            addCriterion("update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdEqualTo(String value) {
            addCriterion("update_user_id =", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotEqualTo(String value) {
            addCriterion("update_user_id <>", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThan(String value) {
            addCriterion("update_user_id >", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("update_user_id >=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThan(String value) {
            addCriterion("update_user_id <", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThanOrEqualTo(String value) {
            addCriterion("update_user_id <=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLike(String value) {
            addCriterion("update_user_id like", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotLike(String value) {
            addCriterion("update_user_id not like", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIn(List<String> values) {
            addCriterion("update_user_id in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotIn(List<String> values) {
            addCriterion("update_user_id not in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdBetween(String value1, String value2) {
            addCriterion("update_user_id between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotBetween(String value1, String value2) {
            addCriterion("update_user_id not between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNull() {
            addCriterion("platform_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNotNull() {
            addCriterion("platform_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdEqualTo(String value) {
            addCriterion("platform_id =", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotEqualTo(String value) {
            addCriterion("platform_id <>", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThan(String value) {
            addCriterion("platform_id >", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThanOrEqualTo(String value) {
            addCriterion("platform_id >=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThan(String value) {
            addCriterion("platform_id <", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThanOrEqualTo(String value) {
            addCriterion("platform_id <=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLike(String value) {
            addCriterion("platform_id like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotLike(String value) {
            addCriterion("platform_id not like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIn(List<String> values) {
            addCriterion("platform_id in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotIn(List<String> values) {
            addCriterion("platform_id not in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdBetween(String value1, String value2) {
            addCriterion("platform_id between", value1, value2, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotBetween(String value1, String value2) {
            addCriterion("platform_id not between", value1, value2, "platformId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}