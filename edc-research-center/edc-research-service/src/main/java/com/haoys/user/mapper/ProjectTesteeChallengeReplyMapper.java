package com.haoys.user.mapper;

import com.haoys.user.domain.vo.project.ProjectChallengeApplyVo;
import com.haoys.user.model.ProjectTesteeChallengeReply;
import com.haoys.user.model.ProjectTesteeChallengeReplyExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectTesteeChallengeReplyMapper {
    long countByExample(ProjectTesteeChallengeReplyExample example);

    int deleteByExample(ProjectTesteeChallengeReplyExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectTesteeChallengeReply record);

    int insertSelective(ProjectTesteeChallengeReply record);

    List<ProjectTesteeChallengeReply> selectByExampleWithBLOBs(ProjectTesteeChallengeReplyExample example);

    List<ProjectTesteeChallengeReply> selectByExample(ProjectTesteeChallengeReplyExample example);

    ProjectTesteeChallengeReply selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectTesteeChallengeReply record, @Param("example") ProjectTesteeChallengeReplyExample example);

    int updateByExampleWithBLOBs(@Param("record") ProjectTesteeChallengeReply record, @Param("example") ProjectTesteeChallengeReplyExample example);

    int updateByExample(@Param("record") ProjectTesteeChallengeReply record, @Param("example") ProjectTesteeChallengeReplyExample example);

    int updateByPrimaryKeySelective(ProjectTesteeChallengeReply record);

    int updateByPrimaryKeyWithBLOBs(ProjectTesteeChallengeReply record);

    int updateByPrimaryKey(ProjectTesteeChallengeReply record);

    /**
     * 获取手动质疑的恢复信息列表
     * @param challengeId  质疑ID
     * @return
     */
    List<ProjectChallengeApplyVo> getApplyListByChallengeId(Long challengeId);
}
