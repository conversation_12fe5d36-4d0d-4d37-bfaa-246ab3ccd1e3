package com.haoys.user.mapper;

import com.haoys.user.domain.param.flow.FlowPlanParam;
import com.haoys.user.model.FlowPlan;
import com.haoys.user.model.FlowPlanExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface FlowPlanMapper {
    long countByExample(FlowPlanExample example);

    int deleteByExample(FlowPlanExample example);

    int deleteByPrimaryKey(Long id);

    int insert(FlowPlan record);

    int insertSelective(FlowPlan record);

    List<FlowPlan> selectByExample(FlowPlanExample example);

    FlowPlan selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") FlowPlan record, @Param("example") FlowPlanExample example);

    int updateByExample(@Param("record") FlowPlan record, @Param("example") FlowPlanExample example);

    int updateByPrimaryKeySelective(FlowPlan record);

    int updateByPrimaryKey(FlowPlan record);

    /**
     * 查询已经发布的流程的数量
     * @return
     */
    int countPublish(Long projectId);

    /**
     * 获取流程集合
     * @param flowPlanParam
     * @return
     */
    List<FlowPlan> list(FlowPlanParam flowPlanParam);

}
