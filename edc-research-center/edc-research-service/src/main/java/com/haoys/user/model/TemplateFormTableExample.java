package com.haoys.user.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TemplateFormTableExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TemplateFormTableExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNull() {
            addCriterion("template_id is null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNotNull() {
            addCriterion("template_id is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdEqualTo(Long value) {
            addCriterion("template_id =", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotEqualTo(Long value) {
            addCriterion("template_id <>", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThan(Long value) {
            addCriterion("template_id >", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThanOrEqualTo(Long value) {
            addCriterion("template_id >=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThan(Long value) {
            addCriterion("template_id <", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThanOrEqualTo(Long value) {
            addCriterion("template_id <=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIn(List<Long> values) {
            addCriterion("template_id in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotIn(List<Long> values) {
            addCriterion("template_id not in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdBetween(Long value1, Long value2) {
            addCriterion("template_id between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotBetween(Long value1, Long value2) {
            addCriterion("template_id not between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNull() {
            addCriterion("form_id is null");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNotNull() {
            addCriterion("form_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormIdEqualTo(Long value) {
            addCriterion("form_id =", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotEqualTo(Long value) {
            addCriterion("form_id <>", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThan(Long value) {
            addCriterion("form_id >", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_id >=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThan(Long value) {
            addCriterion("form_id <", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThanOrEqualTo(Long value) {
            addCriterion("form_id <=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdIn(List<Long> values) {
            addCriterion("form_id in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotIn(List<Long> values) {
            addCriterion("form_id not in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdBetween(Long value1, Long value2) {
            addCriterion("form_id between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotBetween(Long value1, Long value2) {
            addCriterion("form_id not between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdIsNull() {
            addCriterion("form_detail_id is null");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdIsNotNull() {
            addCriterion("form_detail_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdEqualTo(Long value) {
            addCriterion("form_detail_id =", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdNotEqualTo(Long value) {
            addCriterion("form_detail_id <>", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdGreaterThan(Long value) {
            addCriterion("form_detail_id >", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_detail_id >=", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdLessThan(Long value) {
            addCriterion("form_detail_id <", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdLessThanOrEqualTo(Long value) {
            addCriterion("form_detail_id <=", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdIn(List<Long> values) {
            addCriterion("form_detail_id in", values, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdNotIn(List<Long> values) {
            addCriterion("form_detail_id not in", values, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdBetween(Long value1, Long value2) {
            addCriterion("form_detail_id between", value1, value2, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdNotBetween(Long value1, Long value2) {
            addCriterion("form_detail_id not between", value1, value2, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(String value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(String value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(String value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(String value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(String value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(String value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLike(String value) {
            addCriterion("type like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotLike(String value) {
            addCriterion("type not like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<String> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<String> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(String value1, String value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(String value1, String value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andLabelIsNull() {
            addCriterion("label is null");
            return (Criteria) this;
        }

        public Criteria andLabelIsNotNull() {
            addCriterion("label is not null");
            return (Criteria) this;
        }

        public Criteria andLabelEqualTo(String value) {
            addCriterion("label =", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotEqualTo(String value) {
            addCriterion("label <>", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelGreaterThan(String value) {
            addCriterion("label >", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelGreaterThanOrEqualTo(String value) {
            addCriterion("label >=", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelLessThan(String value) {
            addCriterion("label <", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelLessThanOrEqualTo(String value) {
            addCriterion("label <=", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelLike(String value) {
            addCriterion("label like", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotLike(String value) {
            addCriterion("label not like", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelIn(List<String> values) {
            addCriterion("label in", values, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotIn(List<String> values) {
            addCriterion("label not in", values, "label");
            return (Criteria) this;
        }

        public Criteria andLabelBetween(String value1, String value2) {
            addCriterion("label between", value1, value2, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotBetween(String value1, String value2) {
            addCriterion("label not between", value1, value2, "label");
            return (Criteria) this;
        }

        public Criteria andFieldNameIsNull() {
            addCriterion("field_name is null");
            return (Criteria) this;
        }

        public Criteria andFieldNameIsNotNull() {
            addCriterion("field_name is not null");
            return (Criteria) this;
        }

        public Criteria andFieldNameEqualTo(String value) {
            addCriterion("field_name =", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameNotEqualTo(String value) {
            addCriterion("field_name <>", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameGreaterThan(String value) {
            addCriterion("field_name >", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameGreaterThanOrEqualTo(String value) {
            addCriterion("field_name >=", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameLessThan(String value) {
            addCriterion("field_name <", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameLessThanOrEqualTo(String value) {
            addCriterion("field_name <=", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameLike(String value) {
            addCriterion("field_name like", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameNotLike(String value) {
            addCriterion("field_name not like", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameIn(List<String> values) {
            addCriterion("field_name in", values, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameNotIn(List<String> values) {
            addCriterion("field_name not in", values, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameBetween(String value1, String value2) {
            addCriterion("field_name between", value1, value2, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameNotBetween(String value1, String value2) {
            addCriterion("field_name not between", value1, value2, "fieldName");
            return (Criteria) this;
        }

        public Criteria andLangValueIsNull() {
            addCriterion("lang_value is null");
            return (Criteria) this;
        }

        public Criteria andLangValueIsNotNull() {
            addCriterion("lang_value is not null");
            return (Criteria) this;
        }

        public Criteria andLangValueEqualTo(String value) {
            addCriterion("lang_value =", value, "langValue");
            return (Criteria) this;
        }

        public Criteria andLangValueNotEqualTo(String value) {
            addCriterion("lang_value <>", value, "langValue");
            return (Criteria) this;
        }

        public Criteria andLangValueGreaterThan(String value) {
            addCriterion("lang_value >", value, "langValue");
            return (Criteria) this;
        }

        public Criteria andLangValueGreaterThanOrEqualTo(String value) {
            addCriterion("lang_value >=", value, "langValue");
            return (Criteria) this;
        }

        public Criteria andLangValueLessThan(String value) {
            addCriterion("lang_value <", value, "langValue");
            return (Criteria) this;
        }

        public Criteria andLangValueLessThanOrEqualTo(String value) {
            addCriterion("lang_value <=", value, "langValue");
            return (Criteria) this;
        }

        public Criteria andLangValueLike(String value) {
            addCriterion("lang_value like", value, "langValue");
            return (Criteria) this;
        }

        public Criteria andLangValueNotLike(String value) {
            addCriterion("lang_value not like", value, "langValue");
            return (Criteria) this;
        }

        public Criteria andLangValueIn(List<String> values) {
            addCriterion("lang_value in", values, "langValue");
            return (Criteria) this;
        }

        public Criteria andLangValueNotIn(List<String> values) {
            addCriterion("lang_value not in", values, "langValue");
            return (Criteria) this;
        }

        public Criteria andLangValueBetween(String value1, String value2) {
            addCriterion("lang_value between", value1, value2, "langValue");
            return (Criteria) this;
        }

        public Criteria andLangValueNotBetween(String value1, String value2) {
            addCriterion("lang_value not between", value1, value2, "langValue");
            return (Criteria) this;
        }

        public Criteria andTitleIsNull() {
            addCriterion("title is null");
            return (Criteria) this;
        }

        public Criteria andTitleIsNotNull() {
            addCriterion("title is not null");
            return (Criteria) this;
        }

        public Criteria andTitleEqualTo(String value) {
            addCriterion("title =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotEqualTo(String value) {
            addCriterion("title <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThan(String value) {
            addCriterion("title >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThanOrEqualTo(String value) {
            addCriterion("title >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThan(String value) {
            addCriterion("title <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThanOrEqualTo(String value) {
            addCriterion("title <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotLike(String value) {
            addCriterion("title not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleIn(List<String> values) {
            addCriterion("title in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotIn(List<String> values) {
            addCriterion("title not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleBetween(String value1, String value2) {
            addCriterion("title between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotBetween(String value1, String value2) {
            addCriterion("title not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andContentIsNull() {
            addCriterion("content is null");
            return (Criteria) this;
        }

        public Criteria andContentIsNotNull() {
            addCriterion("content is not null");
            return (Criteria) this;
        }

        public Criteria andContentEqualTo(String value) {
            addCriterion("content =", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotEqualTo(String value) {
            addCriterion("content <>", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentGreaterThan(String value) {
            addCriterion("content >", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentGreaterThanOrEqualTo(String value) {
            addCriterion("content >=", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLessThan(String value) {
            addCriterion("content <", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLessThanOrEqualTo(String value) {
            addCriterion("content <=", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLike(String value) {
            addCriterion("content like", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotLike(String value) {
            addCriterion("content not like", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentIn(List<String> values) {
            addCriterion("content in", values, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotIn(List<String> values) {
            addCriterion("content not in", values, "content");
            return (Criteria) this;
        }

        public Criteria andContentBetween(String value1, String value2) {
            addCriterion("content between", value1, value2, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotBetween(String value1, String value2) {
            addCriterion("content not between", value1, value2, "content");
            return (Criteria) this;
        }

        public Criteria andPlaceholderIsNull() {
            addCriterion("placeholder is null");
            return (Criteria) this;
        }

        public Criteria andPlaceholderIsNotNull() {
            addCriterion("placeholder is not null");
            return (Criteria) this;
        }

        public Criteria andPlaceholderEqualTo(String value) {
            addCriterion("placeholder =", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderNotEqualTo(String value) {
            addCriterion("placeholder <>", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderGreaterThan(String value) {
            addCriterion("placeholder >", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderGreaterThanOrEqualTo(String value) {
            addCriterion("placeholder >=", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderLessThan(String value) {
            addCriterion("placeholder <", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderLessThanOrEqualTo(String value) {
            addCriterion("placeholder <=", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderLike(String value) {
            addCriterion("placeholder like", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderNotLike(String value) {
            addCriterion("placeholder not like", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderIn(List<String> values) {
            addCriterion("placeholder in", values, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderNotIn(List<String> values) {
            addCriterion("placeholder not in", values, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderBetween(String value1, String value2) {
            addCriterion("placeholder between", value1, value2, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderNotBetween(String value1, String value2) {
            addCriterion("placeholder not between", value1, value2, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPanelSizeIsNull() {
            addCriterion("panel_size is null");
            return (Criteria) this;
        }

        public Criteria andPanelSizeIsNotNull() {
            addCriterion("panel_size is not null");
            return (Criteria) this;
        }

        public Criteria andPanelSizeEqualTo(String value) {
            addCriterion("panel_size =", value, "panelSize");
            return (Criteria) this;
        }

        public Criteria andPanelSizeNotEqualTo(String value) {
            addCriterion("panel_size <>", value, "panelSize");
            return (Criteria) this;
        }

        public Criteria andPanelSizeGreaterThan(String value) {
            addCriterion("panel_size >", value, "panelSize");
            return (Criteria) this;
        }

        public Criteria andPanelSizeGreaterThanOrEqualTo(String value) {
            addCriterion("panel_size >=", value, "panelSize");
            return (Criteria) this;
        }

        public Criteria andPanelSizeLessThan(String value) {
            addCriterion("panel_size <", value, "panelSize");
            return (Criteria) this;
        }

        public Criteria andPanelSizeLessThanOrEqualTo(String value) {
            addCriterion("panel_size <=", value, "panelSize");
            return (Criteria) this;
        }

        public Criteria andPanelSizeLike(String value) {
            addCriterion("panel_size like", value, "panelSize");
            return (Criteria) this;
        }

        public Criteria andPanelSizeNotLike(String value) {
            addCriterion("panel_size not like", value, "panelSize");
            return (Criteria) this;
        }

        public Criteria andPanelSizeIn(List<String> values) {
            addCriterion("panel_size in", values, "panelSize");
            return (Criteria) this;
        }

        public Criteria andPanelSizeNotIn(List<String> values) {
            addCriterion("panel_size not in", values, "panelSize");
            return (Criteria) this;
        }

        public Criteria andPanelSizeBetween(String value1, String value2) {
            addCriterion("panel_size between", value1, value2, "panelSize");
            return (Criteria) this;
        }

        public Criteria andPanelSizeNotBetween(String value1, String value2) {
            addCriterion("panel_size not between", value1, value2, "panelSize");
            return (Criteria) this;
        }

        public Criteria andHiddenIsNull() {
            addCriterion("hidden is null");
            return (Criteria) this;
        }

        public Criteria andHiddenIsNotNull() {
            addCriterion("hidden is not null");
            return (Criteria) this;
        }

        public Criteria andHiddenEqualTo(Boolean value) {
            addCriterion("hidden =", value, "hidden");
            return (Criteria) this;
        }

        public Criteria andHiddenNotEqualTo(Boolean value) {
            addCriterion("hidden <>", value, "hidden");
            return (Criteria) this;
        }

        public Criteria andHiddenGreaterThan(Boolean value) {
            addCriterion("hidden >", value, "hidden");
            return (Criteria) this;
        }

        public Criteria andHiddenGreaterThanOrEqualTo(Boolean value) {
            addCriterion("hidden >=", value, "hidden");
            return (Criteria) this;
        }

        public Criteria andHiddenLessThan(Boolean value) {
            addCriterion("hidden <", value, "hidden");
            return (Criteria) this;
        }

        public Criteria andHiddenLessThanOrEqualTo(Boolean value) {
            addCriterion("hidden <=", value, "hidden");
            return (Criteria) this;
        }

        public Criteria andHiddenIn(List<Boolean> values) {
            addCriterion("hidden in", values, "hidden");
            return (Criteria) this;
        }

        public Criteria andHiddenNotIn(List<Boolean> values) {
            addCriterion("hidden not in", values, "hidden");
            return (Criteria) this;
        }

        public Criteria andHiddenBetween(Boolean value1, Boolean value2) {
            addCriterion("hidden between", value1, value2, "hidden");
            return (Criteria) this;
        }

        public Criteria andHiddenNotBetween(Boolean value1, Boolean value2) {
            addCriterion("hidden not between", value1, value2, "hidden");
            return (Criteria) this;
        }

        public Criteria andRequireTypeIsNull() {
            addCriterion("require_type is null");
            return (Criteria) this;
        }

        public Criteria andRequireTypeIsNotNull() {
            addCriterion("require_type is not null");
            return (Criteria) this;
        }

        public Criteria andRequireTypeEqualTo(String value) {
            addCriterion("require_type =", value, "requireType");
            return (Criteria) this;
        }

        public Criteria andRequireTypeNotEqualTo(String value) {
            addCriterion("require_type <>", value, "requireType");
            return (Criteria) this;
        }

        public Criteria andRequireTypeGreaterThan(String value) {
            addCriterion("require_type >", value, "requireType");
            return (Criteria) this;
        }

        public Criteria andRequireTypeGreaterThanOrEqualTo(String value) {
            addCriterion("require_type >=", value, "requireType");
            return (Criteria) this;
        }

        public Criteria andRequireTypeLessThan(String value) {
            addCriterion("require_type <", value, "requireType");
            return (Criteria) this;
        }

        public Criteria andRequireTypeLessThanOrEqualTo(String value) {
            addCriterion("require_type <=", value, "requireType");
            return (Criteria) this;
        }

        public Criteria andRequireTypeLike(String value) {
            addCriterion("require_type like", value, "requireType");
            return (Criteria) this;
        }

        public Criteria andRequireTypeNotLike(String value) {
            addCriterion("require_type not like", value, "requireType");
            return (Criteria) this;
        }

        public Criteria andRequireTypeIn(List<String> values) {
            addCriterion("require_type in", values, "requireType");
            return (Criteria) this;
        }

        public Criteria andRequireTypeNotIn(List<String> values) {
            addCriterion("require_type not in", values, "requireType");
            return (Criteria) this;
        }

        public Criteria andRequireTypeBetween(String value1, String value2) {
            addCriterion("require_type between", value1, value2, "requireType");
            return (Criteria) this;
        }

        public Criteria andRequireTypeNotBetween(String value1, String value2) {
            addCriterion("require_type not between", value1, value2, "requireType");
            return (Criteria) this;
        }

        public Criteria andRequiredIsNull() {
            addCriterion("required is null");
            return (Criteria) this;
        }

        public Criteria andRequiredIsNotNull() {
            addCriterion("required is not null");
            return (Criteria) this;
        }

        public Criteria andRequiredEqualTo(Boolean value) {
            addCriterion("required =", value, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredNotEqualTo(Boolean value) {
            addCriterion("required <>", value, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredGreaterThan(Boolean value) {
            addCriterion("required >", value, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredGreaterThanOrEqualTo(Boolean value) {
            addCriterion("required >=", value, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredLessThan(Boolean value) {
            addCriterion("required <", value, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredLessThanOrEqualTo(Boolean value) {
            addCriterion("required <=", value, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredIn(List<Boolean> values) {
            addCriterion("required in", values, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredNotIn(List<Boolean> values) {
            addCriterion("required not in", values, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredBetween(Boolean value1, Boolean value2) {
            addCriterion("required between", value1, value2, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredNotBetween(Boolean value1, Boolean value2) {
            addCriterion("required not between", value1, value2, "required");
            return (Criteria) this;
        }

        public Criteria andShowTitleIsNull() {
            addCriterion("show_title is null");
            return (Criteria) this;
        }

        public Criteria andShowTitleIsNotNull() {
            addCriterion("show_title is not null");
            return (Criteria) this;
        }

        public Criteria andShowTitleEqualTo(Boolean value) {
            addCriterion("show_title =", value, "showTitle");
            return (Criteria) this;
        }

        public Criteria andShowTitleNotEqualTo(Boolean value) {
            addCriterion("show_title <>", value, "showTitle");
            return (Criteria) this;
        }

        public Criteria andShowTitleGreaterThan(Boolean value) {
            addCriterion("show_title >", value, "showTitle");
            return (Criteria) this;
        }

        public Criteria andShowTitleGreaterThanOrEqualTo(Boolean value) {
            addCriterion("show_title >=", value, "showTitle");
            return (Criteria) this;
        }

        public Criteria andShowTitleLessThan(Boolean value) {
            addCriterion("show_title <", value, "showTitle");
            return (Criteria) this;
        }

        public Criteria andShowTitleLessThanOrEqualTo(Boolean value) {
            addCriterion("show_title <=", value, "showTitle");
            return (Criteria) this;
        }

        public Criteria andShowTitleIn(List<Boolean> values) {
            addCriterion("show_title in", values, "showTitle");
            return (Criteria) this;
        }

        public Criteria andShowTitleNotIn(List<Boolean> values) {
            addCriterion("show_title not in", values, "showTitle");
            return (Criteria) this;
        }

        public Criteria andShowTitleBetween(Boolean value1, Boolean value2) {
            addCriterion("show_title between", value1, value2, "showTitle");
            return (Criteria) this;
        }

        public Criteria andShowTitleNotBetween(Boolean value1, Boolean value2) {
            addCriterion("show_title not between", value1, value2, "showTitle");
            return (Criteria) this;
        }

        public Criteria andShowContentIsNull() {
            addCriterion("show_content is null");
            return (Criteria) this;
        }

        public Criteria andShowContentIsNotNull() {
            addCriterion("show_content is not null");
            return (Criteria) this;
        }

        public Criteria andShowContentEqualTo(Boolean value) {
            addCriterion("show_content =", value, "showContent");
            return (Criteria) this;
        }

        public Criteria andShowContentNotEqualTo(Boolean value) {
            addCriterion("show_content <>", value, "showContent");
            return (Criteria) this;
        }

        public Criteria andShowContentGreaterThan(Boolean value) {
            addCriterion("show_content >", value, "showContent");
            return (Criteria) this;
        }

        public Criteria andShowContentGreaterThanOrEqualTo(Boolean value) {
            addCriterion("show_content >=", value, "showContent");
            return (Criteria) this;
        }

        public Criteria andShowContentLessThan(Boolean value) {
            addCriterion("show_content <", value, "showContent");
            return (Criteria) this;
        }

        public Criteria andShowContentLessThanOrEqualTo(Boolean value) {
            addCriterion("show_content <=", value, "showContent");
            return (Criteria) this;
        }

        public Criteria andShowContentIn(List<Boolean> values) {
            addCriterion("show_content in", values, "showContent");
            return (Criteria) this;
        }

        public Criteria andShowContentNotIn(List<Boolean> values) {
            addCriterion("show_content not in", values, "showContent");
            return (Criteria) this;
        }

        public Criteria andShowContentBetween(Boolean value1, Boolean value2) {
            addCriterion("show_content between", value1, value2, "showContent");
            return (Criteria) this;
        }

        public Criteria andShowContentNotBetween(Boolean value1, Boolean value2) {
            addCriterion("show_content not between", value1, value2, "showContent");
            return (Criteria) this;
        }

        public Criteria andDefaultValueIsNull() {
            addCriterion("default_value is null");
            return (Criteria) this;
        }

        public Criteria andDefaultValueIsNotNull() {
            addCriterion("default_value is not null");
            return (Criteria) this;
        }

        public Criteria andDefaultValueEqualTo(String value) {
            addCriterion("default_value =", value, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andDefaultValueNotEqualTo(String value) {
            addCriterion("default_value <>", value, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andDefaultValueGreaterThan(String value) {
            addCriterion("default_value >", value, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andDefaultValueGreaterThanOrEqualTo(String value) {
            addCriterion("default_value >=", value, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andDefaultValueLessThan(String value) {
            addCriterion("default_value <", value, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andDefaultValueLessThanOrEqualTo(String value) {
            addCriterion("default_value <=", value, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andDefaultValueLike(String value) {
            addCriterion("default_value like", value, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andDefaultValueNotLike(String value) {
            addCriterion("default_value not like", value, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andDefaultValueIn(List<String> values) {
            addCriterion("default_value in", values, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andDefaultValueNotIn(List<String> values) {
            addCriterion("default_value not in", values, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andDefaultValueBetween(String value1, String value2) {
            addCriterion("default_value between", value1, value2, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andDefaultValueNotBetween(String value1, String value2) {
            addCriterion("default_value not between", value1, value2, "defaultValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueIsNull() {
            addCriterion("unit_value is null");
            return (Criteria) this;
        }

        public Criteria andUnitValueIsNotNull() {
            addCriterion("unit_value is not null");
            return (Criteria) this;
        }

        public Criteria andUnitValueEqualTo(String value) {
            addCriterion("unit_value =", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueNotEqualTo(String value) {
            addCriterion("unit_value <>", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueGreaterThan(String value) {
            addCriterion("unit_value >", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueGreaterThanOrEqualTo(String value) {
            addCriterion("unit_value >=", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueLessThan(String value) {
            addCriterion("unit_value <", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueLessThanOrEqualTo(String value) {
            addCriterion("unit_value <=", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueLike(String value) {
            addCriterion("unit_value like", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueNotLike(String value) {
            addCriterion("unit_value not like", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueIn(List<String> values) {
            addCriterion("unit_value in", values, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueNotIn(List<String> values) {
            addCriterion("unit_value not in", values, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueBetween(String value1, String value2) {
            addCriterion("unit_value between", value1, value2, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueNotBetween(String value1, String value2) {
            addCriterion("unit_value not between", value1, value2, "unitValue");
            return (Criteria) this;
        }

        public Criteria andDicResourceIsNull() {
            addCriterion("dic_resource is null");
            return (Criteria) this;
        }

        public Criteria andDicResourceIsNotNull() {
            addCriterion("dic_resource is not null");
            return (Criteria) this;
        }

        public Criteria andDicResourceEqualTo(String value) {
            addCriterion("dic_resource =", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceNotEqualTo(String value) {
            addCriterion("dic_resource <>", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceGreaterThan(String value) {
            addCriterion("dic_resource >", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceGreaterThanOrEqualTo(String value) {
            addCriterion("dic_resource >=", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceLessThan(String value) {
            addCriterion("dic_resource <", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceLessThanOrEqualTo(String value) {
            addCriterion("dic_resource <=", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceLike(String value) {
            addCriterion("dic_resource like", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceNotLike(String value) {
            addCriterion("dic_resource not like", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceIn(List<String> values) {
            addCriterion("dic_resource in", values, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceNotIn(List<String> values) {
            addCriterion("dic_resource not in", values, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceBetween(String value1, String value2) {
            addCriterion("dic_resource between", value1, value2, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceNotBetween(String value1, String value2) {
            addCriterion("dic_resource not between", value1, value2, "dicResource");
            return (Criteria) this;
        }

        public Criteria andRefDicIdIsNull() {
            addCriterion("ref_dic_id is null");
            return (Criteria) this;
        }

        public Criteria andRefDicIdIsNotNull() {
            addCriterion("ref_dic_id is not null");
            return (Criteria) this;
        }

        public Criteria andRefDicIdEqualTo(String value) {
            addCriterion("ref_dic_id =", value, "refDicId");
            return (Criteria) this;
        }

        public Criteria andRefDicIdNotEqualTo(String value) {
            addCriterion("ref_dic_id <>", value, "refDicId");
            return (Criteria) this;
        }

        public Criteria andRefDicIdGreaterThan(String value) {
            addCriterion("ref_dic_id >", value, "refDicId");
            return (Criteria) this;
        }

        public Criteria andRefDicIdGreaterThanOrEqualTo(String value) {
            addCriterion("ref_dic_id >=", value, "refDicId");
            return (Criteria) this;
        }

        public Criteria andRefDicIdLessThan(String value) {
            addCriterion("ref_dic_id <", value, "refDicId");
            return (Criteria) this;
        }

        public Criteria andRefDicIdLessThanOrEqualTo(String value) {
            addCriterion("ref_dic_id <=", value, "refDicId");
            return (Criteria) this;
        }

        public Criteria andRefDicIdLike(String value) {
            addCriterion("ref_dic_id like", value, "refDicId");
            return (Criteria) this;
        }

        public Criteria andRefDicIdNotLike(String value) {
            addCriterion("ref_dic_id not like", value, "refDicId");
            return (Criteria) this;
        }

        public Criteria andRefDicIdIn(List<String> values) {
            addCriterion("ref_dic_id in", values, "refDicId");
            return (Criteria) this;
        }

        public Criteria andRefDicIdNotIn(List<String> values) {
            addCriterion("ref_dic_id not in", values, "refDicId");
            return (Criteria) this;
        }

        public Criteria andRefDicIdBetween(String value1, String value2) {
            addCriterion("ref_dic_id between", value1, value2, "refDicId");
            return (Criteria) this;
        }

        public Criteria andRefDicIdNotBetween(String value1, String value2) {
            addCriterion("ref_dic_id not between", value1, value2, "refDicId");
            return (Criteria) this;
        }

        public Criteria andDefaultDicValueIsNull() {
            addCriterion("default_dic_value is null");
            return (Criteria) this;
        }

        public Criteria andDefaultDicValueIsNotNull() {
            addCriterion("default_dic_value is not null");
            return (Criteria) this;
        }

        public Criteria andDefaultDicValueEqualTo(String value) {
            addCriterion("default_dic_value =", value, "defaultDicValue");
            return (Criteria) this;
        }

        public Criteria andDefaultDicValueNotEqualTo(String value) {
            addCriterion("default_dic_value <>", value, "defaultDicValue");
            return (Criteria) this;
        }

        public Criteria andDefaultDicValueGreaterThan(String value) {
            addCriterion("default_dic_value >", value, "defaultDicValue");
            return (Criteria) this;
        }

        public Criteria andDefaultDicValueGreaterThanOrEqualTo(String value) {
            addCriterion("default_dic_value >=", value, "defaultDicValue");
            return (Criteria) this;
        }

        public Criteria andDefaultDicValueLessThan(String value) {
            addCriterion("default_dic_value <", value, "defaultDicValue");
            return (Criteria) this;
        }

        public Criteria andDefaultDicValueLessThanOrEqualTo(String value) {
            addCriterion("default_dic_value <=", value, "defaultDicValue");
            return (Criteria) this;
        }

        public Criteria andDefaultDicValueLike(String value) {
            addCriterion("default_dic_value like", value, "defaultDicValue");
            return (Criteria) this;
        }

        public Criteria andDefaultDicValueNotLike(String value) {
            addCriterion("default_dic_value not like", value, "defaultDicValue");
            return (Criteria) this;
        }

        public Criteria andDefaultDicValueIn(List<String> values) {
            addCriterion("default_dic_value in", values, "defaultDicValue");
            return (Criteria) this;
        }

        public Criteria andDefaultDicValueNotIn(List<String> values) {
            addCriterion("default_dic_value not in", values, "defaultDicValue");
            return (Criteria) this;
        }

        public Criteria andDefaultDicValueBetween(String value1, String value2) {
            addCriterion("default_dic_value between", value1, value2, "defaultDicValue");
            return (Criteria) this;
        }

        public Criteria andDefaultDicValueNotBetween(String value1, String value2) {
            addCriterion("default_dic_value not between", value1, value2, "defaultDicValue");
            return (Criteria) this;
        }

        public Criteria andOptionsIsNull() {
            addCriterion("options is null");
            return (Criteria) this;
        }

        public Criteria andOptionsIsNotNull() {
            addCriterion("options is not null");
            return (Criteria) this;
        }

        public Criteria andOptionsEqualTo(String value) {
            addCriterion("options =", value, "options");
            return (Criteria) this;
        }

        public Criteria andOptionsNotEqualTo(String value) {
            addCriterion("options <>", value, "options");
            return (Criteria) this;
        }

        public Criteria andOptionsGreaterThan(String value) {
            addCriterion("options >", value, "options");
            return (Criteria) this;
        }

        public Criteria andOptionsGreaterThanOrEqualTo(String value) {
            addCriterion("options >=", value, "options");
            return (Criteria) this;
        }

        public Criteria andOptionsLessThan(String value) {
            addCriterion("options <", value, "options");
            return (Criteria) this;
        }

        public Criteria andOptionsLessThanOrEqualTo(String value) {
            addCriterion("options <=", value, "options");
            return (Criteria) this;
        }

        public Criteria andOptionsLike(String value) {
            addCriterion("options like", value, "options");
            return (Criteria) this;
        }

        public Criteria andOptionsNotLike(String value) {
            addCriterion("options not like", value, "options");
            return (Criteria) this;
        }

        public Criteria andOptionsIn(List<String> values) {
            addCriterion("options in", values, "options");
            return (Criteria) this;
        }

        public Criteria andOptionsNotIn(List<String> values) {
            addCriterion("options not in", values, "options");
            return (Criteria) this;
        }

        public Criteria andOptionsBetween(String value1, String value2) {
            addCriterion("options between", value1, value2, "options");
            return (Criteria) this;
        }

        public Criteria andOptionsNotBetween(String value1, String value2) {
            addCriterion("options not between", value1, value2, "options");
            return (Criteria) this;
        }

        public Criteria andExpandIsNull() {
            addCriterion("expand is null");
            return (Criteria) this;
        }

        public Criteria andExpandIsNotNull() {
            addCriterion("expand is not null");
            return (Criteria) this;
        }

        public Criteria andExpandEqualTo(String value) {
            addCriterion("expand =", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotEqualTo(String value) {
            addCriterion("expand <>", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandGreaterThan(String value) {
            addCriterion("expand >", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandGreaterThanOrEqualTo(String value) {
            addCriterion("expand >=", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLessThan(String value) {
            addCriterion("expand <", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLessThanOrEqualTo(String value) {
            addCriterion("expand <=", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLike(String value) {
            addCriterion("expand like", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotLike(String value) {
            addCriterion("expand not like", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandIn(List<String> values) {
            addCriterion("expand in", values, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotIn(List<String> values) {
            addCriterion("expand not in", values, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandBetween(String value1, String value2) {
            addCriterion("expand between", value1, value2, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotBetween(String value1, String value2) {
            addCriterion("expand not between", value1, value2, "expand");
            return (Criteria) this;
        }

        public Criteria andCopyVariableIdIsNull() {
            addCriterion("copy_variable_id is null");
            return (Criteria) this;
        }

        public Criteria andCopyVariableIdIsNotNull() {
            addCriterion("copy_variable_id is not null");
            return (Criteria) this;
        }

        public Criteria andCopyVariableIdEqualTo(Long value) {
            addCriterion("copy_variable_id =", value, "copyVariableId");
            return (Criteria) this;
        }

        public Criteria andCopyVariableIdNotEqualTo(Long value) {
            addCriterion("copy_variable_id <>", value, "copyVariableId");
            return (Criteria) this;
        }

        public Criteria andCopyVariableIdGreaterThan(Long value) {
            addCriterion("copy_variable_id >", value, "copyVariableId");
            return (Criteria) this;
        }

        public Criteria andCopyVariableIdGreaterThanOrEqualTo(Long value) {
            addCriterion("copy_variable_id >=", value, "copyVariableId");
            return (Criteria) this;
        }

        public Criteria andCopyVariableIdLessThan(Long value) {
            addCriterion("copy_variable_id <", value, "copyVariableId");
            return (Criteria) this;
        }

        public Criteria andCopyVariableIdLessThanOrEqualTo(Long value) {
            addCriterion("copy_variable_id <=", value, "copyVariableId");
            return (Criteria) this;
        }

        public Criteria andCopyVariableIdIn(List<Long> values) {
            addCriterion("copy_variable_id in", values, "copyVariableId");
            return (Criteria) this;
        }

        public Criteria andCopyVariableIdNotIn(List<Long> values) {
            addCriterion("copy_variable_id not in", values, "copyVariableId");
            return (Criteria) this;
        }

        public Criteria andCopyVariableIdBetween(Long value1, Long value2) {
            addCriterion("copy_variable_id between", value1, value2, "copyVariableId");
            return (Criteria) this;
        }

        public Criteria andCopyVariableIdNotBetween(Long value1, Long value2) {
            addCriterion("copy_variable_id not between", value1, value2, "copyVariableId");
            return (Criteria) this;
        }

        public Criteria andCopyTableIdIsNull() {
            addCriterion("copy_table_id is null");
            return (Criteria) this;
        }

        public Criteria andCopyTableIdIsNotNull() {
            addCriterion("copy_table_id is not null");
            return (Criteria) this;
        }

        public Criteria andCopyTableIdEqualTo(Long value) {
            addCriterion("copy_table_id =", value, "copyTableId");
            return (Criteria) this;
        }

        public Criteria andCopyTableIdNotEqualTo(Long value) {
            addCriterion("copy_table_id <>", value, "copyTableId");
            return (Criteria) this;
        }

        public Criteria andCopyTableIdGreaterThan(Long value) {
            addCriterion("copy_table_id >", value, "copyTableId");
            return (Criteria) this;
        }

        public Criteria andCopyTableIdGreaterThanOrEqualTo(Long value) {
            addCriterion("copy_table_id >=", value, "copyTableId");
            return (Criteria) this;
        }

        public Criteria andCopyTableIdLessThan(Long value) {
            addCriterion("copy_table_id <", value, "copyTableId");
            return (Criteria) this;
        }

        public Criteria andCopyTableIdLessThanOrEqualTo(Long value) {
            addCriterion("copy_table_id <=", value, "copyTableId");
            return (Criteria) this;
        }

        public Criteria andCopyTableIdIn(List<Long> values) {
            addCriterion("copy_table_id in", values, "copyTableId");
            return (Criteria) this;
        }

        public Criteria andCopyTableIdNotIn(List<Long> values) {
            addCriterion("copy_table_id not in", values, "copyTableId");
            return (Criteria) this;
        }

        public Criteria andCopyTableIdBetween(Long value1, Long value2) {
            addCriterion("copy_table_id between", value1, value2, "copyTableId");
            return (Criteria) this;
        }

        public Criteria andCopyTableIdNotBetween(Long value1, Long value2) {
            addCriterion("copy_table_id not between", value1, value2, "copyTableId");
            return (Criteria) this;
        }

        public Criteria andExtData1IsNull() {
            addCriterion("ext_data_1 is null");
            return (Criteria) this;
        }

        public Criteria andExtData1IsNotNull() {
            addCriterion("ext_data_1 is not null");
            return (Criteria) this;
        }

        public Criteria andExtData1EqualTo(String value) {
            addCriterion("ext_data_1 =", value, "extData1");
            return (Criteria) this;
        }

        public Criteria andExtData1NotEqualTo(String value) {
            addCriterion("ext_data_1 <>", value, "extData1");
            return (Criteria) this;
        }

        public Criteria andExtData1GreaterThan(String value) {
            addCriterion("ext_data_1 >", value, "extData1");
            return (Criteria) this;
        }

        public Criteria andExtData1GreaterThanOrEqualTo(String value) {
            addCriterion("ext_data_1 >=", value, "extData1");
            return (Criteria) this;
        }

        public Criteria andExtData1LessThan(String value) {
            addCriterion("ext_data_1 <", value, "extData1");
            return (Criteria) this;
        }

        public Criteria andExtData1LessThanOrEqualTo(String value) {
            addCriterion("ext_data_1 <=", value, "extData1");
            return (Criteria) this;
        }

        public Criteria andExtData1Like(String value) {
            addCriterion("ext_data_1 like", value, "extData1");
            return (Criteria) this;
        }

        public Criteria andExtData1NotLike(String value) {
            addCriterion("ext_data_1 not like", value, "extData1");
            return (Criteria) this;
        }

        public Criteria andExtData1In(List<String> values) {
            addCriterion("ext_data_1 in", values, "extData1");
            return (Criteria) this;
        }

        public Criteria andExtData1NotIn(List<String> values) {
            addCriterion("ext_data_1 not in", values, "extData1");
            return (Criteria) this;
        }

        public Criteria andExtData1Between(String value1, String value2) {
            addCriterion("ext_data_1 between", value1, value2, "extData1");
            return (Criteria) this;
        }

        public Criteria andExtData1NotBetween(String value1, String value2) {
            addCriterion("ext_data_1 not between", value1, value2, "extData1");
            return (Criteria) this;
        }

        public Criteria andExtData2IsNull() {
            addCriterion("ext_data_2 is null");
            return (Criteria) this;
        }

        public Criteria andExtData2IsNotNull() {
            addCriterion("ext_data_2 is not null");
            return (Criteria) this;
        }

        public Criteria andExtData2EqualTo(String value) {
            addCriterion("ext_data_2 =", value, "extData2");
            return (Criteria) this;
        }

        public Criteria andExtData2NotEqualTo(String value) {
            addCriterion("ext_data_2 <>", value, "extData2");
            return (Criteria) this;
        }

        public Criteria andExtData2GreaterThan(String value) {
            addCriterion("ext_data_2 >", value, "extData2");
            return (Criteria) this;
        }

        public Criteria andExtData2GreaterThanOrEqualTo(String value) {
            addCriterion("ext_data_2 >=", value, "extData2");
            return (Criteria) this;
        }

        public Criteria andExtData2LessThan(String value) {
            addCriterion("ext_data_2 <", value, "extData2");
            return (Criteria) this;
        }

        public Criteria andExtData2LessThanOrEqualTo(String value) {
            addCriterion("ext_data_2 <=", value, "extData2");
            return (Criteria) this;
        }

        public Criteria andExtData2Like(String value) {
            addCriterion("ext_data_2 like", value, "extData2");
            return (Criteria) this;
        }

        public Criteria andExtData2NotLike(String value) {
            addCriterion("ext_data_2 not like", value, "extData2");
            return (Criteria) this;
        }

        public Criteria andExtData2In(List<String> values) {
            addCriterion("ext_data_2 in", values, "extData2");
            return (Criteria) this;
        }

        public Criteria andExtData2NotIn(List<String> values) {
            addCriterion("ext_data_2 not in", values, "extData2");
            return (Criteria) this;
        }

        public Criteria andExtData2Between(String value1, String value2) {
            addCriterion("ext_data_2 between", value1, value2, "extData2");
            return (Criteria) this;
        }

        public Criteria andExtData2NotBetween(String value1, String value2) {
            addCriterion("ext_data_2 not between", value1, value2, "extData2");
            return (Criteria) this;
        }

        public Criteria andExtData3IsNull() {
            addCriterion("ext_data_3 is null");
            return (Criteria) this;
        }

        public Criteria andExtData3IsNotNull() {
            addCriterion("ext_data_3 is not null");
            return (Criteria) this;
        }

        public Criteria andExtData3EqualTo(String value) {
            addCriterion("ext_data_3 =", value, "extData3");
            return (Criteria) this;
        }

        public Criteria andExtData3NotEqualTo(String value) {
            addCriterion("ext_data_3 <>", value, "extData3");
            return (Criteria) this;
        }

        public Criteria andExtData3GreaterThan(String value) {
            addCriterion("ext_data_3 >", value, "extData3");
            return (Criteria) this;
        }

        public Criteria andExtData3GreaterThanOrEqualTo(String value) {
            addCriterion("ext_data_3 >=", value, "extData3");
            return (Criteria) this;
        }

        public Criteria andExtData3LessThan(String value) {
            addCriterion("ext_data_3 <", value, "extData3");
            return (Criteria) this;
        }

        public Criteria andExtData3LessThanOrEqualTo(String value) {
            addCriterion("ext_data_3 <=", value, "extData3");
            return (Criteria) this;
        }

        public Criteria andExtData3Like(String value) {
            addCriterion("ext_data_3 like", value, "extData3");
            return (Criteria) this;
        }

        public Criteria andExtData3NotLike(String value) {
            addCriterion("ext_data_3 not like", value, "extData3");
            return (Criteria) this;
        }

        public Criteria andExtData3In(List<String> values) {
            addCriterion("ext_data_3 in", values, "extData3");
            return (Criteria) this;
        }

        public Criteria andExtData3NotIn(List<String> values) {
            addCriterion("ext_data_3 not in", values, "extData3");
            return (Criteria) this;
        }

        public Criteria andExtData3Between(String value1, String value2) {
            addCriterion("ext_data_3 between", value1, value2, "extData3");
            return (Criteria) this;
        }

        public Criteria andExtData3NotBetween(String value1, String value2) {
            addCriterion("ext_data_3 not between", value1, value2, "extData3");
            return (Criteria) this;
        }

        public Criteria andExtData4IsNull() {
            addCriterion("ext_data_4 is null");
            return (Criteria) this;
        }

        public Criteria andExtData4IsNotNull() {
            addCriterion("ext_data_4 is not null");
            return (Criteria) this;
        }

        public Criteria andExtData4EqualTo(String value) {
            addCriterion("ext_data_4 =", value, "extData4");
            return (Criteria) this;
        }

        public Criteria andExtData4NotEqualTo(String value) {
            addCriterion("ext_data_4 <>", value, "extData4");
            return (Criteria) this;
        }

        public Criteria andExtData4GreaterThan(String value) {
            addCriterion("ext_data_4 >", value, "extData4");
            return (Criteria) this;
        }

        public Criteria andExtData4GreaterThanOrEqualTo(String value) {
            addCriterion("ext_data_4 >=", value, "extData4");
            return (Criteria) this;
        }

        public Criteria andExtData4LessThan(String value) {
            addCriterion("ext_data_4 <", value, "extData4");
            return (Criteria) this;
        }

        public Criteria andExtData4LessThanOrEqualTo(String value) {
            addCriterion("ext_data_4 <=", value, "extData4");
            return (Criteria) this;
        }

        public Criteria andExtData4Like(String value) {
            addCriterion("ext_data_4 like", value, "extData4");
            return (Criteria) this;
        }

        public Criteria andExtData4NotLike(String value) {
            addCriterion("ext_data_4 not like", value, "extData4");
            return (Criteria) this;
        }

        public Criteria andExtData4In(List<String> values) {
            addCriterion("ext_data_4 in", values, "extData4");
            return (Criteria) this;
        }

        public Criteria andExtData4NotIn(List<String> values) {
            addCriterion("ext_data_4 not in", values, "extData4");
            return (Criteria) this;
        }

        public Criteria andExtData4Between(String value1, String value2) {
            addCriterion("ext_data_4 between", value1, value2, "extData4");
            return (Criteria) this;
        }

        public Criteria andExtData4NotBetween(String value1, String value2) {
            addCriterion("ext_data_4 not between", value1, value2, "extData4");
            return (Criteria) this;
        }

        public Criteria andPointVariableIdIsNull() {
            addCriterion("point_variable_id is null");
            return (Criteria) this;
        }

        public Criteria andPointVariableIdIsNotNull() {
            addCriterion("point_variable_id is not null");
            return (Criteria) this;
        }

        public Criteria andPointVariableIdEqualTo(Long value) {
            addCriterion("point_variable_id =", value, "pointVariableId");
            return (Criteria) this;
        }

        public Criteria andPointVariableIdNotEqualTo(Long value) {
            addCriterion("point_variable_id <>", value, "pointVariableId");
            return (Criteria) this;
        }

        public Criteria andPointVariableIdGreaterThan(Long value) {
            addCriterion("point_variable_id >", value, "pointVariableId");
            return (Criteria) this;
        }

        public Criteria andPointVariableIdGreaterThanOrEqualTo(Long value) {
            addCriterion("point_variable_id >=", value, "pointVariableId");
            return (Criteria) this;
        }

        public Criteria andPointVariableIdLessThan(Long value) {
            addCriterion("point_variable_id <", value, "pointVariableId");
            return (Criteria) this;
        }

        public Criteria andPointVariableIdLessThanOrEqualTo(Long value) {
            addCriterion("point_variable_id <=", value, "pointVariableId");
            return (Criteria) this;
        }

        public Criteria andPointVariableIdIn(List<Long> values) {
            addCriterion("point_variable_id in", values, "pointVariableId");
            return (Criteria) this;
        }

        public Criteria andPointVariableIdNotIn(List<Long> values) {
            addCriterion("point_variable_id not in", values, "pointVariableId");
            return (Criteria) this;
        }

        public Criteria andPointVariableIdBetween(Long value1, Long value2) {
            addCriterion("point_variable_id between", value1, value2, "pointVariableId");
            return (Criteria) this;
        }

        public Criteria andPointVariableIdNotBetween(Long value1, Long value2) {
            addCriterion("point_variable_id not between", value1, value2, "pointVariableId");
            return (Criteria) this;
        }

        public Criteria andEnableAssociateIsNull() {
            addCriterion("enable_associate is null");
            return (Criteria) this;
        }

        public Criteria andEnableAssociateIsNotNull() {
            addCriterion("enable_associate is not null");
            return (Criteria) this;
        }

        public Criteria andEnableAssociateEqualTo(Boolean value) {
            addCriterion("enable_associate =", value, "enableAssociate");
            return (Criteria) this;
        }

        public Criteria andEnableAssociateNotEqualTo(Boolean value) {
            addCriterion("enable_associate <>", value, "enableAssociate");
            return (Criteria) this;
        }

        public Criteria andEnableAssociateGreaterThan(Boolean value) {
            addCriterion("enable_associate >", value, "enableAssociate");
            return (Criteria) this;
        }

        public Criteria andEnableAssociateGreaterThanOrEqualTo(Boolean value) {
            addCriterion("enable_associate >=", value, "enableAssociate");
            return (Criteria) this;
        }

        public Criteria andEnableAssociateLessThan(Boolean value) {
            addCriterion("enable_associate <", value, "enableAssociate");
            return (Criteria) this;
        }

        public Criteria andEnableAssociateLessThanOrEqualTo(Boolean value) {
            addCriterion("enable_associate <=", value, "enableAssociate");
            return (Criteria) this;
        }

        public Criteria andEnableAssociateIn(List<Boolean> values) {
            addCriterion("enable_associate in", values, "enableAssociate");
            return (Criteria) this;
        }

        public Criteria andEnableAssociateNotIn(List<Boolean> values) {
            addCriterion("enable_associate not in", values, "enableAssociate");
            return (Criteria) this;
        }

        public Criteria andEnableAssociateBetween(Boolean value1, Boolean value2) {
            addCriterion("enable_associate between", value1, value2, "enableAssociate");
            return (Criteria) this;
        }

        public Criteria andEnableAssociateNotBetween(Boolean value1, Boolean value2) {
            addCriterion("enable_associate not between", value1, value2, "enableAssociate");
            return (Criteria) this;
        }

        public Criteria andConditionExpressionIsNull() {
            addCriterion("condition_expression is null");
            return (Criteria) this;
        }

        public Criteria andConditionExpressionIsNotNull() {
            addCriterion("condition_expression is not null");
            return (Criteria) this;
        }

        public Criteria andConditionExpressionEqualTo(String value) {
            addCriterion("condition_expression =", value, "conditionExpression");
            return (Criteria) this;
        }

        public Criteria andConditionExpressionNotEqualTo(String value) {
            addCriterion("condition_expression <>", value, "conditionExpression");
            return (Criteria) this;
        }

        public Criteria andConditionExpressionGreaterThan(String value) {
            addCriterion("condition_expression >", value, "conditionExpression");
            return (Criteria) this;
        }

        public Criteria andConditionExpressionGreaterThanOrEqualTo(String value) {
            addCriterion("condition_expression >=", value, "conditionExpression");
            return (Criteria) this;
        }

        public Criteria andConditionExpressionLessThan(String value) {
            addCriterion("condition_expression <", value, "conditionExpression");
            return (Criteria) this;
        }

        public Criteria andConditionExpressionLessThanOrEqualTo(String value) {
            addCriterion("condition_expression <=", value, "conditionExpression");
            return (Criteria) this;
        }

        public Criteria andConditionExpressionLike(String value) {
            addCriterion("condition_expression like", value, "conditionExpression");
            return (Criteria) this;
        }

        public Criteria andConditionExpressionNotLike(String value) {
            addCriterion("condition_expression not like", value, "conditionExpression");
            return (Criteria) this;
        }

        public Criteria andConditionExpressionIn(List<String> values) {
            addCriterion("condition_expression in", values, "conditionExpression");
            return (Criteria) this;
        }

        public Criteria andConditionExpressionNotIn(List<String> values) {
            addCriterion("condition_expression not in", values, "conditionExpression");
            return (Criteria) this;
        }

        public Criteria andConditionExpressionBetween(String value1, String value2) {
            addCriterion("condition_expression between", value1, value2, "conditionExpression");
            return (Criteria) this;
        }

        public Criteria andConditionExpressionNotBetween(String value1, String value2) {
            addCriterion("condition_expression not between", value1, value2, "conditionExpression");
            return (Criteria) this;
        }

        public Criteria andEnableViewConfigIsNull() {
            addCriterion("enable_view_config is null");
            return (Criteria) this;
        }

        public Criteria andEnableViewConfigIsNotNull() {
            addCriterion("enable_view_config is not null");
            return (Criteria) this;
        }

        public Criteria andEnableViewConfigEqualTo(Boolean value) {
            addCriterion("enable_view_config =", value, "enableViewConfig");
            return (Criteria) this;
        }

        public Criteria andEnableViewConfigNotEqualTo(Boolean value) {
            addCriterion("enable_view_config <>", value, "enableViewConfig");
            return (Criteria) this;
        }

        public Criteria andEnableViewConfigGreaterThan(Boolean value) {
            addCriterion("enable_view_config >", value, "enableViewConfig");
            return (Criteria) this;
        }

        public Criteria andEnableViewConfigGreaterThanOrEqualTo(Boolean value) {
            addCriterion("enable_view_config >=", value, "enableViewConfig");
            return (Criteria) this;
        }

        public Criteria andEnableViewConfigLessThan(Boolean value) {
            addCriterion("enable_view_config <", value, "enableViewConfig");
            return (Criteria) this;
        }

        public Criteria andEnableViewConfigLessThanOrEqualTo(Boolean value) {
            addCriterion("enable_view_config <=", value, "enableViewConfig");
            return (Criteria) this;
        }

        public Criteria andEnableViewConfigIn(List<Boolean> values) {
            addCriterion("enable_view_config in", values, "enableViewConfig");
            return (Criteria) this;
        }

        public Criteria andEnableViewConfigNotIn(List<Boolean> values) {
            addCriterion("enable_view_config not in", values, "enableViewConfig");
            return (Criteria) this;
        }

        public Criteria andEnableViewConfigBetween(Boolean value1, Boolean value2) {
            addCriterion("enable_view_config between", value1, value2, "enableViewConfig");
            return (Criteria) this;
        }

        public Criteria andEnableViewConfigNotBetween(Boolean value1, Boolean value2) {
            addCriterion("enable_view_config not between", value1, value2, "enableViewConfig");
            return (Criteria) this;
        }

        public Criteria andLabConfigScopeIsNull() {
            addCriterion("lab_config_scope is null");
            return (Criteria) this;
        }

        public Criteria andLabConfigScopeIsNotNull() {
            addCriterion("lab_config_scope is not null");
            return (Criteria) this;
        }

        public Criteria andLabConfigScopeEqualTo(String value) {
            addCriterion("lab_config_scope =", value, "labConfigScope");
            return (Criteria) this;
        }

        public Criteria andLabConfigScopeNotEqualTo(String value) {
            addCriterion("lab_config_scope <>", value, "labConfigScope");
            return (Criteria) this;
        }

        public Criteria andLabConfigScopeGreaterThan(String value) {
            addCriterion("lab_config_scope >", value, "labConfigScope");
            return (Criteria) this;
        }

        public Criteria andLabConfigScopeGreaterThanOrEqualTo(String value) {
            addCriterion("lab_config_scope >=", value, "labConfigScope");
            return (Criteria) this;
        }

        public Criteria andLabConfigScopeLessThan(String value) {
            addCriterion("lab_config_scope <", value, "labConfigScope");
            return (Criteria) this;
        }

        public Criteria andLabConfigScopeLessThanOrEqualTo(String value) {
            addCriterion("lab_config_scope <=", value, "labConfigScope");
            return (Criteria) this;
        }

        public Criteria andLabConfigScopeLike(String value) {
            addCriterion("lab_config_scope like", value, "labConfigScope");
            return (Criteria) this;
        }

        public Criteria andLabConfigScopeNotLike(String value) {
            addCriterion("lab_config_scope not like", value, "labConfigScope");
            return (Criteria) this;
        }

        public Criteria andLabConfigScopeIn(List<String> values) {
            addCriterion("lab_config_scope in", values, "labConfigScope");
            return (Criteria) this;
        }

        public Criteria andLabConfigScopeNotIn(List<String> values) {
            addCriterion("lab_config_scope not in", values, "labConfigScope");
            return (Criteria) this;
        }

        public Criteria andLabConfigScopeBetween(String value1, String value2) {
            addCriterion("lab_config_scope between", value1, value2, "labConfigScope");
            return (Criteria) this;
        }

        public Criteria andLabConfigScopeNotBetween(String value1, String value2) {
            addCriterion("lab_config_scope not between", value1, value2, "labConfigScope");
            return (Criteria) this;
        }

        public Criteria andSortIsNull() {
            addCriterion("sort is null");
            return (Criteria) this;
        }

        public Criteria andSortIsNotNull() {
            addCriterion("sort is not null");
            return (Criteria) this;
        }

        public Criteria andSortEqualTo(Integer value) {
            addCriterion("sort =", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotEqualTo(Integer value) {
            addCriterion("sort <>", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThan(Integer value) {
            addCriterion("sort >", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThanOrEqualTo(Integer value) {
            addCriterion("sort >=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThan(Integer value) {
            addCriterion("sort <", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThanOrEqualTo(Integer value) {
            addCriterion("sort <=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortIn(List<Integer> values) {
            addCriterion("sort in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotIn(List<Integer> values) {
            addCriterion("sort not in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortBetween(Integer value1, Integer value2) {
            addCriterion("sort between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotBetween(Integer value1, Integer value2) {
            addCriterion("sort not between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andVersionInfoIsNull() {
            addCriterion("version_info is null");
            return (Criteria) this;
        }

        public Criteria andVersionInfoIsNotNull() {
            addCriterion("version_info is not null");
            return (Criteria) this;
        }

        public Criteria andVersionInfoEqualTo(String value) {
            addCriterion("version_info =", value, "versionInfo");
            return (Criteria) this;
        }

        public Criteria andVersionInfoNotEqualTo(String value) {
            addCriterion("version_info <>", value, "versionInfo");
            return (Criteria) this;
        }

        public Criteria andVersionInfoGreaterThan(String value) {
            addCriterion("version_info >", value, "versionInfo");
            return (Criteria) this;
        }

        public Criteria andVersionInfoGreaterThanOrEqualTo(String value) {
            addCriterion("version_info >=", value, "versionInfo");
            return (Criteria) this;
        }

        public Criteria andVersionInfoLessThan(String value) {
            addCriterion("version_info <", value, "versionInfo");
            return (Criteria) this;
        }

        public Criteria andVersionInfoLessThanOrEqualTo(String value) {
            addCriterion("version_info <=", value, "versionInfo");
            return (Criteria) this;
        }

        public Criteria andVersionInfoLike(String value) {
            addCriterion("version_info like", value, "versionInfo");
            return (Criteria) this;
        }

        public Criteria andVersionInfoNotLike(String value) {
            addCriterion("version_info not like", value, "versionInfo");
            return (Criteria) this;
        }

        public Criteria andVersionInfoIn(List<String> values) {
            addCriterion("version_info in", values, "versionInfo");
            return (Criteria) this;
        }

        public Criteria andVersionInfoNotIn(List<String> values) {
            addCriterion("version_info not in", values, "versionInfo");
            return (Criteria) this;
        }

        public Criteria andVersionInfoBetween(String value1, String value2) {
            addCriterion("version_info between", value1, value2, "versionInfo");
            return (Criteria) this;
        }

        public Criteria andVersionInfoNotBetween(String value1, String value2) {
            addCriterion("version_info not between", value1, value2, "versionInfo");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNull() {
            addCriterion("create_user is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNotNull() {
            addCriterion("create_user is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserEqualTo(String value) {
            addCriterion("create_user =", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotEqualTo(String value) {
            addCriterion("create_user <>", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThan(String value) {
            addCriterion("create_user >", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThanOrEqualTo(String value) {
            addCriterion("create_user >=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThan(String value) {
            addCriterion("create_user <", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThanOrEqualTo(String value) {
            addCriterion("create_user <=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLike(String value) {
            addCriterion("create_user like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotLike(String value) {
            addCriterion("create_user not like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserIn(List<String> values) {
            addCriterion("create_user in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotIn(List<String> values) {
            addCriterion("create_user not in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserBetween(String value1, String value2) {
            addCriterion("create_user between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotBetween(String value1, String value2) {
            addCriterion("create_user not between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNull() {
            addCriterion("update_user is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNotNull() {
            addCriterion("update_user is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserEqualTo(String value) {
            addCriterion("update_user =", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotEqualTo(String value) {
            addCriterion("update_user <>", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThan(String value) {
            addCriterion("update_user >", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThanOrEqualTo(String value) {
            addCriterion("update_user >=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThan(String value) {
            addCriterion("update_user <", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThanOrEqualTo(String value) {
            addCriterion("update_user <=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLike(String value) {
            addCriterion("update_user like", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotLike(String value) {
            addCriterion("update_user not like", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIn(List<String> values) {
            addCriterion("update_user in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotIn(List<String> values) {
            addCriterion("update_user not in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserBetween(String value1, String value2) {
            addCriterion("update_user between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotBetween(String value1, String value2) {
            addCriterion("update_user not between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNull() {
            addCriterion("platform_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNotNull() {
            addCriterion("platform_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdEqualTo(String value) {
            addCriterion("platform_id =", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotEqualTo(String value) {
            addCriterion("platform_id <>", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThan(String value) {
            addCriterion("platform_id >", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThanOrEqualTo(String value) {
            addCriterion("platform_id >=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThan(String value) {
            addCriterion("platform_id <", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThanOrEqualTo(String value) {
            addCriterion("platform_id <=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLike(String value) {
            addCriterion("platform_id like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotLike(String value) {
            addCriterion("platform_id not like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIn(List<String> values) {
            addCriterion("platform_id in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotIn(List<String> values) {
            addCriterion("platform_id not in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdBetween(String value1, String value2) {
            addCriterion("platform_id between", value1, value2, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotBetween(String value1, String value2) {
            addCriterion("platform_id not between", value1, value2, "platformId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}