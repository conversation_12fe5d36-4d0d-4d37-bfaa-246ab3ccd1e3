package com.haoys.user.mapper;

import com.haoys.user.model.ProjectTasteStat;
import com.haoys.user.model.ProjectTasteStatExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectTasteStatMapper {
    long countByExample(ProjectTasteStatExample example);

    int deleteByExample(ProjectTasteStatExample example);

    int insert(ProjectTasteStat record);

    int insertSelective(ProjectTasteStat record);

    List<ProjectTasteStat> selectByExampleWithBLOBs(ProjectTasteStatExample example);

    List<ProjectTasteStat> selectByExample(ProjectTasteStatExample example);

    int updateByExampleSelective(@Param("record") ProjectTasteStat record, @Param("example") ProjectTasteStatExample example);

    int updateByExampleWithBLOBs(@Param("record") ProjectTasteStat record, @Param("example") ProjectTasteStatExample example);

    int updateByExample(@Param("record") ProjectTasteStat record, @Param("example") ProjectTasteStatExample example);
}