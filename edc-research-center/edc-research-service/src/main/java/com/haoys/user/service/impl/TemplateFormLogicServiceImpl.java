package com.haoys.user.service.impl;

import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.crf.TemplateFormLogicParam;
import com.haoys.user.domain.vo.ecrf.TemplateFormConfigVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDetailVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormLogicVo;
import com.haoys.user.mapper.TemplateFormLogicDataMapper;
import com.haoys.user.mapper.TemplateFormLogicMapper;
import com.haoys.user.model.ProjectTesteeResult;
import com.haoys.user.model.ProjectVisitConfig;
import com.haoys.user.model.TemplateFormLogic;
import com.haoys.user.model.TemplateFormLogicData;
import com.haoys.user.model.TemplateFormLogicDataExample;
import com.haoys.user.model.TemplateFormLogicExample;
import com.haoys.user.service.ProjectTesteeResultService;
import com.haoys.user.service.ProjectTesteeInfoService;
import com.haoys.user.service.ProjectVisitConfigService;
import com.haoys.user.service.TemplateConfigService;
import com.haoys.user.service.TemplateFormLogicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Service
public class TemplateFormLogicServiceImpl implements TemplateFormLogicService {

    @Autowired
    private TemplateFormLogicMapper templateFormLogicMapper;
    @Autowired
    private TemplateFormLogicDataMapper templateFormLogicDataMapper;

    @Autowired
    private TemplateConfigService templateConfigService;
    @Autowired
    private ProjectTesteeInfoService projectTesteeInfoService;
    @Autowired
    private ProjectVisitConfigService projectVisitConfigService;
    @Autowired
    private ProjectTesteeResultService projectTesteeResultService;

    @Override
    public CustomResult saveFormVariableLogicData(TemplateFormLogicParam templateFormLogicParam) {
        CustomResult customResult = new CustomResult();
        List<TemplateFormLogicParam.TemplateFormLogicDataParam> logicParamDataList = templateFormLogicParam.getDataList();
        if(logicParamDataList == null || logicParamDataList.size() ==0){
            customResult.setMessage(BusinessConfig.PROJECT_FORM_LOGIN_MESSAGE_NOT_NULL);
            return customResult;
        }
        if(templateFormLogicParam.getId() == null){
            TemplateFormLogic templateFormLogic = new TemplateFormLogic();
            templateFormLogic.setId(SnowflakeIdWorker.getUuid());
            templateFormLogic.setVisitId(templateFormLogicParam.getVisitId());
            templateFormLogic.setFormId(templateFormLogicParam.getFormId());
            templateFormLogic.setTemplateId(templateFormLogicParam.getTemplateId());
            templateFormLogic.setFormDetailId(templateFormLogicParam.getFormDetailId());
            templateFormLogic.setExpressionId(templateFormLogicParam.getExpressionId());
            if(templateFormLogicParam.getConditionList() != null){
                templateFormLogic.setConditionList(templateFormLogicParam.getConditionList().toString());
            }
            if(templateFormLogicParam.getOptionList() != null){
                templateFormLogic.setOptionList(templateFormLogicParam.getOptionList().toString());
            }
            if(templateFormLogicParam.getExtands() != null){
                templateFormLogic.setExtands(templateFormLogicParam.getExtands().toString());
            }
            templateFormLogic.setLabel(templateFormLogicParam.getLabel());
            templateFormLogic.setCreateTime(new Date());
            templateFormLogic.setCreateUser(templateFormLogicParam.getCreateUser());
            templateFormLogic.setStatus("0");
            templateFormLogicMapper.insertSelective(templateFormLogic);
            //保存显示条件表达式记录
            List<TemplateFormLogicParam.TemplateFormLogicDataParam> templateFormLogicParamDataList = templateFormLogicParam.getDataList();
            for (TemplateFormLogicParam.TemplateFormLogicDataParam templateFormLogicDataParam : templateFormLogicParamDataList) {
                templateFormLogicDataParam.setId(SnowflakeIdWorker.getUuid());
                templateFormLogicDataParam.setLogicId(templateFormLogic.getId());
                templateFormLogicDataParam.setCreateTime(new Date());
                templateFormLogicDataParam.setCreateUserId(templateFormLogicParam.getCreateUser());
                templateFormLogicDataParam.setStatus("0");
                TemplateFormLogicData templateFormLogicData = new TemplateFormLogicData();
                BeanUtils.copyProperties(templateFormLogicDataParam, templateFormLogicData);
                templateFormLogicDataMapper.insertSelective(templateFormLogicData);
            }
        }else{
            TemplateFormLogic templateFormLogic = templateFormLogicMapper.selectByPrimaryKey(templateFormLogicParam.getId());
            templateFormLogic.setExpressionId(templateFormLogicParam.getExpressionId());
            if(templateFormLogicParam.getConditionList() != null){
                templateFormLogic.setConditionList(templateFormLogicParam.getConditionList().toString());
            }
            if(templateFormLogicParam.getOptionList() != null){
                templateFormLogic.setOptionList(templateFormLogicParam.getOptionList().toString());
            }
            if(templateFormLogicParam.getExtands() != null){
                templateFormLogic.setExtands(templateFormLogicParam.getExtands().toString());
            }
            templateFormLogic.setLabel(templateFormLogicParam.getLabel());
            templateFormLogic.setTemplateId(templateFormLogicParam.getTemplateId());
            templateFormLogic.setUpdateTime(new Date());
            templateFormLogic.setUpdateUser(templateFormLogicParam.getCreateUser());
            templateFormLogicMapper.updateByPrimaryKeySelective(templateFormLogic);
            //更新显示条件表达式记录
            List<TemplateFormLogicParam.TemplateFormLogicDataParam> templateFormLogicParamDataList = templateFormLogicParam.getDataList();
            for (TemplateFormLogicParam.TemplateFormLogicDataParam templateFormLogicDataParam : templateFormLogicParamDataList) {
                templateFormLogicDataParam.setLogicId(templateFormLogic.getId());
                TemplateFormLogicData templateFormLogicData = new TemplateFormLogicData();
                BeanUtils.copyProperties(templateFormLogicDataParam, templateFormLogicData);
                if(templateFormLogicDataParam.getId() == null){
                    templateFormLogicData.setId(SnowflakeIdWorker.getUuid());
                    templateFormLogicData.setCreateTime(new Date());
                    templateFormLogicData.setCreateUserId(templateFormLogicParam.getCreateUser());
                    templateFormLogicData.setStatus("0");
                    templateFormLogicDataMapper.insertSelective(templateFormLogicData);
                }else{
                    templateFormLogicData.setUpdateTime(new Date());
                    templateFormLogicData.setUpdateUserId(templateFormLogicParam.getCreateUser());
                }
                templateFormLogicDataMapper.updateByPrimaryKeySelective(templateFormLogicData);
            }
        }
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }

    @Override
    public List<TemplateFormLogicVo> getFormVariableLogicList(String templateId, String projectId, String visitId, String formId, String detailId, String testeeId) {
        List<TemplateFormLogicVo> dataList = new ArrayList();
        TemplateFormLogicExample example = new TemplateFormLogicExample();
        TemplateFormLogicExample.Criteria criteria = example.createCriteria();
        criteria.andVisitIdEqualTo(Long.parseLong(visitId));
        criteria.andFormIdEqualTo(Long.parseLong(formId));
        criteria.andFormDetailIdEqualTo(Long.parseLong(detailId));
        List<TemplateFormLogic> templateFormLogicList = templateFormLogicMapper.selectByExample(example);
        if(templateFormLogicList == null || templateFormLogicList.size() ==0){
            TemplateFormLogicVo templateFormLogicVo = new TemplateFormLogicVo();
            if(StringUtils.isNotBlank(projectId)){
                List<ProjectVisitConfig> visitList = projectVisitConfigService.getProjectVisitListByPlanId(projectId, "", "");
                templateFormLogicVo.setVisitOptionList(visitList);
                List<TemplateFormConfigVo> testeeVisitFormList = projectTesteeInfoService.getTesteeVisitFormList("", projectId, "", visitId);
                templateFormLogicVo.setFormOptionList(testeeVisitFormList);
                List<TemplateFormDetailVo> templateFormDetailList = templateConfigService.getTemplateFormDetailConfigListByFormId(null, formId, "", "1", "", "");
                templateFormLogicVo.setDetailOptionList(templateFormDetailList);
            }
            if(StringUtils.isNotBlank(templateId)){
                List<ProjectVisitConfig> projectVisitConfigList = projectVisitConfigService.getProjectVisitListByTemplateId(templateId);
                templateFormLogicVo.setVisitOptionList(projectVisitConfigList);
                List<TemplateFormConfigVo> testeeVisitFormList = projectTesteeInfoService.getTesteeVisitFormList(templateId, "", "", visitId);
                templateFormLogicVo.setFormOptionList(testeeVisitFormList);
                List<TemplateFormDetailVo> templateFormDetailList = templateConfigService.getTemplateFormDetailConfigListByFormId(templateId, formId, "", "1", "", "");
                templateFormLogicVo.setDetailOptionList(templateFormDetailList);
            }
            dataList.add(templateFormLogicVo);
            return dataList;
        }
        for (TemplateFormLogic templateFormLogic : templateFormLogicList) {
            TemplateFormLogicVo templateFormLogicVo = new TemplateFormLogicVo();
            BeanUtils.copyProperties(templateFormLogic, templateFormLogicVo);
            List<TemplateFormLogicVo.TemplateFormLogicDataVo> templateFormLogicDataVoList = new ArrayList<>();
            TemplateFormLogicDataExample _example = new TemplateFormLogicDataExample();
            TemplateFormLogicDataExample.Criteria criteria1 = _example.createCriteria();
            criteria1.andLogicIdEqualTo(templateFormLogic.getId());
            List<TemplateFormLogicData> templateFormLogicData = templateFormLogicDataMapper.selectByExample(_example);
            for (TemplateFormLogicData templateFormLogicDataValue : templateFormLogicData) {
                TemplateFormLogicVo.TemplateFormLogicDataVo templateFormLogicDataVo = new TemplateFormLogicVo.TemplateFormLogicDataVo();
                BeanUtils.copyProperties(templateFormLogicDataValue, templateFormLogicDataVo);

                //查询对应填充数据
                if(StringUtils.isNotBlank(projectId)){
                    List<ProjectVisitConfig> visitList = projectVisitConfigService.getProjectVisitListByPlanId(projectId, "", "");
                    templateFormLogicDataVo.setVisitOptionList(visitList);
                    List<TemplateFormConfigVo> testeeVisitFormList = projectTesteeInfoService.getTesteeVisitFormList("", projectId, "", templateFormLogicDataValue.getTargetVisitId().toString());
                    templateFormLogicDataVo.setFormOptionList(testeeVisitFormList);
                    List<TemplateFormDetailVo> templateFormDetailList = templateConfigService.getTemplateFormDetailConfigListByFormId(null, templateFormLogicDataValue.getTargetFormId().toString(), "", "1", "", "");
                    templateFormLogicDataVo.setDetailOptionList(templateFormDetailList);
                    if(StringUtils.isNotBlank(testeeId)){
                        ProjectTesteeResult customTesteeFormOneResult = projectTesteeResultService.getProjectTesteeFormOneResult(projectId,
                                "", templateFormLogicDataValue.getTargetVisitId().toString(),
                                templateFormLogicDataValue.getTargetFormId().toString(),
                                "", templateFormLogicDataValue.getTargetDetailId().toString(), testeeId);
                        if(customTesteeFormOneResult != null){
                            templateFormLogicDataVo.setTesteeResultValue(customTesteeFormOneResult.getFieldValue());
                        }
                    }
                }
                if(StringUtils.isNotBlank(templateId)){
                    List<ProjectVisitConfig> projectVisitConfigList = projectVisitConfigService.getProjectVisitListByTemplateId(templateId);
                    templateFormLogicDataVo.setVisitOptionList(projectVisitConfigList);
                    List<TemplateFormConfigVo> testeeVisitFormList = projectTesteeInfoService.getTesteeVisitFormList(templateId, "", "", templateFormLogicDataValue.getTargetVisitId().toString());
                    templateFormLogicDataVo.setFormOptionList(testeeVisitFormList);
                    List<TemplateFormDetailVo> templateFormDetailList = templateConfigService.getTemplateFormDetailConfigListByFormId(templateId, templateFormLogicDataValue.getTargetFormId().toString(), "", "1", "", "");
                    templateFormLogicDataVo.setDetailOptionList(templateFormDetailList);
                }
                templateFormLogicDataVoList.add(templateFormLogicDataVo);
            }
            templateFormLogicVo.setDataList(templateFormLogicDataVoList);
            dataList.add(templateFormLogicVo);
        }
        return dataList;
    }

    @Override
    public List<TemplateFormLogicVo> getFormLogicConfigListByTemplateId(String templateId, String formId, String detailId) {
        List<TemplateFormLogicVo> dataList = new ArrayList<>();
        TemplateFormLogicExample example = new TemplateFormLogicExample();
        TemplateFormLogicExample.Criteria criteria = example.createCriteria();
        criteria.andTemplateIdEqualTo(Long.parseLong(templateId));
        criteria.andFormIdEqualTo(Long.parseLong(formId));
        if(StringUtils.isNotBlank(detailId)){
            criteria.andFormDetailIdEqualTo(Long.parseLong(detailId));
        }
        List<TemplateFormLogic> templateFormLogicList = templateFormLogicMapper.selectByExample(example);
        for (TemplateFormLogic templateFormLogic : templateFormLogicList) {
            TemplateFormLogicVo templateFormLogicVo = new TemplateFormLogicVo();
            BeanUtils.copyProperties(templateFormLogic, templateFormLogicVo);
            List<TemplateFormLogicVo.TemplateFormLogicDataVo> templateFormLogicDataVoList = new ArrayList<>();
            TemplateFormLogicDataExample _example = new TemplateFormLogicDataExample();
            TemplateFormLogicDataExample.Criteria criteria1 = _example.createCriteria();
            criteria1.andLogicIdEqualTo(templateFormLogic.getId());
            List<TemplateFormLogicData> templateFormLogicData = templateFormLogicDataMapper.selectByExample(_example);
            for (TemplateFormLogicData templateFormLogicDatum : templateFormLogicData) {
                TemplateFormLogicVo.TemplateFormLogicDataVo templateFormLogicDataVo = new TemplateFormLogicVo.TemplateFormLogicDataVo();
                BeanUtils.copyProperties(templateFormLogicDatum, templateFormLogicDataVo);
                templateFormLogicDataVoList.add(templateFormLogicDataVo);
            }
            templateFormLogicVo.setDataList(templateFormLogicDataVoList);
            dataList.add(templateFormLogicVo);
        }
        return dataList;
    }

    @Override
    public CustomResult deleteFormLogicData(String id) {
        CustomResult customResult = new CustomResult();
        TemplateFormLogicData templateFormLogicData = templateFormLogicDataMapper.selectByPrimaryKey(Long.parseLong(id));
        if(templateFormLogicData == null){
            customResult.setMessage(BusinessConfig.RETURN_MESSAGE_RECORD_NOT_FOUND);
            return customResult;
        }
        templateFormLogicDataMapper.deleteByPrimaryKey(Long.parseLong(id));
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }
}
