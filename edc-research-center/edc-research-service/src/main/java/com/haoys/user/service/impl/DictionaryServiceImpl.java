package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.bean.BeanUtils;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.dto.SystemDictionaryParam;
import com.haoys.user.domain.enums.SystemDictEnums;
import com.haoys.user.domain.param.dict.DictParamQuery;
import com.haoys.user.domain.vo.system.DictionaryVo;
import com.haoys.user.exception.CustomerException;
import com.haoys.user.expand.DictionaryCacheUtils;
import com.haoys.user.mapper.DictionaryFromMapper;
import com.haoys.user.mapper.DictionaryMapper;
import com.haoys.user.model.Dictionary;
import com.haoys.user.model.DictionaryExample;
import com.haoys.user.model.DictionaryFrom;
import com.haoys.user.service.DictionaryService;
import com.haoys.user.service.TemplateConfigService;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class DictionaryServiceImpl extends BaseService implements DictionaryService {

    private long DEFAULT_PARENT_CODE = 0;
    
    @Autowired
    private DictionaryMapper dictionaryMapper;
    @Autowired
    private DictionaryFromMapper dictionaryFromMapper;
    @Autowired
    private TemplateConfigService templateConfigService;

    /**
     * 查询字典列表
     * @param param 搜索条件
     * @return
     */
    @Override
    public CommonPage<DictionaryVo> getDictionaryListForPage(DictParamQuery param) {
        Page<Object> page = PageHelper.startPage(param.getPageNum(), param.getPageSize());
        CommonPage<DictionaryVo> commonPage = new CommonPage<>();
        List<Dictionary> dictionaryList = dictionaryMapper.selectList(param);
        List<DictionaryVo> dictionaryVoList = new ArrayList<>();
        for(Dictionary dl : dictionaryList){
            DictionaryVo vo = new DictionaryVo();
            BeanUtils.copyProperties(dl, vo);
            dictionaryVoList.add(vo);
        }
        commonPage.setList(dictionaryVoList);
        commonPageWrapper(param.getPageNum(), param.getPageSize(), page, commonPage);
        return commonPage;
    }



    /**
     * 保存系统字典以及系统字典的值
     * @param systemDictionaryParam
     * @return
     */
    @Override
    public CommonResult<Object> saveDictionary(SystemDictionaryParam systemDictionaryParam) {
        // 创建字典对象并保存
        // 校验名称是否重复
        if (StringUtils.isBlank(systemDictionaryParam.getParentId())){
            systemDictionaryParam.setParentId(DEFAULT_PARENT_CODE+"");
        }
        extracted(systemDictionaryParam,Long.parseLong(systemDictionaryParam.getParentId()),false);
        String userId = SecurityUtils.getUserId().toString();
        Dictionary dictionary = new Dictionary();
        dictionary.setId(SnowflakeIdWorker.getUuid());
        dictionary.setCreateTime(new Date());
        dictionary.setCreateUser(userId);
        dictionary.setSort(systemDictionaryParam.getSort());
        dictionary.setParentId(Long.parseLong(systemDictionaryParam.getParentId()));
        dictionary.setName(systemDictionaryParam.getName());
        dictionary.setEnName(systemDictionaryParam.getEnName());
        dictionary.setDescription(systemDictionaryParam.getDescription());
        dictionary.setValue(systemDictionaryParam.getValue());
        dictionary.setCode(systemDictionaryParam.getCode());
        dictionary.setScoreValue(systemDictionaryParam.getScoreValue());
        dictionary.setTenantId(SecurityUtils.getSystemTenantId());
        dictionary.setPlatformId(SecurityUtils.getSystemPlatformId());
        dictionary.setStatus(BusinessConfig.ENABLED_STATUS.toString());
        if("0".equals(systemDictionaryParam.getParentId())){
            dictionary.setCode(systemDictionaryParam.getCode());

        }else {
            dictionary.setCode(null);

        }

        dictionaryMapper.insert(dictionary);
        DictionaryCacheUtils.removeDictCache(systemDictionaryParam.getParentId());

        String key2 = "DICT:" + ":1:" + dictionary.getId();
        DictionaryCacheUtils.removeDictCache(key2);
        return CommonResult.success(null);
    }

    @Override
    public Dictionary getDictionaryInfo(String id) {
        if(!NumberUtil.isLong(id)){return null;}
        return dictionaryMapper.selectByPrimaryKey(Long.parseLong(id));
    }

    @Override
    public CommonResult<Object> updateDictionary(SystemDictionaryParam systemDictionaryParam) {
        // 创建字典对象并保存
        // 校验名称是否重复
        Dictionary dict = dictionaryMapper.selectByPrimaryKey(systemDictionaryParam.getId());
        extracted(systemDictionaryParam,dict.getParentId(),false);
        String userId = SecurityUtils.getUserId().toString();
        dict.setUpdateUser(userId);
        dict.setUpdateTime(new Date());
        dict.setName(systemDictionaryParam.getName());
        dict.setEnName(systemDictionaryParam.getEnName());
        dict.setDescription(systemDictionaryParam.getDescription());
        dict.setValue(systemDictionaryParam.getValue());
        dict.setScoreValue(systemDictionaryParam.getScoreValue());
        dict.setSort(systemDictionaryParam.getSort());

        if("0".equals(dict.getParentId().toString())){
            dict.setCode(systemDictionaryParam.getCode());

        }
        dictionaryMapper.updateByPrimaryKey(dict);
        DictionaryCacheUtils.removeDictCache(dict.getParentId().toString());

        String key2 = "DICT:" + ":1:" + dict.getId();
        DictionaryCacheUtils.removeDictCache(key2);
        return CommonResult.success(null);
    }

    /**
     * 新增和更新字典时候的校验
     * @param param
     * @param isCheckEnName 是否校验英文名称
     */
    private void extracted(SystemDictionaryParam param , Long parentId, boolean isCheckEnName) {
        // 校验名称是否重复
         Dictionary dictByName0 = getDictByName(param.getName(), parentId);
        if (dictByName0!=null && !dictByName0.getId().equals(param.getId())){
            if (DEFAULT_PARENT_CODE==parentId){
                throw new CustomerException(SystemDictEnums.E70000);
            }else {
                throw new CustomerException(SystemDictEnums.E70001);
            }

        }

        if (isCheckEnName){
            // 校验英文名称是否重复
            if (StringUtils.isNotEmpty(param.getEnName())){
                Dictionary dictItemByEnName = getDictByEnName(param.getEnName(),parentId);
                if (dictItemByEnName!=null && !dictItemByEnName.getId().equals(param.getId())){
                    if (DEFAULT_PARENT_CODE==parentId){
                        throw new CustomerException(SystemDictEnums.E70000);
                    }else {
                        throw new CustomerException(SystemDictEnums.E70001);
                    }
                }
            }
        }
        // 校验字典编码是否重复
//        Dictionary dictByCode0 = getDictByCode(param.getCode(), parentId);
//        if (dictByCode0!=null && !dictByCode0.getId().equals(param.getId()) ){
//            if (DEFAULT_PARENT_CODE==parentId){
//                throw new CustomerException(SystemDictEnums.E70000);
//            }else {
//                throw new CustomerException(SystemDictEnums.E70001);
//            }
//        }
    }

    @Override
    public CommonResult<Object> removeSystemDictionaryById(String dictId) {
        long id = Long.parseLong(dictId);
        Dictionary dictionary = dictionaryMapper.selectByPrimaryKey(id);
        if (dictionary!=null){
            if(dictionary.getParentId()==DEFAULT_PARENT_CODE){
                Boolean checked = templateConfigService.checkSystemDictionaryTypeReference(dictId);
                if(checked){
                    return CommonResult.failed(SystemDictEnums.E70002.getMessage());
                }
                dictionaryMapper.deleteByPrimaryKey(id);
            }else {
                Boolean checked = templateConfigService.checkSystemDictionaryOptionReference(dictId);
                if(checked){
                    return CommonResult.failed(SystemDictEnums.E70003.getMessage());
                }
            }
            DictionaryCacheUtils.removeDictCache(dictionary.getParentId().toString());
            dictionaryMapper.deleteByPrimaryKey(id);
            return CommonResult.success(null);
        }
        return CommonResult.failed();
    }
    
    @Override
    public CommonResult<Object> enableOrUnable(String id,String status) {
        Dictionary dictionary = dictionaryMapper.selectByPrimaryKey(Long.parseLong(id));
        if (BusinessConfig.DISABLED_STATUS.toString().equals(status)){
            if(dictionary.getParentId()==DEFAULT_PARENT_CODE){
                Boolean checked = templateConfigService.checkSystemDictionaryTypeReference(id);
                if(checked){
                    return CommonResult.failed(SystemDictEnums.E70003.getMessage());
                }
            }else {
                Boolean checked = templateConfigService.checkSystemDictionaryOptionReference(id);
                if(checked){
                    return CommonResult.failed(SystemDictEnums.E70003.getMessage());
                }
            }
            DictionaryCacheUtils.removeDictCache(dictionary.getParentId().toString());
        }
        dictionary.setStatus(status);
        dictionary.setUpdateTime(new Date());
        dictionary.setUpdateUser(SecurityUtils.getUserIdValue());
        dictionaryMapper.updateByPrimaryKey(dictionary);
        return CommonResult.success(null);
    }

    /**
     * 构建字典对象
     * @param userId
     * @param parentId
     * @param param
     * @return
     */
    @NotNull
    private static Dictionary getDictionary(String userId, Long parentId, SystemDictionaryParam param) {
        Dictionary dictItem = new Dictionary();
        dictItem.setId(SnowflakeIdWorker.getUuid());
        dictItem.setUpdateUser(userId);
        dictItem.setCreateTime(new Date());
        dictItem.setCreateUser(userId);
        dictItem.setUpdateTime(new Date());
        dictItem.setSort(param.getSort());
        dictItem.setParentId(parentId);
        dictItem.setName(param.getName());
        dictItem.setEnName(param.getEnName());
        dictItem.setDescription(param.getDescription());
        dictItem.setValue(param.getValue());
        dictItem.setCode(param.getCode());
        return dictItem;
    }

    @Override
    public Dictionary getDictByCode(String code,Long parentId) {
        DictionaryExample example = new DictionaryExample();
        DictionaryExample.Criteria criteria = example.createCriteria();
        criteria.andCodeEqualTo(code);
        criteria.andParentIdEqualTo(parentId);
        List<Dictionary> dictItems = dictionaryMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(dictItems)){
            return dictItems.get(0);
        }
        return null;
    }
    
    @Override
    public Dictionary getDictInfoByNameAndCode(String name, String code) {
        DictionaryExample example = new DictionaryExample();
        DictionaryExample.Criteria criteria = example.createCriteria();
        criteria.andNameEqualTo(name);
        criteria.andCodeEqualTo(code);
        criteria.andStatusEqualTo(BusinessConfig.ENABLED_STATUS.toString());
        List<Dictionary> dictItems = dictionaryMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(dictItems)){
            return dictItems.get(0);
        }
        return null;
    }
    
    
    @Override
    public Dictionary getDictByName(String dictName,Long parentId) {
        DictionaryExample example = new DictionaryExample();
        DictionaryExample.Criteria criteria = example.createCriteria();
        criteria.andNameEqualTo(dictName);
        criteria.andParentIdEqualTo(parentId);
        List<Dictionary> dictItems = dictionaryMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(dictItems)){
            return dictItems.get(0);
        }
        return null;
    }


    @Override
    public Dictionary getDictByEnName(String dictEnName,Long parentId) {
        DictionaryExample example = new DictionaryExample();
        DictionaryExample.Criteria criteria = example.createCriteria();
        criteria.andEnNameEqualTo(dictEnName);
        criteria.andParentIdEqualTo(parentId);
        List<Dictionary> dictItems = dictionaryMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(dictItems)){
            return dictItems.get(0);
        }
        return null;
    }

    @Override
    public String getDictNameById(Long dictId) {
        Dictionary dict = dictionaryMapper.selectByPrimaryKey(dictId);
        if (dict!=null){
            return dict.getName();
        }
        return null;
    }

    @Override
    public List<Dictionary> getDictionaryListByParentId(String parentId, String parentCode, String name) {
        if(StringUtils.isEmpty(parentId) && StringUtils.isEmpty(parentCode)){return null;}
        //List<Dictionary> dictCache = DictUtils.getDictCache(parentId);
        List<Dictionary> dictCache = null;
        DictParamQuery param = new DictParamQuery();
        if(StringUtils.isNotEmpty(parentId)){param.setParentId(parentId);}
        if(StringUtils.isNotEmpty(parentCode)){param.setCode(parentCode);}
        param.setPageSize(Integer.MAX_VALUE);
        param.setStatus(BusinessConfig.ENABLED_STATUS.toString());
        dictCache = dictionaryMapper.selectList(param);
        DictionaryCacheUtils.setDictCache(parentId,dictCache);
        if (StringUtils.isNotEmpty(name)){
            return  dictCache.stream().filter(dictionary -> dictionary.getName().contains(name)).collect(Collectors.toList());
        }
        return dictCache;
    }

    @Override
    public int saveDictionaryFrom(DictionaryFrom dictionaryFrom){
        Dictionary dictionary = dictionaryMapper.selectByPrimaryKey(dictionaryFrom.getDictionaryId());
        if(dictionary != null){
            dictionaryFrom.setDictionaryName(dictionary.getName() == null ? "" : dictionary.getName());
            dictionaryFrom.setDictionaryEnName(dictionary.getEnName() == null ? "" : dictionary.getEnName());
            dictionaryFrom.setDictionaryCode(dictionary.getCode() == null ? "" : dictionary.getCode());
            dictionaryFrom.setTenantId(SecurityUtils.getSystemTenantId());
            dictionaryFrom.setPlatformId(SecurityUtils.getSystemPlatformId());
            dictionaryFrom.setCreateTime(new Date());

        }
        return dictionaryFromMapper.insert(dictionaryFrom);
    }


}
