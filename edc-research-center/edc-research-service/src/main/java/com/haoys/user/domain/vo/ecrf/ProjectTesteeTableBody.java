package com.haoys.user.domain.vo.ecrf;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.haoys.user.domain.expand.TemplateLabConfigExpand;
import com.haoys.user.domain.vo.testee.ProjectTesteeFormImageVo;
import com.haoys.user.model.ProjectTesteeChallenge;
import com.haoys.user.model.TemplateFormVariableRule;
import com.haoys.user.model.TemplateVariableViewBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ProjectTesteeTableBody {

    @Data
    public static class RowDataDesc{

        @ApiModelProperty(value = "表格行记录名称")
        private String rowName = "";

        @ApiModelProperty(value = "表格行记录编号")
        private String rowNumber;

        @ApiModelProperty(value = "表格id")
        private String formDetailId;

        @ApiModelProperty(value = "质疑状态标识")
        private String challengeStatus;

        @ApiModelProperty(value = "是否发我质疑标识")
        private String challengeButtonStatus;

        @ApiModelProperty(value = "表格录入完成状态")
        private String complateStatus = "1";

        @ApiModelProperty(value = "表格行数据")
        private List<ProjectTesteeTableData> rowData = new ArrayList<>();

        //@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        @ApiModelProperty(value = "创建时间")
        private String createTime;
    }

    @Data
    public static class ProjectTesteeTableData {

        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "项目id")
        private Long projectId;

        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "访视id")
        private Long visitId;

        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "表单项id")
        private Long formId;

        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "表格单元格记录id-字段问题")
        private Long id;

        @ApiModelProperty(value = "表格记录id")
        private String testeeResultTableId;

        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "表单项表格id")
        private Long formTableId;

        @ApiModelProperty(value = "表单项表格视图值id")
        private String formTableViewId;

        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "参与者id")
        private Long testeeId;

        @ApiModelProperty(value = "字段名称")
        private String label;

        @ApiModelProperty(value = "字段code")
        private String fieldName;

        @ApiModelProperty(value = "表单变量是否隐藏")
        private Boolean hidden;

        @ApiModelProperty(value = "扩展字段1")
        private String extData1;

        @ApiModelProperty(value = "扩展字段2")
        private String extData2;

        @ApiModelProperty(value = "扩展字段3")
        private String extData3;

        @ApiModelProperty(value = "扩展字段4")
        private String extData4;

        @ApiModelProperty(value = "所属变量id")
        private Long pointVariableId;

        @ApiModelProperty(value = "是否启用关联属性")
        private Boolean enableAssociate;

        @ApiModelProperty(value = "是否开启选项联动控制")
        private Boolean enableViewConfig = false;

        private List<TemplateVariableViewBase> templateVariableViewBaseList = new ArrayList<>();

        @ApiModelProperty(value = "条件表达式")
        private String conditionExpression;

        @ApiModelProperty(value = "表格行记录提交记录")
        private String fieldValue;

        @ApiModelProperty(value = "字段文本值")
        private String fieldText;

        @ApiModelProperty(value = "表单录入值计量单位")
        private String unitValue = "";

        @ApiModelProperty(value = "单位文本值")
        private String unitText;

        @ApiModelProperty(value = "表格图片")
        List<ProjectTesteeFormImageVo> variableImageList = new ArrayList<>();

        @ApiModelProperty(value = "类型")
        private String type;

        @ApiModelProperty(value = "公式计算信息")
        private TemplateFormVariableRule variableRule;

        @ApiModelProperty(value = "实验室配置")
        private TemplateLabConfigExpand templateLabConfigExpand;

        @ApiModelProperty(value = "系统质疑信息")
        private ProjectTesteeChallenge systemChallenge;

        @ApiModelProperty(value = "手动质疑信息")
        private ProjectTesteeChallenge customChallenge;

        @ApiModelProperty(value = "变量是否参与计算")
        private Boolean isCompute = false;

    }
}
