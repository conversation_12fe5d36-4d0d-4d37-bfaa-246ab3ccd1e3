package com.haoys.user.domain.vo.project;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.haoys.user.model.SystemFileInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class ProjectAnnouncementVo {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "附件ids")
    private String fileIds;

    @ApiModelProperty(value = "发布状态0-未发布/1-已发布")
    private String status;

    @ApiModelProperty(value = "类型 公告1/帮助2")
    private String type;

    @ApiModelProperty(value = "发布类型 SRC-EDC-1/SRC-ePRO-2")
    private String systemType;

    @ApiModelProperty(value = "项目研究中心id")
    private Long projectOrgId;

    @ApiModelProperty(value = "中心名称")
    private String projectOrgName;

    @ApiModelProperty(value = "数据状态 0/1")
    private Boolean sealFlag;

    @ApiModelProperty(value = "创建者")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    private String updateUser;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

    @ApiModelProperty(value = "公告内容")
    private String content;

    @ApiModelProperty(value = "发布人")
    private String userName;

    private List<SystemFileInfo> systemFileInfoList = new ArrayList<>();
}
