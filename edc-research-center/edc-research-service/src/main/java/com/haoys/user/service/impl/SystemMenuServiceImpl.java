package com.haoys.user.service.impl;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.ObjectConversion;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.dto.ProjectMenuDto;
import com.haoys.user.domain.entity.ProjectMenuQuery;
import com.haoys.user.domain.entity.TreeSelect;
import com.haoys.user.domain.vo.auth.SystemMenuVo;
import com.haoys.user.mapper.ProjectMenuMapper;
import com.haoys.user.mapper.SystemMenuMapper;
import com.haoys.user.mapper.SystemRoleMapper;
import com.haoys.user.model.ProjectMenu;
import com.haoys.user.model.SystemMenu;
import com.haoys.user.model.SystemMenuExample;
import com.haoys.user.model.SystemRole;
import com.haoys.user.model.SystemUserInfo;
import com.haoys.user.service.SystemMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class SystemMenuServiceImpl implements SystemMenuService {

    @Autowired
    private SystemMenuMapper systemMenuMapper;
    @Autowired
    private SystemRoleMapper systemRoleMapper;
    @Autowired
    private ProjectMenuMapper projectMenuMapper;
    @Autowired
    private SystemUserInfoServiceImpl systemUserInfoService;


    @Override
    public CommonResult create(SystemMenu systemMenu) {
        // 菜单验证
        if (StringUtils.isNull(systemMenu.getId())) {
            return CommonResult.failed(ResultCode.REQUEST_BUSSINESS_PARAM_FAIL.getCode(), ResultCode.REQUEST_BUSSINESS_PARAM_FAIL.getMessage());
        }
        Boolean enableManageView = systemMenu.getEnableManageView() != null && systemMenu.getEnableManageView();
        if (!enableManageView && !systemMenu.getId().toString().contains("000")) {
            return CommonResult.failed(ResultCode.SYSTEM_MENU_SETTING_NOT_ALLOWED.getCode(), ResultCode.SYSTEM_MENU_SETTING_NOT_ALLOWED.getMessage());
        }
        updateLevel(systemMenu);
        systemMenu.setId(SnowflakeIdWorker.getUuid());
        systemMenu.setCreateTime(new Date());
        systemMenu.setCreateUser(SecurityUtils.getUserIdValue());
        systemMenu.setTenantId(SecurityUtils.getSystemTenantId());
        systemMenu.setPlatformId(SecurityUtils.getSystemPlatformId());
        systemMenuMapper.insertSelective(systemMenu);
        return CommonResult.success("");
    }

    /**
     * 修改菜单层级
     */
    private void updateLevel(SystemMenu systemMenu) {
        if (systemMenu.getParentId() == 0) {
            //没有父菜单时为一级菜单
            systemMenu.setLevel(0);
        } else {
            //有父菜单时选择根据父菜单level设置
            SystemMenu parentMenu = systemMenuMapper.selectByPrimaryKey(systemMenu.getParentId());
            if (parentMenu != null) {
                systemMenu.setLevel(parentMenu.getLevel() + 1);
            } else {
                systemMenu.setLevel(0);
            }
        }
    }

    @Override
    public CommonResult update(Long id, SystemMenu systemMenu) {
        systemMenu.setId(id);
        //updateLevel(systemMenu);
        systemMenuMapper.updateByPrimaryKeySelective(systemMenu);
        return CommonResult.success("");
    }

    @Override
    public SystemMenu getSystemMenuInfoByMenuId(Long id) {
        return systemMenuMapper.selectByPrimaryKey(id);
    }

    @Override
    public int delete(Long id) {
        return systemMenuMapper.deleteByPrimaryKey(id);
    }

    @Override
    public List<SystemMenu> getSystemMenuList(SystemMenu systemMenu) {
        SystemMenuExample example = new SystemMenuExample();
        example.setOrderByClause("parent_id,sort");
        return systemMenuMapper.selectByExample(example);
    }

    @Override
    public List<SystemMenuVo> getSystemPlatformMenuNameList(String userId, String groupName, String tenantId) {
        List<SystemMenuVo> dataList = new ArrayList<>();
        List<SystemMenuVo> systemPlatformMenuNameList = systemMenuMapper.getSystemPlatformMenuNameList(userId, tenantId);
        List<String> systemMenuTypeList = Arrays.asList(Constants.SYSTEM_MENU_TYPE_01, Constants.SYSTEM_MENU_TYPE_08, Constants.SYSTEM_MENU_TYPE_09, Constants.SYSTEM_MENU_TYPE_10, Constants.SYSTEM_MENU_TYPE_11);
        systemPlatformMenuNameList.forEach(systemMenuVo ->{
            SystemMenuVo systemMenuVo1 = new SystemMenuVo();
            BeanUtils.copyProperties(systemMenuVo,systemMenuVo1);
            if(systemMenuVo.getMenuType().equals(Constants.SYSTEM_MENU_TYPE_07)){
                return;
            }
            if(systemMenuTypeList.contains(systemMenuVo.getMenuType())){
                List<SystemMenu> systemUserMenuList = systemMenuMapper.getSystemUserMenuList(userId, systemMenuVo.getMenuType(), groupName);
                List<SystemMenu> systemMenuList = ObjectConversion.copy(systemUserMenuList, SystemMenu.class);
                List<SystemMenu> menuTreeList = buildMenuTree(systemMenuList);
                List<SystemMenuVo> systemMenuVoList = ObjectConversion.copy(menuTreeList, SystemMenuVo.class);
                systemMenuVo1.setChildrens(systemMenuVoList);
            }
            dataList.add(systemMenuVo1);
        });
        return dataList;
    }

    @Override
    public Set<String> selectMenuPermsByUserId(Long userId) {
        List<String> perms = systemMenuMapper.selectMenuPermsByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (String perm : perms) {
            if (StringUtils.isNotEmpty(perm)) {
                permsSet.addAll(Arrays.asList(perm.trim().split(",")));
            }
        }
        return permsSet;
    }

    @Override
    public Set<String> selectMenuPermsByProject(Long userId) {
        List<String> perms = systemMenuMapper.selectMenuPermsByProject(userId);
        Set<String> permsSet = new HashSet<>();
        for (String perm : perms) {
            if (StringUtils.isNotEmpty(perm)) {
                permsSet.addAll(Arrays.asList(perm.trim().split(",")));
            }
        }
        return permsSet;
    }

    @Override
    public List<TreeSelect> getProjectMenuTreeListByProjectId(Long projectId) {
        List<SystemMenu> menus = systemMenuMapper.selectListByProjectId(projectId);
        List<SystemMenu> menuTrees = buildMenuTree(menus);
        List<TreeSelect> treeSelectList = menuTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
        return treeSelectList;
    }

    @Override
    public List<Long> selectMenuListByRoleId(Long roleId) {
        SystemRole systemRole = systemRoleMapper.selectByPrimaryKey(roleId);
        return systemMenuMapper.selectMenuListByRoleId(roleId, systemRole.isMenuCheckStrictly());
    }

    @Override
    public List<Long> selectMenuListByProjectRoleId(Long roleId) {
        //ProjectRole projectRole = projectRoleMapper.selectByPrimaryKey(roleId);
        return systemMenuMapper.selectMenuListByProjectRoleId(roleId, false);
    }

    @Override
    public List<SystemMenu> selectProjectUserMenuListByUserId(Long projectId, String roleId, Long userId, Long parentId,
                                                              String groupName, Boolean projectManagerOwner, Boolean HideResearchMethod) {
        List<SystemMenu> systemMenuList;
        List<SystemMenu> copySystemMenuList = new ArrayList<>();
        if(projectManagerOwner){
            systemMenuList = systemMenuMapper.selectProjectUserMenuListByUserId(projectId, userId, parentId, groupName,  roleId);
        }else{
            systemMenuList = systemMenuMapper.selectProjectOrgUserMenuListByUserId(projectId, userId, parentId, groupName, roleId);
        }
        for (SystemMenu menu : systemMenuList) {
            if(menu.getParentId() != 0){
                menu.setComponent(menu.getComponent()+"/"+projectId);
            }
            if(HideResearchMethod){
                SystemMenu systemMenu = new SystemMenu();
                BeanUtils.copyProperties(menu, systemMenu);
                if(!"config_org_list".equals(menu.getName())){
                    copySystemMenuList.add(systemMenu);
                }
            }
        }
        List<SystemMenu> menuTrees = buildMenuTree(HideResearchMethod ? copySystemMenuList : systemMenuList);
        return menuTrees;
    }

    @Override
    public List<SystemMenu> getProjectRouterListByProjectIAndUserId(Long projectId, Long userId) {
        List<SystemMenu> menus = systemMenuMapper.selectProjectUserMenuListByUserId(projectId, userId, null, "", "");
        return menus;
    }

    public List<SystemMenu> buildMenuTree(List<SystemMenu> menus) {
        List<SystemMenu> returnList = new ArrayList<>();
        List<Long> tempList = new ArrayList<>();
        for (SystemMenu dept : menus) {
            tempList.add(dept.getId());
        }
        for (Iterator<SystemMenu> iterator = menus.iterator(); iterator.hasNext(); ) {
            SystemMenu menu = iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(menu.getParentId())) {
                recursionFn(menus, menu);
                returnList.add(menu);
            }
        }
        if (returnList.isEmpty()) {
            returnList = menus;
        }
        return returnList;
    }

    /**
     * 递归列表
     * @param list
     * @param systemMenu
     */
    private void recursionFn(List<SystemMenu> list, SystemMenu systemMenu) {
        List<SystemMenu> childList = getChildList(list, systemMenu);
        systemMenu.setChildrens(childList);
        for (SystemMenu tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SystemMenu> getChildList(List<SystemMenu> list, SystemMenu t) {
        List<SystemMenu> systemMenuList = new ArrayList<>();
        Iterator<SystemMenu> menuIterator = list.iterator();
        while (menuIterator.hasNext()) {
            SystemMenu systemMenu = menuIterator.next();
            if (systemMenu.getParentId().longValue() == t.getId().longValue()) {
                systemMenuList.add(systemMenu);
            }
        }
        return systemMenuList;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SystemMenu> list, SystemMenu t)
    {
        return getChildList(list, t).size() > 0 ? true : false;
    }


    /**
     * 将UmsMenu转化为UmsMenuNode并设置children属性
     */
    private SystemMenu covertMenuNode(SystemMenu menu, List<SystemMenu> menuList) {
        List<SystemMenu> children = menuList.stream().filter(subMenu -> subMenu.getParentId().equals(menu.getId())).map(subMenu -> covertMenuNode(subMenu, menuList)).collect(Collectors.toList());
        menu.setChildrens(children);
        return menu;
    }


    public List<SystemMenu> getSystemUserMenuList(String userId, String menuType, String groupName, boolean showChildrenStructure) {
        List<SystemMenu> menus = systemMenuMapper.getSystemUserMenuList(userId, menuType, groupName);
        if(!showChildrenStructure){return menus;}
        List<SystemMenu> menuTrees = buildMenuTree(menus);
        return menuTrees;
    }

    @Override
    public void batchSaveProjectMenuListByProjectId(ProjectMenuDto projectMenuDto) {
        List<ProjectMenuQuery> list = new ArrayList<>();
        for (Long menuId : projectMenuDto.getMenuId()) {
            ProjectMenuQuery projectMenuQuery = new ProjectMenuQuery();
            projectMenuQuery.setProjectId(projectMenuDto.getProjectId());
            projectMenuQuery.setMenuId(menuId);
            list.add(projectMenuQuery);
        }
        projectMenuMapper.batchSaveProjectMenuList(list);
    }

    @Override
    public List<TreeSelect> selectSystemMenuListByMenuType(String menuType, String status, Integer hidden) {
        if(StringUtils.isEmpty(menuType)){menuType = "2";}
        SystemMenuExample systemMenuExample = new SystemMenuExample();
        SystemMenuExample.Criteria criteria = systemMenuExample.createCriteria();
        criteria.andMenuTypeEqualTo(menuType);
        criteria.andLevelGreaterThan(-1);
        criteria.andHiddenEqualTo(BusinessConfig.ENABLED_STATUS);
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        systemMenuExample.setOrderByClause("id asc");
        List<SystemMenu> menuList = systemMenuMapper.selectByExample(systemMenuExample);
        String finalMenuType = menuType;
        List<SystemMenu> collect = menuList.stream().filter(item -> item.getMenuType().equals(finalMenuType)).collect(Collectors.toList());
        List<SystemMenu> menuTrees = buildMenuTree(collect);
        return menuTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    @Override
    public List<Long> selectMenuListByProjectId(Long projectId) {
        List<Long> ids = new ArrayList<>();
        List<ProjectMenu> projectMenuList= projectMenuMapper.selectProjectMenuByProjectId(projectId);
        for (ProjectMenu item : projectMenuList) {
            ids.add(item.getMenuId());
        }
        return ids;
    }

    @Override
    public void updateProjectMenuListByProjectId(ProjectMenuDto projectMenuDto) {
        projectMenuMapper.deleteProjectMenuByProjectId(projectMenuDto.getProjectId());
        batchSaveProjectMenuListByProjectId(projectMenuDto);
    }

    @Override
    public List<SystemMenu> selectMenuListByUserId(Long userId) {
        SystemMenuExample example = new SystemMenuExample();
        SystemMenuExample.Criteria criteria = example.createCriteria();
        criteria.andHiddenEqualTo(0);
        List<String> menutype = new ArrayList<>();
        menutype.add("1");
        menutype.add("2");
        menutype.add("10");
        criteria.andMenuTypeIn(menutype);
        return systemMenuMapper.selectByExample(example);
    }

    @Override
    public List<TreeSelect> buildMenuTreeSelect(List<SystemMenu> systemMenuList) {
        List<SystemMenu> menuTrees = buildMenuTree(systemMenuList);
        return menuTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }
    
    @Override
    public List<SystemMenu> treeList(String menuType) {
        return getSystemMenuTreeList(menuType, false);
    }
    
    @Override
    public List<SystemMenu> getSystemMenuTreeList(String menuType, boolean enableViewValidData) {
        String userName = SecurityUtils.getUserName();
        SystemUserInfo systemUserInfo = systemUserInfoService.getUserBaseInfoByUserName(userName);
        Boolean companyOwnerUser = systemUserInfo.getCompanyOwnerUser() != null && systemUserInfo.getCompanyOwnerUser();
        Boolean platformAdminCount = SecurityUtils.getLoginUser().isAdmin();
        List<String> menuTypeList = new ArrayList<>();
        if(StringUtils.isBlank(menuType)){
            menuTypeList.add(Constants.SYSTEM_MENU_TYPE_01);
            menuTypeList.add(Constants.SYSTEM_MENU_TYPE_02);
            menuTypeList.add(Constants.SYSTEM_MENU_TYPE_03);
            menuTypeList.add(Constants.SYSTEM_MENU_TYPE_04);
            menuTypeList.add(Constants.SYSTEM_MENU_TYPE_05);
            menuTypeList.add(Constants.SYSTEM_MENU_TYPE_06);
            menuTypeList.add(Constants.SYSTEM_MENU_TYPE_07);
            menuTypeList.add(Constants.SYSTEM_MENU_TYPE_08);
            menuTypeList.add(Constants.SYSTEM_MENU_TYPE_09);
            menuTypeList.add(Constants.SYSTEM_MENU_TYPE_10);
            menuTypeList.add(Constants.SYSTEM_MENU_TYPE_11);
        }else{
            menuTypeList = Arrays.asList(menuType);
        }
        SystemMenuExample systemMenuExample = new SystemMenuExample();
        SystemMenuExample.Criteria criteria = systemMenuExample.createCriteria();
        if(!enableViewValidData){
            criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        }
        if(platformAdminCount){
            criteria.andEnableManageViewIsNotNull();
        }else{
            criteria.andEnableManageViewEqualTo(false);
        }
        criteria.andMenuTypeIn(menuTypeList);
        if(!companyOwnerUser){
            criteria.andHiddenEqualTo(0);
        }
        List<SystemMenu> menuList = systemMenuMapper.selectByExample(systemMenuExample);
        List<SystemMenu> result = menuList.stream().filter(menu -> menu.getParentId().equals(0L)).map(menu -> covertMenuNode(menu, menuList)).collect(Collectors.toList());
        return result;
    }
}
