package com.haoys.user.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 性能监控配置类
 * 用于配置性能监控相关的参数
 * 
 * <AUTHOR>
 * @since 2025-07-06
 */
@Data
@Component
@ConfigurationProperties(prefix = "performance.monitor")
public class PerformanceMonitorConfig {

    /**
     * 访问令牌（用于无需登录的监控访问）
     */
    private String accessToken = "edc-performance-monitor-2025";

    /**
     * 令牌有效期（分钟）
     */
    private Integer tokenExpireMinutes = 120;

    /**
     * 最大连接数限制
     */
    private Integer maxConnections = 50;

    /**
     * 是否启用accessToken认证
     */
    private Boolean enableAccessToken = true;

    /**
     * 监控数据刷新间隔（秒）
     */
    private Integer refreshInterval = 5;

    /**
     * 是否启用详细日志
     */
    private Boolean enableDetailedLogging = false;
}
