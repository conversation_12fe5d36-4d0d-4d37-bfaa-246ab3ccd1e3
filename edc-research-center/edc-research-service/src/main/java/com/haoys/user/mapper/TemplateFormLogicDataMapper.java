package com.haoys.user.mapper;

import com.haoys.user.model.TemplateFormLogicData;
import com.haoys.user.model.TemplateFormLogicDataExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TemplateFormLogicDataMapper {
    long countByExample(TemplateFormLogicDataExample example);

    int deleteByExample(TemplateFormLogicDataExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TemplateFormLogicData record);

    int insertSelective(TemplateFormLogicData record);

    List<TemplateFormLogicData> selectByExample(TemplateFormLogicDataExample example);

    TemplateFormLogicData selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TemplateFormLogicData record, @Param("example") TemplateFormLogicDataExample example);

    int updateByExample(@Param("record") TemplateFormLogicData record, @Param("example") TemplateFormLogicDataExample example);

    int updateByPrimaryKeySelective(TemplateFormLogicData record);

    int updateByPrimaryKey(TemplateFormLogicData record);
}