package com.haoys.user.model;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class ProjectVisitUser implements Serializable {
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "参与者id")
    private Long testeeId;

    @ApiModelProperty(value = "参与者配置信息")
    private String testeeConfig;

    @ApiModelProperty(value = "参与者编号")
    private String testeeCode;

    @ApiModelProperty(value = "就诊卡号")
    private String visitCardNo;

    @ApiModelProperty(value = "所属研究中心id")
    private String ownerOrgId;

    @ApiModelProperty(value = "所属研究中心名称")
    private String ownerOrgName;

    @ApiModelProperty(value = "主管医生id")
    private String ownerDoctorId;

    @ApiModelProperty(value = "知情日期")
    private Date informedDate;

    @ApiModelProperty(value = "所属表单")
    private Long resourceFormId;

    @ApiModelProperty(value = "是否为医生自建病历 1-医生创建 0-患者端创建")
    private Boolean selfRecord;

    @ApiModelProperty(value = "项目绑定状态 1-绑定 0-未绑定")
    private Boolean bindResult;

    @ApiModelProperty(value = "患者绑定时间")
    private Date bindTime;

    @ApiModelProperty(value = "参与者是否需要审核 1-是 0-否")
    private Boolean reviewFlag;

    @ApiModelProperty(value = "审核状态")
    private String reviewStatus;

    @ApiModelProperty(value = "审核人")
    private String reviewUserId;

    @ApiModelProperty(value = "审核时间")
    private Date reviewTime;

    @ApiModelProperty(value = "项目患者来源 1-医生端创建 2-患者自建 3-其他来源")
    private String bindResource;

    @ApiModelProperty(value = "数据状态 0/1")
    private String status;

    @ApiModelProperty(value = "参与者研究状态 参照字典说明")
    private String researchStatus;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createUserId;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

    private static final long serialVersionUID = 1L;

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getTesteeId() {
        return testeeId;
    }

    public void setTesteeId(Long testeeId) {
        this.testeeId = testeeId;
    }

    public String getTesteeConfig() {
        return testeeConfig;
    }

    public void setTesteeConfig(String testeeConfig) {
        this.testeeConfig = testeeConfig;
    }

    public String getTesteeCode() {
        return testeeCode;
    }

    public void setTesteeCode(String testeeCode) {
        this.testeeCode = testeeCode;
    }

    public String getVisitCardNo() {
        return visitCardNo;
    }

    public void setVisitCardNo(String visitCardNo) {
        this.visitCardNo = visitCardNo;
    }

    public String getOwnerOrgId() {
        return ownerOrgId;
    }

    public void setOwnerOrgId(String ownerOrgId) {
        this.ownerOrgId = ownerOrgId;
    }

    public String getOwnerOrgName() {
        return ownerOrgName;
    }

    public void setOwnerOrgName(String ownerOrgName) {
        this.ownerOrgName = ownerOrgName;
    }

    public String getOwnerDoctorId() {
        return ownerDoctorId;
    }

    public void setOwnerDoctorId(String ownerDoctorId) {
        this.ownerDoctorId = ownerDoctorId;
    }

    public Date getInformedDate() {
        return informedDate;
    }

    public void setInformedDate(Date informedDate) {
        this.informedDate = informedDate;
    }

    public Long getResourceFormId() {
        return resourceFormId;
    }

    public void setResourceFormId(Long resourceFormId) {
        this.resourceFormId = resourceFormId;
    }

    public Boolean getSelfRecord() {
        return selfRecord;
    }

    public void setSelfRecord(Boolean selfRecord) {
        this.selfRecord = selfRecord;
    }

    public Boolean getBindResult() {
        return bindResult;
    }

    public void setBindResult(Boolean bindResult) {
        this.bindResult = bindResult;
    }

    public Date getBindTime() {
        return bindTime;
    }

    public void setBindTime(Date bindTime) {
        this.bindTime = bindTime;
    }

    public Boolean getReviewFlag() {
        return reviewFlag;
    }

    public void setReviewFlag(Boolean reviewFlag) {
        this.reviewFlag = reviewFlag;
    }

    public String getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(String reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getReviewUserId() {
        return reviewUserId;
    }

    public void setReviewUserId(String reviewUserId) {
        this.reviewUserId = reviewUserId;
    }

    public Date getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(Date reviewTime) {
        this.reviewTime = reviewTime;
    }

    public String getBindResource() {
        return bindResource;
    }

    public void setBindResource(String bindResource) {
        this.bindResource = bindResource;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getResearchStatus() {
        return researchStatus;
    }

    public void setResearchStatus(String researchStatus) {
        this.researchStatus = researchStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", projectId=").append(projectId);
        sb.append(", testeeId=").append(testeeId);
        sb.append(", testeeConfig=").append(testeeConfig);
        sb.append(", testeeCode=").append(testeeCode);
        sb.append(", visitCardNo=").append(visitCardNo);
        sb.append(", ownerOrgId=").append(ownerOrgId);
        sb.append(", ownerOrgName=").append(ownerOrgName);
        sb.append(", ownerDoctorId=").append(ownerDoctorId);
        sb.append(", informedDate=").append(informedDate);
        sb.append(", resourceFormId=").append(resourceFormId);
        sb.append(", selfRecord=").append(selfRecord);
        sb.append(", bindResult=").append(bindResult);
        sb.append(", bindTime=").append(bindTime);
        sb.append(", reviewFlag=").append(reviewFlag);
        sb.append(", reviewStatus=").append(reviewStatus);
        sb.append(", reviewUserId=").append(reviewUserId);
        sb.append(", reviewTime=").append(reviewTime);
        sb.append(", bindResource=").append(bindResource);
        sb.append(", status=").append(status);
        sb.append(", researchStatus=").append(researchStatus);
        sb.append(", createTime=").append(createTime);
        sb.append(", createUserId=").append(createUserId);
        sb.append(", tenantId=").append(tenantId);
        sb.append(", platformId=").append(platformId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}