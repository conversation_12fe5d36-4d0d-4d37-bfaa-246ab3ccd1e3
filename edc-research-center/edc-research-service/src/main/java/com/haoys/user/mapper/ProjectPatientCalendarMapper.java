package com.haoys.user.mapper;

import com.haoys.user.model.ProjectPatientCalendar;
import com.haoys.user.model.ProjectPatientCalendarExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectPatientCalendarMapper {
    long countByExample(ProjectPatientCalendarExample example);

    int deleteByExample(ProjectPatientCalendarExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectPatientCalendar record);

    int insertSelective(ProjectPatientCalendar record);

    List<ProjectPatientCalendar> selectByExample(ProjectPatientCalendarExample example);

    ProjectPatientCalendar selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectPatientCalendar record, @Param("example") ProjectPatientCalendarExample example);

    int updateByExample(@Param("record") ProjectPatientCalendar record, @Param("example") ProjectPatientCalendarExample example);

    int updateByPrimaryKeySelective(ProjectPatientCalendar record);

    int updateByPrimaryKey(ProjectPatientCalendar record);
}