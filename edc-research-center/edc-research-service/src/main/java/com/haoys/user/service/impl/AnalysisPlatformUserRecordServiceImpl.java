package com.haoys.user.service.impl;

import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.mapper.AnalysisPlatformUserRecordMapper;
import com.haoys.user.model.AnalysisPlatformUserRecord;
import com.haoys.user.service.AnalysisPlatformUserRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class AnalysisPlatformUserRecordServiceImpl implements AnalysisPlatformUserRecordService {

    private final AnalysisPlatformUserRecordMapper analysisPlatformUserRecordMapper;

    @Value("${spring.profiles.active}")
    private String profileEnv;


    @Override
    public void saveAnalysisPlatformUserAccount(String operator, String username, String systemTenantId, String systemPlatformId) {
        // 查询账号是否已经完成同步
        AnalysisPlatformUserRecord analysisPlatformUserRecord = analysisPlatformUserRecordMapper.getPlatformUserAccountByUserName(username, systemPlatformId);
        if(analysisPlatformUserRecord == null){
            AnalysisPlatformUserRecord record = new AnalysisPlatformUserRecord();
            record.setId(SnowflakeIdWorker.getUuid());
            record.setUserId(Long.parseLong(operator));
            record.setUserName(username);
            record.setDeployEnv(profileEnv);
            record.setCreateUserId(operator);
            record.setCreateTime(new Date());
            record.setTenantId(systemTenantId);
            record.setPlatformId(systemPlatformId);
            analysisPlatformUserRecordMapper.insertSelective(record);
        }

    }
}
