package com.haoys.user.domain.vo.testee;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.haoys.user.domain.vo.ecrf.TemplateTableRowHeadVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Data
public class ProjectTesteeVo {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "参与者用户id")
    private Long id;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "系统用户标识id")
    private Long userId;

    @ApiModelProperty(value = "bdp用户id")
    private String bdpUserId;

    @ApiModelProperty(value = "姓名")
    private String realName = "";

    @ApiModelProperty(value = "就诊卡号")
    private String visitCardNo;
    
    @ApiModelProperty(value = "姓名缩写")
    private String acronym;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "参与者编号")
    private String testeeCode;

    @ApiModelProperty(value = "所属中心id")
    private String ownerOrgId;

    @ApiModelProperty(value = "所属中心名称")
    private String ownerOrgName = "";

    @ApiModelProperty(value = "性别 设置参数为：男、女")
    private String gender;

    @ApiModelProperty(value = "主管医生")
    private String ownerDoctorId;

    @ApiModelProperty(value = "主管医生名称")
    private String ownerDoctorName = "";

    @ApiModelProperty(value = "身份证号码")
    private String idcard;

    @ApiModelProperty(value = "出生日期")
    private Date birthday;

    @ApiModelProperty(value = "联系方式")
    private String contant;

    @ApiModelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "身高")
    private Double height;

    @ApiModelProperty(value = "体重")
    private Double weight;

    @ApiModelProperty(value = "数据状态")
    private String status;

    @ApiModelProperty(value = "参与者扩展信息")
    //@JsonRawValue
    private String expand;

    @ApiModelProperty(value = "访视集合")
    private List<VisitConfig> visitList;

    @ApiModelProperty(value = "参与者动态字段集合")
    private List<FormOptions> dataList;

    @ApiModelProperty(value = "所属项目名称")
    private String projectName;

    @ApiModelProperty(value = "是否参与者审核")
    private Boolean reviewFlag;

    @ApiModelProperty(value = "医生自建病历")
    private Boolean selfRecord = false;

    @ApiModelProperty(value = "审核状态")
    private String reviewStatus;

    @ApiModelProperty(value = "审核人")
    private String reviewUserId;

    @ApiModelProperty(value = "审核时间")
    private Date reviewTime;

    @ApiModelProperty(value = "项目患者来源 1-医生端创建 2-患者自建 3-其他来源")
    private String bindResource;

    @ApiModelProperty(value = "项目绑定状态 1-绑定 0-未绑定")
    private Boolean bindResult;

    @ApiModelProperty(value = "患者添加时间")
    private Date createTime;

    @Data
    public static class VisitConfig {

        @ApiModelProperty(value = "访视id")
        private String visitId;
        @ApiModelProperty(value = "访视名称")
        private String visitName;
        @ApiModelProperty(value = "未关闭质疑总量")
        private String changleCount;
        @ApiModelProperty(value = "完成状态标识 1-未录入 2-录入中 3-已完成")
        private String complateStatus = "";

    }

    @Data
    public static class FormOptions {

        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "字段名称")
        private Long id;
        @ApiModelProperty(value = "字段名称")
        private String label;
        @ApiModelProperty(value = "组件类型 input checkbox select radio")
        private String type;
        @ApiModelProperty(value = "字段key")
        private String key;
        @ApiModelProperty(value = "提示语")
        private String placeholder;
        @ApiModelProperty(value = "默认值")
        private String defaultValue;
        @ApiModelProperty(value = "是否必填项")
        private Boolean required;
        @ApiModelProperty(value = "下拉框、单选按钮等填充数据")
        private String options;
        @ApiModelProperty(value = "表单输入值")
        private String value;

        @ApiModelProperty(value = "表格head定义规则")
        private List<TemplateTableRowHeadVo> dataRowHeadList = new ArrayList<>();

        @ApiModelProperty(value = "扩展属性-行记录提交数据")
        private Object rows;

        @ApiModelProperty(value = "表格行记录描述")
        private Object RowDesc;

    }

}
