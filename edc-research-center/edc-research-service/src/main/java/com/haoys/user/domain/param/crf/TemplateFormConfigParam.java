package com.haoys.user.domain.param.crf;

import com.haoys.user.common.constants.Constants;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class TemplateFormConfigParam implements Serializable {


    @ApiModelProperty(value = "模版id")
    private Long templateId;

    @ApiModelProperty(value = "模版名称")
    private String templateName;

    @ApiModelProperty(value = "模版code")
    private String templateCode;

    @ApiModelProperty(value = "模版描述")
    private String description;

    @ApiModelProperty(value = "将当前表单另存为表单模版")
    private Boolean createTemplate = false;

    @ApiModelProperty(value = "复制表单模版")
    private Boolean copyTemplateForm = false;

    @ApiModelProperty(value = "表单模版类型")
    private String configType = Constants.PROJECT_TEMPLATE_CONFIG_TYPE_02;

    @ApiModelProperty(value = "另存为表单的原始id")
    private Long copyFormId;

    // @NotNull(message = "项目id不能为空")
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "表单项id-编辑表单时设置")
    private Long formId;

    @ApiModelProperty(value = "表单分类id-必须设置-->需求变更 可选项")
    private String groupName;
    
    @ApiModelProperty(value = "表单名称")
    private String formName;

    @ApiModelProperty(value = "表单code")
    private String formCode;

    @ApiModelProperty(value = "表单配置信息")
    private String formConfig;

    @ApiModelProperty(value = "表单类型")
    private String formType;

    @ApiModelProperty(value = "版本号")
    private String version;

    @ApiModelProperty(value = "是否上传原始资料")
    private Boolean uploadResourceFile = true;

    @ApiModelProperty(value = "是否开启患者端")
    private Boolean openEpro = false;

    @ApiModelProperty(value = "是否全局表单")
    private Boolean globalScope = false;

    @ApiModelProperty(value = "是否患者专用表单")
    private Boolean testeeForm = false;

    @ApiModelProperty(value = "是否不良事件等定制表单")
    private Boolean customForm;
    
    @ApiModelProperty(value = "是否入组表单")
    private Boolean joinGroup = false;

    @ApiModelProperty(value = "表单排序")
    private Integer sort;

    @ApiModelProperty(value = "数据状态0/1")
    private String status;

    @ApiModelProperty(value = "表单变量集合")
    private List<TemplateFormDetailParam> formDetailParamList = new ArrayList<>();

    @ApiModelProperty(value = "操作人")
    private String createUser;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;


}
