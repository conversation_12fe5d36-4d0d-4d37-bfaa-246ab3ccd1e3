package com.haoys.user.domain.dto;

import com.haoys.user.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 成员管理导出异常信息
 */
@Data
public class ProjectUserExportDto implements Serializable {

    private static final long serialVersionUID = 6599209531769719516L;

    /** 用户名称 */
    @Excel(name = "姓名")
    private String realName;

    /** 所属中心 */
    @Excel(name = "所属中心")
    private String orginfo;

    /** 所属科室 */
    @Excel(name = "所属科室")
    private String department;

    /** 职称 */
    @Excel(name = "职称")
    private String positional;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String mobile;

    /** 角色名称 */
    @Excel(name = "角色")
    private String rolename;

    @Excel(name = "错误描述")
    private String errorMessage;
}
