package com.haoys.user.domain.vo.testee;

import com.haoys.user.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ProjectTesteeImportVo implements Serializable {

    @ApiModelProperty(value = "系统用户id")
    private Long userId;

    @Excel(name = "参与者编号")
    @ApiModelProperty(value = "参与者编号")
    private String code;

    @Excel(name = "姓名")
    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "所属中心id")
    private String ownerOrgId;

    @Excel(name = "所属中心名称")
    @ApiModelProperty(value = "所属中心名称")
    private String ownerOrgName;

    @Excel(name = "性别")
    @ApiModelProperty(value = "性别 男、女")
    private String gender;

    @ApiModelProperty(value = "主管医生id")
    private String ownerDoctor;

    @Excel(name = "主管医生")
    @ApiModelProperty(value = "主管医生姓名")
    private String ownerDoctorName;

    @Excel(name = "出生日期"/*, dateFormat = "yyyy-MM-dd"*/)
    @ApiModelProperty(value = "出生日期")
    private Date birthdayViewValue;

    @Excel(name = "联系方式")
    @ApiModelProperty(value = "联系方式")
    private String contant;

    @ApiModelProperty(value = "创建人")
    private String createUser;

}