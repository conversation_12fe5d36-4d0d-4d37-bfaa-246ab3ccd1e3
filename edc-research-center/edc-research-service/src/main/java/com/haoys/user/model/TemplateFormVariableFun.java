package com.haoys.user.model;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

public class TemplateFormVariableFun implements Serializable {
    @ApiModelProperty(value = "变量可设置函数的id")
    private Integer id;

    @ApiModelProperty(value = "函数名称")
    private String funName;

    @ApiModelProperty(value = "函数")
    private String fun;

    @ApiModelProperty(value = "函数例子")
    private String funExample;

    @ApiModelProperty(value = "函数说明")
    private String funDesc;

    @ApiModelProperty(value = "函数类型")
    private Integer funType;

    @ApiModelProperty(value = "状态")
    private Integer status;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getFunName() {
        return funName;
    }

    public void setFunName(String funName) {
        this.funName = funName;
    }

    public String getFun() {
        return fun;
    }

    public void setFun(String fun) {
        this.fun = fun;
    }

    public String getFunExample() {
        return funExample;
    }

    public void setFunExample(String funExample) {
        this.funExample = funExample;
    }

    public String getFunDesc() {
        return funDesc;
    }

    public void setFunDesc(String funDesc) {
        this.funDesc = funDesc;
    }

    public Integer getFunType() {
        return funType;
    }

    public void setFunType(Integer funType) {
        this.funType = funType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", funName=").append(funName);
        sb.append(", fun=").append(fun);
        sb.append(", funExample=").append(funExample);
        sb.append(", funDesc=").append(funDesc);
        sb.append(", funType=").append(funType);
        sb.append(", status=").append(status);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}