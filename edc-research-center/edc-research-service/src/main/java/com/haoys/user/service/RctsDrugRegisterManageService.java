package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.domain.param.rcts.ModifyDrugParam;
import com.haoys.user.domain.param.rcts.SaveDrugParam;
import com.haoys.user.domain.param.rcts.saveDrugDistributeParam;
import com.haoys.user.domain.vo.rcts.DrugDistributeVo;
import com.haoys.user.domain.vo.rcts.DrugRegisterVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/6 14:25
 */
public interface RctsDrugRegisterManageService {

    int saveDrugRegister(SaveDrugParam param);

    CommonPage<DrugRegisterVo> getDrugRegisterPage(String projectId, String warningStatus, String materialStatus,
                                                   String materialCode, String materialName,
                                                   int pageNum, int pageSize);

    DrugRegisterVo getDrugRegister(Long id);

    int modifyDrugRegister(ModifyDrugParam param);

    int removeDrugRegister(Long id);

    List<Map<String, String>> getMaterialCode(String projectId);

    void checkExpirationDrug();

    String saveDrugDistribute(saveDrugDistributeParam param);


    CommonPage<DrugDistributeVo> getDrugDistributePage(String projectId, String testeeCode, String acronym,
                                                       String queryMaterial, int pageNum, int pageSize );

    DrugDistributeVo getDrugDistribute(Long id);

}
