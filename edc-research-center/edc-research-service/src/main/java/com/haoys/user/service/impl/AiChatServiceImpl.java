package com.haoys.user.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.dashscope.aigc.generation.Generation;
import com.alibaba.dashscope.aigc.generation.GenerationParam;
import com.alibaba.dashscope.aigc.generation.GenerationResult;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.Role;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.core.domain.model.LoginUserInfo;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.util.JwtTokenHelper;
import com.haoys.user.config.AiChatConfig;
import com.haoys.user.domain.dto.AiChatRequestDto;
import com.haoys.user.domain.entity.AiChatMessage;
import com.haoys.user.domain.entity.AiChatSession;
import com.haoys.user.domain.entity.AiModelConfig;
import com.haoys.user.domain.entity.AiTokenUsage;
import com.haoys.user.domain.vo.AiChatResponseVo;
import com.haoys.user.domain.vo.AiChatSessionVo;
import com.haoys.user.mapper.AiChatMessageMapper;
import com.haoys.user.mapper.AiChatSessionMapper;
import com.haoys.user.mapper.AiModelConfigMapper;
import com.haoys.user.mapper.AiTokenUsageMapper;
import com.haoys.user.service.AiChatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.Executor;
import javax.imageio.ImageIO;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xslf.usermodel.XSLFSlide;
import org.apache.poi.xslf.usermodel.XSLFShape;
import org.apache.poi.xslf.usermodel.XSLFTextShape;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * AI聊天服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Slf4j
@Service
public class AiChatServiceImpl implements AiChatService {

    /**
     * 匿名用户标识
     */
    private static final String ANONYMOUS_USER = "anonymousUser";

    /**
     * 匿名用户每日使用限制
     */
    private static final int ANONYMOUS_DAILY_LIMIT = 20;
    
    @Autowired
    private AiChatConfig aiChatConfig;
    
    @Autowired
    @Qualifier("aiChatSessionMapper")
    private AiChatSessionMapper sessionMapper;

    @Autowired
    @Qualifier("aiChatMessageMapper")
    private AiChatMessageMapper messageMapper;

    @Autowired
    @Qualifier("aiModelConfigMapper")
    private AiModelConfigMapper modelConfigMapper;

    @Autowired
    @Qualifier("aiTokenUsageMapper")
    private AiTokenUsageMapper tokenUsageMapper;

    @Autowired
    private JwtTokenHelper jwtTokenHelper;

    @Autowired
    @Qualifier("taskExecutor")
    private Executor taskExecutor;
    
    @Override
    public CommonResult<AiChatResponseVo> sendMessage(AiChatRequestDto request) {
        try {
            // 1. 参数验证
            if (StrUtil.isBlank(request.getContent())) {
                return CommonResult.failed("消息内容不能为空");
            }

            // 2. 检查用户每日使用限制（仅限制匿名用户）
            String currentUserId = getUserIdFromRequest();
            String limitCheckResult = checkUserDailyUsageLimitWithMessage(currentUserId);
            if (limitCheckResult != null) {
                return CommonResult.failed(limitCheckResult);
            }
            
            // 3. 获取或创建会话
            AiChatSession session = getOrCreateSession(request);
            if (session == null) {
                return CommonResult.failed("创建会话失败");
            }

            // 4. 获取模型配置
            AiModelConfig modelConfig = getModelConfig(request.getModelType(), request.getModelName());
            if (modelConfig == null) {
                return CommonResult.failed("模型配置不存在或未启用");
            }

            // 5. 保存用户消息
            AiChatMessage userMessage = saveUserMessage(session, request);

            // 6. 构建上下文消息
            List<Message> contextMessages = buildContextMessages(session.getSessionId(), request);

            // 7. 调用AI模型
            long startTime = System.currentTimeMillis();
            GenerationResult result = callAiModel(modelConfig, contextMessages);
            int responseTime = (int) (System.currentTimeMillis() - startTime);

            // 8. 处理AI响应
            String aiResponse = extractAiResponse(result);
            if (StrUtil.isBlank(aiResponse)) {
                return CommonResult.failed("AI模型返回空响应");
            }

            // 9. 保存AI消息
            AiChatMessage aiMessage = saveAiMessage(session, aiResponse, result, responseTime);

            // 10. 更新会话统计
            updateSessionStats(session, result);

            // 11. 记录Token使用
            recordTokenUsage(session, userMessage, aiMessage, result, modelConfig);

            // 12. 构建响应
            AiChatResponseVo response = buildResponse(session, aiMessage, result, responseTime);
            
            return CommonResult.success(response, "消息发送成功");
            
        } catch (Exception e) {
            log.error("发送消息失败", e);
            return CommonResult.failed("发送消息失败: " + e.getMessage());
        }
    }
    
    @Override
    public SseEmitter sendMessageStream(AiChatRequestDto request) {
        // 设置更长的超时时间，支持长时间对话
        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时

        // 设置超时和完成回调
        emitter.onTimeout(() -> {
            log.warn("流式响应超时，会话ID: {}", request.getSessionId());
            emitter.complete();
        });

        emitter.onError((throwable) -> {
            log.error("流式响应发生错误，会话ID: {}, 错误: {}", request.getSessionId(), throwable.getMessage());
        });

        // 异步处理流式响应
        CompletableFuture.runAsync(() -> {
            try {
                log.info("开始处理流式消息，会话ID: {}, 用户消息: {}", request.getSessionId(),
                        request.getContent().substring(0, Math.min(request.getContent().length(), 100)));

                // 1. 检查用户每日使用限制（仅限制匿名用户）
                String currentUserId = getUserIdFromRequest();
                String limitCheckResult = checkUserDailyUsageLimitWithMessage(currentUserId);
                if (limitCheckResult != null) {
                    sendErrorResponse(emitter, "USAGE_LIMIT_EXCEEDED", limitCheckResult);
                    return;
                }

                // 2. 参数验证
                if (StrUtil.isBlank(request.getContent())) {
                    sendErrorResponse(emitter, "INVALID_PARAMETER", "消息内容不能为空");
                    return;
                }

                // 3. 获取或创建会话
                AiChatSession session = getOrCreateSession(request);
                if (session == null) {
                    sendErrorResponse(emitter, "SESSION_ERROR", "创建会话失败");
                    return;
                }

                // 4. 获取模型配置
                AiModelConfig modelConfig = getModelConfig(request.getModelType(), request.getModelName());
                if (modelConfig == null) {
                    sendErrorResponse(emitter, "MODEL_ERROR", "模型配置不存在: " + request.getModelType() + "/" + request.getModelName());
                    return;
                }

                // 5. 保存用户消息
                AiChatMessage userMessage = saveUserMessage(session, request);
                log.debug("用户消息已保存，消息ID: {}", userMessage.getMessageId());

                // 6. 构建上下文消息
                List<Message> contextMessages = buildContextMessages(session.getSessionId(), request);
                log.debug("上下文消息构建完成，消息数量: {}", contextMessages.size());

                // 7. 发送开始响应
                sendStartResponse(emitter, session);

                // 8. 流式调用AI模型
                callAiModelStream(modelConfig, contextMessages, emitter, session, userMessage);

            } catch (Exception e) {
                log.error("流式消息发送失败，会话ID: {}, 错误: {}", request.getSessionId(), e.getMessage(), e);
                sendErrorResponse(emitter, "INTERNAL_ERROR", "系统内部错误: " + e.getMessage());
            }
        }, taskExecutor);

        return emitter;
    }

    /**
     * 发送开始响应
     */
    private void sendStartResponse(SseEmitter emitter, AiChatSession session) {
        try {
            AiChatResponseVo startResponse = new AiChatResponseVo();
            startResponse.setSessionId(session.getSessionId());
            startResponse.setContent("");
            startResponse.setIsStream(true);
            startResponse.setStatus("started");


            emitter.send(SseEmitter.event()
                    .name("message")
                    .data(JSONUtil.toJsonStr(startResponse))
                    .reconnectTime(3000));

            log.debug("发送开始响应，会话ID: {}", session.getSessionId());
        } catch (IOException e) {
            log.error("发送开始响应失败: {}", e.getMessage());
            throw new RuntimeException("发送开始响应失败", e);
        }
    }

    /**
     * 发送错误响应
     */
    private void sendErrorResponse(SseEmitter emitter, String errorCode, String errorMessage) {
        try {
            AiChatResponseVo errorResponse = new AiChatResponseVo();
            errorResponse.setStatus("error");
            errorResponse.setErrorMessage(errorMessage);

            emitter.send(SseEmitter.event()
                    .name("error")
                    .data(JSONUtil.toJsonStr(errorResponse)));

            emitter.complete();
            log.warn("发送错误响应: {} - {}", errorCode, errorMessage);
        } catch (IOException e) {
            log.error("发送错误响应失败: {}", e.getMessage());
            emitter.completeWithError(e);
        }
    }
    
    @Override
    public CommonResult<AiChatResponseVo> uploadAndParseFile(String sessionId, MultipartFile file, String question) {
        try {
            // 1. 文件验证
            if (file == null || file.isEmpty()) {
                return CommonResult.failed("文件不能为空");
            }
            
            // 2. 文件类型验证
            String fileName = file.getOriginalFilename();
            String fileExtension = getFileExtension(fileName);
            if (!isFileTypeSupported(fileExtension)) {
                return CommonResult.failed("不支持的文件类型: " + fileExtension);
            }
            
            // 3. 文件大小验证
            if (file.getSize() > aiChatConfig.getMaxFileSize() * 1024 * 1024) {
                return CommonResult.failed("文件大小超过限制: " + aiChatConfig.getMaxFileSize() + "MB");
            }
            
            // 4. 解析文件内容
            String fileContent = parseFileContent(file);
            if (StrUtil.isBlank(fileContent)) {
                return CommonResult.failed("文件解析失败或内容为空");
            }
            
            // 5. 构建聊天请求
            AiChatRequestDto request = new AiChatRequestDto();
            request.setSessionId(sessionId);
            request.setContent(buildFileAnalysisPrompt(fileName, fileContent, question));
            
            // 6. 发送消息
            return sendMessage(request);
            
        } catch (Exception e) {
            log.error("文件上传解析失败", e);
            return CommonResult.failed("文件上传解析失败: " + e.getMessage());
        }
    }
    
    @Override
    public CommonResult<AiChatSession> createSession(String title, String modelType, String modelName) {
        try {
            String userId = getUserIdFromRequest();
            String userName = getUserNameFromRequest();

            // 检查用户每日使用限制（仅限制匿名用户）
            String limitCheckResult = checkUserDailyUsageLimitWithMessage(userId);
            if (limitCheckResult != null) {
                return CommonResult.failed(limitCheckResult);
            }

            // 检查用户会话数量限制
            int userSessionCount = sessionMapper.countByUserId(userId, 1);
            if (userSessionCount >= aiChatConfig.getMaxSessionsPerUser()) {
                return CommonResult.failed("会话数量已达上限: " + aiChatConfig.getMaxSessionsPerUser());
            }
            
            // 创建会话
            AiChatSession session = new AiChatSession();
            session.setSessionId(IdUtil.simpleUUID());
            session.setUserId(userId);
            session.setUserName(userName);
            session.setTitle(StrUtil.isBlank(title) ? "新对话" : title);
            session.setModelType(StrUtil.isBlank(modelType) ? aiChatConfig.getDefaultModelType() : modelType);
            session.setModelName(StrUtil.isBlank(modelName) ? aiChatConfig.getDefaultModelName() : modelName);
            session.setStatus(1);
            session.setTotalTokens(0L);
            session.setTotalCost(BigDecimal.ZERO);
            session.setMessageCount(0);
            session.setCreateTime(new Date());
            session.setUpdateTime(new Date());
            
            sessionMapper.insert(session);
            
            return CommonResult.success(session, "会话创建成功");
            
        } catch (Exception e) {
            log.error("创建会话失败", e);
            return CommonResult.failed("创建会话失败: " + e.getMessage());
        }
    }
    
    // 私有辅助方法
    private AiChatSession getOrCreateSession(AiChatRequestDto request) {
        if (StrUtil.isNotBlank(request.getSessionId())) {
            AiChatSession session = sessionMapper.selectBySessionId(request.getSessionId());
            if (session != null && session.getStatus() == 1) {
                return session;
            }
        }
        
        // 创建新会话
        CommonResult<AiChatSession> result = createSession(request.getTitle(), request.getModelType(), request.getModelName());
        return result.getData();
    }
    
    private AiModelConfig getModelConfig(String modelType, String modelName) {
        if (StrUtil.isNotBlank(modelType) && StrUtil.isNotBlank(modelName)) {
            return modelConfigMapper.selectByTypeAndName(modelType, modelName);
        }
        
        // 使用默认模型
        return modelConfigMapper.selectByTypeAndName(
            aiChatConfig.getDefaultModelType(), 
            aiChatConfig.getDefaultModelName()
        );
    }
    
    private AiChatMessage saveUserMessage(AiChatSession session, AiChatRequestDto request) {
        AiChatMessage message = new AiChatMessage();
        message.setMessageId(IdUtil.simpleUUID());
        message.setSessionId(session.getSessionId());
        message.setUserId(session.getUserId());
        message.setRole("user");
        message.setContent(request.getContent());
        message.setContentType("text");
        message.setTokens(0); // 用户消息Token数暂时设为0
        message.setCost(BigDecimal.ZERO);
        message.setIsStream(request.getStream());
        message.setCreateTime(new Date());

        messageMapper.insert(message);
        return message;
    }

    private List<Message> buildContextMessages(String sessionId, AiChatRequestDto request) {
        List<Message> messages = new ArrayList<>();

        // 添加系统消息
        if (StrUtil.isNotBlank(request.getSystemPrompt())) {
            messages.add(Message.builder()
                .role(Role.SYSTEM.getValue())
                .content(request.getSystemPrompt())
                .build());
        }

        // 获取历史消息作为上下文
        List<AiChatMessage> contextMessages = messageMapper.selectContextMessages(sessionId, 10);
        for (AiChatMessage msg : contextMessages) {
            messages.add(Message.builder()
                .role(msg.getRole())
                .content(msg.getContent())
                .build());
        }

        // 添加当前用户消息
        messages.add(Message.builder()
            .role(Role.USER.getValue())
            .content(request.getContent())
            .build());

        return messages;
    }

    private GenerationResult callAiModel(AiModelConfig modelConfig, List<Message> messages)
            throws ApiException, NoApiKeyException, InputRequiredException {
        Generation gen = new Generation();

        GenerationParam param = GenerationParam.builder()
            .apiKey(modelConfig.getApiKey())
            .model(modelConfig.getModelName())
            .messages(messages)
            .maxTokens(modelConfig.getMaxTokens())
            .temperature(modelConfig.getTemperature().floatValue())
            .topP(modelConfig.getTopP().doubleValue())
            .resultFormat(GenerationParam.ResultFormat.MESSAGE)
            .build();

        return gen.call(param);
    }

    private String extractAiResponse(GenerationResult result) {
        if (result != null && result.getOutput() != null && result.getOutput().getChoices() != null) {
            return result.getOutput().getChoices().stream()
                .findFirst()
                .map(choice -> choice.getMessage().getContent())
                .orElse("");
        }
        return "";
    }

    private AiChatMessage saveAiMessage(AiChatSession session, String content,
                                       GenerationResult result, int responseTime) {
        AiChatMessage message = new AiChatMessage();
        message.setMessageId(IdUtil.simpleUUID());
        message.setSessionId(session.getSessionId());
        message.setUserId(session.getUserId());
        message.setRole("assistant");
        message.setContent(content);
        message.setContentType("text");
        message.setTokens(getTokensFromResult(result));
        message.setCost(calculateCost(result, session.getModelType()));
        message.setResponseTime(responseTime);
        message.setIsStream(false);
        message.setCreateTime(new Date());

        messageMapper.insert(message);
        return message;
    }

    private void updateSessionStats(AiChatSession session, GenerationResult result) {
        int messageCount = messageMapper.countBySessionId(session.getSessionId());
        int tokens = getTokensFromResult(result);
        BigDecimal cost = calculateCost(result, session.getModelType());

        session.setMessageCount(messageCount);
        session.setTotalTokens(session.getTotalTokens() + tokens);
        session.setTotalCost(session.getTotalCost().add(cost));
        session.setLastMessageTime(new Date());
        session.setUpdateTime(new Date());

        sessionMapper.updateBySessionId(session);
    }

    private void recordTokenUsage(AiChatSession session, AiChatMessage userMessage,
                                 AiChatMessage aiMessage, GenerationResult result, AiModelConfig modelConfig) {
        AiTokenUsage usage = new AiTokenUsage();
        usage.setUserId(session.getUserId());
        usage.setUserName(session.getUserName());
        usage.setModelType(session.getModelType());
        usage.setModelName(session.getModelName());
        usage.setSessionId(session.getSessionId());
        usage.setMessageId(aiMessage.getMessageId());

        // 从结果中提取Token信息
        if (result.getUsage() != null) {
            usage.setInputTokens(result.getUsage().getInputTokens());
            usage.setOutputTokens(result.getUsage().getOutputTokens());
            usage.setTotalTokens(result.getUsage().getTotalTokens());
        }

        // 计算成本
        usage.setInputCost(modelConfig.getTokenPriceInput().multiply(new BigDecimal(usage.getInputTokens())));
        usage.setOutputCost(modelConfig.getTokenPriceOutput().multiply(new BigDecimal(usage.getOutputTokens())));
        usage.setTotalCost(usage.getInputCost().add(usage.getOutputCost()));

        Date now = new Date();
        usage.setUsageDate(now);
        usage.setUsageHour(Calendar.getInstance().get(Calendar.HOUR_OF_DAY));
        usage.setCreateTime(now);

        tokenUsageMapper.insert(usage);
    }

    private AiChatResponseVo buildResponse(AiChatSession session, AiChatMessage aiMessage,
                                          GenerationResult result, int responseTime) {
        AiChatResponseVo response = new AiChatResponseVo();
        response.setSessionId(session.getSessionId());
        response.setMessageId(aiMessage.getMessageId());
        response.setContent(aiMessage.getContent());
        response.setIsStream(false);
        response.setStatus("success");
        response.setModelType(session.getModelType());
        response.setModelName(session.getModelName());
        response.setResponseTime(responseTime);
        response.setCreateTime(aiMessage.getCreateTime());

        // Token使用情况
        AiChatResponseVo.TokenUsage tokenUsage = new AiChatResponseVo.TokenUsage();
        if (result.getUsage() != null) {
            tokenUsage.setInputTokens(result.getUsage().getInputTokens());
            tokenUsage.setOutputTokens(result.getUsage().getOutputTokens());
            tokenUsage.setTotalTokens(result.getUsage().getTotalTokens());
            tokenUsage.setTotalCost(aiMessage.getCost());
        }
        response.setTokenUsage(tokenUsage);

        // 会话信息
        AiChatResponseVo.SessionInfo sessionInfo = new AiChatResponseVo.SessionInfo();
        sessionInfo.setTitle(session.getTitle());
        sessionInfo.setMessageCount(session.getMessageCount());
        sessionInfo.setTotalTokens(session.getTotalTokens());
        sessionInfo.setTotalCost(session.getTotalCost());
        sessionInfo.setLastMessageTime(session.getLastMessageTime());
        response.setSessionInfo(sessionInfo);

        return response;
    }

    /**
     * 流式调用AI模型
     * 实现真正的流式响应，提供更好的用户体验
     */
    private void callAiModelStream(AiModelConfig modelConfig, List<Message> messages,
                                  SseEmitter emitter, AiChatSession session, AiChatMessage userMessage) {
        long startTime = System.currentTimeMillis();
        StringBuilder fullContent = new StringBuilder();
        GenerationResult result = null;

        try {
            log.info("开始流式调用AI模型，模型: {}/{}, 会话: {}",
                    modelConfig.getModelType(), modelConfig.getModelName(), session.getSessionId());

            // 调用AI模型获取完整响应
            result = callAiModel(modelConfig, messages);
            String content = extractAiResponse(result);

            if (StrUtil.isBlank(content)) {
                throw new RuntimeException("AI模型返回空内容");
            }

            // 智能分割内容进行流式输出
            List<String> chunks = splitContentForStreaming(content);

            for (int i = 0; i < chunks.size(); i++) {
                String chunk = chunks.get(i);
                fullContent.append(chunk);

                // 构建流式响应
                AiChatResponseVo response = new AiChatResponseVo();
                response.setSessionId(session.getSessionId());
                response.setContent(fullContent.toString());
                response.setIsStream(true);
                response.setStatus("streaming");

                // 发送数据
                emitter.send(SseEmitter.event()
                        .data(JSONUtil.toJsonStr(response)));

                // 动态延迟，让用户感受到自然的打字效果
                try {
                    int delay = calculateStreamDelay(chunk);
                    Thread.sleep(delay);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("流式输出被中断");
                    break;
                }
            }

            // 保存AI消息到数据库
            int tokens = getTokensFromResult(result);
            AiChatMessage aiMessage = saveAiMessage(session, fullContent.toString(), result, tokens);

            // 更新会话统计
            updateSessionStats(session, result);

            // 记录使用统计
            recordTokenUsage(session, userMessage, aiMessage, result, modelConfig);

            // 发送完成响应
            AiChatResponseVo finalResponse = buildResponse(session, aiMessage, result, tokens);
            finalResponse.setStatus("completed");

            emitter.send(SseEmitter.event()
                    .data(JSONUtil.toJsonStr(finalResponse)));

            emitter.complete();

            long duration = System.currentTimeMillis() - startTime;
            log.info("流式响应完成，会话: {}, 耗时: {}ms, Token数: {}, 内容长度: {}",
                    session.getSessionId(), duration, tokens, fullContent.length());

        } catch (Exception e) {
            log.error("流式调用失败，会话: {}, 错误: {}", session.getSessionId(), e.getMessage(), e);

            try {
                AiChatResponseVo errorResponse = new AiChatResponseVo();
                errorResponse.setSessionId(session.getSessionId());
                errorResponse.setStatus("error");
                errorResponse.setErrorMessage("AI模型调用失败: " + e.getMessage());

                emitter.send(SseEmitter.event()
                        .name("error")
                        .data(JSONUtil.toJsonStr(errorResponse)));

            } catch (IOException ioException) {
                log.error("发送错误消息失败: {}", ioException.getMessage());
            }

            emitter.completeWithError(e);
        }
    }

    /**
     * 智能分割内容用于流式输出
     * 按照句子、段落等自然边界进行分割
     */
    private List<String> splitContentForStreaming(String content) {
        List<String> chunks = new ArrayList<>();

        if (StrUtil.isBlank(content)) {
            return chunks;
        }

        // 按句子分割（中英文句号、问号、感叹号）
        String[] sentences = content.split("(?<=[。！？.!?])\\s*");

        for (String sentence : sentences) {
            if (StrUtil.isNotBlank(sentence)) {
                // 如果句子太长，进一步按逗号分割
                if (sentence.length() > 100) {
                    String[] parts = sentence.split("(?<=[，,])\\s*");
                    for (String part : parts) {
                        if (StrUtil.isNotBlank(part)) {
                            chunks.add(part);
                        }
                    }
                } else {
                    chunks.add(sentence);
                }
            }
        }

        // 如果没有分割出内容，按字符分割
        if (chunks.isEmpty()) {
            int chunkSize = Math.max(1, content.length() / 20); // 分成20块
            for (int i = 0; i < content.length(); i += chunkSize) {
                int end = Math.min(i + chunkSize, content.length());
                chunks.add(content.substring(i, end));
            }
        }

        return chunks;
    }

    /**
     * 计算流式输出延迟
     * 根据内容长度和类型动态调整延迟时间
     */
    private int calculateStreamDelay(String chunk) {
        if (StrUtil.isBlank(chunk)) {
            return 50;
        }

        int baseDelay = 30; // 基础延迟30ms
        int lengthFactor = Math.min(chunk.length() * 5, 200); // 长度因子，最多200ms

        // 如果包含标点符号，增加延迟模拟思考
        if (chunk.matches(".*[。！？.!?].*")) {
            lengthFactor += 100;
        }

        return baseDelay + lengthFactor;
    }

    private int getTokensFromResult(GenerationResult result) {
        if (result != null && result.getUsage() != null) {
            return result.getUsage().getTotalTokens();
        }
        return 0;
    }

    private BigDecimal calculateCost(GenerationResult result, String modelType) {
        // 简化的成本计算，实际应该根据模型配置计算
        if (result != null && result.getUsage() != null) {
            int tokens = result.getUsage().getTotalTokens();
            return new BigDecimal(tokens).multiply(new BigDecimal("0.000002"));
        }
        return BigDecimal.ZERO;
    }

    private String getFileExtension(String fileName) {
        if (StrUtil.isBlank(fileName)) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex + 1).toLowerCase() : "";
    }

    private boolean isFileTypeSupported(String extension) {
        return Arrays.asList(aiChatConfig.getSupportedFileTypes()).contains(extension);
    }

    /**
     * 解析文件内容
     * 支持多种文件格式的内容提取，性能可靠，功能完整
     *
     * @param file 上传的文件
     * @return 解析后的文本内容
     * @throws IOException 文件读取异常
     */
    private String parseFileContent(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        String fileName = file.getOriginalFilename();
        String extension = getFileExtension(fileName).toLowerCase();

        log.info("开始解析文件: {}, 类型: {}, 大小: {} bytes", fileName, extension, file.getSize());

        try {
            switch (extension) {
                case "txt":
                case "md":
                case "log":
                case "csv":
                    return parseTextFile(file);

                case "pdf":
                    return "PDF文件解析需要PDFBox依赖，当前版本暂不支持";

                case "doc":
                    return "DOC文件解析需要POI HWPF依赖，当前版本暂不支持";

                case "docx":
                    return parseDocxFile(file);

                case "xls":
                    return parseXlsFile(file);

                case "xlsx":
                    return parseXlsxFile(file);

                case "ppt":
                    return "PPT文件解析需要POI HSLF依赖，当前版本暂不支持";

                case "pptx":
                    return parsePptxFile(file);

                case "jpg":
                case "jpeg":
                case "png":
                case "gif":
                case "bmp":
                case "webp":
                    return parseImageFile(file);

                default:
                    throw new UnsupportedOperationException("不支持的文件类型: " + extension);
            }
        } catch (Exception e) {
            log.error("文件解析失败: {}, 错误: {}", fileName, e.getMessage(), e);
            throw new IOException("文件解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析文本文件
     */
    private String parseTextFile(MultipartFile file) throws IOException {
        byte[] bytes = file.getBytes();
        // 尝试多种编码格式
        String[] encodings = {"UTF-8", "GBK", "GB2312", "ISO-8859-1"};

        for (String encoding : encodings) {
            try {
                String content = new String(bytes, encoding);
                // 检查是否包含乱码字符
                if (!containsGarbledText(content)) {
                    log.debug("使用编码 {} 成功解析文本文件", encoding);
                    return content.trim();
                }
            } catch (Exception e) {
                log.debug("使用编码 {} 解析失败: {}", encoding, e.getMessage());
            }
        }

        // 如果所有编码都失败，使用UTF-8作为默认
        return new String(bytes, StandardCharsets.UTF_8).trim();
    }



    /**
     * 解析DOCX文件
     */
    private String parseDocxFile(MultipartFile file) throws IOException {
        try (XWPFDocument document = new XWPFDocument(file.getInputStream())) {
            StringBuilder content = new StringBuilder();

            for (XWPFParagraph paragraph : document.getParagraphs()) {
                String text = paragraph.getText();
                if (StrUtil.isNotBlank(text)) {
                    content.append(text).append("\n");
                }
            }

            log.debug("DOCX文件解析完成，段落数: {}, 内容长度: {}",
                     document.getParagraphs().size(), content.length());
            return content.toString().trim();
        }
    }

    /**
     * 解析XLS文件
     */
    private String parseXlsFile(MultipartFile file) throws IOException {
        try (HSSFWorkbook workbook = new HSSFWorkbook(file.getInputStream())) {
            return parseExcelWorkbook(workbook);
        }
    }

    /**
     * 解析XLSX文件
     */
    private String parseXlsxFile(MultipartFile file) throws IOException {
        try (XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream())) {
            return parseExcelWorkbook(workbook);
        }
    }

    /**
     * 解析Excel工作簿
     */
    private String parseExcelWorkbook(Workbook workbook) {
        StringBuilder content = new StringBuilder();

        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet sheet = workbook.getSheetAt(i);
            content.append("工作表: ").append(sheet.getSheetName()).append("\n");

            for (Row row : sheet) {
                StringBuilder rowContent = new StringBuilder();
                for (Cell cell : row) {
                    String cellValue = getCellValueAsString(cell);
                    if (StrUtil.isNotBlank(cellValue)) {
                        rowContent.append(cellValue).append("\t");
                    }
                }
                if (rowContent.length() > 0) {
                    content.append(rowContent.toString().trim()).append("\n");
                }
            }
            content.append("\n");
        }

        log.debug("Excel文件解析完成，工作表数: {}, 内容长度: {}",
                 workbook.getNumberOfSheets(), content.length());
        return content.toString().trim();
    }



    /**
     * 解析PPTX文件
     */
    private String parsePptxFile(MultipartFile file) throws IOException {
        try (XMLSlideShow slideShow = new XMLSlideShow(file.getInputStream())) {
            StringBuilder content = new StringBuilder();

            for (XSLFSlide slide : slideShow.getSlides()) {
                content.append("幻灯片 ").append(slide.getSlideNumber()).append(":\n");

                for (XSLFShape shape : slide.getShapes()) {
                    if (shape instanceof XSLFTextShape) {
                        XSLFTextShape textShape = (XSLFTextShape) shape;
                        String text = textShape.getText();
                        if (StrUtil.isNotBlank(text)) {
                            content.append(text).append("\n");
                        }
                    }
                }
                content.append("\n");
            }

            log.debug("PPTX文件解析完成，幻灯片数: {}, 内容长度: {}",
                     slideShow.getSlides().size(), content.length());
            return content.toString().trim();
        }
    }

    /**
     * 解析图片文件（OCR功能）
     */
    private String parseImageFile(MultipartFile file) throws IOException {
        // 这里应该集成OCR服务，如Tesseract或云服务
        // 目前返回基础信息
        BufferedImage image = ImageIO.read(file.getInputStream());
        if (image != null) {
            String info = String.format("图片信息: 宽度=%d, 高度=%d, 格式=%s\n注意：OCR文字识别功能需要额外配置",
                                      image.getWidth(), image.getHeight(), getFileExtension(file.getOriginalFilename()));
            log.debug("图片文件解析完成: {}x{}", image.getWidth(), image.getHeight());
            return info;
        } else {
            throw new IOException("无法读取图片文件");
        }
    }

    /**
     * 获取单元格值作为字符串
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 检查文本是否包含乱码
     */
    private boolean containsGarbledText(String text) {
        if (StrUtil.isBlank(text)) {
            return false;
        }

        // 检查是否包含大量不可打印字符或特殊字符
        int garbledCount = 0;
        int totalCount = text.length();

        for (char c : text.toCharArray()) {
            if (c < 32 && c != '\n' && c != '\r' && c != '\t') {
                garbledCount++;
            } else if (c == 0xFFFD) { // Unicode替换字符
                garbledCount++;
            }
        }

        // 如果乱码字符超过10%，认为是乱码
        return (double) garbledCount / totalCount > 0.1;
    }

    private String buildFileAnalysisPrompt(String fileName, String fileContent, String question) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请分析以下文件内容：\n\n");
        prompt.append("文件名：").append(fileName).append("\n");
        prompt.append("文件内容：\n").append(fileContent).append("\n\n");

        if (StrUtil.isNotBlank(question)) {
            prompt.append("用户问题：").append(question).append("\n\n");
        }

        prompt.append("请根据文件内容回答用户的问题，如果没有具体问题，请总结文件的主要内容。");

        return prompt.toString();
    }

    @Override
    public CommonResult<AiChatSessionVo> getSession(String sessionId) {
        try {
            AiChatSession session = sessionMapper.selectBySessionId(sessionId);
            if (session == null) {
                return CommonResult.failed("会话不存在");
            }

            // 检查权限
            String currentUserId = getUserIdFromRequest();
            if (!session.getUserId().equals(currentUserId)) {
                return CommonResult.failed("无权访问此会话");
            }

            AiChatSessionVo sessionVo = new AiChatSessionVo();
            BeanUtils.copyProperties(session, sessionVo);

            // 获取最近消息
            List<AiChatMessage> recentMessages = messageMapper.selectBySessionId(sessionId, 0, 10);
            List<AiChatSessionVo.MessageInfo> messageInfos = recentMessages.stream()
                .map(msg -> {
                    AiChatSessionVo.MessageInfo info = new AiChatSessionVo.MessageInfo();
                    BeanUtils.copyProperties(msg, info);
                    return info;
                })
                .collect(Collectors.toList());
            sessionVo.setRecentMessages(messageInfos);

            return CommonResult.success(sessionVo);

        } catch (Exception e) {
            log.error("获取会话详情失败", e);
            return CommonResult.failed("获取会话详情失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<CommonPage<AiChatSessionVo>> getUserSessions(String userId, Integer pageNum, Integer pageSize) {
        try {
            if (StrUtil.isBlank(userId)) {
                userId = getUserIdFromRequest();
            }

            // 检查权限
            String currentUserId = getUserIdFromRequest();
            if (!userId.equals(currentUserId)) {
                return CommonResult.failed("无权访问其他用户的会话");
            }

            pageNum = pageNum == null || pageNum < 1 ? 1 : pageNum;
            pageSize = pageSize == null || pageSize < 1 ? 10 : pageSize;

            int offset = (pageNum - 1) * pageSize;

            List<AiChatSession> sessions = sessionMapper.selectByUserId(userId, null, offset, pageSize);
            int total = sessionMapper.countByUserId(userId, null);

            List<AiChatSessionVo> sessionVos = sessions.stream()
                .map(session -> {
                    AiChatSessionVo vo = new AiChatSessionVo();
                    BeanUtils.copyProperties(session, vo);
                    return vo;
                })
                .collect(Collectors.toList());

            CommonPage<AiChatSessionVo> page = CommonPage.restPage(sessionVos);
            page.setPageNum(pageNum);
            page.setPageSize(pageSize);
            page.setTotal((long) total);
            page.setTotalPage((int) Math.ceil((double) total / pageSize));

            return CommonResult.success(page);

        } catch (Exception e) {
            log.error("获取用户会话列表失败", e);
            return CommonResult.failed("获取用户会话列表失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<List<AiChatResponseVo.MessageInfo>> getSessionMessages(String sessionId, Integer pageNum, Integer pageSize) {
        try {
            // 检查会话权限
            AiChatSession session = sessionMapper.selectBySessionId(sessionId);
            if (session == null) {
                return CommonResult.failed("会话不存在");
            }

            String currentUserId = getUserIdFromRequest();
            if (!session.getUserId().equals(currentUserId)) {
                return CommonResult.failed("无权访问此会话");
            }

            pageNum = pageNum == null || pageNum < 1 ? 1 : pageNum;
            pageSize = pageSize == null || pageSize < 1 ? 20 : pageSize;

            int offset = (pageNum - 1) * pageSize;

            List<AiChatMessage> messages = messageMapper.selectBySessionId(sessionId, offset, pageSize);
            List<AiChatResponseVo.MessageInfo> messageInfos = messages.stream()
                .map(msg -> {
                    AiChatResponseVo.MessageInfo info = new AiChatResponseVo.MessageInfo();
                    info.setMessageId(msg.getMessageId());
                    info.setRole(msg.getRole());
                    info.setContent(msg.getContent());
                    info.setContentType(msg.getContentType());
                    info.setTokens(msg.getTokens());
                    info.setCost(msg.getCost());
                    info.setCreateTime(msg.getCreateTime());
                    return info;
                })
                .collect(Collectors.toList());

            return CommonResult.success(messageInfos);

        } catch (Exception e) {
            log.error("获取会话消息失败", e);
            return CommonResult.failed("获取会话消息失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<Void> endSession(String sessionId) {
        try {
            AiChatSession session = sessionMapper.selectBySessionId(sessionId);
            if (session == null) {
                return CommonResult.failed("会话不存在");
            }

            String currentUserId = getUserIdFromRequest();
            if (!session.getUserId().equals(currentUserId)) {
                return CommonResult.failed("无权操作此会话");
            }

            sessionMapper.updateStatus(sessionId, 0);

            return CommonResult.success(null, "会话已结束");

        } catch (Exception e) {
            log.error("结束会话失败", e);
            return CommonResult.failed("结束会话失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<Void> deleteSession(String sessionId) {
        try {
            AiChatSession session = sessionMapper.selectBySessionId(sessionId);
            if (session == null) {
                return CommonResult.failed("会话不存在");
            }

            String currentUserId = getUserIdFromRequest();
            if (!session.getUserId().equals(currentUserId)) {
                return CommonResult.failed("无权删除此会话");
            }

            // 删除会话消息
            messageMapper.deleteBySessionId(sessionId);

            // 删除会话
            sessionMapper.deleteBySessionId(sessionId);

            return CommonResult.success(null, "会话已删除");

        } catch (Exception e) {
            log.error("删除会话失败", e);
            return CommonResult.failed("删除会话失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<List<AiModelConfig>> getAvailableModels() {
        try {
            List<AiModelConfig> models = modelConfigMapper.selectEnabledConfigs();
            return CommonResult.success(models);
        } catch (Exception e) {
            log.error("获取可用模型列表失败", e);
            return CommonResult.failed("获取可用模型列表失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<Map<String, Object>> getUserTokenUsage(String userId, String period) {
        try {
            if (StrUtil.isBlank(userId)) {
                userId = getUserIdFromRequest();
            }

            String currentUserId = getUserIdFromRequest();
            if (!userId.equals(currentUserId)) {
                return CommonResult.failed("无权查看其他用户的使用统计");
            }

            Map<String, Object> result = new HashMap<>();

            switch (period) {
                case "today":
                    result = tokenUsageMapper.selectUserTodayUsage(userId);
                    break;
                case "month":
                    result = tokenUsageMapper.selectUserMonthUsage(userId);
                    break;
                default:
                    // 默认查询最近7天
                    Date endDate = new Date();
                    Date startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000L);
                    result = tokenUsageMapper.selectUserUsageStats(userId, startDate, endDate);
                    break;
            }

            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("获取用户Token使用统计失败", e);
            return CommonResult.failed("获取用户Token使用统计失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<Map<String, Object>> getSystemUsageStats(String period) {
        try {
            Date endDate = new Date();
            Date startDate;

            switch (period) {
                case "today":
                    startDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000L);
                    break;
                case "week":
                    startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000L);
                    break;
                case "month":
                    startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000L);
                    break;
                default:
                    startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000L);
                    break;
            }

            Map<String, Object> stats = tokenUsageMapper.selectSystemUsageStats(startDate, endDate);

            // 添加模型使用分布
            List<Map<String, Object>> modelUsage = tokenUsageMapper.selectUsageByModel(startDate, endDate);
            stats.put("modelUsage", modelUsage);

            // 添加Top用户排行
            List<Map<String, Object>> topUsers = tokenUsageMapper.selectTopUsers(startDate, endDate, 10);
            stats.put("topUsers", topUsers);

            return CommonResult.success(stats);

        } catch (Exception e) {
            log.error("获取系统使用统计失败", e);
            return CommonResult.failed("获取系统使用统计失败: " + e.getMessage());
        }
    }

    @Override
    public void cleanupTimeoutSessions() {
        try {
            int deletedCount = sessionMapper.deleteTimeoutSessions(aiChatConfig.getSessionTimeout());
            log.info("清理超时会话完成，删除数量: {}", deletedCount);
        } catch (Exception e) {
            log.error("清理超时会话失败", e);
        }
    }

    /**
     * 安全地获取用户ID
     * 如果未获取到当前登录用户，使用anonymousUser保存数据
     *
     * @return 用户ID，未登录时返回"anonymousUser"
     */
    private String safeGetUserId() {
        try {
            String userId = SecurityUtils.getUserIdValue();
            if (StrUtil.isNotBlank(userId)) {
                return userId;
            }
        } catch (Exception e) {
            log.debug("获取登录用户ID失败: {}", e.getMessage());
        }
        return ANONYMOUS_USER;
    }

    /**
     * 安全地获取用户名
     * 如果未获取到当前登录用户，使用"匿名用户"
     *
     * @return 用户名，未登录时返回"匿名用户"
     */
    private String safeGetUserName() {
        try {
            String userName = SecurityUtils.getUserName();
            if (StrUtil.isNotBlank(userName)) {
                return userName;
            }
        } catch (Exception e) {
            log.debug("获取登录用户名失败: {}", e.getMessage());
        }
        return "匿名用户";
    }

    /**
     * 检查用户每日使用限制
     * 登录用户不受任何使用次数限制，只有匿名用户受每日限制
     *
     * @param userId 用户ID
     * @return 是否超过限制 - true表示超过限制，false表示未超过限制
     */
    private boolean checkUserDailyUsageLimit(String userId) {
        // 登录用户不受任何使用次数限制
        if (!ANONYMOUS_USER.equals(userId)) {
            log.debug("登录用户[{}]不受使用次数限制，允许无限制使用", userId);
            return false;
        }

        // 匿名用户检查每日使用限制
        try {
            log.debug("检查匿名用户每日使用限制，当前限制: {} 次/天", ANONYMOUS_DAILY_LIMIT);

            // 查询匿名用户今日使用次数
            Map<String, Object> todayUsage = tokenUsageMapper.selectUserTodayUsage(userId);
            if (todayUsage != null) {
                Object requestCountObj = todayUsage.get("requestCount");
                if (requestCountObj != null) {
                    int requestCount = Integer.parseInt(requestCountObj.toString());
                    log.debug("匿名用户今日已使用次数: {}/{}", requestCount, ANONYMOUS_DAILY_LIMIT);

                    if (requestCount >= ANONYMOUS_DAILY_LIMIT) {
                        log.warn("匿名用户今日使用次数已达上限: {}/{} 次，请登录后继续使用", requestCount, ANONYMOUS_DAILY_LIMIT);
                        return true;
                    }
                }
            } else {
                log.debug("匿名用户今日尚未使用，允许使用");
            }
        } catch (Exception e) {
            log.error("检查匿名用户使用限制失败，为安全起见拒绝请求", e);
            // 出现异常时为了安全起见，认为已超限
            return true;
        }

        return false;
    }

    /**
     * 检查用户每日使用限制并返回相应的错误消息
     * 登录用户不受任何使用次数限制，只有匿名用户受每日限制
     *
     * @param userId 用户ID
     * @return 错误消息，如果没有限制则返回null
     */
    private String checkUserDailyUsageLimitWithMessage(String userId) {
        // 登录用户不受任何使用次数限制
        if (!ANONYMOUS_USER.equals(userId)) {
            log.debug("登录用户[{}]不受使用次数限制，允许无限制使用", userId);
            return null;
        }

        // 匿名用户检查每日使用限制
        try {
            log.debug("检查匿名用户每日使用限制，当前限制: {} 次/天", ANONYMOUS_DAILY_LIMIT);

            // 查询匿名用户今日使用次数
            Map<String, Object> todayUsage = tokenUsageMapper.selectUserTodayUsage(userId);
            if (todayUsage != null) {
                Object requestCountObj = todayUsage.get("requestCount");
                if (requestCountObj != null) {
                    int requestCount = Integer.parseInt(requestCountObj.toString());
                    log.debug("匿名用户今日已使用次数: {}/{}", requestCount, ANONYMOUS_DAILY_LIMIT);

                    if (requestCount >= ANONYMOUS_DAILY_LIMIT) {
                        log.warn("匿名用户今日使用次数已达上限: {}/{} 次，请登录后继续使用", requestCount, ANONYMOUS_DAILY_LIMIT);
                        return "匿名用户每日使用次数已达上限: " + ANONYMOUS_DAILY_LIMIT + " 次，请登录后继续使用";
                    }
                }
            } else {
                log.debug("匿名用户今日尚未使用，允许使用");
            }
        } catch (Exception e) {
            log.error("检查匿名用户使用限制失败，为安全起见拒绝请求", e);
            // 出现异常时为了安全起见，认为已超限
            return "系统繁忙，请稍后重试";
        }

        return null;
    }

    /**
     * 从请求头中获取用户ID
     * 优先从X-Access-Token中解析用户信息，如果失败则使用匿名用户
     *
     * @return 用户ID，未登录时返回"anonymousUser"
     */
    private String getUserIdFromRequest() {
        try {
            HttpServletRequest request = getCurrentHttpRequest();
            if (request != null) {
                // 从请求头中获取X-Access-Token并解析用户信息
                LoginUserInfo userInfo = jwtTokenHelper.getSystemLoginUserByAccessToken(request);
                if (userInfo != null && userInfo.getId() != null) {
                    String userId = userInfo.getId().toString();
                    log.debug("从请求头X-Access-Token成功获取用户ID: {}", userId);
                    return userId;
                }
            }

            // 如果从请求头获取失败，尝试从Spring Security上下文获取
            String userId = SecurityUtils.getUserIdValue();
            if (StrUtil.isNotBlank(userId)) {
                log.debug("从Spring Security上下文获取用户ID: {}", userId);
                return userId;
            }
        } catch (Exception e) {
            log.debug("获取登录用户ID失败: {}", e.getMessage());
        }

        log.debug("未能获取到登录用户ID，使用匿名用户");
        return ANONYMOUS_USER;
    }

    /**
     * 从请求头中获取用户名
     * 优先从X-Access-Token中解析用户信息，如果失败则使用匿名用户
     * @return 用户名，未登录时返回"anonymousUser"
     */
    private String getUserNameFromRequest() {
        try {
            HttpServletRequest request = getCurrentHttpRequest();
            if (request != null) {
                // 从请求头中获取X-Access-Token并解析用户信息
                LoginUserInfo userInfo = jwtTokenHelper.getSystemLoginUserByAccessToken(request);
                if (userInfo != null && StrUtil.isNotBlank(userInfo.getUsername())) {
                    String userName = userInfo.getUsername();
                    log.debug("从请求头X-Access-Token成功获取用户名: {}", userName);
                    return userName;
                }
            }

            // 如果从请求头获取失败，尝试从Spring Security上下文获取
            String userName = SecurityUtils.getUserName();
            if (StrUtil.isNotBlank(userName)) {
                log.debug("从Spring Security上下文获取用户名: {}", userName);
                return userName;
            }
        } catch (Exception e) {
            log.debug("获取登录用户名失败: {}", e.getMessage());
        }

        log.debug("未能获取到登录用户名，使用匿名用户");
        return ANONYMOUS_USER;
    }

    /**
     * 获取当前HTTP请求对象
     *
     * @return HttpServletRequest对象，如果获取失败返回null
     */
    private HttpServletRequest getCurrentHttpRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                return attributes.getRequest();
            }
        } catch (Exception e) {
            log.debug("获取当前HTTP请求失败: {}", e.getMessage());
        }
        return null;
    }
}
