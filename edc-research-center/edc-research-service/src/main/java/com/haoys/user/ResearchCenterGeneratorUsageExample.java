package com.haoys.user;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.model.ProjectConfigModule;
import com.haoys.user.service.ProjectConfigModuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * ResearchCenterGenerator生成的Service复杂查询使用示例
 * 展示如何使用生成的各种复杂查询方法
 * 
 * <AUTHOR>
 * @version 3.0.0
 * @since 1.0.0
 */
@Slf4j
@Component
public class ResearchCenterGeneratorUsageExample {

    @Autowired
    private ProjectConfigModuleService projectConfigModuleService;

    /**
     * 示例1：多条件AND查询
     * 适用场景：需要同时满足多个精确条件的查询
     */
    public void example1_MultipleConditionsQuery() {
        log.info("=== 示例1：多条件AND查询 ===");
        
        // 构建查询条件
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("status", 1);  // 状态为1
        conditions.put("type", "CONFIG");  // 类型为CONFIG
        conditions.put("is_deleted", 0);  // 未删除
        
        // 执行查询
        CommonResult<List<ProjectConfigModule>> result = 
            projectConfigModuleService.listByMultipleConditions(conditions);
        
        if (result.isSuccess()) {
            List<ProjectConfigModule> records = result.getData();
            log.info("查询成功，找到 {} 条记录", records.size());
            records.forEach(record ->
                log.info("记录ID: {}, 名称: {}", record.getModuleId(), record.getModuleName()));
        } else {
            log.error("查询失败: {}", result.getMessage());
        }
    }

    /**
     * 示例2：范围查询
     * 适用场景：时间范围、数值范围等查询
     */
    public void example2_RangeQuery() {
        log.info("=== 示例2：范围查询 ===");
        
        // 查询创建时间在指定范围内的记录
        Date startDate = new Date(System.currentTimeMillis() - 30L * 24 * 60 * 60 * 1000); // 30天前
        Date endDate = new Date(); // 现在
        
        CommonResult<List<ProjectConfigModule>> result = 
            projectConfigModuleService.listByRange("create_time", startDate, endDate);
        
        if (result.isSuccess()) {
            List<ProjectConfigModule> records = result.getData();
            log.info("范围查询成功，找到 {} 条记录", records.size());
        } else {
            log.error("范围查询失败: {}", result.getMessage());
        }
    }

    /**
     * 示例3：模糊查询
     * 适用场景：根据关键词搜索名称、描述等文本字段
     */
    public void example3_LikeQuery() {
        log.info("=== 示例3：模糊查询 ===");
        
        // 根据模块名称模糊查询
        String keyword = "用户";
        CommonResult<List<ProjectConfigModule>> result = 
            projectConfigModuleService.listByLike("module_name", keyword);
        
        if (result.isSuccess()) {
            List<ProjectConfigModule> records = result.getData();
            log.info("模糊查询成功，找到包含 '{}' 的 {} 条记录", keyword, records.size());
        } else {
            log.error("模糊查询失败: {}", result.getMessage());
        }
    }

    /**
     * 示例4：IN查询
     * 适用场景：查询指定ID列表、状态列表等
     */
    public void example4_InQuery() {
        log.info("=== 示例4：IN查询 ===");
        
        // 查询指定状态列表的记录
        List<Object> statusList = Arrays.asList(1, 2, 3);
        CommonResult<List<ProjectConfigModule>> result = 
            projectConfigModuleService.listByIn("status", statusList);
        
        if (result.isSuccess()) {
            List<ProjectConfigModule> records = result.getData();
            log.info("IN查询成功，找到状态在 {} 中的 {} 条记录", statusList, records.size());
        } else {
            log.error("IN查询失败: {}", result.getMessage());
        }
    }

    /**
     * 示例5：条件统计（使用现有方法）
     * 适用场景：统计满足条件的记录总数
     */
    public void example5_CountByCondition() {
        log.info("=== 示例5：条件统计 ===");

        // 统计活跃状态的记录总数
        Map<String, Object> params = new HashMap<>();
        params.put("status", 1);

        CommonResult<Long> result = projectConfigModuleService.countByCondition(params);

        if (result.isSuccess()) {
            Long count = result.getData();
            log.info("统计成功，活跃记录总数: {}", count);
        } else {
            log.error("统计失败: {}", result.getMessage());
        }
    }

    /**
     * 示例6：条件查询（使用现有方法）
     * 适用场景：需要灵活组合多种查询条件
     */
    public void example6_ConditionQuery() {
        log.info("=== 示例6：条件查询 ===");

        // 设置查询条件
        Map<String, Object> params = new HashMap<>();
        params.put("source_from", "SYSTEM");

        CommonResult<List<ProjectConfigModule>> result =
            projectConfigModuleService.listByCondition(params);

        if (result.isSuccess()) {
            List<ProjectConfigModule> records = result.getData();
            log.info("条件查询成功，找到 {} 条记录", records.size());
        } else {
            log.error("条件查询失败: {}", result.getMessage());
        }
    }

    /**
     * 示例7：分页条件查询（使用现有方法）
     * 适用场景：需要分页显示的条件查询
     */
    public void example7_PageByCondition() {
        log.info("=== 示例7：分页条件查询 ===");

        Map<String, Object> params = new HashMap<>();
        params.put("source_from", "SYSTEM");

        // 分页参数
        Integer pageNum = 1;
        Integer pageSize = 10;

        CommonResult<CommonPage<ProjectConfigModule>> result =
            projectConfigModuleService.pageByCondition(params, pageNum, pageSize);

        if (result.isSuccess()) {
            CommonPage<ProjectConfigModule> page = result.getData();
            log.info("分页条件查询成功，当前页: {}, 页大小: {}, 总记录数: {}, 总页数: {}",
                page.getPageNum(), page.getPageSize(), page.getTotal(), page.getTotalPage());

            List<ProjectConfigModule> records = page.getList();
            records.forEach(record ->
                log.info("记录ID: {}, 名称: {}", record.getModuleId(), record.getModuleName()));
        } else {
            log.error("分页条件查询失败: {}", result.getMessage());
        }
    }

    /*
     * 以下示例展示了代码生成器新增的复杂查询功能
     * 这些方法需要重新生成Service接口和实现类才能使用
     */

    /**
     * 示例8：复杂组合查询 - 精确匹配（需要重新生成代码）
     * 适用场景：需要灵活组合多种查询条件
     */
    /*
    public void example8_ComplexQuery_ExactMatch() {
        log.info("=== 示例8：复杂组合查询 - 精确匹配 ===");

        Map<String, Object> queryParams = new HashMap<>();

        // 设置精确匹配条件
        Map<String, Object> exactConditions = new HashMap<>();
        exactConditions.put("source_from", "SYSTEM");
        queryParams.put("exactConditions", exactConditions);

        CommonResult<List<ProjectConfigModule>> result =
            projectConfigModuleService.complexQuery(queryParams);

        if (result.isSuccess()) {
            List<ProjectConfigModule> records = result.getData();
            log.info("复杂查询(精确匹配)成功，找到 {} 条记录", records.size());
        } else {
            log.error("复杂查询失败: {}", result.getMessage());
        }
    }
    */

    /**
     * 示例9：总数统计（需要重新生成代码）
     */
    /*
    public void example9_CountTotal() {
        log.info("=== 示例9：总数统计 ===");

        Map<String, Object> params = new HashMap<>();
        params.put("source_from", "SYSTEM");

        CommonResult<Long> result = projectConfigModuleService.countTotal(params);

        if (result.isSuccess()) {
            Long count = result.getData();
            log.info("统计成功，记录总数: {}", count);
        } else {
            log.error("统计失败: {}", result.getMessage());
        }
    }
    */

    /**
     * 运行所有示例
     */
    public void runAllExamples() {
        log.info("开始运行ResearchCenterGenerator查询使用示例...");

        try {
            example1_MultipleConditionsQuery();
            example2_RangeQuery();
            example3_LikeQuery();
            example4_InQuery();
            example5_CountByCondition();
            example6_ConditionQuery();
            example7_PageByCondition();

            log.info("所有示例运行完成！");
            log.info("注意：复杂查询功能需要重新生成Service接口和实现类才能使用");
        } catch (Exception e) {
            log.error("运行示例时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 查询参数构建器示例
     * 提供便捷的查询参数构建方法
     */
    public static class QueryParamsBuilder {
        private Map<String, Object> params = new HashMap<>();
        
        public static QueryParamsBuilder create() {
            return new QueryParamsBuilder();
        }
        
        public QueryParamsBuilder exactConditions(Map<String, Object> conditions) {
            params.put("exactConditions", conditions);
            return this;
        }
        
        public QueryParamsBuilder range(String field, Object startValue, Object endValue) {
            params.put("rangeField", field);
            params.put("startValue", startValue);
            params.put("endValue", endValue);
            return this;
        }
        
        public QueryParamsBuilder like(String field, String keyword) {
            params.put("likeField", field);
            params.put("keyword", keyword);
            return this;
        }
        
        public QueryParamsBuilder in(String field, List<Object> values) {
            params.put("inField", field);
            params.put("values", values);
            return this;
        }
        
        public Map<String, Object> build() {
            return params;
        }
    }
}
