package com.haoys.user.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProjectVisitConfigExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectVisitConfigExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andPlanIdIsNull() {
            addCriterion("plan_id is null");
            return (Criteria) this;
        }

        public Criteria andPlanIdIsNotNull() {
            addCriterion("plan_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlanIdEqualTo(Long value) {
            addCriterion("plan_id =", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotEqualTo(Long value) {
            addCriterion("plan_id <>", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdGreaterThan(Long value) {
            addCriterion("plan_id >", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdGreaterThanOrEqualTo(Long value) {
            addCriterion("plan_id >=", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdLessThan(Long value) {
            addCriterion("plan_id <", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdLessThanOrEqualTo(Long value) {
            addCriterion("plan_id <=", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdIn(List<Long> values) {
            addCriterion("plan_id in", values, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotIn(List<Long> values) {
            addCriterion("plan_id not in", values, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdBetween(Long value1, Long value2) {
            addCriterion("plan_id between", value1, value2, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotBetween(Long value1, Long value2) {
            addCriterion("plan_id not between", value1, value2, "planId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNull() {
            addCriterion("template_id is null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNotNull() {
            addCriterion("template_id is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdEqualTo(Long value) {
            addCriterion("template_id =", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotEqualTo(Long value) {
            addCriterion("template_id <>", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThan(Long value) {
            addCriterion("template_id >", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThanOrEqualTo(Long value) {
            addCriterion("template_id >=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThan(Long value) {
            addCriterion("template_id <", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThanOrEqualTo(Long value) {
            addCriterion("template_id <=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIn(List<Long> values) {
            addCriterion("template_id in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotIn(List<Long> values) {
            addCriterion("template_id not in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdBetween(Long value1, Long value2) {
            addCriterion("template_id between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotBetween(Long value1, Long value2) {
            addCriterion("template_id not between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andPreVisitIdIsNull() {
            addCriterion("pre_visit_id is null");
            return (Criteria) this;
        }

        public Criteria andPreVisitIdIsNotNull() {
            addCriterion("pre_visit_id is not null");
            return (Criteria) this;
        }

        public Criteria andPreVisitIdEqualTo(Long value) {
            addCriterion("pre_visit_id =", value, "preVisitId");
            return (Criteria) this;
        }

        public Criteria andPreVisitIdNotEqualTo(Long value) {
            addCriterion("pre_visit_id <>", value, "preVisitId");
            return (Criteria) this;
        }

        public Criteria andPreVisitIdGreaterThan(Long value) {
            addCriterion("pre_visit_id >", value, "preVisitId");
            return (Criteria) this;
        }

        public Criteria andPreVisitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("pre_visit_id >=", value, "preVisitId");
            return (Criteria) this;
        }

        public Criteria andPreVisitIdLessThan(Long value) {
            addCriterion("pre_visit_id <", value, "preVisitId");
            return (Criteria) this;
        }

        public Criteria andPreVisitIdLessThanOrEqualTo(Long value) {
            addCriterion("pre_visit_id <=", value, "preVisitId");
            return (Criteria) this;
        }

        public Criteria andPreVisitIdIn(List<Long> values) {
            addCriterion("pre_visit_id in", values, "preVisitId");
            return (Criteria) this;
        }

        public Criteria andPreVisitIdNotIn(List<Long> values) {
            addCriterion("pre_visit_id not in", values, "preVisitId");
            return (Criteria) this;
        }

        public Criteria andPreVisitIdBetween(Long value1, Long value2) {
            addCriterion("pre_visit_id between", value1, value2, "preVisitId");
            return (Criteria) this;
        }

        public Criteria andPreVisitIdNotBetween(Long value1, Long value2) {
            addCriterion("pre_visit_id not between", value1, value2, "preVisitId");
            return (Criteria) this;
        }

        public Criteria andVisitNameIsNull() {
            addCriterion("visit_name is null");
            return (Criteria) this;
        }

        public Criteria andVisitNameIsNotNull() {
            addCriterion("visit_name is not null");
            return (Criteria) this;
        }

        public Criteria andVisitNameEqualTo(String value) {
            addCriterion("visit_name =", value, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitNameNotEqualTo(String value) {
            addCriterion("visit_name <>", value, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitNameGreaterThan(String value) {
            addCriterion("visit_name >", value, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitNameGreaterThanOrEqualTo(String value) {
            addCriterion("visit_name >=", value, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitNameLessThan(String value) {
            addCriterion("visit_name <", value, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitNameLessThanOrEqualTo(String value) {
            addCriterion("visit_name <=", value, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitNameLike(String value) {
            addCriterion("visit_name like", value, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitNameNotLike(String value) {
            addCriterion("visit_name not like", value, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitNameIn(List<String> values) {
            addCriterion("visit_name in", values, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitNameNotIn(List<String> values) {
            addCriterion("visit_name not in", values, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitNameBetween(String value1, String value2) {
            addCriterion("visit_name between", value1, value2, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitNameNotBetween(String value1, String value2) {
            addCriterion("visit_name not between", value1, value2, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitTypeIsNull() {
            addCriterion("visit_type is null");
            return (Criteria) this;
        }

        public Criteria andVisitTypeIsNotNull() {
            addCriterion("visit_type is not null");
            return (Criteria) this;
        }

        public Criteria andVisitTypeEqualTo(String value) {
            addCriterion("visit_type =", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeNotEqualTo(String value) {
            addCriterion("visit_type <>", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeGreaterThan(String value) {
            addCriterion("visit_type >", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeGreaterThanOrEqualTo(String value) {
            addCriterion("visit_type >=", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeLessThan(String value) {
            addCriterion("visit_type <", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeLessThanOrEqualTo(String value) {
            addCriterion("visit_type <=", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeLike(String value) {
            addCriterion("visit_type like", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeNotLike(String value) {
            addCriterion("visit_type not like", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeIn(List<String> values) {
            addCriterion("visit_type in", values, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeNotIn(List<String> values) {
            addCriterion("visit_type not in", values, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeBetween(String value1, String value2) {
            addCriterion("visit_type between", value1, value2, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeNotBetween(String value1, String value2) {
            addCriterion("visit_type not between", value1, value2, "visitType");
            return (Criteria) this;
        }

        public Criteria andOwnerPeroidIsNull() {
            addCriterion("owner_peroid is null");
            return (Criteria) this;
        }

        public Criteria andOwnerPeroidIsNotNull() {
            addCriterion("owner_peroid is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerPeroidEqualTo(String value) {
            addCriterion("owner_peroid =", value, "ownerPeroid");
            return (Criteria) this;
        }

        public Criteria andOwnerPeroidNotEqualTo(String value) {
            addCriterion("owner_peroid <>", value, "ownerPeroid");
            return (Criteria) this;
        }

        public Criteria andOwnerPeroidGreaterThan(String value) {
            addCriterion("owner_peroid >", value, "ownerPeroid");
            return (Criteria) this;
        }

        public Criteria andOwnerPeroidGreaterThanOrEqualTo(String value) {
            addCriterion("owner_peroid >=", value, "ownerPeroid");
            return (Criteria) this;
        }

        public Criteria andOwnerPeroidLessThan(String value) {
            addCriterion("owner_peroid <", value, "ownerPeroid");
            return (Criteria) this;
        }

        public Criteria andOwnerPeroidLessThanOrEqualTo(String value) {
            addCriterion("owner_peroid <=", value, "ownerPeroid");
            return (Criteria) this;
        }

        public Criteria andOwnerPeroidLike(String value) {
            addCriterion("owner_peroid like", value, "ownerPeroid");
            return (Criteria) this;
        }

        public Criteria andOwnerPeroidNotLike(String value) {
            addCriterion("owner_peroid not like", value, "ownerPeroid");
            return (Criteria) this;
        }

        public Criteria andOwnerPeroidIn(List<String> values) {
            addCriterion("owner_peroid in", values, "ownerPeroid");
            return (Criteria) this;
        }

        public Criteria andOwnerPeroidNotIn(List<String> values) {
            addCriterion("owner_peroid not in", values, "ownerPeroid");
            return (Criteria) this;
        }

        public Criteria andOwnerPeroidBetween(String value1, String value2) {
            addCriterion("owner_peroid between", value1, value2, "ownerPeroid");
            return (Criteria) this;
        }

        public Criteria andOwnerPeroidNotBetween(String value1, String value2) {
            addCriterion("owner_peroid not between", value1, value2, "ownerPeroid");
            return (Criteria) this;
        }

        public Criteria andFollowUpPeroidIsNull() {
            addCriterion("follow_up_peroid is null");
            return (Criteria) this;
        }

        public Criteria andFollowUpPeroidIsNotNull() {
            addCriterion("follow_up_peroid is not null");
            return (Criteria) this;
        }

        public Criteria andFollowUpPeroidEqualTo(Integer value) {
            addCriterion("follow_up_peroid =", value, "followUpPeroid");
            return (Criteria) this;
        }

        public Criteria andFollowUpPeroidNotEqualTo(Integer value) {
            addCriterion("follow_up_peroid <>", value, "followUpPeroid");
            return (Criteria) this;
        }

        public Criteria andFollowUpPeroidGreaterThan(Integer value) {
            addCriterion("follow_up_peroid >", value, "followUpPeroid");
            return (Criteria) this;
        }

        public Criteria andFollowUpPeroidGreaterThanOrEqualTo(Integer value) {
            addCriterion("follow_up_peroid >=", value, "followUpPeroid");
            return (Criteria) this;
        }

        public Criteria andFollowUpPeroidLessThan(Integer value) {
            addCriterion("follow_up_peroid <", value, "followUpPeroid");
            return (Criteria) this;
        }

        public Criteria andFollowUpPeroidLessThanOrEqualTo(Integer value) {
            addCriterion("follow_up_peroid <=", value, "followUpPeroid");
            return (Criteria) this;
        }

        public Criteria andFollowUpPeroidIn(List<Integer> values) {
            addCriterion("follow_up_peroid in", values, "followUpPeroid");
            return (Criteria) this;
        }

        public Criteria andFollowUpPeroidNotIn(List<Integer> values) {
            addCriterion("follow_up_peroid not in", values, "followUpPeroid");
            return (Criteria) this;
        }

        public Criteria andFollowUpPeroidBetween(Integer value1, Integer value2) {
            addCriterion("follow_up_peroid between", value1, value2, "followUpPeroid");
            return (Criteria) this;
        }

        public Criteria andFollowUpPeroidNotBetween(Integer value1, Integer value2) {
            addCriterion("follow_up_peroid not between", value1, value2, "followUpPeroid");
            return (Criteria) this;
        }

        public Criteria andFollowUpUnitIsNull() {
            addCriterion("follow_up_unit is null");
            return (Criteria) this;
        }

        public Criteria andFollowUpUnitIsNotNull() {
            addCriterion("follow_up_unit is not null");
            return (Criteria) this;
        }

        public Criteria andFollowUpUnitEqualTo(String value) {
            addCriterion("follow_up_unit =", value, "followUpUnit");
            return (Criteria) this;
        }

        public Criteria andFollowUpUnitNotEqualTo(String value) {
            addCriterion("follow_up_unit <>", value, "followUpUnit");
            return (Criteria) this;
        }

        public Criteria andFollowUpUnitGreaterThan(String value) {
            addCriterion("follow_up_unit >", value, "followUpUnit");
            return (Criteria) this;
        }

        public Criteria andFollowUpUnitGreaterThanOrEqualTo(String value) {
            addCriterion("follow_up_unit >=", value, "followUpUnit");
            return (Criteria) this;
        }

        public Criteria andFollowUpUnitLessThan(String value) {
            addCriterion("follow_up_unit <", value, "followUpUnit");
            return (Criteria) this;
        }

        public Criteria andFollowUpUnitLessThanOrEqualTo(String value) {
            addCriterion("follow_up_unit <=", value, "followUpUnit");
            return (Criteria) this;
        }

        public Criteria andFollowUpUnitLike(String value) {
            addCriterion("follow_up_unit like", value, "followUpUnit");
            return (Criteria) this;
        }

        public Criteria andFollowUpUnitNotLike(String value) {
            addCriterion("follow_up_unit not like", value, "followUpUnit");
            return (Criteria) this;
        }

        public Criteria andFollowUpUnitIn(List<String> values) {
            addCriterion("follow_up_unit in", values, "followUpUnit");
            return (Criteria) this;
        }

        public Criteria andFollowUpUnitNotIn(List<String> values) {
            addCriterion("follow_up_unit not in", values, "followUpUnit");
            return (Criteria) this;
        }

        public Criteria andFollowUpUnitBetween(String value1, String value2) {
            addCriterion("follow_up_unit between", value1, value2, "followUpUnit");
            return (Criteria) this;
        }

        public Criteria andFollowUpUnitNotBetween(String value1, String value2) {
            addCriterion("follow_up_unit not between", value1, value2, "followUpUnit");
            return (Criteria) this;
        }

        public Criteria andVisitWindowStartIsNull() {
            addCriterion("visit_window_start is null");
            return (Criteria) this;
        }

        public Criteria andVisitWindowStartIsNotNull() {
            addCriterion("visit_window_start is not null");
            return (Criteria) this;
        }

        public Criteria andVisitWindowStartEqualTo(Integer value) {
            addCriterion("visit_window_start =", value, "visitWindowStart");
            return (Criteria) this;
        }

        public Criteria andVisitWindowStartNotEqualTo(Integer value) {
            addCriterion("visit_window_start <>", value, "visitWindowStart");
            return (Criteria) this;
        }

        public Criteria andVisitWindowStartGreaterThan(Integer value) {
            addCriterion("visit_window_start >", value, "visitWindowStart");
            return (Criteria) this;
        }

        public Criteria andVisitWindowStartGreaterThanOrEqualTo(Integer value) {
            addCriterion("visit_window_start >=", value, "visitWindowStart");
            return (Criteria) this;
        }

        public Criteria andVisitWindowStartLessThan(Integer value) {
            addCriterion("visit_window_start <", value, "visitWindowStart");
            return (Criteria) this;
        }

        public Criteria andVisitWindowStartLessThanOrEqualTo(Integer value) {
            addCriterion("visit_window_start <=", value, "visitWindowStart");
            return (Criteria) this;
        }

        public Criteria andVisitWindowStartIn(List<Integer> values) {
            addCriterion("visit_window_start in", values, "visitWindowStart");
            return (Criteria) this;
        }

        public Criteria andVisitWindowStartNotIn(List<Integer> values) {
            addCriterion("visit_window_start not in", values, "visitWindowStart");
            return (Criteria) this;
        }

        public Criteria andVisitWindowStartBetween(Integer value1, Integer value2) {
            addCriterion("visit_window_start between", value1, value2, "visitWindowStart");
            return (Criteria) this;
        }

        public Criteria andVisitWindowStartNotBetween(Integer value1, Integer value2) {
            addCriterion("visit_window_start not between", value1, value2, "visitWindowStart");
            return (Criteria) this;
        }

        public Criteria andVisitWindowEndIsNull() {
            addCriterion("visit_window_end is null");
            return (Criteria) this;
        }

        public Criteria andVisitWindowEndIsNotNull() {
            addCriterion("visit_window_end is not null");
            return (Criteria) this;
        }

        public Criteria andVisitWindowEndEqualTo(Integer value) {
            addCriterion("visit_window_end =", value, "visitWindowEnd");
            return (Criteria) this;
        }

        public Criteria andVisitWindowEndNotEqualTo(Integer value) {
            addCriterion("visit_window_end <>", value, "visitWindowEnd");
            return (Criteria) this;
        }

        public Criteria andVisitWindowEndGreaterThan(Integer value) {
            addCriterion("visit_window_end >", value, "visitWindowEnd");
            return (Criteria) this;
        }

        public Criteria andVisitWindowEndGreaterThanOrEqualTo(Integer value) {
            addCriterion("visit_window_end >=", value, "visitWindowEnd");
            return (Criteria) this;
        }

        public Criteria andVisitWindowEndLessThan(Integer value) {
            addCriterion("visit_window_end <", value, "visitWindowEnd");
            return (Criteria) this;
        }

        public Criteria andVisitWindowEndLessThanOrEqualTo(Integer value) {
            addCriterion("visit_window_end <=", value, "visitWindowEnd");
            return (Criteria) this;
        }

        public Criteria andVisitWindowEndIn(List<Integer> values) {
            addCriterion("visit_window_end in", values, "visitWindowEnd");
            return (Criteria) this;
        }

        public Criteria andVisitWindowEndNotIn(List<Integer> values) {
            addCriterion("visit_window_end not in", values, "visitWindowEnd");
            return (Criteria) this;
        }

        public Criteria andVisitWindowEndBetween(Integer value1, Integer value2) {
            addCriterion("visit_window_end between", value1, value2, "visitWindowEnd");
            return (Criteria) this;
        }

        public Criteria andVisitWindowEndNotBetween(Integer value1, Integer value2) {
            addCriterion("visit_window_end not between", value1, value2, "visitWindowEnd");
            return (Criteria) this;
        }

        public Criteria andVisitWindowUnitIsNull() {
            addCriterion("visit_window_unit is null");
            return (Criteria) this;
        }

        public Criteria andVisitWindowUnitIsNotNull() {
            addCriterion("visit_window_unit is not null");
            return (Criteria) this;
        }

        public Criteria andVisitWindowUnitEqualTo(String value) {
            addCriterion("visit_window_unit =", value, "visitWindowUnit");
            return (Criteria) this;
        }

        public Criteria andVisitWindowUnitNotEqualTo(String value) {
            addCriterion("visit_window_unit <>", value, "visitWindowUnit");
            return (Criteria) this;
        }

        public Criteria andVisitWindowUnitGreaterThan(String value) {
            addCriterion("visit_window_unit >", value, "visitWindowUnit");
            return (Criteria) this;
        }

        public Criteria andVisitWindowUnitGreaterThanOrEqualTo(String value) {
            addCriterion("visit_window_unit >=", value, "visitWindowUnit");
            return (Criteria) this;
        }

        public Criteria andVisitWindowUnitLessThan(String value) {
            addCriterion("visit_window_unit <", value, "visitWindowUnit");
            return (Criteria) this;
        }

        public Criteria andVisitWindowUnitLessThanOrEqualTo(String value) {
            addCriterion("visit_window_unit <=", value, "visitWindowUnit");
            return (Criteria) this;
        }

        public Criteria andVisitWindowUnitLike(String value) {
            addCriterion("visit_window_unit like", value, "visitWindowUnit");
            return (Criteria) this;
        }

        public Criteria andVisitWindowUnitNotLike(String value) {
            addCriterion("visit_window_unit not like", value, "visitWindowUnit");
            return (Criteria) this;
        }

        public Criteria andVisitWindowUnitIn(List<String> values) {
            addCriterion("visit_window_unit in", values, "visitWindowUnit");
            return (Criteria) this;
        }

        public Criteria andVisitWindowUnitNotIn(List<String> values) {
            addCriterion("visit_window_unit not in", values, "visitWindowUnit");
            return (Criteria) this;
        }

        public Criteria andVisitWindowUnitBetween(String value1, String value2) {
            addCriterion("visit_window_unit between", value1, value2, "visitWindowUnit");
            return (Criteria) this;
        }

        public Criteria andVisitWindowUnitNotBetween(String value1, String value2) {
            addCriterion("visit_window_unit not between", value1, value2, "visitWindowUnit");
            return (Criteria) this;
        }

        public Criteria andExpandIsNull() {
            addCriterion("expand is null");
            return (Criteria) this;
        }

        public Criteria andExpandIsNotNull() {
            addCriterion("expand is not null");
            return (Criteria) this;
        }

        public Criteria andExpandEqualTo(String value) {
            addCriterion("expand =", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotEqualTo(String value) {
            addCriterion("expand <>", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandGreaterThan(String value) {
            addCriterion("expand >", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandGreaterThanOrEqualTo(String value) {
            addCriterion("expand >=", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLessThan(String value) {
            addCriterion("expand <", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLessThanOrEqualTo(String value) {
            addCriterion("expand <=", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLike(String value) {
            addCriterion("expand like", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotLike(String value) {
            addCriterion("expand not like", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandIn(List<String> values) {
            addCriterion("expand in", values, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotIn(List<String> values) {
            addCriterion("expand not in", values, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandBetween(String value1, String value2) {
            addCriterion("expand between", value1, value2, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotBetween(String value1, String value2) {
            addCriterion("expand not between", value1, value2, "expand");
            return (Criteria) this;
        }

        public Criteria andConfigDataIsNull() {
            addCriterion("config_data is null");
            return (Criteria) this;
        }

        public Criteria andConfigDataIsNotNull() {
            addCriterion("config_data is not null");
            return (Criteria) this;
        }

        public Criteria andConfigDataEqualTo(String value) {
            addCriterion("config_data =", value, "configData");
            return (Criteria) this;
        }

        public Criteria andConfigDataNotEqualTo(String value) {
            addCriterion("config_data <>", value, "configData");
            return (Criteria) this;
        }

        public Criteria andConfigDataGreaterThan(String value) {
            addCriterion("config_data >", value, "configData");
            return (Criteria) this;
        }

        public Criteria andConfigDataGreaterThanOrEqualTo(String value) {
            addCriterion("config_data >=", value, "configData");
            return (Criteria) this;
        }

        public Criteria andConfigDataLessThan(String value) {
            addCriterion("config_data <", value, "configData");
            return (Criteria) this;
        }

        public Criteria andConfigDataLessThanOrEqualTo(String value) {
            addCriterion("config_data <=", value, "configData");
            return (Criteria) this;
        }

        public Criteria andConfigDataLike(String value) {
            addCriterion("config_data like", value, "configData");
            return (Criteria) this;
        }

        public Criteria andConfigDataNotLike(String value) {
            addCriterion("config_data not like", value, "configData");
            return (Criteria) this;
        }

        public Criteria andConfigDataIn(List<String> values) {
            addCriterion("config_data in", values, "configData");
            return (Criteria) this;
        }

        public Criteria andConfigDataNotIn(List<String> values) {
            addCriterion("config_data not in", values, "configData");
            return (Criteria) this;
        }

        public Criteria andConfigDataBetween(String value1, String value2) {
            addCriterion("config_data between", value1, value2, "configData");
            return (Criteria) this;
        }

        public Criteria andConfigDataNotBetween(String value1, String value2) {
            addCriterion("config_data not between", value1, value2, "configData");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNull() {
            addCriterion("create_user is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNotNull() {
            addCriterion("create_user is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserEqualTo(Long value) {
            addCriterion("create_user =", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotEqualTo(Long value) {
            addCriterion("create_user <>", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThan(Long value) {
            addCriterion("create_user >", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThanOrEqualTo(Long value) {
            addCriterion("create_user >=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThan(Long value) {
            addCriterion("create_user <", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThanOrEqualTo(Long value) {
            addCriterion("create_user <=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserIn(List<Long> values) {
            addCriterion("create_user in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotIn(List<Long> values) {
            addCriterion("create_user not in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserBetween(Long value1, Long value2) {
            addCriterion("create_user between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotBetween(Long value1, Long value2) {
            addCriterion("create_user not between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNull() {
            addCriterion("update_user is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNotNull() {
            addCriterion("update_user is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserEqualTo(Long value) {
            addCriterion("update_user =", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotEqualTo(Long value) {
            addCriterion("update_user <>", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThan(Long value) {
            addCriterion("update_user >", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThanOrEqualTo(Long value) {
            addCriterion("update_user >=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThan(Long value) {
            addCriterion("update_user <", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThanOrEqualTo(Long value) {
            addCriterion("update_user <=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIn(List<Long> values) {
            addCriterion("update_user in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotIn(List<Long> values) {
            addCriterion("update_user not in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserBetween(Long value1, Long value2) {
            addCriterion("update_user between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotBetween(Long value1, Long value2) {
            addCriterion("update_user not between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andSortIsNull() {
            addCriterion("sort is null");
            return (Criteria) this;
        }

        public Criteria andSortIsNotNull() {
            addCriterion("sort is not null");
            return (Criteria) this;
        }

        public Criteria andSortEqualTo(Integer value) {
            addCriterion("sort =", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotEqualTo(Integer value) {
            addCriterion("sort <>", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThan(Integer value) {
            addCriterion("sort >", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThanOrEqualTo(Integer value) {
            addCriterion("sort >=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThan(Integer value) {
            addCriterion("sort <", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThanOrEqualTo(Integer value) {
            addCriterion("sort <=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortIn(List<Integer> values) {
            addCriterion("sort in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotIn(List<Integer> values) {
            addCriterion("sort not in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortBetween(Integer value1, Integer value2) {
            addCriterion("sort between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotBetween(Integer value1, Integer value2) {
            addCriterion("sort not between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNull() {
            addCriterion("platform_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNotNull() {
            addCriterion("platform_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdEqualTo(String value) {
            addCriterion("platform_id =", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotEqualTo(String value) {
            addCriterion("platform_id <>", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThan(String value) {
            addCriterion("platform_id >", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThanOrEqualTo(String value) {
            addCriterion("platform_id >=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThan(String value) {
            addCriterion("platform_id <", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThanOrEqualTo(String value) {
            addCriterion("platform_id <=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLike(String value) {
            addCriterion("platform_id like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotLike(String value) {
            addCriterion("platform_id not like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIn(List<String> values) {
            addCriterion("platform_id in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotIn(List<String> values) {
            addCriterion("platform_id not in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdBetween(String value1, String value2) {
            addCriterion("platform_id between", value1, value2, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotBetween(String value1, String value2) {
            addCriterion("platform_id not between", value1, value2, "platformId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}