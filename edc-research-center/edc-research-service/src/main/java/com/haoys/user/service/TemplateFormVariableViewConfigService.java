package com.haoys.user.service;

import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.param.crf.TemplateVariableViewConfigParam;
import com.haoys.user.domain.vo.ecrf.TemplateFormVariableViewConfigVo;
import com.haoys.user.model.TemplateVariableViewBase;

import java.util.List;

public interface TemplateFormVariableViewConfigService {

    /**
     * 保存选项联动规则
     * @param templateVariableViewConfigParam
     * @return
     */
    CustomResult saveTemplateFormVariableViewConfig(TemplateVariableViewConfigParam templateVariableViewConfigParam);

    /**
     * 根据optionId查询表单联动控制变量规则列表
     * @param projectId
     * @param formId
     * @param optionValueId
     * @return
     */
    List<TemplateFormVariableViewConfigVo> getTemplateFormVariableListByOptionId(String projectId, String formId, String optionValueId);
    
    List<TemplateFormVariableViewConfigVo> getTemplateFormVariableListByOptionIds(String projectId, String formId, String optionValueIds);

    TemplateVariableViewBase getTemplateVariableBaseInfo(String projectId, String formId, String formDetailId, String formTableId, String optionValueId);

    List<TemplateVariableViewBase> getTemplateVariableOptionListByVariableId(String projectId, String formId, String formDetailId, String formTableId);
}
