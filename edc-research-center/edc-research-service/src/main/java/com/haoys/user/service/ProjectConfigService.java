package com.haoys.user.service;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.domain.vo.project.ProjectConfigVo;
import com.haoys.user.model.ProjectConfigBase;
import com.haoys.user.model.ProjectConfigValue;

import java.util.List;

public interface ProjectConfigService {

    /**
     * 保存配置项的值
     * @param projectConfigValues 配置项的数据值的集合
     * @return
     */
    CommonResult<Object> saveProjectConfigValue(List<ProjectConfigValue> projectConfigValues);

    /**
     * 获取项目配置
     * @param projectId 项目id
     * @param moduleName 模块名称（非必传）
     * @return 项目配置
     */
    CommonResult<List<ProjectConfigVo>> getProjectConfigByProjectId(String projectId, String moduleName);
    /**
     * 获取配置
     * @param projectId 项目id
     * @param configCode 配置code（非必传）
     * @return 配置
     */
    ProjectConfigVo getProjectBaseConfig(String projectId, String configCode);

    /**
     * 保存配置项
     * @param configBase
     * @return
     */
    CommonResult<Object> saveProjectConfig(ProjectConfigBase configBase);
}
