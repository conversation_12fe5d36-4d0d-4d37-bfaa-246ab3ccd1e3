package com.haoys.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.type.AviatorDouble;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.config.AviatorConfig;
import com.haoys.user.exception.ServiceException;
import com.haoys.user.mapper.*;
import com.haoys.user.model.*;
import com.haoys.user.service.TemplateFormDvpRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 逻辑配置service实现类
 */
@Slf4j
@Service
public class TemplateFormDvpRuleServiceImpl extends BaseService implements TemplateFormDvpRuleService {
    
    @Resource
    private TemplateFormDvpRuleMapper templateFormDvpRuleMapper;
    
    @Resource
    private TemplateFormDvpValMapper templateFormDvpValMapper;
    
    @Resource
    private TemplateFormDetailMapper templateFormDetailMapper;
    
    @Resource
    private TemplateFormTableMapper templateFormTableMapper;
    @Resource
    private ProjectTesteeChallengeMapper projectTesteeChallengeMapper;
    
    /**
     * 新增
     *
     * @param dvpRule 逻辑配置信息
     * @return
     */
    @Override
    public CommonResult<Object> add(TemplateFormDvpRule dvpRule) {
        dvpRule.setId(SnowflakeIdWorker.getUuid());
        dvpRule.setCreateUserId(SecurityUtils.getUserIdValue());
        dvpRule.setCreateTime(new Date());
        dvpRule.setPlatformId(SecurityUtils.getSystemPlatformId());
        dvpRule.setTenantId(SecurityUtils.getSystemTenantId());
        dvpRule.setStatus(BusinessConfig.VALID_STATUS);
        dvpRule.setEnabled(true);
        int insert = templateFormDvpRuleMapper.insert(dvpRule);
        
        if (StringUtils.isNotEmpty(dvpRule.getArithmeticValue())) {
            Pattern pattern = Pattern.compile("\\$(.*?)\\$");
            Matcher matcher = pattern.matcher(dvpRule.getArithmeticValue());
            while (matcher.find()) {
                String content = matcher.group(1);
                String[] ids = content.split("_");
                TemplateFormDvpVal val = new TemplateFormDvpVal();
                val.setId(SnowflakeIdWorker.getUuid());
                val.setDvpRuleId(dvpRule.getId());
                val.setProjectId(dvpRule.getProjectId());
                val.setVisitId(Long.parseLong(ids[0]));
                val.setFormId(Long.parseLong(ids[1]));
                val.setFormDetailId(Long.parseLong(ids[2]));
                // 获取变量的信息，判断变量的类型
                TemplateFormDetail detail = templateFormDetailMapper.selectByPrimaryKey(Long.parseLong(ids[2]));
                if (detail != null) {
                    val.setFieldType(detail.getType());
                    val.setFormDetailId(Long.parseLong(ids[2]));
                    val.setDicResource(detail.getDicResource());
                } else {
                    TemplateFormTable templateFormTable = templateFormTableMapper.selectByPrimaryKey(Long.parseLong(ids[2]));
                    if (templateFormTable != null) {
                        val.setFieldType(templateFormTable.getType());
                        val.setFormDetailId(templateFormTable.getFormDetailId());
                        val.setDicResource(templateFormTable.getDicResource());
                    }
                }
                templateFormDvpValMapper.insert(val);
            }
        }
        return insert > 0 ? CommonResult.success("") : CommonResult.failed();
    }
    
    /**
     * 编辑
     *
     * @param dvpRule 逻辑配置信息
     * @return
     */
    public CommonResult<Object> modify(TemplateFormDvpRule dvpRule) {
        dvpRule.setUpdateUserId(SecurityUtils.getUserIdValue());
        dvpRule.setUpdateTime(new Date());
        int insert = templateFormDvpRuleMapper.updateByPrimaryKeySelective(dvpRule);
        
        if (StringUtils.isNotEmpty(dvpRule.getArithmeticValue())) {
            TemplateFormDvpValExample example = new TemplateFormDvpValExample();
            TemplateFormDvpValExample.Criteria criteria = example.createCriteria();
            criteria.andDvpRuleIdEqualTo(dvpRule.getId());
            templateFormDvpValMapper.deleteByExample(example);
            
            Pattern pattern = Pattern.compile("\\$(.*?)\\$");
            Matcher matcher = pattern.matcher(dvpRule.getArithmeticValue());
            while (matcher.find()) {
                String content = matcher.group(1);
                String[] ids = content.split("_");
                TemplateFormDvpVal val = new TemplateFormDvpVal();
                val.setDvpRuleId(dvpRule.getId());
                val.setId(SnowflakeIdWorker.getUuid());
                val.setProjectId(dvpRule.getProjectId());
                val.setVisitId(Long.parseLong(ids[0]));
                val.setFormId(Long.parseLong(ids[1]));
                // 获取变量的信息，判断变量的类型
                TemplateFormDetail detail = templateFormDetailMapper.selectByPrimaryKey(Long.parseLong(ids[2]));
                if (detail != null) {
                    val.setFieldType(detail.getType());
                    val.setFormDetailId(Long.parseLong(ids[2]));
                    val.setDicResource(detail.getDicResource());
                } else {
                    TemplateFormTable templateFormTable = templateFormTableMapper.selectByPrimaryKey(Long.parseLong(ids[2]));
                    if (templateFormTable != null) {
                        val.setFieldType(templateFormTable.getType());
                        val.setFormDetailId(templateFormTable.getFormDetailId());
                        val.setDicResource(templateFormTable.getDicResource());
                    }
                }
                templateFormDvpValMapper.insert(val);
            }
        }
        return insert > 0 ? CommonResult.success("") : CommonResult.failed();
    }
    
    /**
     * 编辑
     *
     * @param ruleId 逻辑配置信息的ID
     * @return
     */
    @Override
    public CommonResult<Object> enable(Long ruleId) {
        TemplateFormDvpRule rule = templateFormDvpRuleMapper.selectByPrimaryKey(ruleId);
        if (rule != null) {
            rule.getEnabled();
            if (rule.getEnabled()) {
                rule.setEnabled(false);
            } else {
                rule.setEnabled(true);
            }
        }
        int insert = templateFormDvpRuleMapper.updateByPrimaryKeySelective(rule);
        return insert > 0 ? CommonResult.success("") : CommonResult.failed();
    }
    
    /**
     * 列表
     *
     * @param pageNum
     * @param pageSize
     * @param ruleType  触发类型
     * @param checkName 逻辑名称
     * @param projectId
     * @return
     */
    @Override
    public CommonPage<Object> list(Integer pageNum, Integer pageSize, String ruleType, String checkName, String projectId) {
        
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        TemplateFormDvpRuleExample example = new TemplateFormDvpRuleExample();
        TemplateFormDvpRuleExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotEmpty(projectId)) {
            criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        }
        if (StringUtils.isNotEmpty(checkName)) {
            criteria.andCheckNameLike("%" + checkName + "%");
        }
        if (StringUtils.isNotEmpty(ruleType)) {
            criteria.andRuleTypeEqualTo(ruleType);
        }
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        example.setOrderByClause(" create_time desc ");
        List<TemplateFormDvpRule> list = templateFormDvpRuleMapper.selectByExampleWithBLOBs(example);
        return commonPageListWrapper(pageNum, pageSize, page, list);
    }
    
    /**
     * 删除
     *
     * @param ruleId 规则ID
     * @return
     */
    @Override
    @Transactional
    public CommonResult<Object> remove(Long ruleId) {
        TemplateFormDvpRule rule = templateFormDvpRuleMapper.selectByPrimaryKey(ruleId);
        if (rule != null) {
            // 判断是否已经存在质疑了，如果不存在可以删除，如果已经存在，不可以删除。
            ProjectTesteeChallengeExample example = new ProjectTesteeChallengeExample();
            ProjectTesteeChallengeExample.Criteria criteria = example.createCriteria();
            criteria.andDvpRuleIdEqualTo(ruleId);
            long l = projectTesteeChallengeMapper.countByExample(example);
            if (l > 0) {
                return CommonResult.failed("系统质疑已经存在,无法删除");
            }
            int i = templateFormDvpRuleMapper.deleteByPrimaryKey(ruleId);
            TemplateFormDvpValExample exampleVal = new TemplateFormDvpValExample();
            TemplateFormDvpValExample.Criteria criteriaVal = exampleVal.createCriteria();
            criteriaVal.andDvpRuleIdEqualTo(ruleId);
            templateFormDvpValMapper.deleteByExample(exampleVal);
            return i > 0 ? CommonResult.success("") : CommonResult.failed();
        }
        return CommonResult.failed("质疑不存在");
        
    }
    
    @Override
    public List<TemplateFormDvpRule> searchRules(TemplateFormDvpRule dvpRule) {
        
        TemplateFormDvpRuleExample example = new TemplateFormDvpRuleExample();
        TemplateFormDvpRuleExample.Criteria criteria = example.createCriteria();
        if (dvpRule.getProjectId() != null) {
            criteria.andProjectIdEqualTo(dvpRule.getProjectId());
        }
        if (dvpRule.getVisitId() != null) {
            criteria.andVisitIdEqualTo(dvpRule.getVisitId());
        }
        if (dvpRule.getFormId() != null) {
            criteria.andFormIdEqualTo(dvpRule.getFormId());
        }
        if (dvpRule.getFormDetailId() != null) {
            criteria.andFormDetailIdEqualTo(dvpRule.getFormDetailId());
        }
        if (dvpRule.getFormTableId() != null) {
            criteria.andFormTableIdEqualTo(dvpRule.getFormTableId());
        }
        criteria.andEnabledEqualTo(true);
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        return templateFormDvpRuleMapper.selectByExample(example);
    }
    
    
    /**
     * 公式计算
     * @param rule 规则 例如 a>2
     * @param data 参与计算的数据 例如 k=a v=3
     * @return
     */
    public Object compute(String rule, Map<String, Object> data) {
        try {
            if(rule.contains("setDateLessThan")){
                log.info("rule: {}", rule);
                //AviatorEvaluator.addFunction(new AviatorConfig.SetDateLessThanFunction());
            }
            Expression compiledExp = AviatorEvaluator.compile(rule);
            Object execute = compiledExp.execute(data);
            if (execute instanceof Double) {
                BigDecimal bd = new BigDecimal(String.valueOf(execute));
                return bd.setScale(0, RoundingMode.HALF_UP);
            }
            if (execute instanceof String) {
                try {
                    double v1 = Double.parseDouble(execute.toString());
                    BigDecimal bd = new BigDecimal(v1);
                    return bd.setScale(0, RoundingMode.HALF_UP);
                } catch (Exception e) {
                    return execute;
                }
            }
            return execute;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }
    
    public static void main(String[] args) {
        // 定义日期格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        
        // 定义两个日期字符串
        String dateStr1 = "2025-05-12";
        String dateStr2 = "2025-06-15";
        
        
        
        
        
        // 解析日期字符串为日期对象
        /*Date date1 = null;
        Date date2 = null;
        try {
            date1 = dateFormat.parse(dateStr1);
            date2 = dateFormat.parse(dateStr2);
        } catch (ParseException e) {
            e.printStackTrace();
            return;
        }*/
        
        // 注册自定义函数
        //AviatorEvaluator.addFunction(new DateDiffFunction());
        AviatorEvaluator.addFunction(new AviatorConfig.DateBetweenDayFunction());
        
        // 创建表达式
        String expression = "date_between_day(date1, date2)";
        Expression compiledExp = AviatorEvaluator.compile(expression);
        
        // 创建变量环境
        Map<String, Object> env = new HashMap<>();
        env.put("date1", dateStr1);
        env.put("date2", dateStr2);
        
        // 执行表达式
        Double interval = (Double) compiledExp.execute(env);
        
        // 输出结果
        System.out.println("日期间隔（天数）: " + interval);
    }
    
    // 自定义日期差函数
    public static class DateDiffFunction extends AbstractFunction {
        @Override
        public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
            Date date1 = coerceToDate(arg1.getValue(env));
            Date date2 = coerceToDate(arg2.getValue(env));
            
            long diff = date2.getTime() - date1.getTime();
            long days = diff / (24 * 60 * 60 * 1000);
            
            return new AviatorDouble(days);
        }
        
        @Override
        public String getName() {
            return "date_between_day";
        }
        
        // 手动实现将对象转换为 Date 类型的方法
        private Date coerceToDate(Object obj) {
            if (obj instanceof Date) {
                return (Date) obj;
            } else if (obj instanceof String) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                try {
                    return dateFormat.parse((String) obj);
                } catch (ParseException e) {
                    throw new IllegalArgumentException("Invalid date format. Please use this format:'yyyy-MM-dd'");
                }
            } else {
                throw new IllegalArgumentException("Unsupported type for date coercion");
            }
        }
    }
}