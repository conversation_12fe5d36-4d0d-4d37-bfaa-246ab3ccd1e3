package com.haoys.user.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * AI聊天消息实体类
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Data
@ApiModel(value = "AiChatMessage", description = "AI聊天消息")
public class AiChatMessage implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "主键ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    
    @ApiModelProperty(value = "消息ID")
    private String messageId;
    
    @ApiModelProperty(value = "会话ID")
    private String sessionId;
    
    @ApiModelProperty(value = "用户ID")
    private String userId;
    
    @ApiModelProperty(value = "角色(user,assistant,system)")
    private String role;
    
    @ApiModelProperty(value = "消息内容")
    private String content;
    
    @ApiModelProperty(value = "内容类型(text,image,file)")
    private String contentType;
    
    @ApiModelProperty(value = "文件信息(JSON格式)")
    private String fileInfo;
    
    @ApiModelProperty(value = "消息Token数")
    private Integer tokens;
    
    @ApiModelProperty(value = "消息消费金额")
    private BigDecimal cost;
    
    @ApiModelProperty(value = "响应时间(毫秒)")
    private Integer responseTime;
    
    @ApiModelProperty(value = "是否流式响应")
    private Boolean isStream;
    
    @ApiModelProperty(value = "错误信息")
    private String errorInfo;
    
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
