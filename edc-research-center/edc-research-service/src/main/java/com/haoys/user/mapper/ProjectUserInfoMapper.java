package com.haoys.user.mapper;

import com.haoys.user.domain.param.project.ProjectUserQueryParam;
import com.haoys.user.domain.vo.project.ProjectUserVo;
import com.haoys.user.model.ProjectUserInfo;
import com.haoys.user.model.ProjectUserInfoExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProjectUserInfoMapper {
    long countByExample(ProjectUserInfoExample example);

    int deleteByExample(ProjectUserInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectUserInfo record);

    int insertSelective(ProjectUserInfo record);

    List<ProjectUserInfo> selectByExample(ProjectUserInfoExample example);

    ProjectUserInfo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectUserInfo record, @Param("example") ProjectUserInfoExample example);

    int updateByExample(@Param("record") ProjectUserInfo record, @Param("example") ProjectUserInfoExample example);

    int updateByPrimaryKeySelective(ProjectUserInfo record);

    int updateByPrimaryKey(ProjectUserInfo record);

    /**
     * 查询项目成员管理集合
     * @return
     */
    List<ProjectUserVo> selectProjectUserListForPage(@Param(value = "projectId") String projectId,
                                                     @Param(value = "accountName") String accountName,
                                                     @Param(value = "aseMobile") String aseMobile,
                                                     @Param(value = "projectOrgId") String projectOrgId,
                                                     @Param(value = "activeStatus") String activeStatus,
                                                     @Param(value = "status") String status,
                                                     @Param(value = "createUserId")String createUserId,
                                                     @Param(value = "lockStatus")Boolean lockStatus);

    ProjectUserInfo getProjectUserInfo(String projectId, String systemUserId);

    /**
     * 删除项目用户
     * @param projectId
     * @param userId
     */
    void deleteProjectUserInfo(String projectId, String userId);

    /**
     * 获取当前登录人受邀请的信息
     * @param projectUserQueryParam
     * @return
     */
    List<ProjectUserVo> selectProjectUserListAndUserId(ProjectUserQueryParam projectUserQueryParam);

    /**
     * 查询系统用户项目列表
     * @param systemUserId
     * @return
     */
    List<ProjectUserInfo> getProjectUserListByUserId(String systemUserId);
}
