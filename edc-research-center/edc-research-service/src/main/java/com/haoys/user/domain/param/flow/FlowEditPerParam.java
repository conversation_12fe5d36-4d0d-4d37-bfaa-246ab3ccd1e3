package com.haoys.user.domain.param.flow;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FlowEditPerParam {

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "流程id")
    private Long flowId;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "PC端可查看权限： 0.选中 1.取消")
    private Boolean pcShowPer;

    @ApiModelProperty(value = "PC端可编辑权限： 0.选中 1.取消")
    private Boolean pcEditPer;

    @ApiModelProperty(value = "移动端可查看权限： 0.选中 1.取消")
    private Boolean mobShowPer;

    @ApiModelProperty(value = "移动端可编辑权限： 0.选中 1.取消")
    private Boolean mobEditPer;

}
