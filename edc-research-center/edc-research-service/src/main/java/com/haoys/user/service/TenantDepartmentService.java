package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.param.system.SystemUserAuthParam;
import com.haoys.user.domain.vo.system.SystemDepartmentVo;
import com.haoys.user.domain.vo.system.SystemUserAuthVo;
import com.haoys.user.domain.vo.system.SystemUserInfoVo;

import java.util.List;
import java.util.Map;

public interface TenantDepartmentService {

    Map<String, Object> getDepartmentList(String id);

    int addDepartment(String name, String personCharge, String description);

    int editDepartment(Long id , String name, String personCharge, String description);

    String deleteDepartment(Long id);

    CommonPage<SystemUserInfoVo> getTenantUserListForPage(String enterpriseId, String realName, String username, Integer status, Boolean activeStatus, Boolean lockStatus, Integer pageNum, Integer pageSize);

    int editDepartmentUserStatus(Long id);
    
    /**
     * 企业管理-查询成员信息
     * @param systemUserId
     * @return
     */
    SystemUserAuthVo getSystemUserInfo(String systemUserId);

    /**
     * 企业管理-添加成员+权限授权
     * @param systemUserAuthParam
     * @return
     */
    CustomResult saveSystemUserAndProjectUser(SystemUserAuthParam systemUserAuthParam);

    List<SystemDepartmentVo> getDepartmentDrop(String id);

    CustomResult deleteSystemUserById(String userId);

}
