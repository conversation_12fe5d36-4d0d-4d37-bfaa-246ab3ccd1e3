package com.haoys.user.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class RctsRandomizedBlindRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public RctsRandomizedBlindRecordExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdIsNull() {
            addCriterion("project_org_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdIsNotNull() {
            addCriterion("project_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdEqualTo(Long value) {
            addCriterion("project_org_id =", value, "projectOrgId");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdNotEqualTo(Long value) {
            addCriterion("project_org_id <>", value, "projectOrgId");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdGreaterThan(Long value) {
            addCriterion("project_org_id >", value, "projectOrgId");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_org_id >=", value, "projectOrgId");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdLessThan(Long value) {
            addCriterion("project_org_id <", value, "projectOrgId");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("project_org_id <=", value, "projectOrgId");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdIn(List<Long> values) {
            addCriterion("project_org_id in", values, "projectOrgId");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdNotIn(List<Long> values) {
            addCriterion("project_org_id not in", values, "projectOrgId");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdBetween(Long value1, Long value2) {
            addCriterion("project_org_id between", value1, value2, "projectOrgId");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("project_org_id not between", value1, value2, "projectOrgId");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNull() {
            addCriterion("batch_no is null");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNotNull() {
            addCriterion("batch_no is not null");
            return (Criteria) this;
        }

        public Criteria andBatchNoEqualTo(Integer value) {
            addCriterion("batch_no =", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotEqualTo(Integer value) {
            addCriterion("batch_no <>", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThan(Integer value) {
            addCriterion("batch_no >", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThanOrEqualTo(Integer value) {
            addCriterion("batch_no >=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThan(Integer value) {
            addCriterion("batch_no <", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThanOrEqualTo(Integer value) {
            addCriterion("batch_no <=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoIn(List<Integer> values) {
            addCriterion("batch_no in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotIn(List<Integer> values) {
            addCriterion("batch_no not in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoBetween(Integer value1, Integer value2) {
            addCriterion("batch_no between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotBetween(Integer value1, Integer value2) {
            addCriterion("batch_no not between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchItemNoIsNull() {
            addCriterion("batch_item_no is null");
            return (Criteria) this;
        }

        public Criteria andBatchItemNoIsNotNull() {
            addCriterion("batch_item_no is not null");
            return (Criteria) this;
        }

        public Criteria andBatchItemNoEqualTo(Integer value) {
            addCriterion("batch_item_no =", value, "batchItemNo");
            return (Criteria) this;
        }

        public Criteria andBatchItemNoNotEqualTo(Integer value) {
            addCriterion("batch_item_no <>", value, "batchItemNo");
            return (Criteria) this;
        }

        public Criteria andBatchItemNoGreaterThan(Integer value) {
            addCriterion("batch_item_no >", value, "batchItemNo");
            return (Criteria) this;
        }

        public Criteria andBatchItemNoGreaterThanOrEqualTo(Integer value) {
            addCriterion("batch_item_no >=", value, "batchItemNo");
            return (Criteria) this;
        }

        public Criteria andBatchItemNoLessThan(Integer value) {
            addCriterion("batch_item_no <", value, "batchItemNo");
            return (Criteria) this;
        }

        public Criteria andBatchItemNoLessThanOrEqualTo(Integer value) {
            addCriterion("batch_item_no <=", value, "batchItemNo");
            return (Criteria) this;
        }

        public Criteria andBatchItemNoIn(List<Integer> values) {
            addCriterion("batch_item_no in", values, "batchItemNo");
            return (Criteria) this;
        }

        public Criteria andBatchItemNoNotIn(List<Integer> values) {
            addCriterion("batch_item_no not in", values, "batchItemNo");
            return (Criteria) this;
        }

        public Criteria andBatchItemNoBetween(Integer value1, Integer value2) {
            addCriterion("batch_item_no between", value1, value2, "batchItemNo");
            return (Criteria) this;
        }

        public Criteria andBatchItemNoNotBetween(Integer value1, Integer value2) {
            addCriterion("batch_item_no not between", value1, value2, "batchItemNo");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberIsNull() {
            addCriterion("randomized_number is null");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberIsNotNull() {
            addCriterion("randomized_number is not null");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberEqualTo(String value) {
            addCriterion("randomized_number =", value, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberNotEqualTo(String value) {
            addCriterion("randomized_number <>", value, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberGreaterThan(String value) {
            addCriterion("randomized_number >", value, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberGreaterThanOrEqualTo(String value) {
            addCriterion("randomized_number >=", value, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberLessThan(String value) {
            addCriterion("randomized_number <", value, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberLessThanOrEqualTo(String value) {
            addCriterion("randomized_number <=", value, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberLike(String value) {
            addCriterion("randomized_number like", value, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberNotLike(String value) {
            addCriterion("randomized_number not like", value, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberIn(List<String> values) {
            addCriterion("randomized_number in", values, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberNotIn(List<String> values) {
            addCriterion("randomized_number not in", values, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberBetween(String value1, String value2) {
            addCriterion("randomized_number between", value1, value2, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andRandomizedNumberNotBetween(String value1, String value2) {
            addCriterion("randomized_number not between", value1, value2, "randomizedNumber");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameIsNull() {
            addCriterion("join_group_name is null");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameIsNotNull() {
            addCriterion("join_group_name is not null");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameEqualTo(String value) {
            addCriterion("join_group_name =", value, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameNotEqualTo(String value) {
            addCriterion("join_group_name <>", value, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameGreaterThan(String value) {
            addCriterion("join_group_name >", value, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameGreaterThanOrEqualTo(String value) {
            addCriterion("join_group_name >=", value, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameLessThan(String value) {
            addCriterion("join_group_name <", value, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameLessThanOrEqualTo(String value) {
            addCriterion("join_group_name <=", value, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameLike(String value) {
            addCriterion("join_group_name like", value, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameNotLike(String value) {
            addCriterion("join_group_name not like", value, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameIn(List<String> values) {
            addCriterion("join_group_name in", values, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameNotIn(List<String> values) {
            addCriterion("join_group_name not in", values, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameBetween(String value1, String value2) {
            addCriterion("join_group_name between", value1, value2, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andJoinGroupNameNotBetween(String value1, String value2) {
            addCriterion("join_group_name not between", value1, value2, "joinGroupName");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeIsNull() {
            addCriterion("randomized_time is null");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeIsNotNull() {
            addCriterion("randomized_time is not null");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeEqualTo(Date value) {
            addCriterion("randomized_time =", value, "randomizedTime");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeNotEqualTo(Date value) {
            addCriterion("randomized_time <>", value, "randomizedTime");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeGreaterThan(Date value) {
            addCriterion("randomized_time >", value, "randomizedTime");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("randomized_time >=", value, "randomizedTime");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeLessThan(Date value) {
            addCriterion("randomized_time <", value, "randomizedTime");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeLessThanOrEqualTo(Date value) {
            addCriterion("randomized_time <=", value, "randomizedTime");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeIn(List<Date> values) {
            addCriterion("randomized_time in", values, "randomizedTime");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeNotIn(List<Date> values) {
            addCriterion("randomized_time not in", values, "randomizedTime");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeBetween(Date value1, Date value2) {
            addCriterion("randomized_time between", value1, value2, "randomizedTime");
            return (Criteria) this;
        }

        public Criteria andRandomizedTimeNotBetween(Date value1, Date value2) {
            addCriterion("randomized_time not between", value1, value2, "randomizedTime");
            return (Criteria) this;
        }

        public Criteria andBindTesteeIdIsNull() {
            addCriterion("bind_testee_id is null");
            return (Criteria) this;
        }

        public Criteria andBindTesteeIdIsNotNull() {
            addCriterion("bind_testee_id is not null");
            return (Criteria) this;
        }

        public Criteria andBindTesteeIdEqualTo(String value) {
            addCriterion("bind_testee_id =", value, "bindTesteeId");
            return (Criteria) this;
        }

        public Criteria andBindTesteeIdNotEqualTo(String value) {
            addCriterion("bind_testee_id <>", value, "bindTesteeId");
            return (Criteria) this;
        }

        public Criteria andBindTesteeIdGreaterThan(String value) {
            addCriterion("bind_testee_id >", value, "bindTesteeId");
            return (Criteria) this;
        }

        public Criteria andBindTesteeIdGreaterThanOrEqualTo(String value) {
            addCriterion("bind_testee_id >=", value, "bindTesteeId");
            return (Criteria) this;
        }

        public Criteria andBindTesteeIdLessThan(String value) {
            addCriterion("bind_testee_id <", value, "bindTesteeId");
            return (Criteria) this;
        }

        public Criteria andBindTesteeIdLessThanOrEqualTo(String value) {
            addCriterion("bind_testee_id <=", value, "bindTesteeId");
            return (Criteria) this;
        }

        public Criteria andBindTesteeIdLike(String value) {
            addCriterion("bind_testee_id like", value, "bindTesteeId");
            return (Criteria) this;
        }

        public Criteria andBindTesteeIdNotLike(String value) {
            addCriterion("bind_testee_id not like", value, "bindTesteeId");
            return (Criteria) this;
        }

        public Criteria andBindTesteeIdIn(List<String> values) {
            addCriterion("bind_testee_id in", values, "bindTesteeId");
            return (Criteria) this;
        }

        public Criteria andBindTesteeIdNotIn(List<String> values) {
            addCriterion("bind_testee_id not in", values, "bindTesteeId");
            return (Criteria) this;
        }

        public Criteria andBindTesteeIdBetween(String value1, String value2) {
            addCriterion("bind_testee_id between", value1, value2, "bindTesteeId");
            return (Criteria) this;
        }

        public Criteria andBindTesteeIdNotBetween(String value1, String value2) {
            addCriterion("bind_testee_id not between", value1, value2, "bindTesteeId");
            return (Criteria) this;
        }

        public Criteria andBindRandomizedTimeIsNull() {
            addCriterion("bind_randomized_time is null");
            return (Criteria) this;
        }

        public Criteria andBindRandomizedTimeIsNotNull() {
            addCriterion("bind_randomized_time is not null");
            return (Criteria) this;
        }

        public Criteria andBindRandomizedTimeEqualTo(Date value) {
            addCriterion("bind_randomized_time =", value, "bindRandomizedTime");
            return (Criteria) this;
        }

        public Criteria andBindRandomizedTimeNotEqualTo(Date value) {
            addCriterion("bind_randomized_time <>", value, "bindRandomizedTime");
            return (Criteria) this;
        }

        public Criteria andBindRandomizedTimeGreaterThan(Date value) {
            addCriterion("bind_randomized_time >", value, "bindRandomizedTime");
            return (Criteria) this;
        }

        public Criteria andBindRandomizedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("bind_randomized_time >=", value, "bindRandomizedTime");
            return (Criteria) this;
        }

        public Criteria andBindRandomizedTimeLessThan(Date value) {
            addCriterion("bind_randomized_time <", value, "bindRandomizedTime");
            return (Criteria) this;
        }

        public Criteria andBindRandomizedTimeLessThanOrEqualTo(Date value) {
            addCriterion("bind_randomized_time <=", value, "bindRandomizedTime");
            return (Criteria) this;
        }

        public Criteria andBindRandomizedTimeIn(List<Date> values) {
            addCriterion("bind_randomized_time in", values, "bindRandomizedTime");
            return (Criteria) this;
        }

        public Criteria andBindRandomizedTimeNotIn(List<Date> values) {
            addCriterion("bind_randomized_time not in", values, "bindRandomizedTime");
            return (Criteria) this;
        }

        public Criteria andBindRandomizedTimeBetween(Date value1, Date value2) {
            addCriterion("bind_randomized_time between", value1, value2, "bindRandomizedTime");
            return (Criteria) this;
        }

        public Criteria andBindRandomizedTimeNotBetween(Date value1, Date value2) {
            addCriterion("bind_randomized_time not between", value1, value2, "bindRandomizedTime");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andExpandIsNull() {
            addCriterion("expand is null");
            return (Criteria) this;
        }

        public Criteria andExpandIsNotNull() {
            addCriterion("expand is not null");
            return (Criteria) this;
        }

        public Criteria andExpandEqualTo(String value) {
            addCriterion("expand =", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotEqualTo(String value) {
            addCriterion("expand <>", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandGreaterThan(String value) {
            addCriterion("expand >", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandGreaterThanOrEqualTo(String value) {
            addCriterion("expand >=", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLessThan(String value) {
            addCriterion("expand <", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLessThanOrEqualTo(String value) {
            addCriterion("expand <=", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLike(String value) {
            addCriterion("expand like", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotLike(String value) {
            addCriterion("expand not like", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandIn(List<String> values) {
            addCriterion("expand in", values, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotIn(List<String> values) {
            addCriterion("expand not in", values, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandBetween(String value1, String value2) {
            addCriterion("expand between", value1, value2, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotBetween(String value1, String value2) {
            addCriterion("expand not between", value1, value2, "expand");
            return (Criteria) this;
        }

        public Criteria andDataFromIsNull() {
            addCriterion("data_from is null");
            return (Criteria) this;
        }

        public Criteria andDataFromIsNotNull() {
            addCriterion("data_from is not null");
            return (Criteria) this;
        }

        public Criteria andDataFromEqualTo(String value) {
            addCriterion("data_from =", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromNotEqualTo(String value) {
            addCriterion("data_from <>", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromGreaterThan(String value) {
            addCriterion("data_from >", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromGreaterThanOrEqualTo(String value) {
            addCriterion("data_from >=", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromLessThan(String value) {
            addCriterion("data_from <", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromLessThanOrEqualTo(String value) {
            addCriterion("data_from <=", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromLike(String value) {
            addCriterion("data_from like", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromNotLike(String value) {
            addCriterion("data_from not like", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromIn(List<String> values) {
            addCriterion("data_from in", values, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromNotIn(List<String> values) {
            addCriterion("data_from not in", values, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromBetween(String value1, String value2) {
            addCriterion("data_from between", value1, value2, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromNotBetween(String value1, String value2) {
            addCriterion("data_from not between", value1, value2, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNull() {
            addCriterion("platform_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNotNull() {
            addCriterion("platform_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdEqualTo(String value) {
            addCriterion("platform_id =", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotEqualTo(String value) {
            addCriterion("platform_id <>", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThan(String value) {
            addCriterion("platform_id >", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThanOrEqualTo(String value) {
            addCriterion("platform_id >=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThan(String value) {
            addCriterion("platform_id <", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThanOrEqualTo(String value) {
            addCriterion("platform_id <=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLike(String value) {
            addCriterion("platform_id like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotLike(String value) {
            addCriterion("platform_id not like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIn(List<String> values) {
            addCriterion("platform_id in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotIn(List<String> values) {
            addCriterion("platform_id not in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdBetween(String value1, String value2) {
            addCriterion("platform_id between", value1, value2, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotBetween(String value1, String value2) {
            addCriterion("platform_id not between", value1, value2, "platformId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}