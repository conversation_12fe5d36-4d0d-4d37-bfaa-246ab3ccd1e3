package com.haoys.user.domain.vo.project;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

public class ProjectTesteeFileVo implements Serializable {
    
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "主键id")
    private Long id;
    
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "项目id")
    private Long projectId;
    
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "方案id")
    private Long planId;
    
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "访视id")
    private Long visitId;
    
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "表单项id")
    private Long formId;
    
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "受试者id")
    private Long testeeId;
    
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "字段分组id")
    private Long groupId;

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "文件原始名称")
    private String originalName;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "文件路径")
    private String fileUrl;

    @ApiModelProperty(value = "文件后缀")
    private String fileExt;

    @ApiModelProperty(value = "文件上传路径")
    private String uploadPath;
    
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "表格行记录编号")
    private Long tableNumber;
    
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "业务资源id")
    private Long resourceId;

    @ApiModelProperty(value = "资源类型")
    private String resourceType;

    @ApiModelProperty(value = "受试者批量上传文件")
    private Boolean batchUpload;

    @ApiModelProperty(value = "批量使用OCR识别")
    private Boolean batchOpenOcr;

    @ApiModelProperty(value = "属性配置")
    private String uploadConfig;

    @ApiModelProperty(value = "图片类型 原图-original 裁剪-crop")
    private String imageType;

    @ApiModelProperty(value = "扩展字段")
    private String expand;

    @ApiModelProperty(value = "扩展属性1")
    private String extData1;

    @ApiModelProperty(value = "扩展属性2")
    private String extData2;
    
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "上一个文件编号id")
    private Long prePageNo;
    
    @ApiModelProperty(value = "文件编号")
    private Integer fileNumber;

    @ApiModelProperty(value = "文件是否合并 0/1")
    private Boolean ifMontage;
    
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "设置合并后的文件id")
    private Long targetFileId;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "数据状态 0-有效 1-无效")
    private String status;

    @ApiModelProperty(value = "是否启用OCR识别 0-不启用 1-开启")
    private Boolean ocrFlag;

    @ApiModelProperty(value = "上传任务日期")
    private Date taskDate;

    @ApiModelProperty(value = "版本号")
    private String version;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public Long getVisitId() {
        return visitId;
    }

    public void setVisitId(Long visitId) {
        this.visitId = visitId;
    }

    public Long getFormId() {
        return formId;
    }

    public void setFormId(Long formId) {
        this.formId = formId;
    }

    public Long getTesteeId() {
        return testeeId;
    }

    public void setTesteeId(Long testeeId) {
        this.testeeId = testeeId;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getOriginalName() {
        return originalName;
    }

    public void setOriginalName(String originalName) {
        this.originalName = originalName;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getFileExt() {
        return fileExt;
    }

    public void setFileExt(String fileExt) {
        this.fileExt = fileExt;
    }

    public String getUploadPath() {
        return uploadPath;
    }

    public void setUploadPath(String uploadPath) {
        this.uploadPath = uploadPath;
    }

    public Long getTableNumber() {
        return tableNumber;
    }

    public void setTableNumber(Long tableNumber) {
        this.tableNumber = tableNumber;
    }

    public Long getResourceId() {
        return resourceId;
    }

    public void setResourceId(Long resourceId) {
        this.resourceId = resourceId;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public Boolean getBatchUpload() {
        return batchUpload;
    }

    public void setBatchUpload(Boolean batchUpload) {
        this.batchUpload = batchUpload;
    }

    public Boolean getBatchOpenOcr() {
        return batchOpenOcr;
    }

    public void setBatchOpenOcr(Boolean batchOpenOcr) {
        this.batchOpenOcr = batchOpenOcr;
    }

    public String getUploadConfig() {
        return uploadConfig;
    }

    public void setUploadConfig(String uploadConfig) {
        this.uploadConfig = uploadConfig;
    }

    public String getImageType() {
        return imageType;
    }

    public void setImageType(String imageType) {
        this.imageType = imageType;
    }

    public String getExpand() {
        return expand;
    }

    public void setExpand(String expand) {
        this.expand = expand;
    }

    public String getExtData1() {
        return extData1;
    }

    public void setExtData1(String extData1) {
        this.extData1 = extData1;
    }

    public String getExtData2() {
        return extData2;
    }

    public void setExtData2(String extData2) {
        this.extData2 = extData2;
    }

    public Long getPrePageNo() {
        return prePageNo;
    }

    public void setPrePageNo(Long prePageNo) {
        this.prePageNo = prePageNo;
    }

    public Integer getFileNumber() {
        return fileNumber;
    }

    public void setFileNumber(Integer fileNumber) {
        this.fileNumber = fileNumber;
    }

    public Boolean getIfMontage() {
        return ifMontage;
    }

    public void setIfMontage(Boolean ifMontage) {
        this.ifMontage = ifMontage;
    }

    public Long getTargetFileId() {
        return targetFileId;
    }

    public void setTargetFileId(Long targetFileId) {
        this.targetFileId = targetFileId;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getOcrFlag() {
        return ocrFlag;
    }

    public void setOcrFlag(Boolean ocrFlag) {
        this.ocrFlag = ocrFlag;
    }

    public Date getTaskDate() {
        return taskDate;
    }

    public void setTaskDate(Date taskDate) {
        this.taskDate = taskDate;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", projectId=").append(projectId);
        sb.append(", planId=").append(planId);
        sb.append(", visitId=").append(visitId);
        sb.append(", formId=").append(formId);
        sb.append(", testeeId=").append(testeeId);
        sb.append(", groupId=").append(groupId);
        sb.append(", groupName=").append(groupName);
        sb.append(", originalName=").append(originalName);
        sb.append(", fileName=").append(fileName);
        sb.append(", fileUrl=").append(fileUrl);
        sb.append(", fileExt=").append(fileExt);
        sb.append(", uploadPath=").append(uploadPath);
        sb.append(", tableNumber=").append(tableNumber);
        sb.append(", resourceId=").append(resourceId);
        sb.append(", resourceType=").append(resourceType);
        sb.append(", batchUpload=").append(batchUpload);
        sb.append(", batchOpenOcr=").append(batchOpenOcr);
        sb.append(", uploadConfig=").append(uploadConfig);
        sb.append(", imageType=").append(imageType);
        sb.append(", expand=").append(expand);
        sb.append(", extData1=").append(extData1);
        sb.append(", extData2=").append(extData2);
        sb.append(", prePageNo=").append(prePageNo);
        sb.append(", fileNumber=").append(fileNumber);
        sb.append(", ifMontage=").append(ifMontage);
        sb.append(", targetFileId=").append(targetFileId);
        sb.append(", sort=").append(sort);
        sb.append(", status=").append(status);
        sb.append(", ocrFlag=").append(ocrFlag);
        sb.append(", taskDate=").append(taskDate);
        sb.append(", version=").append(version);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", tenantId=").append(tenantId);
        sb.append(", platformId=").append(platformId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}