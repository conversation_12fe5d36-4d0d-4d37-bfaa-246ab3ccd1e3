package com.haoys.user.domain.message;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class SmsResponse {
    @JsonProperty("RequestId")
    private String requestId;
    
    @JsonProperty("SendStatusSet")
    private List<SmsStatus> sendStatusSet;
    
    // Getters and Setters
    
    @Data
    public static class SmsStatus {
        @JsonProperty("SerialNo")
        private String serialNo;
        
        @JsonProperty("PhoneNumber")
        private String phoneNumber;
        
        @JsonProperty("Fee")
        private int fee;
        
        @JsonProperty("SessionContext")
        private String sessionContext;
        
        @JsonProperty("Code")
        private String code;
        
        @JsonProperty("Message")
        private String message;
        
        @JsonProperty("IsoCode")
        private String isoCode;
        
        // Get<PERSON> and Setters
    }
}
