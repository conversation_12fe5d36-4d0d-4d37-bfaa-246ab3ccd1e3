package com.haoys.user.mapper;

import com.haoys.user.domain.vo.rcts.RandomizedParamsVo;
import com.haoys.user.model.RctsRandomizedParamsConfig;
import com.haoys.user.model.RctsRandomizedParamsConfigExample;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface RctsRandomizedParamsConfigMapper {
    long countByExample(RctsRandomizedParamsConfigExample example);

    int deleteByExample(RctsRandomizedParamsConfigExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RctsRandomizedParamsConfig record);

    int insertSelective(RctsRandomizedParamsConfig record);

    List<RctsRandomizedParamsConfig> selectByExample(RctsRandomizedParamsConfigExample example);

    RctsRandomizedParamsConfig selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RctsRandomizedParamsConfig record, @Param("example") RctsRandomizedParamsConfigExample example);

    int updateByExample(@Param("record") RctsRandomizedParamsConfig record, @Param("example") RctsRandomizedParamsConfigExample example);

    int updateByPrimaryKeySelective(RctsRandomizedParamsConfig record);

    int updateByPrimaryKey(RctsRandomizedParamsConfig record);


    RandomizedParamsVo getRandomizedParamsConfig(@Param("status") String status, @Param("projectId") Long projectId);

    @Select("SELECT COUNT(*) FROM rcts_randomized_params_config WHERE STATUS = '0' AND project_id = #{projectId} ")
    int countByProjectId(@Param("projectId") Long projectId);
}