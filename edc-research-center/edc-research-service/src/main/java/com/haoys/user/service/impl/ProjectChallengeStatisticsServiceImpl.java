package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.enums.ProjectChallengeEnum;
import com.haoys.user.enums.ProjectRoleEnum;
import com.haoys.user.enums.ProjectTesteeReviewEnum;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.project.ProjectChallengeStatisticsParam;
import com.haoys.user.domain.vo.testee.ProjectChallengeStatisticsVo;
import com.haoys.user.domain.wrapper.ProjectUserInfoWrapper;
import com.haoys.user.mapper.ProjectTesteeChallengeMapper;
import com.haoys.user.mapper.ProjectUserOrgMapper;
import com.haoys.user.service.ProjectChallengeStatisticsService;
import com.haoys.user.service.ProjectUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 质疑统计实现类
 */
@Service
public class ProjectChallengeStatisticsServiceImpl extends BaseService implements ProjectChallengeStatisticsService {


    // TODO
    @Autowired
    private ProjectTesteeChallengeMapper projectTesteeChallengeMapper;
    @Autowired
    private ProjectUserOrgMapper projectUserOrgMapper;

    @Autowired
    private ProjectUserService projectUserService;


    /**
     * 获取质疑统计列表
     * @param param 搜索参数
     * @return 分页；列表
     */
    @Override
    public CommonPage<ProjectChallengeStatisticsVo> list(ProjectChallengeStatisticsParam param) {
        Page<Object> page = null;
        List<ProjectChallengeStatisticsVo> projectCheckList = new ArrayList<>();
        List<String> ids = setOrgIds(param);
        if (CollectionUtil.isNotEmpty(ids)) {
            page = PageHelper.startPage(param.getPageNum(), param.getPageSize());
            param.setIds(ids);
            // 获取质疑统计信息
            projectCheckList = projectTesteeChallengeMapper.getChallengeListByOrgIds(param);
        }

        return commonPageListWrapper(param.getPageNum(), param.getPageSize(), page, projectCheckList);
    }

    private static void setChallengerNum(ProjectChallengeStatisticsVo challenger, Map<String, Integer> countMap) {
        // 未回复的数据
        Integer newChallengeNum = countMap.get(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_01.getName());
        if (newChallengeNum!=null){
            challenger.setNewChallengeNum(newChallengeNum);
        }
        // 已回复的数据
        Integer receiveChallengeNum = countMap.get(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName());
        if (receiveChallengeNum!=null){
            challenger.setReceiveChallengeNum(receiveChallengeNum);
        }
        // 已关闭的数据
        Integer closeChallengeNum = countMap.get(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_03.getName());
        if (closeChallengeNum!=null){
            challenger.setCloseChallengeNum(closeChallengeNum);
        }
    }

    /**
     * 质疑统计柱状图
     * @param param 搜索条件（比如所属中心）
     * @return 柱状图的数据
     */
    @Override
    public ProjectChallengeStatisticsVo challengeChart(ProjectChallengeStatisticsParam param) {
        param.setReviewStatus(ProjectTesteeReviewEnum.PROJECT_TESTEE_REVIEW_STATUS_02.getName());
        ProjectChallengeStatisticsVo vo = new ProjectChallengeStatisticsVo();
        List<String> ids = setOrgIds(param);
        if (CollectionUtil.isNotEmpty(ids)) {
            param.setIds(ids);
            // 获取用户所拥有权限的所属中心未回复，已回复，已关闭的质疑的数量
            List<ProjectChallengeStatisticsVo> list = projectTesteeChallengeMapper.challengeChart(param);
            if (CollectionUtil.isNotEmpty(list)){
                // 转化为map 内容为 key:质疑类型，value:质疑数量
                Map<String, Integer> map = list.stream().collect(Collectors.
                        toMap(ProjectChallengeStatisticsVo::getReplyStatus, ProjectChallengeStatisticsVo::getCount));
                // 设置对象的质疑数量
                setChallengerNum(vo, map);
            }
        }
        return vo;
    }

    /**
     * 获取当前用户所拥有权限的所属中心
     * @param param 搜索参数
     * @return 所属中心的id集合
     */
    public  List<String> setOrgIds(ProjectChallengeStatisticsParam param) {
        List<String> ids = new ArrayList<>();
        if (StringUtils.isBlank(param.getOwnerOrgId())) {
            ProjectUserInfoWrapper projectUserDataVo = projectUserService.getProjectUserRoleInfoByUserId(param.getProjectId(), "", param.getUserId());
            if (projectUserDataVo != null) {
                //String ename = projectUserDataVo.getRoleCode();
                String userId = param.getUserId();
                /*if (ProjectRoleEnum.PROJECT_PA.getCode().equals(ename)) {
                    userId = null;
                }*/
                ids = projectUserOrgMapper.getOrgIdsByProjectIdAndUserId(param.getProjectId(), userId, "");
            }
        } else {
            ids.add(param.getOwnerOrgId());
        }
        return ids;
    }
    /**
     * 角色质疑统计柱状图
     * @param param 搜索条件（比如所属中心）
     * @return 柱状图的数据
     */
    @Override
    public ProjectChallengeStatisticsVo challengeRoleChart(ProjectChallengeStatisticsParam param) {
        param.setReviewStatus(ProjectTesteeReviewEnum.PROJECT_TESTEE_REVIEW_STATUS_02.getName());
        ProjectChallengeStatisticsVo vo = new ProjectChallengeStatisticsVo();
        List<String> ids = setOrgIds(param);
        if (CollectionUtil.isNotEmpty(ids)) {
            param.setIds(ids);
            // 系统质疑统计数量
            Integer systemNum = projectTesteeChallengeMapper.getSystemChallenge(param);
            // cra质疑统计数量
            param.setRoleCode(ProjectRoleEnum.PROJECT_CRA.getCode());
            Integer craNum = projectTesteeChallengeMapper.getCraChallenge(param);
            vo.setSystemChallengeNum(systemNum);
            vo.setCraChallengeNum(craNum);
        }
        return vo;
    }
}
