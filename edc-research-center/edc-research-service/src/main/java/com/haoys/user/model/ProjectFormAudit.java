package com.haoys.user.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ProjectFormAudit implements Serializable {
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    private Long id;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "方案id")
    private Long planId;

    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @ApiModelProperty(value = "表单id")
    private Long formId;

    @ApiModelProperty(value = "受试者id")
    private Long testeeId;

    @ApiModelProperty(value = "审核人id")
    private Long auditUserId;

    @ApiModelProperty(value = "数据状态")
    private String status;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "提交人id")
    private Long submitUserId;

    @ApiModelProperty(value = "创建时间")
    private Date submitTime;

    @ApiModelProperty(value = "更新时间")
    private Date auditTime;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

    private static final long serialVersionUID = 1L;
}
