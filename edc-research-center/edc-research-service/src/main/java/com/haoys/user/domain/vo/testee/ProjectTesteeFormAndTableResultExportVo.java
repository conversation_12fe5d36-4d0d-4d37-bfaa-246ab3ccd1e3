package com.haoys.user.domain.vo.testee;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ProjectTesteeFormAndTableResultExportVo implements Serializable {

    private String visitId;
    private String formId;
    private String formDetailId;
    private String testeeId;
    private String code;
    private String realName;
    private String ownerOrgId;
    private String ownerOrgName;
    private String ownerDoctor;
    private Date createTime;
    private String rowNumber;
    private String formDetailSort;
    private String tableSort;
    private String label;
    private String fieldName;
    private String fieldValue;
}
