package com.haoys.user.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProjectVisitUserExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectVisitUserExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdIsNull() {
            addCriterion("testee_id is null");
            return (Criteria) this;
        }

        public Criteria andTesteeIdIsNotNull() {
            addCriterion("testee_id is not null");
            return (Criteria) this;
        }

        public Criteria andTesteeIdEqualTo(Long value) {
            addCriterion("testee_id =", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdNotEqualTo(Long value) {
            addCriterion("testee_id <>", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdGreaterThan(Long value) {
            addCriterion("testee_id >", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("testee_id >=", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdLessThan(Long value) {
            addCriterion("testee_id <", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdLessThanOrEqualTo(Long value) {
            addCriterion("testee_id <=", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdIn(List<Long> values) {
            addCriterion("testee_id in", values, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdNotIn(List<Long> values) {
            addCriterion("testee_id not in", values, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdBetween(Long value1, Long value2) {
            addCriterion("testee_id between", value1, value2, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdNotBetween(Long value1, Long value2) {
            addCriterion("testee_id not between", value1, value2, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeConfigIsNull() {
            addCriterion("testee_config is null");
            return (Criteria) this;
        }

        public Criteria andTesteeConfigIsNotNull() {
            addCriterion("testee_config is not null");
            return (Criteria) this;
        }

        public Criteria andTesteeConfigEqualTo(String value) {
            addCriterion("testee_config =", value, "testeeConfig");
            return (Criteria) this;
        }

        public Criteria andTesteeConfigNotEqualTo(String value) {
            addCriterion("testee_config <>", value, "testeeConfig");
            return (Criteria) this;
        }

        public Criteria andTesteeConfigGreaterThan(String value) {
            addCriterion("testee_config >", value, "testeeConfig");
            return (Criteria) this;
        }

        public Criteria andTesteeConfigGreaterThanOrEqualTo(String value) {
            addCriterion("testee_config >=", value, "testeeConfig");
            return (Criteria) this;
        }

        public Criteria andTesteeConfigLessThan(String value) {
            addCriterion("testee_config <", value, "testeeConfig");
            return (Criteria) this;
        }

        public Criteria andTesteeConfigLessThanOrEqualTo(String value) {
            addCriterion("testee_config <=", value, "testeeConfig");
            return (Criteria) this;
        }

        public Criteria andTesteeConfigLike(String value) {
            addCriterion("testee_config like", value, "testeeConfig");
            return (Criteria) this;
        }

        public Criteria andTesteeConfigNotLike(String value) {
            addCriterion("testee_config not like", value, "testeeConfig");
            return (Criteria) this;
        }

        public Criteria andTesteeConfigIn(List<String> values) {
            addCriterion("testee_config in", values, "testeeConfig");
            return (Criteria) this;
        }

        public Criteria andTesteeConfigNotIn(List<String> values) {
            addCriterion("testee_config not in", values, "testeeConfig");
            return (Criteria) this;
        }

        public Criteria andTesteeConfigBetween(String value1, String value2) {
            addCriterion("testee_config between", value1, value2, "testeeConfig");
            return (Criteria) this;
        }

        public Criteria andTesteeConfigNotBetween(String value1, String value2) {
            addCriterion("testee_config not between", value1, value2, "testeeConfig");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeIsNull() {
            addCriterion("testee_code is null");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeIsNotNull() {
            addCriterion("testee_code is not null");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeEqualTo(String value) {
            addCriterion("testee_code =", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeNotEqualTo(String value) {
            addCriterion("testee_code <>", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeGreaterThan(String value) {
            addCriterion("testee_code >", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("testee_code >=", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeLessThan(String value) {
            addCriterion("testee_code <", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeLessThanOrEqualTo(String value) {
            addCriterion("testee_code <=", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeLike(String value) {
            addCriterion("testee_code like", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeNotLike(String value) {
            addCriterion("testee_code not like", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeIn(List<String> values) {
            addCriterion("testee_code in", values, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeNotIn(List<String> values) {
            addCriterion("testee_code not in", values, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeBetween(String value1, String value2) {
            addCriterion("testee_code between", value1, value2, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeNotBetween(String value1, String value2) {
            addCriterion("testee_code not between", value1, value2, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andVisitCardNoIsNull() {
            addCriterion("visit_card_no is null");
            return (Criteria) this;
        }

        public Criteria andVisitCardNoIsNotNull() {
            addCriterion("visit_card_no is not null");
            return (Criteria) this;
        }

        public Criteria andVisitCardNoEqualTo(String value) {
            addCriterion("visit_card_no =", value, "visitCardNo");
            return (Criteria) this;
        }

        public Criteria andVisitCardNoNotEqualTo(String value) {
            addCriterion("visit_card_no <>", value, "visitCardNo");
            return (Criteria) this;
        }

        public Criteria andVisitCardNoGreaterThan(String value) {
            addCriterion("visit_card_no >", value, "visitCardNo");
            return (Criteria) this;
        }

        public Criteria andVisitCardNoGreaterThanOrEqualTo(String value) {
            addCriterion("visit_card_no >=", value, "visitCardNo");
            return (Criteria) this;
        }

        public Criteria andVisitCardNoLessThan(String value) {
            addCriterion("visit_card_no <", value, "visitCardNo");
            return (Criteria) this;
        }

        public Criteria andVisitCardNoLessThanOrEqualTo(String value) {
            addCriterion("visit_card_no <=", value, "visitCardNo");
            return (Criteria) this;
        }

        public Criteria andVisitCardNoLike(String value) {
            addCriterion("visit_card_no like", value, "visitCardNo");
            return (Criteria) this;
        }

        public Criteria andVisitCardNoNotLike(String value) {
            addCriterion("visit_card_no not like", value, "visitCardNo");
            return (Criteria) this;
        }

        public Criteria andVisitCardNoIn(List<String> values) {
            addCriterion("visit_card_no in", values, "visitCardNo");
            return (Criteria) this;
        }

        public Criteria andVisitCardNoNotIn(List<String> values) {
            addCriterion("visit_card_no not in", values, "visitCardNo");
            return (Criteria) this;
        }

        public Criteria andVisitCardNoBetween(String value1, String value2) {
            addCriterion("visit_card_no between", value1, value2, "visitCardNo");
            return (Criteria) this;
        }

        public Criteria andVisitCardNoNotBetween(String value1, String value2) {
            addCriterion("visit_card_no not between", value1, value2, "visitCardNo");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdIsNull() {
            addCriterion("owner_org_id is null");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdIsNotNull() {
            addCriterion("owner_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdEqualTo(String value) {
            addCriterion("owner_org_id =", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdNotEqualTo(String value) {
            addCriterion("owner_org_id <>", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdGreaterThan(String value) {
            addCriterion("owner_org_id >", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdGreaterThanOrEqualTo(String value) {
            addCriterion("owner_org_id >=", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdLessThan(String value) {
            addCriterion("owner_org_id <", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdLessThanOrEqualTo(String value) {
            addCriterion("owner_org_id <=", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdLike(String value) {
            addCriterion("owner_org_id like", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdNotLike(String value) {
            addCriterion("owner_org_id not like", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdIn(List<String> values) {
            addCriterion("owner_org_id in", values, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdNotIn(List<String> values) {
            addCriterion("owner_org_id not in", values, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdBetween(String value1, String value2) {
            addCriterion("owner_org_id between", value1, value2, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdNotBetween(String value1, String value2) {
            addCriterion("owner_org_id not between", value1, value2, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameIsNull() {
            addCriterion("owner_org_name is null");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameIsNotNull() {
            addCriterion("owner_org_name is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameEqualTo(String value) {
            addCriterion("owner_org_name =", value, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameNotEqualTo(String value) {
            addCriterion("owner_org_name <>", value, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameGreaterThan(String value) {
            addCriterion("owner_org_name >", value, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("owner_org_name >=", value, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameLessThan(String value) {
            addCriterion("owner_org_name <", value, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameLessThanOrEqualTo(String value) {
            addCriterion("owner_org_name <=", value, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameLike(String value) {
            addCriterion("owner_org_name like", value, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameNotLike(String value) {
            addCriterion("owner_org_name not like", value, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameIn(List<String> values) {
            addCriterion("owner_org_name in", values, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameNotIn(List<String> values) {
            addCriterion("owner_org_name not in", values, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameBetween(String value1, String value2) {
            addCriterion("owner_org_name between", value1, value2, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgNameNotBetween(String value1, String value2) {
            addCriterion("owner_org_name not between", value1, value2, "ownerOrgName");
            return (Criteria) this;
        }

        public Criteria andOwnerDoctorIdIsNull() {
            addCriterion("owner_doctor_id is null");
            return (Criteria) this;
        }

        public Criteria andOwnerDoctorIdIsNotNull() {
            addCriterion("owner_doctor_id is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerDoctorIdEqualTo(String value) {
            addCriterion("owner_doctor_id =", value, "ownerDoctorId");
            return (Criteria) this;
        }

        public Criteria andOwnerDoctorIdNotEqualTo(String value) {
            addCriterion("owner_doctor_id <>", value, "ownerDoctorId");
            return (Criteria) this;
        }

        public Criteria andOwnerDoctorIdGreaterThan(String value) {
            addCriterion("owner_doctor_id >", value, "ownerDoctorId");
            return (Criteria) this;
        }

        public Criteria andOwnerDoctorIdGreaterThanOrEqualTo(String value) {
            addCriterion("owner_doctor_id >=", value, "ownerDoctorId");
            return (Criteria) this;
        }

        public Criteria andOwnerDoctorIdLessThan(String value) {
            addCriterion("owner_doctor_id <", value, "ownerDoctorId");
            return (Criteria) this;
        }

        public Criteria andOwnerDoctorIdLessThanOrEqualTo(String value) {
            addCriterion("owner_doctor_id <=", value, "ownerDoctorId");
            return (Criteria) this;
        }

        public Criteria andOwnerDoctorIdLike(String value) {
            addCriterion("owner_doctor_id like", value, "ownerDoctorId");
            return (Criteria) this;
        }

        public Criteria andOwnerDoctorIdNotLike(String value) {
            addCriterion("owner_doctor_id not like", value, "ownerDoctorId");
            return (Criteria) this;
        }

        public Criteria andOwnerDoctorIdIn(List<String> values) {
            addCriterion("owner_doctor_id in", values, "ownerDoctorId");
            return (Criteria) this;
        }

        public Criteria andOwnerDoctorIdNotIn(List<String> values) {
            addCriterion("owner_doctor_id not in", values, "ownerDoctorId");
            return (Criteria) this;
        }

        public Criteria andOwnerDoctorIdBetween(String value1, String value2) {
            addCriterion("owner_doctor_id between", value1, value2, "ownerDoctorId");
            return (Criteria) this;
        }

        public Criteria andOwnerDoctorIdNotBetween(String value1, String value2) {
            addCriterion("owner_doctor_id not between", value1, value2, "ownerDoctorId");
            return (Criteria) this;
        }

        public Criteria andInformedDateIsNull() {
            addCriterion("informed_date is null");
            return (Criteria) this;
        }

        public Criteria andInformedDateIsNotNull() {
            addCriterion("informed_date is not null");
            return (Criteria) this;
        }

        public Criteria andInformedDateEqualTo(Date value) {
            addCriterion("informed_date =", value, "informedDate");
            return (Criteria) this;
        }

        public Criteria andInformedDateNotEqualTo(Date value) {
            addCriterion("informed_date <>", value, "informedDate");
            return (Criteria) this;
        }

        public Criteria andInformedDateGreaterThan(Date value) {
            addCriterion("informed_date >", value, "informedDate");
            return (Criteria) this;
        }

        public Criteria andInformedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("informed_date >=", value, "informedDate");
            return (Criteria) this;
        }

        public Criteria andInformedDateLessThan(Date value) {
            addCriterion("informed_date <", value, "informedDate");
            return (Criteria) this;
        }

        public Criteria andInformedDateLessThanOrEqualTo(Date value) {
            addCriterion("informed_date <=", value, "informedDate");
            return (Criteria) this;
        }

        public Criteria andInformedDateIn(List<Date> values) {
            addCriterion("informed_date in", values, "informedDate");
            return (Criteria) this;
        }

        public Criteria andInformedDateNotIn(List<Date> values) {
            addCriterion("informed_date not in", values, "informedDate");
            return (Criteria) this;
        }

        public Criteria andInformedDateBetween(Date value1, Date value2) {
            addCriterion("informed_date between", value1, value2, "informedDate");
            return (Criteria) this;
        }

        public Criteria andInformedDateNotBetween(Date value1, Date value2) {
            addCriterion("informed_date not between", value1, value2, "informedDate");
            return (Criteria) this;
        }

        public Criteria andResourceFormIdIsNull() {
            addCriterion("resource_form_id is null");
            return (Criteria) this;
        }

        public Criteria andResourceFormIdIsNotNull() {
            addCriterion("resource_form_id is not null");
            return (Criteria) this;
        }

        public Criteria andResourceFormIdEqualTo(Long value) {
            addCriterion("resource_form_id =", value, "resourceFormId");
            return (Criteria) this;
        }

        public Criteria andResourceFormIdNotEqualTo(Long value) {
            addCriterion("resource_form_id <>", value, "resourceFormId");
            return (Criteria) this;
        }

        public Criteria andResourceFormIdGreaterThan(Long value) {
            addCriterion("resource_form_id >", value, "resourceFormId");
            return (Criteria) this;
        }

        public Criteria andResourceFormIdGreaterThanOrEqualTo(Long value) {
            addCriterion("resource_form_id >=", value, "resourceFormId");
            return (Criteria) this;
        }

        public Criteria andResourceFormIdLessThan(Long value) {
            addCriterion("resource_form_id <", value, "resourceFormId");
            return (Criteria) this;
        }

        public Criteria andResourceFormIdLessThanOrEqualTo(Long value) {
            addCriterion("resource_form_id <=", value, "resourceFormId");
            return (Criteria) this;
        }

        public Criteria andResourceFormIdIn(List<Long> values) {
            addCriterion("resource_form_id in", values, "resourceFormId");
            return (Criteria) this;
        }

        public Criteria andResourceFormIdNotIn(List<Long> values) {
            addCriterion("resource_form_id not in", values, "resourceFormId");
            return (Criteria) this;
        }

        public Criteria andResourceFormIdBetween(Long value1, Long value2) {
            addCriterion("resource_form_id between", value1, value2, "resourceFormId");
            return (Criteria) this;
        }

        public Criteria andResourceFormIdNotBetween(Long value1, Long value2) {
            addCriterion("resource_form_id not between", value1, value2, "resourceFormId");
            return (Criteria) this;
        }

        public Criteria andSelfRecordIsNull() {
            addCriterion("self_record is null");
            return (Criteria) this;
        }

        public Criteria andSelfRecordIsNotNull() {
            addCriterion("self_record is not null");
            return (Criteria) this;
        }

        public Criteria andSelfRecordEqualTo(Boolean value) {
            addCriterion("self_record =", value, "selfRecord");
            return (Criteria) this;
        }

        public Criteria andSelfRecordNotEqualTo(Boolean value) {
            addCriterion("self_record <>", value, "selfRecord");
            return (Criteria) this;
        }

        public Criteria andSelfRecordGreaterThan(Boolean value) {
            addCriterion("self_record >", value, "selfRecord");
            return (Criteria) this;
        }

        public Criteria andSelfRecordGreaterThanOrEqualTo(Boolean value) {
            addCriterion("self_record >=", value, "selfRecord");
            return (Criteria) this;
        }

        public Criteria andSelfRecordLessThan(Boolean value) {
            addCriterion("self_record <", value, "selfRecord");
            return (Criteria) this;
        }

        public Criteria andSelfRecordLessThanOrEqualTo(Boolean value) {
            addCriterion("self_record <=", value, "selfRecord");
            return (Criteria) this;
        }

        public Criteria andSelfRecordIn(List<Boolean> values) {
            addCriterion("self_record in", values, "selfRecord");
            return (Criteria) this;
        }

        public Criteria andSelfRecordNotIn(List<Boolean> values) {
            addCriterion("self_record not in", values, "selfRecord");
            return (Criteria) this;
        }

        public Criteria andSelfRecordBetween(Boolean value1, Boolean value2) {
            addCriterion("self_record between", value1, value2, "selfRecord");
            return (Criteria) this;
        }

        public Criteria andSelfRecordNotBetween(Boolean value1, Boolean value2) {
            addCriterion("self_record not between", value1, value2, "selfRecord");
            return (Criteria) this;
        }

        public Criteria andBindResultIsNull() {
            addCriterion("bind_result is null");
            return (Criteria) this;
        }

        public Criteria andBindResultIsNotNull() {
            addCriterion("bind_result is not null");
            return (Criteria) this;
        }

        public Criteria andBindResultEqualTo(Boolean value) {
            addCriterion("bind_result =", value, "bindResult");
            return (Criteria) this;
        }

        public Criteria andBindResultNotEqualTo(Boolean value) {
            addCriterion("bind_result <>", value, "bindResult");
            return (Criteria) this;
        }

        public Criteria andBindResultGreaterThan(Boolean value) {
            addCriterion("bind_result >", value, "bindResult");
            return (Criteria) this;
        }

        public Criteria andBindResultGreaterThanOrEqualTo(Boolean value) {
            addCriterion("bind_result >=", value, "bindResult");
            return (Criteria) this;
        }

        public Criteria andBindResultLessThan(Boolean value) {
            addCriterion("bind_result <", value, "bindResult");
            return (Criteria) this;
        }

        public Criteria andBindResultLessThanOrEqualTo(Boolean value) {
            addCriterion("bind_result <=", value, "bindResult");
            return (Criteria) this;
        }

        public Criteria andBindResultIn(List<Boolean> values) {
            addCriterion("bind_result in", values, "bindResult");
            return (Criteria) this;
        }

        public Criteria andBindResultNotIn(List<Boolean> values) {
            addCriterion("bind_result not in", values, "bindResult");
            return (Criteria) this;
        }

        public Criteria andBindResultBetween(Boolean value1, Boolean value2) {
            addCriterion("bind_result between", value1, value2, "bindResult");
            return (Criteria) this;
        }

        public Criteria andBindResultNotBetween(Boolean value1, Boolean value2) {
            addCriterion("bind_result not between", value1, value2, "bindResult");
            return (Criteria) this;
        }

        public Criteria andBindTimeIsNull() {
            addCriterion("bind_time is null");
            return (Criteria) this;
        }

        public Criteria andBindTimeIsNotNull() {
            addCriterion("bind_time is not null");
            return (Criteria) this;
        }

        public Criteria andBindTimeEqualTo(Date value) {
            addCriterion("bind_time =", value, "bindTime");
            return (Criteria) this;
        }

        public Criteria andBindTimeNotEqualTo(Date value) {
            addCriterion("bind_time <>", value, "bindTime");
            return (Criteria) this;
        }

        public Criteria andBindTimeGreaterThan(Date value) {
            addCriterion("bind_time >", value, "bindTime");
            return (Criteria) this;
        }

        public Criteria andBindTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("bind_time >=", value, "bindTime");
            return (Criteria) this;
        }

        public Criteria andBindTimeLessThan(Date value) {
            addCriterion("bind_time <", value, "bindTime");
            return (Criteria) this;
        }

        public Criteria andBindTimeLessThanOrEqualTo(Date value) {
            addCriterion("bind_time <=", value, "bindTime");
            return (Criteria) this;
        }

        public Criteria andBindTimeIn(List<Date> values) {
            addCriterion("bind_time in", values, "bindTime");
            return (Criteria) this;
        }

        public Criteria andBindTimeNotIn(List<Date> values) {
            addCriterion("bind_time not in", values, "bindTime");
            return (Criteria) this;
        }

        public Criteria andBindTimeBetween(Date value1, Date value2) {
            addCriterion("bind_time between", value1, value2, "bindTime");
            return (Criteria) this;
        }

        public Criteria andBindTimeNotBetween(Date value1, Date value2) {
            addCriterion("bind_time not between", value1, value2, "bindTime");
            return (Criteria) this;
        }

        public Criteria andReviewFlagIsNull() {
            addCriterion("review_flag is null");
            return (Criteria) this;
        }

        public Criteria andReviewFlagIsNotNull() {
            addCriterion("review_flag is not null");
            return (Criteria) this;
        }

        public Criteria andReviewFlagEqualTo(Boolean value) {
            addCriterion("review_flag =", value, "reviewFlag");
            return (Criteria) this;
        }

        public Criteria andReviewFlagNotEqualTo(Boolean value) {
            addCriterion("review_flag <>", value, "reviewFlag");
            return (Criteria) this;
        }

        public Criteria andReviewFlagGreaterThan(Boolean value) {
            addCriterion("review_flag >", value, "reviewFlag");
            return (Criteria) this;
        }

        public Criteria andReviewFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("review_flag >=", value, "reviewFlag");
            return (Criteria) this;
        }

        public Criteria andReviewFlagLessThan(Boolean value) {
            addCriterion("review_flag <", value, "reviewFlag");
            return (Criteria) this;
        }

        public Criteria andReviewFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("review_flag <=", value, "reviewFlag");
            return (Criteria) this;
        }

        public Criteria andReviewFlagIn(List<Boolean> values) {
            addCriterion("review_flag in", values, "reviewFlag");
            return (Criteria) this;
        }

        public Criteria andReviewFlagNotIn(List<Boolean> values) {
            addCriterion("review_flag not in", values, "reviewFlag");
            return (Criteria) this;
        }

        public Criteria andReviewFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("review_flag between", value1, value2, "reviewFlag");
            return (Criteria) this;
        }

        public Criteria andReviewFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("review_flag not between", value1, value2, "reviewFlag");
            return (Criteria) this;
        }

        public Criteria andReviewStatusIsNull() {
            addCriterion("review_status is null");
            return (Criteria) this;
        }

        public Criteria andReviewStatusIsNotNull() {
            addCriterion("review_status is not null");
            return (Criteria) this;
        }

        public Criteria andReviewStatusEqualTo(String value) {
            addCriterion("review_status =", value, "reviewStatus");
            return (Criteria) this;
        }

        public Criteria andReviewStatusNotEqualTo(String value) {
            addCriterion("review_status <>", value, "reviewStatus");
            return (Criteria) this;
        }

        public Criteria andReviewStatusGreaterThan(String value) {
            addCriterion("review_status >", value, "reviewStatus");
            return (Criteria) this;
        }

        public Criteria andReviewStatusGreaterThanOrEqualTo(String value) {
            addCriterion("review_status >=", value, "reviewStatus");
            return (Criteria) this;
        }

        public Criteria andReviewStatusLessThan(String value) {
            addCriterion("review_status <", value, "reviewStatus");
            return (Criteria) this;
        }

        public Criteria andReviewStatusLessThanOrEqualTo(String value) {
            addCriterion("review_status <=", value, "reviewStatus");
            return (Criteria) this;
        }

        public Criteria andReviewStatusLike(String value) {
            addCriterion("review_status like", value, "reviewStatus");
            return (Criteria) this;
        }

        public Criteria andReviewStatusNotLike(String value) {
            addCriterion("review_status not like", value, "reviewStatus");
            return (Criteria) this;
        }

        public Criteria andReviewStatusIn(List<String> values) {
            addCriterion("review_status in", values, "reviewStatus");
            return (Criteria) this;
        }

        public Criteria andReviewStatusNotIn(List<String> values) {
            addCriterion("review_status not in", values, "reviewStatus");
            return (Criteria) this;
        }

        public Criteria andReviewStatusBetween(String value1, String value2) {
            addCriterion("review_status between", value1, value2, "reviewStatus");
            return (Criteria) this;
        }

        public Criteria andReviewStatusNotBetween(String value1, String value2) {
            addCriterion("review_status not between", value1, value2, "reviewStatus");
            return (Criteria) this;
        }

        public Criteria andReviewUserIdIsNull() {
            addCriterion("review_user_id is null");
            return (Criteria) this;
        }

        public Criteria andReviewUserIdIsNotNull() {
            addCriterion("review_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andReviewUserIdEqualTo(String value) {
            addCriterion("review_user_id =", value, "reviewUserId");
            return (Criteria) this;
        }

        public Criteria andReviewUserIdNotEqualTo(String value) {
            addCriterion("review_user_id <>", value, "reviewUserId");
            return (Criteria) this;
        }

        public Criteria andReviewUserIdGreaterThan(String value) {
            addCriterion("review_user_id >", value, "reviewUserId");
            return (Criteria) this;
        }

        public Criteria andReviewUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("review_user_id >=", value, "reviewUserId");
            return (Criteria) this;
        }

        public Criteria andReviewUserIdLessThan(String value) {
            addCriterion("review_user_id <", value, "reviewUserId");
            return (Criteria) this;
        }

        public Criteria andReviewUserIdLessThanOrEqualTo(String value) {
            addCriterion("review_user_id <=", value, "reviewUserId");
            return (Criteria) this;
        }

        public Criteria andReviewUserIdLike(String value) {
            addCriterion("review_user_id like", value, "reviewUserId");
            return (Criteria) this;
        }

        public Criteria andReviewUserIdNotLike(String value) {
            addCriterion("review_user_id not like", value, "reviewUserId");
            return (Criteria) this;
        }

        public Criteria andReviewUserIdIn(List<String> values) {
            addCriterion("review_user_id in", values, "reviewUserId");
            return (Criteria) this;
        }

        public Criteria andReviewUserIdNotIn(List<String> values) {
            addCriterion("review_user_id not in", values, "reviewUserId");
            return (Criteria) this;
        }

        public Criteria andReviewUserIdBetween(String value1, String value2) {
            addCriterion("review_user_id between", value1, value2, "reviewUserId");
            return (Criteria) this;
        }

        public Criteria andReviewUserIdNotBetween(String value1, String value2) {
            addCriterion("review_user_id not between", value1, value2, "reviewUserId");
            return (Criteria) this;
        }

        public Criteria andReviewTimeIsNull() {
            addCriterion("review_time is null");
            return (Criteria) this;
        }

        public Criteria andReviewTimeIsNotNull() {
            addCriterion("review_time is not null");
            return (Criteria) this;
        }

        public Criteria andReviewTimeEqualTo(Date value) {
            addCriterion("review_time =", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeNotEqualTo(Date value) {
            addCriterion("review_time <>", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeGreaterThan(Date value) {
            addCriterion("review_time >", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("review_time >=", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeLessThan(Date value) {
            addCriterion("review_time <", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeLessThanOrEqualTo(Date value) {
            addCriterion("review_time <=", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeIn(List<Date> values) {
            addCriterion("review_time in", values, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeNotIn(List<Date> values) {
            addCriterion("review_time not in", values, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeBetween(Date value1, Date value2) {
            addCriterion("review_time between", value1, value2, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeNotBetween(Date value1, Date value2) {
            addCriterion("review_time not between", value1, value2, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andBindResourceIsNull() {
            addCriterion("bind_resource is null");
            return (Criteria) this;
        }

        public Criteria andBindResourceIsNotNull() {
            addCriterion("bind_resource is not null");
            return (Criteria) this;
        }

        public Criteria andBindResourceEqualTo(String value) {
            addCriterion("bind_resource =", value, "bindResource");
            return (Criteria) this;
        }

        public Criteria andBindResourceNotEqualTo(String value) {
            addCriterion("bind_resource <>", value, "bindResource");
            return (Criteria) this;
        }

        public Criteria andBindResourceGreaterThan(String value) {
            addCriterion("bind_resource >", value, "bindResource");
            return (Criteria) this;
        }

        public Criteria andBindResourceGreaterThanOrEqualTo(String value) {
            addCriterion("bind_resource >=", value, "bindResource");
            return (Criteria) this;
        }

        public Criteria andBindResourceLessThan(String value) {
            addCriterion("bind_resource <", value, "bindResource");
            return (Criteria) this;
        }

        public Criteria andBindResourceLessThanOrEqualTo(String value) {
            addCriterion("bind_resource <=", value, "bindResource");
            return (Criteria) this;
        }

        public Criteria andBindResourceLike(String value) {
            addCriterion("bind_resource like", value, "bindResource");
            return (Criteria) this;
        }

        public Criteria andBindResourceNotLike(String value) {
            addCriterion("bind_resource not like", value, "bindResource");
            return (Criteria) this;
        }

        public Criteria andBindResourceIn(List<String> values) {
            addCriterion("bind_resource in", values, "bindResource");
            return (Criteria) this;
        }

        public Criteria andBindResourceNotIn(List<String> values) {
            addCriterion("bind_resource not in", values, "bindResource");
            return (Criteria) this;
        }

        public Criteria andBindResourceBetween(String value1, String value2) {
            addCriterion("bind_resource between", value1, value2, "bindResource");
            return (Criteria) this;
        }

        public Criteria andBindResourceNotBetween(String value1, String value2) {
            addCriterion("bind_resource not between", value1, value2, "bindResource");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andResearchStatusIsNull() {
            addCriterion("research_status is null");
            return (Criteria) this;
        }

        public Criteria andResearchStatusIsNotNull() {
            addCriterion("research_status is not null");
            return (Criteria) this;
        }

        public Criteria andResearchStatusEqualTo(String value) {
            addCriterion("research_status =", value, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andResearchStatusNotEqualTo(String value) {
            addCriterion("research_status <>", value, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andResearchStatusGreaterThan(String value) {
            addCriterion("research_status >", value, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andResearchStatusGreaterThanOrEqualTo(String value) {
            addCriterion("research_status >=", value, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andResearchStatusLessThan(String value) {
            addCriterion("research_status <", value, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andResearchStatusLessThanOrEqualTo(String value) {
            addCriterion("research_status <=", value, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andResearchStatusLike(String value) {
            addCriterion("research_status like", value, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andResearchStatusNotLike(String value) {
            addCriterion("research_status not like", value, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andResearchStatusIn(List<String> values) {
            addCriterion("research_status in", values, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andResearchStatusNotIn(List<String> values) {
            addCriterion("research_status not in", values, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andResearchStatusBetween(String value1, String value2) {
            addCriterion("research_status between", value1, value2, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andResearchStatusNotBetween(String value1, String value2) {
            addCriterion("research_status not between", value1, value2, "researchStatus");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(String value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(String value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(String value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(String value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLike(String value) {
            addCriterion("create_user_id like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotLike(String value) {
            addCriterion("create_user_id not like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<String> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<String> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(String value1, String value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(String value1, String value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNull() {
            addCriterion("platform_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNotNull() {
            addCriterion("platform_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdEqualTo(String value) {
            addCriterion("platform_id =", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotEqualTo(String value) {
            addCriterion("platform_id <>", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThan(String value) {
            addCriterion("platform_id >", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThanOrEqualTo(String value) {
            addCriterion("platform_id >=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThan(String value) {
            addCriterion("platform_id <", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThanOrEqualTo(String value) {
            addCriterion("platform_id <=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLike(String value) {
            addCriterion("platform_id like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotLike(String value) {
            addCriterion("platform_id not like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIn(List<String> values) {
            addCriterion("platform_id in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotIn(List<String> values) {
            addCriterion("platform_id not in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdBetween(String value1, String value2) {
            addCriterion("platform_id between", value1, value2, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotBetween(String value1, String value2) {
            addCriterion("platform_id not between", value1, value2, "platformId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}