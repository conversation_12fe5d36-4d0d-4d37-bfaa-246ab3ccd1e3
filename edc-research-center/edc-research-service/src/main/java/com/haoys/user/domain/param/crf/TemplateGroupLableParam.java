package com.haoys.user.domain.param.crf;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TemplateGroupLableParam implements Serializable {

    @ApiModelProperty(value = "分类id-编辑时设置")
    private Long id;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @ApiModelProperty(value = "分类名称")
    private String lableName;

    @ApiModelProperty(value = "父级节点")
    private Long parentId = 0L;

    @ApiModelProperty(value = "类型 1-表单项 2-访视时间")
    private String resourceType;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "排序")
    private Integer sort;

}
