package com.haoys.user.generator.template;

import lombok.extern.slf4j.Slf4j;
import com.haoys.user.generator.config.GeneratorConfig;
import com.haoys.user.generator.model.TableInfo;
import com.haoys.user.generator.model.ColumnInfo;
import com.haoys.user.generator.template.impl.*;

import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 模板引擎
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
@Slf4j
public class TemplateEngine {
    
    private final GeneratorConfig config;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    public TemplateEngine(GeneratorConfig config) {
        this.config = config;
    }
    
    /**
     * 生成实体类
     */
    public void generateEntity(TableInfo tableInfo, String filePath) throws IOException {
        Map<String, Object> context = createBaseContext(tableInfo);
        String content = EntityTemplate.generate(context);
        writeToFile(content, filePath);
    }
    
    /**
     * 生成Mapper接口
     */
    public void generateMapper(TableInfo tableInfo, String filePath) throws IOException {
        Map<String, Object> context = createBaseContext(tableInfo);
        String content = MapperTemplate.generate(context);
        writeToFile(content, filePath);
    }
    
    /**
     * 生成Mapper XML
     */
    public void generateMapperXml(TableInfo tableInfo, String filePath) throws IOException {
        Map<String, Object> context = createBaseContext(tableInfo);
        String content = MapperXmlTemplate.generate(context);
        writeToFile(content, filePath);
    }
    
    /**
     * 生成Service接口
     */
    public void generateService(TableInfo tableInfo, String filePath) throws IOException {
        Map<String, Object> context = createBaseContext(tableInfo);
        String content = ServiceTemplate.generate(context);
        writeToFile(content, filePath);
    }
    
    /**
     * 生成Service实现类
     */
    public void generateServiceImpl(TableInfo tableInfo, String filePath) throws IOException {
        Map<String, Object> context = createBaseContext(tableInfo);
        String content = ServiceImplTemplate.generate(context);
        writeToFile(content, filePath);
    }
    
    /**
     * 生成Controller
     */
    public void generateController(TableInfo tableInfo, String filePath) throws IOException {
        Map<String, Object> context = createBaseContext(tableInfo);
        String content = ControllerTemplate.generate(context);
        writeToFile(content, filePath);
    }
    
    /**
     * 生成测试类
     */
    public void generateTest(TableInfo tableInfo, String filePath) throws IOException {
        Map<String, Object> context = createBaseContext(tableInfo);
        String content = TestTemplate.generate(context);
        writeToFile(content, filePath);
    }
    
    /**
     * 创建基础上下文
     */
    private Map<String, Object> createBaseContext(TableInfo tableInfo) {
        Map<String, Object> context = new HashMap<>();
        
        // 基础信息
        context.put("config", config);
        context.put("tableInfo", tableInfo);
        context.put("currentDate", dateFormat.format(new Date()));
        
        // 包信息
        context.put("modelPackage", config.getModelPackage());
        context.put("mapperPackage", config.getMapperPackage());
        context.put("servicePackage", config.getServicePackage());
        context.put("serviceImplPackage", config.getServiceImplPackage());
        context.put("controllerPackage", config.getControllerPackage());
        
        // 类名信息
        context.put("entityName", tableInfo.getEntityName());
        context.put("entityNameLowerCase", tableInfo.getEntityNameLowerCase());
        context.put("mapperName", tableInfo.getEntityName() + "Mapper");
        context.put("serviceName", tableInfo.getEntityName() + "Service");
        context.put("serviceImplName", tableInfo.getEntityName() + "ServiceImpl");
        context.put("controllerName", tableInfo.getEntityName() + "Controller");
        
        // 主键信息
        ColumnInfo primaryKeyColumn = tableInfo.getPrimaryKeyColumn();
        if (primaryKeyColumn != null) {
            context.put("primaryKeyType", primaryKeyColumn.getJavaType());
            context.put("primaryKeyProperty", primaryKeyColumn.getJavaProperty());
            context.put("primaryKeyColumn", primaryKeyColumn.getColumnName());
        } else {
            context.put("primaryKeyType", "Long");
            context.put("primaryKeyProperty", "id");
            context.put("primaryKeyColumn", "id");
        }
        
        // 功能开关
        context.put("enableSwagger", config.isEnableSwagger());
        context.put("enableCache", config.isEnableCache());
        context.put("enableBatchOperations", config.isEnableBatchOperations());
        context.put("enableComplexQuery", config.isEnableComplexQuery());
        context.put("enableBlobQuery", config.isEnableBlobQuery());
        context.put("enableAggregateQuery", config.isEnableAggregateQuery());
        
        // 表信息
        context.put("tableName", tableInfo.getTableName());
        context.put("tableComment", tableInfo.getTableComment());
        context.put("columns", tableInfo.getColumns());
        context.put("nonPrimaryKeyColumns", tableInfo.getNonPrimaryKeyColumns());
        context.put("blobColumns", tableInfo.getBlobColumns());
        context.put("nonBlobColumns", tableInfo.getNonBlobColumns());
        context.put("hasBlobColumns", tableInfo.hasBlobColumns());
        context.put("hasDateColumns", tableInfo.hasDateColumns());
        
        return context;
    }
    
    /**
     * 写入文件
     */
    private void writeToFile(String content, String filePath) throws IOException {
        try (FileWriter writer = new FileWriter(filePath)) {
            writer.write(content);
            writer.flush();
        }
        log.debug("文件写入完成: {}", filePath);
    }
}
