package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.bussiness.RedisKeyContants;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.domain.enums.ProjectFlowReturnEnums;
import com.haoys.user.domain.param.flow.FlowParam;
import com.haoys.user.domain.param.project.ProjectVisitConfigParam;
import com.haoys.user.domain.vo.ecrf.TemplateFormConfigVo;
import com.haoys.user.domain.vo.flow.ProjectFlowFormVo;
import com.haoys.user.domain.vo.flow.ProjectFlowVo;
import com.haoys.user.domain.vo.project.ProjectVisitVo;
import com.haoys.user.mapper.FlowFormSetMapper;
import com.haoys.user.mapper.ProjectVisitConfigMapper;
import com.haoys.user.mapper.ProjectVisitTesteeRecordMapper;
import com.haoys.user.model.*;
import com.haoys.user.service.FlowPlanService;
import com.haoys.user.service.ProjectTesteeResultService;
import com.haoys.user.service.ProjectVisitConfigService;
import com.haoys.user.service.TemplateConfigService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class ProjectVisitConfigServiceImpl implements ProjectVisitConfigService {

    private final ProjectVisitConfigMapper projectVisitConfigMapper;
    private final ProjectVisitTesteeRecordMapper projectVisitTesteeRecordMapper;
    private final TemplateConfigService templateConfigService;
    private final RedisTemplateService redisTemplateService;
    private final ProjectTesteeResultService projectTesteeResultService;
    private final FlowFormSetMapper flowFormSetMapper;
    private final FlowPlanService flowPlanService;


    @Override
    public List<ProjectVisitConfig> getProjectVisitListForPage(String visitName, String templateId, String projectId, Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        ProjectVisitConfigExample example = new ProjectVisitConfigExample();
        ProjectVisitConfigExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(templateId)) {
            criteria.andTemplateIdEqualTo(Long.parseLong(templateId));
        }
        if (StringUtils.isNotBlank(projectId)) {
            criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        }
        if (StringUtils.isNotBlank(visitName)) {
            criteria.andVisitNameLike(visitName);
        }
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        example.setOrderByClause("sort asc");
        List<ProjectVisitConfig> projectVisitConfigList = projectVisitConfigMapper.selectByExample(example);
        return projectVisitConfigList;
    }

    @Override
    public CustomResult saveProjectVisitConfig(ProjectVisitConfigParam projectVisitConfigParam) {
        CustomResult customResult = new CustomResult();
        if(!projectVisitConfigParam.getAutoCreateTemplate() && projectVisitConfigParam.getProjectId() == null && projectVisitConfigParam.getTemplateId() == null){
            customResult.setMessage(BusinessConfig.RETURN_MESSAGE_RECORD_PARAMS_ERROR);
            return customResult;
        }
        Template template = new Template();
        if(StringUtils.isNotEmpty(projectVisitConfigParam.getConfigType())){
            //生成模版
            if(projectVisitConfigParam.getTemplateId() == null){
                template.setId(SnowflakeIdWorker.getUuid());
                template.setIfPrivate(false);
                template.setConfigType(projectVisitConfigParam.getConfigType());
                template.setStatus(BusinessConfig.VALID_STATUS);
                template.setCreateUserId(projectVisitConfigParam.getCreateUserId());
                template.setCreateTime(new Date());
                template.setTenantId(SecurityUtils.getSystemTenantId());
                template.setPlatformId(SecurityUtils.getSystemPlatformId());
                templateConfigService.saveTemplateBaseInfo(template);
                projectVisitConfigParam.setTemplateId(template.getId());
            }else{
                template = templateConfigService.getTemplateBaseInfoById(projectVisitConfigParam.getTemplateId());
            }
        }

        ProjectVisitConfig projectVisitConfig = new ProjectVisitConfig();
        if (projectVisitConfigParam.getId() == null) {
            BeanUtils.copyProperties(projectVisitConfigParam, projectVisitConfig);
            projectVisitConfig.setId(SnowflakeIdWorker.getUuid());
            projectVisitConfig.setStatus(BusinessConfig.VALID_STATUS);
            projectVisitConfig.setCreateTime(new Date());
            projectVisitConfig.setCreateUser(Long.parseLong(projectVisitConfigParam.getCreateUserId()));
            if(template.getId() != null){
                projectVisitConfig.setTemplateId(template.getId());
            }


            if(!projectVisitConfigParam.getCopyTemplate()){
                //设置当前访视的关联id
                ProjectVisitConfig preVisitConfigVo;
                if(projectVisitConfigParam.getTemplateId() != null){
                    preVisitConfigVo = getVisitConfigByTemplateId(projectVisitConfigParam.getTemplateId().toString(), projectVisitConfigParam.getPreSortValue().toString());
                }else{
                    preVisitConfigVo = getVisitConfigByVisitSort(projectVisitConfig.getProjectId().toString(), projectVisitConfigParam.getPreSortValue().toString());
                }
                if(preVisitConfigVo != null){
                    projectVisitConfig.setPreVisitId(preVisitConfigVo.getId());
                }

                ProjectVisitConfig nextVisitConfigVo;
                if(projectVisitConfigParam.getTemplateId() != null){
                    nextVisitConfigVo = getVisitConfigByTemplateId(projectVisitConfigParam.getTemplateId().toString(), projectVisitConfigParam.getPreSortValue().toString());
                }else{
                    nextVisitConfigVo = getVisitConfigByVisitSort(projectVisitConfig.getProjectId().toString(), projectVisitConfigParam.getNextSortValue().toString());
                }
                if(nextVisitConfigVo != null){
                    nextVisitConfigVo.setPreVisitId(projectVisitConfig.getId());
                    projectVisitConfigMapper.updateByPrimaryKey(nextVisitConfigVo);
                }
            }
            projectVisitConfigMapper.insert(projectVisitConfig);
        } else {
            projectVisitConfig = projectVisitConfigMapper.selectByPrimaryKey(projectVisitConfigParam.getId());
            if(projectVisitConfigParam.getProjectId() != null){
                //验证访视顺序是否变更
                if(projectVisitConfig.getSort() != projectVisitConfigParam.getSort()){
                    List<ProjectTesteeResult> projectTesteeVisitTimeResult = projectTesteeResultService.getProjectTesteeVisitTimeResult(projectVisitConfigParam.getProjectId().toString(), projectVisitConfigParam.getPreVisitId().toString());
                    if(projectVisitConfigParam.getForceUpdate() && projectTesteeVisitTimeResult != null && projectTesteeVisitTimeResult.size() >0){
                        customResult.setCode(ResultCode.BUSINESS_PROJECT_VISIT_SORT_MESSAGE_01.getCode());
                        customResult.setMessage(ResultCode.BUSINESS_PROJECT_VISIT_SORT_MESSAGE_01.getMessage());
                        return customResult;
                    }
                }
            }

            BeanUtils.copyProperties(projectVisitConfigParam, projectVisitConfig);
            projectVisitConfig.setUpdateTime(new Date());
            projectVisitConfig.setUpdateUser(Long.parseLong(projectVisitConfigParam.getCreateUserId()));
            //设置当前访视的关联id
            ProjectVisitConfig preVisitConfigVo;
            if(projectVisitConfigParam.getTemplateId() != null){
                preVisitConfigVo = getVisitConfigByTemplateId(projectVisitConfigParam.getTemplateId().toString(), projectVisitConfigParam.getPreSortValue().toString());
            }else{
                preVisitConfigVo = getVisitConfigByVisitSort(projectVisitConfig.getProjectId().toString(), projectVisitConfigParam.getPreSortValue().toString());
            }
            if(preVisitConfigVo != null){
                projectVisitConfig.setPreVisitId(preVisitConfigVo.getId());
            }
            if("1".equals(projectVisitConfigParam.getSort().toString())){
                projectVisitConfig.setPreVisitId(0L);
            }
            ProjectVisitConfig nextVisitConfigVo;
            if(projectVisitConfigParam.getTemplateId() != null){
                nextVisitConfigVo = getVisitConfigByTemplateId(projectVisitConfigParam.getTemplateId().toString(), projectVisitConfigParam.getPreSortValue().toString());
            }else{
                nextVisitConfigVo = getVisitConfigByVisitSort(projectVisitConfig.getProjectId().toString(), projectVisitConfigParam.getNextSortValue().toString());
            }
            if(nextVisitConfigVo != null){
                nextVisitConfigVo.setPreVisitId(projectVisitConfig.getId());
                projectVisitConfigMapper.updateByPrimaryKey(nextVisitConfigVo);
            }
            projectVisitConfigMapper.updateByPrimaryKey(projectVisitConfig);
        }
        Map<String,Object> dataMap = new HashMap<>();
        dataMap.put("templateId", template.getId() == null ? "" : template.getId().toString());
        dataMap.put("id", projectVisitConfig.getId().toString());
        dataMap.put("sortValue", projectVisitConfig.getSort());
        customResult.setData(dataMap);
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }

    private ProjectVisitConfig getVisitConfigByVisitSort(String projectId, String sort) {
        ProjectVisitConfigExample example = new ProjectVisitConfigExample();
        ProjectVisitConfigExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andSortEqualTo(Integer.parseInt(sort));
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        List<ProjectVisitConfig> projectVisitConfigList = projectVisitConfigMapper.selectByExample(example);
        if(projectVisitConfigList != null && projectVisitConfigList.size() >0){
            return projectVisitConfigList.get(0);
        }
        return null;
    }

    private ProjectVisitConfig getVisitConfigByTemplateId(String templateId, String sort) {
        ProjectVisitConfigExample example = new ProjectVisitConfigExample();
        ProjectVisitConfigExample.Criteria criteria = example.createCriteria();
        criteria.andTemplateIdEqualTo(Long.parseLong(templateId));
        criteria.andSortEqualTo(Integer.parseInt(sort));
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        List<ProjectVisitConfig> projectVisitConfigList = projectVisitConfigMapper.selectByExample(example);
        if(projectVisitConfigList != null && projectVisitConfigList.size() >0){
            return projectVisitConfigList.get(0);
        }
        return null;
    }


    public List<ProjectVisitConfig> getProjectVisitListByTemplateId(String templateId) {
        ProjectVisitConfigExample projectVisitConfigExample = new ProjectVisitConfigExample();
        ProjectVisitConfigExample.Criteria criteria = projectVisitConfigExample.createCriteria();
        criteria.andTemplateIdEqualTo(Long.parseLong(templateId));
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        projectVisitConfigExample.setOrderByClause("sort asc");
        List<ProjectVisitConfig> projectVisitConfigList = projectVisitConfigMapper.selectByExample(projectVisitConfigExample);
        return projectVisitConfigList;
    }

    @Override
    public List<ProjectVisitConfig> getProjectVisitListByPlanId(String projectId, String planId, String visitId) {
        ProjectVisitConfigExample example = new ProjectVisitConfigExample();
        ProjectVisitConfigExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        if(StringUtils.isBlank(planId)){
            FlowPlan flowPlan = flowPlanService.getPlanByProjectId(projectId);
            planId = flowPlan.getId().toString();
        }
        criteria.andPlanIdEqualTo(Long.parseLong(planId));
        if(StringUtils.isNotBlank(visitId)){criteria.andIdEqualTo((Long.parseLong(visitId)));}
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        example.setOrderByClause("sort asc");
        List<ProjectVisitConfig> projectVisitConfigList = projectVisitConfigMapper.selectByExample(example);
        return projectVisitConfigList;
    }

    @Override
    public ProjectVisitVo getProjectVisitBaseConfigByVisitId(String visitId) {
        // from cache
        ProjectVisitVo projectVisitVo = new ProjectVisitVo();
        Object variableCache = redisTemplateService.get(RedisKeyContants.PROJECT_TEMPLATE_VISIT_CONFIG.concat(visitId));
        if(variableCache != null){
            ProjectVisitConfig projectVisitConfig = JSON.parseObject(variableCache.toString(), ProjectVisitConfig.class);
            BeanUtils.copyProperties(projectVisitConfig, projectVisitVo);
            return projectVisitVo;
        }
        ProjectVisitConfig projectVisitConfig = projectVisitConfigMapper.selectByPrimaryKey(Long.parseLong(visitId));
        BeanUtils.copyProperties(projectVisitConfig, projectVisitVo);
        return projectVisitVo;
    }

    @Override
    public CustomResult deleteProjectVisitConfig(String visitId, String forceDelete, String operator) {
        CustomResult customResult = new CustomResult();
        ProjectVisitConfig projectVisitConfig = projectVisitConfigMapper.selectByPrimaryKey(Long.parseLong(visitId));
        if (projectVisitConfig == null) {
            customResult.setMessage("访视记录不存在");
            return customResult;
        }
        //查询访视表单信息 如果存在强制弹出提示信息
        List<TemplateFormConfigVo> templateFormConfigVoList = templateConfigService.getTemplateFormConfigListByPlanId("", "", "", null, null,null, false);
        if ("0".equals(forceDelete) && templateFormConfigVoList != null || templateFormConfigVoList.size() > 0) {
            customResult.setMessage("当前访视已存在表单数据，请慎重操作，一旦删除无法恢复");
            return customResult;
        }
        //删除
        if ("1".equals(forceDelete)) {
            projectVisitConfig.setStatus(BusinessConfig.NO_VALID_STATUS);
            projectVisitConfig.setUpdateTime(new Date());
            projectVisitConfig.setUpdateUser(Long.parseLong(operator));

            //设置上一次访视的id
            if(projectVisitConfig.getPreVisitId() != null){
                ProjectVisitConfig projectVisitConfigVo = getVisitConfigByPreVisitId(projectVisitConfig.getPreVisitId().toString());
                if(projectVisitConfigVo != null){
                    projectVisitConfig.setPreVisitId(projectVisitConfigVo.getId());
                    projectVisitConfigMapper.updateByPrimaryKey(projectVisitConfigVo);
                }
            }
            projectVisitConfigMapper.updateByPrimaryKeySelective(projectVisitConfig);
        }
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }

    public ProjectVisitConfig getVisitConfigByPreVisitId(String preVisitId) {
        ProjectVisitConfigExample example = new ProjectVisitConfigExample();
        ProjectVisitConfigExample.Criteria criteria = example.createCriteria();
        criteria.andPreVisitIdEqualTo(Long.parseLong(preVisitId));
        List<ProjectVisitConfig> projectVisitConfigs = projectVisitConfigMapper.selectByExample(example);
        if(projectVisitConfigs != null && projectVisitConfigs.size() >0){
            return projectVisitConfigs.get(0);
        }
        return null;
    }

    public ProjectVisitTesteeRecord getProjectVisitTesteeRecord(String projectId, String visitId, String testeeId){
        ProjectVisitTesteeRecordExample example = new ProjectVisitTesteeRecordExample();
        ProjectVisitTesteeRecordExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andVisitIdEqualTo(Long.parseLong(visitId));
        criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        List<ProjectVisitTesteeRecord> projectVisitTesteeRecords = projectVisitTesteeRecordMapper.selectByExample(example);
        if(CollectionUtil.isEmpty(projectVisitTesteeRecords)){
            return null;
        }
        return projectVisitTesteeRecords.get(0);
    }

    //入组条件设置
    @Override
    public void insertProjectVisitTesteeRecord(String projectId, String testeeId, String visitId) {
        ProjectVisitTesteeRecord record = new ProjectVisitTesteeRecord();
        record.setId(SnowflakeIdWorker.getUuid());
        record.setProjectId(Long.parseLong(projectId));
        record.setTesteeId(Long.parseLong(testeeId));
        record.setVisitId(Long.parseLong(visitId));
        //
        record.setCreateTime(new Date());
        projectVisitTesteeRecordMapper.insertSelective(record);

    }

    @Override
    public void insertProjectVisitTesteeRecordSelective(ProjectVisitTesteeRecord projectVisitTesteeRecordVo) {
        projectVisitTesteeRecordMapper.insertSelective(projectVisitTesteeRecordVo);
    }

    @Override
    public void updateProjectVisitTesteeRecord(ProjectVisitTesteeRecord projectVisitTesteeRecord) {
        projectVisitTesteeRecordMapper.updateByPrimaryKey(projectVisitTesteeRecord);
    }

    @Override
    public List<ProjectVisitTesteeRecord> getProjectVisitFollowRealTimeCount(String projectId, String orgIds) {
        List<ProjectVisitTesteeRecord> projectVisitTesteeRecords = projectVisitTesteeRecordMapper.getProjectVisitFollowRealTimeList(projectId, orgIds);
        return projectVisitTesteeRecords;
    }

    @Override
    public int getProjectPlannedVisitCount(String projectId, String orgIds) {
        List<ProjectVisitTesteeRecord> projectVisitTesteeRecords = projectVisitTesteeRecordMapper.getProjectPlannedVisitList(projectId, orgIds);
        return projectVisitTesteeRecords.size();
    }

    @Override
    public List<ProjectVisitTesteeRecord> getProjectOverdueVisitFollowRealTimeNotNullCount(String projectId, String orgIds, String nextFollowRealNotNullValue) {
        List<ProjectVisitTesteeRecord> projectVisitTesteeRecords = projectVisitTesteeRecordMapper.getProjectOverdueVisitFollowRealTimeNotNullCount(projectId, orgIds, nextFollowRealNotNullValue);
        return projectVisitTesteeRecords;
    }

    @Override
    public ProjectVisitConfig getProjectVisitConfigByVisitName(String projectId, String visitName) {
        ProjectVisitConfigExample example = new ProjectVisitConfigExample();
        ProjectVisitConfigExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andVisitNameEqualTo(visitName);
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        List<ProjectVisitConfig> projectVisitConfigList = projectVisitConfigMapper.selectByExample(example);
        if(projectVisitConfigList != null && projectVisitConfigList.size() >0){
            return projectVisitConfigList.get(0);
        }
        return null;
    }


    @Override
    public int getProjectOverdueVisitFollowRealTimeNullCount(String projectId, String orgIds, String nextFollowRealNullValue) {
        List<ProjectVisitTesteeRecord> projectVisitTesteeRecords = projectVisitTesteeRecordMapper.getProjectOverdueVisitFollowRealTimeNullCount(projectId, orgIds, nextFollowRealNullValue);
        return projectVisitTesteeRecords.size();
    }


    /**
     * 获取研究流程列表
     * @param planId
     * @return 研究流程列表
     */
    @Override
    public List<ProjectVisitConfig> list(Long planId) {
        ProjectVisitConfigExample example = new ProjectVisitConfigExample();
        example.setOrderByClause(" sort asc ");
        ProjectVisitConfigExample.Criteria criteria = example.createCriteria();
        criteria.andPlanIdEqualTo(planId);
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        return projectVisitConfigMapper.selectByExample(example);
    }

    /**
     * 根据项目id获取访视流程
     * @param projectId 项目id
     * @return  流程列表
     */
    @Override
    public List<ProjectVisitConfig> listByProjectId(Long projectId) {
        // 根据项目获取发布的计划
        FlowPlan flowPlan = flowPlanService.getPlanByProjectId(projectId+"");
        if (flowPlan!=null){
            ProjectVisitConfigExample example = new ProjectVisitConfigExample();
            example.setOrderByClause(" sort asc ");
            ProjectVisitConfigExample.Criteria criteria = example.createCriteria();
            criteria.andPlanIdEqualTo(flowPlan.getId());
            criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
            return projectVisitConfigMapper.selectByExample(example);
        }
        return new ArrayList<>();
    }
    
    @Override
    public List<ProjectVisitConfig> getProjectVisitConfigListByProjectId(String projectId) {
        if (StringUtils.isNotBlank(projectId)) {
            ProjectVisitConfigExample example = new ProjectVisitConfigExample();
            ProjectVisitConfigExample.Criteria criteria = example.createCriteria();
            criteria.andProjectIdEqualTo(Long.parseLong(projectId));
            criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
            example.setOrderByClause("sort asc");
            return projectVisitConfigMapper.selectByExample(example);
        }
        return Collections.emptyList();
    }
    
    /**
     * 根据访视名称查询访视id
     * @param flowParam 创建研究流程
     * @return 创建成功的条数
     */
    @Override
    public CommonResult<Object> create(FlowParam flowParam) {
        ProjectVisitConfig config = new ProjectVisitConfig();
        BeanUtils.copyProperties(flowParam,config);
        //校验名称是否已重复
        List<ProjectVisitConfig> list = getByPlanIdOrName(flowParam.getPlanId(), flowParam.getVisitName());
        if (CollectionUtil.isNotEmpty(list)){
            return CommonResult.failed(ProjectFlowReturnEnums.E60004);
        }
        // 获取最大的排序号
        Integer sort = projectVisitConfigMapper.getMaxSort(flowParam.getPlanId());
        if (sort==null){
            sort=0;
        }
        config.setId(SnowflakeIdWorker.getUuid());
        config.setStatus(BusinessConfig.VALID_STATUS);
        config.setCreateUser(Long.parseLong(SecurityUtils.getUserIdValue()));
        config.setCreateTime(new Date());
        config.setTenantId(SecurityUtils.getSystemTenantId());
        config.setPlatformId(SecurityUtils.getSystemPlatformId());
        config.setSort(sort+1);
        int insert = projectVisitConfigMapper.insert(config);
        redisTemplateService.set(RedisKeyContants.PROJECT_TEMPLATE_VISIT_CONFIG.concat(config.getId().toString()), JSON.toJSONString(config));
        flowParam.setId(config.getId());
        return CommonResult.success(insert);
    }

    /**
     * 根据访视名称查询访视id
     * @param param 创建研究流程
     * @return 创建成功的条数
     */
    @Override
    public CommonResult<Object> update(FlowParam param) {
        //校验名称是否已重复
        List<ProjectVisitConfig> list = getByPlanIdOrName(param.getPlanId(), param.getVisitName());
        if (CollectionUtil.isNotEmpty(list)){
            ProjectVisitConfig config = list.get(0);
            if (!Objects.equals(config.getId(), param.getId())){
                return CommonResult.failed();
            }
        }
        ProjectVisitConfig config = projectVisitConfigMapper.selectByPrimaryKey(param.getId());
        config.setVisitName(param.getVisitName());
        config.setVisitType(param.getVisitType());
        config.setOwnerPeroid(param.getOwnerPeroid());
        config.setUpdateUser(Long.parseLong(SecurityUtils.getUserIdValue()));
        config.setUpdateTime(new Date());
        int insert = projectVisitConfigMapper.updateByPrimaryKey(config);
        redisTemplateService.set(RedisKeyContants.PROJECT_TEMPLATE_VISIT_CONFIG.concat(config.getId().toString()), JSON.toJSONString(config));
        return CommonResult.success(insert);
    }

    @Override
    public CommonResult<Object> delete(Long id) {
        ProjectVisitConfig config = projectVisitConfigMapper.selectByPrimaryKey(id);
        if (config!=null){
            // 查询是否存在已经绑定的信息。
            FlowFormSetExample example = new FlowFormSetExample();
            FlowFormSetExample.Criteria criteria = example.createCriteria();
            criteria.andVisitIdEqualTo(id);
            List<FlowFormSet> list = flowFormSetMapper.selectByExample(example);
            if (CollectionUtil.isNotEmpty(list)){
                return CommonResult.failed(ProjectFlowReturnEnums.E60003);
            }
            int delete = projectVisitConfigMapper.deleteByPrimaryKey(id);
            if (delete>0){
                return CommonResult.success(null);
            }
        }
        return CommonResult.failed();
    }

    public List<ProjectVisitConfig> getByPlanIdOrName(Long planId,String visitName){
        ProjectVisitConfigExample example = new ProjectVisitConfigExample();
        ProjectVisitConfigExample.Criteria criteria = example.createCriteria();
        criteria.andPlanIdEqualTo(planId);
        if (StringUtils.isNotBlank(visitName)){
            criteria.andVisitNameEqualTo(visitName);
        }
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        return projectVisitConfigMapper.selectByExample(example);
    }

    @Override
    public List<ProjectFlowVo> getProjectVisitConfigByProjectId(String projectId,Boolean isH5) {
        // 根据项目获取发布的计划
        FlowPlan flowPlan = flowPlanService.getPlanByProjectId(projectId);
        List<ProjectFlowVo> data = new ArrayList<>();
        if (flowPlan!=null){
            // 根据项目id和计划id获取访视信息
            List<ProjectVisitConfig> list = list(flowPlan.getId());
            for (ProjectVisitConfig config:list){
                ProjectFlowVo vo = new ProjectFlowVo();
                vo.setId(config.getId());
                vo.setVisitName(config.getVisitName());
                vo.setOwnerPeroid(config.getOwnerPeroid());
                vo.setVisitType(config.getVisitType());
                vo.setPlanId(flowPlan.getId());
                // 获取绑定的表单信息
                FlowFormSet formSet = new FlowFormSet();
                formSet.setVisitId(config.getId());
                if (isH5){
                    formSet.setMoRoAuth(true);
                    formSet.setMoRwAuth(true);
                }else {
                    formSet.setPcRoAuth(true);
                    formSet.setPcRwAuth(true);
                }
                List<ProjectFlowFormVo> projectFlowFormVos = flowFormSetMapper.listByFlowIdAndFilterPer(formSet);
                vo.setFormList(projectFlowFormVos);
                data.add(vo);
            }
        }
        return data;
    }

    @Override
    public CommonResult<Object> move(List<Long> ids) {
        if (CollectionUtil.isNotEmpty(ids)){
            for (int i = 0; i < ids.size(); i++) {
                ProjectVisitConfig config = projectVisitConfigMapper.selectByPrimaryKey(ids.get(i));
                if (config!=null){
                    config.setSort(i);
                    projectVisitConfigMapper.updateByPrimaryKey(config);
                }
            }
        }
        return CommonResult.success(null);
    }


}
