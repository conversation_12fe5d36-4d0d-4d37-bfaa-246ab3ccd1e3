package com.haoys.user.mapper;

import com.haoys.user.model.TemplateFormGroupTable;
import com.haoys.user.model.TemplateFormGroupTableExample;
import java.util.List;

import com.haoys.user.model.TemplateFormGroupVariable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TemplateFormGroupTableMapper {
    long countByExample(TemplateFormGroupTableExample example);

    int deleteByExample(TemplateFormGroupTableExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TemplateFormGroupTable record);

    int insertSelective(TemplateFormGroupTable record);

    List<TemplateFormGroupTable> selectByExample(TemplateFormGroupTableExample example);

    TemplateFormGroupTable selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TemplateFormGroupTable record, @Param("example") TemplateFormGroupTableExample example);

    int updateByExample(@Param("record") TemplateFormGroupTable record, @Param("example") TemplateFormGroupTableExample example);

    int updateByPrimaryKeySelective(TemplateFormGroupTable record);

    int updateByPrimaryKey(TemplateFormGroupTable record);

    int deleteByFormDetailId(Long formDetailId);

    List<TemplateFormGroupTable> getListByFormGroupVariable(TemplateFormGroupVariable variable);

    int deleteById(Long id);

    int deleteTemplateFormGroupTableByGroupId(@Param("groupId")String groupId, @Param("validStatus") String validStatus,@Param("userId") String userId);

}
