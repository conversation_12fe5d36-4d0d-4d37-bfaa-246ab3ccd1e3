package com.haoys.user.domain.param.project;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class ProjectAnnouncementParam {
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "公告内容")
    private String content;

    @ApiModelProperty(value = "项目研究中心id")
    private Long projectOrgId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "删除附件ids")
    private String delFileIds;

    private List<ProjectAnnouncementParam.FileData> fileData = new ArrayList<>();


    @Data
    public static class FileData implements Serializable {

        @ApiModelProperty(value = "新文件名称")
        private String newFileName;
        @ApiModelProperty(value = "原文件名称")
        private String originalFilename;
        @ApiModelProperty(value = "文件key")
        private String fileNameKey;
        @ApiModelProperty(value = "文件路径")
        private String url;
        @ApiModelProperty(value = "本地路径")
        private String fileName;

    }
}
