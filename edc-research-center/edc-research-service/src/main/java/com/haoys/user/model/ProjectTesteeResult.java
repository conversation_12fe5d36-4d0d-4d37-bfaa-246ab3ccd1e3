package com.haoys.user.model;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ProjectTesteeResult implements Serializable {
    private Long id;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "方案id")
    private Long planId;

    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @ApiModelProperty(value = "表单id")
    private Long formId;

    @ApiModelProperty(value = "流程扩展表单id")
    private Long formExpandId;

    @ApiModelProperty(value = "表单变量id")
    private Long formDetailId;

    @ApiModelProperty(value = "受试者id")
    private Long testeeId;

    @ApiModelProperty(value = "字段组分组id")
    private Long groupId;

    @ApiModelProperty(value = "字段组模版变量id")
    private Long resourceVariableId;

    @ApiModelProperty(value = "字段映射元数据id")
    private Long baseVariableId;

    @ApiModelProperty(value = "字段名称")
    private String label;

    @ApiModelProperty(value = "字段code")
    private String fieldName;

    @ApiModelProperty(value = "字段值")
    private String fieldValue;

    @ApiModelProperty(value = "字段文本值")
    private String fieldText;

    @ApiModelProperty(value = "计量单位")
    private String unitValue;

    @ApiModelProperty(value = "单位文本值")
    private String unitText;

    @ApiModelProperty(value = "量表打分")
    private BigDecimal scoreValue;

    @ApiModelProperty(value = "选项联动隐藏标识0/1")
    private Boolean optionHidden;

    @ApiModelProperty(value = "表格使用 1-未录入 2-录入中 3-完成")
    private String complateStatus;

    @ApiModelProperty(value = "参与者录入标识")
    private Boolean testeeInput;

    @ApiModelProperty(value = "数据来源")
    private String dataFrom;

    @ApiModelProperty(value = "数据状态")
    private String status;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public Long getVisitId() {
        return visitId;
    }

    public void setVisitId(Long visitId) {
        this.visitId = visitId;
    }

    public Long getFormId() {
        return formId;
    }

    public void setFormId(Long formId) {
        this.formId = formId;
    }

    public Long getFormExpandId() {
        return formExpandId;
    }

    public void setFormExpandId(Long formExpandId) {
        this.formExpandId = formExpandId;
    }

    public Long getFormDetailId() {
        return formDetailId;
    }

    public void setFormDetailId(Long formDetailId) {
        this.formDetailId = formDetailId;
    }

    public Long getTesteeId() {
        return testeeId;
    }

    public void setTesteeId(Long testeeId) {
        this.testeeId = testeeId;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Long getResourceVariableId() {
        return resourceVariableId;
    }

    public void setResourceVariableId(Long resourceVariableId) {
        this.resourceVariableId = resourceVariableId;
    }

    public Long getBaseVariableId() {
        return baseVariableId;
    }

    public void setBaseVariableId(Long baseVariableId) {
        this.baseVariableId = baseVariableId;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getFieldValue() {
        return fieldValue;
    }

    public void setFieldValue(String fieldValue) {
        this.fieldValue = fieldValue;
    }

    public String getFieldText() {
        return fieldText;
    }

    public void setFieldText(String fieldText) {
        this.fieldText = fieldText;
    }

    public String getUnitValue() {
        return unitValue;
    }

    public void setUnitValue(String unitValue) {
        this.unitValue = unitValue;
    }

    public String getUnitText() {
        return unitText;
    }

    public void setUnitText(String unitText) {
        this.unitText = unitText;
    }

    public BigDecimal getScoreValue() {
        return scoreValue;
    }

    public void setScoreValue(BigDecimal scoreValue) {
        this.scoreValue = scoreValue;
    }

    public Boolean getOptionHidden() {
        return optionHidden;
    }

    public void setOptionHidden(Boolean optionHidden) {
        this.optionHidden = optionHidden;
    }

    public String getComplateStatus() {
        return complateStatus;
    }

    public void setComplateStatus(String complateStatus) {
        this.complateStatus = complateStatus;
    }

    public Boolean getTesteeInput() {
        return testeeInput;
    }

    public void setTesteeInput(Boolean testeeInput) {
        this.testeeInput = testeeInput;
    }

    public String getDataFrom() {
        return dataFrom;
    }

    public void setDataFrom(String dataFrom) {
        this.dataFrom = dataFrom;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", projectId=").append(projectId);
        sb.append(", planId=").append(planId);
        sb.append(", visitId=").append(visitId);
        sb.append(", formId=").append(formId);
        sb.append(", formExpandId=").append(formExpandId);
        sb.append(", formDetailId=").append(formDetailId);
        sb.append(", testeeId=").append(testeeId);
        sb.append(", groupId=").append(groupId);
        sb.append(", resourceVariableId=").append(resourceVariableId);
        sb.append(", baseVariableId=").append(baseVariableId);
        sb.append(", label=").append(label);
        sb.append(", fieldName=").append(fieldName);
        sb.append(", fieldValue=").append(fieldValue);
        sb.append(", fieldText=").append(fieldText);
        sb.append(", unitValue=").append(unitValue);
        sb.append(", unitText=").append(unitText);
        sb.append(", scoreValue=").append(scoreValue);
        sb.append(", optionHidden=").append(optionHidden);
        sb.append(", complateStatus=").append(complateStatus);
        sb.append(", testeeInput=").append(testeeInput);
        sb.append(", dataFrom=").append(dataFrom);
        sb.append(", status=").append(status);
        sb.append(", sort=").append(sort);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", tenantId=").append(tenantId);
        sb.append(", platformId=").append(platformId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}