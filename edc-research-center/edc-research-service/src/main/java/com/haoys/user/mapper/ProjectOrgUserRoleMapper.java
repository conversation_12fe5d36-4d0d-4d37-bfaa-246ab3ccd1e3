package com.haoys.user.mapper;

import com.haoys.user.domain.dto.ProjectUserOrgRoleVo;
import com.haoys.user.domain.vo.auth.ProjectOrgUserRoleVo;
import com.haoys.user.model.ProjectOrgUserRole;
import com.haoys.user.model.ProjectOrgUserRoleExample;
import java.util.List;

import com.haoys.user.model.SystemUserInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProjectOrgUserRoleMapper {
    long countByExample(ProjectOrgUserRoleExample example);

    int deleteByExample(ProjectOrgUserRoleExample example);

    int deleteByPrimaryKey(@Param("roleId") Long roleId, @Param("userId") Long userId);

    int insert(ProjectOrgUserRole record);

    int insertSelective(ProjectOrgUserRole record);

    List<ProjectOrgUserRole> selectByExample(ProjectOrgUserRoleExample example);

    ProjectOrgUserRole selectByPrimaryKey(@Param("roleId") Long roleId, @Param("userId") Long userId);

    int updateByExampleSelective(@Param("record") ProjectOrgUserRole record, @Param("example") ProjectOrgUserRoleExample example);

    int updateByExample(@Param("record") ProjectOrgUserRole record, @Param("example") ProjectOrgUserRoleExample example);

    int updateByPrimaryKeySelective(ProjectOrgUserRole record);

    int updateByPrimaryKey(ProjectOrgUserRole record);

    int countByRoleId(Long roleId);

    List<SystemUserInfo> selectUseRoleUser(long parseLong);

    /**
     * //删除项目研究中心用户关联角色
     * @param projectId
     * @param userId
     */
    void deleteProjectOrgUserRoleByUserId(String projectId, String userId);
    /**
     * 根据项目id和用户id获取
     * @param projectId
     * @param userId
     * @return
     */
    List<ProjectUserOrgRoleVo> getProjectOrgUserRoleListByProjectIdAndUserId(@Param("projectId") String projectId, @Param("userId") String userId);

    /**
     * 查询用户项目研究中心角色关联
     * @param projectId
     * @param userId
     * @param projectOrgRoleId
     * @return
     */
    ProjectOrgUserRole getProjectOrgUserRoleByUserIdAndRoleId(String projectId, String userId, String projectOrgRoleId);

    /**
     * 查询授权全部研究中心的项目用户
     * @param projectId
     * @return
     */
    List<ProjectOrgUserRoleVo> getProjectUserOrgRoleByOwnerTotalAuth(Long projectId);
}
