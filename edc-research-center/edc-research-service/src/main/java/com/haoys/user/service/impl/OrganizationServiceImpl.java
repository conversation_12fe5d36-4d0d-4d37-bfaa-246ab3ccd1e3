package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.bussiness.RedisKeyContants;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.excel.ExcelUtil;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.dto.ProjectOrgParam;
import com.haoys.user.domain.dto.ProjectUserOrgRoleVo;
import com.haoys.user.domain.entity.ProjectRoleQuery;
import com.haoys.user.domain.param.project.ProjectUserQueryParam;
import com.haoys.user.domain.vo.auth.ProjectMenuVo;
import com.haoys.user.domain.vo.auth.ProjectOrgUserRoleVo;
import com.haoys.user.domain.vo.overview.ProjectComprehensiveVo;
import com.haoys.user.domain.vo.project.ProjectOrgExcelVo;
import com.haoys.user.domain.vo.project.ProjectOrgExportVo;
import com.haoys.user.domain.vo.project.ProjectOrgIdentCodeVo;
import com.haoys.user.domain.vo.project.ProjectOrgVo;
import com.haoys.user.domain.vo.project.ProjectUserVo;
import com.haoys.user.domain.vo.project.ProjectVo;
import com.haoys.user.domain.vo.system.OrganizationVo;
import com.haoys.user.domain.vo.system.SystemOrgInfoVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeVo;
import com.haoys.user.domain.wrapper.ProjectUserInfoWrapper;
import com.haoys.user.enums.ProjectRoleEnum;
import com.haoys.user.exception.ServiceException;
import com.haoys.user.mapper.ProjectOrgInfoMapper;
import com.haoys.user.mapper.ProjectUserOrgMapper;
import com.haoys.user.mapper.SystemUserOrgMapper;
import com.haoys.user.model.Organization;
import com.haoys.user.model.Project;
import com.haoys.user.model.ProjectOrgInfo;
import com.haoys.user.model.ProjectOrgInfoExample;
import com.haoys.user.model.ProjectOrgRole;
import com.haoys.user.model.ProjectRole;
import com.haoys.user.model.ProjectUserOrg;
import com.haoys.user.model.ProjectUserOrgExample;
import com.haoys.user.model.SystemOrgInfo;
import com.haoys.user.model.SystemUserInfo;
import com.haoys.user.model.SystemUserOrg;
import com.haoys.user.service.OrganizationService;
import com.haoys.user.service.ProjectBaseManageService;
import com.haoys.user.service.ProjectOrgRoleService;
import com.haoys.user.service.ProjectRoleService;
import com.haoys.user.service.ProjectTesteeChallengeService;
import com.haoys.user.service.ProjectTesteeInfoService;
import com.haoys.user.service.ProjectUserService;
import com.haoys.user.service.ProjectVisitConfigService;
import com.haoys.user.service.SystemAreaService;
import com.haoys.user.service.SystemOrgInfoService;
import com.haoys.user.service.SystemUserInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class OrganizationServiceImpl extends BaseService implements OrganizationService {

    private final ProjectOrgInfoMapper projectOrgInfoMapper;
    private final ProjectUserOrgMapper projectUserOrgMapper;
    private final SystemUserOrgMapper systemUserOrgMapper;
    private final SystemUserInfoService systemUserInfoService;
    private final SystemOrgInfoService systemOrgInfoService;
    private final SystemAreaService systemAreaService;
    private final ProjectBaseManageService projectBaseManageService;
    private final ProjectTesteeInfoService projectTesteeInfoService;
    private final ProjectUserService projectUserService;
    private final ProjectTesteeChallengeService projectTesteeChallengeService;
    private final ProjectVisitConfigService projectVisitConfigService;
    private final ProjectOrgRoleService projectOrgRoleService;
    private final ProjectRoleService projectRoleService;
    private final RedisTemplateService redisTemplateService;

    @Override
    public CustomResult saveProjectOrgInfo(ProjectOrgParam projectOrgParam){
        CustomResult customResult = new CustomResult();
        ProjectOrgInfo projectOrg = projectOrgInfoMapper.getProjectOrgListByProjectIdAndOrgId(projectOrgParam.getProjectId().toString(), projectOrgParam.getOrgId().toString(), projectOrgParam.getCode());
        if(projectOrg != null && !projectOrg.getId().equals(projectOrgParam.getId())){
            customResult.setCode(ResultCode.PROJECT_ORG_NAME_EXIST.getCode());
            customResult.setMessage(ResultCode.PROJECT_ORG_NAME_EXIST.getMessage());
            return customResult;
        }
        ProjectOrgInfo projectOrgCodeInfo = projectOrgInfoMapper.getProjectOrgInfoByProjectIdAndCode(projectOrgParam.getProjectId().toString(), projectOrgParam.getCode());
        if(projectOrgCodeInfo != null && !projectOrgCodeInfo.getId().equals(projectOrgParam.getId())){
            customResult.setCode(ResultCode.PROJECT_ORG_CODE_EXIST.getCode());
            customResult.setMessage(ResultCode.PROJECT_ORG_CODE_EXIST.getMessage());
            return customResult;
        }

        if(projectOrgParam.getId() == null){
            ProjectOrgInfo projectOrgInfo = new ProjectOrgInfo();
            BeanUtils.copyProperties(projectOrgParam, projectOrgInfo);
            projectOrgInfo.setId(SnowflakeIdWorker.getUuid());
            projectOrgInfo.setCreateTime(new Date());
            projectOrgInfo.setCreateUserId(projectOrgParam.getCreateUserId());
            projectOrgInfo.setTenantId(SecurityUtils.getSystemTenantId());
            projectOrgInfo.setPlatformId(SecurityUtils.getSystemPlatformId());
            // 创建一个唯一识别码
            String identCode = getIdentCode();
            projectOrgInfo.setIdentCode(identCode);

            projectOrgInfoMapper.insert(projectOrgInfo);

            if(projectOrgParam.getUpdateSystemOrgAreaInfo()){
                SystemOrgInfo systemOrgInfo = systemOrgInfoService.selectSystemOrgInfoByOrgId(projectOrgParam.getOrgId().toString().toString());
                if(systemOrgInfo != null){
                    systemOrgInfo.setProvinceCode(projectOrgParam.getProvinceCode());
                    systemOrgInfo.setCityCode(projectOrgParam.getCityCode());
                    systemOrgInfo.setCountyCode(projectOrgParam.getCountyCode());
                    systemOrgInfoService.updateSystemOrgAreaInfoByOrgId(systemOrgInfo);
                }
            }

            initProjectOrgRole(projectOrgParam, projectOrgInfo.getId());

            //AsyncTaskManager.ownerTask().execute(AsyncTaskFactory.initProjectOrgRole(projectOrgParam, projectOrgInfo.getId(), MessageUtils.message("user.not.exists"), Constants.SYSTEM_LOG_LOGIN));

            // 新添加研究中心需要查询对PA角色中心进行同步
            List<ProjectOrgUserRoleVo> projectOrgUserRoleList = projectOrgRoleService.getProjectUserOrgRoleByOwnerTotalAuth(projectOrgParam.getProjectId());
            for (ProjectOrgUserRoleVo projectOrgUserRole : projectOrgUserRoleList) {
                // 如果研究中心角色存在则忽略
                String roleId = "";
                ProjectOrgRole projectOrgRoleBaseInfo = projectOrgRoleService.getProjectOrgRoleBaseInfo(projectOrgUserRole.getRoleId());
                if(projectOrgRoleBaseInfo == null){
                    ProjectRoleQuery projectRoleQuery = new ProjectRoleQuery();
                    projectRoleQuery.setProjectId(projectOrgParam.getProjectId());
                    projectRoleQuery.setId(SnowflakeIdWorker.getUuid());
                    projectRoleQuery.setOrgId(projectOrgParam.getOrgId());
                    projectRoleQuery.setProjectOrgId(projectOrgInfo.getId());
                    projectRoleQuery.setProjectOrgCode(projectOrgParam.getCode());
                    projectRoleQuery.setResourceRoleId(projectOrgUserRole.getResourceRoleId());
                    List<String> menuIds = new ArrayList<>();
                    List<ProjectMenuVo> projectMenuList = projectRoleService.selectProjectTemplateMenuInfo(Constants.SYSTEM_PROJECT_MENN_TEMPLATE_ID, projectOrgUserRole.getEname());
                    for (ProjectMenuVo item : projectMenuList) {
                        menuIds.add(item.getMenuId().toString());
                    }
                    projectRoleQuery.setMenuIds(menuIds);

                    projectRoleQuery.setName(projectOrgUserRole.getRoleName());
                    projectRoleQuery.setEnname(projectOrgUserRole.getEname());
                    CommonResult commonResult = projectOrgRoleService.insertProjectOrgRole(projectRoleQuery);
                    if(ResultCode.SUCCESS.getCode() == commonResult.getCode()){
                        roleId = commonResult.getData().toString();
                    }
                }else{
                    roleId = projectOrgRoleBaseInfo.getId().toString();
                }
                //保存项目成员记录(研究中心和研究中心角色)
                this.saveProjectUserOrgInfo(projectOrgParam.getProjectId().toString(), projectOrgUserRole.getUserId().toString(), projectOrgInfo.getOrgId().toString(), projectOrgParam.getCode());
                projectOrgRoleService.saveProjectUserOrgRole(projectOrgParam.getProjectId().toString(), projectOrgUserRole.getUserId().toString(), roleId, null);
            }
        }else{
            ProjectOrgInfo projectOrgInfo = projectOrgInfoMapper.selectByPrimaryKey(projectOrgParam.getId());
            if(projectOrgInfo != null){
                BeanUtils.copyProperties(projectOrgParam, projectOrgInfo);
                projectOrgInfo.setUpdateUserId(projectOrgParam.getCreateUserId());
                projectOrgInfo.setUpdateTime(new Date());
                projectOrgInfoMapper.updateByPrimaryKeySelective(projectOrgInfo);
                // 如果Code变更 需要同步项目研究中心code
                List<ProjectOrgRole> projectOrgRoleList = projectOrgRoleService.getProjectOrgRoleByProjectOrgCode(projectOrgParam.getProjectId(), projectOrgParam.getOrgId(), projectOrgParam.getCode());
                for (ProjectOrgRole projectOrgRole : projectOrgRoleList) {
                    ProjectRoleQuery projectOrgRoleQuery = new ProjectRoleQuery();
                    BeanUtils.copyProperties(projectOrgRole, projectOrgRoleQuery);
                    projectOrgRoleQuery.setProjectOrgCode(projectOrgParam.getCode());
                    projectOrgRoleService.updateProjectOrgRole(projectOrgRoleQuery);
                }
            }
        }
        return customResult;
    }


    public void initProjectOrgRole(ProjectOrgParam projectOrgParam, Long projectOrgId){
        List<String> projectRoleCodeArray = Arrays.asList(ProjectRoleEnum.PROJECT_PM.getCode(), ProjectRoleEnum.PROJECT_PI.getCode(),
                ProjectRoleEnum.PROJECT_SUB_PI.getCode(),ProjectRoleEnum.PROJECT_CRA.getCode(),ProjectRoleEnum.PROJECT_CRC.getCode(),
                ProjectRoleEnum.PROJECT_PH.getCode(),ProjectRoleEnum.PROJECT_QA.getCode(),ProjectRoleEnum.PROJECT_MEDICAL.getCode(),
                ProjectRoleEnum.PROJECT_DM.getCode(),ProjectRoleEnum.PROJECT_PA.getCode());

        projectRoleCodeArray.forEach(projectRoleCode ->{
            // 查询角色模版状态
            ProjectRole projectTemplateRole = projectRoleService.getProjectTemplateRoleInfoByRoleCode(projectRoleCode);
            if(projectTemplateRole.getStatus() == 1){
                return;
            }

            ProjectRoleQuery projectRoleQuery = new ProjectRoleQuery();
            projectRoleQuery.setProjectId(projectOrgParam.getProjectId());
            projectRoleQuery.setOrgId(projectOrgParam.getOrgId());
            projectRoleQuery.setProjectOrgId(projectOrgId);
            projectRoleQuery.setProjectOrgCode(projectOrgParam.getCode());
            projectRoleQuery.setResourceRoleId(projectTemplateRole.getId());
            List<String> menuIds = new ArrayList<>();
            List<ProjectMenuVo> projectMenuList = projectRoleService.selectProjectTemplateMenuInfo(Constants.SYSTEM_PROJECT_MENN_TEMPLATE_ID, projectRoleCode);
            for (ProjectMenuVo item : projectMenuList) {
                menuIds.add(item.getMenuId().toString());
            }
            projectRoleQuery.setMenuIds(menuIds);

            projectRoleQuery.setName(projectTemplateRole.getName());
            projectRoleQuery.setEnname(projectTemplateRole.getEnname());
            CommonResult commonResult = projectOrgRoleService.insertProjectOrgRole(projectRoleQuery);
            if(ResultCode.SUCCESS.getCode() != commonResult.getCode()){
                log.error("initProjectOrgRole init error {}", projectTemplateRole.getName());
            }
        });
    }
    
    @Override
    public ProjectOrgInfo getProjectOrganizationInfo(String projectId) {
        return projectOrgInfoMapper.getProjectOrganizationInfo(projectId);
    }
    
    
    private String getIdentCode(){
        // 随机生成6位随机数
        String randomNumeric = RandomStringUtils.randomNumeric(6);
        // 校验该随机数是否已经存在
        ProjectOrgInfoExample example = new ProjectOrgInfoExample();
        ProjectOrgInfoExample.Criteria criteria = example.createCriteria();
        criteria.andIdentCodeEqualTo(randomNumeric);
        long count = projectOrgInfoMapper.countByExample(example);
        if (count>0){
           return getIdentCode();
        }else {
            return randomNumeric;
        }
    }

    public static void main(String[] args) {
        Random random = new Random();
        int randomNumber = random.nextInt(999999) + 100000;
        log.info("randomNumber: {}", randomNumber);
    }

    @Override
    public CustomResult updateProjectOrgInfo(String projectId, String orgId, String code, String officer, String expands, String userId) {
        CustomResult customResult = new CustomResult();
        ProjectOrgInfo projectOrg = projectOrgInfoMapper.getProjectOrgListByProjectIdAndOrgId(projectId, orgId, "");
        if(projectOrg == null){
            customResult.setCode(ResultCode.RETURN_MESSAGE_RECORD_NOT_FOUND.getCode());
            customResult.setMessage(ResultCode.RETURN_MESSAGE_RECORD_NOT_FOUND.getMessage());
            return customResult;
        }
        /*List<ProjectOrgInfo> projectOrgList = projectOrgInfoMapper.getProjectOrgListByProjectIdAndCodeResult(projectId, code);
        if(CollectionUtil.isNotEmpty(projectOrgList)){
            String projectOrgId = projectOrgList.get(0).getOrgId().toString();
            if(projectOrgId.equals(orgId)){
                customResult.setCode(ResultCode.PROJECT_ORG_CODE_EXIST.getCode());
                customResult.setMessage(ResultCode.PROJECT_ORG_CODE_EXIST.getMessage());
                return customResult;
            }
        }*/
        projectOrg.setCode(code);
        projectOrg.setOfficer(officer);
        projectOrg.setExpands(expands);
        ProjectOrgInfoExample example = new ProjectOrgInfoExample();
        ProjectOrgInfoExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andOrgIdEqualTo(Long.parseLong(orgId));
        projectOrgInfoMapper.updateByExampleSelective(projectOrg, example);
        return customResult;
    }

    @Override
    public ProjectUserOrg getProjectUserOrgInfo(String projectId, String userId, String orgId) {
        ProjectUserOrgExample example = new ProjectUserOrgExample();
        ProjectUserOrgExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andUserIdEqualTo(Long.parseLong(userId));
        if(StringUtils.isNotEmpty(orgId)){
            criteria.andOrgIdEqualTo(Long.parseLong(orgId));
        }
        List<ProjectUserOrg> projectUserOrgList = projectUserOrgMapper.selectByExample(example);
        if(projectUserOrgList != null && projectUserOrgList.size() >0){
            return projectUserOrgList.get(0);
        }
        return null;
    }

    @Override
    public void deleteProjectUserOrgInfo(String projectId, String userId, String orgId) {
        ProjectUserOrgExample example = new ProjectUserOrgExample();
        ProjectUserOrgExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andUserIdEqualTo(Long.parseLong(userId));
        if(StringUtils.isNotEmpty(orgId)){
            criteria.andOrgIdEqualTo(Long.parseLong(orgId));
        }
        List<ProjectUserOrg> projectUserOrgList = projectUserOrgMapper.selectByExample(example);
        for (ProjectUserOrg projectUserOrg : projectUserOrgList) {
            projectUserOrgMapper.deleteByExample(example);
        }
    }

    @Override
    public String saveProjectUserOrgInfo(String projectId, String userId, String systemOrgId, String projectOrgCode) {
        ProjectUserOrgExample example = new ProjectUserOrgExample();
        ProjectUserOrgExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andUserIdEqualTo(Long.parseLong(userId));
        criteria.andOrgIdEqualTo(Long.parseLong(systemOrgId));
        if (StringUtils.isNotEmpty(projectOrgCode)){
            criteria.andOrgCodeEqualTo(projectOrgCode);
        }
        List<ProjectUserOrg> projectUserOrgList = projectUserOrgMapper.selectByExample(example);
        if(CollectionUtil.isEmpty(projectUserOrgList)){
            ProjectUserOrg projectUserOrg = new ProjectUserOrg();
            projectUserOrg.setProjectId(Long.parseLong(projectId));
            projectUserOrg.setUserId(Long.parseLong(userId));
            projectUserOrg.setOrgId(Long.parseLong(systemOrgId));
            projectUserOrg.setOrgCode(projectOrgCode);
            projectUserOrg.setCreateTime(new Date());
            projectUserOrg.setCreateUserId(SecurityUtils.getUserIdValue());
            projectUserOrg.setTenantId(SecurityUtils.getSystemTenantId());
            projectUserOrg.setPlatformId(SecurityUtils.getSystemPlatformId());
            projectUserOrgMapper.insertSelective(projectUserOrg);
        }
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    @Override
    public String saveSystemUserOrgInfo(String userId, String orgId) {
        deleteSystemUserOrgInfo(userId);
        SystemUserOrg systemUserOrg = new SystemUserOrg();
        systemUserOrg.setId(SnowflakeIdWorker.getUuid());
        systemUserOrg.setUserId(Long.parseLong(userId));
        systemUserOrg.setOrgId(Long.parseLong(orgId));
        systemUserOrg.setCreateTime(new Date());
        systemUserOrg.setTenantId(SecurityUtils.getSystemTenantId());
        systemUserOrg.setPlatformId(SecurityUtils.getSystemPlatformId());
        systemUserOrgMapper.insertSelective(systemUserOrg);
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    @Override
    public String saveBatchProjectOrg(MultipartFile file, String projectId, List<ProjectOrgExportVo> errorList, String createUserId) throws Exception {
        Set<String> orgList = new HashSet<>();
        int successNum = 0;
        int failureNum = 0;
        StringBuilder msg = new StringBuilder();
        ExcelUtil<ProjectOrgExcelVo> excelUtil = new ExcelUtil<>(ProjectOrgExcelVo.class);
        List<ProjectOrgExcelVo> projectOrgList = excelUtil.importExcel(file.getInputStream());
        for (ProjectOrgExcelVo projectOrgExcelVo : projectOrgList) {
            ProjectOrgExportVo projectOrgExportVo = new ProjectOrgExportVo();
            if(projectOrgExcelVo == null){
                failureNum++;
                projectOrgExportVo.setErrorMessage(BusinessConfig.RETURN_ORG_TEMPLATE_NOT_FOUND);
                errorList.add(projectOrgExportVo);
                continue;
            }else{
                String orgName = projectOrgExcelVo.getOrgName();
                projectOrgExportVo.setOrgName(orgName);
                SystemOrgInfoVo systemOrgInfoQuery = systemOrgInfoService.selectSystemOrgInfoByOrgName(orgName);
                if(systemOrgInfoQuery == null){
                    failureNum++;
                    projectOrgExportVo.setErrorMessage(BusinessConfig.PROJECT_ORG_RECORD_NOT_FOUND);
                    errorList.add(projectOrgExportVo);
                    continue;
                }
                ProjectOrgInfoExample example = new ProjectOrgInfoExample();
                ProjectOrgInfoExample.Criteria criteria = example.createCriteria();
                criteria.andProjectIdEqualTo(Long.parseLong(projectId));
                criteria.andOrgIdEqualTo(systemOrgInfoQuery.getOrgId());
                List<ProjectOrgInfo> projectOrgInfoList = projectOrgInfoMapper.selectByExample(example);
                if(projectOrgInfoList != null && projectOrgInfoList.size() >0){
                    failureNum++;
                    projectOrgExportVo.setErrorMessage(BusinessConfig.PROJECT_ORG_RECORD_FOUND);
                    errorList.add(projectOrgExportVo);
                    continue;
                }
                successNum++;
                orgList.add(String.valueOf(systemOrgInfoQuery.getOrgId()));
            }
        }
        saveBatchProjectOrgInfo(projectId, orgList, createUserId);
        if(failureNum > 0){
            msg.insert(0, "成功导入 " + successNum + " 条数据，失败导入 " + failureNum + "条数据，请重新导入");
        }else{
            msg.insert(0, "成功导入 " + successNum + " 条数据");
        }
        return msg.toString();
    }

    @Override
    public String deleteSystemUserOrgInfo(String userId) {
        systemUserOrgMapper.deleteSystemUserOrgInfo(userId);
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    @Override
    public CommonPage<ProjectComprehensiveVo> getProjectComprehensiveList(String projectId, String userId, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        List<ProjectComprehensiveVo> dataList = new ArrayList<>();
        List<String> orgList = new ArrayList<>();
        String orgIds = null;
        List<OrganizationVo> projectUserOrgList = this.getProjectUserOrgList(projectId, userId);
        if(CollectionUtil.isNotEmpty(projectUserOrgList)){
            for (OrganizationVo organizationVo : projectUserOrgList) {
                orgList.add(organizationVo.getId().toString());
            }
            String orgIdValue = orgList.stream().collect(Collectors.joining(","));
            orgIds = getQueryWrapperParams(orgIdValue);
        }

        ProjectUserInfoWrapper projectUserDataVo = projectUserService.getProjectUserRoleInfoByUserId(projectId, "", userId);
        if(projectUserDataVo != null){
            /*String ename = projectUserDataVo.getRoleCode();
            if(ProjectRoleEnum.PROJECT_PA.getCode().equals(ename) *//*|| "DM".equals(ename)*//*){
                List<String> organizationValue = getProjectUserOrgListByProjectId(projectId);
                String orgIdValue = organizationValue.stream().collect(Collectors.joining(","));
                orgIds = getQueryWrapperParams(orgIdValue);
            }*/
        }

        List<ProjectOrgVo> projectOrgVoList = projectOrgInfoMapper.getProjectOrgListForPage(projectId, null, null, orgIds);

        for (ProjectOrgVo projectOrgVo : projectOrgVoList) {
            ProjectComprehensiveVo projectComprehensiveVo = new ProjectComprehensiveVo();
            projectComprehensiveVo.setOrgName(projectOrgVo.getOrgName());

            List<String> paCountList = new ArrayList<>();
            List<String> piCountList = new ArrayList<>();
            List<String> crcCountList = new ArrayList<>();

            ProjectUserQueryParam projectUserParam = new ProjectUserQueryParam();
            projectUserParam.setProjectId(projectId);
            projectUserParam.setProjectOrgId(projectOrgVo.getOrgId().toString());
            projectUserParam.setPageNum(1);
            projectUserParam.setPageSize(100);
            List<ProjectUserVo> projectUserVoList = projectUserService.selectProjectUserListForPage(projectUserParam);
            for (ProjectUserVo projectUserVo : projectUserVoList) {
                /*if(ProjectRoleEnum.PROJECT_PA.getCode().equals(projectUserVo.getRoleCode())){
                    paCountList.add(projectUserVo.getId().toString());
                }
                if("PI".equals(projectUserVo.getRoleCode())){
                    piCountList.add(projectUserVo.getId().toString());
                }
                if("CRC".equals(projectUserVo.getRoleCode())){
                    crcCountList.add(projectUserVo.getId().toString());
                }*/
            }
            projectComprehensiveVo.setPaCount(paCountList.size());
            projectComprehensiveVo.setPiCount(piCountList.size());
            projectComprehensiveVo.setCrcCount(crcCountList.size());

            orgIds = getQueryWrapperParams(projectOrgVo.getOrgId().toString());
            //单中心查询
            int projectTesteeCount = projectTesteeInfoService.getProjectTesteeCount(projectId, userId, orgIds, true);
            projectComprehensiveVo.setIncreasedCount(projectTesteeCount);

            //质疑总量
            int changleCount = projectTesteeChallengeService.getProjectChallengeUnclosedCount(projectId, null, null, orgIds);
            projectComprehensiveVo.setChangleCount(changleCount);

            //实际访视总量
            /*Set<Long> followRealTimecount = new HashSet<>();
            List<ProjectVisitTesteeRecord> followRealTimeList = projectVisitConfigService.getProjectVisitFollowRealTimeCount(projectId, orgIds);
            for (ProjectVisitTesteeRecord projectVisitTesteeRecord : followRealTimeList) {
                followRealTimecount.add(projectVisitTesteeRecord.getTesteeId());
            }
            List<ProjectVisitTesteeRecord> overdueVisitList = projectVisitConfigService.getProjectOverdueVisitFollowRealTimeNotNullCount(projectId, orgIds, "nextFollowRealNotNullValue");
            for (ProjectVisitTesteeRecord projectVisitTesteeRecord : overdueVisitList) {
                followRealTimecount.add(projectVisitTesteeRecord.getTesteeId());
            }*/

            int plannedVisitCount = projectVisitConfigService.getProjectPlannedVisitCount(projectId, orgIds);
            projectComprehensiveVo.setPlannedVisitCount(plannedVisitCount);

            //超窗访视记录
            int overdueVisitCount = projectVisitConfigService.getProjectOverdueVisitFollowRealTimeNullCount(projectId, orgIds, "nextFollowRealNullValue");
            projectComprehensiveVo.setOverdueCount(overdueVisitCount);
            dataList.add(projectComprehensiveVo);
        }
        return commonPageListWrapper(pageNum, pageSize, page, dataList);
    }

    @Override
    public List<String> getProjectUserOrgListByProjectId(String projectId) {
        List<String> dataList = new ArrayList<>();
        List<ProjectOrgVo> projectOrgList = projectOrgInfoMapper.getProjectOrgListForPage(projectId, null, null, null);
        for (ProjectOrgVo projectOrgVo : projectOrgList) {
            dataList.add(projectOrgVo.getOrgId().toString());
        }
        return dataList;
    }

    @Override
    public ProjectOrgInfo getProjectOrgInfo(String projectOrgId) {
        return projectOrgInfoMapper.selectByPrimaryKey(Long.parseLong(projectOrgId));
    }

    @Override
    public ProjectVo getProjectByOrgIdentCode(String projectId, String identCode) {
        Project projectBaseInfo = projectBaseManageService.getProjectBaseInfo(projectId);
        if (projectBaseInfo != null){
            ProjectOrgInfoExample example = new ProjectOrgInfoExample();
            ProjectOrgInfoExample.Criteria criteria = example.createCriteria();
            criteria.andProjectIdEqualTo(projectBaseInfo.getId());
            criteria.andIdentCodeEqualTo(identCode);
            List<ProjectOrgInfo> orgInfoList = projectOrgInfoMapper.selectByExample(example);
            if (CollectionUtil.isNotEmpty(orgInfoList)){
                ProjectOrgInfo projectOrgInfo = orgInfoList.get(0);
                ProjectVo projectVo = new ProjectVo();
                projectVo.setId(projectBaseInfo.getId());
                projectVo.setName(projectBaseInfo.getName());
                projectVo.setCode(projectBaseInfo.getCode());
                Organization systemOrganizationInfo = this.getSystemOrganizationInfo(projectOrgInfo.getOrgId().toString());
                List<OrganizationVo> orgList = new ArrayList<>();
                OrganizationVo org= new OrganizationVo();
                org.setName(systemOrganizationInfo.getName());
                org.setId(systemOrganizationInfo.getId());
                orgList.add(org);
                projectVo.setOrgList(orgList);
                return projectVo;
            }
        }
        return null;
    }


    @Override
    public ProjectOrgIdentCodeVo getProjectOrgInfoByOrgIdentCode(String orgId) {
        ProjectOrgIdentCodeVo vo = new ProjectOrgIdentCodeVo();
        ProjectOrgInfo orgInfo = projectOrgInfoMapper.selectByPrimaryKey(Long.parseLong(orgId));
        if (orgInfo!=null){
            vo.setIndentCode(orgInfo.getIdentCode());
            ProjectVo projectVo = projectBaseManageService.getProjectViewInfo(orgInfo.getProjectId().toString());
            vo.setProjectName(projectVo.getName());
            Organization systemOrgInfo = this.getSystemOrganizationInfo(orgInfo.getOrgId().toString());
            vo.setOrgName(systemOrgInfo.getName());
        }
        return vo;
    }

    @Override
    public List<ProjectOrgInfo> selectProjectByIdentCode(String identCode) {
        return projectOrgInfoMapper.selectProjectByIdentCode(identCode);
    }

    @Override
    public Long getDefaultProjectOrgId(String tenantId, String platformId) {
        return projectOrgInfoMapper.getDefaultProjectOrgId(tenantId, platformId);
    }


    public String saveBatchProjectOrgInfo(String projectId, Set<String> orgIds, String createUserId){
        if(orgIds == null || orgIds.size() ==0){
            return "项目中心不能为空";
        }
        for (String orgId : orgIds) {
            saveProjectOrgInfo(null);
        }
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    @Override
    public CustomResult deleteProjectOrgByOrgId(String projectId, String projectOrgId){
        CustomResult customResult = new CustomResult();
        // 查询是否取得项目授权
        Set<String> realNameSet = new HashSet<>();
        List<ProjectUserOrg> projectOrgInfoList = projectUserOrgMapper.selectOrgListByProjectIdAndOrgId(projectId, projectOrgId);
        projectOrgInfoList.forEach(projectUserOrg->{
            List<ProjectUserOrgRoleVo> projectOrgUserRoleList = projectOrgRoleService.getProjectOrgUserRoleListByProjectIdAndUserId(projectId, projectUserOrg.getUserId().toString());
            if(CollectionUtil.isNotEmpty(projectOrgUserRoleList)){
                projectOrgUserRoleList.forEach(projectUserOrgRole->{
                    SystemUserInfo userBaseInfo = systemUserInfoService.getUserBaseInfo(projectUserOrgRole.getUserId());
                    if(userBaseInfo != null){
                        realNameSet.add(userBaseInfo.getRealName());
                    }
                });
            }
        });
        if(realNameSet.size() >0){
            throw new ServiceException(ResultCode.BUSINESS_DELETE_SYSTEM_USER_MESSAGE.getCode()+"", ResultCode.BUSINESS_DELETE_SYSTEM_USER_MESSAGE.getMessage().replace("${realName}", realNameSet.toString()));
        }
        List<ProjectTesteeVo> projectTesteeList = projectTesteeInfoService.getProjectTesteeAnalysisListByProjectId(projectId, "", projectOrgId);
        if(CollectionUtil.isNotEmpty(projectTesteeList)){
            customResult.setCode(ResultCode.PROJECT_ORG_USER_EXIST.getCode());
            customResult.setMessage(ResultCode.PROJECT_ORG_USER_EXIST.getMessage());
            return customResult;
        }
        projectOrgInfoMapper.deleteProjectOrgByOrgId(projectId, projectOrgId);
        return customResult;
    }

    @Override
    public List<ProjectOrgVo> getProjectOrgListForPage(String projectId, String orgId, String name, String officer, Integer pageNum, Integer pageSize){
        PageHelper.startPage(pageNum, pageSize);
        if(StringUtils.isNotEmpty(orgId)){
            orgId = getQueryWrapperParams(orgId);
        }
        List<ProjectOrgVo> projectOrgVoList = projectOrgInfoMapper.getProjectOrgListForPage(projectId, name, officer, orgId);
        projectOrgVoList.forEach(data->{
            if(StringUtils.isNotEmpty(data.getExpands())){
                String[] areaArray = data.getExpands().split(",");
                String areaName = systemAreaService.getSystemAreaNameByCode(areaArray[0], areaArray[1], areaArray[2]);
                data.setAreaName(areaName);

                data.setProvinceCode(areaArray[0]);
                data.setCityCode(areaArray[1]);
                data.setCountyCode(areaArray[2]);
            }
        });
        return projectOrgVoList;
    }


    public List<ProjectOrgVo> getProjectOrgListByCondition(String projectId, String orgId, String name){
        List<ProjectOrgVo> projectOrgVoList = projectOrgInfoMapper.getProjectOrgListForPage(projectId, name, null, orgId);
        return projectOrgVoList;
    }

        @Override
    public List<OrganizationVo> getUserOrgList(String userId){
        List<OrganizationVo> dataList = new ArrayList<>();
        ProjectUserOrgExample example = new ProjectUserOrgExample();
        ProjectUserOrgExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(Long.parseLong(userId));
        List<ProjectUserOrg> projectUserOrgList = projectUserOrgMapper.selectByExample(example);
        for (ProjectUserOrg projectUserOrg : projectUserOrgList) {
            SystemOrgInfo organization = systemOrgInfoService.selectSystemOrgInfoByOrgId(projectUserOrg.getOrgId().toString());
            if(organization != null){
                OrganizationVo organizationVo = new OrganizationVo();
                organizationVo.setId(organization.getOrgId());
                organizationVo.setName(organization.getOrgName());
                dataList.add(organizationVo);
            }
        }
        return dataList;
    }

    @Override
    public OrganizationVo getSystemUserOrgInfo(String userId){
        OrganizationVo organizationVo = new OrganizationVo();
        ProjectUserOrgExample example = new ProjectUserOrgExample();
        ProjectUserOrgExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(Long.parseLong(userId));
        List<ProjectUserOrg> userOrgList = projectUserOrgMapper.selectByExample(example);
        for (ProjectUserOrg projectUserOrg : userOrgList) {
            SystemOrgInfo organization = systemOrgInfoService.selectSystemOrgInfoByOrgId(projectUserOrg.getOrgId().toString());
            organizationVo.setId(organization.getOrgId());
            organizationVo.setName(organization.getOrgName());
            return organizationVo;
        }
        return null;
    }

    @Override
    public List<OrganizationVo> getProjectUserOrgList(String projectId, String userId) {
        List<OrganizationVo> dataList = new ArrayList<>();
        ProjectUserOrgExample example = new ProjectUserOrgExample();
        ProjectUserOrgExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(Long.parseLong(userId));
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        List<ProjectUserOrg> projectUserOrgList = projectUserOrgMapper.selectByExample(example);
        for (ProjectUserOrg projectUserOrg : projectUserOrgList) {
            OrganizationVo organizationVo = new OrganizationVo();
            organizationVo.setId(projectUserOrg.getOrgId());
            Organization organizationInfo = getSystemOrganizationInfo(projectUserOrg.getOrgId().toString());
            if(organizationInfo != null){
                organizationVo.setName(organizationInfo.getName());
            }
            dataList.add(organizationVo);
        }
        return dataList;
    }

    @Override
    public OrganizationVo getProjectUserOrgInfo(String projectId, String userId, String projectOrgId, String projectOrgCode) {
        OrganizationVo organizationVo = new OrganizationVo();
        ProjectUserOrgExample example = new ProjectUserOrgExample();
        ProjectUserOrgExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(Long.parseLong(userId));
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        List<ProjectUserOrg> projectUserOrgList = projectUserOrgMapper.selectByExample(example);
        for (ProjectUserOrg projectUserOrg : projectUserOrgList) {
            SystemOrgInfo organization = systemOrgInfoService.selectSystemOrgInfoByOrgId(projectUserOrg.getOrgId().toString());
            organizationVo.setId(organization.getOrgId());
            organizationVo.setName(organization.getOrgName());
            return organizationVo;
        }
        return null;
    }

    @Override
    public List<OrganizationVo> getOrganizationListForCombobox(String projectId, String orgName, String createUserId, String showProjectOrgCode, String showSelectAllOption) {
        List<OrganizationVo> dataList = new ArrayList<>();
        Project projectBaseInfo = projectBaseManageService.getProjectBaseInfo(projectId);
        if(projectBaseInfo != null){
            String createUser = projectBaseInfo.getCreateUser();
            if("1".equals(showSelectAllOption)){
                OrganizationVo organizationVo = new OrganizationVo();
                organizationVo.setName("全部研究中心");
                organizationVo.setProjectOrgCode("");
                dataList.add(organizationVo);
            }
            List<ProjectOrgVo> projectOrgList = projectOrgInfoMapper.getProjectOrgListForPage(projectId, orgName, null, null);
            for (ProjectOrgVo projectOrgVo : projectOrgList) {
                OrganizationVo organizationVo = new OrganizationVo();
                organizationVo.setId(projectOrgVo.getOrgId());
                organizationVo.setProjectOrgId(projectOrgVo.getId().toString());
                organizationVo.setIdentCode(projectOrgVo.getIdentCode());
                if("1".equals(showProjectOrgCode)){
                    organizationVo.setName(projectOrgVo.getCode() +"-"+ projectOrgVo.getOrgName());
                    organizationVo.setProjectOrgCode(projectOrgVo.getCode());
                    organizationVo.setSystemCode(projectOrgVo.getOrgCode());
                }else{
                    organizationVo.setName(projectOrgVo.getOrgName());
                }
                Object projectOrgId = redisTemplateService.get(RedisKeyContants.SEARCH_PROJECT_ORG_KEY + projectId + SecurityUtils.getUserIdValue());
                if(projectOrgId != null){
                    if(projectOrgVo.getId().toString().equals(projectOrgId.toString())){
                        organizationVo.setSelected(true);
                    }
                }
                dataList.add(organizationVo);
            }
        }
        return dataList;
    }

    @Override
    public Organization getSystemOrganizationInfo(String orgId) {
        Organization organization = new Organization();
        SystemOrgInfo systemOrgInfo = systemOrgInfoService.selectSystemOrgInfoByOrgId(orgId);
        if(systemOrgInfo == null){return null;}
        organization.setId(systemOrgInfo.getOrgId());
        organization.setName(systemOrgInfo.getOrgName());
        organization.setOrgCode(systemOrgInfo.getOrgCode());
        return organization;
    }

}
