package com.haoys.user.domain.vo.ecrf;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TemplateFormTableExcelImportVo implements Serializable {


    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @ApiModelProperty(value = "表单id")
    private Long formId;

    @ApiModelProperty(value = "表单详情id")
    private Long formDetailId;

    @ApiModelProperty(value = "表格单元格id")
    private Long formTableId;

    @ApiModelProperty(value = "模板id")
    private Long templateId;

    @ApiModelProperty(value = "组件类型")
    private String type;

    @ApiModelProperty(value = "字段key")
    private String key;

    @ApiModelProperty(value = "字段名称")
    private String label;

    @ApiModelProperty(value = "是否隐藏，默认展示字段0")
    private Boolean hidden;

    @ApiModelProperty(value = "是否必填项 默认非必填项")
    private Boolean required;

    @ApiModelProperty(value = "扩展信息")
    private String expand;

    @ApiModelProperty(value = "扩展字段1-用作初始化列展示")
    private String extData1;

    @ApiModelProperty(value = "扩展字段2-字段扩展属性")
    private String extData2;

    @ApiModelProperty(value = "扩展字段3-保存被删除前的原始数据")
    private String extData3;

    @ApiModelProperty(value = "扩展字段4")
    private String extData4;

    @ApiModelProperty(value = "所属变量id")
    private Long pointVariableId;

    @ApiModelProperty(value = "是否启用关联属性")
    private Boolean enableAssociate;

    @ApiModelProperty(value = "条件表达式")
    private String conditionExpression;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "数据状态")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "修改人")
    private String updateUser;
}
