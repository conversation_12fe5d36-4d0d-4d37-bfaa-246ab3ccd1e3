package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.domain.dto.DictExportDto;
import com.haoys.user.domain.dto.DictItemExportDto;
import com.haoys.user.domain.dto.ProjectDictionaryDto;
import com.haoys.user.domain.param.dict.ProjectDictParam;
import com.haoys.user.model.Dictionary;
import com.haoys.user.model.ProjectDictionary;

import java.util.List;

/**
 * 项目字典和计量单位字典service
 */
public interface ProjectDictionaryService {


    /**
     * 查询字典列表
     * @param projectDictParam 搜索条件
     * @return
     */
    CommonPage<ProjectDictionary> getDictionaryListForPage(ProjectDictParam projectDictParam);
    
    List<ProjectDictionary> getDictionaryList(String projectId, String dicType, String parentId, String name);

    /**
     * 保存系统字典以及系统字典的值
     * @param dictionaryParam
     * @return
     */
    CommonResult<Object> saveDictionary(ProjectDictionaryDto dictionaryParam);
    /**
     * 获取系统字典以及系统字典的值
     * @param id 字典id
     * @return
     */
    ProjectDictionary getDictionaryInfo(String id);

    /**
     * 修改字典信息
     * @param dictionaryParam
     * @return
     */
    CommonResult<Object> updateDictionary(ProjectDictionaryDto dto);
    /**
     * 根据字典名称 获取字典
     *
     * @param projectId
     * @param dictName  字典名称
     * @param parentId
     * @return
     */
    ProjectDictionary getDictByName(String projectId, String dictName, String parentId);

    /**
     * 根据字典名称 获取字典
     * @param dictEnName 字典英文名称
     * @param parentId
     * @return
     */
    ProjectDictionary getDictByEnName(String dictEnName,String parentId);

    /**
     * 根据字典编码 获取字典
     * @param code 字典编码
     * @param parentId 父id
     * @return
     */
    ProjectDictionary getDictByCode(String code,String parentId);

    /**
     * 根据父id获取字典明细 不分页
     * @return
     */
    List<Dictionary> getDictionaryListByParentId(String projectId, String parentId, String name, String dicType);

    /**
     * 根据id进行删除
     * @param dictId 字典id
     * @return
     */
    CommonResult<Object> removeById(String dictId);

    /**
     * 启用或者停用
     * @param id 字典id
     * @param status 状态
     * @return 结果
     */
    int enableOrUnable(String id, String status);

    /**
     * 导出字典查询方法
     * @param param 参数
     * @return
     */
    List<DictExportDto> getList(ProjectDictParam param);

    /**
     * 导出字典明细查询方法
     * @param param
     * @return
     */
    List<DictItemExportDto> exportDictItem(ProjectDictParam param);

    /**
     * 初始化项目字典
     * @param projectId 项目id
     * @return
     */
    CommonResult<Object> initProjectDictionary(Long projectId);


    /**
     * 根据
     * @param dictId 字典id
     * @param dictType 字典类型 1-系统字典2-表单字典 3-数据单位
     * @return
     */
    Dictionary getDict(Long dictId,String dictType);
    
    ProjectDictionary getProjectDictionaryByParentIdAndCode(String projectId, String parentId, String code);
}
