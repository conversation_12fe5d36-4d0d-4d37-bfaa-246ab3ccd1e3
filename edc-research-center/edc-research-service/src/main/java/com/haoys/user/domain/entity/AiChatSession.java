package com.haoys.user.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * AI聊天会话实体类
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Data
@ApiModel(value = "AiChatSession", description = "AI聊天会话")
public class AiChatSession implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "主键ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    
    @ApiModelProperty(value = "会话ID")
    private String sessionId;
    
    @ApiModelProperty(value = "用户ID")
    private String userId;
    
    @ApiModelProperty(value = "用户名")
    private String userName;
    
    @ApiModelProperty(value = "会话标题")
    private String title;
    
    @ApiModelProperty(value = "使用的模型类型")
    private String modelType;
    
    @ApiModelProperty(value = "具体模型名称")
    private String modelName;
    
    @ApiModelProperty(value = "会话状态(0-已结束,1-进行中)")
    private Integer status;
    
    @ApiModelProperty(value = "总消耗Token数")
    private Long totalTokens;
    
    @ApiModelProperty(value = "总消费金额")
    private BigDecimal totalCost;
    
    @ApiModelProperty(value = "消息数量")
    private Integer messageCount;
    
    @ApiModelProperty(value = "最后消息时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastMessageTime;
    
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
