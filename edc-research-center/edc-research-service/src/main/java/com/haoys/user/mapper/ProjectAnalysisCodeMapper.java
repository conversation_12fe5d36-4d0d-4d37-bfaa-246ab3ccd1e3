package com.haoys.user.mapper;

import com.haoys.user.model.ProjectAnalysisCode;
import com.haoys.user.model.ProjectAnalysisCodeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectAnalysisCodeMapper {
    long countByExample(ProjectAnalysisCodeExample example);

    int deleteByExample(ProjectAnalysisCodeExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectAnalysisCode record);

    int insertSelective(ProjectAnalysisCode record);

    List<ProjectAnalysisCode> selectByExample(ProjectAnalysisCodeExample example);

    ProjectAnalysisCode selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectAnalysisCode record, @Param("example") ProjectAnalysisCodeExample example);

    int updateByExample(@Param("record") ProjectAnalysisCode record, @Param("example") ProjectAnalysisCodeExample example);

    int updateByPrimaryKeySelective(ProjectAnalysisCode record);

    int updateByPrimaryKey(ProjectAnalysisCode record);
}