package com.haoys.user.mapper;

import com.haoys.user.domain.template.TemplateFormVariableWrapper;
import com.haoys.user.domain.vo.ecrf.TemplateFormConfigVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDetailVo;
import com.haoys.user.domain.vo.ecrf.TemplateTesteeJoinGroupFormVo;
import com.haoys.user.model.TemplateFormConfig;
import com.haoys.user.model.TemplateFormConfigExample;
import java.util.List;
import java.util.Map;

import com.haoys.user.model.TemplateFormDetail;
import com.haoys.user.model.TemplateFormTable;
import org.apache.ibatis.annotations.Param;

public interface TemplateFormConfigMapper {
    long countByExample(TemplateFormConfigExample example);

    int deleteByExample(TemplateFormConfigExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TemplateFormConfig record);

    int insertSelective(TemplateFormConfig record);

    List<TemplateFormConfig> selectByExample(TemplateFormConfigExample example);

    TemplateFormConfig selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TemplateFormConfig record, @Param("example") TemplateFormConfigExample example);

    int updateByExample(@Param("record") TemplateFormConfig record, @Param("example") TemplateFormConfigExample example);

    int updateByPrimaryKeySelective(TemplateFormConfig record);

    int updateByPrimaryKey(TemplateFormConfig record);

    /**
     * 查询模板表单项列表-系统模板和项目模板
     * @param templateId
     * @param templateName
     * @param projectId
     * @param configType
     * @param templateStatus
     * @param createUserId
     * @param tenantId
     * @return
     */
    List<TemplateFormConfigVo> getCustomTemplateFormConfigList(String templateId, String templateName, String projectId, String configType, String templateStatus, String createUserId, String tenantId);

    /**
     * 查询数据分析平台同步项目的所有表单和表格变量集合
     * @param projectId
     * @param planId
     * @return
     */
    List<TemplateFormDetail> getResearchTemplateFormVariableList(String projectId, String planId);


    /**
     * 根据项目id查询所有表单集合
     * @param projectId
     * @param queryTable
     * @return
     */
    List<TemplateFormConfig> getProjectFormListByProjectId(String projectId, String queryTable);

    /**
     * 根据表单id获取表单变量分页列表
     * @param templateId
     * @param formId
     * @param formDetailName
     * @param type
     * @return
     */
    List<TemplateFormVariableWrapper> getTemplateFormVariableListByFormIdForPage(String templateId, String formId, String formDetailName, String type);

    /**
     * 根据fieldName查询字段信息
     * @param fieldName
     * @return
     */
    String getTemplateFormDetailConfigByFieldName(String fieldName);


    /**
     * 根据项目查询表单列表
     * @param templateId
     * @param projectId
     * @param formName
     * @param publishStatus
     * @return
     */
    List<TemplateFormConfigVo> getTemplateFormConfigListByProjectId(String templateId, String projectId, String formName, Boolean publishStatus);

    /**
     * 根据方案查询表单列表
     * @param params
     * @return
     */
    List<TemplateFormConfigVo> getTemplateFormConfigListByPlanId(Map<String, Object> params);

    /**
     * 查询项目表单变量列表
     * @param projectId
     * @param formId
     * @return
     */
    List<TemplateFormDetail> getFormDetailBaseConfigListByFormId(String projectId, String formId);

    /**
     * 查询表格所在Header信息
     * @param formDetailId
     * @return
     */
    List<TemplateFormTable> getFormTableBaseConfigListByFormDetailId(Long formDetailId);

    Boolean getTemplateFormCode(String templateId, String projectId, String formCode);

    Boolean getTemplateFormDetailCode(String projectId, String formId, String fieldName);

    Boolean getTemplateFormTableCodeResult(String projectId, String formDetailId, String fieldName);

    List getSystemDictionaryTypeReference(String dictionaryId);

    List getProjectDictionaryTypeReference(String projectId, String dictionaryId);

    /**
     * 查询自定义标题列表
     * @param projectId
     * @param fieldName
     * @param showTitleView
     * @return
     */
    List<TemplateFormDetailVo> getTesteeCustomTitleList(String projectId, String fieldName, String showTitleView);

    /**
     * 查询参与者表单
     * @param projectId
     * @return
     */
    TemplateFormConfig getTemplateTesteeFormConfig(Long projectId);

    /**
     * 参与者表单字段列表
     * @param projectId
     * @param formId
     * @param showTitle
     * @return
     */
    List<TemplateFormDetailVo> getTemplateTesteeFormDetailBaseInfo(@Param("projectId") String projectId,@Param("formId")  String formId, @Param("showTitle") Boolean showTitle);

    TemplateFormDetailVo getTemplateTesteeFormDetailConfig(String projectId, String formId, String fieldName);
    
    TemplateTesteeJoinGroupFormVo getTemplateTesteeJoinGroupFormConfig(String projectId, String planId);
}