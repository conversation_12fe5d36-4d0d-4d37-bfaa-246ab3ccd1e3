package com.haoys.user.domain.vo.ecrf;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.haoys.user.model.ProjectTesteeChallenge;
import com.haoys.user.model.TemplateFormVariableRule;
import com.haoys.user.model.TemplateVariableViewBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class TemplateTableVo implements Serializable {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "id")
    private Long id;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "字段组模版单元格所在列id")
    private Long baseVariableTableId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "表单项id")
    private Long formId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "表单项详情id")
    private Long formDetailId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "模板id")
    private Long templateId;

    @ApiModelProperty(value = "组件类型")
    private String type;

    @ApiModelProperty(value = "数据库字段code")
    private String fieldName;

    @ApiModelProperty(value = "绑定属性")
    private String model;

    @ApiModelProperty(value = "字段名称")
    private String label;

    @ApiModelProperty(value = "字段英文名称")
    private String langValue;

    private String placeholder;

    @ApiModelProperty(value = "计量单位")
    private String unitValue = "";

    @ApiModelProperty(value = "是否隐藏，默认展示字段0")
    private Boolean hidden;

    @ApiModelProperty(value = "必填类型1-非必填2-强制必填3-必填提示")
    private String requireType;

    @ApiModelProperty(value = "是否必填项 默认非必填项")
    private Boolean required;

    @ApiModelProperty(value = "默认值")
    private String defaultValue = "";

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "字段下拉框选项")
    private String options;

    @ApiModelProperty(value = "字典来源(1-系统字典2-项目字典)")
    private String dicResource;

    @ApiModelProperty(value = "引用字典id")
    private String refDicId;

    @ApiModelProperty(value = "字典默认值")
    private String defaultDicValue;

    @ApiModelProperty(value = "控件尺寸")
    private String panelSize;

    @ApiModelProperty(value = "是否显示字段名称0/1")
    private Boolean showTitle = false;

    @ApiModelProperty(value = "是否显示内容0/1")
    private Boolean showContent = false;

    @ApiModelProperty(value = "扩展信息")
    private String expand;

    @ApiModelProperty(value = "扩展属性字段对象")
    private Object expandValue;
    
    @ApiModelProperty(value = "实验室配置指定范围 visit-访视 form-表单")
    private String labConfigScope;

    @ApiModelProperty(value = "扩展字段1")
    private String extData1;

    @ApiModelProperty(value = "扩展字段2")
    private String extData2;

    @ApiModelProperty(value = "扩展字段3")
    private String extData3;

    @ApiModelProperty(value = "扩展字段4")
    private String extData4;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "所属变量id")
    private Long pointVariableId;

    @ApiModelProperty(value = "是否启用关联属性")
    private Boolean enableAssociate;

    @ApiModelProperty(value = "条件表达式")
    private String conditionExpression;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "状态：0 正常，1 删除")
    private String status;

    @ApiModelProperty(value = "字典来源数据-按照类型查询")
    private List<TemplateFormDictionaryVo> templateFormDictionaryList = new ArrayList<>();

    private List<TemplateVariableViewBase> templateVariableViewBaseList = new ArrayList<>();

    @ApiModelProperty(value = "公式计算信息")
    private TemplateFormVariableRule variableRule;

    @ApiModelProperty(value = "质疑信息")
    private ProjectTesteeChallenge projectChallengeList;
    
    @ApiModelProperty(value = "是否存在实验室配置true/false")
    private Boolean enabledLabConfig = false;
    
}
