package com.haoys.user.domain.vo.flow;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class FlowPlanFormVo {


    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "方案id")
    private String planId;

    @ApiModelProperty(value = "方案名称")
    private String planName;

    @ApiModelProperty(value = "表单id")
    private String formId;
    
    private String formSetId;

    @ApiModelProperty(value = "表单名称")
    private String formName;
    
    private String formCode;
    
    private String formType;

    @ApiModelProperty(value = "pc端查看权限")
    private Boolean pcRoAuth;

    @ApiModelProperty(value = "pc端编辑权限")
    private Boolean pcRwAuth;

    @ApiModelProperty(value = "移动端查看权限")
    private Boolean moRoAuth;

    @ApiModelProperty(value = "移动端编辑权限")
    private Boolean moRwAuth;

    @ApiModelProperty(value = "数据状态0/1")
    private String status;

    @ApiModelProperty(value = "发布状态0/1")
    private Boolean publishStatus;

    @ApiModelProperty(value = "发布时间")
    private Date publishTime;

    @ApiModelProperty(value = "发布人")
    private String publishUserId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

}
