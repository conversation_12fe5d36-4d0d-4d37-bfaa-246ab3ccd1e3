package com.haoys.user.mapper;

import com.haoys.user.domain.vo.project.ProjectApplyUserVo;
import com.haoys.user.model.ProjectApplyUser;
import com.haoys.user.model.ProjectApplyUserExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectApplyUserMapper {
    long countByExample(ProjectApplyUserExample example);

    int deleteByExample(ProjectApplyUserExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectApplyUser record);

    int insertSelective(ProjectApplyUser record);

    List<ProjectApplyUser> selectByExample(ProjectApplyUserExample example);

    ProjectApplyUser selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectApplyUser record, @Param("example") ProjectApplyUserExample example);

    int updateByExample(@Param("record") ProjectApplyUser record, @Param("example") ProjectApplyUserExample example);

    int updateByPrimaryKeySelective(ProjectApplyUser record);

    int updateByPrimaryKey(ProjectApplyUser record);

    /**
     * 项目申请列表
     * @param projectId
     * @param applyUserName
     * @param projectCreateUser
     * @param orgId
     * @param startDate
     * @param endDate
     * @param status
     * @return
     */
    List<ProjectApplyUserVo> getUserApplyListForPage(String projectId, String applyUserName, String projectCreateUser, String orgId, String startDate, String endDate, String status);
}