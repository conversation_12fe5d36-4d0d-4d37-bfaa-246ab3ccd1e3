package com.haoys.user.model;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

public class TemplateFormDvpVal implements Serializable {
    private Long id;

    @ApiModelProperty(value = "逻辑规则ID")
    private Long dvpRuleId;

    @ApiModelProperty(value = "项目")
    private Long projectId;

    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @ApiModelProperty(value = "表单id")
    private Long formId;

    @ApiModelProperty(value = "字段组id")
    private Long groupId;

    @ApiModelProperty(value = "变量id")
    private Long formDetailId;

    @ApiModelProperty(value = "表格变量id")
    private Long formTableId;

    @ApiModelProperty(value = "字段类型")
    private String fieldType;

    @ApiModelProperty(value = "字典来源(1-系统字典2-表单字典 3-数据单位)")
    private String dicResource;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDvpRuleId() {
        return dvpRuleId;
    }

    public void setDvpRuleId(Long dvpRuleId) {
        this.dvpRuleId = dvpRuleId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getVisitId() {
        return visitId;
    }

    public void setVisitId(Long visitId) {
        this.visitId = visitId;
    }

    public Long getFormId() {
        return formId;
    }

    public void setFormId(Long formId) {
        this.formId = formId;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Long getFormDetailId() {
        return formDetailId;
    }

    public void setFormDetailId(Long formDetailId) {
        this.formDetailId = formDetailId;
    }

    public Long getFormTableId() {
        return formTableId;
    }

    public void setFormTableId(Long formTableId) {
        this.formTableId = formTableId;
    }

    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    public String getDicResource() {
        return dicResource;
    }

    public void setDicResource(String dicResource) {
        this.dicResource = dicResource;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", dvpRuleId=").append(dvpRuleId);
        sb.append(", projectId=").append(projectId);
        sb.append(", visitId=").append(visitId);
        sb.append(", formId=").append(formId);
        sb.append(", groupId=").append(groupId);
        sb.append(", formDetailId=").append(formDetailId);
        sb.append(", formTableId=").append(formTableId);
        sb.append(", fieldType=").append(fieldType);
        sb.append(", dicResource=").append(dicResource);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
