package com.haoys.user.interceptor;

import cn.hutool.core.util.StrUtil;
import com.haoys.user.common.core.domain.model.LoginUserInfo;
import com.haoys.user.common.ip.RequestIpUtils;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.service.SystemMonitorService;
import com.haoys.user.util.JwtTokenHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

/**
 * 访问日志拦截器
 * 用于记录用户访问日志和更新在线用户信息
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Slf4j
@Component
public class AccessLogInterceptor implements HandlerInterceptor {

    @Autowired
    private SystemMonitorService systemMonitorService;

    @Autowired
    private JwtTokenHelper jwtTokenHelper;

    @Autowired
    private RedisTemplateService redisTemplateService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 记录请求开始时间
        request.setAttribute("startTime", System.currentTimeMillis());
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        try {
            // 计算响应时间
            Long startTime = (Long) request.getAttribute("startTime");
            Long responseTime = startTime != null ? System.currentTimeMillis() - startTime : null;
            
            // 获取用户信息
            String userId = getUserId(request);
            String userName = getUserName(request);
            String realName = getRealName(request);
            String sessionId = getSessionId(request);
            
            // 获取请求信息
            String requestUrl = request.getRequestURI();
            String requestMethod = request.getMethod();
            String requestIp = RequestIpUtils.getIpAddress(request);
            String userAgent = request.getHeader("User-Agent");
            
            // 获取响应状态
            Integer responseStatus = response.getStatus();
            
            // 过滤不需要记录的请求
            if (shouldSkipLogging(requestUrl)) {
                return;
            }
            
            // 检查是否为登录请求
            boolean isLoginRequest = isLoginRequest(requestUrl, requestMethod);

            // 异步记录访问日志
            try {
                if (isLoginRequest) {
                    // 获取登录相关信息
                    String loginType = getLoginType(request);
                    String phoneNumber = getPhoneNumber(request);
                    Boolean loginSuccess = getLoginSuccess(response, ex);
                    String failureReason = getLoginFailureReason(response, ex);

                    // 记录登录日志
                    systemMonitorService.recordLoginLog(
                        userId, userName, realName, sessionId,
                        requestUrl, requestMethod, requestIp,
                        userAgent, responseTime, responseStatus,
                        loginType, phoneNumber, loginSuccess, failureReason
                    );

                    // 如果登录成功，记录用户登录信息
                    if (Boolean.TRUE.equals(loginSuccess) && StrUtil.isNotBlank(userId) && StrUtil.isNotBlank(sessionId)) {
                        String token = getAccessToken(request);
                        systemMonitorService.recordUserLogin(
                            userId, userName, realName, sessionId,
                            requestIp, userAgent, token, loginType, phoneNumber
                        );
                    }
                } else {
                    // 记录普通访问日志
                    systemMonitorService.recordAccessLog(
                        userId, userName, realName, sessionId,
                        requestUrl, requestMethod, requestIp,
                        userAgent, responseTime, responseStatus
                    );

                    // 更新在线用户信息
                    if (StrUtil.isNotBlank(userId) && StrUtil.isNotBlank(sessionId)) {
                        String token = getAccessToken(request);
                        systemMonitorService.updateOnlineUser(
                            userId, userName, realName, sessionId,
                            requestIp, userAgent, token
                        );
                    }
                }
            } catch (Exception e) {
                log.error("记录访问日志失败: {}", e.getMessage(), e);
            }
            
        } catch (Exception e) {
            log.error("访问日志拦截器处理失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取用户ID
     * 参照AiChatController中的getUserIdFromRequest方法实现
     */
    private String getUserId(HttpServletRequest request) {
        // 1. 优先从Spring Security上下文获取
        try {
            LoginUserInfo loginUserInfo = SecurityUtils.getLoginUser();
            if (loginUserInfo != null && loginUserInfo.getId() != null) {
                return loginUserInfo.getId().toString();
            }
        } catch (Exception e) {
            // 忽略异常，继续其他方式获取
            log.debug("从Spring Security上下文获取用户ID失败: {}", e.getMessage());
        }

        // 2. 从JWT Token中解析用户ID
        try {
            String tokenKey = jwtTokenHelper.getTokenKeyFromRequest(request);
            if (StrUtil.isNotBlank(tokenKey)) {
                LoginUserInfo tokenUserInfo = (LoginUserInfo) redisTemplateService.get(tokenKey);
                if (tokenUserInfo != null && tokenUserInfo.getId() != null) {
                    return tokenUserInfo.getId().toString();
                }
            }
        } catch (Exception e) {
            // 忽略异常，继续其他方式获取
            log.debug("从JWT Token中获取用户ID失败: {}", e.getMessage());
        }

        // 3. 从Session中获取用户ID
        try {
            HttpSession session = request.getSession(false);
            if (session != null) {
                Object userId = session.getAttribute("userId");
                if (userId != null) {
                    return userId.toString();
                }
            }
        } catch (Exception e) {
            log.debug("从Session中获取用户ID失败: {}", e.getMessage());
        }

        // 4. 从请求头中获取用户ID
        try {
            String userId = request.getHeader("X-User-Id");
            if (StrUtil.isNotBlank(userId)) {
                return userId;
            }
        } catch (Exception e) {
            log.debug("从请求头中获取用户ID失败: {}", e.getMessage());
        }

        // 4.5. 从请求头中获取X-Access-Token并查询用户信息
        try {
            String accessToken = request.getHeader("X-Access-Token");
            if (StrUtil.isNotBlank(accessToken)) {
                String userId = getUserIdFromAccessToken(accessToken);
                if (StrUtil.isNotBlank(userId)) {
                    log.debug("从请求头X-Access-Token获取到用户ID: {}", userId);
                    return userId;
                }
            }
        } catch (Exception e) {
            log.debug("从请求头X-Access-Token获取用户ID失败: {}", e.getMessage());
        }

        // 5. 从Cookie中获取access_token并查询用户信息
        try {
            String accessToken = getAccessTokenFromCookie(request);
            if (StrUtil.isNotBlank(accessToken)) {
                String userId = getUserIdFromAccessToken(accessToken);
                if (StrUtil.isNotBlank(userId)) {
                    log.debug("从Cookie中的access_token获取到用户ID: {}", userId);
                    return userId;
                }
            }
        } catch (Exception e) {
            log.debug("从Cookie中的access_token获取用户ID失败: {}", e.getMessage());
        }

        // 6. 如果都获取不到，返回匿名用户标识
        return "anonymousUser";
    }

    /**
     * 获取用户名
     * 参照AiChatController中的getUserNameFromRequest方法实现
     */
    private String getUserName(HttpServletRequest request) {
        // 1. 优先从Spring Security上下文获取
        try {
            LoginUserInfo loginUserInfo = SecurityUtils.getLoginUser();
            if (loginUserInfo != null && StrUtil.isNotBlank(loginUserInfo.getUsername())) {
                return loginUserInfo.getUsername();
            }
        } catch (Exception e) {
            // 忽略异常，继续其他方式获取
            log.debug("从Spring Security上下文获取用户名失败: {}", e.getMessage());
        }

        // 2. 从JWT Token中解析用户名
        try {
            String tokenKey = jwtTokenHelper.getTokenKeyFromRequest(request);
            if (StrUtil.isNotBlank(tokenKey)) {
                LoginUserInfo tokenUserInfo = (LoginUserInfo) redisTemplateService.get(tokenKey);
                if (tokenUserInfo != null && StrUtil.isNotBlank(tokenUserInfo.getUsername())) {
                    return tokenUserInfo.getUsername();
                }
            }
        } catch (Exception e) {
            // 忽略异常，继续其他方式获取
            log.debug("从JWT Token中获取用户名失败: {}", e.getMessage());
        }

        // 3. 从Session中获取用户名
        try {
            HttpSession session = request.getSession(false);
            if (session != null) {
                Object userName = session.getAttribute("userName");
                if (userName != null) {
                    return userName.toString();
                }
            }
        } catch (Exception e) {
            log.debug("从Session中获取用户名失败: {}", e.getMessage());
        }

        // 4. 从请求头中获取用户名
        try {
            String userName = request.getHeader("X-User-Name");
            if (StrUtil.isNotBlank(userName)) {
                return userName;
            }
        } catch (Exception e) {
            log.debug("从请求头中获取用户名失败: {}", e.getMessage());
        }

        // 4.5. 从请求头中获取X-Access-Token并查询用户名
        try {
            String accessToken = request.getHeader("X-Access-Token");
            if (StrUtil.isNotBlank(accessToken)) {
                String userName = getUserNameFromAccessToken(accessToken);
                if (StrUtil.isNotBlank(userName)) {
                    log.debug("从请求头X-Access-Token获取到用户名: {}", userName);
                    return userName;
                }
            }
        } catch (Exception e) {
            log.debug("从请求头X-Access-Token获取用户名失败: {}", e.getMessage());
        }

        // 5. 从Cookie中获取access_token并查询用户名
        try {
            String accessToken = getAccessTokenFromCookie(request);
            if (StrUtil.isNotBlank(accessToken)) {
                String userName = getUserNameFromAccessToken(accessToken);
                if (StrUtil.isNotBlank(userName)) {
                    log.debug("从Cookie中的access_token获取到用户名: {}", userName);
                    return userName;
                }
            }
        } catch (Exception e) {
            log.debug("从Cookie中的access_token获取用户名失败: {}", e.getMessage());
        }

        // 6. 如果都获取不到，返回匿名用户标识
        return "匿名用户";
    }

    /**
     * 获取真实姓名
     * 参照AiChatController中的getRealNameFromRequest方法实现
     */
    private String getRealName(HttpServletRequest request) {
        // 1. 优先从Spring Security上下文获取
        try {
            LoginUserInfo loginUserInfo = SecurityUtils.getLoginUser();
            if (loginUserInfo != null && StrUtil.isNotBlank(loginUserInfo.getRealName())) {
                return loginUserInfo.getRealName();
            }
        } catch (Exception e) {
            // 忽略异常，继续其他方式获取
            log.debug("从Spring Security上下文获取真实姓名失败: {}", e.getMessage());
        }

        // 2. 从JWT Token中解析真实姓名
        try {
            String tokenKey = jwtTokenHelper.getTokenKeyFromRequest(request);
            if (StrUtil.isNotBlank(tokenKey)) {
                LoginUserInfo tokenUserInfo = (LoginUserInfo) redisTemplateService.get(tokenKey);
                if (tokenUserInfo != null && StrUtil.isNotBlank(tokenUserInfo.getRealName())) {
                    return tokenUserInfo.getRealName();
                }
            }
        } catch (Exception e) {
            // 忽略异常，继续其他方式获取
            log.debug("从JWT Token中获取真实姓名失败: {}", e.getMessage());
        }

        // 3. 从Session中获取真实姓名
        try {
            HttpSession session = request.getSession(false);
            if (session != null) {
                Object realName = session.getAttribute("realName");
                if (realName != null) {
                    return realName.toString();
                }
            }
        } catch (Exception e) {
            log.debug("从Session中获取真实姓名失败: {}", e.getMessage());
        }

        // 4. 从请求头中获取真实姓名
        try {
            String realName = request.getHeader("X-Real-Name");
            if (StrUtil.isNotBlank(realName)) {
                return realName;
            }
        } catch (Exception e) {
            log.debug("从请求头中获取真实姓名失败: {}", e.getMessage());
        }

        // 4.5. 从请求头中获取X-Access-Token并查询真实姓名
        try {
            String accessToken = request.getHeader("X-Access-Token");
            if (StrUtil.isNotBlank(accessToken)) {
                String realName = getRealNameFromAccessToken(accessToken);
                if (StrUtil.isNotBlank(realName)) {
                    log.debug("从请求头X-Access-Token获取到真实姓名: {}", realName);
                    return realName;
                }
            }
        } catch (Exception e) {
            log.debug("从请求头X-Access-Token获取真实姓名失败: {}", e.getMessage());
        }

        // 5. 从Cookie中获取access_token并查询真实姓名
        try {
            String accessToken = getAccessTokenFromCookie(request);
            if (StrUtil.isNotBlank(accessToken)) {
                String realName = getRealNameFromAccessToken(accessToken);
                if (StrUtil.isNotBlank(realName)) {
                    log.debug("从Cookie中的access_token获取到真实姓名: {}", realName);
                    return realName;
                }
            }
        } catch (Exception e) {
            log.debug("从Cookie中的access_token获取真实姓名失败: {}", e.getMessage());
        }

        // 6. 如果都获取不到，返回匿名用户标识
        return "匿名用户";
    }

    /**
     * 获取会话ID
     */
    private String getSessionId(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            return session.getId();
        }

        // 如果没有session，尝试从请求头获取
        String sessionId = request.getHeader("X-Session-Id");
        if (StrUtil.isNotBlank(sessionId)) {
            return sessionId;
        }

        // 如果都获取不到，返回匿名会话标识
        return "anonymousSession";
    }

    /**
     * 获取访问令牌
     */
    private String getAccessToken(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (StrUtil.isNotBlank(token) && token.startsWith("Bearer ")) {
            return token.substring(7);
        }
        
        token = request.getParameter("accessToken");
        if (StrUtil.isNotBlank(token)) {
            return token;
        }
        
        return null;
    }

    /**
     * 判断是否跳过日志记录
     */
    private boolean shouldSkipLogging(String requestUrl) {
        if (StrUtil.isBlank(requestUrl)) {
            return true;
        }
        
        // 跳过静态资源
        String[] skipPatterns = {
            "/static/", "/css/", "/js/", "/images/", "/fonts/",
            "/favicon.ico", "/robots.txt", "/sitemap.xml",
            "/actuator/", "/health", "/metrics",
            "/swagger-", "/v2/api-docs", "/webjars/"
        };
        
        for (String pattern : skipPatterns) {
            if (requestUrl.contains(pattern)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 判断是否为登录请求
     */
    private boolean isLoginRequest(String requestUrl, String requestMethod) {
        if (StrUtil.isBlank(requestUrl) || !"POST".equalsIgnoreCase(requestMethod)) {
            return false;
        }

        // 登录相关的URL模式
        String[] loginPatterns = {
            "/login", "/auth/login", "/user/login", "/system/login",
            "/mobile/login", "/phone/login", "/sms/login",
            "/oauth/token", "/auth/token", "/api/login"
        };

        for (String pattern : loginPatterns) {
            if (requestUrl.contains(pattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取登录类型
     */
    private String getLoginType(HttpServletRequest request) {
        // 从请求参数或请求体中获取登录类型
        String loginType = request.getParameter("loginType");
        if (StrUtil.isNotBlank(loginType)) {
            return loginType;
        }

        // 根据请求URL判断登录类型
        String requestUrl = request.getRequestURI();
        if (requestUrl.contains("phone") || requestUrl.contains("sms") || requestUrl.contains("mobile")) {
            return "PHONE_CODE";
        } else if (requestUrl.contains("oauth") || requestUrl.contains("sso")) {
            return "SSO";
        } else if (requestUrl.contains("wechat") || requestUrl.contains("wx")) {
            return "WECHAT";
        } else {
            return "USERNAME_PASSWORD";
        }
    }

    /**
     * 获取手机号
     */
    private String getPhoneNumber(HttpServletRequest request) {
        // 从请求参数中获取手机号
        String phone = request.getParameter("phone");
        if (StrUtil.isNotBlank(phone)) {
            return phone;
        }

        phone = request.getParameter("mobile");
        if (StrUtil.isNotBlank(phone)) {
            return phone;
        }

        phone = request.getParameter("phoneNumber");
        if (StrUtil.isNotBlank(phone)) {
            return phone;
        }

        return null;
    }

    /**
     * 判断登录是否成功
     */
    private Boolean getLoginSuccess(HttpServletResponse response, Exception ex) {
        // 如果有异常，登录失败
        if (ex != null) {
            return false;
        }

        // 根据响应状态码判断
        int status = response.getStatus();
        if (status >= 200 && status < 300) {
            return true;
        } else if (status >= 400) {
            return false;
        }

        // 默认返回null，表示无法确定
        return null;
    }

    /**
     * 获取登录失败原因
     */
    private String getLoginFailureReason(HttpServletResponse response, Exception ex) {
        if (ex != null) {
            return ex.getMessage();
        }

        int status = response.getStatus();
        switch (status) {
            case 401:
                return "用户名或密码错误";
            case 403:
                return "账户被禁用或权限不足";
            case 423:
                return "账户被锁定";
            case 429:
                return "登录尝试次数过多";
            default:
                if (status >= 400) {
                    return "登录失败，状态码: " + status;
                }
                return null;
        }
    }

    /**
     * 根据access_token获取用户ID
     *
     * @param accessToken 访问令牌
     * @return 用户ID，如果获取失败返回null
     */
    private String getUserIdFromAccessToken(String accessToken) {
        try {
            // 1. 首先尝试使用SecureTokenService获取扩展信息
            // 注意：这里需要注入SecureTokenService，暂时使用JWT方式
            LoginUserInfo loginUserInfo = jwtTokenHelper.getSystemLoginUserByToken(accessToken);
            if (loginUserInfo != null && loginUserInfo.getId() != null) {
                log.debug("从JWT Token获取到用户ID: {}", loginUserInfo.getId());
                return loginUserInfo.getId().toString();
            }
        } catch (Exception e) {
            log.debug("从JWT Token获取用户ID失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 根据access_token获取用户名
     *
     * @param accessToken 访问令牌
     * @return 用户名，如果获取失败返回null
     */
    private String getUserNameFromAccessToken(String accessToken) {
        try {
            // 1. 首先尝试使用JWT Token获取用户信息
            LoginUserInfo loginUserInfo = jwtTokenHelper.getSystemLoginUserByToken(accessToken);
            if (loginUserInfo != null && StrUtil.isNotBlank(loginUserInfo.getUsername())) {
                log.debug("从JWT Token获取到用户名: {}", loginUserInfo.getUsername());
                return loginUserInfo.getUsername();
            }
        } catch (Exception e) {
            log.debug("从JWT Token获取用户名失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 根据access_token获取真实姓名
     *
     * @param accessToken 访问令牌
     * @return 真实姓名，如果获取失败返回null
     */
    private String getRealNameFromAccessToken(String accessToken) {
        try {
            // 1. 首先尝试使用JWT Token获取用户信息
            LoginUserInfo loginUserInfo = jwtTokenHelper.getSystemLoginUserByToken(accessToken);
            if (loginUserInfo != null) {
                String realName = loginUserInfo.getRealName();
                if (StrUtil.isBlank(realName)) {
                    realName = loginUserInfo.getUsername(); // 如果没有真实姓名，使用用户名
                }
                log.debug("从JWT Token获取到真实姓名: {}", realName);
                return realName;
            }
        } catch (Exception e) {
            log.debug("从JWT Token获取真实姓名失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 从Cookie中获取access_token
     *
     * @param request HTTP请求
     * @return access_token，如果获取失败返回null
     */
    private String getAccessTokenFromCookie(HttpServletRequest request) {
        try {
            javax.servlet.http.Cookie[] cookies = request.getCookies();
            if (cookies != null) {
                for (javax.servlet.http.Cookie cookie : cookies) {
                    if ("access_token".equals(cookie.getName())) {
                        return cookie.getValue();
                    }
                }
            }
        } catch (Exception e) {
            log.debug("从Cookie中获取access_token失败: {}", e.getMessage());
        }
        return null;
    }
}
