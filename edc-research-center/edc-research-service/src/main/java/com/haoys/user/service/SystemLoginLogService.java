package com.haoys.user.service;


import com.haoys.user.common.api.CommonPage;
import com.haoys.user.domain.entity.SystemLoginLogQuery;
import com.haoys.user.domain.vo.system.SystemLoginLogVo;
import com.haoys.user.model.SystemLoginLog;

import javax.servlet.http.HttpServletResponse;


/**
 */
public interface SystemLoginLogService {

    void insertSystemUserLoginLog(SystemLoginLog systemLoginLog);

    /**
     * 查询系统集合
     *
     * @param systemLoginLogQuery 访问日志对象
     * @return 登录记录集合
     */
    CommonPage<SystemLoginLogVo> selectLoginRecordList(SystemLoginLogQuery systemLoginLogQuery, Integer pageNum, Integer pageSize);

    /**
     * 批量删除系统
     * @param infoIds 需要删除的ID
     * @return
     */
    int deleteLogininforByIds(Long[] infoIds);

    /**
     * 清空系统登录日志
     */
    void cleanLogininfor();

    void importSystemLoginLog(HttpServletResponse response, SystemLoginLogQuery systemLoginLogQuery);

}
