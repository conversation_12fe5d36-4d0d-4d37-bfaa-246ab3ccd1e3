package com.haoys.user.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.domain.vo.project.ProjectApplyUserVo;
import com.haoys.user.mapper.ProjectApplyUserMapper;
import com.haoys.user.mapper.ProjectUserInfoMapper;
import com.haoys.user.model.ProjectApplyUser;
import com.haoys.user.model.ProjectApplyUserExample;
import com.haoys.user.model.ProjectUserInfo;
import com.haoys.user.model.ProjectUserInfoExample;
import com.haoys.user.service.ProjectApplyUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class ProjectApplyUserServiceImpl implements ProjectApplyUserService {

    private final ProjectApplyUserMapper projectApplyUserMapper;
    private final ProjectUserInfoMapper projectUserInfoMapper;

    @Override
    public List<ProjectApplyUser> getQueryProjectList(Long orgId, String userId) {
        ProjectApplyUserExample example = new ProjectApplyUserExample();
        ProjectApplyUserExample.Criteria criteria = example.createCriteria();
        criteria.andOrgIdEqualTo(orgId);
        criteria.andUserIdEqualTo(userId);
        List<ProjectApplyUser> projectApplyUsers = projectApplyUserMapper.selectByExample(example);
        return projectApplyUsers;
    }


    public boolean getProjectUserResult(String projectId, String userId){
        ProjectApplyUserExample example = new ProjectApplyUserExample();
        ProjectApplyUserExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andUserIdEqualTo(userId);
        List<ProjectApplyUser> projectApplyUserList = projectApplyUserMapper.selectByExample(example);
        return (projectApplyUserList != null && projectApplyUserList.size() >0);
    }

    @Override
    public void insert(ProjectApplyUser projectApplyUser) {
        projectApplyUserMapper.insert(projectApplyUser);
    }

    @Override
    public ProjectApplyUser selectByPrimaryKey(long id) {
        return projectApplyUserMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<ProjectApplyUser> selectByExample(ProjectApplyUserExample example) {
        return projectApplyUserMapper.selectByExample(example);
    }

    @Override
    public List<ProjectApplyUserVo> getUserApplyListForPage(String projectId, String applyUserName, String projectCreateUser, String orgId, String startDate, String endDate, String status) {
        return projectApplyUserMapper.getUserApplyListForPage(projectId, applyUserName, projectCreateUser, orgId, startDate, endDate, status);
    }

    @Override
    public void updateByPrimaryKeySelective(ProjectApplyUser projectApplyUser) {
        projectApplyUserMapper.updateByPrimaryKeySelective(projectApplyUser);
    }


    /**
     * 申请加入项目
     * @param projectId 项目id
     * @return 申请加入结果
     */
    @Override
    public CommonResult<Object> apply(Long projectId) {
        ProjectApplyUser applyUser = new ProjectApplyUser();
        applyUser.setId(SnowflakeIdWorker.getUuid());
        applyUser.setProjectId(projectId);
        applyUser.setUserId(SecurityUtils.getUserId().toString());
        applyUser.setApplyTime(new Date());
        applyUser.setCreateTime(new Date());
        applyUser.setCheckStatus(BusinessConfig.PROJECT_APPLY_STATUS_1);
        applyUser.setTenantId(SecurityUtils.getSystemTenantId());
        applyUser.setPlatformId(SecurityUtils.getSystemPlatformId());
        int insert = projectApplyUserMapper.insert(applyUser);
        return insert>0?CommonResult.success(""):CommonResult.failed();
    }

    /**
     * 获取申请列表
     * @return 申请列表
     */
    @Override
    public CommonResult<Object> applyList() {
        ProjectApplyUserExample example = new ProjectApplyUserExample();
        ProjectApplyUserExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(SecurityUtils.getUserId().toString());
        List<ProjectApplyUser> list = projectApplyUserMapper.selectByExample(example);
        return CommonResult.success(list);
    }
    /**
     * 获取审批列表
     * @return 审批列表
     */
    @Override
    public CommonResult<List<ProjectApplyUser>> auditList() {
        // 获取当前用户是项目管理员的项目。
        ProjectUserInfoExample example = new ProjectUserInfoExample();
        ProjectUserInfoExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(SecurityUtils.getUserId());
        criteria.andPaRoleEqualTo(true);
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        List<ProjectUserInfo> projectUserInfoList = projectUserInfoMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(projectUserInfoList)){
            // 循环存放项目id
            List<Long> projectIds = new ArrayList<>();
            projectUserInfoList.forEach(y->projectIds.add(y.getProjectId()));
            // 根据项目id获取项目申请列表
            ProjectApplyUserExample example1 = new ProjectApplyUserExample();
            ProjectApplyUserExample.Criteria criteria1 = example1.createCriteria();
            criteria1.andProjectIdIn(projectIds);
            criteria1.andCheckStatusNotEqualTo(BusinessConfig.PROJECT_APPLY_STATUS_0);
            List<ProjectApplyUser> list = projectApplyUserMapper.selectByExample(example1);
            return CommonResult.success(list);
        }
        return CommonResult.success(new ArrayList<>());
    }

    /**
     * 审批
     * @param applyId 申请记录id
     * @param applyStatus 审批状态
     * @return 审批结果
     */
    @Override
    public CommonResult<Object> audit(Long applyId, String applyStatus) {
        ProjectApplyUser applyUser = projectApplyUserMapper.selectByPrimaryKey(applyId);
        if (applyUser!=null){
            applyUser.setCheckStatus(applyStatus);
            applyUser.setCheckTime(new Date());
            applyUser.setCheckUserId(SecurityUtils.getUserId().toString());
            int i = projectApplyUserMapper.updateByPrimaryKey(applyUser);
            return i>0?CommonResult.success(""):CommonResult.failed();

        }
        return CommonResult.failed();
    }

}
