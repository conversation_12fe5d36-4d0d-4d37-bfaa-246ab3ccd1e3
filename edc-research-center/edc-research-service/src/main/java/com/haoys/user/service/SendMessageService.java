package com.haoys.user.service;

import com.haoys.user.domain.vo.system.SendMessageRecordVo;
import com.haoys.user.model.SendMessageRecord;

import java.util.List;

public interface SendMessageService {
    
    void insertSendMessageRecord(String messageAccount, String code, String requestId, String responseText);
    
    List<SendMessageRecord> getSendMessageList(Integer currentPage, Integer pageSize);
    
    List<SendMessageRecordVo> getSendMessageListForPage(String mobile, Integer currentPage, Integer pageSize);
}
