package com.haoys.user.domain.vo.testee;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectTesteeVisitExcelVo implements Serializable {


    @Excel(name = "id",orderNum = "1")
    private String id;
    @Excel(name = "访视名称",orderNum = "2",groupName = "访视表单信息")
    private String visitName;
    @Excel(name = "姓名",orderNum = "3",width = 20,groupName = "访视表单信息")
    private String realName;
    @Excel(name = "标签名称",orderNum = "4",groupName = "访视表单信息")
    private String lable;
    @Excel(name = "字段名称",orderNum = "5",groupName = "访视表单信息")
    private String fieldName;
    @Excel(name = "选择项",orderNum = "6",groupName = "提交结果")
    private String fieldValue;

}
