package com.haoys.user.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ProjectAnalysisRecordVo implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "数据分析批次号")
    private String batchCode;

    @ApiModelProperty(value = "样本数据id")
    private Long sampleId;

    @ApiModelProperty(value = "患者姓名")
    private String sampleName;

    @ApiModelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "诊断描述")
    private String diagnosticDesc;

    @ApiModelProperty(value = "药方内容")
    private String content;
}
