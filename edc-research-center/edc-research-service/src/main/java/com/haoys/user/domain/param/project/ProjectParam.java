package com.haoys.user.domain.param.project;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@Data
public class ProjectParam {

    @ApiModelProperty(value = "项目id-编辑数据时设置")
    private Long id;

    @ApiModelProperty(value = "项目名称")
    @NotEmpty(message = "项目名称不能为空")
    private String name;

    @ApiModelProperty(value = "项目编号")
    @NotEmpty(message = "研究编号不能为空")
    private String code;

    @ApiModelProperty(value = "组长单位")
    @NotEmpty(message = "组长单位不能为空")
    private String groupUnit;

    @ApiModelProperty(value = "项目开始时间 yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty(value = "项目结束时间 yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    @ApiModelProperty(value = "是否启用药方分析")
    private Boolean enablePrescriptionAnalysis = false;

    @ApiModelProperty(value = "项目说明")
    private String description;

    @ApiModelProperty(value = "项目是否公开 1-公开 0-不公开")
    private Boolean ifPublic = true;

    @ApiModelProperty(value = "是否针对指定中心发布 1-指定中心发布 0-否")
    private Boolean ifPublishOrg;

    @ApiModelProperty(value = "项目性质-前瞻性和回顾性")
    private String projectNature;

    @ApiModelProperty(value = "研究领域")
    //@NotEmpty(message = "研究领域不能为空")
    private String researchArea;

    @ApiModelProperty(value = "发起单位")
    //@NotEmpty(message = "发起单位不能为空")
    private String nitiator;

    @ApiModelProperty(value = "发起人")
    //@NotEmpty(message = "发起人不能为空")
    private String sponsor;

    @ApiModelProperty(value = "注册号")
    private String registeNumber;

    @ApiModelProperty(value = "备案号")
    private String recordNumber;

    @ApiModelProperty(value = "模板id")
    private Long templateId;

    @ApiModelProperty(value = "参与者录入总量")
    private Integer totalCount;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

    @ApiModelProperty(value = "计划开始时间")
    private Date planStartDate;

    @ApiModelProperty(value = "计划入组数量")
    private Integer planGroupNum;

    @ApiModelProperty(value = "申办单位联系方式")
    private String nitiatorContact;

    @ApiModelProperty(value = "计划参与研究中心数量")
    private Integer involvedOrgNum;

    @ApiModelProperty(value = "组长单位主要研究者")
    private String guResearcher;

    @ApiModelProperty(value = "组长单位主要研究者联系方式")
    private String guContact;

    @ApiModelProperty(value = "项目类型")
    private String projectType;

    @ApiModelProperty(value = "是否同步创建研究中心")
    private Boolean createProjectOrgId = false;

    @ApiModelProperty(value = "数据库id")
    private String databaseId;

    @ApiModelProperty(value = "数据库来源")
    private String databaseFrom;

    @ApiModelProperty(value = "研究方式 1-单中心 2-多中心")
    private String researchMethod;
    
    @ApiModelProperty(value = "创建项目方式 1-手动 2-引用项目 3-引用表单")
    private String createMethod;
    
    @ApiModelProperty(value = "引用项目id")
    private String referenceProjectId;
    
    @ApiModelProperty(value = "是否开启随机配置0/1")
    private Boolean enableRandomizedConfig = false;
}
