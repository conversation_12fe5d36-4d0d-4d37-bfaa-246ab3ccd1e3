package com.haoys.user.service;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.domain.vo.testee.ProjectTesteeVariableSyncVo;
import com.haoys.user.model.ProjectTesteeVariableSync;

/**
 * 自定义标题-映射
 */
public interface ProjectTesteeVariableSyncService {
    /**
     * 保存
     * @param projectTesteeVariableSync
     * @return
     */
    CommonResult<Object> create(ProjectTesteeVariableSync projectTesteeVariableSync);

    /**
     * 根据id获取
     * @param baseVariableId 从源同步id
     * @return
     */
    CommonResult<ProjectTesteeVariableSyncVo> getByBaseVariableId(Long baseVariableId);

    ProjectTesteeVariableSyncVo getVariableSyncByBaseVariableId(Long baseVariableId);
}
