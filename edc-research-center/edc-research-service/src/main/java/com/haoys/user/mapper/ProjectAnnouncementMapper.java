package com.haoys.user.mapper;

import com.haoys.user.domain.vo.project.ProjectAnnouncementVo;
import com.haoys.user.model.ProjectAnnouncement;
import com.haoys.user.model.ProjectAnnouncementExample;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface ProjectAnnouncementMapper {
    long countByExample(ProjectAnnouncementExample example);

    int deleteByExample(ProjectAnnouncementExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectAnnouncement record);

    int insertSelective(ProjectAnnouncement record);

    List<ProjectAnnouncement> selectByExample(ProjectAnnouncementExample example);

    ProjectAnnouncement selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectAnnouncement record, @Param("example") ProjectAnnouncementExample example);

    int updateByExample(@Param("record") ProjectAnnouncement record, @Param("example") ProjectAnnouncementExample example);

    int updateByPrimaryKeySelective(ProjectAnnouncement record);

    int updateByPrimaryKey(ProjectAnnouncement record);

    List<ProjectAnnouncementVo> selectProjectAnnouncementList(ProjectAnnouncement record);

}