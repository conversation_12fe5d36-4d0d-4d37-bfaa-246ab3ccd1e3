package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.async.AsyncTaskProjectTemplateFormConfig;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.dto.ProjectOrgParam;
import com.haoys.user.domain.param.project.ProjectParam;
import com.haoys.user.domain.vo.auth.ProjectRoleVo;
import com.haoys.user.domain.vo.overview.ProjectOverViewVo;
import com.haoys.user.domain.vo.project.ProjectApplyUserVo;
import com.haoys.user.domain.vo.project.ProjectNameAndCodeResult;
import com.haoys.user.domain.vo.project.ProjectTesteeOrgVo;
import com.haoys.user.domain.vo.project.ProjectUserOrgVo;
import com.haoys.user.domain.vo.project.ProjectUserVo;
import com.haoys.user.domain.vo.project.ProjectVo;
import com.haoys.user.domain.vo.project.SysDepartmentVo;
import com.haoys.user.domain.vo.project.SystemUserInfoExtendVo;
import com.haoys.user.domain.vo.system.OrganizationVo;
import com.haoys.user.domain.vo.system.SystemUserInfoVo;
import com.haoys.user.domain.wrapper.ProjectUserInfoWrapper;
import com.haoys.user.enums.ProjectApplyEnum;
import com.haoys.user.enums.ProjectStatusEnum;
import com.haoys.user.exception.ServiceException;
import com.haoys.user.manager.AsyncTaskManager;
import com.haoys.user.mapper.ProjectMapper;
import com.haoys.user.model.Organization;
import com.haoys.user.model.Project;
import com.haoys.user.model.ProjectApplyUser;
import com.haoys.user.model.ProjectApplyUserExample;
import com.haoys.user.model.ProjectExample;
import com.haoys.user.model.ProjectUserOrg;
import com.haoys.user.model.ProjectVisitTesteeRecord;
import com.haoys.user.model.ProjectVisitUserExample;
import com.haoys.user.model.SystemMenu;
import com.haoys.user.model.SystemOrgInfo;
import com.haoys.user.model.SystemTenantUser;
import com.haoys.user.model.SystemUserInfo;
import com.haoys.user.service.DictionaryService;
import com.haoys.user.service.FlowPlanService;
import com.haoys.user.service.OrganizationService;
import com.haoys.user.service.ProjectApplyUserService;
import com.haoys.user.service.ProjectBaseManageService;
import com.haoys.user.service.ProjectDictionaryService;
import com.haoys.user.service.ProjectRoleService;
import com.haoys.user.service.ProjectTesteeChallengeService;
import com.haoys.user.service.ProjectTesteeInfoService;
import com.haoys.user.service.ProjectUserService;
import com.haoys.user.service.ProjectVisitConfigService;
import com.haoys.user.service.ProjectVisitUserService;
import com.haoys.user.service.SystemDepartmentService;
import com.haoys.user.service.SystemMenuService;
import com.haoys.user.service.SystemOrgInfoService;
import com.haoys.user.service.SystemTenantUserService;
import com.haoys.user.service.SystemUserInfoService;
import com.haoys.user.service.TemplateConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class ProjectBaseManageServiceImpl extends BaseService implements ProjectBaseManageService {

    private final ProjectMapper projectMapper;
    private final ProjectVisitUserService projectVisitUserService;
    private final ProjectApplyUserService projectApplyUserService;
    private final SystemUserInfoService systemUserInfoService;
    private final OrganizationService organizationService;
    private final DictionaryService dictionaryService;
    private final ProjectUserService projectUserService;
    private final ProjectRoleService projectRoleService;
    private final SystemDepartmentService systemDepartmentService;
    private final ProjectTesteeChallengeService projectTesteeChallengeService;
    private final ProjectTesteeInfoService projectTesteeInfoService;
    private final ProjectVisitConfigService projectVisitConfigService;
    private final ProjectDictionaryService projectDictionaryService;
    private final TemplateConfigService templateConfigService;
    private final FlowPlanService flowPlanService;
    private final SystemOrgInfoService orgInfoService;
    private final SystemMenuService systemMenuService;
    private final SystemTenantUserService systemTenantUserService;
    @Override
    public CustomResult saveProjectBaseInfo(ProjectParam projectParam) {
        CustomResult data = new CustomResult();

        // 验证名称和code是否存在
        ProjectNameAndCodeResult projectNameAndCodeResult = checkProjectNameAndCodeResult(projectParam.getId(), projectParam.getName(), projectParam.getCode());
        if(projectNameAndCodeResult.getProjectNameExist()){
            data.setCode(ResultCode.BUSINESS_SYSTEM_PROJECT_MESSAGE_01.getCode());
            data.setMessage(ResultCode.BUSINESS_SYSTEM_PROJECT_MESSAGE_01.getMessage());
            return data;
        }
        if(projectNameAndCodeResult.getProjectCodeExist()){
            data.setCode(ResultCode.BUSINESS_SYSTEM_PROJECT_MESSAGE_02.getCode());
            data.setMessage(ResultCode.BUSINESS_SYSTEM_PROJECT_MESSAGE_02.getMessage());
            return data;
        }
        String systemTenantId = SecurityUtils.getSystemTenantId();
        String systemPlatformId = SecurityUtils.getSystemPlatformId();
        
        Project project;
        if(projectParam.getId() == null){
            project = new Project();
            BeanUtils.copyProperties(projectParam, project);
            project.setId(SnowflakeIdWorker.getUuid());
            project.setCreateTime(new Date());
            project.setStatus(BusinessConfig.VALID_STATUS);
            project.setProjectSwitch(0);
            project.setTenantId(systemTenantId);
            project.setPlatformId(systemPlatformId);
            projectMapper.insertSelective(project);
            
            // 初始化项目字典
            projectDictionaryService.initProjectDictionary(project.getId());
            // 初始化项目用户
            projectUserService.saveProjectManageUserInfo(project.getId().toString(), projectParam.getCreateUser(), systemTenantId, systemPlatformId);
            // 初始化角色模版
            projectRoleService.initSystemProjectRoleInfo(project.getId().toString(), "0", projectParam.getCreateUser());
            // 初始化参与者基本信息字段
            projectTesteeInfoService.initTesteeFormBaseInfo(project.getId().toString());
            
            if(BusinessConfig.PROJECT_CREATE_METHOD_1.equals(projectParam.getCreateMethod())){
                flowPlanService.initPlan(project.getId());
                data.setData(project.getId().toString());
            }
           
            // 复制引用项目表单
            if(StringUtils.isNotEmpty(projectParam.getReferenceProjectId())){
                boolean copyPlanAndFlow = BusinessConfig.PROJECT_CREATE_METHOD_2.equals(projectParam.getCreateMethod());
                if(!copyPlanAndFlow){
                    flowPlanService.initPlan(project.getId());
                }
            }
            AsyncTaskManager.ownerTask().execute(AsyncTaskProjectTemplateFormConfig.updateProjectTemplateFormAndVariableConfig(project.getId().toString(), projectParam.getReferenceProjectId(), projectParam.getCreateMethod(), projectParam.getCreateUser(), systemTenantId, systemPlatformId), 5000);
            //专病平台需要设置默认研究中心
            if(projectParam.getCreateProjectOrgId()){
                ProjectOrgParam projectOrgParam = new ProjectOrgParam();
                projectOrgParam.setProjectId(project.getId());
                projectOrgParam.setCode(RandomUtil.randomNumbers(8));
                //projectOrgParam.setOrgId(organizationService.getDefaultProjectOrgId(systemTenantId, systemPlatformId));
                projectOrgParam.setOrgId(NumberUtil.parseLong(projectParam.getGroupUnit()));
                projectOrgParam.setCreateUserId(projectParam.getCreateUser());
                CustomResult customResult = organizationService.saveProjectOrgInfo(projectOrgParam);
                if(ResultCode.SUCCESS.getCode() != customResult.getCode()){
                    log.error("sync project orgCode init error {} >>>>>>>>>>>>>>> ", customResult.getMessage());
                }
            }
        }else {
            project = projectMapper.selectByPrimaryKey(projectParam.getId());
            BeanUtils.copyProperties(projectParam, project);
            project.setUpdateTime(new Date());
            project.setUpdateUser(projectParam.getCreateUser());
            projectUserService.saveProjectManageUserInfo(project.getId().toString(), projectParam.getCreateUser(), systemTenantId, systemPlatformId);
            projectMapper.updateByPrimaryKeySelective(project);
            projectTesteeInfoService.initTesteeFormBaseInfo(project.getId().toString());
        }
        return data;
    }

    private ProjectNameAndCodeResult checkProjectNameAndCodeResult(Long projectId, String name, String code) {
        ProjectNameAndCodeResult projectNameAndCodeResult = new ProjectNameAndCodeResult();
        String systemTenantId = SecurityUtils.getSystemTenantId();
        String systemPlatformId = SecurityUtils.getSystemPlatformId();
        Project projectInfo = projectMapper.getProjectBaseInfoByName(name, systemTenantId, systemPlatformId);
        projectNameAndCodeResult.setProjectNameExist(projectInfo != null && !projectInfo.getId().equals(projectId));
        Project projectCodeInfo = projectMapper.getProjectBaseInfoByCode(code, systemTenantId, systemPlatformId);
        projectNameAndCodeResult.setProjectCodeExist(projectCodeInfo != null && !projectCodeInfo.getId().equals(projectId));
        return projectNameAndCodeResult;
    }

    @Override
    //@DS("slave_1")
    public CommonPage<ProjectVo> getPublicProjectListForPage(String databaseId, String projectName, String operator, Integer pageSize, Integer pageNum) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        List<ProjectVo> dataList = new ArrayList<>();
        Map<String,Object> params = new HashMap<>();
        if(StringUtils.isNotBlank(projectName)){
            params.put("projectName", projectName);
        }
        params.put("userId", operator);
        params.put("databaseId", databaseId);
        params.put("tenantId", SecurityUtils.getSystemTenantId());
        params.put("platformId", SecurityUtils.getSystemPlatformId());
        List<ProjectVo> projectList = projectMapper.getPublicProjectListForPage(params);
        for (ProjectVo project : projectList) {
            ProjectVo projectVo = new ProjectVo();
            BeanUtils.copyProperties(project, projectVo);
            if(StringUtils.isNotEmpty(project.getResearchArea())){
                //设置项目研究领域字典
                String researchAreaValue = dictionaryService.getDictNameById(Long.parseLong(project.getResearchArea()));
                projectVo.setResearchAreaValue(researchAreaValue);
            }

            if(StringUtils.isBlank(project.getStatus())){
                projectVo.setStatus(ProjectApplyEnum.PROJECT_APPLY_STATUS_01.getName());
                projectVo.setStatusView(ProjectApplyEnum.PROJECT_APPLY_STATUS_01.getValue());
            }
            if(operator.equals(project.getApplyUserId())){
                String projectApplyValue = ProjectApplyEnum.getProjectApplyValue(project.getStatus());
                projectVo.setStatus(project.getStatus());
                projectVo.setStatusView(projectApplyValue);
            }
            if(operator.equals(project.getCreateUser())){
                projectVo.setStatus(ProjectApplyEnum.PROJECT_APPLY_STATUS_03.getName());
                projectVo.setStatusView(ProjectApplyEnum.PROJECT_APPLY_STATUS_03.getValue());
            }
            dataList.add(projectVo);
        }
        return commonPageListWrapper(pageNum, pageSize, page, dataList);
    }

    /**
     * 我的项目列表
     *
     * @param databaseId
     * @param createUserId           当前登录人
     * @param projectOrgId           项目研究中心id
     * @param projectName            项目名称
     * @param enableRandomizedConfig
     * @param pageSize               每页显示条数
     * @param pageNum                第几页
     * @return List<ProjectVo>
     */
    @Override
    public CommonPage<ProjectVo> getOwnerProjectListForPage(String databaseId, String createUserId, String projectOrgId, String projectName, Boolean enableRandomizedConfig, Integer pageSize, Integer pageNum) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        Map<String,Object> params = new HashMap<>();
        if(StringUtils.isNotBlank(projectName)){
            params.put("projectName", projectName);
        }
        if(StringUtils.isNotBlank(projectOrgId)){
            params.put("projectOrgId", projectOrgId);
        }
        params.put("enableProjectRandomizedConfig", enableRandomizedConfig);
        params.put("userId", createUserId);
        params.put("databaseId", databaseId);
        params.put("tenantId", SecurityUtils.getSystemTenantId());
        params.put("platformId", SecurityUtils.getSystemPlatformId());
        List<ProjectVo> projectList = projectMapper.getOwnerProjectListForPage(params);
        for (ProjectVo projectVo : projectList) {
            ProjectUserVo projectUserInfo = projectUserService.getProjectUserInfo(projectVo.getId().toString(), createUserId);
            boolean projectOwner = projectVo.getCreateUser().equals(createUserId);
            boolean projectUserPa = projectUserInfo == null ? false : projectUserInfo.getPaRole();
            if(projectOwner || projectUserPa){
                // 查询项目所属机构
                List<OrganizationVo> projectOrgList = organizationService.getOrganizationListForCombobox(projectVo.getId().toString(), "", "", "", "");
                projectVo.setOrgList(projectOrgList);
            }else{
                List<OrganizationVo> projectOrgList = new ArrayList<>();
                List<ProjectUserOrgVo> projectUserJoinOrgList = projectUserService.getProjectUserJoinOrgListByUserId(projectVo.getId().toString(), createUserId);
                for (ProjectUserOrgVo projectUserOrgVo : projectUserJoinOrgList) {
                    OrganizationVo organizationVo = new OrganizationVo();
                    organizationVo.setName(projectUserOrgVo.getOrgName());
                    organizationVo.setProjectOrgId(projectUserOrgVo.getProjectOrgId().toString());
                    organizationVo.setId(projectUserOrgVo.getOrgId());
                    organizationVo.setProjectOrgCode(projectUserOrgVo.getCode());
                    projectOrgList.add(organizationVo);
                }
                projectVo.setOrgList(projectOrgList);
            }

            if(StringUtils.isNotEmpty(projectVo.getResearchArea())){
                //设置项目研究领域字典
                String researchAreaValue = dictionaryService.getDictNameById(Long.parseLong(projectVo.getResearchArea()));
                projectVo.setResearchAreaValue(researchAreaValue);
            }

            if(createUserId.equals(projectVo.getCreateUser())){
                projectVo.setShowApplyListButton(true);
            }
            if(ProjectApplyEnum.PROJECT_APPLY_STATUS_03.getName().equals(projectVo.getStatus())){
                projectVo.setStatus(ProjectApplyEnum.PROJECT_APPLY_STATUS_03.getName());
                projectVo.setStatusView(ProjectApplyEnum.PROJECT_APPLY_STATUS_03.getValue());
            }
            // 查询项目配置权限
            projectVo.setShowProjectConfigButton(projectUserPa);
            List<SystemMenu> systemMenuList = systemMenuService.selectProjectUserMenuListByUserId(projectVo.getId(), "", Long.parseLong(createUserId), null, Constants.SYSTEM_MENU_GROUP_NAME_4, projectOwner, false);
            systemMenuList.forEach(systemMenu -> {
                if(projectOwner || systemMenu.getName().equalsIgnoreCase("项目配置")){
                    projectVo.setShowProjectConfigButton(true);
                }
            });
        }
        return commonPageListWrapper(pageNum, pageSize, page, projectList);
    }

    /**
     * 项目申请列表
     * @param projectId
     * @param applyUserName
     * @param projectCreateUser
     * @param orgId
     * @param applyDate
     * @param status
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    public CommonPage<ProjectApplyUserVo> getProjectUserApplyListForPage(String projectId, String applyUserName, String projectCreateUser, String orgId, String applyDate, String status, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        String startDate = null;
        String endDate = null;
        if(StringUtils.isNotBlank(applyDate)){ startDate = applyDate+" 00:00:00";  endDate = applyDate+" 23:59:59"; }
        List<ProjectApplyUserVo> projectApplyUserList = projectApplyUserService.getUserApplyListForPage(projectId, applyUserName, projectCreateUser, orgId, startDate, endDate, status);
        for (ProjectApplyUserVo projectApplyUserVo : projectApplyUserList) {
            if(ProjectStatusEnum.PROJECT_STATUS_01.getName().equals(status)){
                projectApplyUserVo.setStatus(ProjectStatusEnum.PROJECT_STATUS_01.getName());
            }
            if(ProjectStatusEnum.PROJECT_STATUS_02.getName().equals(status)){
                projectApplyUserVo.setStatus(ProjectStatusEnum.PROJECT_STATUS_02.getName());
            }
            if(ProjectStatusEnum.PROJECT_STATUS_03.getName().equals(status)){
                projectApplyUserVo.setStatus(ProjectStatusEnum.PROJECT_STATUS_03.getName());
            }
            SystemUserInfoExtendVo systemUserInfoExtendVo = systemUserInfoService.getSystemUserInfoByUserId(projectApplyUserVo.getUserId());
            if(systemUserInfoExtendVo == null){
                throw new ServiceException(projectApplyUserVo.getUserId() + BusinessConfig.RETURN_MESSAGE_RECORD_NOT_FOUND);
            }
            if(StringUtils.isNotBlank(systemUserInfoExtendVo.getDepartment())){
                SysDepartmentVo department = systemDepartmentService.getDepartment(Long.parseLong(systemUserInfoExtendVo.getDepartment()));
                if(department != null){
                    projectApplyUserVo.setDepartment(department.getName());
                }
            }
            projectApplyUserVo.setPositional(dictionaryService.getDictNameById(Long.parseLong(systemUserInfoExtendVo.getPositional())));
            projectApplyUserVo.setRealName(systemUserInfoExtendVo.getRealName());
            List<ProjectRoleVo> projectRoleListByUserId = projectRoleService.getProjectRoleListByUserId(projectId, projectApplyUserVo.getUserId());
            if(projectRoleListByUserId.size()>0){
                ProjectRoleVo projectRoleVo = projectRoleListByUserId.get(0);
                projectApplyUserVo.setRoleId(Long.parseLong(projectRoleVo.getRoleId()));
                projectApplyUserVo.setRoleName(projectRoleVo.getName());
            }
        }
        return commonPageListWrapper(pageSize, pageNum, page, projectApplyUserList);
    }

    @Override
    public List<Project> getProjectUserInfoList(List<Long> projectIds) {
        ProjectExample example = new ProjectExample();
        ProjectExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(projectIds);
        criteria.andTenantIdEqualTo(SecurityUtils.getSystemTenantId());
        criteria.andPlatformIdEqualTo(SecurityUtils.getSystemPlatformId());
        return projectMapper.selectByExample(example);
    }

    /**
     * 项目审核
     * @param id
     * @param status
     * @param operator
     * @return
     */
    @Override
    public String updateCheckProjectstatus(String id, String status, String operator) {
        SystemUserInfoVo systemUserInfo = systemUserInfoService.getSystemUserInfoByAccountName(operator);
        if(StringUtils.isBlank(status)){ return "参数设置不正确"; }
        ProjectApplyUser projectApplyUser = projectApplyUserService.selectByPrimaryKey(Long.parseLong(id));
        if(projectApplyUser == null){return "找不到对应的申请记录";}
        Project project = projectMapper.selectByPrimaryKey(projectApplyUser.getProjectId());
        if(project == null){ return "申请项目不存在"; }
        if(!project.getCreateUser().equals(systemUserInfo.getId().toString())){
            return "没有权限，需项目负责人进行审核";
        }
        if(ProjectStatusEnum.PROJECT_STATUS_01.getName().equals(status)){
            return "禁止设置此状态，请联系管理员";
        }
        projectApplyUser.setCheckStatus(status);
        if(ProjectStatusEnum.PROJECT_STATUS_02.getName().equals(status)){
            projectApplyUser.setStatus(ProjectApplyEnum.PROJECT_APPLY_STATUS_03.getName());
        }
        if(ProjectStatusEnum.PROJECT_STATUS_03.getName().equals(status)){
            projectApplyUser.setStatus(ProjectApplyEnum.PROJECT_APPLY_STATUS_04.getName());
        }
        if(ProjectStatusEnum.PROJECT_STATUS_04.getName().equals(status)){
            projectApplyUser.setStatus(ProjectApplyEnum.PROJECT_APPLY_STATUS_05.getName());
        }
        projectApplyUser.setCheckTime(new Date());
        projectApplyUser.setCheckUserId(systemUserInfo.getId().toString());
        projectApplyUserService.updateByPrimaryKeySelective(projectApplyUser);
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }


    @Override
    public String saveProjectUser(String projectId, String orgId, String userId) {
        return saveJoinProject( projectId, orgId, userId, true, false);
    }

    @Override
    public String saveProjectUserLeader(String projectId, String orgId, String userId) {
        return saveJoinProject( projectId, orgId, userId, true, true);
    }

    @Override
    public String saveApplyProjectUser(String projectId, String orgId, String userId) {
        return saveJoinProject( projectId, orgId, userId, false, false);
    }

    /**
     * 在项目中添加设置成员
     * @param projectId         项目id
     * @param orgId             中心id
     * @param userId            用户id
     * @param projectAddFlag    是否为添加成员
     * @param projectLeader     是否设置项目负责人
     * @return
     */
    private String saveJoinProject(String projectId, String orgId, String userId, boolean projectAddFlag, boolean projectLeader) {
        //项目负责人无需申请
        Project project = projectMapper.selectByPrimaryKey(Long.parseLong(projectId));
        if(project == null){
            return BusinessConfig.RETURN_MESSAGE_RECORD_NOT_FOUND;
        }

        if(StringUtils.isBlank(orgId)){
            OrganizationVo userPrimaryOrgInfo = organizationService.getSystemUserOrgInfo(userId);
            if(userPrimaryOrgInfo == null){
                return BusinessConfig.USER_ORG_RECORD_NOT_FOUND;
            }
            orgId = userPrimaryOrgInfo.getId().toString();
        }
        //判断是否申请过当前项目 如果不存在申请记录，新增未审核申请记录
        ProjectApplyUserExample example = new ProjectApplyUserExample();
        ProjectApplyUserExample.Criteria criteria = example.createCriteria();

        criteria.andProjectIdEqualTo(Long.parseLong(projectId)).andUserIdEqualTo(userId).andOrgIdEqualTo(Long.parseLong(orgId));
        List<ProjectApplyUser> projectApplyUsers = projectApplyUserService.selectByExample(example);
        if(projectApplyUsers != null && projectApplyUsers.size() >0){
            ProjectApplyUser projectApplyUser = projectApplyUsers.get(0);
            if(ProjectApplyEnum.PROJECT_APPLY_STATUS_01.getName().equals(projectApplyUser.getStatus())){
                return ProjectApplyEnum.PROJECT_APPLY_STATUS_01.getValue();
            }
            if(ProjectApplyEnum.PROJECT_APPLY_STATUS_02.getName().equals(projectApplyUser.getStatus())){
                return ProjectApplyEnum.PROJECT_APPLY_STATUS_02.getValue();
            }
            if(ProjectApplyEnum.PROJECT_APPLY_STATUS_03.getName().equals(projectApplyUser.getStatus())){
                return ProjectApplyEnum.PROJECT_APPLY_STATUS_03.getValue();
            }
            if(ProjectApplyEnum.PROJECT_APPLY_STATUS_04.getName().equals(projectApplyUser.getStatus())){
                return ProjectApplyEnum.PROJECT_APPLY_STATUS_04.getValue();
            }
            if(ProjectApplyEnum.PROJECT_APPLY_STATUS_05.getName().equals(projectApplyUser.getStatus())){
                return ProjectApplyEnum.PROJECT_APPLY_STATUS_05.getValue();
            }
        }
        ProjectApplyUser projectApplyUser = new ProjectApplyUser();
        projectApplyUser.setId(SnowflakeIdWorker.getUuid());
        projectApplyUser.setProjectId(Long.parseLong(projectId));
        projectApplyUser.setUserId(userId);
        projectApplyUser.setApplyTime(new Date());
        //如果不设置 则以主中心机构申请加入
        if(StringUtils.isNotBlank(orgId)){
            Organization organization = organizationService.getSystemOrganizationInfo(orgId);
            projectApplyUser.setOrgId(Long.parseLong(orgId));
            projectApplyUser.setOrgName(organization.getName());
            ProjectUserOrg projectUserOrgInfo = organizationService.getProjectUserOrgInfo(projectId, userId, orgId);
            if(projectUserOrgInfo == null){
                organizationService.saveProjectUserOrgInfo(projectId, userId, orgId, "");
            }
        }else{
            SystemUserInfoExtendVo adminExtendVo = systemUserInfoService.getSystemUserInfoByUserId(userId);
            List<OrganizationVo> organizationVoList = adminExtendVo.getUserOrganizationInfo();
            for (OrganizationVo organizationVo : organizationVoList) {
                projectApplyUser.setOrgId(organizationVo.getId());
                projectApplyUser.setOrgName(organizationVo.getName());
            }
        }
        projectApplyUser.setStatus(ProjectApplyEnum.PROJECT_APPLY_STATUS_02.getName());
        projectApplyUser.setCheckStatus(ProjectStatusEnum.PROJECT_STATUS_01.getName());
        projectApplyUser.setCreateTime(new Date());
        projectApplyUser.setProjectLeader(false);
        if(projectAddFlag){
            projectApplyUser.setStatus(ProjectApplyEnum.PROJECT_APPLY_STATUS_03.getName());
            projectApplyUser.setCheckStatus(ProjectStatusEnum.PROJECT_STATUS_02.getName());
            projectApplyUser.setCheckTime(new Date());
        }
        if(projectLeader){
            projectApplyUser.setProjectLeader(true);
        }
        projectApplyUserService.insert(projectApplyUser);
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    @Override
    public String deleteProjectUser(String projectId, String userId, String orgId){
        //验证参与者是否关联所属主管医生  项目申请记录
        ProjectApplyUserExample example = new ProjectApplyUserExample();
        ProjectApplyUserExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andUserIdEqualTo(userId);
        List<ProjectApplyUser> projectApplyUserList = projectApplyUserService.selectByExample(example);
        if(CollectionUtil.isNotEmpty(projectApplyUserList)){
            throw new ServiceException(ResultCode.PROJECT_APPLY_USER_EXIST);
        }
        ProjectUserOrg projectUserOrgInfo = organizationService.getProjectUserOrgInfo(projectId, userId, orgId);
        if(projectUserOrgInfo != null){
            organizationService.deleteProjectUserOrgInfo(projectId, userId, orgId);
        }
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    @Override
    public CustomResult deleteProjectBaseInfoAndProjectAuth(String projectId, String userId) {
        return null;
    }


    @Override
    public Project getProjectBaseInfo(String projectId) {
        return projectMapper.selectByPrimaryKey(Long.parseLong(projectId));
    }

    @Override
    public CustomResult deleteProjectBaseInfo(String projectId, String userId) {
        CustomResult data = new CustomResult();
        Project projectBaseInfo = getProjectBaseInfo(projectId);
        if(projectBaseInfo == null){
            data.setMessage("项目记录不存在");
            return data;
        }
        // 录入表单后禁止删除
        ProjectVisitUserExample example = new ProjectVisitUserExample();
        ProjectVisitUserExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        long count = projectVisitUserService.countByExample(example);
        if(count >0){
            data.setMessage(ResultCode.PROJECT_MANAGE_DEL.getMessage());
            return data;
        }
        // set delete flag
        projectBaseInfo.setCode(projectBaseInfo.getCode().concat(RandomStringUtils.randomAlphabetic(4)));
        projectBaseInfo.setUpdateUser(userId);
        projectBaseInfo.setUpdateTime(new Date());
        projectBaseInfo.setStatus(BusinessConfig.NO_VALID_STATUS);
        projectMapper.updateByPrimaryKeySelective(projectBaseInfo);
        data.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return data;
    }

    @Override
    public CommonPage<ProjectVo> getProjectListForPage(String projectName, String researchArea, Boolean ifPublic, String operator, Integer pageSize, Integer pageNum) {
        SystemUserInfo systemUserInfo = systemUserInfoService.selectByPrimaryKey(Long.valueOf(operator));
        SystemTenantUser systemTenantUser = systemTenantUserService.getSystemTenantUserByUserId(systemUserInfo.getId().toString());
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        Map<String,Object> params = new HashMap<>();
        params.put("tenantId", SecurityUtils.getSystemTenantId());
        params.put("platformId", SecurityUtils.getSystemPlatformId());
        if(StringUtils.isNotBlank(projectName)){
            params.put("name", projectName);
        }
        if(systemTenantUser == null || systemTenantUser.getCompanyOwnerUser() == null || !systemTenantUser.getCompanyOwnerUser()){
            params.put("userId", operator);
        }
        if(StringUtils.isNotBlank(researchArea)){
            params.put("researchArea", researchArea);
        }
        if(ifPublic != null && !"".equals(ifPublic)){
            params.put("ifPublic", ifPublic);
        }
        List<ProjectVo> projectList = projectMapper.getProjectManageListForPage(params);
        return commonPageListWrapper(pageNum, pageSize, page, projectList);
    }
    
    @Override
    public List<ProjectVo> getProjectList(String projectName, String researchArea, Boolean ifPublic) {
        Map<String,Object> params = new HashMap<>();
        params.put("projectName", projectName);
        params.put("researchArea", researchArea);
        params.put("ifPublic", ifPublic);
        params.put("tenantId", SecurityUtils.getSystemTenantId());
        params.put("platformId", SecurityUtils.getSystemPlatformId());
        return projectMapper.getProjectList(params);
    }

    @Override
    public ProjectOverViewVo getProjectOverViewData(String projectId, String userId) {
        ProjectOverViewVo projectOverViewVo = new ProjectOverViewVo();
        //查询质疑总量
        List<String> dataList = new ArrayList<>();
        String orgIds = null;
        List<OrganizationVo> projectUserOrgList = organizationService.getProjectUserOrgList(projectId, userId);
        if(CollectionUtil.isNotEmpty(projectUserOrgList)){
            for (OrganizationVo organizationVo : projectUserOrgList) {
                dataList.add(organizationVo.getId().toString());
            }
            String orgIdValue = dataList.stream().collect(Collectors.joining(","));
            orgIds = getQueryWrapperParams(orgIdValue);
        }
        //项目负责人PA  数据经理DM 查询全部机构
        ProjectUserInfoWrapper projectUserVo = projectUserService.getProjectUserRoleInfoByUserId(projectId, "", userId);
        if(projectUserVo != null){
            /*String ename = projectUserVo.getRoleCode();
            if(ProjectRoleEnum.PROJECT_PA.getCode().equals(ename) *//*|| "DM".equals(ename)*//*){
                List<String> organizationValue = organizationService.getProjectUserOrgListByProjectId(projectId);
                String orgIdValue = organizationValue.stream().collect(Collectors.joining(","));
                orgIds = getQueryWrapperParams(orgIdValue);
            }*/
        }
        //质疑总量
        int changleCount = projectTesteeChallengeService.getProjectChallengeUnclosedCount(projectId, null, null, orgIds);
        projectOverViewVo.setVisitChangleCount(changleCount);

        //新增参与者总量
        int count  = projectTesteeInfoService.getProjectTesteeCount(projectId, userId, orgIds, false);
        projectOverViewVo.setTesteeCount(count);

        Set<Long> followRealTimecount = new HashSet<>();
        //实际录入的访视总量
        List<ProjectVisitTesteeRecord> followRealTimeList = projectVisitConfigService.getProjectVisitFollowRealTimeCount(projectId, orgIds);
        /*for (ProjectVisitTesteeRecord projectVisitTesteeRecord : followRealTimeList) {
            followRealTimecount.add(projectVisitTesteeRecord.getTesteeId());
        }
        List<ProjectVisitTesteeRecord> overdueVisitList = projectVisitConfigService.getProjectOverdueVisitFollowRealTimeNotNullCount(projectId, orgIds, "nextFollowRealNotNullValue");
        for (ProjectVisitTesteeRecord projectVisitTesteeRecord : overdueVisitList) {
            followRealTimecount.add(projectVisitTesteeRecord.getTesteeId());
        }*/
        projectOverViewVo.setRealVisitCount(followRealTimeList.size());

        //计划访视总量
        int plannedVisitCount = projectVisitConfigService.getProjectPlannedVisitCount(projectId, orgIds);
        projectOverViewVo.setPlannedCount(plannedVisitCount);

        //超窗访视记录
        int overdueVisitCount = projectVisitConfigService.getProjectOverdueVisitFollowRealTimeNullCount(projectId, orgIds, "nextFollowRealNullValue");
        projectOverViewVo.setOverdueCount(overdueVisitCount);
        return projectOverViewVo;
    }

    @Override
    public ProjectVo getProjectViewInfo(String projectId) {
        ProjectVo projectVo = new ProjectVo();
        Project projectBaseInfo = this.getProjectBaseInfo(projectId);
        if(projectBaseInfo != null){
            BeanUtils.copyProperties(projectBaseInfo, projectVo);
            // 设置组长单位
            if (StringUtils.isNotBlank(projectVo.getGroupUnit())){
                SystemOrgInfo orgInfoQuery = orgInfoService.selectSystemOrgInfoByOrgId(projectVo.getGroupUnit());
                if (orgInfoQuery!=null){
                    projectVo.setGroupUnitName(orgInfoQuery.getOrgName());
                }
            }
            // 设置申办单为的名称
            if (StringUtils.isNotBlank(projectVo.getNitiator())){
                SystemOrgInfo orgInfoQuery = orgInfoService.selectSystemOrgInfoByOrgId(projectVo.getNitiator());
                if (orgInfoQuery!=null){
                    projectVo.setNitiatorName(orgInfoQuery.getOrgName());
                }
            }
        }
        return projectVo;
    }

    @Override
    public CustomResult modifyIfPublicByProjectId(Long id){
        CustomResult data = new CustomResult();
        Project project = projectMapper.selectByPrimaryKey(id);
        if(project.getIfPublic()){
            // 录入表单后禁止删除
            ProjectVisitUserExample example = new ProjectVisitUserExample();
            ProjectVisitUserExample.Criteria criteria = example.createCriteria();
            criteria.andProjectIdEqualTo(id);
            long count = projectVisitUserService.countByExample(example);
            if(count >0){
                data.setMessage(ResultCode.PROJECT_MANAGE_STOP.getMessage());
                return data;
            }else {
                project.setIfPublic(!project.getIfPublic());
                data.setMessage(ResultCode.PROJECT_MANAGE_IFPUBLIC_1.getMessage());

            }
        }else {
            project.setIfPublic(!project.getIfPublic());
            data.setMessage(ResultCode.PROJECT_MANAGE_IFPUBLIC_0.getMessage());

        }
        projectMapper.updateByPrimaryKeySelective(project);
//        data.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);

        return data;
    }

    @Override
    public List<ProjectVo> getOwnerProjectListForH5(String userId) {
        List<ProjectVo> ownerProjectList = projectMapper.getOwnerProjectListForH5(userId);
        ownerProjectList.forEach(projectVo -> {
            List<ProjectTesteeOrgVo> projectOrgList = projectTesteeInfoService.getTesteeJoinOrgListByUserId(projectVo.getId().toString(), userId);
            projectVo.setJoinOrgList(projectOrgList);
        });
        return ownerProjectList;
    }
    
    @Override
    public CustomResult updateProjectRandomConfig(String projectId, Boolean ifRandom) {
        CustomResult data = new CustomResult();
        Project project = projectMapper.selectByPrimaryKey(Long.parseLong(projectId));
        project.setEnableRandomizedConfig(ifRandom);
        project.setUpdateTime(new Date());
        project.setUpdateUser(SecurityUtils.getUserIdValue());
        projectMapper.updateByPrimaryKeySelective(project);
        return data;
    }
    
    @Override
    public Project getEnableProjectBaseInfo(String loginTenantId, String loginPlatformId) {
        return projectMapper.getEnableProjectBaseInfo(loginTenantId, loginPlatformId);
    }
    
}
