package com.haoys.user.domain.param.project;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
public class ProjectTesteeCustomTableParam implements Serializable {

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @ApiModelProperty(value = "表单项id")
    private Long formId;
    
    @ApiModelProperty(value = "表单扩展id")
    private Long formExpandId;

    @ApiModelProperty(value = "参与者id")
    private Long testeeId;

    @ApiModelProperty(value = "表格变量id")
    private Long formDetailId;

    @ApiModelProperty(value = "字段组模版表格id")
    private Long resourceVariableId;

    @ApiModelProperty(value = "字段组分组id")
    private Long groupId;

    @ApiModelProperty(value = "是否OCR识别")
    private boolean ocrRecord = false;

    @ApiModelProperty(value = "多行记录-设置初始化数据")
    private List<List<ProjectTesteeTableParam.TableRowData>> rowList = new LinkedList<>();

    @ApiModelProperty(value = "是否患者端提交")
    private boolean patientSubmit = false;

    @ApiModelProperty(value = "患者端任务录入完成情况 1-待录入、2-已录入、3-已完成")
    private String complateStatus;

    @ApiModelProperty(value = "表格完成状态1-未录入 2-录入中 3-已完成")
    private String tableComplateStatus;

    @ApiModelProperty(value = "操作人id")
    private String createUserId;

}
