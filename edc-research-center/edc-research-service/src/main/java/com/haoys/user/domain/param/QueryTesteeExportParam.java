package com.haoys.user.domain.param;

import lombok.Data;

import java.io.Serializable;

@Data
public class QueryTesteeExportParam implements Serializable {

    private Boolean fieldGroup;
    private String operatorValue;
    private String visitId = "";
    private String formId = "";
    private String conditionName = "";
    // 变量所属类型
    private String type = "";
    // 变量组件类型 input checkbox 。。。。。
    private String varType = "";
    // 普通变量、表格id
    private String formDetailId = "";
    private String formTableId = "";
    private String groupId = "";
    private String relation = "";
    private String fieldName = "";
    private String inputValue = "";
}
