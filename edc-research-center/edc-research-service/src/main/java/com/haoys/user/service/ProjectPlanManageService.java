package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.param.project.ProjectPatientPlanParam;
import com.haoys.user.domain.vo.project.ProjectPlanVo;
import com.haoys.user.model.ProjectPatientPlan;
import com.haoys.user.model.ProjectPatientPlanDetail;

import java.util.List;

public interface ProjectPlanManageService {


    CommonPage<ProjectPlanVo> getProjectPlanListForPage(String projectId, String name, String userId, Integer pageSize, Integer pageNum);

    CustomResult saveProjectPlan(ProjectPatientPlanParam projectPatientPlanParam);

    ProjectPlanVo getProjectPlanInfo(String planId);

    CustomResult updateProjectPlanStatus(String planId, String status, String userId);

    ProjectPlanVo getRecentProjectPlan(String projectId);

    /**
     * 根据任务查询方案列表
     * @param taskId
     * @return
     */
    List<ProjectPatientPlanDetail> getProjectPlanListByCondition(String taskId);

    List<ProjectPatientPlan> getProjectPlanListByProjectId(Long projectId);

    List<ProjectPlanVo> getProjectPlanConditionList(String projectId);
}
