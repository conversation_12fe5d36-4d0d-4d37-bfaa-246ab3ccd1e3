package com.haoys.user.domain.vo.patient;

import com.haoys.user.domain.vo.project.ProjectPlanVo;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class PatientTaskFormDetailVo implements Serializable {

    private String projectId;

    private String planId;

    private String taskId;

    private String testeeId;

    private ProjectPlanVo.ProjectPatientPlanVariableVo projectPatientPlanVariableVo = new ProjectPlanVo.ProjectPatientPlanVariableVo();

    private List<PatientTaskFormVariableVo> dataList = new ArrayList<>();


}
