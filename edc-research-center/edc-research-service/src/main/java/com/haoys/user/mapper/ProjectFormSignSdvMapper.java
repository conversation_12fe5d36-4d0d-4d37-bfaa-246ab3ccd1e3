package com.haoys.user.mapper;

import com.haoys.user.model.ProjectFormSignSdv;
import com.haoys.user.model.ProjectFormSignSdvExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectFormSignSdvMapper {
    long countByExample(ProjectFormSignSdvExample example);

    int deleteByExample(ProjectFormSignSdvExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectFormSignSdv record);

    int insertSelective(ProjectFormSignSdv record);

    List<ProjectFormSignSdv> selectByExample(ProjectFormSignSdvExample example);

    ProjectFormSignSdv selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectFormSignSdv record, @Param("example") ProjectFormSignSdvExample example);

    int updateByExample(@Param("record") ProjectFormSignSdv record, @Param("example") ProjectFormSignSdvExample example);

    int updateByPrimaryKeySelective(ProjectFormSignSdv record);

    int updateByPrimaryKey(ProjectFormSignSdv record);
}