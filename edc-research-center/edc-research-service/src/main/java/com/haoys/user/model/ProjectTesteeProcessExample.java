package com.haoys.user.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProjectTesteeProcessExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectTesteeProcessExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andPlanIdIsNull() {
            addCriterion("plan_id is null");
            return (Criteria) this;
        }

        public Criteria andPlanIdIsNotNull() {
            addCriterion("plan_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlanIdEqualTo(Long value) {
            addCriterion("plan_id =", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotEqualTo(Long value) {
            addCriterion("plan_id <>", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdGreaterThan(Long value) {
            addCriterion("plan_id >", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdGreaterThanOrEqualTo(Long value) {
            addCriterion("plan_id >=", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdLessThan(Long value) {
            addCriterion("plan_id <", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdLessThanOrEqualTo(Long value) {
            addCriterion("plan_id <=", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdIn(List<Long> values) {
            addCriterion("plan_id in", values, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotIn(List<Long> values) {
            addCriterion("plan_id not in", values, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdBetween(Long value1, Long value2) {
            addCriterion("plan_id between", value1, value2, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotBetween(Long value1, Long value2) {
            addCriterion("plan_id not between", value1, value2, "planId");
            return (Criteria) this;
        }

        public Criteria andVisitIdIsNull() {
            addCriterion("visit_id is null");
            return (Criteria) this;
        }

        public Criteria andVisitIdIsNotNull() {
            addCriterion("visit_id is not null");
            return (Criteria) this;
        }

        public Criteria andVisitIdEqualTo(Long value) {
            addCriterion("visit_id =", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdNotEqualTo(Long value) {
            addCriterion("visit_id <>", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdGreaterThan(Long value) {
            addCriterion("visit_id >", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("visit_id >=", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdLessThan(Long value) {
            addCriterion("visit_id <", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdLessThanOrEqualTo(Long value) {
            addCriterion("visit_id <=", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdIn(List<Long> values) {
            addCriterion("visit_id in", values, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdNotIn(List<Long> values) {
            addCriterion("visit_id not in", values, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdBetween(Long value1, Long value2) {
            addCriterion("visit_id between", value1, value2, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdNotBetween(Long value1, Long value2) {
            addCriterion("visit_id not between", value1, value2, "visitId");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNull() {
            addCriterion("form_id is null");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNotNull() {
            addCriterion("form_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormIdEqualTo(Long value) {
            addCriterion("form_id =", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotEqualTo(Long value) {
            addCriterion("form_id <>", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThan(Long value) {
            addCriterion("form_id >", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_id >=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThan(Long value) {
            addCriterion("form_id <", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThanOrEqualTo(Long value) {
            addCriterion("form_id <=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdIn(List<Long> values) {
            addCriterion("form_id in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotIn(List<Long> values) {
            addCriterion("form_id not in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdBetween(Long value1, Long value2) {
            addCriterion("form_id between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotBetween(Long value1, Long value2) {
            addCriterion("form_id not between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdIsNull() {
            addCriterion("form_expand_id is null");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdIsNotNull() {
            addCriterion("form_expand_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdEqualTo(Long value) {
            addCriterion("form_expand_id =", value, "formExpandId");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdNotEqualTo(Long value) {
            addCriterion("form_expand_id <>", value, "formExpandId");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdGreaterThan(Long value) {
            addCriterion("form_expand_id >", value, "formExpandId");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_expand_id >=", value, "formExpandId");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdLessThan(Long value) {
            addCriterion("form_expand_id <", value, "formExpandId");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdLessThanOrEqualTo(Long value) {
            addCriterion("form_expand_id <=", value, "formExpandId");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdIn(List<Long> values) {
            addCriterion("form_expand_id in", values, "formExpandId");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdNotIn(List<Long> values) {
            addCriterion("form_expand_id not in", values, "formExpandId");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdBetween(Long value1, Long value2) {
            addCriterion("form_expand_id between", value1, value2, "formExpandId");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdNotBetween(Long value1, Long value2) {
            addCriterion("form_expand_id not between", value1, value2, "formExpandId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdIsNull() {
            addCriterion("testee_id is null");
            return (Criteria) this;
        }

        public Criteria andTesteeIdIsNotNull() {
            addCriterion("testee_id is not null");
            return (Criteria) this;
        }

        public Criteria andTesteeIdEqualTo(Long value) {
            addCriterion("testee_id =", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdNotEqualTo(Long value) {
            addCriterion("testee_id <>", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdGreaterThan(Long value) {
            addCriterion("testee_id >", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("testee_id >=", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdLessThan(Long value) {
            addCriterion("testee_id <", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdLessThanOrEqualTo(Long value) {
            addCriterion("testee_id <=", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdIn(List<Long> values) {
            addCriterion("testee_id in", values, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdNotIn(List<Long> values) {
            addCriterion("testee_id not in", values, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdBetween(Long value1, Long value2) {
            addCriterion("testee_id between", value1, value2, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdNotBetween(Long value1, Long value2) {
            addCriterion("testee_id not between", value1, value2, "testeeId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdIsNull() {
            addCriterion("owner_org_id is null");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdIsNotNull() {
            addCriterion("owner_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdEqualTo(String value) {
            addCriterion("owner_org_id =", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdNotEqualTo(String value) {
            addCriterion("owner_org_id <>", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdGreaterThan(String value) {
            addCriterion("owner_org_id >", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdGreaterThanOrEqualTo(String value) {
            addCriterion("owner_org_id >=", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdLessThan(String value) {
            addCriterion("owner_org_id <", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdLessThanOrEqualTo(String value) {
            addCriterion("owner_org_id <=", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdLike(String value) {
            addCriterion("owner_org_id like", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdNotLike(String value) {
            addCriterion("owner_org_id not like", value, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdIn(List<String> values) {
            addCriterion("owner_org_id in", values, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdNotIn(List<String> values) {
            addCriterion("owner_org_id not in", values, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdBetween(String value1, String value2) {
            addCriterion("owner_org_id between", value1, value2, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andOwnerOrgIdNotBetween(String value1, String value2) {
            addCriterion("owner_org_id not between", value1, value2, "ownerOrgId");
            return (Criteria) this;
        }

        public Criteria andComplateStatusIsNull() {
            addCriterion("complate_status is null");
            return (Criteria) this;
        }

        public Criteria andComplateStatusIsNotNull() {
            addCriterion("complate_status is not null");
            return (Criteria) this;
        }

        public Criteria andComplateStatusEqualTo(String value) {
            addCriterion("complate_status =", value, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andComplateStatusNotEqualTo(String value) {
            addCriterion("complate_status <>", value, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andComplateStatusGreaterThan(String value) {
            addCriterion("complate_status >", value, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andComplateStatusGreaterThanOrEqualTo(String value) {
            addCriterion("complate_status >=", value, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andComplateStatusLessThan(String value) {
            addCriterion("complate_status <", value, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andComplateStatusLessThanOrEqualTo(String value) {
            addCriterion("complate_status <=", value, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andComplateStatusLike(String value) {
            addCriterion("complate_status like", value, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andComplateStatusNotLike(String value) {
            addCriterion("complate_status not like", value, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andComplateStatusIn(List<String> values) {
            addCriterion("complate_status in", values, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andComplateStatusNotIn(List<String> values) {
            addCriterion("complate_status not in", values, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andComplateStatusBetween(String value1, String value2) {
            addCriterion("complate_status between", value1, value2, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andComplateStatusNotBetween(String value1, String value2) {
            addCriterion("complate_status not between", value1, value2, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andChangleCountIsNull() {
            addCriterion("changle_count is null");
            return (Criteria) this;
        }

        public Criteria andChangleCountIsNotNull() {
            addCriterion("changle_count is not null");
            return (Criteria) this;
        }

        public Criteria andChangleCountEqualTo(Integer value) {
            addCriterion("changle_count =", value, "changleCount");
            return (Criteria) this;
        }

        public Criteria andChangleCountNotEqualTo(Integer value) {
            addCriterion("changle_count <>", value, "changleCount");
            return (Criteria) this;
        }

        public Criteria andChangleCountGreaterThan(Integer value) {
            addCriterion("changle_count >", value, "changleCount");
            return (Criteria) this;
        }

        public Criteria andChangleCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("changle_count >=", value, "changleCount");
            return (Criteria) this;
        }

        public Criteria andChangleCountLessThan(Integer value) {
            addCriterion("changle_count <", value, "changleCount");
            return (Criteria) this;
        }

        public Criteria andChangleCountLessThanOrEqualTo(Integer value) {
            addCriterion("changle_count <=", value, "changleCount");
            return (Criteria) this;
        }

        public Criteria andChangleCountIn(List<Integer> values) {
            addCriterion("changle_count in", values, "changleCount");
            return (Criteria) this;
        }

        public Criteria andChangleCountNotIn(List<Integer> values) {
            addCriterion("changle_count not in", values, "changleCount");
            return (Criteria) this;
        }

        public Criteria andChangleCountBetween(Integer value1, Integer value2) {
            addCriterion("changle_count between", value1, value2, "changleCount");
            return (Criteria) this;
        }

        public Criteria andChangleCountNotBetween(Integer value1, Integer value2) {
            addCriterion("changle_count not between", value1, value2, "changleCount");
            return (Criteria) this;
        }

        public Criteria andFormComplateStatusIsNull() {
            addCriterion("form_complate_status is null");
            return (Criteria) this;
        }

        public Criteria andFormComplateStatusIsNotNull() {
            addCriterion("form_complate_status is not null");
            return (Criteria) this;
        }

        public Criteria andFormComplateStatusEqualTo(String value) {
            addCriterion("form_complate_status =", value, "formComplateStatus");
            return (Criteria) this;
        }

        public Criteria andFormComplateStatusNotEqualTo(String value) {
            addCriterion("form_complate_status <>", value, "formComplateStatus");
            return (Criteria) this;
        }

        public Criteria andFormComplateStatusGreaterThan(String value) {
            addCriterion("form_complate_status >", value, "formComplateStatus");
            return (Criteria) this;
        }

        public Criteria andFormComplateStatusGreaterThanOrEqualTo(String value) {
            addCriterion("form_complate_status >=", value, "formComplateStatus");
            return (Criteria) this;
        }

        public Criteria andFormComplateStatusLessThan(String value) {
            addCriterion("form_complate_status <", value, "formComplateStatus");
            return (Criteria) this;
        }

        public Criteria andFormComplateStatusLessThanOrEqualTo(String value) {
            addCriterion("form_complate_status <=", value, "formComplateStatus");
            return (Criteria) this;
        }

        public Criteria andFormComplateStatusLike(String value) {
            addCriterion("form_complate_status like", value, "formComplateStatus");
            return (Criteria) this;
        }

        public Criteria andFormComplateStatusNotLike(String value) {
            addCriterion("form_complate_status not like", value, "formComplateStatus");
            return (Criteria) this;
        }

        public Criteria andFormComplateStatusIn(List<String> values) {
            addCriterion("form_complate_status in", values, "formComplateStatus");
            return (Criteria) this;
        }

        public Criteria andFormComplateStatusNotIn(List<String> values) {
            addCriterion("form_complate_status not in", values, "formComplateStatus");
            return (Criteria) this;
        }

        public Criteria andFormComplateStatusBetween(String value1, String value2) {
            addCriterion("form_complate_status between", value1, value2, "formComplateStatus");
            return (Criteria) this;
        }

        public Criteria andFormComplateStatusNotBetween(String value1, String value2) {
            addCriterion("form_complate_status not between", value1, value2, "formComplateStatus");
            return (Criteria) this;
        }

        public Criteria andTableComplateStatusIsNull() {
            addCriterion("table_complate_status is null");
            return (Criteria) this;
        }

        public Criteria andTableComplateStatusIsNotNull() {
            addCriterion("table_complate_status is not null");
            return (Criteria) this;
        }

        public Criteria andTableComplateStatusEqualTo(String value) {
            addCriterion("table_complate_status =", value, "tableComplateStatus");
            return (Criteria) this;
        }

        public Criteria andTableComplateStatusNotEqualTo(String value) {
            addCriterion("table_complate_status <>", value, "tableComplateStatus");
            return (Criteria) this;
        }

        public Criteria andTableComplateStatusGreaterThan(String value) {
            addCriterion("table_complate_status >", value, "tableComplateStatus");
            return (Criteria) this;
        }

        public Criteria andTableComplateStatusGreaterThanOrEqualTo(String value) {
            addCriterion("table_complate_status >=", value, "tableComplateStatus");
            return (Criteria) this;
        }

        public Criteria andTableComplateStatusLessThan(String value) {
            addCriterion("table_complate_status <", value, "tableComplateStatus");
            return (Criteria) this;
        }

        public Criteria andTableComplateStatusLessThanOrEqualTo(String value) {
            addCriterion("table_complate_status <=", value, "tableComplateStatus");
            return (Criteria) this;
        }

        public Criteria andTableComplateStatusLike(String value) {
            addCriterion("table_complate_status like", value, "tableComplateStatus");
            return (Criteria) this;
        }

        public Criteria andTableComplateStatusNotLike(String value) {
            addCriterion("table_complate_status not like", value, "tableComplateStatus");
            return (Criteria) this;
        }

        public Criteria andTableComplateStatusIn(List<String> values) {
            addCriterion("table_complate_status in", values, "tableComplateStatus");
            return (Criteria) this;
        }

        public Criteria andTableComplateStatusNotIn(List<String> values) {
            addCriterion("table_complate_status not in", values, "tableComplateStatus");
            return (Criteria) this;
        }

        public Criteria andTableComplateStatusBetween(String value1, String value2) {
            addCriterion("table_complate_status between", value1, value2, "tableComplateStatus");
            return (Criteria) this;
        }

        public Criteria andTableComplateStatusNotBetween(String value1, String value2) {
            addCriterion("table_complate_status not between", value1, value2, "tableComplateStatus");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(String value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(String value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(String value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(String value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLike(String value) {
            addCriterion("create_user_id like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotLike(String value) {
            addCriterion("create_user_id not like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<String> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<String> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(String value1, String value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(String value1, String value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNull() {
            addCriterion("update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNotNull() {
            addCriterion("update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdEqualTo(String value) {
            addCriterion("update_user_id =", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotEqualTo(String value) {
            addCriterion("update_user_id <>", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThan(String value) {
            addCriterion("update_user_id >", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("update_user_id >=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThan(String value) {
            addCriterion("update_user_id <", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThanOrEqualTo(String value) {
            addCriterion("update_user_id <=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLike(String value) {
            addCriterion("update_user_id like", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotLike(String value) {
            addCriterion("update_user_id not like", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIn(List<String> values) {
            addCriterion("update_user_id in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotIn(List<String> values) {
            addCriterion("update_user_id not in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdBetween(String value1, String value2) {
            addCriterion("update_user_id between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotBetween(String value1, String value2) {
            addCriterion("update_user_id not between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNull() {
            addCriterion("platform_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNotNull() {
            addCriterion("platform_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdEqualTo(String value) {
            addCriterion("platform_id =", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotEqualTo(String value) {
            addCriterion("platform_id <>", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThan(String value) {
            addCriterion("platform_id >", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThanOrEqualTo(String value) {
            addCriterion("platform_id >=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThan(String value) {
            addCriterion("platform_id <", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThanOrEqualTo(String value) {
            addCriterion("platform_id <=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLike(String value) {
            addCriterion("platform_id like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotLike(String value) {
            addCriterion("platform_id not like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIn(List<String> values) {
            addCriterion("platform_id in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotIn(List<String> values) {
            addCriterion("platform_id not in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdBetween(String value1, String value2) {
            addCriterion("platform_id between", value1, value2, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotBetween(String value1, String value2) {
            addCriterion("platform_id not between", value1, value2, "platformId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}