package com.haoys.user.domain.vo.system;

import com.haoys.user.model.SystemExceptionLog;
import io.swagger.annotations.ApiModelProperty;

public class SystemExceptionLogVo extends SystemExceptionLog {

    @ApiModelProperty(value = "ID字符串形式，避免JavaScript精度丢失")
    private String idStr;

    @ApiModelProperty(value = "异常类型")
    private String exceptionType;

    @ApiModelProperty(value = "IP地址")
    private String ipAddress;

    @ApiModelProperty(value = "日志级别")
    private String logLevel;

    @ApiModelProperty(value = "堆栈跟踪")
    private String stackTrace;

    /**
     * 无参构造器
     */
    public SystemExceptionLogVo() {
        super();
    }

    /**
     * 带参构造器
     */
    public SystemExceptionLogVo(String systemUserId, String userName, String description, String requestUrl, String ipAddress, String stackTraceException, String exceptionMessage) {
        super(systemUserId, userName, description, requestUrl, ipAddress, stackTraceException, exceptionMessage);
    }

    public String getIdStr() {
        return idStr;
    }

    public void setIdStr(String idStr) {
        this.idStr = idStr;
    }

    public String getExceptionType() {
        return exceptionType;
    }

    public void setExceptionType(String exceptionType) {
        this.exceptionType = exceptionType;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getLogLevel() {
        return logLevel;
    }

    public void setLogLevel(String logLevel) {
        this.logLevel = logLevel;
    }

    public String getStackTrace() {
        return stackTrace;
    }

    public void setStackTrace(String stackTrace) {
        this.stackTrace = stackTrace;
    }
}
