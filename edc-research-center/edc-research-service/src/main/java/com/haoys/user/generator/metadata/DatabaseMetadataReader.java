package com.haoys.user.generator.metadata;

import lombok.extern.slf4j.Slf4j;
import com.haoys.user.generator.config.GeneratorConfig;
import com.haoys.user.generator.model.TableInfo;
import com.haoys.user.generator.model.ColumnInfo;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据库元数据读取器
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
@Slf4j
public class DatabaseMetadataReader {
    
    private final GeneratorConfig config;
    
    public DatabaseMetadataReader(GeneratorConfig config) {
        this.config = config;
    }
    
    /**
     * 读取表信息
     */
    public TableInfo readTableInfo(String tableName) throws SQLException {
        log.info("开始读取表信息: {}", tableName);
        
        try (Connection connection = getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            
            TableInfo tableInfo = new TableInfo();
            tableInfo.setTableName(tableName);
            tableInfo.setEntityName(config.getEntityName());
            
            // 读取表注释
            String tableComment = getTableComment(connection, tableName);
            tableInfo.setTableComment(tableComment);
            
            // 读取列信息
            List<ColumnInfo> columns = readColumns(metaData, tableName);
            tableInfo.setColumns(columns);
            
            // 读取主键信息
            String primaryKey = readPrimaryKey(metaData, tableName);
            tableInfo.setPrimaryKey(primaryKey);
            
            log.info("表信息读取完成: {} -> {} 列", tableName, columns.size());
            return tableInfo;
        }
    }
    
    /**
     * 获取数据库连接
     */
    private Connection getConnection() throws SQLException {
        try {
            Class.forName(config.getJdbcDriver());
            return DriverManager.getConnection(
                config.getJdbcUrl(), 
                config.getJdbcUsername(), 
                config.getJdbcPassword()
            );
        } catch (ClassNotFoundException e) {
            throw new SQLException("JDBC驱动加载失败: " + config.getJdbcDriver(), e);
        }
    }
    
    /**
     * 获取表注释
     */
    private String getTableComment(Connection connection, String tableName) {
        String sql = "SELECT TABLE_COMMENT FROM INFORMATION_SCHEMA.TABLES " +
                    "WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?";
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, tableName);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("TABLE_COMMENT");
                }
            }
        } catch (SQLException e) {
            log.warn("获取表注释失败: {}", e.getMessage());
        }
        
        return "";
    }
    
    /**
     * 读取列信息
     */
    private List<ColumnInfo> readColumns(DatabaseMetaData metaData, String tableName) throws SQLException {
        List<ColumnInfo> columns = new ArrayList<>();
        
        try (ResultSet rs = metaData.getColumns(null, null, tableName, null)) {
            while (rs.next()) {
                ColumnInfo column = new ColumnInfo();
                
                // 基本信息
                column.setColumnName(rs.getString("COLUMN_NAME"));
                column.setJdbcType(rs.getInt("DATA_TYPE"));
                column.setJdbcTypeName(rs.getString("TYPE_NAME"));
                column.setColumnSize(rs.getInt("COLUMN_SIZE"));
                column.setDecimalDigits(rs.getInt("DECIMAL_DIGITS"));
                column.setNullable(rs.getInt("NULLABLE") == DatabaseMetaData.columnNullable);
                column.setColumnComment(rs.getString("REMARKS"));
                column.setDefaultValue(rs.getString("COLUMN_DEF"));
                
                // 自动递增
                String autoIncrement = rs.getString("IS_AUTOINCREMENT");
                column.setAutoIncrement("YES".equalsIgnoreCase(autoIncrement));
                
                // 转换为Java类型
                column.setJavaType(convertToJavaType(column.getJdbcType(), column.getJdbcTypeName()));
                column.setJavaProperty(convertToJavaProperty(column.getColumnName()));
                
                // 判断是否为BLOB类型
                column.setBlobType(isBlobType(column.getJdbcType()));
                
                columns.add(column);
                
                log.debug("列信息: {} -> {} ({})", 
                    column.getColumnName(), 
                    column.getJavaProperty(), 
                    column.getJavaType());
            }
        }
        
        return columns;
    }
    
    /**
     * 读取主键信息
     */
    private String readPrimaryKey(DatabaseMetaData metaData, String tableName) throws SQLException {
        try (ResultSet rs = metaData.getPrimaryKeys(null, null, tableName)) {
            if (rs.next()) {
                return rs.getString("COLUMN_NAME");
            }
        }
        return null;
    }
    
    /**
     * 转换为Java类型
     */
    private String convertToJavaType(int jdbcType, String jdbcTypeName) {
        switch (jdbcType) {
            case Types.TINYINT:
                return "Byte";
            case Types.SMALLINT:
                return "Short";
            case Types.INTEGER:
                return "Integer";
            case Types.BIGINT:
                return "Long";
            case Types.FLOAT:
                return "Float";
            case Types.DOUBLE:
            case Types.NUMERIC:
            case Types.DECIMAL:
                return "Double";
            case Types.BOOLEAN:
            case Types.BIT:
                return "Boolean";
            case Types.CHAR:
            case Types.VARCHAR:
            case Types.LONGVARCHAR:
            case Types.NCHAR:
            case Types.NVARCHAR:
            case Types.LONGNVARCHAR:
                return "String";
            case Types.DATE:
                return "Date";
            case Types.TIME:
                return "Time";
            case Types.TIMESTAMP:
                return "Date";
            case Types.BLOB:
            case Types.LONGVARBINARY:
                return "byte[]";
            case Types.CLOB:
            case Types.NCLOB:
                return "String";
            default:
                if ("TEXT".equalsIgnoreCase(jdbcTypeName) || 
                    "LONGTEXT".equalsIgnoreCase(jdbcTypeName) ||
                    "MEDIUMTEXT".equalsIgnoreCase(jdbcTypeName)) {
                    return "String";
                }
                return "String";
        }
    }
    
    /**
     * 转换为Java属性名（驼峰命名）
     */
    private String convertToJavaProperty(String columnName) {
        if (columnName == null || columnName.isEmpty()) {
            return columnName;
        }
        
        StringBuilder result = new StringBuilder();
        boolean nextUpperCase = false;
        
        for (int i = 0; i < columnName.length(); i++) {
            char c = columnName.charAt(i);
            if (c == '_') {
                nextUpperCase = true;
            } else {
                if (nextUpperCase) {
                    result.append(Character.toUpperCase(c));
                    nextUpperCase = false;
                } else {
                    result.append(Character.toLowerCase(c));
                }
            }
        }
        
        return result.toString();
    }
    
    /**
     * 判断是否为BLOB类型
     */
    private boolean isBlobType(int jdbcType) {
        return jdbcType == Types.BLOB || 
               jdbcType == Types.LONGVARBINARY ||
               jdbcType == Types.CLOB ||
               jdbcType == Types.NCLOB;
    }
}
