package com.haoys.user;

import com.haoys.user.generator.core.CodeGenerator;
import com.haoys.user.generator.config.GeneratorConfig;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * 研究中心代码生成器 - 完善版
 *
 * 功能特性：
 * 1. 统一路径配置管理，便于维护和调整
 * 2. 支持多种条件的复杂查询方式
 * 3. 简化Mapper接口，去除冗余聚合查询
 * 4. Controller代码生成到API模块，层次分类明确
 * 5. 从配置文件读取数据源和表名配置
 * 6. 确保Java 8兼容性和代码规范
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 1.0.0
 */
public class ResearchCenterGenerator {

    // ========== 路径配置常量 ==========
    /**
     * 项目根路径
     */
    private static final String PROJECT_ROOT = "/Users/<USER>/source_code/edc-research-master";

    /**
     * 研究中心模块根路径
     */
    private static final String RESEARCH_CENTER_ROOT = PROJECT_ROOT + "/edc-research-center";

    /**
     * Service模块路径
     */
    private static final String SERVICE_MODULE_PATH = RESEARCH_CENTER_ROOT + "/edc-research-service";

    /**
     * API模块路径
     */
    private static final String API_MODULE_PATH = RESEARCH_CENTER_ROOT + "/edc-research-api";

    /**
     * 资源文件路径
     */
    private static final String RESOURCES_PATH = SERVICE_MODULE_PATH + "/src/main/resources";

    /**
     * 配置文件路径
     */
    private static final String GENERATOR_PROPERTIES = RESOURCES_PATH + "/generator.properties";
    private static final String GENERATOR_CONFIG_XML = RESOURCES_PATH + "/generatorMysqlConfig.xml";

    // ========== 包名配置常量 ==========
    /**
     * 基础包名
     */
    private static final String BASE_PACKAGE = "com.haoys.user";

    /**
     * 各层包名
     */
    private static final String MODEL_PACKAGE = BASE_PACKAGE + ".model";
    private static final String MAPPER_PACKAGE = BASE_PACKAGE + ".mapper";
    private static final String SERVICE_PACKAGE = BASE_PACKAGE + ".service";
    private static final String SERVICE_IMPL_PACKAGE = BASE_PACKAGE + ".service.impl";
    private static final String CONTROLLER_PACKAGE = BASE_PACKAGE + ".controller";

    public static void main(String[] args) throws Exception {
        printBanner();

        try {
            // 验证环境和配置
            validateEnvironment();

            // 从XML配置文件读取要生成的表名列表
            List<String> tableNames = readTableNamesFromConfig();

            if (tableNames.isEmpty()) {
                System.out.println("⚠️  未在配置文件中找到要生成的表，使用默认表名: project_config_module");
                tableNames.add("project_config_module");
            }

            System.out.println("📋 发现 " + tableNames.size() + " 个表需要生成代码");
            printTableList(tableNames);

            // 为每个表生成代码
            int successCount = 0;
            int failCount = 0;

            for (String tableName : tableNames) {
                System.out.println("\n🚀 开始生成表 [" + tableName + "] 的代码...");

                try {
                    // 创建生成器配置
                    GeneratorConfig config = createGeneratorConfig(tableName);

                    // 创建代码生成器
                    CodeGenerator generator = new CodeGenerator(config);

                    // 执行代码生成
                    generator.generate();

                    System.out.println("✅ 表 [" + tableName + "] 代码生成完成");
                    successCount++;
                } catch (Exception e) {
                    System.err.println("❌ 表 [" + tableName + "] 代码生成失败: " + e.getMessage());
                    e.printStackTrace();
                    failCount++;
                }
            }

            printSummary(successCount, failCount);

        } catch (Exception e) {
            System.err.println("💥 代码生成器启动失败: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 打印启动横幅
     */
    private static void printBanner() {
        System.out.println("╔══════════════════════════════════════════════════════════════╗");
        System.out.println("║                  研究中心代码生成器 v3.0.0                    ║");
        System.out.println("║                     Research Center Generator                ║");
        System.out.println("╠══════════════════════════════════════════════════════════════╣");
        System.out.println("║ 特性: 统一路径配置 | 复杂查询支持 | 简化Mapper | API模块分离  ║");
        System.out.println("║ 兼容: Java 8 | Spring Boot | MyBatis | Swagger | 事务处理   ║");
        System.out.println("╚══════════════════════════════════════════════════════════════╝");
        System.out.println();
    }

    /**
     * Java 8兼容的字符串重复方法
     * 替代Java 11的String.repeat()方法
     *
     * @param str 要重复的字符串
     * @param count 重复次数
     * @return 重复后的字符串
     */
    private static String repeatString(String str, int count) {
        if (count <= 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    /**
     * 验证环境和配置
     */
    private static void validateEnvironment() {
        System.out.println("🔍 验证环境配置...");

        // 验证配置文件存在
        java.io.File propsFile = new java.io.File(GENERATOR_PROPERTIES);
        java.io.File xmlFile = new java.io.File(GENERATOR_CONFIG_XML);

        if (!propsFile.exists()) {
            throw new RuntimeException("配置文件不存在: " + GENERATOR_PROPERTIES);
        }
        if (!xmlFile.exists()) {
            throw new RuntimeException("配置文件不存在: " + GENERATOR_CONFIG_XML);
        }

        System.out.println("✅ 环境配置验证通过");
    }

    /**
     * 打印表列表
     */
    private static void printTableList(List<String> tableNames) {
        System.out.println("\n📊 待生成代码的表列表:");
        for (int i = 0; i < tableNames.size(); i++) {
            System.out.println("   " + (i + 1) + ". " + tableNames.get(i));
        }
    }

    /**
     * 打印生成结果摘要
     */
    private static void printSummary(int successCount, int failCount) {
        System.out.println("\n" + repeatString("=", 60));
        System.out.println("📈 代码生成结果摘要:");
        System.out.println("   ✅ 成功: " + successCount + " 个表");
        System.out.println("   ❌ 失败: " + failCount + " 个表");
        System.out.println("   📁 Controller输出目录: " + API_MODULE_PATH + "/src/main/java/" + CONTROLLER_PACKAGE.replace(".", "/"));
        System.out.println("   📁 Service输出目录: " + SERVICE_MODULE_PATH + "/src/main/java/" + SERVICE_PACKAGE.replace(".", "/"));
        System.out.println(repeatString("=", 60));

        if (failCount == 0) {
            System.out.println("🎉 所有代码生成完成！请检查生成的代码文件，确保符合项目规范。");
        } else {
            System.out.println("⚠️  部分代码生成失败，请检查错误信息并重新生成。");
        }
    }

    /**
     * 从XML配置文件读取要生成的表名列表
     */
    private static List<String> readTableNamesFromConfig() {
        List<String> tableNames = new ArrayList<>();
        try {
            System.out.println("📖 读取配置文件: " + GENERATOR_CONFIG_XML);

            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(GENERATOR_CONFIG_XML);

            NodeList tableNodes = document.getElementsByTagName("table");
            for (int i = 0; i < tableNodes.getLength(); i++) {
                Element tableElement = (Element) tableNodes.item(i);
                String tableName = tableElement.getAttribute("tableName");
                if (tableName != null && !tableName.trim().isEmpty() && !tableName.contains("%")) {
                    tableNames.add(tableName.trim());
                    System.out.println("   ✓ 发现表配置: " + tableName);
                }
            }

            if (tableNames.isEmpty()) {
                System.out.println("   ⚠️  配置文件中没有启用的表配置（所有table标签都被注释）");
            }

        } catch (Exception e) {
            System.err.println("❌ 读取XML配置文件失败: " + e.getMessage());
            e.printStackTrace();
        }
        return tableNames;
    }

    /**
     * 从properties文件读取数据库配置
     */
    private static Properties loadDatabaseProperties() {
        Properties props = new Properties();
        try (InputStream input = new FileInputStream(GENERATOR_PROPERTIES)) {
            props.load(input);
            System.out.println("✅ 成功加载数据库配置文件");

            // 验证必要的配置项
            String[] requiredKeys = {"jdbc.driverClass", "jdbc.connectionURL", "jdbc.userId", "jdbc.password"};
            for (String key : requiredKeys) {
                if (props.getProperty(key) == null || props.getProperty(key).trim().isEmpty()) {
                    System.err.println("⚠️  配置项缺失或为空: " + key);
                }
            }

        } catch (Exception e) {
            System.err.println("❌ 加载数据库配置文件失败: " + e.getMessage());
            System.out.println("🔄 使用默认数据库配置...");

            // 使用默认配置
            props.setProperty("jdbc.driverClass", "com.mysql.cj.jdbc.Driver");
            props.setProperty("jdbc.connectionURL", "********************************************************************************************************************");
            props.setProperty("jdbc.userId", "root");
            props.setProperty("jdbc.password", "Asd123456##");
        }
        return props;
    }

    /**
     * 创建生成器配置
     */
    private static GeneratorConfig createGeneratorConfig(String tableName) {
        System.out.println("⚙️  创建生成器配置 - 表: " + tableName);

        GeneratorConfig config = new GeneratorConfig();

        // 从配置文件读取数据库配置
        Properties dbProps = loadDatabaseProperties();
        config.setJdbcDriver(dbProps.getProperty("jdbc.driverClass"));
        config.setJdbcUrl(dbProps.getProperty("jdbc.connectionURL"));
        config.setJdbcUsername(dbProps.getProperty("jdbc.userId"));
        config.setJdbcPassword(dbProps.getProperty("jdbc.password"));

        // 包配置 - 使用统一常量
        config.setBasePackage(BASE_PACKAGE);
        config.setModelPackage(MODEL_PACKAGE);
        config.setMapperPackage(MAPPER_PACKAGE);
        config.setServicePackage(SERVICE_PACKAGE);
        config.setServiceImplPackage(SERVICE_IMPL_PACKAGE);
        config.setControllerPackage(CONTROLLER_PACKAGE);

        // 路径配置 - 使用统一常量，模块分离
        config.setJavaPath(SERVICE_MODULE_PATH + "/src/main/java");
        config.setResourcePath(SERVICE_MODULE_PATH + "/src/main/resources");
        config.setTestPath(SERVICE_MODULE_PATH + "/src/test/java");

        // Controller放在API模块，实现层次分类
        config.setControllerPath(API_MODULE_PATH + "/src/main/java/" + CONTROLLER_PACKAGE.replace(".", "/"));

        // 表配置 - 动态设置表名
        config.setTableName(tableName);
        config.setEntityName(convertToEntityName(tableName));

        // 功能配置 - 优化配置，支持复杂查询，简化聚合查询
        config.setEnableSwagger(true);
        config.setEnableCache(true);
        config.setEnableBatchOperations(true);
        config.setEnableComplexQuery(true);  // 支持多种条件的复杂查询
        config.setEnableBlobQuery(false);    // 简化BLOB查询
        config.setEnableAggregateQuery(false); // 去除冗余聚合查询，只保留简单CRUD
        config.setOverwriteFiles(true);

        // 作者信息
        config.setAuthor("system");
        config.setVersion("3.0.0");
        config.setModuleDescription("研究中心" + convertToEntityName(tableName) + "模块");

        System.out.println("   ✓ 配置创建完成");
        printConfigSummary(config, tableName);

        return config;
    }

    /**
     * 打印配置摘要
     */
    private static void printConfigSummary(GeneratorConfig config, String tableName) {
        System.out.println("   📋 配置摘要:");
        System.out.println("      表名: " + tableName);
        System.out.println("      实体类: " + config.getEntityName());
        System.out.println("      Service路径: " + config.getServicePath());
        System.out.println("      Controller路径: " + config.getControllerPath());
        System.out.println("      复杂查询: " + (config.isEnableComplexQuery() ? "启用" : "禁用"));
        System.out.println("      聚合查询: " + (config.isEnableAggregateQuery() ? "启用" : "禁用"));
    }

    /**
     * 将表名转换为实体类名
     * 例如: project_testee_file -> ProjectTesteeFile
     */
    private static String convertToEntityName(String tableName) {
        if (tableName == null || tableName.isEmpty()) {
            return tableName;
        }

        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = true;

        for (char c : tableName.toCharArray()) {
            if (c == '_') {
                capitalizeNext = true;
            } else if (capitalizeNext) {
                result.append(Character.toUpperCase(c));
                capitalizeNext = false;
            } else {
                result.append(Character.toLowerCase(c));
            }
        }

        return result.toString();
    }

    /**
     * 生成指定表的代码示例
     * 使用示例：generateCodeForTable("project_config_module");
     */
    public static void generateCodeForTable(String tableName) throws Exception {
        System.out.println("🎯 单表代码生成示例 - 表: " + tableName);

        try {
            // 验证环境
            validateEnvironment();

            // 创建配置
            GeneratorConfig config = createGeneratorConfig(tableName);

            // 创建生成器
            CodeGenerator generator = new CodeGenerator(config);

            // 执行生成
            generator.generate();

            System.out.println("✅ 表 [" + tableName + "] 代码生成完成");

            // 打印生成的文件列表
            printGeneratedFiles(config);

        } catch (Exception e) {
            System.err.println("❌ 代码生成失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 打印生成的文件列表
     */
    private static void printGeneratedFiles(GeneratorConfig config) {
        String entityName = config.getEntityName();
        System.out.println("\n📁 生成的文件列表:");
        System.out.println("   1. 实体类: " + config.getModelPath() + "/" + entityName + ".java");
        System.out.println("   2. Mapper接口: " + config.getMapperPath() + "/" + entityName + "Mapper.java");
        System.out.println("   3. Mapper XML: " + config.getMapperXmlPath() + "/" + entityName + "Mapper.xml");
        System.out.println("   4. Service接口: " + config.getServicePath() + "/" + entityName + "Service.java");
        System.out.println("   5. Service实现: " + config.getServiceImplPath() + "/" + entityName + "ServiceImpl.java");
        System.out.println("   6. Controller: " + config.getControllerPath() + "/" + entityName + "Controller.java");
        System.out.println("   7. 测试类: " + config.getTestClassPath() + "/" + entityName + "ServiceTest.java");
    }

    /**
     * 完整使用示例
     * 演示如何使用代码生成器的各种功能
     */
    public static void usageExample() {
        System.out.println("📚 ResearchCenterGenerator 使用示例:");
        System.out.println();

        System.out.println("1. 生成所有配置文件中的表:");
        System.out.println("   ResearchCenterGenerator.main(new String[]{});");
        System.out.println();

        System.out.println("2. 生成指定单个表:");
        System.out.println("   try {");
        System.out.println("       ResearchCenterGenerator.generateCodeForTable(\"project_config_module\");");
        System.out.println("   } catch (Exception e) {");
        System.out.println("       e.printStackTrace();");
        System.out.println("   }");
        System.out.println();

        System.out.println("3. 配置文件说明:");
        System.out.println("   - generator.properties: 数据库连接配置");
        System.out.println("   - generatorMysqlConfig.xml: 表配置，取消注释需要生成的表");
        System.out.println();

        System.out.println("4. 生成的代码特性:");
        System.out.println("   - 支持复杂查询条件");
        System.out.println("   - 简化的Mapper接口，去除冗余聚合查询");
        System.out.println("   - Controller生成到API模块");
        System.out.println("   - Service生成到Service模块");
        System.out.println("   - 完整的CRUD操作");
        System.out.println("   - Swagger注解支持");
        System.out.println("   - 事务处理支持");
        System.out.println("   - Java 8兼容");
        System.out.println();

        System.out.println("5. 目录结构:");
        System.out.println("   edc-research-center/");
        System.out.println("   ├── edc-research-api/src/main/java/com/haoys/user/controller/");
        System.out.println("   └── edc-research-service/src/main/java/com/haoys/user/");
        System.out.println("       ├── model/");
        System.out.println("       ├── mapper/");
        System.out.println("       ├── service/");
        System.out.println("       └── service/impl/");
    }
}
