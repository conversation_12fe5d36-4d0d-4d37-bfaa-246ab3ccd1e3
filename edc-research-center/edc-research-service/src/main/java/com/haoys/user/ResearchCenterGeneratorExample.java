package com.haoys.user;

/**
 * 研究中心代码生成器使用示例
 * 
 * 本示例展示了如何使用ResearchCenterGenerator进行代码生成
 * 包括配置说明、使用方法、生成结果等
 * 
 * <AUTHOR>
 * @version 3.0.0
 * @since 1.0.0
 */
public class ResearchCenterGeneratorExample {

    public static void main(String[] args) {
        System.out.println("╔══════════════════════════════════════════════════════════════╗");
        System.out.println("║              研究中心代码生成器使用示例                        ║");
        System.out.println("║                ResearchCenterGenerator Example              ║");
        System.out.println("╚══════════════════════════════════════════════════════════════╝");
        System.out.println();

        // 显示使用示例
        ResearchCenterGenerator.usageExample();

        System.out.println("\n" + repeatString("=", 60));
        System.out.println("🚀 开始执行示例代码生成...");
        System.out.println(repeatString("=", 60));

        try {
            // 示例1: 生成单个表的代码
            example1_GenerateSingleTable();
            
            // 示例2: 生成所有配置表的代码
            example2_GenerateAllTables();
            
            // 示例3: 自定义配置生成
            example3_CustomConfiguration();
            
        } catch (Exception e) {
            System.err.println("❌ 示例执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Java 8兼容的字符串重复方法
     * 替代Java 11的String.repeat()方法
     *
     * @param str 要重复的字符串
     * @param count 重复次数
     * @return 重复后的字符串
     */
    private static String repeatString(String str, int count) {
        if (count <= 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    /**
     * 示例1: 生成单个表的代码
     */
    private static void example1_GenerateSingleTable() {
        System.out.println("\n📝 示例1: 生成单个表的代码");
        System.out.println(repeatString("-", 40));
        
        try {
            // 生成project_config_module表的代码
            String tableName = "project_config_module";
            System.out.println("🎯 目标表: " + tableName);
            
            ResearchCenterGenerator.generateCodeForTable(tableName);
            
            System.out.println("✅ 示例1执行成功");
            
        } catch (Exception e) {
            System.err.println("❌ 示例1执行失败: " + e.getMessage());
        }
    }

    /**
     * 示例2: 生成所有配置表的代码
     */
    private static void example2_GenerateAllTables() {
        System.out.println("\n📝 示例2: 生成所有配置表的代码");
        System.out.println(repeatString("-", 40));
        
        try {
            System.out.println("🎯 读取配置文件中的所有表进行生成");
            
            // 调用主方法生成所有表
            ResearchCenterGenerator.main(new String[]{});
            
            System.out.println("✅ 示例2执行成功");
            
        } catch (Exception e) {
            System.err.println("❌ 示例2执行失败: " + e.getMessage());
        }
    }

    /**
     * 示例3: 自定义配置生成
     */
    private static void example3_CustomConfiguration() {
        System.out.println("\n📝 示例3: 自定义配置说明");
        System.out.println(repeatString("-", 40));
        
        System.out.println("🔧 自定义配置步骤:");
        System.out.println("   1. 修改 generator.properties 配置数据库连接");
        System.out.println("   2. 修改 generatorMysqlConfig.xml 启用需要的表");
        System.out.println("   3. 运行 ResearchCenterGenerator.main()");
        System.out.println();
        
        System.out.println("📋 配置文件位置:");
        System.out.println("   - generator.properties: edc-research-service/src/main/resources/");
        System.out.println("   - generatorMysqlConfig.xml: edc-research-service/src/main/resources/");
        System.out.println();
        
        System.out.println("🎨 生成代码特性:");
        System.out.println("   ✓ 统一路径配置管理");
        System.out.println("   ✓ 支持复杂查询条件");
        System.out.println("   ✓ 简化Mapper接口");
        System.out.println("   ✓ Controller生成到API模块");
        System.out.println("   ✓ 完整CRUD操作");
        System.out.println("   ✓ Swagger注解");
        System.out.println("   ✓ 事务处理");
        System.out.println("   ✓ Java 8兼容");
        System.out.println();
        
        System.out.println("📁 生成文件结构:");
        System.out.println("   edc-research-center/");
        System.out.println("   ├── edc-research-api/");
        System.out.println("   │   └── src/main/java/com/haoys/user/controller/");
        System.out.println("   │       └── [Entity]Controller.java");
        System.out.println("   └── edc-research-service/");
        System.out.println("       └── src/main/java/com/haoys/user/");
        System.out.println("           ├── model/[Entity].java");
        System.out.println("           ├── mapper/[Entity]Mapper.java");
        System.out.println("           ├── service/[Entity]Service.java");
        System.out.println("           └── service/impl/[Entity]ServiceImpl.java");
        System.out.println("       └── src/main/resources/com/haoys/user/mapper/");
        System.out.println("           └── [Entity]Mapper.xml");
        System.out.println();
        
        System.out.println("✅ 示例3说明完成");
    }

    /**
     * 配置文件示例说明
     */
    public static void showConfigurationExamples() {
        System.out.println("📄 配置文件示例:");
        System.out.println();
        
        System.out.println("1. generator.properties 示例:");
        System.out.println("   jdbc.driverClass=com.mysql.cj.jdbc.Driver");
        System.out.println("   jdbc.connectionURL=***********************************************");
        System.out.println("   jdbc.userId=root");
        System.out.println("   jdbc.password=Asd123456##");
        System.out.println();
        
        System.out.println("2. generatorMysqlConfig.xml 表配置示例:");
        System.out.println("   <!-- 启用表生成 -->");
        System.out.println("   <table tableName='project_config_module' domainObjectName='ProjectConfigModule'/>");
        System.out.println("   ");
        System.out.println("   <!-- 禁用表生成 -->");
        System.out.println("   <!--<table tableName='other_table' domainObjectName='OtherTable'/>-->");
        System.out.println();
        
        System.out.println("3. 特殊字段处理示例:");
        System.out.println("   <table tableName='example_table' domainObjectName='ExampleTable'>");
        System.out.println("       <columnOverride column=\"content\" property=\"content\" jdbcType=\"VARCHAR\"/>");
        System.out.println("   </table>");
    }

    /**
     * 常见问题解答
     */
    public static void showFAQ() {
        System.out.println("❓ 常见问题解答:");
        System.out.println();
        
        System.out.println("Q1: 如何修改生成代码的输出路径？");
        System.out.println("A1: 修改ResearchCenterGenerator类中的路径常量配置");
        System.out.println();
        
        System.out.println("Q2: 如何添加新的表进行代码生成？");
        System.out.println("A2: 在generatorMysqlConfig.xml中添加table标签并取消注释");
        System.out.println();
        
        System.out.println("Q3: 生成的代码如何集成到项目中？");
        System.out.println("A3: 生成的代码已按模块分离，直接编译即可使用");
        System.out.println();
        
        System.out.println("Q4: 如何处理TEXT字段避免生成BLOB？");
        System.out.println("A4: 使用columnOverride配置，设置jdbcType为VARCHAR");
        System.out.println();
        
        System.out.println("Q5: 如何确保Java 8兼容性？");
        System.out.println("A5: 生成器已确保所有代码兼容Java 8，无需额外配置");
    }
}
