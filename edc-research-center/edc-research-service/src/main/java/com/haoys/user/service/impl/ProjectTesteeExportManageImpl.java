package com.haoys.user.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.excel.export.ExcelStyleUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.bussiness.RedisKeyContants;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.excel.CustomExcelExportService;
import com.haoys.user.common.file.ImageCompressionService;
import com.haoys.user.common.pdf.PDFTemplateUtil;
import com.haoys.user.common.pdf.ZipUtils;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.common.util.DateUtil;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.testee.ProjectExportFiledParam;
import com.haoys.user.domain.param.testee.ProjectExportFlowParam;
import com.haoys.user.domain.param.testee.ProjectExportFlowParam2;
import com.haoys.user.domain.param.testee.ProjectTesteeExportParam;
import com.haoys.user.domain.param.testee.ProjectTesteeExportSelectParam;
import com.haoys.user.domain.vo.ExportTesteeVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeTableBody;
import com.haoys.user.domain.vo.ecrf.TemplateFormConfigVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDetailVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDictionaryVo;
import com.haoys.user.domain.vo.ecrf.TemplateTableVo;
import com.haoys.user.domain.vo.project.ProjectVisitVo;
import com.haoys.user.domain.vo.project.ProjectVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeFormImageVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeTableResultExportVo;
import com.haoys.user.mapper.FlowFormSetMapper;
import com.haoys.user.mapper.ProjectTesteeExportMapper;
import com.haoys.user.model.FlowFormSet;
import com.haoys.user.model.FlowPlan;
import com.haoys.user.model.Organization;
import com.haoys.user.model.ProjectOrgInfo;
import com.haoys.user.model.ProjectTesteeExport;
import com.haoys.user.model.ProjectTesteeExportExample;
import com.haoys.user.model.ProjectTesteeExportManage;
import com.haoys.user.model.ProjectTesteeInfo;
import com.haoys.user.model.ProjectVisitConfig;
import com.haoys.user.service.FlowPlanService;
import com.haoys.user.service.OrganizationService;
import com.haoys.user.service.ProjectBaseManageService;
import com.haoys.user.service.ProjectTesteeExportManageService;
import com.haoys.user.service.ProjectTesteeInfoService;
import com.haoys.user.service.ProjectTesteeTableService;
import com.haoys.user.service.ProjectVisitConfigService;
import com.haoys.user.service.TemplateConfigService;
import com.haoys.user.storge.cloud.OssStorageConfig;
import com.pmstation.spss.SPSSWriter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.util.HtmlUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;


/**
 * 项目参与者导出管理实现类
 *
 * 重要修复（2025-07-24）：
 * - 修复了Excel导出中字段名冲突导致的数据错误赋值问题
 * - 问题：不同表单中相同字段名（如"ECOG评分日期"）会相互覆盖
 * - 解决：使用"访视ID_表单ID_字段名"格式生成唯一字段键
 * - 影响：确保治疗期访视(C2V1)和筛选期访视(V0)的ECOG评分日期字段数据正确分离
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class ProjectTesteeExportManageImpl implements ProjectTesteeExportManageService {

    // PDF导出图片URL配置常量
    /**
     * PDF导出是否使用原始URL（true：使用原始URL，false：使用压缩URL）
     * 默认使用原始URL以确保图片质量
     */
    private static final boolean USE_ORIGINAL_IMAGE_URL = true;

    /**
     * PDF导出是否启用图片缓存（true：启用缓存，false：不缓存）
     * 默认不缓存图片，避免缓存一致性问题
     */
    private static final boolean ENABLE_IMAGE_CACHE = false;

    /**
     * PDF导出是否默认使用优化版本（true：使用优化版本，false：使用原始版本）
     * 默认使用优化版本以提升性能，解决标准版本的N+1查询和导出卡住问题
     */
    private static final boolean USE_OPTIMIZED_VERSION_BY_DEFAULT = true;

    /**
     * PDF导出是否强制使用高性能版本（true：强制使用高性能版本，false：允许使用标准版本）
     * 默认强制使用高性能版本，确保导出稳定性
     */
    private static final boolean FORCE_HIGH_PERFORMANCE_VERSION = true;

    // Excel导出图片配置常量
    /**
     * Excel导出图片变量显示模式（true：显示真实预览图片，false：显示图片访问路径）
     * 默认使用路径模式，提高导出性能
     */
    private static final boolean EXCEL_EXPORT_SHOW_IMAGE_PREVIEW = false;

    /**
     * Excel导出图片变量路径显示模式（true：显示完整URL，false：显示相对路径）
     * 默认显示完整URL，便于访问
     */
    private static final boolean EXCEL_EXPORT_SHOW_FULL_IMAGE_URL = true;

    private final OssStorageConfig storageConfig;
    private final ProjectTesteeExportMapper projectTesteeExportMapper;
    private final TemplateConfigService templateConfigService;
    private final ProjectTesteeInfoService projectTesteeInfoService;
    private final ProjectVisitConfigService projectVisitConfigService;
    private final ProjectTesteeTableService projectTesteeTableService;
    private final ProjectBaseManageService projectBaseManageService;
    private final OrganizationService organizationService;
    private final ProjectVisitConfigService visitConfigService;
    private final FlowPlanService flowPlanService;
    private final FlowFormSetMapper flowFormSetMapper;
    private final RedisTemplateService redisTemplateService;
    private final ImageCompressionService imageCompressionService;
    private final ProjectTesteeExportOptimizedImpl optimizedExportService;

    @Override
    public CommonResult<List<ProjectTesteeExportManage>> list(Long projectId, Long orgId, String exportName, Integer exportType) {
        ProjectTesteeExportSelectParam selectParam = new ProjectTesteeExportSelectParam();
        selectParam.setProjectId(projectId);
        selectParam.setOrgId(orgId);
        selectParam.setUserId(Objects.requireNonNull(SecurityUtils.getUserId()).toString());
        selectParam.setExportType(exportType);
        selectParam.setExportName(exportName);
        List<ProjectTesteeExportManage> list = projectTesteeExportMapper.selectList2(selectParam);
        return CommonResult.success(list);
    }

    @Override
    public CommonResult<Object> saveProjectTesteeRecordExport(ProjectTesteeExportParam param) {
        ProjectTesteeExport projectTesteeExport = new ProjectTesteeExport();

        param.setUserId(Objects.requireNonNull(SecurityUtils.getUserId()).toString());
        param.setTenantId(SecurityUtils.getSystemTenantId());
        param.setPlatformId(SecurityUtils.getSystemPlatformId());
        param.setFileUrlName(SecurityUtils.getUserId().toString() + "-" + UUID.randomUUID());
        Long exportFileId = SnowflakeIdWorker.getUuid();
        projectTesteeExport.setId(exportFileId);
        projectTesteeExport.setProjectId(Long.parseLong(param.getProjectId()));
        projectTesteeExport.setTaskName(param.getExportName());
        projectTesteeExport.setCreateTime(new Date());
        projectTesteeExport.setOperator(Long.parseLong(param.getUserId()));
        projectTesteeExport.setOrgId(param.getOrgId());
        projectTesteeExport.setStatus(BusinessConfig.VALID_STATUS);
        projectTesteeExport.setExportStatus(BusinessConfig.TESTEE_EXPORT_STATUS_1);
        projectTesteeExport.setExportFileType(param.getExportType().toString());
        projectTesteeExport.setTenantId(param.getTenantId());
        projectTesteeExport.setPlatformId(param.getPlatformId());
        projectTesteeExportMapper.insert(projectTesteeExport);
        // 存放到redis中
        redisTemplateService.lPush(RedisKeyContants.USER_DOWN+param.getUserId(),exportFileId);
        // 构建导出文件
        this.asyncExport(param,exportFileId);
        return CommonResult.success(null);
    }

    private void asyncExport(ProjectTesteeExportParam param,Long exportFileId){
        ExecutorService executorService = Executors.newFixedThreadPool(1);
        executorService.execute(()->{
            if (param.getExportType().equals(1)) {
                exportExcelTestee(param, Constants.EXPORT_FILE_SUFFIX,exportFileId);
            } else if (param.getExportType().equals(2)) {
                exportExcelTestee(param, Constants.EXPORT_FILE_CSV,exportFileId);
            } else if (param.getExportType().equals(3)) {
                exportPdf(param,exportFileId);
            } else if (param.getExportType().equals(4)) {
                exportSas(param,Constants.EXPORT_FILE_SUFFIX_SAS,exportFileId);
            }else if (param.getExportType().equals(7)) {
                exportSav(param,Constants.EXPORT_FILE_SUFFIX_SAV,exportFileId);
            }
        });
        executorService.shutdown();
    }

    /**
     * 根据每个参与者导出pdf，把所有的参与者压缩到一个zip压缩包中
     * @param projectTesteeExportParam
     * @param exportFileId
     */
        private void exportPdf(ProjectTesteeExportParam projectTesteeExportParam, Long exportFileId) {
        // 检查是否启用优化版本
        boolean useOptimizedVersion = shouldUseOptimizedVersion(projectTesteeExportParam);

        if (useOptimizedVersion) {
            log.info("使用优化版PDF导出，参与者数量: {}", projectTesteeExportParam.getTesteeIds().size());
            try {
                optimizedExportService.exportPdfOptimized(projectTesteeExportParam, exportFileId);
                return;
            } catch (Exception e) {
                log.error("优化版PDF导出失败，回退到原始版本", e);
            }
        }

        // 原始版本的导出逻辑
        log.info("使用原始版PDF导出，参与者数量: {}", projectTesteeExportParam.getTesteeIds().size());

        List<ProjectExportFlowParam> exportList = extracted(projectTesteeExportParam);
        
        // 拼接html
        String vsStyle = "font-family:SimSun;margin-top:5px;width:100%;";
        String line = "<div style='height: 5px;border-bottom: 1px solid  #b4adad;'></div>";
        String tableStyle = "border: 1px solid #b4adad;width: 100%;margin-bottom: 20px;background-color: transparent;border-collapse: collapse;border-spacing: 0;display: table;";
        String tableTheadAndTbodyStyle = "display: table-header-group;vertical-align: middle;border-color: inherit;";
        String tableTrStyle = "display: table-row;ertical-align: inherit;border-color: inherit;";
        String tableTdStyle = "font-family:SimSun;border-top: 0;border-bottom-width: 2px;border: 1px solid  #b4adad;vertical-align: bottom;padding: 8px;line-height: 1.42857143;text-align: left;display: table-cell;";

        List<ProjectTesteeInfo> testeeIdList = projectTesteeInfoService.getProjectTesteeListByIds(projectTesteeExportParam.getProjectId(),projectTesteeExportParam.getOrgId(),projectTesteeExportParam.getTesteeIds());
        ProjectVo projectVo = projectBaseManageService.getProjectViewInfo(projectTesteeExportParam.getProjectId());
        ProjectOrgInfo projectOrgInfo = organizationService.getProjectOrgInfo(projectTesteeExportParam.getOrgId());
        Organization systemOrgInfo = organizationService.getSystemOrganizationInfo(projectOrgInfo.getOrgId().toString());

        Map<String, ByteArrayOutputStream> byteFileMap = new LinkedHashMap<>();
        try {
        for (int k = 0; k < testeeIdList.size(); k++) {
            StringBuilder html = new StringBuilder();
            ProjectTesteeInfo testeeInfo = testeeIdList.get(k);
            String testeeInfoCode = testeeInfo.getCode();
            String realName = testeeInfo.getRealName();
            
            for (int x = 0; x < exportList.size(); x++) {
                ProjectExportFlowParam projectExportFlowParam = exportList.get(x);
                String visitId = projectExportFlowParam.getVisitId();
                // 设置访视信息
                ProjectVisitVo projectVisitVo = projectVisitConfigService.getProjectVisitBaseConfigByVisitId(visitId);
                html.append("<div class='content'>");
                // .append("</span>").append("<span style='font-family:SimSun;float: right;'>访视阶段:").append(projectVisitVo.getVisitName()).append("</span>
                html.append("<h3 style='font-family:SimSun;'>").append("<span style='margin-left:20px'>").append(projectVisitVo.getVisitName()).append("</span>").append("</h3>");
                // 设置表单信息
                List<ProjectExportFiledParam> checkFormList = projectExportFlowParam.getForms();
                if (CollectionUtil.isNotEmpty(checkFormList)) {
                    for (int f = 0; f < checkFormList.size(); f++) {
                        ProjectExportFiledParam form = checkFormList.get(f);
                        // 获取表单信息
                        TemplateFormConfigVo templateFormConfigVo = templateConfigService.getTemplateFormConfigBaseInfoByFormId(form.getFormId());
                        html.append("<div class='form'>");
                        //html.append("<div class='header' style='border-bottom: 1px solid #000;overflow:hidden'><span style='font-family:SimSun;float: left;'>参与者编号:").append(testeeInfoCode).append("</span>").append("<span style='font-family:SimSun; float:right;'>姓名:").append(realName).append("</span></div>");
                        html.append("<h4 style='font-family:SimSun;'").append("<span style='margin-left:20px'>").append(templateFormConfigVo.getFormName()).append("</span>").append("</h4>");
                        html.append(line);
                        // 设置表单变量信息
                        List<String> variableIds = form.getVariableIds();
                        List<TemplateFormDetailVo> testeeVisitFormDetailRecordList = projectTesteeInfoService.getTesteeVisitFormDetail(projectTesteeExportParam.getProjectId(),"", visitId, form.getFormId(), "", null, projectOrgInfo.getId().toString(), testeeInfo.getId().toString(), "4", "0");
                        if (CollectionUtil.isNotEmpty(testeeVisitFormDetailRecordList)) {
                            for (int i = 0; i < testeeVisitFormDetailRecordList.size(); i++) {
                                TemplateFormDetailVo templateFormDetailVo = testeeVisitFormDetailRecordList.get(i);
                                if(StringUtils.isNotBlank(templateFormDetailVo.getFieldValue())){
                                    String fileValue = HtmlUtils.htmlUnescape(templateFormDetailVo.getFieldValue());
                                    templateFormDetailVo.setFieldValue(fileValue);
                                    //templateFormDetailVo.setFieldValue("<![CDATA[ "+ fileValue + "]]>");
                                }
                                if (!BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE.equals(templateFormDetailVo.getType())) {
                                    // 普通变量
                                    if (projectTesteeExportParam.getExportAll() || variableIds.contains(templateFormDetailVo.getId().toString())) {
                                        // 变量是单行文本 ，多行文本，日期，文字展示
                                        if (BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO.equals(templateFormDetailVo.getType())
                                                || BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(templateFormDetailVo.getType())
                                                || BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT.equals(templateFormDetailVo.getType())
                                        ) {
                                            // 变量是单选，多选，下拉单选
                                            List<TemplateFormDictionaryVo> dictionaryList = templateFormDetailVo.getTemplateFormDictionaryList();
                                            html.append("<div style='").append(vsStyle).append("' >").append(templateFormDetailVo.getLabel()).append(":");
                                            if (CollectionUtil.isNotEmpty(dictionaryList)) {
                                                html.append("<span style='margin-left:20px'></span>");
                                                if (BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(templateFormDetailVo.getType())) {
                                                    String fieldValue = templateFormDetailVo.getFieldValue();
                                                    if(StringUtils.isNotBlank(fieldValue)){
                                                        // 使用增强的复选框处理方法生成PDF内容
                                                        processCheckboxForPdf(fieldValue, dictionaryList, html);
                                                    }
                                                } else {
                                                    for (TemplateFormDictionaryVo dictionaryVo : dictionaryList) {
                                                        html.append("<span style='margin-left:20px'></span>");
                                                        if(dictionaryVo.getId().equals(templateFormDetailVo.getFieldValue())){
                                                            html.append("<input type='checkbox' checked='true' />").append(dictionaryVo.getName());
                                                        }else{
                                                            html.append("<input type='checkbox' />").append(dictionaryVo.getName());
                                                        }
                                                    }
                                                }
                                            }
                                            html.append("</div>");

                                        } else if (BusinessConfig.PROJECT_VISIT_CRF_FORM_NUMBER.equals(templateFormDetailVo.getType())) {
                                            html.append("<div style='").append(vsStyle).append("' >").append(templateFormDetailVo.getLabel()).append(":").append("<span style='margin-left:20px'>").append(templateFormDetailVo.getFieldValue()).append("</span>").append("</div>");
                                        } else if (BusinessConfig.PROJECT_VISIT_CRF_FORM_IMAGE.equals(templateFormDetailVo.getType())) {
                                            html.append("<div style='").append(vsStyle).append("' >").append("<span style='margin-left:20px'>").append(templateFormDetailVo.getLabel()).append(":</span>");
                                            List<ProjectTesteeFormImageVo> variableImageList = templateFormDetailVo.getVariableImageList();
                                            if (CollectionUtil.isNotEmpty(variableImageList)) {
                                                html.append("<div style='width:100%'>");
                                                for (ProjectTesteeFormImageVo imageVo : variableImageList) {
                                                    // 使用图片压缩服务获取压缩后的图片URL
                                                    String compressedImageUrl = imageCompressionService.compressExistingImage(imageVo.getFileUrl());
                                                    html.append("<img src='").append(compressedImageUrl).append("' style='width:150px;height:150px' ").append(" />");
                                                }
                                                html.append("</div>");
                                            }
                                            html.append("</div>");
                                        } else if (BusinessConfig.PROJECT_VISIT_CRF_FORM_IMAGE_VIEW.equals(templateFormDetailVo.getType())) {
                                            html.append("<div style='").append(vsStyle).append("' >").append("<span style='margin-left:20px'>").append(templateFormDetailVo.getLabel()).append(":</span>");
                                            Object expandValue = templateFormDetailVo.getExpand();
                                            if (expandValue != null && !expandValue.toString().trim().isEmpty()) {
                                                try {
                                                    JSONObject object = JSON.parseObject(expandValue.toString());
                                                    if (object != null && object.containsKey("imgUrl")) {
                                                        html.append("<div style='width:100%'>");
                                                        // 使用图片压缩服务获取压缩后的图片URL
                                                        String originalImageUrl = object.getString("imgUrl");
                                                        if (originalImageUrl != null && !originalImageUrl.trim().isEmpty()) {
                                                            String compressedImageUrl = imageCompressionService.compressExistingImage(originalImageUrl);
                                                            html.append("<img src='").append(compressedImageUrl).append("' style='width:150px;height:150px' ").append(" />");
                                                        } else {
                                                            log.warn("图片展示字段imgUrl为空，字段: {}", templateFormDetailVo.getLabel());
                                                        }
                                                        html.append("</div>");
                                                    } else {
                                                        log.warn("图片展示字段JSON中缺少imgUrl属性，字段: {}", templateFormDetailVo.getLabel());
                                                    }
                                                } catch (Exception e) {
                                                    log.error("图片展示字段JSON解析失败，字段: {}，原始数据: {}，错误: {}",
                                                             templateFormDetailVo.getLabel(), expandValue, e.getMessage());
                                                }
                                            } else {
                                                log.debug("图片展示字段expand为空，字段: {}", templateFormDetailVo.getLabel());
                                            }
                                            html.append("</div>");
                                        } else if (BusinessConfig.PROJECT_VISIT_CRF_FORM_SLIDER.equals(templateFormDetailVo.getType())) {
                                            html.append("<div style='").append(vsStyle).append("' >").append(templateFormDetailVo.getLabel()).append(":").append("<span style='margin-left:20px'>").append(templateFormDetailVo.getFieldValue()).append("</span>").append("</div>");
                                        }
                                        else if (BusinessConfig.PROJECT_VISIT_CRF_FORM_TEXT_VIEW.equals(templateFormDetailVo.getType())) {
                                            Object expandValue = templateFormDetailVo.getExpand();
                                            if (expandValue != null && expandValue != "") {
                                                JSONObject object = JSON.parseObject(expandValue.toString());
                                                html.append("<div style='").append(vsStyle).append("' >").append(templateFormDetailVo.getLabel()).append(":").append(object.get("textContent")).append("<span style='margin-left:20px'>").append(templateFormDetailVo.getFieldValue()).append("</span>").append("</div>");
                                            }
                                        } else {
                                            html.append("<div style='").append(vsStyle).append("' >").append(templateFormDetailVo.getLabel()).append(":").append("<span style='margin-left:20px'>").append(templateFormDetailVo.getFieldValue()).append("</span>").append("</div>");
                                        }
                                        html.append(line);
                                    }
                                } else {
                                    // 表格变量
                                    //html.append(line);
                                    html.append("<div style='").append(vsStyle).append("'>").append(templateFormDetailVo.getLabel()).append("</div>");
                                    html.append("<table style='").append(tableStyle).append("' >");
                                    // 设置表头
                                    List<TemplateTableVo> tableHeadRowList = templateFormDetailVo.getTableHeadRowList();
                                    if (CollectionUtil.isNotEmpty(tableHeadRowList)) {
                                        html.append("<thead style='").append(tableTheadAndTbodyStyle).append("'>");
                                        StringBuffer trS = new StringBuffer();
                                        for (TemplateTableVo tableVo : tableHeadRowList) {
                                            if (projectTesteeExportParam.getExportAll() || variableIds.contains(tableVo.getId().toString())) {
                                                trS.append("<th style='").append(tableTdStyle).append("'>").append(tableVo.getLabel()).append("</th>");
                                            }
                                        }
                                        if (trS.length()>0){
                                            html.append("<tr>");
                                            //html.append("<th style='").append(tableTdStyle).append("'>").append("编号").append("</th>");
                                            html.append(trS);
                                            html.append("</tr>");
                                        }
                                        html.append("</thead>");
                                    }
                                    List<ProjectTesteeTableBody.RowDataDesc> rows = templateFormDetailVo.getRows();
                                    if (CollectionUtil.isNotEmpty(rows)) {
                                        html.append("<tbody style='").append(tableTheadAndTbodyStyle).append("'>");
                                        for (ProjectTesteeTableBody.RowDataDesc row : rows) {
                                            html.append("<tr style='").append(tableTrStyle).append("'>");
                                            List<ProjectTesteeTableBody.ProjectTesteeTableData> rowData = row.getRowData();
                                            if (CollectionUtil.isNotEmpty(rowData)) {
                                                for (int i1 = 0; i1 < rowData.size() - 1; i1++) {
                                                    if (i1 == 0) {
//                                                        // 第一行是编号
                                                        //html.append("<td style='").append(tableTdStyle).append("'>").append(i1 + 1).append("</td>");
                                                    } else {
                                                        html.append("<td style='").append(tableTdStyle).append("'>").append(rowData.get(i1).getFieldValue()).append("</td>");
                                                    }
                                                }
                                            }
                                            html.append("</tr>");
                                        }
                                        html.append("</tbody>");
                                    }
                                    html.append("</table>");
                                }
                            }
                        }
                        html.append("</div>");
                        // 只在不是最后一个表单时添加分页符
                        if (f < checkFormList.size() - 1) {
                            html.append("<div style='page-break-after: always;'></div>");
                        }
                    }
                }
                html.append("</div>");
                // 只在不是最后一个访视时添加分页符
                if (x < exportList.size() - 1) {
                    html.append("<div style='page-break-after: always;'></div>");
                }
            }
            Map<String, Object> data = new HashMap<>();
            data.put("projectName", projectVo.getName());
            data.put("version", "版本号：V1 版本日期："+ DateUtil.getCurrentDate());
            data.put("testeeCode", testeeInfo.getCode());
            data.put("orgName", systemOrgInfo.getName());
            /*Document document = Jsoup.parse(html.toString());
            String htmlValue = document.body().html();*/
            data.put("content", html.toString());
            
            String header = "编号:"+ testeeInfo.getCode() + " 姓名:"+ testeeInfo.getRealName();
            //String header = "<div class='header' style='border-bottom: 1px solid #000;overflow:hidden'><span style='font-family:SimSun;float: left;'>参与者编号:" + testeeInfoCode + "</span><span style='font-family:SimSun; float:right;'>姓名:" + realName + "</span></div>";
            
            ByteArrayOutputStream outputStream;
            try {
                outputStream = PDFTemplateUtil.createPDF(header, data, "testee.ftl");
                byteFileMap.put(testeeInfo.getCode() + Constants.EXPORT_FILE_SUFFIX_PDF, outputStream);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                e.printStackTrace();
            }
        }
        String basePath = storageConfig.getUploadFolder().concat(storageConfig.getRootPath()).concat("/").concat(projectTesteeExportParam.getProjectId());
        String targetPath = basePath.concat("/").concat(projectTesteeExportParam.getFileUrlName() + Constants.EXPORT_FILE_SUFFIX_ZIP);
        // 将ByteArrayOutputStream中的数据写入本地文件
        FileOutputStream fileOutputStream = null;

        try {

            if (!new File(basePath).exists()) {
                new File(basePath).mkdirs();
            }
            ByteArrayOutputStream outputStream = ZipUtils.downZipFile(byteFileMap);
            if (outputStream == null) {
                throw new RuntimeException("ZIP文件生成失败");
            }
            fileOutputStream = new FileOutputStream(targetPath);
            outputStream.writeTo(fileOutputStream);
            fileOutputStream.flush();
            fileOutputStream.close();
            String fileUrl = storageConfig.getViewUrl() + Constants.SYSTEM_VIEW_PATH + storageConfig.getRootPath() + "/" + projectTesteeExportParam.getProjectId() + "/" + projectTesteeExportParam.getFileUrlName() + Constants.EXPORT_FILE_SUFFIX_ZIP;
            projectTesteeExportParam.setFileUrlName(fileUrl);
            this.updateExportInfo(exportFileId,projectTesteeExportParam.getFileUrlName(),BusinessConfig.TESTEE_EXPORT_STATUS_0);
        } catch (Exception exception) {
            this.updateExportInfo(exportFileId,null,BusinessConfig.TESTEE_EXPORT_STATUS_2);
            exception.printStackTrace();
        } finally {
            if (fileOutputStream != null) {
                try {
                    fileOutputStream.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        }catch (Exception e){
            // 更新参与者导出信息(已完成...)
            this.updateExportInfo(exportFileId,null,BusinessConfig.TESTEE_EXPORT_STATUS_2);
            e.printStackTrace();
        }
    }

    /**
     * 判断是否应该使用优化版本
     * 支持常量控制和参与者数量判断
     *
     * 重构说明（2025-07-25）：
     * - 增加强制使用高性能版本的控制
     * - 解决标准版本导出卡住的问题（如参与者0069）
     * - 默认使用高性能版本，确保导出稳定性
     */
    private boolean shouldUseOptimizedVersion(ProjectTesteeExportParam param) {
        // 如果强制使用高性能版本，则直接返回true
        if (FORCE_HIGH_PERFORMANCE_VERSION) {
            log.info("强制使用高性能版本导出，确保导出稳定性");
            return param.getTesteeIds() != null;
        }

        // 如果常量设置为默认使用优化版本，则直接返回true
        if (USE_OPTIMIZED_VERSION_BY_DEFAULT) {
            log.debug("根据常量配置使用优化版本导出");
            return param.getTesteeIds() != null;
        }

        // 否则根据参与者数量判断：当参与者数量大于等于5个时使用优化版本
        boolean useOptimized = param.getTesteeIds() != null && param.getTesteeIds().size() >= 5;
        log.debug("根据参与者数量({})判断是否使用优化版本: {}",
                 param.getTesteeIds() != null ? param.getTesteeIds().size() : 0, useOptimized);
        return useOptimized;
    }

    /**
     * 导出excel 文件 - 重构版本，解决N+1查询和性能问题
     * @param projectTesteeExportParam 导出参数
     * @param excelType 文件类型
     * @param uuid 导出任务ID
     */
    private void exportExcelTestee(ProjectTesteeExportParam projectTesteeExportParam, String excelType, Long uuid) {
        long startTime = System.currentTimeMillis();
        int totalTestees = projectTesteeExportParam.getTesteeIds().size();

        log.info("开始Excel导出，参与者数量: {}, 导出类型: {}", totalTestees, excelType);

        // 初始化进度
        updateProgress(uuid, 0, totalTestees, "开始Excel导出...");

        OutputStream outputStream = null;
        Workbook workbook = null;

        try {
            // 1. 准备文件路径
            String basePath = storageConfig.getUploadFolder().concat(storageConfig.getRootPath()).concat("/").concat(projectTesteeExportParam.getProjectId()).concat(Constants.SYSTEM_VIEW_EXCEL_PATH);

            // 生成直观的Excel文件名
            String excelFileName = generateExcelFileName(projectTesteeExportParam, excelType);
            String targetPath = basePath.concat(excelFileName);

            updateProgress(uuid, 0, totalTestees, "创建导出文件...");

            // 确保目录存在
            File baseDir = new File(basePath);
            if (!baseDir.exists()) {
                boolean created = baseDir.mkdirs();
                log.info("创建导出目录: {}, 结果: {}", basePath, created);
            }

            File file = new File(targetPath);
            outputStream = Files.newOutputStream(file.toPath());

            // 2. 预加载基础数据，避免重复查询 (0-10%)
            updateProgressWithStage(uuid, 0, totalTestees, "预加载基础数据...", ExcelExportStage.PRELOAD_DATA);
            ExcelExportDataCache dataCache = preloadExcelExportData(projectTesteeExportParam, uuid);

            // 3. 创建导出表头 (10-15%)
            updateProgressWithStage(uuid, 0, totalTestees, "构建表头结构...", ExcelExportStage.BUILD_HEADER);
            List<ExcelExportEntity> exportTitle = createExportTitle();

            // 4. 批量获取表头和数据，解决N+1查询问题 (15-85%)
            updateProgressWithStage(uuid, 0, totalTestees, "批量加载数据...", ExcelExportStage.LOAD_DATA);
            List<Map<String, Object>> dataList = new ArrayList<>();
            initTitleAndDataOptimized(dataCache, projectTesteeExportParam, exportTitle, dataList, uuid);

            // 5. 构建Excel工作簿 (85-100%)
            updateProgressWithStage(uuid, 0, totalTestees, "生成Excel文件...", ExcelExportStage.GENERATE_FILE);
            List<Map<String, Object>> sheetsList = new ArrayList<>();

            ExportParams params = new ExportParams(dataCache.projectVo.getName(), dataCache.projectVo.getName(), ExcelType.XSSF);
            params.setCreateHeadRows(true);
            params.setSheetName(dataCache.projectVo.getName());
            params.setStyle(ExcelStyleUtil.class);

            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("title", params);
            dataMap.put("entityList", exportTitle);
            dataMap.put("data", dataList);
            sheetsList.add(dataMap);

            // 生成Excel工作簿 (90-95%)
            updateProgressWithStage(uuid, 0.0, totalTestees, "创建Excel工作簿...", ExcelExportStage.GENERATE_FILE);
            workbook = exportMultiSheetExcel(sheetsList);

            // 写入文件 (95-98%)
            updateProgressWithStage(uuid, 0.5, totalTestees, "写入Excel文件...", ExcelExportStage.GENERATE_FILE);
            workbook.write(outputStream);

            // 6. 构建下载URL (98-100%)
            updateProgressWithStage(uuid, 0.8, totalTestees, "生成下载链接...", ExcelExportStage.GENERATE_FILE);
            String fileUrl = storageConfig.getViewUrl() + Constants.SYSTEM_VIEW_PATH + storageConfig.getRootPath() + "/" +
                           projectTesteeExportParam.getProjectId().concat(Constants.SYSTEM_VIEW_EXCEL_PATH) + excelFileName;

            // 7. 更新导出状态为完成 (100%)
            updateExportInfo(uuid, fileUrl, BusinessConfig.TESTEE_EXPORT_STATUS_0);
            updateProgressWithStage(uuid, 1.0, totalTestees, "导出完成", ExcelExportStage.GENERATE_FILE);

            long endTime = System.currentTimeMillis();
            log.info("Excel导出完成，耗时: {}ms, 参与者数量: {}, 文件大小: {} bytes",
                    endTime - startTime, totalTestees, file.length());

        } catch (Exception e) {
            log.error("Excel导出失败，参与者数量: {}", totalTestees, e);
            updateExportInfo(uuid, null, BusinessConfig.TESTEE_EXPORT_STATUS_2);
            updateProgress(uuid, 0, totalTestees, "导出失败: " + e.getMessage());
        } finally {
            // 8. 确保资源释放
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.warn("关闭输出流失败", e);
                }
            }
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    log.warn("关闭工作簿失败", e);
                }
            }
        }
    }
  /**
     * 导出sas 文件
     *
     * @param projectTesteeExportParam
     * @param uuid
     */
    private void exportSas(ProjectTesteeExportParam projectTesteeExportParam, String excelType, Long uuid) {

        try {
            String basePath = storageConfig.getUploadFolder().concat(projectTesteeExportParam.getProjectId());
            // 生成直观的SAS文件名
            String sasFileName = generateExcelFileName(projectTesteeExportParam, excelType);
            String targetPath = basePath.concat("/").concat(sasFileName);

            FileWriter writer = null;
            try {
                if (!new File(basePath).exists()) {
                    new File(basePath).mkdirs();
                }
                File file = new File(targetPath);
                writer = new FileWriter(file);
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            }
            /*------------------------------------------------导出表头分割线-----------------------------------------------------*/

            // 创建一个导出表头的集合
            List<ExcelExportEntity> exportTitle = createExportTitle();
            // 表格数据
            List<Map<String, Object>> dataList = new ArrayList<>();

            // 项目信息
            ProjectVo projectVo = projectBaseManageService.getProjectViewInfo(projectTesteeExportParam.getProjectId());

            // 获取表头和表格数据
            initTitleAndData(projectVo,projectTesteeExportParam, exportTitle, dataList);

            writer.write("DATA Source;");
            writer.write(System.getProperty("line.separator"));
            writer.write("INPUT");

            for (int i = 0; i < exportTitle.size(); i++) {
                ExcelExportEntity excelExportEntity = exportTitle.get(i);
                writer.write(" "+excelExportEntity.getName()+" $");
            }
            writer.write(";");
            writer.write(System.getProperty("line.separator"));
            writer.write("DATALINES;");
            writer.write(System.getProperty("line.separator"));


            if (CollectionUtil.isNotEmpty(dataList)){
                for (Map<String, Object> map : dataList) {
                    for (int i = 0; i < exportTitle.size(); i++) {
                        ExcelExportEntity excelExportEntity = exportTitle.get(i);
                        Object key = excelExportEntity.getKey();
                        Object fileValue = map.get(key);
                        String v = ObjectUtil.isEmpty(fileValue)?".":fileValue.toString();
                        writer.write(" "+v);
                    }
                    writer.write(System.getProperty("line.separator"));
                }
            }
            writer.write(";");
            writer.write(System.getProperty("line.separator"));
            writer.write("RUN;");
            writer.write(System.getProperty("line.separator"));
            writer.write("PROC PRINT DATA=Source;");
            writer.write(System.getProperty("line.separator"));
            writer.write("RUN;");

            try {
                // 更新参与者导出信息(已完成...)
                projectTesteeExportParam.setFileUrlName(storageConfig.getViewUrl() + Constants.SYSTEM_VIEW_PATH + projectTesteeExportParam.getProjectId()+"/"+ sasFileName);
                this.updateExportInfo(uuid,projectTesteeExportParam.getFileUrlName(),BusinessConfig.TESTEE_EXPORT_STATUS_0);
            } finally {
                try {
                    writer.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            this.updateExportInfo(uuid,null,BusinessConfig.TESTEE_EXPORT_STATUS_2);
        }
    }
/**
     * 导出sav 文件
     *
     * @param projectTesteeExportParam
     * @param uuid
     */
    private void exportSav(ProjectTesteeExportParam projectTesteeExportParam, String excelType, Long uuid) {

        try {
            String basePath = storageConfig.getUploadFolder().concat(projectTesteeExportParam.getProjectId());
            // 生成直观的SAV文件名
            String savFileName = generateExcelFileName(projectTesteeExportParam, excelType);
            String targetPath = basePath.concat("/").concat(savFileName);
            OutputStream out=null;
            try {
                if (!new File(basePath).exists()) {
                    new File(basePath).mkdirs();
                }
                File file = new File(targetPath);
                out = new FileOutputStream(file);
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            }
            /*------------------------------------------------导出表头分割线-----------------------------------------------------*/

            // 创建一个导出表头的集合
            List<ExcelExportEntity> exportTitle = createExportTitle();
            // 表格数据
            List<Map<String, Object>> dataList = new ArrayList<>();

            // 项目信息
            ProjectVo projectVo = projectBaseManageService.getProjectViewInfo(projectTesteeExportParam.getProjectId());

            // 获取表头和表格数据
            initTitleAndData(projectVo,projectTesteeExportParam, exportTitle, dataList);
            if (out!=null) {

                SPSSWriter outSPSS = new SPSSWriter(out, "utf-8");
                outSPSS.setCalculateNumberOfCases(false);
                outSPSS.addDictionarySection(-1);

                for (int i = 0; i < exportTitle.size(); i++) {
                    ExcelExportEntity excelExportEntity = exportTitle.get(i);
                    outSPSS.addStringVar("sav"+excelExportEntity.getKey().toString(), 150, excelExportEntity.getName());
                }

                outSPSS.addDataSection();
                if (CollectionUtil.isNotEmpty(dataList)) {
                    for (Map<String, Object> map : dataList) {
                        for (int i = 0; i < exportTitle.size(); i++) {
                             ExcelExportEntity excelExportEntity = exportTitle.get(i);
                            Object key = excelExportEntity.getKey();
                            Object fileValue = map.get(key);
                            String v = ObjectUtil.isEmpty(fileValue) ? " " : fileValue.toString();
                            outSPSS.addData(v);
                        }
                    }
                }
                outSPSS.addFinishSection();
                try {
                    // 更新参与者导出信息(已完成...)
                    projectTesteeExportParam.setFileUrlName(storageConfig.getViewUrl() + Constants.SYSTEM_VIEW_PATH + projectTesteeExportParam.getProjectId() + "/" + savFileName);
                    this.updateExportInfo(uuid, projectTesteeExportParam.getFileUrlName(), BusinessConfig.TESTEE_EXPORT_STATUS_0);
                } finally {
                    try {
                        out.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            this.updateExportInfo(uuid,null,BusinessConfig.TESTEE_EXPORT_STATUS_2);
        }
    }

    /**
     * Excel导出阶段枚举 - 用于精确控制进度显示
     */
    public enum ExcelExportStage {
        INIT(0, 5, "初始化导出任务"),
        PRELOAD_DATA(5, 15, "预加载基础数据"),
        BUILD_HEADER(15, 20, "构建表头结构"),
        LOAD_DATA(20, 80, "批量加载数据"),
        PROCESS_DATA(80, 90, "处理参与者数据"),
        GENERATE_FILE(90, 100, "生成Excel文件");

        private final int startPercent;
        private final int endPercent;
        private final String description;

        ExcelExportStage(int startPercent, int endPercent, String description) {
            this.startPercent = startPercent;
            this.endPercent = endPercent;
            this.description = description;
        }

        public int getStartPercent() { return startPercent; }
        public int getEndPercent() { return endPercent; }
        public String getDescription() { return description; }

        /**
         * 计算当前阶段的进度百分比
         * @param currentProgress 当前阶段的进度 (0.0 - 1.0)
         * @return 总体进度百分比
         */
        public int calculatePercent(double currentProgress) {
            if (currentProgress < 0) currentProgress = 0;
            if (currentProgress > 1) currentProgress = 1;
            return (int) (startPercent + (endPercent - startPercent) * currentProgress);
        }
    }

    /**
     * Excel导出数据缓存类 - 存储预加载的数据，避免重复查询
     */
    private static class ExcelExportDataCache {
        ProjectVo projectVo;
        List<ProjectExportFlowParam> exportList;
        ProjectOrgInfo projectOrgInfo;
        Organization systemOrgInfo;
        List<ProjectTesteeInfo> testeeList;
        Map<String, ProjectVisitVo> visitConfigMap;
        Map<String, TemplateFormConfigVo> formConfigMap;
        Map<String, List<TemplateFormDetailVo>> formDetailMap;
        Map<String, List<TemplateTableVo>> tableRowMap;
    }

    /**
     * 预加载Excel导出所需的基础数据，避免N+1查询问题
     * @param projectTesteeExportParam 导出参数
     * @param exportFileId 导出文件ID，用于进度更新
     * @return 数据缓存对象
     */
    private ExcelExportDataCache preloadExcelExportData(ProjectTesteeExportParam projectTesteeExportParam, Long exportFileId) {
        log.info("开始预加载Excel导出基础数据");
        long startTime = System.currentTimeMillis();

        ExcelExportDataCache cache = new ExcelExportDataCache();

        try {
            // 1. 加载项目信息 (0-20%)
            updateProgressWithStage(exportFileId, 0.0, 0, "加载项目信息...", ExcelExportStage.PRELOAD_DATA);
            cache.projectVo = projectBaseManageService.getProjectViewInfo(projectTesteeExportParam.getProjectId());
            log.debug("加载项目信息: {}", cache.projectVo.getName());

            // 2. 加载导出流程配置 (20-40%)
            updateProgressWithStage(exportFileId, 0.2, 0, "加载导出流程配置...", ExcelExportStage.PRELOAD_DATA);
            cache.exportList = extracted(projectTesteeExportParam);
            log.debug("加载导出流程数量: {}", cache.exportList.size());

            // 3. 加载机构信息 (40-50%)
            updateProgressWithStage(exportFileId, 0.4, 0, "加载机构信息...", ExcelExportStage.PRELOAD_DATA);
            cache.projectOrgInfo = organizationService.getProjectOrgInfo(projectTesteeExportParam.getOrgId());
            cache.systemOrgInfo = organizationService.getSystemOrganizationInfo(cache.projectOrgInfo.getOrgId().toString());
            log.debug("加载机构信息: {}", cache.systemOrgInfo.getName());

            // 4. 批量加载参与者信息 (50-60%)
            updateProgressWithStage(exportFileId, 0.5, 0, "加载参与者信息...", ExcelExportStage.PRELOAD_DATA);
            cache.testeeList = projectTesteeInfoService.getProjectTesteeListByIds(
                projectTesteeExportParam.getProjectId(),
                projectTesteeExportParam.getOrgId(),
                projectTesteeExportParam.getTesteeIds()
            );
            log.debug("加载参与者数量: {}", cache.testeeList.size());

            // 5. 预加载访视配置信息到缓存 (60-70%)
            updateProgressWithStage(exportFileId, 0.6, 0, "预加载访视配置...", ExcelExportStage.PRELOAD_DATA);
            cache.visitConfigMap = new HashMap<>();
            for (ProjectExportFlowParam exportFlow : cache.exportList) {
                String visitId = exportFlow.getVisitId();
                String cacheKey = "visit_config_" + visitId;

                // 先尝试从Redis缓存获取
                ProjectVisitVo visitConfig = getObjectFromCache(cacheKey, ProjectVisitVo.class);
                if (visitConfig == null) {
                    visitConfig = projectVisitConfigService.getProjectVisitBaseConfigByVisitId(visitId);
                    // 缓存访视配置，有效期1小时
                    cacheToRedis(cacheKey, visitConfig, 3600);
                }
                cache.visitConfigMap.put(visitId, visitConfig);
            }
            log.debug("预加载访视配置数量: {}", cache.visitConfigMap.size());

            // 6. 预加载表单配置信息到缓存 (70-100%)
            updateProgressWithStage(exportFileId, 0.7, 0, "预加载表单配置...", ExcelExportStage.PRELOAD_DATA);
            cache.formConfigMap = new HashMap<>();
            cache.formDetailMap = new HashMap<>();
            cache.tableRowMap = new HashMap<>();

            int totalForms = cache.exportList.stream()
                .mapToInt(flow -> flow.getForms().size())
                .sum();
            int processedForms = 0;

            for (ProjectExportFlowParam exportFlow : cache.exportList) {
                for (ProjectExportFiledParam form : exportFlow.getForms()) {
                    String formId = form.getFormId();

                    // 更新表单加载进度
                    double formProgress = 0.7 + 0.3 * ((double) processedForms / totalForms);
                    updateProgressWithStage(exportFileId, formProgress, 0,
                        String.format("加载表单配置 (%d/%d)...", processedForms + 1, totalForms),
                        ExcelExportStage.PRELOAD_DATA);

                    // 加载表单基础配置
                    String formCacheKey = "form_config_" + formId;
                    TemplateFormConfigVo formConfig = getObjectFromCache(formCacheKey, TemplateFormConfigVo.class);
                    if (formConfig == null) {
                        formConfig = templateConfigService.getTemplateFormConfigBaseInfoByFormId(formId);
                        cacheToRedis(formCacheKey, formConfig, 3600);
                    }
                    cache.formConfigMap.put(formId, formConfig);

                    // 加载表单详细配置
                    String formDetailCacheKey = "form_detail_" + formId;
                    List<TemplateFormDetailVo> formDetails = getListFromCache(formDetailCacheKey, TemplateFormDetailVo.class);
                    if (formDetails == null) {
                        formDetails = templateConfigService.getTemplateFormDetailConfigListByFormId(null, formId, null, "1", "1", "");
                        cacheToRedis(formDetailCacheKey, formDetails, 3600);
                    }
                    cache.formDetailMap.put(formId, formDetails);

                    // 预加载表格行配置
                    for (TemplateFormDetailVo detail : formDetails) {
                        if (BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE.equals(detail.getType())) {
                            String tableRowCacheKey = "table_row_" + formId + "_" + detail.getId();
                            List<TemplateTableVo> tableRows = getListFromCache(tableRowCacheKey, TemplateTableVo.class);
                            if (tableRows == null) {
                                tableRows = projectTesteeTableService.getProjectTesteeTableRowHead(formId, detail.getId().toString(), false);
                                cacheToRedis(tableRowCacheKey, tableRows, 3600);
                            }
                            cache.tableRowMap.put(formId + "_" + detail.getId(), tableRows);
                        }
                    }

                    processedForms++;
                }
            }
            log.debug("预加载表单配置数量: {}, 表单详情数量: {}, 表格行配置数量: {}",
                     cache.formConfigMap.size(), cache.formDetailMap.size(), cache.tableRowMap.size());

            long endTime = System.currentTimeMillis();
            log.info("Excel导出基础数据预加载完成，耗时: {}ms", endTime - startTime);

            return cache;

        } catch (Exception e) {
            log.error("预加载Excel导出基础数据失败", e);
            throw new RuntimeException("预加载基础数据失败", e);
        }
    }

    /**
     * 获取表头和表格数据 - 原始方法，保持兼容性
     * @param projectVo 项目信息
     * @param projectTesteeExportParam 导出参数
     * @param exportTitle 导出表头
     * @param dataList 数据列表
     */
    private void initTitleAndData(ProjectVo projectVo,ProjectTesteeExportParam projectTesteeExportParam, List<ExcelExportEntity> exportTitle, List<Map<String, Object>> dataList) {
        // 表格sheet
        ExportParams params = new ExportParams(projectVo.getName(), projectVo.getName(), ExcelType.XSSF);
        params.setCreateHeadRows(true);
        params.setSheetName(projectVo.getName());
        params.setStyle(ExcelStyleUtil.class);
        if (CollectionUtil.isNotEmpty(projectTesteeExportParam.getExportList())) {
            List<ProjectExportFlowParam> exportList = extracted(projectTesteeExportParam);
            for (int x = 0; x < exportList.size(); x++) {
                ProjectExportFlowParam ex = exportList.get(x);
                // 设置表头 - 调整序号位置到流程名称前面
                exportTitle.add(new ExcelExportEntity("序号", "xh_" + x, 20));
                exportTitle.add(new ExcelExportEntity("流程名称", "visit_" + x, 20));

                // 导出的表单信息
                List<ProjectExportFiledParam> forms = ex.getForms();
                if (CollectionUtil.isNotEmpty(exportList)) {
                    for (int f = 0; f < forms.size(); f++) {
                        ProjectExportFiledParam form = forms.get(f);
                        exportTitle.add(new ExcelExportEntity("表单名称", "form_" + ex.getVisitId() + "_" + f, 20));
                        // 导出表单id
                        String formId = form.getFormId();
                        // 导出字段ids
                        List<String> filedIds = form.getVariableIds();
                        if (projectTesteeExportParam.getExportAll() || CollectionUtil.isNotEmpty(filedIds)) {
                            // 获取表单变量字段
                            List<TemplateFormDetailVo> formDetails = templateConfigService.getTemplateFormDetailConfigListByFormId(null, formId, null, "1", "1", "");

                            formDetails.forEach(detail -> {

                                if (!BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE.equals(detail.getType())) {
                                    if (projectTesteeExportParam.getExportAll() || filedIds.contains(detail.getId().toString())) {
                                        // 如果不是表格，是普通变量直接拼接到把变量拼接到表头中
                                        // 使用唯一字段键，避免字段名冲突
                                        String uniqueFieldKey = generateUniqueFieldKey(ex.getVisitId(), formId, detail.getFieldName());
                                        exportTitle.add(new ExcelExportEntity(detail.getLabel(), uniqueFieldKey, 20));
                                        log.debug("原始版本添加表头: {} -> {}", detail.getLabel(), uniqueFieldKey);
                                    }
                                } else {
                                    // 如果是表格，获取表格的变量
                                    List<TemplateTableVo> rowList = projectTesteeTableService.getProjectTesteeTableRowHead(formId.toString(), detail.getId().toString(), false);
                                    // 获取参与者最大的数据行号
                                    int maxRow = 0;
                                    // 循环每一个参与者进行查询数据。
                                    for (String testeeId : projectTesteeExportParam.getTesteeIds()) {
                                        List<ProjectTesteeTableResultExportVo> tableData = projectTesteeTableService.getProjectTesteeTableRecordsWithVariableIds(projectTesteeExportParam.getProjectId(), projectTesteeExportParam.getOrgId(), null, formId.toString(), detail.getId().toString(), null, testeeId);
                                        if (CollectionUtil.isNotEmpty(tableData)) {
                                            // 表格数据根据tableSort 进行分组。分组成每一行
                                            Map<String, List<ProjectTesteeTableResultExportVo>> tableDateMap = tableData.stream().collect(Collectors.groupingBy(ProjectTesteeTableResultExportVo::getRowNumber, LinkedHashMap::new, Collectors.toList()));
                                            if (tableDateMap.size() > maxRow) {
                                                maxRow = tableDateMap.size();
                                            }
                                        }
                                    }
                                    List<TemplateTableVo> rows = new ArrayList<>();
                                    if (CollectionUtil.isNotEmpty(rowList)) {
                                        rowList.forEach(row -> {
                                            if (projectTesteeExportParam.getExportAll() || filedIds.contains(row.getId().toString())) {
                                                rows.add(row);
                                            }
                                        });
                                    }
                                    if (rows.size() > 0 && maxRow > 0) {
                                        for (int i = 0; i < maxRow; i++) {
                                            for (TemplateTableVo row : rows) {
                                                // 使用唯一字段键构建表格表头，避免字段名冲突
                                                String uniqueTableKey = generateUniqueFieldKey(ex.getVisitId(), formId, row.getFieldName() + "-" + i);
                                                // 优化表格标题显示格式：字段名称（第几行）
                                                String tableTitle = row.getLabel() + "（第" + (i + 1) + "行）";
                                                exportTitle.add(new ExcelExportEntity(tableTitle, uniqueTableKey, 20));
                                                log.debug("原始版本添加表格表头: {} -> {}", tableTitle, uniqueTableKey);
                                            }
                                        }
                                    }
                                }
                            });
                        }
                    }
                }
            }
            /*------------------------------------------------导出数据获取分割线-----------------------------------------------------*/

            // 获取研究中心
            ProjectOrgInfo projectOrgInfo = organizationService.getProjectOrgInfo(projectTesteeExportParam.getOrgId());
            Organization systemOrgInfo = organizationService.getSystemOrganizationInfo(projectOrgInfo.getOrgId().toString());
            List<ProjectTesteeInfo> testeeIdList = projectTesteeInfoService.getProjectTesteeListByIds(projectTesteeExportParam.getProjectId(), projectTesteeExportParam.getOrgId(), projectTesteeExportParam.getTesteeIds());
            for (int k = 0; k < testeeIdList.size(); k++) {
                Map<String, Object> map = new HashMap<>();
                // 参与者的基本信息
                ProjectTesteeInfo testeeInfo = testeeIdList.get(k);
                map.put("code", testeeInfo.getCode());
                map.put("orgName", systemOrgInfo.getName());
                for (int x = 0; x < exportList.size(); x++) {
                    ProjectExportFlowParam ex = exportList.get(x);

                    // 获取访视流程的信息
                    // 方式id
                    String visitId = ex.getVisitId();
                    ProjectVisitVo projectVisitBaseConfig = projectVisitConfigService.getProjectVisitBaseConfigByVisitId(visitId.toString());
                    // 给每一个参与者设置访视信息 - 使用下标从1开始作为序号
                    map.put("xh_" + x, x + 1);
                    map.put("visit_" + x, projectVisitBaseConfig.getVisitName());

                    // 导出的表单信息
                    List<ProjectExportFiledParam> forms = ex.getForms();
                    if (CollectionUtil.isNotEmpty(exportList)) {
                        for (int f = 0; f < forms.size(); f++) {
                            ProjectExportFiledParam form = forms.get(f);
                            // 导出表单id
                            String formId = form.getFormId();
                            TemplateFormConfigVo templateFormConfigVo = templateConfigService.getTemplateFormConfigBaseInfoByFormId(formId.toString());
                            map.put("form_" + visitId + "_" + f, templateFormConfigVo.getFormName());

                            List<TemplateFormDetailVo> fromDataList = projectTesteeInfoService.getTesteeVisitFormDetail(projectTesteeExportParam.getProjectId(),
                                    null, visitId.toString(), formId.toString(), "", null, "", testeeInfo.getId().toString(), null, "");
                            if (CollectionUtil.isNotEmpty(fromDataList)) {
                                for (TemplateFormDetailVo resultVo : fromDataList) {
                                    if (!BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE.equals(resultVo.getType())) {
                                        // 变量数据
                                        if (BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO.equals(resultVo.getType())
                                                || BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(resultVo.getType())
                                                || BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT.equals(resultVo.getType())
                                        ) {
                                            // 单选，多选，下拉
                                            // 字典信息
                                            if (StringUtils.isNotBlank(resultVo.getFieldValue())) {
                                                List<TemplateFormDictionaryVo> dictionaryList = resultVo.getTemplateFormDictionaryList();
                                                if (CollectionUtil.isNotEmpty(dictionaryList)) {
                                                    // 构建唯一字段键，避免字段名冲突
                                                    String uniqueFieldKey = generateUniqueFieldKey(visitId, formId, resultVo.getFieldName());
                                                    if (BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(resultVo.getType())) {
                                                        // 使用增强的复选框处理方法
                                                        processCheckboxFieldValue(resultVo.getFieldValue(), uniqueFieldKey, dictionaryList, map);
                                                    } else {
                                                        for (TemplateFormDictionaryVo dictionaryVo : dictionaryList) {
                                                            if (dictionaryVo.getId().equals(resultVo.getFieldValue())) {
                                                                map.put(uniqueFieldKey, dictionaryVo.getName());
                                                                log.debug("原始版本设置选择字段: {} = {}", uniqueFieldKey, dictionaryVo.getName());
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        } else if (BusinessConfig.PROJECT_VISIT_CRF_FORM_IMAGE.equals(resultVo.getType())) {
                                            // 处理图片变量
                                            processImageVariableForExcel(resultVo, map);
                                        } else if (BusinessConfig.PROJECT_VISIT_CRF_FORM_IMAGE_VIEW.equals(resultVo.getType())) {
                                            // 处理图片展示变量
                                            processImageViewVariableForExcel(resultVo, map);
                                        } else {
                                            // 构建唯一字段键，避免字段名冲突
                                            String uniqueFieldKey = generateUniqueFieldKey(visitId, formId, resultVo.getFieldName());
                                            map.put(uniqueFieldKey, resultVo.getFieldValue());

                                            // 特别记录ECOG评分日期字段的赋值情况
                                            if (resultVo.getFieldName().contains("ECOG评分日期") ||
                                                (resultVo.getFieldName().contains("ECOG") && resultVo.getFieldName().contains("日期"))) {
                                                log.info("【原始版本-ECOG评分日期字段赋值】字段键: {}, 字段值: {}, 访视: {}, 表单: {}, 原字段名: {}",
                                                        uniqueFieldKey, resultVo.getFieldValue(), visitId, formId, resultVo.getFieldName());
                                            } else {
                                                log.debug("原始版本设置普通字段: {} = {}", uniqueFieldKey, resultVo.getFieldValue());
                                            }
                                        }
                                    } else {
                                        // 表格数据
                                        List<ProjectTesteeTableBody.RowDataDesc> rows = resultVo.getRows();
                                        if (CollectionUtil.isNotEmpty(rows)) {

                                            for (int i = 0; i < rows.size(); i++) {
                                                List<ProjectTesteeTableBody.ProjectTesteeTableData> rowDataList = rows.get(i).getRowData();
                                                if (CollectionUtil.isNotEmpty(rowDataList)) {
                                                    for (int n = 0; n < rowDataList.size(); n++) {
                                                        ProjectTesteeTableBody.ProjectTesteeTableData tableData = rowDataList.get(n);
                                                        // 构建唯一表格字段键，避免字段名冲突
                                                        String uniqueTableKey = generateUniqueFieldKey(visitId, formId, tableData.getFieldName() + "-" + i);
                                                        map.put(uniqueTableKey, tableData.getFieldValue());
                                                        log.debug("原始版本设置表格字段: {} = {}", uniqueTableKey, tableData.getFieldValue());
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                dataList.add(map);
            }
        }
    }

    /**
     * 优化版本的表头和数据初始化方法 - 解决N+1查询问题
     * @param dataCache 预加载的数据缓存
     * @param projectTesteeExportParam 导出参数
     * @param exportTitle 导出表头
     * @param dataList 数据列表
     * @param exportFileId 导出文件ID，用于进度更新
     */
    private void initTitleAndDataOptimized(ExcelExportDataCache dataCache,
                                         ProjectTesteeExportParam projectTesteeExportParam,
                                         List<ExcelExportEntity> exportTitle,
                                         List<Map<String, Object>> dataList,
                                         Long exportFileId) {
        log.info("开始优化版本的表头和数据初始化");
        long startTime = System.currentTimeMillis();

        try {
            if (CollectionUtil.isEmpty(projectTesteeExportParam.getExportList())) {
                log.warn("导出列表为空，跳过数据初始化");
                return;
            }

            // 1. 构建表头结构
            buildExportTitleOptimized(dataCache, projectTesteeExportParam, exportTitle);

            // 2. 批量加载所有参与者的表单数据，解决N+1查询问题
            Map<String, Map<String, List<TemplateFormDetailVo>>> allTesteeFormData =
                batchLoadTesteeFormDataForExcel(dataCache, projectTesteeExportParam, exportFileId);

            // 3. 构建数据行
            buildDataListOptimized(dataCache, projectTesteeExportParam, dataList, allTesteeFormData, exportFileId);

            long endTime = System.currentTimeMillis();
            log.info("优化版本表头和数据初始化完成，耗时: {}ms, 表头数量: {}, 数据行数: {}",
                    endTime - startTime, exportTitle.size(), dataList.size());

        } catch (Exception e) {
            log.error("优化版本表头和数据初始化失败", e);
            throw new RuntimeException("数据初始化失败", e);
        }
    }

    /**
     * 构建导出表头 - 优化版本
     */
    private void buildExportTitleOptimized(ExcelExportDataCache dataCache,
                                         ProjectTesteeExportParam projectTesteeExportParam,
                                         List<ExcelExportEntity> exportTitle) {
        log.debug("开始构建导出表头");

        for (int x = 0; x < dataCache.exportList.size(); x++) {
            ProjectExportFlowParam ex = dataCache.exportList.get(x);

            // 设置访视相关表头 - 调整序号位置到流程名称前面
            exportTitle.add(new ExcelExportEntity("序号", "xh_" + x, 20));
            exportTitle.add(new ExcelExportEntity("流程名称", "visit_" + x, 20));

            // 处理表单信息
            List<ProjectExportFiledParam> forms = ex.getForms();
            if (CollectionUtil.isNotEmpty(forms)) {
                for (int f = 0; f < forms.size(); f++) {
                    ProjectExportFiledParam form = forms.get(f);
                    String formId = form.getFormId();
                    List<String> filedIds = form.getVariableIds();

                    exportTitle.add(new ExcelExportEntity("表单名称", "form_" + ex.getVisitId() + "_" + f, 20));

                    if (projectTesteeExportParam.getExportAll() || CollectionUtil.isNotEmpty(filedIds)) {
                        // 从缓存获取表单详情
                        List<TemplateFormDetailVo> formDetails = dataCache.formDetailMap.get(formId);
                        if (CollectionUtil.isNotEmpty(formDetails)) {
                            buildFormTitleOptimized(formDetails, filedIds, projectTesteeExportParam,
                                                  exportTitle, dataCache, formId, ex.getVisitId());
                        }
                    }
                }
            }
        }

        log.debug("导出表头构建完成，表头数量: {}", exportTitle.size());
    }

    /**
     * 构建表单相关表头
     */
    private void buildFormTitleOptimized(List<TemplateFormDetailVo> formDetails,
                                       List<String> filedIds,
                                       ProjectTesteeExportParam projectTesteeExportParam,
                                       List<ExcelExportEntity> exportTitle,
                                       ExcelExportDataCache dataCache,
                                       String formId,
                                       String visitId) {

        for (TemplateFormDetailVo detail : formDetails) {
            if (!BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE.equals(detail.getType())) {
                // 普通变量 - 使用唯一字段键
                if (projectTesteeExportParam.getExportAll() || filedIds.contains(detail.getId().toString())) {
                    String uniqueFieldKey = generateUniqueFieldKey(visitId, formId, detail.getFieldName());
                    exportTitle.add(new ExcelExportEntity(detail.getLabel(), uniqueFieldKey, 20));
                    log.debug("添加表头: {} -> {}", detail.getLabel(), uniqueFieldKey);
                }
            } else {
                // 表格变量 - 从缓存获取表格行配置
                String tableRowKey = formId + "_" + detail.getId();
                List<TemplateTableVo> rowList = dataCache.tableRowMap.get(tableRowKey);

                if (CollectionUtil.isNotEmpty(rowList)) {
                    // 计算最大行数 - 优化查询
                    int maxRow = calculateMaxRowOptimized(projectTesteeExportParam, formId, detail.getId().toString());

                    List<TemplateTableVo> filteredRows = rowList.stream()
                        .filter(row -> projectTesteeExportParam.getExportAll() || filedIds.contains(row.getId().toString()))
                        .collect(Collectors.toList());

                    if (!filteredRows.isEmpty() && maxRow > 0) {
                        for (int i = 0; i < maxRow; i++) {
                            for (TemplateTableVo row : filteredRows) {
                                // 使用唯一字段键构建表格表头
                                String uniqueTableKey = generateUniqueFieldKey(visitId, formId, row.getFieldName() + "-" + i);
                                // 优化表格标题显示格式：字段名称（第几行）
                                String tableTitle = row.getLabel() + "（第" + (i + 1) + "行）";
                                exportTitle.add(new ExcelExportEntity(tableTitle, uniqueTableKey, 20));
                                log.debug("添加表格表头: {} -> {}", tableTitle, uniqueTableKey);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 优化版本的最大行数计算 - 减少重复查询
     */
    private int calculateMaxRowOptimized(ProjectTesteeExportParam projectTesteeExportParam,
                                       String formId, String detailId) {
        int maxRow = 0;

        // 批量查询所有参与者的表格数据
        try {
            for (String testeeId : projectTesteeExportParam.getTesteeIds()) {
                List<ProjectTesteeTableResultExportVo> tableData =
                    projectTesteeTableService.getProjectTesteeTableRecordsWithVariableIds(
                        projectTesteeExportParam.getProjectId(), projectTesteeExportParam.getOrgId(), null, formId, detailId,
                        null, testeeId);

                if (CollectionUtil.isNotEmpty(tableData)) {
                    Map<String, List<ProjectTesteeTableResultExportVo>> tableDateMap =
                        tableData.stream().collect(Collectors.groupingBy(
                            ProjectTesteeTableResultExportVo::getRowNumber,
                            LinkedHashMap::new,
                            Collectors.toList()));
                    maxRow = Math.max(maxRow, tableDateMap.size());
                }
            }
        } catch (Exception e) {
            log.warn("计算表格最大行数失败，formId: {}, detailId: {}", formId, detailId, e);
        }

        return maxRow;
    }


    /**
     * 批量加载所有参与者的表单数据 - 解决N+1查询问题
     */
    private Map<String, Map<String, List<TemplateFormDetailVo>>> batchLoadTesteeFormDataForExcel(
            ExcelExportDataCache dataCache, ProjectTesteeExportParam projectTesteeExportParam, Long exportFileId) {

        log.info("开始批量加载参与者表单数据");
        long startTime = System.currentTimeMillis();
        String projectOrgId = projectTesteeExportParam.getOrgId();
        FlowPlan flowPlan = flowPlanService.getPlanByProjectId(projectTesteeExportParam.getProjectId());
        
        Map<String, Map<String, List<TemplateFormDetailVo>>> allTesteeFormData = new HashMap<>();
        int totalTestees = dataCache.testeeList.size();
        int processedTestees = 0;

        try {
            for (ProjectTesteeInfo testeeInfo : dataCache.testeeList) {
                String testeeId = testeeInfo.getId().toString();
                Map<String, List<TemplateFormDetailVo>> testeeFormData = new HashMap<>();

                // 更新数据加载进度 (LOAD_DATA阶段: 20-80%)
                double stageProgress = (double) processedTestees / totalTestees;
                String statusMessage = String.format("加载参与者 %s 的表单数据 (%d/%d)",
                                                    testeeInfo.getCode(), processedTestees + 1, totalTestees);
                updateProgressWithStage(exportFileId, stageProgress, totalTestees, statusMessage, ExcelExportStage.LOAD_DATA);

                for (ProjectExportFlowParam exportFlow : dataCache.exportList) {
                    String visitId = exportFlow.getVisitId();

                    for (ProjectExportFiledParam form : exportFlow.getForms()) {
                        String formId = form.getFormId();
                        String key = visitId + "_" + formId;

                        try {
                            // 批量查询参与者的表单数据
                            List<TemplateFormDetailVo> formDetailList =
                                projectTesteeInfoService.getTesteeVisitFormDetail(
                                    projectTesteeExportParam.getProjectId(), flowPlan.getId().toString(), visitId, formId, "", null, projectOrgId, testeeId, null, "");

                            testeeFormData.put(key, formDetailList != null ? formDetailList : new ArrayList<>());

                        } catch (Exception e) {
                            log.warn("加载参与者 {} 的表单数据失败，visitId: {}, formId: {}",
                                    testeeInfo.getCode(), visitId, formId, e);
                            testeeFormData.put(key, new ArrayList<>());
                        }
                    }
                }

                allTesteeFormData.put(testeeId, testeeFormData);
                processedTestees++;
            }

            long endTime = System.currentTimeMillis();
            log.info("批量加载参与者表单数据完成，耗时: {}ms, 参与者数量: {}",
                    endTime - startTime, dataCache.testeeList.size());

            return allTesteeFormData;

        } catch (Exception e) {
            log.error("批量加载参与者表单数据失败", e);
            throw new RuntimeException("批量加载表单数据失败", e);
        }
    }

    /**
     * 构建数据列表 - 优化版本，包含进度更新和异常处理
     */
    private void buildDataListOptimized(ExcelExportDataCache dataCache,
                                      ProjectTesteeExportParam projectTesteeExportParam,
                                      List<Map<String, Object>> dataList,
                                      Map<String, Map<String, List<TemplateFormDetailVo>>> allTesteeFormData,
                                      Long exportFileId) {

        log.info("开始构建数据列表");
        int totalTestees = dataCache.testeeList.size();
        int processedCount = 0;

        for (ProjectTesteeInfo testeeInfo : dataCache.testeeList) {
            try {
                processedCount++;

                // 计算当前阶段的进度 (PROCESS_DATA阶段: 80-90%)
                double stageProgress = (double) processedCount / totalTestees;
                String statusMessage = String.format("正在处理参与者 %s (%d/%d)",
                                                    testeeInfo.getCode(), processedCount, totalTestees);
                updateProgressWithStage(exportFileId, stageProgress, totalTestees, statusMessage, ExcelExportStage.PROCESS_DATA);

                Map<String, Object> rowData = new HashMap<>();

                // 设置基础信息
                rowData.put("code", testeeInfo.getCode());
                rowData.put("orgName", dataCache.systemOrgInfo.getName());

                // 处理每个访视的数据
                buildTesteeVisitData(dataCache, projectTesteeExportParam, testeeInfo,
                                   allTesteeFormData, rowData);

                dataList.add(rowData);

                log.debug("完成参与者 {} 的数据构建", testeeInfo.getCode());

            } catch (Exception e) {
                log.error("处理参与者 {} 数据时发生错误，跳过该参与者继续处理", testeeInfo.getCode(), e);

                // 即使失败也要更新进度，确保进度条正常推进
                double stageProgress = (double) processedCount / totalTestees;
                String statusMessage = String.format("处理参与者 %s 失败，继续下一个 (%d/%d)",
                                                    testeeInfo.getCode(), processedCount, totalTestees);
                updateProgressWithStage(exportFileId, stageProgress, totalTestees, statusMessage, ExcelExportStage.PROCESS_DATA);

                // 添加错误数据行，避免数据不一致
                Map<String, Object> errorRowData = new HashMap<>();
                errorRowData.put("code", testeeInfo.getCode());
                errorRowData.put("orgName", dataCache.systemOrgInfo.getName());
                dataList.add(errorRowData);
            }
        }

        log.info("数据列表构建完成，成功处理: {}/{} 个参与者", processedCount, totalTestees);
    }

    /**
     * 构建单个参与者的访视数据
     */
    private void buildTesteeVisitData(ExcelExportDataCache dataCache,
                                    ProjectTesteeExportParam projectTesteeExportParam,
                                    ProjectTesteeInfo testeeInfo,
                                    Map<String, Map<String, List<TemplateFormDetailVo>>> allTesteeFormData,
                                    Map<String, Object> rowData) {

        String testeeId = testeeInfo.getId().toString();
        Map<String, List<TemplateFormDetailVo>> testeeFormData = allTesteeFormData.get(testeeId);

        if (testeeFormData == null) {
            log.warn("参与者 {} 的表单数据为空", testeeInfo.getCode());
            return;
        }

        for (int x = 0; x < dataCache.exportList.size(); x++) {
            ProjectExportFlowParam exportFlow = dataCache.exportList.get(x);
            String visitId = exportFlow.getVisitId();

            // 设置访视信息 - 使用下标从1开始作为序号
            ProjectVisitVo visitConfig = dataCache.visitConfigMap.get(visitId);
            if (visitConfig != null) {
                rowData.put("xh_" + x, x + 1);
                rowData.put("visit_" + x, visitConfig.getVisitName());
            }

            // 处理表单数据
            buildTesteeFormData(dataCache, projectTesteeExportParam, exportFlow,
                              testeeFormData, rowData, x);
        }
    }

    /**
     * 构建单个参与者的表单数据
     */
    private void buildTesteeFormData(ExcelExportDataCache dataCache,
                                   ProjectTesteeExportParam projectTesteeExportParam,
                                   ProjectExportFlowParam exportFlow,
                                   Map<String, List<TemplateFormDetailVo>> testeeFormData,
                                   Map<String, Object> rowData,
                                   int visitIndex) {

        String visitId = exportFlow.getVisitId();
        List<ProjectExportFiledParam> forms = exportFlow.getForms();

        if (CollectionUtil.isEmpty(forms)) {
            return;
        }

        for (int f = 0; f < forms.size(); f++) {
            ProjectExportFiledParam form = forms.get(f);
            String formId = form.getFormId();

            // 设置表单名称
            TemplateFormConfigVo formConfig = dataCache.formConfigMap.get(formId);
            if (formConfig != null) {
                rowData.put("form_" + visitId + "_" + f, formConfig.getFormName());
            }

            // 获取该参与者的表单数据
            String formDataKey = visitId + "_" + formId;
            List<TemplateFormDetailVo> formDetailList = testeeFormData.get(formDataKey);

            if (CollectionUtil.isNotEmpty(formDetailList)) {
                processFormDetailData(formDetailList, form.getVariableIds(),
                                    projectTesteeExportParam.getExportAll(), rowData, visitId, formId);
            }
        }
    }

    /**
     * 处理表单详细数据
     */
    private void processFormDetailData(List<TemplateFormDetailVo> formDetailList,
                                     List<String> variableIds,
                                     Boolean exportAll,
                                     Map<String, Object> rowData,
                                     String visitId,
                                     String formId) {

        for (TemplateFormDetailVo resultVo : formDetailList) {
            try {
                if (!BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE.equals(resultVo.getType())) {
                    // 处理普通变量
                    processNormalVariable(resultVo, variableIds, exportAll, rowData, visitId, formId);
                } else {
                    // 处理表格变量
                    processTableVariable(resultVo, rowData, visitId, formId);
                }
            } catch (Exception e) {
                log.warn("处理表单字段数据失败，字段: {}, 访视: {}, 表单: {}", resultVo.getFieldName(), visitId, formId, e);
            }
        }
    }

    /**
     * 处理普通变量数据
     */
    private void processNormalVariable(TemplateFormDetailVo resultVo,
                                     List<String> variableIds,
                                     Boolean exportAll,
                                     Map<String, Object> rowData,
                                     String visitId,
                                     String formId) {

        // 检查是否需要导出该字段
        if (!exportAll && (variableIds == null || !variableIds.contains(resultVo.getId().toString()))) {
            return;
        }

        String fieldValue = resultVo.getFieldValue();
        String fieldName = resultVo.getFieldName();

        // 构建唯一的字段键，避免不同表单间的字段名冲突
        String uniqueFieldKey = generateUniqueFieldKey(visitId, formId, fieldName);

        if (BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO.equals(resultVo.getType())
                || BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(resultVo.getType())
                || BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT.equals(resultVo.getType())) {

            // 处理选择类型字段
            processSelectTypeField(resultVo, rowData, uniqueFieldKey);

        } else if (BusinessConfig.PROJECT_VISIT_CRF_FORM_IMAGE.equals(resultVo.getType())) {
            // 处理图片变量
            processImageVariableForExcel(resultVo, rowData, uniqueFieldKey);
        } else if (BusinessConfig.PROJECT_VISIT_CRF_FORM_IMAGE_VIEW.equals(resultVo.getType())) {
            // 处理图片展示变量
            processImageViewVariableForExcel(resultVo, rowData, uniqueFieldKey);
        } else {
            // 处理其他类型字段
            rowData.put(uniqueFieldKey, fieldValue);

            // 特别记录ECOG评分日期字段的赋值情况
            if (fieldName.contains("ECOG评分日期") || fieldName.contains("ECOG") && fieldName.contains("日期")) {
                log.info("【ECOG评分日期字段赋值】字段键: {}, 字段值: {}, 访视: {}, 表单: {}, 原字段名: {}",
                        uniqueFieldKey, fieldValue, visitId, formId, fieldName);
            } else {
                log.debug("设置字段数据: {} = {}, 访视: {}, 表单: {}", uniqueFieldKey, fieldValue, visitId, formId);
            }
        }
    }

    /**
     * 处理选择类型字段（单选、多选、下拉）
     */
    private void processSelectTypeField(TemplateFormDetailVo resultVo, Map<String, Object> rowData, String uniqueFieldKey) {
        String fieldValue = resultVo.getFieldValue();
        String fieldName = resultVo.getFieldName();

        if (StringUtils.isBlank(fieldValue)) {
            rowData.put(uniqueFieldKey, "");
            return;
        }

        List<TemplateFormDictionaryVo> dictionaryList = resultVo.getTemplateFormDictionaryList();
        if (CollectionUtil.isEmpty(dictionaryList)) {
            rowData.put(uniqueFieldKey, fieldValue);
            return;
        }

        try {
            if (BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(resultVo.getType())) {
                // 多选框处理 - 增强JSON格式问题处理
                processCheckboxFieldValue(fieldValue, uniqueFieldKey, dictionaryList, rowData);
            } else {
                // 单选框和下拉框处理
                for (TemplateFormDictionaryVo dictionaryVo : dictionaryList) {
                    if (dictionaryVo.getId().equals(fieldValue)) {
                        rowData.put(uniqueFieldKey, dictionaryVo.getName());
                        log.debug("设置选择字段数据: {} = {} (字典值)", uniqueFieldKey, dictionaryVo.getName());
                        return;
                    }
                }
                rowData.put(uniqueFieldKey, fieldValue); // 如果没找到对应的字典值，使用原值
                log.debug("设置选择字段数据: {} = {} (原值)", uniqueFieldKey, fieldValue);
            }
        } catch (Exception e) {
            log.warn("处理选择类型字段失败，字段: {}, 值: {}", fieldName, fieldValue, e);
            rowData.put(uniqueFieldKey, fieldValue);
        }
    }

    /**
     * 处理表格变量数据
     */
    private void processTableVariable(TemplateFormDetailVo resultVo, Map<String, Object> rowData, String visitId, String formId) {
        List<ProjectTesteeTableBody.RowDataDesc> rows = resultVo.getRows();
        if (CollectionUtil.isEmpty(rows)) {
            return;
        }

        try {
            for (int i = 0; i < rows.size(); i++) {
                List<ProjectTesteeTableBody.ProjectTesteeTableData> rowDataList = rows.get(i).getRowData();
                if (CollectionUtil.isNotEmpty(rowDataList)) {
                    for (ProjectTesteeTableBody.ProjectTesteeTableData tableData : rowDataList) {
                        // 构建唯一的表格字段键，避免不同表单间的字段名冲突
                        String uniqueTableKey = generateUniqueFieldKey(visitId, formId, tableData.getFieldName() + "-" + i);
                        rowData.put(uniqueTableKey, tableData.getFieldValue());
                        log.debug("设置表格字段数据: {} = {}, 访视: {}, 表单: {}", uniqueTableKey, tableData.getFieldValue(), visitId, formId);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("处理表格变量数据失败，字段: {}, 访视: {}, 表单: {}", resultVo.getFieldName(), visitId, formId, e);
        }
    }

    /**
     * 生成唯一的字段键，避免不同表单间的字段名冲突
     * @param visitId 访视ID
     * @param formId 表单ID
     * @param fieldName 字段名称
     * @return 唯一的字段键
     */
    private String generateUniqueFieldKey(String visitId, String formId, String fieldName) {
        // 使用访视ID_表单ID_字段名的格式确保唯一性
        String uniqueKey = visitId + "_" + formId + "_" + fieldName;
        log.debug("生成唯一字段键: visitId={}, formId={}, fieldName={} -> {}", visitId, formId, fieldName, uniqueKey);
        return uniqueKey;
    }

    /**
     * 处理图片变量 - 重载方法支持唯一字段键
     */
    private void processImageVariableForExcel(TemplateFormDetailVo resultVo, Map<String, Object> rowData, String uniqueFieldKey) {
        // 调用原有的图片处理方法，但使用唯一键
        processImageVariableForExcel(resultVo, rowData);
        // 如果原方法使用了fieldName作为key，需要重新设置为唯一键
        String originalKey = resultVo.getFieldName();
        if (rowData.containsKey(originalKey) && !originalKey.equals(uniqueFieldKey)) {
            Object value = rowData.remove(originalKey);
            rowData.put(uniqueFieldKey, value);
            log.debug("重新映射图片字段: {} -> {}", originalKey, uniqueFieldKey);
        }
    }

    /**
     * 处理图片展示变量 - 重载方法支持唯一字段键
     */
    private void processImageViewVariableForExcel(TemplateFormDetailVo resultVo, Map<String, Object> rowData, String uniqueFieldKey) {
        // 调用原有的图片展示处理方法，但使用唯一键
        processImageViewVariableForExcel(resultVo, rowData);
        // 如果原方法使用了fieldName作为key，需要重新设置为唯一键
        String originalKey = resultVo.getFieldName();
        if (rowData.containsKey(originalKey) && !originalKey.equals(uniqueFieldKey)) {
            Object value = rowData.remove(originalKey);
            rowData.put(uniqueFieldKey, value);
            log.debug("重新映射图片展示字段: {} -> {}", originalKey, uniqueFieldKey);
        }
    }

    @Override
    public List<List<ExportTesteeVo>> getSimpleDataAnalysisList(ProjectTesteeExportParam exportParam) {
        List<ExcelExportEntity> exportTitle = createExportTitle();
        List<Map<String, Object>> dataList = new ArrayList<>();
        List<ProjectExportFlowParam> exportList = new ArrayList<>();
        List<ProjectVisitConfig> visitConfigs = visitConfigService.listByProjectId(Long.parseLong(exportParam.getProjectId()));
        for (int x = 0; x < visitConfigs.size(); x++) {
            ProjectVisitConfig config = visitConfigs.get(x);
            List<FlowFormSet> flowFormSets = flowFormSetMapper.listByFlowId(config.getId());
            ProjectExportFlowParam param = new ProjectExportFlowParam();
            param.setVisitId(config.getId().toString());
            if (CollectionUtil.isNotEmpty(flowFormSets)) {
                List<ProjectExportFiledParam> forms = new ArrayList<>();
                // 导出的表单信息
                for (int f = 0; f < flowFormSets.size(); f++) {
                    FlowFormSet formSet = flowFormSets.get(f);
                    // 导出表单id
                    Long formId = formSet.getFormId();
                    ProjectExportFiledParam filedParam = new ProjectExportFiledParam();
                    filedParam.setFormId(formId.toString());
                    forms.add(filedParam);
                    // 获取表单变量字段
                    List<TemplateFormDetailVo> formDetails = templateConfigService.getTemplateFormDetailConfigListByFormId(null, formId.toString(), null, "1", "1", "");
                    formDetails.forEach(detail -> {
                        if (!BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE.equals(detail.getType())) {
                            // 如果不是表格，是普通变量直接拼接到把变量拼接到表头中
                            exportTitle.add(new ExcelExportEntity(detail.getLabel(), config.getId()+"_"+formId+"_"+detail.getFieldName(), 20));
                        } else {
                            // 如果是表格，获取表格的变量
                            List<TemplateTableVo> rowList = projectTesteeTableService.getProjectTesteeTableRowHead(formId.toString(), detail.getId().toString(), false);
                            // 获取参与者最大的数据行号
                            int maxRow = 0;
                            // 循环每一个参与者进行查询数据。
                            for (String testeeId : exportParam.getTesteeIds()) {
                                List<ProjectTesteeTableResultExportVo> tableData = projectTesteeTableService.getProjectTesteeTableRecordsWithVariableIds(exportParam.getProjectId(), exportParam.getOrgId(), null, formId.toString(), detail.getId().toString(), null, testeeId);
                                if (CollectionUtil.isNotEmpty(tableData)) {
                                    // 表格数据根据tableSort 进行分组。分组成每一行
                                    Map<String, List<ProjectTesteeTableResultExportVo>> tableDateMap = tableData.stream().collect(Collectors.groupingBy(ProjectTesteeTableResultExportVo::getRowNumber, LinkedHashMap::new, Collectors.toList()));
                                    if (tableDateMap.size() > maxRow) {
                                        maxRow = tableDateMap.size();
                                    }
                                }
                            }
                            List<TemplateTableVo> rows = new ArrayList<>();
                            if (CollectionUtil.isNotEmpty(rowList)) {
                                rowList.forEach(row -> {
                                    rows.add(row);
                                });
                            }
                            if (rows.size() > 0 && maxRow > 0) {
                                for (int i = 0; i < maxRow; i++) {
                                    for (TemplateTableVo row : rows) {
                                        // 优化表格标题显示格式：字段名称（第几行）
                                        String tableTitle = row.getLabel() + "（第" + (i + 1) + "行）";
                                        exportTitle.add(new ExcelExportEntity(tableTitle, config.getId()+"_"+formId+"_"+row.getFieldName() + "-" + i, 20));
                                    }
                                }
                            }
                        }
                    });
                }
                param.setForms(forms);
            }
            exportList.add(param);
        }

        /*------------------------------------------------导出数据获取分割线-----------------------------------------------------*/
        ProjectOrgInfo projectOrgInfo = organizationService.getProjectOrgInfo(exportParam.getOrgId());
        Organization systemOrgInfo = organizationService.getSystemOrganizationInfo(projectOrgInfo.getOrgId().toString());
        List<ProjectTesteeInfo> testeeIdList = projectTesteeInfoService.getProjectTesteeListByIds(exportParam.getProjectId(),exportParam.getOrgId(),exportParam.getTesteeIds());
        for (int k = 0; k < testeeIdList.size(); k++) {
            Map<String, Object> map = new HashMap<>();
            // 参与者的基本信息
            ProjectTesteeInfo testeeInfo = testeeIdList.get(k);
            map.put("code", testeeInfo.getCode());
            map.put("orgName", systemOrgInfo.getName());

            for (int x = 0; x < exportList.size(); x++) {
                ProjectExportFlowParam ex = exportList.get(x);
                String visitId = ex.getVisitId();
                List<ProjectExportFiledParam> forms = ex.getForms();
                if (CollectionUtil.isNotEmpty(exportList)) {
                    for (int f = 0; f < forms.size(); f++) {
                        ProjectExportFiledParam form = forms.get(f);
                        // 导出表单id
                        String formId = form.getFormId();
                        List<TemplateFormDetailVo> fromDataList = projectTesteeInfoService.getTesteeVisitFormDetail(exportParam.getProjectId(),
                                null, visitId.toString(), formId.toString(), "", null, "", testeeInfo.getId().toString(), null, "");
                        if (CollectionUtil.isNotEmpty(fromDataList)) {
                            for (TemplateFormDetailVo resultVo : fromDataList) {
                                if (!BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE.equals(resultVo.getType())) {
                                    // 变量数据
                                    if (BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO.equals(resultVo.getType())
                                            || BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(resultVo.getType())
                                            || BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT.equals(resultVo.getType())
                                    ) {
                                        // 单选，多选，下拉
                                        // 字典信息
                                        if (StringUtils.isNotBlank(resultVo.getFieldValue())) {
                                            List<TemplateFormDictionaryVo> dictionaryList = resultVo.getTemplateFormDictionaryList();
                                            if (CollectionUtil.isNotEmpty(dictionaryList)) {
                                                if (BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(resultVo.getType())) {
                                                    // 使用增强的复选框处理方法
                                                    Map<String, Object> tempMap = new HashMap<>();
                                                    processCheckboxFieldValue(resultVo.getFieldValue(), "temp", dictionaryList, tempMap);
                                                    map.put(visitId+"_"+formId+"_"+resultVo.getFieldName(), tempMap.get("temp"));
                                                } else {
                                                    for (TemplateFormDictionaryVo dictionaryVo : dictionaryList) {
                                                        if (dictionaryVo.getId().equals(resultVo.getFieldValue())) {
                                                            map.put(visitId+"_"+formId+"_"+resultVo.getFieldName(), dictionaryVo.getName());
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    } else {
                                        map.put(visitId+"_"+formId+"_"+resultVo.getFieldName(), resultVo.getFieldValue());
                                    }
                                } else {
                                    // 表格数据
                                    List<ProjectTesteeTableBody.RowDataDesc> rows = resultVo.getRows();
                                    if (CollectionUtil.isNotEmpty(rows)) {

                                        for (int i = 0; i < rows.size(); i++) {
                                            List<ProjectTesteeTableBody.ProjectTesteeTableData> rowDataList = rows.get(i).getRowData();
                                            if (CollectionUtil.isNotEmpty(rowDataList)) {
                                                for (int n = 0; n < rowDataList.size(); n++) {
                                                    ProjectTesteeTableBody.ProjectTesteeTableData tableData = rowDataList.get(n);
                                                    map.put(visitId+"_"+formId+"_"+tableData.getFieldName() + "-" + i, tableData.getFieldValue());
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            dataList.add(map);
        }

        List<List<ExportTesteeVo>> testeeVos = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(dataList)){
            for (Map<String, Object> map : dataList) {
                List<ExportTesteeVo> vos = new ArrayList<>();
                for (int i = 0; i < exportTitle.size(); i++) {
                    ExcelExportEntity excelExportEntity = exportTitle.get(i);
                    ExportTesteeVo vo = new ExportTesteeVo();
                    Object key = excelExportEntity.getKey();
                    Object fileValue = map.get(key);
                    if (i>1){
                        vo.setFieldValue(Objects.isNull(fileValue)?"":fileValue.toString());
                        String [] ids =key.toString().split("_");
                        vo.setFieldLabel(excelExportEntity.getName());
                        vo.setFieldName(ids[2]);
                        vo.setFormId(ids[1]);
                        vo.setVisitId(ids[0]);
                    }else {
                        vo.setFieldValue(Objects.isNull(fileValue)?"":fileValue.toString());
                        vo.setFieldLabel(excelExportEntity.getName());
                        vo.setFieldName(key.toString());
                    }
                    vos.add(vo);
                }
                testeeVos.add(vos);
            }
        }
        return testeeVos;
    }


    /**
     * 获取导出详情
     *
     * @param projectId
     * @param orgId
     * @param exportType
     * @return
     */
    @Override
    public CommonResult<ProjectTesteeExportParam> getExportInfo(Long projectId, Long orgId, Integer exportType) {
        ProjectTesteeExportExample example = new ProjectTesteeExportExample();
        ProjectTesteeExportExample.Criteria criteria = example.createCriteria();
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        criteria.andProjectIdEqualTo(projectId);
        criteria.andOrgIdEqualTo(orgId.toString());
        criteria.andOperatorEqualTo(SecurityUtils.getUserId());
        List<ProjectTesteeExport> list = projectTesteeExportMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(list)) {
            ProjectTesteeExport projectTesteeExport = list.get(0);
            String description = projectTesteeExport.getDescription();
            if (StringUtils.isNotBlank(description)) {
                ProjectTesteeExportParam param = JSON.parseObject(description, ProjectTesteeExportParam.class);
                return CommonResult.success(param);
            }
        }
        return CommonResult.success(null);
    }

    /**
     * 获取下载管理
     * @return
     */
    @Override
    public CommonResult<List<ProjectTesteeExportManage>> getDownList() {
        String key = RedisKeyContants.USER_DOWN + SecurityUtils.getUserId();
        List<Object> list = redisTemplateService.lRange(key, 0, redisTemplateService.lSize(key));
        if (CollectionUtil.isNotEmpty(list)){
            List<ProjectTesteeExportManage> list2 = projectTesteeExportMapper.getDownList(list);
            return CommonResult.success(list2);
        }
        return CommonResult.success(new ArrayList<>());
    }

    // 对导出的字段和流程进行结构化
    private static List<ProjectExportFlowParam> extracted(ProjectTesteeExportParam param) {

        // 导出的流程
        List<ProjectExportFlowParam> exportList = new ArrayList<>();

        // 存放导出的数据配置集合（流程，表单，字段）
        Map<Long, ProjectExportFlowParam> visitMap = new HashMap();

        // 存放导出的数据配置集合（流程，表单，字段）
        Map<String, ProjectExportFiledParam> formMap = new HashMap();

        for (ProjectExportFlowParam2 projectExportFlowParam2 : param.getExportList()) {
            if(projectExportFlowParam2.getVisitId()==null){
                continue;
            }
            ProjectExportFlowParam exportFlowParam = visitMap.get(projectExportFlowParam2.getVisitId());
            if (exportFlowParam == null) {
                ProjectExportFlowParam flowParam = new ProjectExportFlowParam();
                flowParam.setVisitId(projectExportFlowParam2.getVisitId().toString());
                List<ProjectExportFiledParam> forms = new ArrayList<>();
                ProjectExportFiledParam filedParam = new ProjectExportFiledParam();
                filedParam.setFormId(projectExportFlowParam2.getFormId().toString());
                List<String> variableIds = new ArrayList<>();
                variableIds.add(projectExportFlowParam2.getVariableId().toString());
                filedParam.setVariableIds(variableIds);
                forms.add(filedParam);
                flowParam.setForms(forms);
                visitMap.put(projectExportFlowParam2.getVisitId(), flowParam);
                formMap.put(projectExportFlowParam2.getVisitId() + "-" + projectExportFlowParam2.getFormId(), filedParam);
                exportList.add(flowParam);
            } else {
                List<ProjectExportFiledParam> forms = exportFlowParam.getForms();

                ProjectExportFiledParam filedParam = formMap.get(projectExportFlowParam2.getVisitId() + "-" + projectExportFlowParam2.getFormId());
                if (filedParam == null) {
                    ProjectExportFiledParam filedParam0 = new ProjectExportFiledParam();
                    filedParam0.setFormId(projectExportFlowParam2.getFormId().toString());
                    List<String> variableIds = new ArrayList<>();
                    variableIds.add(projectExportFlowParam2.getVariableId().toString());
                    filedParam0.setVariableIds(variableIds);
                    forms.add(filedParam0);
                    formMap.put(projectExportFlowParam2.getVisitId() + "-" + projectExportFlowParam2.getFormId(), filedParam0);
                } else {
                    List<String> variableIds = filedParam.getVariableIds();
                    variableIds.add(projectExportFlowParam2.getVariableId().toString());
                }
            }
        }
        return exportList;
    }


    /**
     * 更新参与者导出信息(已完成/导出失败)
     */
    private void updateExportInfo(Long uuid,String fileUrl,Integer status) {
        ProjectTesteeExport exportInfo = getExportInfo(uuid);
        if (exportInfo != null) {
            if (BusinessConfig.TESTEE_EXPORT_STATUS_0.equals(status)){
                exportInfo.setDownloadUrl(fileUrl);
            }
            exportInfo.setExportStatus(status);
            projectTesteeExportMapper.updateByPrimaryKey(exportInfo);
        }
    }

    private ProjectTesteeExport getExportInfo(Long uuid) {
        return projectTesteeExportMapper.selectByPrimaryKey(uuid);
    }

    /**
     * 获取导出进度
     * @param exportFileId 导出文件ID
     * @return 导出进度信息
     */
    @Override
    public CommonResult<ProjectTesteeExport> getExportProgress(Long exportFileId) {
        try {
            ProjectTesteeExport exportInfo = getExportInfo(exportFileId);
            if (exportInfo == null) {
                return CommonResult.failed("导出任务不存在");
            }
            return CommonResult.success(exportInfo);
        } catch (Exception e) {
            log.error("获取导出进度失败", e);
            return CommonResult.failed("获取导出进度失败: " + e.getMessage());
        }
    }

    /**
     * 从Redis缓存获取对象数据
     */
    private <T> T getObjectFromCache(String cacheKey, Class<T> clazz) {
        try {
            Object cachedValue = redisTemplateService.get(cacheKey);
            if (cachedValue != null) {
                String jsonString = cachedValue.toString();
                return JSON.parseObject(jsonString, clazz);
            }
        } catch (Exception e) {
            log.warn("从缓存获取对象数据失败，key: {}, 数据类型: {}", cacheKey, clazz.getSimpleName(), e);
        }
        return null;
    }

    /**
     * 从Redis缓存获取List数据
     */
    private <T> List<T> getListFromCache(String cacheKey, Class<T> elementClass) {
        try {
            Object cachedValue = redisTemplateService.get(cacheKey);
            if (cachedValue != null) {
                String jsonString = cachedValue.toString();
                return JSON.parseArray(jsonString, elementClass);
            }
        } catch (Exception e) {
            log.warn("从缓存获取List数据失败，key: {}, 元素类型: {}", cacheKey, elementClass.getSimpleName(), e);
        }
        return null;
    }

    /**
     * 将数据缓存到Redis
     */
    private void cacheToRedis(String cacheKey, Object data, long expireSeconds) {
        try {
            if (data != null) {
                String jsonData = JSON.toJSONString(data);
                redisTemplateService.set(cacheKey, jsonData, expireSeconds);
                log.debug("数据已缓存，key: {}, 过期时间: {}秒", cacheKey, expireSeconds);
            }
        } catch (Exception e) {
            log.warn("缓存数据失败，key: {}", cacheKey, e);
        }
    }

    /**
     * 基于导出阶段更新进度
     * @param exportFileId 导出文件ID
     * @param stageProgress 当前阶段的进度 (0.0 - 1.0)
     * @param totalCount 总数量
     * @param currentStatus 当前状态描述
     * @param stage 当前导出阶段
     */
    private void updateProgressWithStage(Long exportFileId, double stageProgress, int totalCount, String currentStatus, ExcelExportStage stage) {
        try {
            // 根据阶段计算总体进度百分比
            int progressPercent = stage.calculatePercent(stageProgress);

            // 计算已处理数量（仅在数据处理阶段有意义）
            int processedCount = 0;
            if (stage == ExcelExportStage.PROCESS_DATA && totalCount > 0) {
                processedCount = (int) (totalCount * stageProgress);
            }

            ProjectTesteeExport exportRecord = new ProjectTesteeExport();
            exportRecord.setId(exportFileId);
            exportRecord.setProgressPercent(progressPercent);
            exportRecord.setProcessedCount(processedCount);
            exportRecord.setTotalCount(totalCount);
            exportRecord.setCurrentStatus(currentStatus);

            projectTesteeExportMapper.updateByPrimaryKeySelective(exportRecord);

            log.debug("更新Excel导出进度: {} ({}%) - {} [阶段: {}]",
                     processedCount > 0 ? processedCount + "/" + totalCount : "N/A",
                     progressPercent, currentStatus, stage.getDescription());
        } catch (Exception e) {
            log.warn("更新Excel导出进度失败: {}", e.getMessage());
        }
    }

    /**
     * 更新导出进度 - 兼容原有方法
     * @param exportFileId 导出文件ID
     * @param processedCount 已处理数量
     * @param totalCount 总数量
     * @param currentStatus 当前状态描述
     */
    private void updateProgress(Long exportFileId, int processedCount, int totalCount, String currentStatus) {
        try {
            // 计算进度百分比
            int progressPercent = totalCount > 0 ? (int) ((double) processedCount / totalCount * 100) : 0;

            ProjectTesteeExport exportRecord = new ProjectTesteeExport();
            exportRecord.setId(exportFileId);
            exportRecord.setProgressPercent(progressPercent);
            exportRecord.setProcessedCount(processedCount);
            exportRecord.setTotalCount(totalCount);
            exportRecord.setCurrentStatus(currentStatus);

            projectTesteeExportMapper.updateByPrimaryKeySelective(exportRecord);

            log.debug("更新Excel导出进度: {}/{} ({}%) - {}", processedCount, totalCount, progressPercent, currentStatus);
        } catch (Exception e) {
            log.warn("更新Excel导出进度失败: {}", e.getMessage());
        }
    }

    /**
     * 创建一个导出表头的集合
     *
     * @return
     */
    private static List<ExcelExportEntity> createExportTitle() {
        List<ExcelExportEntity> entityList = new LinkedList<>();
        entityList.add(new ExcelExportEntity("编号", "code", 20));
        entityList.add(new ExcelExportEntity("研究中心", "orgName", 20));
        return entityList;
    }


    /**
     * 动态列 多个sheet页导出
     * param list
     *
     * @return
     */
    private Workbook exportMultiSheetExcel(List<Map<String, Object>> list) {
        Workbook workbook = new XSSFWorkbook();
        for (Map<String, Object> map : list) {
            CustomExcelExportService service = new CustomExcelExportService();
            service.createSheetWithList(workbook, (ExportParams) map.get("title"), ExportParams.class, (List<ExcelExportEntity>) map.get("entityList"), (Collection<?>) map.get("data"));
        }
        return workbook;
    }

    /**
     * 获取PDF导出是否使用原始图片URL的配置
     * @return true：使用原始URL，false：使用压缩URL
     */
    public static boolean isUseOriginalImageUrl() {
        return USE_ORIGINAL_IMAGE_URL;
    }

    /**
     * 获取PDF导出是否启用图片缓存的配置
     * @return true：启用缓存，false：不缓存
     */
    public static boolean isEnableImageCache() {
        return ENABLE_IMAGE_CACHE;
    }

    /**
     * 获取PDF导出是否默认使用优化版本的配置
     * @return true：使用优化版本，false：使用原始版本
     */
    public static boolean isUseOptimizedVersionByDefault() {
        return USE_OPTIMIZED_VERSION_BY_DEFAULT;
    }

    /**
     * 获取PDF导出是否强制使用高性能版本的配置
     * @return true：强制使用高性能版本，false：允许使用标准版本
     */
    public static boolean isForceHighPerformanceVersion() {
        return FORCE_HIGH_PERFORMANCE_VERSION;
    }

    /**
     * 获取当前PDF导出配置信息的描述
     * @return 配置信息描述字符串
     */
    public static String getExportConfigDescription() {
        return String.format("PDF导出配置 - 使用原始图片URL: %s, 启用图片缓存: %s",
                           USE_ORIGINAL_IMAGE_URL, ENABLE_IMAGE_CACHE);
    }

    /**
     * 清理JSON字符串中的多余斜杠和转义字符
     * 处理多重转义的JSON字符串，如: "\"\\\"[\\\\\\\"1084337955373453312\\\\\\\"]\\\"\""
     * @param jsonString 原始JSON字符串
     * @return 清理后的JSON字符串
     */
    private String cleanJsonString(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return jsonString;
        }

        log.debug("开始清理JSON字符串，原始值: {}", jsonString);

        String cleaned = jsonString.trim();

        // 处理外层的双引号包装
        if (cleaned.startsWith("\"") && cleaned.endsWith("\"")) {
            cleaned = cleaned.substring(1, cleaned.length() - 1);
            log.debug("移除外层双引号后: {}", cleaned);
        }

        // 递归处理多重转义，直到不再包含转义字符或达到最大处理次数
        int maxIterations = 10; // 防止无限循环
        int iteration = 0;

        while (iteration < maxIterations && (cleaned.contains("\\\"") || cleaned.contains("\\\\"))) {
            iteration++;
            String beforeClean = cleaned;

            // 处理转义的双引号
            cleaned = cleaned.replace("\\\"", "\"");

            // 处理转义的反斜杠
            cleaned = cleaned.replace("\\\\", "\\");

            // 处理转义的斜杠
            cleaned = cleaned.replace("\\/", "/");

            log.debug("第{}次清理后: {}", iteration, cleaned);

            // 如果这次清理没有改变字符串，说明已经清理完成
            if (beforeClean.equals(cleaned)) {
                break;
            }
        }

        // 最终清理：如果仍然有单独的反斜杠，移除它们
        if (cleaned.contains("\\") && !isValidJsonAfterClean(cleaned)) {
            cleaned = cleaned.replace("\\", "");
            log.debug("最终清理移除剩余反斜杠后: {}", cleaned);
        }

        log.debug("JSON字符串清理完成，最终结果: {}", cleaned);
        return cleaned;
    }

    /**
     * 检查清理后的字符串是否是有效的JSON
     * @param jsonString 清理后的JSON字符串
     * @return 是否是有效的JSON
     */
    private boolean isValidJsonAfterClean(String jsonString) {
        try {
            JSON.parse(jsonString);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 处理复选框字段值，支持多种JSON格式的容错解析
     * @param fieldValue 原始字段值
     * @param fieldName 字段名称
     * @param dictionaryList 字典列表
     * @param rowData 行数据Map
     */
    private void processCheckboxFieldValue(String fieldValue, String fieldName,
                                         List<TemplateFormDictionaryVo> dictionaryList,
                                         Map<String, Object> rowData) {
        if (StringUtils.isBlank(fieldValue)) {
            rowData.put(fieldName, "");
            return;
        }

        log.debug("处理复选框字段: {}, 原始值: {}", fieldName, fieldValue);

        // 尝试多种解析策略
        String result = null;

        // 策略1: 直接解析
        result = tryParseCheckboxValue(fieldValue, dictionaryList, "直接解析");
        if (result != null) {
            rowData.put(fieldName, result);
            return;
        }

        // 策略2: 清理后解析
        String cleanedValue = cleanJsonString(fieldValue);
        result = tryParseCheckboxValue(cleanedValue, dictionaryList, "清理后解析");
        if (result != null) {
            rowData.put(fieldName, result);
            return;
        }

        // 策略3: 提取数字ID
        result = tryExtractNumericIds(fieldValue, dictionaryList);
        if (result != null) {
            rowData.put(fieldName, result);
            return;
        }

        // 策略4: 尝试解析为单个值
        result = tryParseSingleValue(fieldValue, dictionaryList);
        if (result != null) {
            rowData.put(fieldName, result);
            return;
        }

        // 所有策略都失败，记录详细错误信息并使用原始值
        log.warn("复选框字段解析失败，字段: {}, 原始值: {}, 已尝试所有解析策略", fieldName, fieldValue);
        rowData.put(fieldName, fieldValue);
    }

    /**
     * 尝试解析复选框值
     * @param value 要解析的值
     * @param dictionaryList 字典列表
     * @param strategy 解析策略名称
     * @return 解析结果，失败返回null
     */
    private String tryParseCheckboxValue(String value, List<TemplateFormDictionaryVo> dictionaryList, String strategy) {
        try {
            JSONArray selectedIds = JSON.parseArray(value);
            StringBuilder valueBuilder = new StringBuilder();

            for (TemplateFormDictionaryVo dictionaryVo : dictionaryList) {
                // 尝试匹配Long类型ID
                if (selectedIds.contains(dictionaryVo.getId())) {
                    if (valueBuilder.length() > 0) {
                        valueBuilder.append(",");
                    }
                    valueBuilder.append(dictionaryVo.getName());
                }
                // 尝试匹配String类型ID
                else if (selectedIds.contains(dictionaryVo.getId().toString())) {
                    if (valueBuilder.length() > 0) {
                        valueBuilder.append(",");
                    }
                    valueBuilder.append(dictionaryVo.getName());
                }
            }

            String result = valueBuilder.toString();
            log.debug("{}成功，解析值: {} -> 结果: {}", strategy, value, result);
            return result;

        } catch (Exception e) {
            log.debug("{}失败，解析值: {}, 错误: {}", strategy, value, e.getMessage());
            return null;
        }
    }

    /**
     * 尝试从字符串中提取数字ID
     * @param value 原始值
     * @param dictionaryList 字典列表
     * @return 解析结果，失败返回null
     */
    private String tryExtractNumericIds(String value, List<TemplateFormDictionaryVo> dictionaryList) {
        try {
            // 使用正则表达式提取所有数字
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\d+");
            java.util.regex.Matcher matcher = pattern.matcher(value);

            List<String> extractedIds = new ArrayList<>();
            while (matcher.find()) {
                extractedIds.add(matcher.group());
            }

            if (extractedIds.isEmpty()) {
                return null;
            }

            StringBuilder valueBuilder = new StringBuilder();
            for (TemplateFormDictionaryVo dictionaryVo : dictionaryList) {
                String dictIdStr = dictionaryVo.getId().toString();
                if (extractedIds.contains(dictIdStr)) {
                    if (valueBuilder.length() > 0) {
                        valueBuilder.append(",");
                    }
                    valueBuilder.append(dictionaryVo.getName());
                }
            }

            String result = valueBuilder.toString();
            if (StringUtils.isNotBlank(result)) {
                log.debug("数字ID提取成功，原始值: {} -> 提取的ID: {} -> 结果: {}", value, extractedIds, result);
                return result;
            }

        } catch (Exception e) {
            log.debug("数字ID提取失败，原始值: {}, 错误: {}", value, e.getMessage());
        }
        return null;
    }

    /**
     * 尝试解析为单个值
     * @param value 原始值
     * @param dictionaryList 字典列表
     * @return 解析结果，失败返回null
     */
    private String tryParseSingleValue(String value, List<TemplateFormDictionaryVo> dictionaryList) {
        try {
            // 移除所有非数字字符，尝试作为单个ID
            String numericValue = value.replaceAll("[^0-9]", "");
            if (StringUtils.isNotBlank(numericValue)) {
                Long singleId = Long.parseLong(numericValue);
                for (TemplateFormDictionaryVo dictionaryVo : dictionaryList) {
                    if (dictionaryVo.getId().equals(singleId)) {
                        log.debug("单个值解析成功，原始值: {} -> ID: {} -> 结果: {}", value, singleId, dictionaryVo.getName());
                        return dictionaryVo.getName();
                    }
                }
            }
        } catch (Exception e) {
            log.debug("单个值解析失败，原始值: {}, 错误: {}", value, e.getMessage());
        }
        return null;
    }

    /**
     * 处理Excel导出中的图片变量
     * @param resultVo 表单详情对象
     * @param rowData 行数据Map
     */
    private void processImageVariableForExcel(TemplateFormDetailVo resultVo, Map<String, Object> rowData) {
        String fieldName = resultVo.getFieldName();

        try {
            List<ProjectTesteeFormImageVo> variableImageList = resultVo.getVariableImageList();
            if (CollectionUtil.isNotEmpty(variableImageList)) {
                if (EXCEL_EXPORT_SHOW_IMAGE_PREVIEW) {
                    // 显示图片预览（实际图片内容）
                    StringBuilder imageContent = new StringBuilder();
                    for (int i = 0; i < variableImageList.size(); i++) {
                        ProjectTesteeFormImageVo imageVo = variableImageList.get(i);
                        if (i > 0) {
                            imageContent.append("; ");
                        }
                        imageContent.append("图片").append(i + 1).append(": ").append(imageVo.getFileName());
                    }
                    rowData.put(fieldName, imageContent.toString());
                } else {
                    // 显示图片访问路径
                    StringBuilder imagePaths = new StringBuilder();
                    for (int i = 0; i < variableImageList.size(); i++) {
                        ProjectTesteeFormImageVo imageVo = variableImageList.get(i);
                        if (i > 0) {
                            imagePaths.append("; ");
                        }
                        String imageUrl = imageVo.getFileUrl();
                        if (EXCEL_EXPORT_SHOW_FULL_IMAGE_URL && !imageUrl.startsWith("http")) {
                            // 构建完整URL
                            imageUrl = storageConfig.getViewUrl() + imageUrl;
                        }
                        imagePaths.append(imageUrl);
                    }
                    rowData.put(fieldName, imagePaths.toString());
                }
            } else {
                rowData.put(fieldName, "");
            }
        } catch (Exception e) {
            log.warn("处理图片变量失败，字段: {}", fieldName, e);
            rowData.put(fieldName, "图片处理失败");
        }
    }

    /**
     * 处理Excel导出中的图片展示变量
     * @param resultVo 表单详情对象
     * @param rowData 行数据Map
     */
    private void processImageViewVariableForExcel(TemplateFormDetailVo resultVo, Map<String, Object> rowData) {
        String fieldName = resultVo.getFieldName();

        try {
            Object expandValue = resultVo.getExpand();
            if (expandValue != null && !expandValue.toString().trim().isEmpty()) {
                try {
                    JSONObject object = JSON.parseObject(expandValue.toString());
                    if (object != null && object.containsKey("imgUrl")) {
                        String imageUrl = object.getString("imgUrl");

                        if (StringUtils.isNotBlank(imageUrl)) {
                            if (EXCEL_EXPORT_SHOW_IMAGE_PREVIEW) {
                                // 显示图片预览信息
                                rowData.put(fieldName, "图片展示: " + imageUrl);
                            } else {
                                // 显示图片访问路径
                                if (EXCEL_EXPORT_SHOW_FULL_IMAGE_URL && !imageUrl.startsWith("http")) {
                                    // 构建完整URL
                                    imageUrl = storageConfig.getViewUrl() + imageUrl;
                                }
                                rowData.put(fieldName, imageUrl);
                            }
                        } else {
                            log.warn("Excel导出：图片展示字段imgUrl为空，字段: {}", fieldName);
                            rowData.put(fieldName, "");
                        }
                    } else {
                        log.warn("Excel导出：图片展示字段JSON中缺少imgUrl属性，字段: {}", fieldName);
                        rowData.put(fieldName, "");
                    }
                } catch (Exception jsonException) {
                    log.error("Excel导出：图片展示字段JSON解析失败，字段: {}，原始数据: {}，错误: {}",
                             fieldName, expandValue, jsonException.getMessage());
                    rowData.put(fieldName, "");
                }
            } else {
                log.debug("Excel导出：图片展示字段expand为空，字段: {}", fieldName);
                rowData.put(fieldName, "");
            }
        } catch (Exception e) {
            log.warn("处理图片展示变量失败，字段: {}", fieldName, e);
            rowData.put(fieldName, "图片展示处理失败");
        }
    }

    /**
     * 获取Excel导出图片显示模式配置
     * @return true：显示真实预览图片，false：显示图片访问路径
     */
    public static boolean isExcelExportShowImagePreview() {
        return EXCEL_EXPORT_SHOW_IMAGE_PREVIEW;
    }

    /**
     * 获取Excel导出图片URL显示模式配置
     * @return true：显示完整URL，false：显示相对路径
     */
    public static boolean isExcelExportShowFullImageUrl() {
        return EXCEL_EXPORT_SHOW_FULL_IMAGE_URL;
    }

    /**
     * 获取Excel导出图片配置信息的描述
     * @return 配置信息描述字符串
     */
    public static String getExcelImageConfigDescription() {
        return String.format("Excel导出图片配置 - 显示预览图片: %s, 显示完整URL: %s",
                           EXCEL_EXPORT_SHOW_IMAGE_PREVIEW, EXCEL_EXPORT_SHOW_FULL_IMAGE_URL);
    }

    /**
     * 处理PDF导出中的复选框内容
     * @param fieldValue 字段值
     * @param dictionaryList 字典列表
     * @param html HTML构建器
     */
    private void processCheckboxForPdf(String fieldValue, List<TemplateFormDictionaryVo> dictionaryList, StringBuilder html) {
        log.debug("处理PDF复选框字段，原始值: {}", fieldValue);

        // 尝试解析复选框值
        List<Long> selectedIds = new ArrayList<>();

        // 策略1: 直接解析
        try {
            JSONArray jsonArray = JSON.parseArray(fieldValue);
            for (Object obj : jsonArray) {
                if (obj instanceof Number) {
                    selectedIds.add(((Number) obj).longValue());
                } else if (obj instanceof String) {
                    try {
                        selectedIds.add(Long.parseLong(obj.toString()));
                    } catch (NumberFormatException e) {
                        log.debug("无法解析为数字: {}", obj);
                    }
                }
            }
        } catch (Exception e) {
            log.debug("直接解析失败，尝试清理后解析");

            // 策略2: 清理后解析
            try {
                String cleanedValue = cleanJsonString(fieldValue);
                JSONArray jsonArray = JSON.parseArray(cleanedValue);
                for (Object obj : jsonArray) {
                    if (obj instanceof Number) {
                        selectedIds.add(((Number) obj).longValue());
                    } else if (obj instanceof String) {
                        try {
                            selectedIds.add(Long.parseLong(obj.toString()));
                        } catch (NumberFormatException ex) {
                            log.debug("无法解析为数字: {}", obj);
                        }
                    }
                }
            } catch (Exception ex) {
                log.debug("清理后解析也失败，尝试提取数字");

                // 策略3: 提取数字
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\d+");
                java.util.regex.Matcher matcher = pattern.matcher(fieldValue);
                while (matcher.find()) {
                    try {
                        selectedIds.add(Long.parseLong(matcher.group()));
                    } catch (NumberFormatException nfe) {
                        log.debug("提取的数字无法解析: {}", matcher.group());
                    }
                }
            }
        }

        // 生成HTML复选框
        if (selectedIds.isEmpty()) {
            log.warn("PDF复选框解析失败，无法提取有效ID，原始值: {}", fieldValue);
            html.append("<span>复选框数据解析失败</span>");
        } else {
            log.debug("PDF复选框解析成功，提取的ID: {}", selectedIds);
            for (TemplateFormDictionaryVo dictionaryVo : dictionaryList) {
                if (selectedIds.contains(dictionaryVo.getId())) {
                    html.append("<input type='checkbox' checked='true' />").append(dictionaryVo.getName());
                } else {
                    html.append("<input type='checkbox' />").append(dictionaryVo.getName());
                }
            }
        }
    }

    /**
     * 生成直观的Excel文件名
     * 参照PDF导出的命名方式，生成包含项目信息和参与者信息的文件名
     * @param projectTesteeExportParam 导出参数
     * @param excelType 文件扩展名
     * @return 生成的文件名
     */
    private String generateExcelFileName(ProjectTesteeExportParam projectTesteeExportParam, String excelType) {
        try {
            // 获取项目信息
            ProjectVo projectVo = projectBaseManageService.getProjectViewInfo(projectTesteeExportParam.getProjectId());
            String projectName = projectVo != null ? projectVo.getName() : "未知项目";

            // 清理项目名称中的特殊字符
            projectName = cleanFileName(projectName);

            // 获取参与者数量
            int testeeCount = projectTesteeExportParam.getTesteeIds() != null ?
                             projectTesteeExportParam.getTesteeIds().size() : 0;

            // 获取当前时间
            String timestamp = DateUtil.formatDate2String(new Date(), "yyyyMMdd_HHmmss");

            // 构建文件名：项目名称_参与者数量_导出时间.xlsx
            String fileName = String.format("%s_%d人_%s%s",
                                           projectName,
                                           testeeCount,
                                           timestamp,
                                           excelType);

            log.info("生成Excel文件名: {}", fileName);
            return fileName;

        } catch (Exception e) {
            log.warn("生成Excel文件名失败，使用默认命名", e);
            // 如果生成失败，使用原有的命名方式作为备选
            String timestamp = DateUtil.formatDate2String(new Date(), "yyyyMMdd_HHmmss");
            return "参与者导出_" + timestamp + excelType;
        }
    }

    /**
     * 清理文件名中的特殊字符
     * @param fileName 原始文件名
     * @return 清理后的文件名
     */
    private String cleanFileName(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return "未知项目";
        }

        // 移除或替换文件名中不允许的字符
        return fileName.replaceAll("[\\\\/:*?\"<>|]", "_")
                      .replaceAll("\\s+", "_")  // 将空格替换为下划线
                      .replaceAll("_{2,}", "_") // 将多个连续下划线替换为单个
                      .trim();
    }
}
