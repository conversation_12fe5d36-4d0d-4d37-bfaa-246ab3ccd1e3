package com.haoys.user.mapper;

import com.haoys.user.domain.vo.ecrf.TemplateFormGroupFieldOptionVo;
import com.haoys.user.model.TemplateFormDetailOptions;
import com.haoys.user.model.TemplateFormDetailOptionsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TemplateFormDetailOptionsMapper {
    long countByExample(TemplateFormDetailOptionsExample example);

    int deleteByExample(TemplateFormDetailOptionsExample example);

    int deleteByPrimaryKey(String id);

    int insert(TemplateFormDetailOptions record);

    int insertSelective(TemplateFormDetailOptions record);

    List<TemplateFormDetailOptions> selectByExample(TemplateFormDetailOptionsExample example);

    TemplateFormDetailOptions selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") TemplateFormDetailOptions record, @Param("example") TemplateFormDetailOptionsExample example);

    int updateByExample(@Param("record") TemplateFormDetailOptions record, @Param("example") TemplateFormDetailOptionsExample example);

    int updateByPrimaryKeySelective(TemplateFormDetailOptions record);

    int updateByPrimaryKey(TemplateFormDetailOptions record);

    List<TemplateFormGroupFieldOptionVo> getTemplateFormGroupFieldOptionListForPage(String groupName);

    void deleteFormGroupFieldByParentId(String parentId);

    List<TemplateFormGroupFieldOptionVo> getFormGroupFieldOptionByParentId(String parentId);
}