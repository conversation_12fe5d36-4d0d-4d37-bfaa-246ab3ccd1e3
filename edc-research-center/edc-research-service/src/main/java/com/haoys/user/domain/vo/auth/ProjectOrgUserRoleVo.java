package com.haoys.user.domain.vo.auth;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ProjectOrgUserRoleVo {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "角色ID")
    private Long roleId;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "角色英文名称")
    private String ename;

    @ApiModelProperty(value = "中心code")
    private String orgCode;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "中心id")
    private Long orgId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "研究中心id")
    private Long projectOrgId;

    @ApiModelProperty(value = "项目角色模版id")
    private Long resourceRoleId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "是否授权全部研究中心")
    private Boolean ownerTotalAuth;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;
}
