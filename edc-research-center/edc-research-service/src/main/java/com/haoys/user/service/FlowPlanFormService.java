package com.haoys.user.service;

import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.vo.flow.FlowPlanFormVo;
import com.haoys.user.model.FlowPlanFormInfo;

import java.util.List;

public interface FlowPlanFormService {


    CustomResult insertFlowPlanFormInfo(String projectId, String planId, String formId, boolean publishSatus, String createUserId);

    /**
     * 批量发布表单
     * @param projectId
     * @param planId
     * @param formIds
     * @param userId
     * @return
     */
    CustomResult batchPublishFormTemplateFormConfig(String projectId, String planId, String formIds, String userId);

    /**
     * 根据流程id查询表单列表
     *
     * @param projectId
     * @param planId
     * @param visitId
     * @param formId
     * @return
     */
    List<FlowPlanFormVo> getFormConfigListByPlanIdAndVisitId(String projectId, String planId, String visitId, String formId);

    /**
     * 查询表单发布列表
     * @param projectId
     * @param formId
     * @return
     */
    List<FlowPlanFormInfo> getFormConfigListByProjectIdAndFormId(String projectId, String formId);
    
    FlowPlanFormInfo getFormConfigListByProjectIdAndFormId(String projectId, String planId, String formId);
    
}
