package com.haoys.user.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class SystemExceptionLog implements Serializable {
    
    @JsonFormat(shape= JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "项目id")
    private String requestUrl;

    @ApiModelProperty(value = "ip地址")
    private String requestIp;

    @ApiModelProperty(value = "异常消息")
    private String exceptionMessage;

    @ApiModelProperty(value = "异常栈信息")
    private String stackTraceMessage;

    @ApiModelProperty(value = "描述信息")
    private String description;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    private static final long serialVersionUID = 1L;

    /**
     * 无参构造器
     */
    public SystemExceptionLog() {
    }

    /**
     * 带参构造器
     */
    public SystemExceptionLog(String systemUserId, String userName, String description, String requestUrl, String ipAddress, String stackTraceException, String exceptionMessage) {
        this.userId = systemUserId;
        this.userName = userName;
        this.requestUrl = requestUrl;
        this.requestIp = ipAddress;
        this.exceptionMessage = exceptionMessage;
        this.stackTraceMessage = stackTraceException;
        this.description = description;
        this.createTime = new Date();
    }
    
    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getRequestUrl() {
        return requestUrl;
    }

    public void setRequestUrl(String requestUrl) {
        this.requestUrl = requestUrl;
    }

    public String getRequestIp() {
        return requestIp;
    }

    public void setRequestIp(String requestIp) {
        this.requestIp = requestIp;
    }

    public String getExceptionMessage() {
        return exceptionMessage;
    }

    public void setExceptionMessage(String exceptionMessage) {
        this.exceptionMessage = exceptionMessage;
    }

    public String getStackTraceMessage() {
        return stackTraceMessage;
    }

    public void setStackTraceMessage(String stackTraceMessage) {
        this.stackTraceMessage = stackTraceMessage;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", userName=").append(userName);
        sb.append(", requestUrl=").append(requestUrl);
        sb.append(", requestIp=").append(requestIp);
        sb.append(", exceptionMessage=").append(exceptionMessage);
        sb.append(", stackTraceMessage=").append(stackTraceMessage);
        sb.append(", description=").append(description);
        sb.append(", createTime=").append(createTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}