package com.haoys.user.mapper;

import com.haoys.user.model.TemplateVariableViewBase;
import com.haoys.user.model.TemplateVariableViewBaseExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TemplateVariableViewBaseMapper {
    long countByExample(TemplateVariableViewBaseExample example);

    int deleteByExample(TemplateVariableViewBaseExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TemplateVariableViewBase record);

    int insertSelective(TemplateVariableViewBase record);

    List<TemplateVariableViewBase> selectByExample(TemplateVariableViewBaseExample example);

    TemplateVariableViewBase selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TemplateVariableViewBase record, @Param("example") TemplateVariableViewBaseExample example);

    int updateByExample(@Param("record") TemplateVariableViewBase record, @Param("example") TemplateVariableViewBaseExample example);

    int updateByPrimaryKeySelective(TemplateVariableViewBase record);

    int updateByPrimaryKey(TemplateVariableViewBase record);

    TemplateVariableViewBase getTemplateFormVariableViewConfig(Long projectId, Long baseFormId, Long baseFormDetailId, Long baseFormTableId, Boolean enableTable, String optionValue);

    TemplateVariableViewBase getTemplateVariableBaseInfo(String projectId, String formId, String formDetailId, String formTableId, String optionValueId);

    List<TemplateVariableViewBase> getTemplateVariableOptionListByVariableId(String projectId, String formId, String formDetailId, String formTableId);

    void deleteTemplateFormVariableViewConfig(Long projectId, Long formId, Long formDetailId);
}