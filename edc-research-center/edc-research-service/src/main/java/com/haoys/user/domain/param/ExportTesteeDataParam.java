package com.haoys.user.domain.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.ArrayList;
import java.util.List;

/**
 * 参与者管理导出参数
 */
@Data
public class ExportTesteeDataParam {

    @ApiModelProperty(value = "导出名称",required = true)
    private String taskName;

    @ApiModelProperty(value = "项目id",required = true)
    private String projectId;

    @ApiModelProperty(value = "所属中心",required = true)
    private String orgId;

    @ApiModelProperty(value = "导出类型：1.excel 2.word")
    private String exportType="1";

    @ApiModelProperty(value = "导出描述")
    private String exportDesc;

    @ApiModelProperty(value = "导出内容")
    private String exportContent;

    @ApiModelProperty(value = "导出列的id",required = true)
    private List<String> rowIdList = new ArrayList<>();

    @ApiModelProperty(value = "导出参与者的id")
    private List<String> testeeIdList;

    @ApiModelProperty(value = "操作人id,不用传递")
    private String operator;

}
