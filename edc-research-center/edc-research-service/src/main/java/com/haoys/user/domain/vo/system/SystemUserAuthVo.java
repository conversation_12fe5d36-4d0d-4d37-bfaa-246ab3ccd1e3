package com.haoys.user.domain.vo.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.haoys.user.domain.vo.project.ProjectUserVo;
import com.haoys.user.domain.vo.project.ProjectVo;
import com.haoys.user.model.ProjectResearchersInfo;
import com.haoys.user.model.SystemRole;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class SystemUserAuthVo implements Serializable {

    private SystemUserInfoVo systemUserInfoVo;

    @ApiModelProperty(value = "系统角色列表")
    List<SystemRole> systemRoleList = new ArrayList<>();

    @ApiModelProperty(value = "项目列表")
    List<ProjectVo> projectList = new ArrayList<>();

    @ApiModelProperty(value = "是否授权全部数据库")
    private Boolean ownerTotalDatabaseAuth = false;

    @ApiModelProperty(value = "是否授权全部项目")
    private Boolean ownerTotalAuth = false;

    @ApiModelProperty(value = "项目授权列表")
    private List<ProjectUserVo> projectUserList = new ArrayList<>();

    @ApiModelProperty(value = "专病库授权列表")
    private List<SystemUserAuthVo.DiseaseDatabase> DiseaseDatabaseList = new ArrayList<>();

    @ApiModelProperty(value = "研究者信息")
    private ProjectResearchersInfo researchersInfo;

    @Data
    public static class DiseaseDatabase {
        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "数据库id")
        private Long databaseId;
        @ApiModelProperty(value = "数据库名称")
        private String databaseName;
        @ApiModelProperty(value = "授权开始日期")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date validateStartDate;
        @ApiModelProperty(value = "授权截止日期")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date validateEndDate;
    }

}
