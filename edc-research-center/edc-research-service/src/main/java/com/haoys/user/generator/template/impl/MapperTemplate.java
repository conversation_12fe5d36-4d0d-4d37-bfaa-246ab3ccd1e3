package com.haoys.user.generator.template.impl;

import com.haoys.user.generator.model.TableInfo;
import java.util.Map;

/**
 * Mapper接口模板
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
public class MapperTemplate {
    
    @SuppressWarnings("unchecked")
    public static String generate(Map<String, Object> context) {
        TableInfo tableInfo = (TableInfo) context.get("tableInfo");
        String mapperPackage = (String) context.get("mapperPackage");
        String modelPackage = (String) context.get("modelPackage");
        String entityName = (String) context.get("entityName");
        String mapperName = (String) context.get("mapperName");
        String primaryKeyType = (String) context.get("primaryKeyType");
        String currentDate = (String) context.get("currentDate");
        
        StringBuilder sb = new StringBuilder();
        
        // 包声明
        sb.append("package ").append(mapperPackage).append(";\n\n");
        
        // 导入语句
        sb.append("import ").append(modelPackage).append(".").append(entityName).append(";\n");
        sb.append("import org.apache.ibatis.annotations.Mapper;\n");
        sb.append("import org.apache.ibatis.annotations.Param;\n");
        sb.append("import org.springframework.stereotype.Repository;\n");
        sb.append("import java.util.List;\n");
        sb.append("import java.util.Map;\n\n");
        
        // 接口注释
        sb.append("/**\n");
        sb.append(" * ").append(tableInfo.getTableDescription()).append("Mapper接口\n");
        sb.append(" * 继承BaseQueryMapper，提供完整的CRUD操作、批量操作、复杂查询等功能\n");
        sb.append(" * \n");
        sb.append(" * <AUTHOR>
        sb.append(" * @version 2.0.0\n");
        sb.append(" * @since 1.0.0\n");
        sb.append(" * @date ").append(currentDate).append("\n");
        sb.append(" */\n");
        
        // 接口注解
        sb.append("@Mapper\n");
        sb.append("@Repository\n");
        
        // 接口声明
        sb.append("public interface ").append(mapperName).append(" {\n\n");

        // 基础CRUD方法
        sb.append("    // ========== 基础CRUD操作 ==========\n\n");

        // 插入方法
        sb.append("    /**\n");
        sb.append("     * 插入记录\n");
        sb.append("     * @param record 实体对象\n");
        sb.append("     * @return 影响行数\n");
        sb.append("     */\n");
        sb.append("    int insert(").append(entityName).append(" record);\n\n");

        sb.append("    /**\n");
        sb.append("     * 选择性插入记录\n");
        sb.append("     * @param record 实体对象\n");
        sb.append("     * @return 影响行数\n");
        sb.append("     */\n");
        sb.append("    int insertSelective(").append(entityName).append(" record);\n\n");

        // 删除方法
        sb.append("    /**\n");
        sb.append("     * 根据主键删除\n");
        sb.append("     * @param id 主键\n");
        sb.append("     * @return 影响行数\n");
        sb.append("     */\n");
        sb.append("    int deleteByPrimaryKey(").append(primaryKeyType).append(" id);\n\n");

        // 更新方法
        sb.append("    /**\n");
        sb.append("     * 根据主键更新\n");
        sb.append("     * @param record 实体对象\n");
        sb.append("     * @return 影响行数\n");
        sb.append("     */\n");
        sb.append("    int updateByPrimaryKey(").append(entityName).append(" record);\n\n");

        sb.append("    /**\n");
        sb.append("     * 根据主键选择性更新\n");
        sb.append("     * @param record 实体对象\n");
        sb.append("     * @return 影响行数\n");
        sb.append("     */\n");
        sb.append("    int updateByPrimaryKeySelective(").append(entityName).append(" record);\n\n");

        // 查询方法
        sb.append("    /**\n");
        sb.append("     * 根据主键查询\n");
        sb.append("     * @param id 主键\n");
        sb.append("     * @return 实体对象\n");
        sb.append("     */\n");
        sb.append("    ").append(entityName).append(" selectByPrimaryKey(").append(primaryKeyType).append(" id);\n\n");

        // 批量操作方法
        sb.append("    // ========== 批量操作 ==========\n\n");

        sb.append("    /**\n");
        sb.append("     * 批量插入\n");
        sb.append("     * @param records 实体对象列表\n");
        sb.append("     * @return 影响行数\n");
        sb.append("     */\n");
        sb.append("    int batchInsert(@Param(\"records\") List<").append(entityName).append("> records);\n\n");

        sb.append("    /**\n");
        sb.append("     * 批量删除\n");
        sb.append("     * @param ids 主键列表\n");
        sb.append("     * @return 影响行数\n");
        sb.append("     */\n");
        sb.append("    int batchDeleteByIds(@Param(\"ids\") List<").append(primaryKeyType).append("> ids);\n\n");

        sb.append("    /**\n");
        sb.append("     * 批量更新\n");
        sb.append("     * @param records 实体对象列表\n");
        sb.append("     * @return 影响行数\n");
        sb.append("     */\n");
        sb.append("    int batchUpdate(@Param(\"records\") List<").append(entityName).append("> records);\n\n");

        // 复杂查询方法
        sb.append("    // ========== 复杂查询操作 ==========\n\n");

        sb.append("    /**\n");
        sb.append("     * 根据条件查询列表\n");
        sb.append("     * @param params 查询参数\n");
        sb.append("     * @return 实体对象列表\n");
        sb.append("     */\n");
        sb.append("    List<").append(entityName).append("> selectByCondition(@Param(\"params\") Map<String, Object> params);\n\n");

        sb.append("    /**\n");
        sb.append("     * 根据条件查询单个对象\n");
        sb.append("     * @param params 查询参数\n");
        sb.append("     * @return 实体对象\n");
        sb.append("     */\n");
        sb.append("    ").append(entityName).append(" selectOneByCondition(@Param(\"params\") Map<String, Object> params);\n\n");

        sb.append("    /**\n");
        sb.append("     * 根据条件统计数量\n");
        sb.append("     * @param params 查询参数\n");
        sb.append("     * @return 记录数量\n");
        sb.append("     */\n");
        sb.append("    long countByCondition(@Param(\"params\") Map<String, Object> params);\n\n");

        sb.append("    /**\n");
        sb.append("     * 多条件AND查询\n");
        sb.append("     * @param conditions 条件映射\n");
        sb.append("     * @return 实体对象列表\n");
        sb.append("     */\n");
        sb.append("    List<").append(entityName).append("> selectByMultipleConditions(@Param(\"conditions\") Map<String, Object> conditions);\n\n");

        sb.append("    /**\n");
        sb.append("     * 范围查询\n");
        sb.append("     * @param field 字段名\n");
        sb.append("     * @param startValue 起始值\n");
        sb.append("     * @param endValue 结束值\n");
        sb.append("     * @return 实体对象列表\n");
        sb.append("     */\n");
        sb.append("    List<").append(entityName).append("> selectByRange(@Param(\"field\") String field, @Param(\"startValue\") Object startValue, @Param(\"endValue\") Object endValue);\n\n");

        sb.append("    /**\n");
        sb.append("     * 模糊查询\n");
        sb.append("     * @param field 字段名\n");
        sb.append("     * @param keyword 关键词\n");
        sb.append("     * @return 实体对象列表\n");
        sb.append("     */\n");
        sb.append("    List<").append(entityName).append("> selectByLike(@Param(\"field\") String field, @Param(\"keyword\") String keyword);\n\n");

        sb.append("    /**\n");
        sb.append("     * IN查询\n");
        sb.append("     * @param field 字段名\n");
        sb.append("     * @param values 值列表\n");
        sb.append("     * @return 实体对象列表\n");
        sb.append("     */\n");
        sb.append("    List<").append(entityName).append("> selectByIn(@Param(\"field\") String field, @Param(\"values\") List<Object> values);\n\n");

        // 简化聚合查询（只保留常用的统计功能）
        sb.append("    // ========== 基础统计查询 ==========\n\n");

        sb.append("    /**\n");
        sb.append("     * 根据条件统计总数\n");
        sb.append("     * @param params 查询参数\n");
        sb.append("     * @return 总数\n");
        sb.append("     */\n");
        sb.append("    long countTotal(@Param(\"params\") Map<String, Object> params);\n\n");

        sb.append("    /**\n");
        sb.append("     * 分组统计\n");
        sb.append("     * @param groupColumn 分组列名\n");
        sb.append("     * @param params 查询参数\n");
        sb.append("     * @return 分组统计结果\n");
        sb.append("     */\n");
        sb.append("    List<Map<String, Object>> groupByColumn(@Param(\"groupColumn\") String groupColumn, @Param(\"params\") Map<String, Object> params);\n\n");

        // 自定义查询方法区域
        sb.append("    // ========== 自定义查询方法 ==========\n");
        sb.append("    // 可以在这里添加业务特定的查询方法\n");
        sb.append("    // 例如：根据业务字段查询、状态查询等\n");
        sb.append("    // 注意：复杂的联表查询建议在Service层处理\n\n");

        sb.append("}\n");
        
        return sb.toString();
    }
}
