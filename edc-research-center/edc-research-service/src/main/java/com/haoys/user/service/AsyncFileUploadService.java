package com.haoys.user.service;

import com.haoys.user.common.file.ImageCompressionUtils;
import com.haoys.user.enums.system.OssTypeEnum;
import com.haoys.user.storge.cloud.OssStorageConfig;
import com.haoys.user.storge.cloud.localStorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.concurrent.CompletableFuture;

/**
 * 异步文件上传服务
 * 用于提高文件上传性能，避免阻塞主流程
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AsyncFileUploadService {

    private final OssStorageConfig storageConfig;
    private final localStorageService torageService;

    /**
     * 异步上传文件到本地存储
     * 当使用云存储时，为了增加可靠性，同时备份到本地存储
     * 
     * @param file 原始文件
     * @param path 存储路径
     * @return CompletableFuture<Boolean> 上传结果
     */
    @Async("fileUploadExecutor")
    public CompletableFuture<Boolean> uploadToLocalStorageAsync(MultipartFile file, String path) {
        try {
            // 只有在使用云存储时才需要本地备份
            if (storageConfig.getOssType().getValue() == OssTypeEnum.LOCAL.getValue()) {
                log.debug("当前使用本地存储，无需异步备份");
                return CompletableFuture.completedFuture(true);
            }

            log.info("开始异步上传文件到本地存储: {}", file.getOriginalFilename());
            
            // 为本地存储重新创建输入流
            InputStream localInputStream = createInputStream(file);
            
            // 上传到本地存储
            torageService.upload(localInputStream, path);
            
            log.info("异步上传文件到本地存储完成: {}", file.getOriginalFilename());
            return CompletableFuture.completedFuture(true);
            
        } catch (Exception e) {
            log.error("异步上传文件到本地存储失败: {}", file.getOriginalFilename(), e);
            return CompletableFuture.completedFuture(false);
        }
    }

    /**
     * 批量异步上传文件到本地存储
     * 
     * @param files 文件数组
     * @param paths 对应的存储路径数组
     * @return CompletableFuture<Boolean> 批量上传结果
     */
    @Async("fileUploadExecutor")
    public CompletableFuture<Boolean> batchUploadToLocalStorageAsync(MultipartFile[] files, String[] paths) {
        try {
            if (files == null || paths == null || files.length != paths.length) {
                log.error("批量异步上传参数错误");
                return CompletableFuture.completedFuture(false);
            }

            // 只有在使用云存储时才需要本地备份
            if (storageConfig.getOssType().getValue() == OssTypeEnum.LOCAL.getValue()) {
                log.debug("当前使用本地存储，无需异步备份");
                return CompletableFuture.completedFuture(true);
            }

            log.info("开始批量异步上传文件到本地存储，文件数量: {}", files.length);
            
            for (int i = 0; i < files.length; i++) {
                try {
                    InputStream localInputStream = createInputStream(files[i]);
                    torageService.upload(localInputStream, paths[i]);
                    log.debug("异步上传文件完成: {}", files[i].getOriginalFilename());
                } catch (Exception e) {
                    log.error("异步上传单个文件失败: {}", files[i].getOriginalFilename(), e);
                    // 继续处理其他文件，不中断整个批量上传
                }
            }
            
            log.info("批量异步上传文件到本地存储完成");
            return CompletableFuture.completedFuture(true);
            
        } catch (Exception e) {
            log.error("批量异步上传文件到本地存储失败", e);
            return CompletableFuture.completedFuture(false);
        }
    }

    /**
     * 创建输入流，支持图片压缩
     * 
     * @param file 原始文件
     * @return InputStream
     * @throws Exception 创建失败异常
     */
    private InputStream createInputStream(MultipartFile file) throws Exception {
        try {
            if (ImageCompressionUtils.isImageFile(file)) {
                return ImageCompressionUtils.createCompressedInputStream(file);
            } else {
                return file.getInputStream();
            }
        } catch (Exception e) {
            log.error("为本地存储创建压缩流失败，使用原文件: {}", file.getOriginalFilename(), e);
            return file.getInputStream();
        }
    }
}
