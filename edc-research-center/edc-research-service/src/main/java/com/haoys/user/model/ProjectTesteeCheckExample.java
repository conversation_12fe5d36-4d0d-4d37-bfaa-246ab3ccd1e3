package com.haoys.user.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProjectTesteeCheckExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectTesteeCheckExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andPlanIdIsNull() {
            addCriterion("plan_id is null");
            return (Criteria) this;
        }

        public Criteria andPlanIdIsNotNull() {
            addCriterion("plan_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlanIdEqualTo(Long value) {
            addCriterion("plan_id =", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotEqualTo(Long value) {
            addCriterion("plan_id <>", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdGreaterThan(Long value) {
            addCriterion("plan_id >", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdGreaterThanOrEqualTo(Long value) {
            addCriterion("plan_id >=", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdLessThan(Long value) {
            addCriterion("plan_id <", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdLessThanOrEqualTo(Long value) {
            addCriterion("plan_id <=", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdIn(List<Long> values) {
            addCriterion("plan_id in", values, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotIn(List<Long> values) {
            addCriterion("plan_id not in", values, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdBetween(Long value1, Long value2) {
            addCriterion("plan_id between", value1, value2, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotBetween(Long value1, Long value2) {
            addCriterion("plan_id not between", value1, value2, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanNameIsNull() {
            addCriterion("plan_name is null");
            return (Criteria) this;
        }

        public Criteria andPlanNameIsNotNull() {
            addCriterion("plan_name is not null");
            return (Criteria) this;
        }

        public Criteria andPlanNameEqualTo(String value) {
            addCriterion("plan_name =", value, "planName");
            return (Criteria) this;
        }

        public Criteria andPlanNameNotEqualTo(String value) {
            addCriterion("plan_name <>", value, "planName");
            return (Criteria) this;
        }

        public Criteria andPlanNameGreaterThan(String value) {
            addCriterion("plan_name >", value, "planName");
            return (Criteria) this;
        }

        public Criteria andPlanNameGreaterThanOrEqualTo(String value) {
            addCriterion("plan_name >=", value, "planName");
            return (Criteria) this;
        }

        public Criteria andPlanNameLessThan(String value) {
            addCriterion("plan_name <", value, "planName");
            return (Criteria) this;
        }

        public Criteria andPlanNameLessThanOrEqualTo(String value) {
            addCriterion("plan_name <=", value, "planName");
            return (Criteria) this;
        }

        public Criteria andPlanNameLike(String value) {
            addCriterion("plan_name like", value, "planName");
            return (Criteria) this;
        }

        public Criteria andPlanNameNotLike(String value) {
            addCriterion("plan_name not like", value, "planName");
            return (Criteria) this;
        }

        public Criteria andPlanNameIn(List<String> values) {
            addCriterion("plan_name in", values, "planName");
            return (Criteria) this;
        }

        public Criteria andPlanNameNotIn(List<String> values) {
            addCriterion("plan_name not in", values, "planName");
            return (Criteria) this;
        }

        public Criteria andPlanNameBetween(String value1, String value2) {
            addCriterion("plan_name between", value1, value2, "planName");
            return (Criteria) this;
        }

        public Criteria andPlanNameNotBetween(String value1, String value2) {
            addCriterion("plan_name not between", value1, value2, "planName");
            return (Criteria) this;
        }

        public Criteria andVisitIdIsNull() {
            addCriterion("visit_id is null");
            return (Criteria) this;
        }

        public Criteria andVisitIdIsNotNull() {
            addCriterion("visit_id is not null");
            return (Criteria) this;
        }

        public Criteria andVisitIdEqualTo(Long value) {
            addCriterion("visit_id =", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdNotEqualTo(Long value) {
            addCriterion("visit_id <>", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdGreaterThan(Long value) {
            addCriterion("visit_id >", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("visit_id >=", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdLessThan(Long value) {
            addCriterion("visit_id <", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdLessThanOrEqualTo(Long value) {
            addCriterion("visit_id <=", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdIn(List<Long> values) {
            addCriterion("visit_id in", values, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdNotIn(List<Long> values) {
            addCriterion("visit_id not in", values, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdBetween(Long value1, Long value2) {
            addCriterion("visit_id between", value1, value2, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdNotBetween(Long value1, Long value2) {
            addCriterion("visit_id not between", value1, value2, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitNameIsNull() {
            addCriterion("visit_name is null");
            return (Criteria) this;
        }

        public Criteria andVisitNameIsNotNull() {
            addCriterion("visit_name is not null");
            return (Criteria) this;
        }

        public Criteria andVisitNameEqualTo(String value) {
            addCriterion("visit_name =", value, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitNameNotEqualTo(String value) {
            addCriterion("visit_name <>", value, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitNameGreaterThan(String value) {
            addCriterion("visit_name >", value, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitNameGreaterThanOrEqualTo(String value) {
            addCriterion("visit_name >=", value, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitNameLessThan(String value) {
            addCriterion("visit_name <", value, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitNameLessThanOrEqualTo(String value) {
            addCriterion("visit_name <=", value, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitNameLike(String value) {
            addCriterion("visit_name like", value, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitNameNotLike(String value) {
            addCriterion("visit_name not like", value, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitNameIn(List<String> values) {
            addCriterion("visit_name in", values, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitNameNotIn(List<String> values) {
            addCriterion("visit_name not in", values, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitNameBetween(String value1, String value2) {
            addCriterion("visit_name between", value1, value2, "visitName");
            return (Criteria) this;
        }

        public Criteria andVisitNameNotBetween(String value1, String value2) {
            addCriterion("visit_name not between", value1, value2, "visitName");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNull() {
            addCriterion("form_id is null");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNotNull() {
            addCriterion("form_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormIdEqualTo(Long value) {
            addCriterion("form_id =", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotEqualTo(Long value) {
            addCriterion("form_id <>", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThan(Long value) {
            addCriterion("form_id >", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_id >=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThan(Long value) {
            addCriterion("form_id <", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThanOrEqualTo(Long value) {
            addCriterion("form_id <=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdIn(List<Long> values) {
            addCriterion("form_id in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotIn(List<Long> values) {
            addCriterion("form_id not in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdBetween(Long value1, Long value2) {
            addCriterion("form_id between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotBetween(Long value1, Long value2) {
            addCriterion("form_id not between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andFormNameIsNull() {
            addCriterion("form_name is null");
            return (Criteria) this;
        }

        public Criteria andFormNameIsNotNull() {
            addCriterion("form_name is not null");
            return (Criteria) this;
        }

        public Criteria andFormNameEqualTo(String value) {
            addCriterion("form_name =", value, "formName");
            return (Criteria) this;
        }

        public Criteria andFormNameNotEqualTo(String value) {
            addCriterion("form_name <>", value, "formName");
            return (Criteria) this;
        }

        public Criteria andFormNameGreaterThan(String value) {
            addCriterion("form_name >", value, "formName");
            return (Criteria) this;
        }

        public Criteria andFormNameGreaterThanOrEqualTo(String value) {
            addCriterion("form_name >=", value, "formName");
            return (Criteria) this;
        }

        public Criteria andFormNameLessThan(String value) {
            addCriterion("form_name <", value, "formName");
            return (Criteria) this;
        }

        public Criteria andFormNameLessThanOrEqualTo(String value) {
            addCriterion("form_name <=", value, "formName");
            return (Criteria) this;
        }

        public Criteria andFormNameLike(String value) {
            addCriterion("form_name like", value, "formName");
            return (Criteria) this;
        }

        public Criteria andFormNameNotLike(String value) {
            addCriterion("form_name not like", value, "formName");
            return (Criteria) this;
        }

        public Criteria andFormNameIn(List<String> values) {
            addCriterion("form_name in", values, "formName");
            return (Criteria) this;
        }

        public Criteria andFormNameNotIn(List<String> values) {
            addCriterion("form_name not in", values, "formName");
            return (Criteria) this;
        }

        public Criteria andFormNameBetween(String value1, String value2) {
            addCriterion("form_name between", value1, value2, "formName");
            return (Criteria) this;
        }

        public Criteria andFormNameNotBetween(String value1, String value2) {
            addCriterion("form_name not between", value1, value2, "formName");
            return (Criteria) this;
        }

        public Criteria andTesteeIdIsNull() {
            addCriterion("testee_id is null");
            return (Criteria) this;
        }

        public Criteria andTesteeIdIsNotNull() {
            addCriterion("testee_id is not null");
            return (Criteria) this;
        }

        public Criteria andTesteeIdEqualTo(Long value) {
            addCriterion("testee_id =", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdNotEqualTo(Long value) {
            addCriterion("testee_id <>", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdGreaterThan(Long value) {
            addCriterion("testee_id >", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("testee_id >=", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdLessThan(Long value) {
            addCriterion("testee_id <", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdLessThanOrEqualTo(Long value) {
            addCriterion("testee_id <=", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdIn(List<Long> values) {
            addCriterion("testee_id in", values, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdNotIn(List<Long> values) {
            addCriterion("testee_id not in", values, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdBetween(Long value1, Long value2) {
            addCriterion("testee_id between", value1, value2, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdNotBetween(Long value1, Long value2) {
            addCriterion("testee_id not between", value1, value2, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeIsNull() {
            addCriterion("testee_code is null");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeIsNotNull() {
            addCriterion("testee_code is not null");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeEqualTo(String value) {
            addCriterion("testee_code =", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeNotEqualTo(String value) {
            addCriterion("testee_code <>", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeGreaterThan(String value) {
            addCriterion("testee_code >", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("testee_code >=", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeLessThan(String value) {
            addCriterion("testee_code <", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeLessThanOrEqualTo(String value) {
            addCriterion("testee_code <=", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeLike(String value) {
            addCriterion("testee_code like", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeNotLike(String value) {
            addCriterion("testee_code not like", value, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeIn(List<String> values) {
            addCriterion("testee_code in", values, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeNotIn(List<String> values) {
            addCriterion("testee_code not in", values, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeBetween(String value1, String value2) {
            addCriterion("testee_code between", value1, value2, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andTesteeCodeNotBetween(String value1, String value2) {
            addCriterion("testee_code not between", value1, value2, "testeeCode");
            return (Criteria) this;
        }

        public Criteria andVariableIdIsNull() {
            addCriterion("variable_id is null");
            return (Criteria) this;
        }

        public Criteria andVariableIdIsNotNull() {
            addCriterion("variable_id is not null");
            return (Criteria) this;
        }

        public Criteria andVariableIdEqualTo(Long value) {
            addCriterion("variable_id =", value, "variableId");
            return (Criteria) this;
        }

        public Criteria andVariableIdNotEqualTo(Long value) {
            addCriterion("variable_id <>", value, "variableId");
            return (Criteria) this;
        }

        public Criteria andVariableIdGreaterThan(Long value) {
            addCriterion("variable_id >", value, "variableId");
            return (Criteria) this;
        }

        public Criteria andVariableIdGreaterThanOrEqualTo(Long value) {
            addCriterion("variable_id >=", value, "variableId");
            return (Criteria) this;
        }

        public Criteria andVariableIdLessThan(Long value) {
            addCriterion("variable_id <", value, "variableId");
            return (Criteria) this;
        }

        public Criteria andVariableIdLessThanOrEqualTo(Long value) {
            addCriterion("variable_id <=", value, "variableId");
            return (Criteria) this;
        }

        public Criteria andVariableIdIn(List<Long> values) {
            addCriterion("variable_id in", values, "variableId");
            return (Criteria) this;
        }

        public Criteria andVariableIdNotIn(List<Long> values) {
            addCriterion("variable_id not in", values, "variableId");
            return (Criteria) this;
        }

        public Criteria andVariableIdBetween(Long value1, Long value2) {
            addCriterion("variable_id between", value1, value2, "variableId");
            return (Criteria) this;
        }

        public Criteria andVariableIdNotBetween(Long value1, Long value2) {
            addCriterion("variable_id not between", value1, value2, "variableId");
            return (Criteria) this;
        }

        public Criteria andVariableTableIdIsNull() {
            addCriterion("variable_table_id is null");
            return (Criteria) this;
        }

        public Criteria andVariableTableIdIsNotNull() {
            addCriterion("variable_table_id is not null");
            return (Criteria) this;
        }

        public Criteria andVariableTableIdEqualTo(Long value) {
            addCriterion("variable_table_id =", value, "variableTableId");
            return (Criteria) this;
        }

        public Criteria andVariableTableIdNotEqualTo(Long value) {
            addCriterion("variable_table_id <>", value, "variableTableId");
            return (Criteria) this;
        }

        public Criteria andVariableTableIdGreaterThan(Long value) {
            addCriterion("variable_table_id >", value, "variableTableId");
            return (Criteria) this;
        }

        public Criteria andVariableTableIdGreaterThanOrEqualTo(Long value) {
            addCriterion("variable_table_id >=", value, "variableTableId");
            return (Criteria) this;
        }

        public Criteria andVariableTableIdLessThan(Long value) {
            addCriterion("variable_table_id <", value, "variableTableId");
            return (Criteria) this;
        }

        public Criteria andVariableTableIdLessThanOrEqualTo(Long value) {
            addCriterion("variable_table_id <=", value, "variableTableId");
            return (Criteria) this;
        }

        public Criteria andVariableTableIdIn(List<Long> values) {
            addCriterion("variable_table_id in", values, "variableTableId");
            return (Criteria) this;
        }

        public Criteria andVariableTableIdNotIn(List<Long> values) {
            addCriterion("variable_table_id not in", values, "variableTableId");
            return (Criteria) this;
        }

        public Criteria andVariableTableIdBetween(Long value1, Long value2) {
            addCriterion("variable_table_id between", value1, value2, "variableTableId");
            return (Criteria) this;
        }

        public Criteria andVariableTableIdNotBetween(Long value1, Long value2) {
            addCriterion("variable_table_id not between", value1, value2, "variableTableId");
            return (Criteria) this;
        }

        public Criteria andVariableResultIdIsNull() {
            addCriterion("variable_result_id is null");
            return (Criteria) this;
        }

        public Criteria andVariableResultIdIsNotNull() {
            addCriterion("variable_result_id is not null");
            return (Criteria) this;
        }

        public Criteria andVariableResultIdEqualTo(Long value) {
            addCriterion("variable_result_id =", value, "variableResultId");
            return (Criteria) this;
        }

        public Criteria andVariableResultIdNotEqualTo(Long value) {
            addCriterion("variable_result_id <>", value, "variableResultId");
            return (Criteria) this;
        }

        public Criteria andVariableResultIdGreaterThan(Long value) {
            addCriterion("variable_result_id >", value, "variableResultId");
            return (Criteria) this;
        }

        public Criteria andVariableResultIdGreaterThanOrEqualTo(Long value) {
            addCriterion("variable_result_id >=", value, "variableResultId");
            return (Criteria) this;
        }

        public Criteria andVariableResultIdLessThan(Long value) {
            addCriterion("variable_result_id <", value, "variableResultId");
            return (Criteria) this;
        }

        public Criteria andVariableResultIdLessThanOrEqualTo(Long value) {
            addCriterion("variable_result_id <=", value, "variableResultId");
            return (Criteria) this;
        }

        public Criteria andVariableResultIdIn(List<Long> values) {
            addCriterion("variable_result_id in", values, "variableResultId");
            return (Criteria) this;
        }

        public Criteria andVariableResultIdNotIn(List<Long> values) {
            addCriterion("variable_result_id not in", values, "variableResultId");
            return (Criteria) this;
        }

        public Criteria andVariableResultIdBetween(Long value1, Long value2) {
            addCriterion("variable_result_id between", value1, value2, "variableResultId");
            return (Criteria) this;
        }

        public Criteria andVariableResultIdNotBetween(Long value1, Long value2) {
            addCriterion("variable_result_id not between", value1, value2, "variableResultId");
            return (Criteria) this;
        }

        public Criteria andVariableNameIsNull() {
            addCriterion("variable_name is null");
            return (Criteria) this;
        }

        public Criteria andVariableNameIsNotNull() {
            addCriterion("variable_name is not null");
            return (Criteria) this;
        }

        public Criteria andVariableNameEqualTo(String value) {
            addCriterion("variable_name =", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameNotEqualTo(String value) {
            addCriterion("variable_name <>", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameGreaterThan(String value) {
            addCriterion("variable_name >", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameGreaterThanOrEqualTo(String value) {
            addCriterion("variable_name >=", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameLessThan(String value) {
            addCriterion("variable_name <", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameLessThanOrEqualTo(String value) {
            addCriterion("variable_name <=", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameLike(String value) {
            addCriterion("variable_name like", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameNotLike(String value) {
            addCriterion("variable_name not like", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameIn(List<String> values) {
            addCriterion("variable_name in", values, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameNotIn(List<String> values) {
            addCriterion("variable_name not in", values, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameBetween(String value1, String value2) {
            addCriterion("variable_name between", value1, value2, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameNotBetween(String value1, String value2) {
            addCriterion("variable_name not between", value1, value2, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableTypeIsNull() {
            addCriterion("variable_type is null");
            return (Criteria) this;
        }

        public Criteria andVariableTypeIsNotNull() {
            addCriterion("variable_type is not null");
            return (Criteria) this;
        }

        public Criteria andVariableTypeEqualTo(String value) {
            addCriterion("variable_type =", value, "variableType");
            return (Criteria) this;
        }

        public Criteria andVariableTypeNotEqualTo(String value) {
            addCriterion("variable_type <>", value, "variableType");
            return (Criteria) this;
        }

        public Criteria andVariableTypeGreaterThan(String value) {
            addCriterion("variable_type >", value, "variableType");
            return (Criteria) this;
        }

        public Criteria andVariableTypeGreaterThanOrEqualTo(String value) {
            addCriterion("variable_type >=", value, "variableType");
            return (Criteria) this;
        }

        public Criteria andVariableTypeLessThan(String value) {
            addCriterion("variable_type <", value, "variableType");
            return (Criteria) this;
        }

        public Criteria andVariableTypeLessThanOrEqualTo(String value) {
            addCriterion("variable_type <=", value, "variableType");
            return (Criteria) this;
        }

        public Criteria andVariableTypeLike(String value) {
            addCriterion("variable_type like", value, "variableType");
            return (Criteria) this;
        }

        public Criteria andVariableTypeNotLike(String value) {
            addCriterion("variable_type not like", value, "variableType");
            return (Criteria) this;
        }

        public Criteria andVariableTypeIn(List<String> values) {
            addCriterion("variable_type in", values, "variableType");
            return (Criteria) this;
        }

        public Criteria andVariableTypeNotIn(List<String> values) {
            addCriterion("variable_type not in", values, "variableType");
            return (Criteria) this;
        }

        public Criteria andVariableTypeBetween(String value1, String value2) {
            addCriterion("variable_type between", value1, value2, "variableType");
            return (Criteria) this;
        }

        public Criteria andVariableTypeNotBetween(String value1, String value2) {
            addCriterion("variable_type not between", value1, value2, "variableType");
            return (Criteria) this;
        }

        public Criteria andInputValueIsNull() {
            addCriterion("input_value is null");
            return (Criteria) this;
        }

        public Criteria andInputValueIsNotNull() {
            addCriterion("input_value is not null");
            return (Criteria) this;
        }

        public Criteria andInputValueEqualTo(String value) {
            addCriterion("input_value =", value, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueNotEqualTo(String value) {
            addCriterion("input_value <>", value, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueGreaterThan(String value) {
            addCriterion("input_value >", value, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueGreaterThanOrEqualTo(String value) {
            addCriterion("input_value >=", value, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueLessThan(String value) {
            addCriterion("input_value <", value, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueLessThanOrEqualTo(String value) {
            addCriterion("input_value <=", value, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueLike(String value) {
            addCriterion("input_value like", value, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueNotLike(String value) {
            addCriterion("input_value not like", value, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueIn(List<String> values) {
            addCriterion("input_value in", values, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueNotIn(List<String> values) {
            addCriterion("input_value not in", values, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueBetween(String value1, String value2) {
            addCriterion("input_value between", value1, value2, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueNotBetween(String value1, String value2) {
            addCriterion("input_value not between", value1, value2, "inputValue");
            return (Criteria) this;
        }

        public Criteria andFieldTextIsNull() {
            addCriterion("field_text is null");
            return (Criteria) this;
        }

        public Criteria andFieldTextIsNotNull() {
            addCriterion("field_text is not null");
            return (Criteria) this;
        }

        public Criteria andFieldTextEqualTo(String value) {
            addCriterion("field_text =", value, "fieldText");
            return (Criteria) this;
        }

        public Criteria andFieldTextNotEqualTo(String value) {
            addCriterion("field_text <>", value, "fieldText");
            return (Criteria) this;
        }

        public Criteria andFieldTextGreaterThan(String value) {
            addCriterion("field_text >", value, "fieldText");
            return (Criteria) this;
        }

        public Criteria andFieldTextGreaterThanOrEqualTo(String value) {
            addCriterion("field_text >=", value, "fieldText");
            return (Criteria) this;
        }

        public Criteria andFieldTextLessThan(String value) {
            addCriterion("field_text <", value, "fieldText");
            return (Criteria) this;
        }

        public Criteria andFieldTextLessThanOrEqualTo(String value) {
            addCriterion("field_text <=", value, "fieldText");
            return (Criteria) this;
        }

        public Criteria andFieldTextLike(String value) {
            addCriterion("field_text like", value, "fieldText");
            return (Criteria) this;
        }

        public Criteria andFieldTextNotLike(String value) {
            addCriterion("field_text not like", value, "fieldText");
            return (Criteria) this;
        }

        public Criteria andFieldTextIn(List<String> values) {
            addCriterion("field_text in", values, "fieldText");
            return (Criteria) this;
        }

        public Criteria andFieldTextNotIn(List<String> values) {
            addCriterion("field_text not in", values, "fieldText");
            return (Criteria) this;
        }

        public Criteria andFieldTextBetween(String value1, String value2) {
            addCriterion("field_text between", value1, value2, "fieldText");
            return (Criteria) this;
        }

        public Criteria andFieldTextNotBetween(String value1, String value2) {
            addCriterion("field_text not between", value1, value2, "fieldText");
            return (Criteria) this;
        }

        public Criteria andUnitValueIsNull() {
            addCriterion("unit_value is null");
            return (Criteria) this;
        }

        public Criteria andUnitValueIsNotNull() {
            addCriterion("unit_value is not null");
            return (Criteria) this;
        }

        public Criteria andUnitValueEqualTo(String value) {
            addCriterion("unit_value =", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueNotEqualTo(String value) {
            addCriterion("unit_value <>", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueGreaterThan(String value) {
            addCriterion("unit_value >", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueGreaterThanOrEqualTo(String value) {
            addCriterion("unit_value >=", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueLessThan(String value) {
            addCriterion("unit_value <", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueLessThanOrEqualTo(String value) {
            addCriterion("unit_value <=", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueLike(String value) {
            addCriterion("unit_value like", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueNotLike(String value) {
            addCriterion("unit_value not like", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueIn(List<String> values) {
            addCriterion("unit_value in", values, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueNotIn(List<String> values) {
            addCriterion("unit_value not in", values, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueBetween(String value1, String value2) {
            addCriterion("unit_value between", value1, value2, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueNotBetween(String value1, String value2) {
            addCriterion("unit_value not between", value1, value2, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitTextIsNull() {
            addCriterion("unit_text is null");
            return (Criteria) this;
        }

        public Criteria andUnitTextIsNotNull() {
            addCriterion("unit_text is not null");
            return (Criteria) this;
        }

        public Criteria andUnitTextEqualTo(String value) {
            addCriterion("unit_text =", value, "unitText");
            return (Criteria) this;
        }

        public Criteria andUnitTextNotEqualTo(String value) {
            addCriterion("unit_text <>", value, "unitText");
            return (Criteria) this;
        }

        public Criteria andUnitTextGreaterThan(String value) {
            addCriterion("unit_text >", value, "unitText");
            return (Criteria) this;
        }

        public Criteria andUnitTextGreaterThanOrEqualTo(String value) {
            addCriterion("unit_text >=", value, "unitText");
            return (Criteria) this;
        }

        public Criteria andUnitTextLessThan(String value) {
            addCriterion("unit_text <", value, "unitText");
            return (Criteria) this;
        }

        public Criteria andUnitTextLessThanOrEqualTo(String value) {
            addCriterion("unit_text <=", value, "unitText");
            return (Criteria) this;
        }

        public Criteria andUnitTextLike(String value) {
            addCriterion("unit_text like", value, "unitText");
            return (Criteria) this;
        }

        public Criteria andUnitTextNotLike(String value) {
            addCriterion("unit_text not like", value, "unitText");
            return (Criteria) this;
        }

        public Criteria andUnitTextIn(List<String> values) {
            addCriterion("unit_text in", values, "unitText");
            return (Criteria) this;
        }

        public Criteria andUnitTextNotIn(List<String> values) {
            addCriterion("unit_text not in", values, "unitText");
            return (Criteria) this;
        }

        public Criteria andUnitTextBetween(String value1, String value2) {
            addCriterion("unit_text between", value1, value2, "unitText");
            return (Criteria) this;
        }

        public Criteria andUnitTextNotBetween(String value1, String value2) {
            addCriterion("unit_text not between", value1, value2, "unitText");
            return (Criteria) this;
        }

        public Criteria andOriginalValueIsNull() {
            addCriterion("original_value is null");
            return (Criteria) this;
        }

        public Criteria andOriginalValueIsNotNull() {
            addCriterion("original_value is not null");
            return (Criteria) this;
        }

        public Criteria andOriginalValueEqualTo(String value) {
            addCriterion("original_value =", value, "originalValue");
            return (Criteria) this;
        }

        public Criteria andOriginalValueNotEqualTo(String value) {
            addCriterion("original_value <>", value, "originalValue");
            return (Criteria) this;
        }

        public Criteria andOriginalValueGreaterThan(String value) {
            addCriterion("original_value >", value, "originalValue");
            return (Criteria) this;
        }

        public Criteria andOriginalValueGreaterThanOrEqualTo(String value) {
            addCriterion("original_value >=", value, "originalValue");
            return (Criteria) this;
        }

        public Criteria andOriginalValueLessThan(String value) {
            addCriterion("original_value <", value, "originalValue");
            return (Criteria) this;
        }

        public Criteria andOriginalValueLessThanOrEqualTo(String value) {
            addCriterion("original_value <=", value, "originalValue");
            return (Criteria) this;
        }

        public Criteria andOriginalValueLike(String value) {
            addCriterion("original_value like", value, "originalValue");
            return (Criteria) this;
        }

        public Criteria andOriginalValueNotLike(String value) {
            addCriterion("original_value not like", value, "originalValue");
            return (Criteria) this;
        }

        public Criteria andOriginalValueIn(List<String> values) {
            addCriterion("original_value in", values, "originalValue");
            return (Criteria) this;
        }

        public Criteria andOriginalValueNotIn(List<String> values) {
            addCriterion("original_value not in", values, "originalValue");
            return (Criteria) this;
        }

        public Criteria andOriginalValueBetween(String value1, String value2) {
            addCriterion("original_value between", value1, value2, "originalValue");
            return (Criteria) this;
        }

        public Criteria andOriginalValueNotBetween(String value1, String value2) {
            addCriterion("original_value not between", value1, value2, "originalValue");
            return (Criteria) this;
        }

        public Criteria andProjectRoleCodeIsNull() {
            addCriterion("project_role_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectRoleCodeIsNotNull() {
            addCriterion("project_role_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectRoleCodeEqualTo(String value) {
            addCriterion("project_role_code =", value, "projectRoleCode");
            return (Criteria) this;
        }

        public Criteria andProjectRoleCodeNotEqualTo(String value) {
            addCriterion("project_role_code <>", value, "projectRoleCode");
            return (Criteria) this;
        }

        public Criteria andProjectRoleCodeGreaterThan(String value) {
            addCriterion("project_role_code >", value, "projectRoleCode");
            return (Criteria) this;
        }

        public Criteria andProjectRoleCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_role_code >=", value, "projectRoleCode");
            return (Criteria) this;
        }

        public Criteria andProjectRoleCodeLessThan(String value) {
            addCriterion("project_role_code <", value, "projectRoleCode");
            return (Criteria) this;
        }

        public Criteria andProjectRoleCodeLessThanOrEqualTo(String value) {
            addCriterion("project_role_code <=", value, "projectRoleCode");
            return (Criteria) this;
        }

        public Criteria andProjectRoleCodeLike(String value) {
            addCriterion("project_role_code like", value, "projectRoleCode");
            return (Criteria) this;
        }

        public Criteria andProjectRoleCodeNotLike(String value) {
            addCriterion("project_role_code not like", value, "projectRoleCode");
            return (Criteria) this;
        }

        public Criteria andProjectRoleCodeIn(List<String> values) {
            addCriterion("project_role_code in", values, "projectRoleCode");
            return (Criteria) this;
        }

        public Criteria andProjectRoleCodeNotIn(List<String> values) {
            addCriterion("project_role_code not in", values, "projectRoleCode");
            return (Criteria) this;
        }

        public Criteria andProjectRoleCodeBetween(String value1, String value2) {
            addCriterion("project_role_code between", value1, value2, "projectRoleCode");
            return (Criteria) this;
        }

        public Criteria andProjectRoleCodeNotBetween(String value1, String value2) {
            addCriterion("project_role_code not between", value1, value2, "projectRoleCode");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(String value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(String value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(String value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(String value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(String value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(String value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLike(String value) {
            addCriterion("type like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotLike(String value) {
            addCriterion("type not like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<String> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<String> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(String value1, String value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(String value1, String value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andContentIsNull() {
            addCriterion("content is null");
            return (Criteria) this;
        }

        public Criteria andContentIsNotNull() {
            addCriterion("content is not null");
            return (Criteria) this;
        }

        public Criteria andContentEqualTo(String value) {
            addCriterion("content =", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotEqualTo(String value) {
            addCriterion("content <>", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentGreaterThan(String value) {
            addCriterion("content >", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentGreaterThanOrEqualTo(String value) {
            addCriterion("content >=", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLessThan(String value) {
            addCriterion("content <", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLessThanOrEqualTo(String value) {
            addCriterion("content <=", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLike(String value) {
            addCriterion("content like", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotLike(String value) {
            addCriterion("content not like", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentIn(List<String> values) {
            addCriterion("content in", values, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotIn(List<String> values) {
            addCriterion("content not in", values, "content");
            return (Criteria) this;
        }

        public Criteria andContentBetween(String value1, String value2) {
            addCriterion("content between", value1, value2, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotBetween(String value1, String value2) {
            addCriterion("content not between", value1, value2, "content");
            return (Criteria) this;
        }

        public Criteria andExpandIsNull() {
            addCriterion("expand is null");
            return (Criteria) this;
        }

        public Criteria andExpandIsNotNull() {
            addCriterion("expand is not null");
            return (Criteria) this;
        }

        public Criteria andExpandEqualTo(String value) {
            addCriterion("expand =", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotEqualTo(String value) {
            addCriterion("expand <>", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandGreaterThan(String value) {
            addCriterion("expand >", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandGreaterThanOrEqualTo(String value) {
            addCriterion("expand >=", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLessThan(String value) {
            addCriterion("expand <", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLessThanOrEqualTo(String value) {
            addCriterion("expand <=", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLike(String value) {
            addCriterion("expand like", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotLike(String value) {
            addCriterion("expand not like", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandIn(List<String> values) {
            addCriterion("expand in", values, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotIn(List<String> values) {
            addCriterion("expand not in", values, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandBetween(String value1, String value2) {
            addCriterion("expand between", value1, value2, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotBetween(String value1, String value2) {
            addCriterion("expand not between", value1, value2, "expand");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andVisitIpIsNull() {
            addCriterion("visit_ip is null");
            return (Criteria) this;
        }

        public Criteria andVisitIpIsNotNull() {
            addCriterion("visit_ip is not null");
            return (Criteria) this;
        }

        public Criteria andVisitIpEqualTo(String value) {
            addCriterion("visit_ip =", value, "visitIp");
            return (Criteria) this;
        }

        public Criteria andVisitIpNotEqualTo(String value) {
            addCriterion("visit_ip <>", value, "visitIp");
            return (Criteria) this;
        }

        public Criteria andVisitIpGreaterThan(String value) {
            addCriterion("visit_ip >", value, "visitIp");
            return (Criteria) this;
        }

        public Criteria andVisitIpGreaterThanOrEqualTo(String value) {
            addCriterion("visit_ip >=", value, "visitIp");
            return (Criteria) this;
        }

        public Criteria andVisitIpLessThan(String value) {
            addCriterion("visit_ip <", value, "visitIp");
            return (Criteria) this;
        }

        public Criteria andVisitIpLessThanOrEqualTo(String value) {
            addCriterion("visit_ip <=", value, "visitIp");
            return (Criteria) this;
        }

        public Criteria andVisitIpLike(String value) {
            addCriterion("visit_ip like", value, "visitIp");
            return (Criteria) this;
        }

        public Criteria andVisitIpNotLike(String value) {
            addCriterion("visit_ip not like", value, "visitIp");
            return (Criteria) this;
        }

        public Criteria andVisitIpIn(List<String> values) {
            addCriterion("visit_ip in", values, "visitIp");
            return (Criteria) this;
        }

        public Criteria andVisitIpNotIn(List<String> values) {
            addCriterion("visit_ip not in", values, "visitIp");
            return (Criteria) this;
        }

        public Criteria andVisitIpBetween(String value1, String value2) {
            addCriterion("visit_ip between", value1, value2, "visitIp");
            return (Criteria) this;
        }

        public Criteria andVisitIpNotBetween(String value1, String value2) {
            addCriterion("visit_ip not between", value1, value2, "visitIp");
            return (Criteria) this;
        }

        public Criteria andLogTypeIsNull() {
            addCriterion("log_type is null");
            return (Criteria) this;
        }

        public Criteria andLogTypeIsNotNull() {
            addCriterion("log_type is not null");
            return (Criteria) this;
        }

        public Criteria andLogTypeEqualTo(String value) {
            addCriterion("log_type =", value, "logType");
            return (Criteria) this;
        }

        public Criteria andLogTypeNotEqualTo(String value) {
            addCriterion("log_type <>", value, "logType");
            return (Criteria) this;
        }

        public Criteria andLogTypeGreaterThan(String value) {
            addCriterion("log_type >", value, "logType");
            return (Criteria) this;
        }

        public Criteria andLogTypeGreaterThanOrEqualTo(String value) {
            addCriterion("log_type >=", value, "logType");
            return (Criteria) this;
        }

        public Criteria andLogTypeLessThan(String value) {
            addCriterion("log_type <", value, "logType");
            return (Criteria) this;
        }

        public Criteria andLogTypeLessThanOrEqualTo(String value) {
            addCriterion("log_type <=", value, "logType");
            return (Criteria) this;
        }

        public Criteria andLogTypeLike(String value) {
            addCriterion("log_type like", value, "logType");
            return (Criteria) this;
        }

        public Criteria andLogTypeNotLike(String value) {
            addCriterion("log_type not like", value, "logType");
            return (Criteria) this;
        }

        public Criteria andLogTypeIn(List<String> values) {
            addCriterion("log_type in", values, "logType");
            return (Criteria) this;
        }

        public Criteria andLogTypeNotIn(List<String> values) {
            addCriterion("log_type not in", values, "logType");
            return (Criteria) this;
        }

        public Criteria andLogTypeBetween(String value1, String value2) {
            addCriterion("log_type between", value1, value2, "logType");
            return (Criteria) this;
        }

        public Criteria andLogTypeNotBetween(String value1, String value2) {
            addCriterion("log_type not between", value1, value2, "logType");
            return (Criteria) this;
        }

        public Criteria andDataFromIsNull() {
            addCriterion("data_from is null");
            return (Criteria) this;
        }

        public Criteria andDataFromIsNotNull() {
            addCriterion("data_from is not null");
            return (Criteria) this;
        }

        public Criteria andDataFromEqualTo(String value) {
            addCriterion("data_from =", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromNotEqualTo(String value) {
            addCriterion("data_from <>", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromGreaterThan(String value) {
            addCriterion("data_from >", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromGreaterThanOrEqualTo(String value) {
            addCriterion("data_from >=", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromLessThan(String value) {
            addCriterion("data_from <", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromLessThanOrEqualTo(String value) {
            addCriterion("data_from <=", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromLike(String value) {
            addCriterion("data_from like", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromNotLike(String value) {
            addCriterion("data_from not like", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromIn(List<String> values) {
            addCriterion("data_from in", values, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromNotIn(List<String> values) {
            addCriterion("data_from not in", values, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromBetween(String value1, String value2) {
            addCriterion("data_from between", value1, value2, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromNotBetween(String value1, String value2) {
            addCriterion("data_from not between", value1, value2, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(String value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(String value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(String value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(String value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLike(String value) {
            addCriterion("create_user_id like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotLike(String value) {
            addCriterion("create_user_id not like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<String> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<String> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(String value1, String value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(String value1, String value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameIsNull() {
            addCriterion("create_user_name is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameIsNotNull() {
            addCriterion("create_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameEqualTo(String value) {
            addCriterion("create_user_name =", value, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameNotEqualTo(String value) {
            addCriterion("create_user_name <>", value, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameGreaterThan(String value) {
            addCriterion("create_user_name >", value, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("create_user_name >=", value, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameLessThan(String value) {
            addCriterion("create_user_name <", value, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameLessThanOrEqualTo(String value) {
            addCriterion("create_user_name <=", value, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameLike(String value) {
            addCriterion("create_user_name like", value, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameNotLike(String value) {
            addCriterion("create_user_name not like", value, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameIn(List<String> values) {
            addCriterion("create_user_name in", values, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameNotIn(List<String> values) {
            addCriterion("create_user_name not in", values, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameBetween(String value1, String value2) {
            addCriterion("create_user_name between", value1, value2, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameNotBetween(String value1, String value2) {
            addCriterion("create_user_name not between", value1, value2, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNull() {
            addCriterion("platform_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNotNull() {
            addCriterion("platform_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdEqualTo(String value) {
            addCriterion("platform_id =", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotEqualTo(String value) {
            addCriterion("platform_id <>", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThan(String value) {
            addCriterion("platform_id >", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThanOrEqualTo(String value) {
            addCriterion("platform_id >=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThan(String value) {
            addCriterion("platform_id <", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThanOrEqualTo(String value) {
            addCriterion("platform_id <=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLike(String value) {
            addCriterion("platform_id like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotLike(String value) {
            addCriterion("platform_id not like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIn(List<String> values) {
            addCriterion("platform_id in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotIn(List<String> values) {
            addCriterion("platform_id not in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdBetween(String value1, String value2) {
            addCriterion("platform_id between", value1, value2, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotBetween(String value1, String value2) {
            addCriterion("platform_id not between", value1, value2, "platformId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}