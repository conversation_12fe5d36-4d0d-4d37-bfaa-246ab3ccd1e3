package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.domain.dto.SystemDictionaryParam;
import com.haoys.user.domain.param.dict.DictParamQuery;
import com.haoys.user.domain.vo.system.DictionaryVo;
import com.haoys.user.model.Dictionary;
import com.haoys.user.model.DictionaryFrom;

import java.util.List;

public interface DictionaryService {

    /**
     * 查询字典列表
     * @param param 搜索条件
     * @return
     */
    CommonPage<DictionaryVo> getDictionaryListForPage(DictParamQuery param);

    /**
     * 保存系统字典以及系统字典的值
     * @param systemDictionaryParam
     * @return
     */
    CommonResult<Object> saveDictionary(SystemDictionaryParam systemDictionaryParam);
    /**
     * 获取系统字典以及系统字典的值
     * @param id 字典id
     * @return
     */
    Dictionary getDictionaryInfo(String id);

    /**
     * 修改字典信息
     * @param systemDictionaryParam
     * @return
     */
    CommonResult<Object> updateDictionary(SystemDictionaryParam systemDictionaryParam);
    /**
     * 根据字典名称 获取字典
     * @param dictName 字典名称
     * @param parentId
     * @return
     */
    Dictionary getDictByName(String dictName,Long parentId);

    /**
     * 根据字典名称 获取字典
     * @param dictEnName 字典英文名称
     * @param parentId
     * @return
     */
    Dictionary getDictByEnName(String dictEnName,Long parentId);

    /**
     * 根据字典编码 获取字典
     * @param code 字典编码
     * @param parentId 父id
     * @return
     */
    Dictionary getDictByCode(String code,Long parentId);
    
    
    Dictionary getDictInfoByNameAndCode(String name, String code);

    /**
     * 根据id删除字典信息
     * @param dictId 字典id
     * @return 删除结果
     */
    CommonResult<Object> removeSystemDictionaryById(String dictId);

    /**
     * 获取字典名称
     * @param dictId 字典id
     * @return
     */
    String getDictNameById(Long dictId);

    /**
     * 根据父id获取字典明细 不分页
     * @param parentId
     * @param parentCode
     * @param name
     * @return
     */
    List<Dictionary> getDictionaryListByParentId(String parentId, String parentCode, String name);

    /**
     * 根据字典id启用/停用
     * @param id 字典id
     * @param status  状态
     * @return  结果
     */
    CommonResult<Object> enableOrUnable(String id, String status);

    int saveDictionaryFrom(DictionaryFrom dictionaryFrom);

}
