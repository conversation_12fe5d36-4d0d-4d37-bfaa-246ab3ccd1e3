package com.haoys.user.mapper;

import com.haoys.user.model.ProjectPatientPlan;
import com.haoys.user.model.ProjectPatientPlanExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectPatientPlanMapper {
    long countByExample(ProjectPatientPlanExample example);

    int deleteByExample(ProjectPatientPlanExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectPatientPlan record);

    int insertSelective(ProjectPatientPlan record);

    List<ProjectPatientPlan> selectByExample(ProjectPatientPlanExample example);

    ProjectPatientPlan selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectPatientPlan record, @Param("example") ProjectPatientPlanExample example);

    int updateByExample(@Param("record") ProjectPatientPlan record, @Param("example") ProjectPatientPlanExample example);

    int updateByPrimaryKeySelective(ProjectPatientPlan record);

    int updateByPrimaryKey(ProjectPatientPlan record);
}