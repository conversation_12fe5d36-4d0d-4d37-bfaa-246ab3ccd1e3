package com.haoys.user.domain.vo.ocr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class ReportStructuralObject {
    @JsonProperty("Angle")
    private Integer angle;
    
    @JsonProperty("StructuralItems")
    private List<StructuralItem> structuralItems;
    
    @JsonProperty("RequestId")
    private String requestId;
    
    // Getters and Setters
    // 建议使用 Lombok @Data 自动生成
}
@Data
class StructuralItem {
    @JsonProperty("Name")
    private String name;
    
    @JsonProperty("Value")
    private String value;
    
    @JsonProperty("Confidence")
    private Integer confidence;
    
    @JsonProperty("ItemCoord")
    private ItemCoord itemCoord;
    
    @JsonProperty("Row")
    private Integer row;
    
    // Getters and Setters
}
@Data
class ItemCoord {
    @JsonProperty("X")
    private Integer x;
    
    @JsonProperty("Y")
    private Integer y;
    
    @JsonProperty("Width")
    private Integer width;
    
    @JsonProperty("Height")
    private Integer height;
    
    // Get<PERSON> and Setters
}
