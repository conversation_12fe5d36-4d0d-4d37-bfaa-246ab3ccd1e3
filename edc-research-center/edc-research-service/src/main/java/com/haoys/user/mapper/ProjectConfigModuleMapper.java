package com.haoys.user.mapper;

import com.haoys.user.model.ProjectConfigModule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Map;

/**
 * 项目配置分组表Mapper接口
 * 继承BaseQueryMapper，提供完整的CRUD操作、批量操作、复杂查询等功能
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 * @date 2025-07-11 00:31:50
 */
@Mapper
@Repository
public interface ProjectConfigModuleMapper {

    // ========== 基础CRUD操作 ==========

    /**
     * 插入记录
     * @param record 实体对象
     * @return 影响行数
     */
    int insert(ProjectConfigModule record);

    /**
     * 选择性插入记录
     * @param record 实体对象
     * @return 影响行数
     */
    int insertSelective(ProjectConfigModule record);

    /**
     * 根据主键删除
     * @param id 主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 根据主键更新
     * @param record 实体对象
     * @return 影响行数
     */
    int updateByPrimaryKey(ProjectConfigModule record);

    /**
     * 根据主键选择性更新
     * @param record 实体对象
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(ProjectConfigModule record);

    /**
     * 根据主键查询
     * @param id 主键
     * @return 实体对象
     */
    ProjectConfigModule selectByPrimaryKey(Long id);

    // ========== 批量操作 ==========

    /**
     * 批量插入
     * @param records 实体对象列表
     * @return 影响行数
     */
    int batchInsert(@Param("records") List<ProjectConfigModule> records);

    /**
     * 批量删除
     * @param ids 主键列表
     * @return 影响行数
     */
    int batchDeleteByIds(@Param("ids") List<Long> ids);

    /**
     * 批量更新
     * @param records 实体对象列表
     * @return 影响行数
     */
    int batchUpdate(@Param("records") List<ProjectConfigModule> records);

    // ========== 复杂查询操作 ==========

    /**
     * 根据条件查询列表
     * @param params 查询参数
     * @return 实体对象列表
     */
    List<ProjectConfigModule> selectByCondition(@Param("params") Map<String, Object> params);

    /**
     * 根据条件查询单个对象
     * @param params 查询参数
     * @return 实体对象
     */
    ProjectConfigModule selectOneByCondition(@Param("params") Map<String, Object> params);

    /**
     * 根据条件统计数量
     * @param params 查询参数
     * @return 记录数量
     */
    long countByCondition(@Param("params") Map<String, Object> params);

    /**
     * 多条件AND查询
     * @param conditions 条件映射
     * @return 实体对象列表
     */
    List<ProjectConfigModule> selectByMultipleConditions(@Param("conditions") Map<String, Object> conditions);

    /**
     * 范围查询
     * @param field 字段名
     * @param startValue 起始值
     * @param endValue 结束值
     * @return 实体对象列表
     */
    List<ProjectConfigModule> selectByRange(@Param("field") String field, @Param("startValue") Object startValue, @Param("endValue") Object endValue);

    /**
     * 模糊查询
     * @param field 字段名
     * @param keyword 关键词
     * @return 实体对象列表
     */
    List<ProjectConfigModule> selectByLike(@Param("field") String field, @Param("keyword") String keyword);

    /**
     * IN查询
     * @param field 字段名
     * @param values 值列表
     * @return 实体对象列表
     */
    List<ProjectConfigModule> selectByIn(@Param("field") String field, @Param("values") List<Object> values);

    // ========== 基础统计查询 ==========

    /**
     * 根据条件统计总数
     * @param params 查询参数
     * @return 总数
     */
    long countTotal(@Param("params") Map<String, Object> params);

    /**
     * 分组统计
     * @param groupColumn 分组列名
     * @param params 查询参数
     * @return 分组统计结果
     */
    List<Map<String, Object>> groupByColumn(@Param("groupColumn") String groupColumn, @Param("params") Map<String, Object> params);

    // ========== 自定义查询方法 ==========
    // 可以在这里添加业务特定的查询方法
    // 例如：根据业务字段查询、状态查询等
    // 注意：复杂的联表查询建议在Service层处理

}
