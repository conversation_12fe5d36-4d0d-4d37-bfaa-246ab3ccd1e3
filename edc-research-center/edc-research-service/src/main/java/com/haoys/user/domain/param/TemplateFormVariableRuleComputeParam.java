package com.haoys.user.domain.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 表单变量规则计算
 */
@Data
public class TemplateFormVariableRuleComputeParam {

    @ApiModelProperty(value = "公式规则")
    private String ruleDetails;

    @ApiModelProperty(value = "参与计算变量的数据")
    private List<RuleComputeParam> variableData;

    @ApiModelProperty(value = "计算变量的精度")
    private String targetVariableConfig = "0";
}
