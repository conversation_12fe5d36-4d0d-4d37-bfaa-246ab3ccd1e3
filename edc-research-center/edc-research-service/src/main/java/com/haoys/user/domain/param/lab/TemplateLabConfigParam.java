package com.haoys.user.domain.param.lab;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class TemplateLabConfigParam {
    
    @ApiModelProperty(value = "主键")
    private Long id;
    
    @NotNull(message = "项目id不能为空")
    @ApiModelProperty(value = "项目id")
    private Long projectId;
    
    @ApiModelProperty(value = "方案id")
    private Long planId;
    
    @ApiModelProperty(value = "访视id")
    private Long visitId;
    
    @NotNull(message = "表单id不能为空")
    @ApiModelProperty(value = "表单id")
    private Long formId;
    
    @ApiModelProperty(value = "字段组id")
    private Long groupId;
    
    @NotNull(message = "变量id不能为空")
    @ApiModelProperty(value = "变量id")
    private Long formDetailId;
    
    @ApiModelProperty(value = "表格变量id")
    private Long formTableId;
    
    @NotEmpty(message = "字段名称不能为空")
    @ApiModelProperty(value = "字段名称")
    private String variableName;
    
    @NotEmpty(message = "指定范围不能为空")
    @ApiModelProperty(value = "指定范围 visit-访视 form-表单")
    private String scope;
    
    @ApiModelProperty(value = "配置类型 1-定量 2-定性")
    private String labType;
    
    @ApiModelProperty(value = "性别 男、女、通用")
    private String genderValue;
    
    @ApiModelProperty(value = "取值上限")
    private BigDecimal upperLimitValue;
    
    @ApiModelProperty(value = "是否包含")
    private Boolean ifUpperContains = false;
    
    @ApiModelProperty(value = "取值下限")
    private BigDecimal lowerLimitValue;
    
    @ApiModelProperty(value = "是否包含")
    private Boolean ifLowerContains = false;
    
    @ApiModelProperty(value = "字典来源")
    private String dictionaryResourceType;
    
    @ApiModelProperty(value = "引用字典id")
    private Long refDicId;
    
    @ApiModelProperty(value = "正常取值-文本值")
    private String normalText;
    
    @ApiModelProperty(value = "正常取值")
    private String normalValue;
    
    @ApiModelProperty(value = "来源中心")
    private Long labResource;
    
    @ApiModelProperty(value = "是否应用到全部研究中心")
    private Boolean applyTotalOrg;
    
    @ApiModelProperty(value = "数据状态0/1")
    private String status;
    
    @ApiModelProperty(value = "变量参数")
    private String selectedOptions;
}
