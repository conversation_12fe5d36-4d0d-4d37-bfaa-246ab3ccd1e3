<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.TemplateFormDvpRuleMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.TemplateFormDvpRule">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="check_name" jdbcType="VARCHAR" property="checkName" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="plan_id" jdbcType="BIGINT" property="planId" />
    <result column="visit_id" jdbcType="BIGINT" property="visitId" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="form_detail_id" jdbcType="BIGINT" property="formDetailId" />
    <result column="form_table_id" jdbcType="BIGINT" property="formTableId" />
    <result column="variable_name" jdbcType="VARCHAR" property="variableName" />
    <result column="form_relation_id" jdbcType="VARCHAR" property="formRelationId" />
    <result column="custom_table" jdbcType="BIT" property="customTable" />
    <result column="table_row_index" jdbcType="VARCHAR" property="tableRowIndex" />
    <result column="query_type" jdbcType="VARCHAR" property="queryType" />
    <result column="query_method" jdbcType="VARCHAR" property="queryMethod" />
    <result column="function_value" jdbcType="VARCHAR" property="functionValue" />
    <result column="arithmetic_value" jdbcType="VARCHAR" property="arithmeticValue" />
    <result column="condition_value" jdbcType="VARCHAR" property="conditionValue" />
    <result column="target_value" jdbcType="VARCHAR" property="targetValue" />
    <result column="rule_type" jdbcType="VARCHAR" property="ruleType" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="enabled" jdbcType="BIT" property="enabled" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.haoys.user.model.TemplateFormDvpRule">
    <result column="selected_options" jdbcType="LONGVARCHAR" property="selectedOptions" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, code, check_name, project_id, plan_id, visit_id, form_id, group_id, form_detail_id,
    form_table_id, variable_name, form_relation_id, custom_table, table_row_index, query_type,
    query_method, function_value, arithmetic_value, condition_value, target_value, rule_type,
    content, enabled, status, create_time, update_time, create_user_id, update_user_id,
    tenant_id, platform_id
  </sql>
  <sql id="Blob_Column_List">
    selected_options
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.haoys.user.model.TemplateFormDvpRuleExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from template_form_dvp_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.haoys.user.model.TemplateFormDvpRuleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from template_form_dvp_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from template_form_dvp_rule
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from template_form_dvp_rule
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.TemplateFormDvpRuleExample">
    delete from template_form_dvp_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.TemplateFormDvpRule">
    insert into template_form_dvp_rule (id, code, check_name,
      project_id, plan_id, visit_id,
      form_id, group_id, form_detail_id,
      form_table_id, variable_name, form_relation_id,
      custom_table, table_row_index, query_type,
      query_method, function_value, arithmetic_value,
      condition_value, target_value, rule_type,
      content, enabled, status,
      create_time, update_time, create_user_id,
      update_user_id, tenant_id, platform_id,
      selected_options)
    values (#{id,jdbcType=BIGINT}, #{code,jdbcType=VARCHAR}, #{checkName,jdbcType=VARCHAR},
      #{projectId,jdbcType=BIGINT}, #{planId,jdbcType=BIGINT}, #{visitId,jdbcType=BIGINT},
      #{formId,jdbcType=BIGINT}, #{groupId,jdbcType=BIGINT}, #{formDetailId,jdbcType=BIGINT},
      #{formTableId,jdbcType=BIGINT}, #{variableName,jdbcType=VARCHAR}, #{formRelationId,jdbcType=VARCHAR},
      #{customTable,jdbcType=BIT}, #{tableRowIndex,jdbcType=VARCHAR}, #{queryType,jdbcType=VARCHAR},
      #{queryMethod,jdbcType=VARCHAR}, #{functionValue,jdbcType=VARCHAR}, #{arithmeticValue,jdbcType=VARCHAR},
      #{conditionValue,jdbcType=VARCHAR}, #{targetValue,jdbcType=VARCHAR}, #{ruleType,jdbcType=VARCHAR},
      #{content,jdbcType=VARCHAR}, #{enabled,jdbcType=BIT}, #{status,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=VARCHAR},
      #{updateUserId,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR},
      #{selectedOptions,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.TemplateFormDvpRule">
    insert into template_form_dvp_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="checkName != null">
        check_name,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="planId != null">
        plan_id,
      </if>
      <if test="visitId != null">
        visit_id,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="formDetailId != null">
        form_detail_id,
      </if>
      <if test="formTableId != null">
        form_table_id,
      </if>
      <if test="variableName != null">
        variable_name,
      </if>
      <if test="formRelationId != null">
        form_relation_id,
      </if>
      <if test="customTable != null">
        custom_table,
      </if>
      <if test="tableRowIndex != null">
        table_row_index,
      </if>
      <if test="queryType != null">
        query_type,
      </if>
      <if test="queryMethod != null">
        query_method,
      </if>
      <if test="functionValue != null">
        function_value,
      </if>
      <if test="arithmeticValue != null">
        arithmetic_value,
      </if>
      <if test="conditionValue != null">
        condition_value,
      </if>
      <if test="targetValue != null">
        target_value,
      </if>
      <if test="ruleType != null">
        rule_type,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
      <if test="selectedOptions != null">
        selected_options,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="checkName != null">
        #{checkName,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        #{visitId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="formDetailId != null">
        #{formDetailId,jdbcType=BIGINT},
      </if>
      <if test="formTableId != null">
        #{formTableId,jdbcType=BIGINT},
      </if>
      <if test="variableName != null">
        #{variableName,jdbcType=VARCHAR},
      </if>
      <if test="formRelationId != null">
        #{formRelationId,jdbcType=VARCHAR},
      </if>
      <if test="customTable != null">
        #{customTable,jdbcType=BIT},
      </if>
      <if test="tableRowIndex != null">
        #{tableRowIndex,jdbcType=VARCHAR},
      </if>
      <if test="queryType != null">
        #{queryType,jdbcType=VARCHAR},
      </if>
      <if test="queryMethod != null">
        #{queryMethod,jdbcType=VARCHAR},
      </if>
      <if test="functionValue != null">
        #{functionValue,jdbcType=VARCHAR},
      </if>
      <if test="arithmeticValue != null">
        #{arithmeticValue,jdbcType=VARCHAR},
      </if>
      <if test="conditionValue != null">
        #{conditionValue,jdbcType=VARCHAR},
      </if>
      <if test="targetValue != null">
        #{targetValue,jdbcType=VARCHAR},
      </if>
      <if test="ruleType != null">
        #{ruleType,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=BIT},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
      <if test="selectedOptions != null">
        #{selectedOptions,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.TemplateFormDvpRuleExample" resultType="java.lang.Long">
    select count(*) from template_form_dvp_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update template_form_dvp_rule
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.checkName != null">
        check_name = #{record.checkName,jdbcType=VARCHAR},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.planId != null">
        plan_id = #{record.planId,jdbcType=BIGINT},
      </if>
      <if test="record.visitId != null">
        visit_id = #{record.visitId,jdbcType=BIGINT},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=BIGINT},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=BIGINT},
      </if>
      <if test="record.formDetailId != null">
        form_detail_id = #{record.formDetailId,jdbcType=BIGINT},
      </if>
      <if test="record.formTableId != null">
        form_table_id = #{record.formTableId,jdbcType=BIGINT},
      </if>
      <if test="record.variableName != null">
        variable_name = #{record.variableName,jdbcType=VARCHAR},
      </if>
      <if test="record.formRelationId != null">
        form_relation_id = #{record.formRelationId,jdbcType=VARCHAR},
      </if>
      <if test="record.customTable != null">
        custom_table = #{record.customTable,jdbcType=BIT},
      </if>
      <if test="record.tableRowIndex != null">
        table_row_index = #{record.tableRowIndex,jdbcType=VARCHAR},
      </if>
      <if test="record.queryType != null">
        query_type = #{record.queryType,jdbcType=VARCHAR},
      </if>
      <if test="record.queryMethod != null">
        query_method = #{record.queryMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.functionValue != null">
        function_value = #{record.functionValue,jdbcType=VARCHAR},
      </if>
      <if test="record.arithmeticValue != null">
        arithmetic_value = #{record.arithmeticValue,jdbcType=VARCHAR},
      </if>
      <if test="record.conditionValue != null">
        condition_value = #{record.conditionValue,jdbcType=VARCHAR},
      </if>
      <if test="record.targetValue != null">
        target_value = #{record.targetValue,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleType != null">
        rule_type = #{record.ruleType,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.enabled != null">
        enabled = #{record.enabled,jdbcType=BIT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
      <if test="record.selectedOptions != null">
        selected_options = #{record.selectedOptions,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update template_form_dvp_rule
    set id = #{record.id,jdbcType=BIGINT},
      code = #{record.code,jdbcType=VARCHAR},
      check_name = #{record.checkName,jdbcType=VARCHAR},
      project_id = #{record.projectId,jdbcType=BIGINT},
      plan_id = #{record.planId,jdbcType=BIGINT},
      visit_id = #{record.visitId,jdbcType=BIGINT},
      form_id = #{record.formId,jdbcType=BIGINT},
      group_id = #{record.groupId,jdbcType=BIGINT},
      form_detail_id = #{record.formDetailId,jdbcType=BIGINT},
      form_table_id = #{record.formTableId,jdbcType=BIGINT},
      variable_name = #{record.variableName,jdbcType=VARCHAR},
      form_relation_id = #{record.formRelationId,jdbcType=VARCHAR},
      custom_table = #{record.customTable,jdbcType=BIT},
      table_row_index = #{record.tableRowIndex,jdbcType=VARCHAR},
      query_type = #{record.queryType,jdbcType=VARCHAR},
      query_method = #{record.queryMethod,jdbcType=VARCHAR},
      function_value = #{record.functionValue,jdbcType=VARCHAR},
      arithmetic_value = #{record.arithmeticValue,jdbcType=VARCHAR},
      condition_value = #{record.conditionValue,jdbcType=VARCHAR},
      target_value = #{record.targetValue,jdbcType=VARCHAR},
      rule_type = #{record.ruleType,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=VARCHAR},
      enabled = #{record.enabled,jdbcType=BIT},
      status = #{record.status,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR},
      selected_options = #{record.selectedOptions,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update template_form_dvp_rule
    set id = #{record.id,jdbcType=BIGINT},
      code = #{record.code,jdbcType=VARCHAR},
      check_name = #{record.checkName,jdbcType=VARCHAR},
      project_id = #{record.projectId,jdbcType=BIGINT},
      plan_id = #{record.planId,jdbcType=BIGINT},
      visit_id = #{record.visitId,jdbcType=BIGINT},
      form_id = #{record.formId,jdbcType=BIGINT},
      group_id = #{record.groupId,jdbcType=BIGINT},
      form_detail_id = #{record.formDetailId,jdbcType=BIGINT},
      form_table_id = #{record.formTableId,jdbcType=BIGINT},
      variable_name = #{record.variableName,jdbcType=VARCHAR},
      form_relation_id = #{record.formRelationId,jdbcType=VARCHAR},
      custom_table = #{record.customTable,jdbcType=BIT},
      table_row_index = #{record.tableRowIndex,jdbcType=VARCHAR},
      query_type = #{record.queryType,jdbcType=VARCHAR},
      query_method = #{record.queryMethod,jdbcType=VARCHAR},
      function_value = #{record.functionValue,jdbcType=VARCHAR},
      arithmetic_value = #{record.arithmeticValue,jdbcType=VARCHAR},
      condition_value = #{record.conditionValue,jdbcType=VARCHAR},
      target_value = #{record.targetValue,jdbcType=VARCHAR},
      rule_type = #{record.ruleType,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=VARCHAR},
      enabled = #{record.enabled,jdbcType=BIT},
      status = #{record.status,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.TemplateFormDvpRule">
    update template_form_dvp_rule
    <set>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="checkName != null">
        check_name = #{checkName,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        plan_id = #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        visit_id = #{visitId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="formDetailId != null">
        form_detail_id = #{formDetailId,jdbcType=BIGINT},
      </if>
      <if test="formTableId != null">
        form_table_id = #{formTableId,jdbcType=BIGINT},
      </if>
      <if test="variableName != null">
        variable_name = #{variableName,jdbcType=VARCHAR},
      </if>
      <if test="formRelationId != null">
        form_relation_id = #{formRelationId,jdbcType=VARCHAR},
      </if>
      <if test="customTable != null">
        custom_table = #{customTable,jdbcType=BIT},
      </if>
      <if test="tableRowIndex != null">
        table_row_index = #{tableRowIndex,jdbcType=VARCHAR},
      </if>
      <if test="queryType != null">
        query_type = #{queryType,jdbcType=VARCHAR},
      </if>
      <if test="queryMethod != null">
        query_method = #{queryMethod,jdbcType=VARCHAR},
      </if>
      <if test="functionValue != null">
        function_value = #{functionValue,jdbcType=VARCHAR},
      </if>
      <if test="arithmeticValue != null">
        arithmetic_value = #{arithmeticValue,jdbcType=VARCHAR},
      </if>
      <if test="conditionValue != null">
        condition_value = #{conditionValue,jdbcType=VARCHAR},
      </if>
      <if test="targetValue != null">
        target_value = #{targetValue,jdbcType=VARCHAR},
      </if>
      <if test="ruleType != null">
        rule_type = #{ruleType,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null">
        enabled = #{enabled,jdbcType=BIT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
      <if test="selectedOptions != null">
        selected_options = #{selectedOptions,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.haoys.user.model.TemplateFormDvpRule">
    update template_form_dvp_rule
    set code = #{code,jdbcType=VARCHAR},
      check_name = #{checkName,jdbcType=VARCHAR},
      project_id = #{projectId,jdbcType=BIGINT},
      plan_id = #{planId,jdbcType=BIGINT},
      visit_id = #{visitId,jdbcType=BIGINT},
      form_id = #{formId,jdbcType=BIGINT},
      group_id = #{groupId,jdbcType=BIGINT},
      form_detail_id = #{formDetailId,jdbcType=BIGINT},
      form_table_id = #{formTableId,jdbcType=BIGINT},
      variable_name = #{variableName,jdbcType=VARCHAR},
      form_relation_id = #{formRelationId,jdbcType=VARCHAR},
      custom_table = #{customTable,jdbcType=BIT},
      table_row_index = #{tableRowIndex,jdbcType=VARCHAR},
      query_type = #{queryType,jdbcType=VARCHAR},
      query_method = #{queryMethod,jdbcType=VARCHAR},
      function_value = #{functionValue,jdbcType=VARCHAR},
      arithmetic_value = #{arithmeticValue,jdbcType=VARCHAR},
      condition_value = #{conditionValue,jdbcType=VARCHAR},
      target_value = #{targetValue,jdbcType=VARCHAR},
      rule_type = #{ruleType,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      enabled = #{enabled,jdbcType=BIT},
      status = #{status,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR},
      selected_options = #{selectedOptions,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.TemplateFormDvpRule">
    update template_form_dvp_rule
    set code = #{code,jdbcType=VARCHAR},
      check_name = #{checkName,jdbcType=VARCHAR},
      project_id = #{projectId,jdbcType=BIGINT},
      plan_id = #{planId,jdbcType=BIGINT},
      visit_id = #{visitId,jdbcType=BIGINT},
      form_id = #{formId,jdbcType=BIGINT},
      group_id = #{groupId,jdbcType=BIGINT},
      form_detail_id = #{formDetailId,jdbcType=BIGINT},
      form_table_id = #{formTableId,jdbcType=BIGINT},
      variable_name = #{variableName,jdbcType=VARCHAR},
      form_relation_id = #{formRelationId,jdbcType=VARCHAR},
      custom_table = #{customTable,jdbcType=BIT},
      table_row_index = #{tableRowIndex,jdbcType=VARCHAR},
      query_type = #{queryType,jdbcType=VARCHAR},
      query_method = #{queryMethod,jdbcType=VARCHAR},
      function_value = #{functionValue,jdbcType=VARCHAR},
      arithmetic_value = #{arithmeticValue,jdbcType=VARCHAR},
      condition_value = #{conditionValue,jdbcType=VARCHAR},
      target_value = #{targetValue,jdbcType=VARCHAR},
      rule_type = #{ruleType,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      enabled = #{enabled,jdbcType=BIT},
      status = #{status,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="getProjectTemplateDVPRuleListForPage" resultType="com.haoys.user.domain.vo.ecrf.TemplateFormDvpRuleVo">
    SELECT
        template_form_dvp_rule.id,template_form_dvp_rule.query_type queryType,template_form_dvp_rule.query_method queryMethod,
        template_form_dvp_rule.condition_value conditionValue,template_form_dvp_rule.target_value targetValue,template_form_dvp_rule.enabled,
        project_visit_config.visit_name visitName,template_form_config.form_name formName,template_form_detail.label formDetailName,
        template_form_dvp_rule.content,template_form_dvp_rule.create_user_id createUserId,template_form_dvp_rule.create_time createTime
    FROM template_form_dvp_rule INNER JOIN project_visit_config ON project_visit_config.id = template_form_dvp_rule.visit_id
    INNER JOIN template_form_config ON template_form_config.id = template_form_dvp_rule.form_id
    INNER JOIN template_form_detail ON template_form_detail.id = template_form_dvp_rule.form_detail_id
    WHERE template_form_dvp_rule.project_id = #{projectId}
    <if test="visitId != null and visitId != ''">
      AND template_form_dvp_rule.visit_id = #{visitId}
    </if>
    <if test="formId != null and formId != ''">
      AND template_form_dvp_rule.form_id = #{formId}
    </if>
    <if test="formDetailId != null and formDetailId != ''">
      AND template_form_dvp_rule.form_detail_id = #{formDetailId}
    </if>
  </select>


</mapper>
