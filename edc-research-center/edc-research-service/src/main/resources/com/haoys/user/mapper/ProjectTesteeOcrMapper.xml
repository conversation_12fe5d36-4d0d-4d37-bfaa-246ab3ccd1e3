<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectTesteeOcrMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectTesteeOcr">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="file_id" jdbcType="BIGINT" property="fileId" />
    <result column="ocr_key" jdbcType="VARCHAR" property="ocrKey" />
    <result column="words_result" jdbcType="VARCHAR" property="wordsResult" />
    <result column="form_words_result" jdbcType="VARCHAR" property="formWordsResult" />
    <result column="general_accurate_result" jdbcType="VARCHAR" property="generalAccurateResult" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="target_form_id" jdbcType="BIGINT" property="targetFormId" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, file_id, ocr_key, words_result, form_words_result, general_accurate_result, 
    status, target_form_id, create_user_id, create_time, update_user_id, update_time, 
    tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectTesteeOcrExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_testee_ocr
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_testee_ocr
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_testee_ocr
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectTesteeOcrExample">
    delete from project_testee_ocr
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectTesteeOcr">
    insert into project_testee_ocr (id, project_id, file_id, 
      ocr_key, words_result, form_words_result, 
      general_accurate_result, status, target_form_id, 
      create_user_id, create_time, update_user_id, 
      update_time, tenant_id, platform_id
      )
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{fileId,jdbcType=BIGINT}, 
      #{ocrKey,jdbcType=VARCHAR}, #{wordsResult,jdbcType=VARCHAR}, #{formWordsResult,jdbcType=VARCHAR}, 
      #{generalAccurateResult,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{targetFormId,jdbcType=BIGINT}, 
      #{createUserId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateUserId,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectTesteeOcr">
    insert into project_testee_ocr
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="fileId != null">
        file_id,
      </if>
      <if test="ocrKey != null">
        ocr_key,
      </if>
      <if test="wordsResult != null">
        words_result,
      </if>
      <if test="formWordsResult != null">
        form_words_result,
      </if>
      <if test="generalAccurateResult != null">
        general_accurate_result,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="targetFormId != null">
        target_form_id,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="fileId != null">
        #{fileId,jdbcType=BIGINT},
      </if>
      <if test="ocrKey != null">
        #{ocrKey,jdbcType=VARCHAR},
      </if>
      <if test="wordsResult != null">
        #{wordsResult,jdbcType=VARCHAR},
      </if>
      <if test="formWordsResult != null">
        #{formWordsResult,jdbcType=VARCHAR},
      </if>
      <if test="generalAccurateResult != null">
        #{generalAccurateResult,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="targetFormId != null">
        #{targetFormId,jdbcType=BIGINT},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectTesteeOcrExample" resultType="java.lang.Long">
    select count(*) from project_testee_ocr
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_testee_ocr
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.fileId != null">
        file_id = #{record.fileId,jdbcType=BIGINT},
      </if>
      <if test="record.ocrKey != null">
        ocr_key = #{record.ocrKey,jdbcType=VARCHAR},
      </if>
      <if test="record.wordsResult != null">
        words_result = #{record.wordsResult,jdbcType=VARCHAR},
      </if>
      <if test="record.formWordsResult != null">
        form_words_result = #{record.formWordsResult,jdbcType=VARCHAR},
      </if>
      <if test="record.generalAccurateResult != null">
        general_accurate_result = #{record.generalAccurateResult,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.targetFormId != null">
        target_form_id = #{record.targetFormId,jdbcType=BIGINT},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_testee_ocr
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      file_id = #{record.fileId,jdbcType=BIGINT},
      ocr_key = #{record.ocrKey,jdbcType=VARCHAR},
      words_result = #{record.wordsResult,jdbcType=VARCHAR},
      form_words_result = #{record.formWordsResult,jdbcType=VARCHAR},
      general_accurate_result = #{record.generalAccurateResult,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      target_form_id = #{record.targetFormId,jdbcType=BIGINT},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectTesteeOcr">
    update project_testee_ocr
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="fileId != null">
        file_id = #{fileId,jdbcType=BIGINT},
      </if>
      <if test="ocrKey != null">
        ocr_key = #{ocrKey,jdbcType=VARCHAR},
      </if>
      <if test="wordsResult != null">
        words_result = #{wordsResult,jdbcType=VARCHAR},
      </if>
      <if test="formWordsResult != null">
        form_words_result = #{formWordsResult,jdbcType=VARCHAR},
      </if>
      <if test="generalAccurateResult != null">
        general_accurate_result = #{generalAccurateResult,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="targetFormId != null">
        target_form_id = #{targetFormId,jdbcType=BIGINT},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectTesteeOcr">
    update project_testee_ocr
    set project_id = #{projectId,jdbcType=BIGINT},
      file_id = #{fileId,jdbcType=BIGINT},
      ocr_key = #{ocrKey,jdbcType=VARCHAR},
      words_result = #{wordsResult,jdbcType=VARCHAR},
      form_words_result = #{formWordsResult,jdbcType=VARCHAR},
      general_accurate_result = #{generalAccurateResult,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      target_form_id = #{targetFormId,jdbcType=BIGINT},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="selectByFileId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from project_testee_ocr where file_id = #{fileId} LIMIT 1
  </select>

  <select id="getProjectTesteeNextImageByFileId" resultType="com.haoys.user.model.ProjectTesteeOcr">
    select <include refid="Base_Column_List" />  from project_testee_ocr where pre_page_no = #{fileId} LIMIT 1
  </select>

</mapper>