<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectOrgInfoMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectOrgInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="ident_code" jdbcType="VARCHAR" property="identCode" />
    <result column="officer" jdbcType="VARCHAR" property="officer" />
    <result column="expands" jdbcType="VARCHAR" property="expands" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, org_id, code, ident_code, officer, expands, create_user_id, create_time,
    update_user_id, update_time, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectOrgInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_org_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from project_org_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_org_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectOrgInfoExample">
    delete from project_org_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectOrgInfo">
    insert into project_org_info (id, project_id, org_id,
      code, ident_code, officer,
      expands, create_user_id, create_time,
      update_user_id, update_time, tenant_id,
      platform_id)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{orgId,jdbcType=BIGINT},
      #{code,jdbcType=VARCHAR}, #{identCode,jdbcType=VARCHAR}, #{officer,jdbcType=VARCHAR},
      #{expands,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updateUserId,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{tenantId,jdbcType=VARCHAR},
      #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectOrgInfo">
    insert into project_org_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="identCode != null">
        ident_code,
      </if>
      <if test="officer != null">
        officer,
      </if>
      <if test="expands != null">
        expands,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="identCode != null">
        #{identCode,jdbcType=VARCHAR},
      </if>
      <if test="officer != null">
        #{officer,jdbcType=VARCHAR},
      </if>
      <if test="expands != null">
        #{expands,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectOrgInfoExample" resultType="java.lang.Long">
    select count(*) from project_org_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_org_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.identCode != null">
        ident_code = #{record.identCode,jdbcType=VARCHAR},
      </if>
      <if test="record.officer != null">
        officer = #{record.officer,jdbcType=VARCHAR},
      </if>
      <if test="record.expands != null">
        expands = #{record.expands,jdbcType=VARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_org_info
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      org_id = #{record.orgId,jdbcType=BIGINT},
      code = #{record.code,jdbcType=VARCHAR},
      ident_code = #{record.identCode,jdbcType=VARCHAR},
      officer = #{record.officer,jdbcType=VARCHAR},
      expands = #{record.expands,jdbcType=VARCHAR},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectOrgInfo">
    update project_org_info
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="identCode != null">
        ident_code = #{identCode,jdbcType=VARCHAR},
      </if>
      <if test="officer != null">
        officer = #{officer,jdbcType=VARCHAR},
      </if>
      <if test="expands != null">
        expands = #{expands,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectOrgInfo">
    update project_org_info
    set project_id = #{projectId,jdbcType=BIGINT},
      org_id = #{orgId,jdbcType=BIGINT},
      code = #{code,jdbcType=VARCHAR},
      ident_code = #{identCode,jdbcType=VARCHAR},
      officer = #{officer,jdbcType=VARCHAR},
      expands = #{expands,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <!--项目中心分页列表-->
  <select id="getProjectOrgListForPage" resultType="com.haoys.user.domain.vo.project.ProjectOrgVo">
    SELECT
      project_org_info.id,
      system_org_info.org_name orgName,
      system_org_info.org_id orgId,
      system_org_info.org_code orgCode,
      project_org_info.code,
      project_org_info.ident_code identCode,
      project_org_info.officer,
      system_org_info.province_code provinceCode,
      system_org_info.city_code cityCode,
      system_org_info.county_code countyCode,
      project_org_info.expands,
      project_org_info.create_time createTime
    FROM
        project_org_info
    INNER JOIN system_org_info ON system_org_info.org_id = project_org_info.org_id
    WHERE project_org_info.project_id = #{projectId}
    <if test="name != null and name != ''">
      AND (system_org_info.org_name LIKE CONCAT('%',#{name},'%') OR project_org_info.code LIKE CONCAT('%',#{name},'%') )
    </if>
    <if test="officer != null and officer != ''">
      AND project_org_info.officer LIKE CONCAT('%',#{officer},'%')
    </if>
    <if test="orgIds != null and orgIds != ''">
      AND system_org_info.org_id IN (${orgIds})
    </if>
  </select>

  <!--通过项目id和中心id查询项目研究中心-->
  <select id="getProjectOrgListByProjectIdAndOrgId" resultMap="BaseResultMap">
    select * from project_org_info where project_id = #{projectId} and org_id = #{orgId}
  </select>

  <!--通过项目id和项目编号查询项目研究中心-->
  <select id="getProjectOrgInfoByProjectIdAndCode" resultType="com.haoys.user.model.ProjectOrgInfo">
    select * from project_org_info where project_id = #{projectId} and code = #{code} limit 1
  </select>

  <!--查询项目中最大的识别码-->
  <select id="getMaxIdentCodeCode" resultType="java.lang.String">
    select max(ident_code) from project_org_info
  </select>

  <!--删除项目研究中心-->
  <delete id="deleteProjectOrgByOrgId">
    delete from project_org_info where project_id = #{projectId} and id = #{projectOrgId}
  </delete>

  <!--通过项目识别码查询项目研究中心-->
  <select id="selectProjectByIdentCode" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    project_org_info.id, project_org_info.project_id, project_org_info.org_id, project_org_info.code, project_org_info.ident_code,
    project_org_info.officer, project_org_info.expands, project_org_info.create_user_id, project_org_info.create_time,
    project_org_info.update_user_id, project_org_info.update_time, project_org_info.tenant_id, project_org_info.platform_id
     from project_org_info
    inner join project on project_org_info.project_id = project.id
    where ident_code = #{identCode,jdbcType=VARCHAR} and project.status = '0'
  </select>

  <select id="getDefaultProjectOrgId" resultType="java.lang.Long">
    select org_id from system_org_info where is_auth = 1 and tenant_id = #{tenantId} and platform_id = #{platformId}
  </select>

  <select id="getProjectOrganizationInfo" resultMap="BaseResultMap">
    select * from project_org_info where project_id = #{projectId} limit 1
  </select>
</mapper>
