<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectConfigBaseMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectConfigBase">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="module_id" jdbcType="BIGINT" property="moduleId" />
    <result column="module_name" jdbcType="VARCHAR" property="moduleName" />
    <result column="config_name" jdbcType="VARCHAR" property="configName" />
    <result column="config_code" jdbcType="VARCHAR" property="configCode" />
    <result column="require" jdbcType="BIT" property="require" />
    <result column="display_type" jdbcType="VARCHAR" property="displayType" />
    <result column="options" jdbcType="VARCHAR" property="options" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="default_value" jdbcType="VARCHAR" property="defaultValue" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, module_id, module_name, config_name, config_code, require, display_type, options, 
    create_user_id, default_value, create_time, update_time, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectConfigBaseExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_config_base
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_config_base
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_config_base
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectConfigBaseExample">
    delete from project_config_base
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectConfigBase">
    insert into project_config_base (id, module_id, module_name, 
      config_name, config_code, require, 
      display_type, options, create_user_id, 
      default_value, create_time, update_time, 
      tenant_id, platform_id)
    values (#{id,jdbcType=BIGINT}, #{moduleId,jdbcType=BIGINT}, #{moduleName,jdbcType=VARCHAR}, 
      #{configName,jdbcType=VARCHAR}, #{configCode,jdbcType=VARCHAR}, #{require,jdbcType=BIT}, 
      #{displayType,jdbcType=VARCHAR}, #{options,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, 
      #{defaultValue,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectConfigBase">
    insert into project_config_base
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="moduleId != null">
        module_id,
      </if>
      <if test="moduleName != null">
        module_name,
      </if>
      <if test="configName != null">
        config_name,
      </if>
      <if test="configCode != null">
        config_code,
      </if>
      <if test="require != null">
        require,
      </if>
      <if test="displayType != null">
        display_type,
      </if>
      <if test="options != null">
        options,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="defaultValue != null">
        default_value,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="moduleId != null">
        #{moduleId,jdbcType=BIGINT},
      </if>
      <if test="moduleName != null">
        #{moduleName,jdbcType=VARCHAR},
      </if>
      <if test="configName != null">
        #{configName,jdbcType=VARCHAR},
      </if>
      <if test="configCode != null">
        #{configCode,jdbcType=VARCHAR},
      </if>
      <if test="require != null">
        #{require,jdbcType=BIT},
      </if>
      <if test="displayType != null">
        #{displayType,jdbcType=VARCHAR},
      </if>
      <if test="options != null">
        #{options,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="defaultValue != null">
        #{defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectConfigBaseExample" resultType="java.lang.Long">
    select count(*) from project_config_base
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_config_base
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.moduleId != null">
        module_id = #{record.moduleId,jdbcType=BIGINT},
      </if>
      <if test="record.moduleName != null">
        module_name = #{record.moduleName,jdbcType=VARCHAR},
      </if>
      <if test="record.configName != null">
        config_name = #{record.configName,jdbcType=VARCHAR},
      </if>
      <if test="record.configCode != null">
        config_code = #{record.configCode,jdbcType=VARCHAR},
      </if>
      <if test="record.require != null">
        require = #{record.require,jdbcType=BIT},
      </if>
      <if test="record.displayType != null">
        display_type = #{record.displayType,jdbcType=VARCHAR},
      </if>
      <if test="record.options != null">
        options = #{record.options,jdbcType=VARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.defaultValue != null">
        default_value = #{record.defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_config_base
    set id = #{record.id,jdbcType=BIGINT},
      module_id = #{record.moduleId,jdbcType=BIGINT},
      module_name = #{record.moduleName,jdbcType=VARCHAR},
      config_name = #{record.configName,jdbcType=VARCHAR},
      config_code = #{record.configCode,jdbcType=VARCHAR},
      require = #{record.require,jdbcType=BIT},
      display_type = #{record.displayType,jdbcType=VARCHAR},
      options = #{record.options,jdbcType=VARCHAR},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      default_value = #{record.defaultValue,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectConfigBase">
    update project_config_base
    <set>
      <if test="moduleId != null">
        module_id = #{moduleId,jdbcType=BIGINT},
      </if>
      <if test="moduleName != null">
        module_name = #{moduleName,jdbcType=VARCHAR},
      </if>
      <if test="configName != null">
        config_name = #{configName,jdbcType=VARCHAR},
      </if>
      <if test="configCode != null">
        config_code = #{configCode,jdbcType=VARCHAR},
      </if>
      <if test="require != null">
        require = #{require,jdbcType=BIT},
      </if>
      <if test="displayType != null">
        display_type = #{displayType,jdbcType=VARCHAR},
      </if>
      <if test="options != null">
        options = #{options,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="defaultValue != null">
        default_value = #{defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectConfigBase">
    update project_config_base
    set module_id = #{moduleId,jdbcType=BIGINT},
      module_name = #{moduleName,jdbcType=VARCHAR},
      config_name = #{configName,jdbcType=VARCHAR},
      config_code = #{configCode,jdbcType=VARCHAR},
      require = #{require,jdbcType=BIT},
      display_type = #{displayType,jdbcType=VARCHAR},
      options = #{options,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      default_value = #{defaultValue,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>