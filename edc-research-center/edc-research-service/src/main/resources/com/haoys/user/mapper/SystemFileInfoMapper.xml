<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.SystemFileInfoMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.SystemFileInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="original_name" jdbcType="VARCHAR" property="originalName" />
    <result column="upload_path" jdbcType="VARCHAR" property="uploadPath" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="resource_type" jdbcType="VARCHAR" property="resourceType" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, file_name, original_name, upload_path, file_url, resource_type, status, create_user_id, 
    create_time, update_user_id, update_time, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.SystemFileInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from system_file_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from system_file_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from system_file_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.SystemFileInfoExample">
    delete from system_file_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.SystemFileInfo">
    insert into system_file_info (id, file_name, original_name, 
      upload_path, file_url, resource_type, 
      status, create_user_id, create_time, 
      update_user_id, update_time, tenant_id, 
      platform_id)
    values (#{id,jdbcType=BIGINT}, #{fileName,jdbcType=VARCHAR}, #{originalName,jdbcType=VARCHAR}, 
      #{uploadPath,jdbcType=VARCHAR}, #{fileUrl,jdbcType=VARCHAR}, #{resourceType,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateUserId,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{tenantId,jdbcType=VARCHAR}, 
      #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.SystemFileInfo">
    insert into system_file_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="originalName != null">
        original_name,
      </if>
      <if test="uploadPath != null">
        upload_path,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="resourceType != null">
        resource_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="originalName != null">
        #{originalName,jdbcType=VARCHAR},
      </if>
      <if test="uploadPath != null">
        #{uploadPath,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.SystemFileInfoExample" resultType="java.lang.Long">
    select count(*) from system_file_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update system_file_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.fileName != null">
        file_name = #{record.fileName,jdbcType=VARCHAR},
      </if>
      <if test="record.originalName != null">
        original_name = #{record.originalName,jdbcType=VARCHAR},
      </if>
      <if test="record.uploadPath != null">
        upload_path = #{record.uploadPath,jdbcType=VARCHAR},
      </if>
      <if test="record.fileUrl != null">
        file_url = #{record.fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceType != null">
        resource_type = #{record.resourceType,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update system_file_info
    set id = #{record.id,jdbcType=BIGINT},
      file_name = #{record.fileName,jdbcType=VARCHAR},
      original_name = #{record.originalName,jdbcType=VARCHAR},
      upload_path = #{record.uploadPath,jdbcType=VARCHAR},
      file_url = #{record.fileUrl,jdbcType=VARCHAR},
      resource_type = #{record.resourceType,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.SystemFileInfo">
    update system_file_info
    <set>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="originalName != null">
        original_name = #{originalName,jdbcType=VARCHAR},
      </if>
      <if test="uploadPath != null">
        upload_path = #{uploadPath,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        resource_type = #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.SystemFileInfo">
    update system_file_info
    set file_name = #{fileName,jdbcType=VARCHAR},
      original_name = #{originalName,jdbcType=VARCHAR},
      upload_path = #{uploadPath,jdbcType=VARCHAR},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      resource_type = #{resourceType,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>