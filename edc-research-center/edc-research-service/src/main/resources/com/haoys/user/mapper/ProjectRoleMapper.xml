<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectRoleMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectRole">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="enname" jdbcType="VARCHAR" property="enname" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="global_default" jdbcType="BIT" property="globalDefault" />
    <result column="system_default" jdbcType="BIT" property="systemDefault" />
    <result column="project_template" jdbcType="BIT" property="projectTemplate" />
    <result column="org_template" jdbcType="BIT" property="orgTemplate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, name, enname, status, sort, global_default, system_default, project_template, 
    org_template, create_user, create_time, update_user, update_time, description, tenant_id, 
    platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectRoleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_role
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_role
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_role
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectRoleExample">
    delete from project_role
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectRole">
    insert into project_role (id, project_id, name, 
      enname, status, sort, 
      global_default, system_default, project_template, 
      org_template, create_user, create_time, 
      update_user, update_time, description, 
      tenant_id, platform_id)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, 
      #{enname,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{sort,jdbcType=INTEGER}, 
      #{globalDefault,jdbcType=BIT}, #{systemDefault,jdbcType=BIT}, #{projectTemplate,jdbcType=BIT}, 
      #{orgTemplate,jdbcType=BIT}, #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{description,jdbcType=VARCHAR}, 
      #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectRole" useGeneratedKeys="true" keyProperty="id">
    insert into project_role
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="enname != null">
        enname,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="globalDefault != null">
        global_default,
      </if>
      <if test="systemDefault != null">
        system_default,
      </if>
      <if test="projectTemplate != null">
        project_template,
      </if>
      <if test="orgTemplate != null">
        org_template,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="enname != null">
        #{enname,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="globalDefault != null">
        #{globalDefault,jdbcType=BIT},
      </if>
      <if test="systemDefault != null">
        #{systemDefault,jdbcType=BIT},
      </if>
      <if test="projectTemplate != null">
        #{projectTemplate,jdbcType=BIT},
      </if>
      <if test="orgTemplate != null">
        #{orgTemplate,jdbcType=BIT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectRoleExample" resultType="java.lang.Long">
    select count(*) from project_role
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_role
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.enname != null">
        enname = #{record.enname,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.globalDefault != null">
        global_default = #{record.globalDefault,jdbcType=BIT},
      </if>
      <if test="record.systemDefault != null">
        system_default = #{record.systemDefault,jdbcType=BIT},
      </if>
      <if test="record.projectTemplate != null">
        project_template = #{record.projectTemplate,jdbcType=BIT},
      </if>
      <if test="record.orgTemplate != null">
        org_template = #{record.orgTemplate,jdbcType=BIT},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_role
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      enname = #{record.enname,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      sort = #{record.sort,jdbcType=INTEGER},
      global_default = #{record.globalDefault,jdbcType=BIT},
      system_default = #{record.systemDefault,jdbcType=BIT},
      project_template = #{record.projectTemplate,jdbcType=BIT},
      org_template = #{record.orgTemplate,jdbcType=BIT},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      description = #{record.description,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectRole">
    update project_role
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="enname != null">
        enname = #{enname,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="globalDefault != null">
        global_default = #{globalDefault,jdbcType=BIT},
      </if>
      <if test="systemDefault != null">
        system_default = #{systemDefault,jdbcType=BIT},
      </if>
      <if test="projectTemplate != null">
        project_template = #{projectTemplate,jdbcType=BIT},
      </if>
      <if test="orgTemplate != null">
        org_template = #{orgTemplate,jdbcType=BIT},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectRole">
    update project_role
    set project_id = #{projectId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      enname = #{enname,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      sort = #{sort,jdbcType=INTEGER},
      global_default = #{globalDefault,jdbcType=BIT},
      system_default = #{systemDefault,jdbcType=BIT},
      project_template = #{projectTemplate,jdbcType=BIT},
      org_template = #{orgTemplate,jdbcType=BIT},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      description = #{description,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!--根据设置条件查询项目角色列表-->
  <select id="selectProjectRoleListByQueryCondition" parameterType="com.haoys.user.domain.entity.ProjectRoleQuery" resultType="com.haoys.user.domain.entity.ProjectRoleQuery">
    select
    <include refid="Base_Column_List" />
        from project_role
    where 1=1
    <if test="id != null and id != ''">
      and id=#{id}
    </if>
    <if test="projectId != null and projectId != ''">
      and project_id = #{projectId}
    </if>
    <if test="name != null and name != ''">
      and name like concat('%', #{name}, '%')
    </if>
    <if test="enname != null and enname != ''">
      and enname like concat('%', #{enname}, '%')
    </if>
    <if test="status != null and status != ''">
      and status = #{status}
    </if>
    <if test="projectTemplate != null">
      and project_template = #{projectTemplate}
    </if>
    <if test="status == null or status == ''">
      and status in (0,1)
    </if>
    order by create_time desc
  </select>

    <select id="selectOne" parameterType="com.haoys.user.domain.entity.ProjectRoleQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from project_role
        <where>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="projectId != null and projectId != ''">
                and project_id = #{projectId}
            </if>
        </where>
    </select>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from project_role
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="getProjectRoleInfoByRoleName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from project_role where project_id = #{projectId} and name = #{roleName} limit 1
    </select>

    <select id="countProjectUserRoleByRoleId" parameterType="com.haoys.user.domain.entity.ProjectRoleQuery" resultType="java.lang.Integer">
    select count(1) from project_user_role where role_id = #{roleId}
  </select>

    <delete id="deleteById" parameterType="java.lang.Long">
    delete from project_role
    where id = #{id,jdbcType=BIGINT}
  </delete>

    <update id="update" parameterType="com.haoys.user.domain.entity.ProjectRoleQuery">
        update project_role
        <set>
            <if test="name != null and name != '' ">name = #{name,jdbcType=VARCHAR},</if>
            <if test="enname != null and enname != '' ">enname = #{enname,jdbcType=VARCHAR},</if>
            <if test="projectId != null">project_id = #{projectId,jdbcType=BIGINT},</if>
            <if test="status != null">status = #{status,jdbcType=INTEGER},</if>
            <if test="sort != null">sort = #{sort,jdbcType=INTEGER},</if>
            <if test="updateUser != null">update_user = #{updateUser,jdbcType=VARCHAR},</if>
            <if test="description != null">description = #{description,jdbcType=VARCHAR},</if>
            update_time = sysdate()
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

  <select id="getProjectRoleListByUserId" resultType="com.haoys.user.domain.vo.auth.ProjectRoleVo">
        SELECT
          project_role.id roleId,
          project_role.name,
          project_role.enname,
          project_role.project_id projectId
      FROM
          project_user_role
          INNER JOIN project_role ON project_role.id = project_user_role.role_id
      WHERE
          project_user_role.user_id = #{userId} and project_role.project_id = #{projectId}
  </select>

  <select id="getProjectRoleInfoByEnName" resultType="com.haoys.user.model.ProjectRole">
    select
    <include refid="Base_Column_List" />
    from project_role where project_id = #{projectId} and enname = #{enname} limit 1
  </select>

  <select id="getProjectTemplateRoleInfoByRoleCode" resultMap="BaseResultMap">
    select  <include refid="Base_Column_List" /> from project_role where project_id is null and enname = #{projectRoleCode} and global_default = true and project_template = true
  </select>

  <select id="getProjectManageRoleInfoByProjectRoleCode" resultMap="BaseResultMap">
    select  <include refid="Base_Column_List" /> from project_role where project_id = #{projectId} and enname = #{code} and tenant_id = #{tenantId} and system_default = true limit 1
  </select>
</mapper>