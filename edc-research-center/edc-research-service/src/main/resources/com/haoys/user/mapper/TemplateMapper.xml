<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.TemplateMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.Template">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="config_type" jdbcType="VARCHAR" property="configType" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="if_private" jdbcType="BIT" property="ifPrivate" />
    <result column="logo_url" jdbcType="VARCHAR" property="logoUrl" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="category_id" jdbcType="INTEGER" property="categoryId" />
    <result column="copy_template_id" jdbcType="VARCHAR" property="copyTemplateId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, code, config_type, description, if_private, logo_url, project_id, category_id, 
    copy_template_id, status, version, create_time, update_time, create_user_id, update_user_id, 
    tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.TemplateExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from template
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from template
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.TemplateExample">
    delete from template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.Template">
    insert into template (id, name, code, 
      config_type, description, if_private, 
      logo_url, project_id, category_id, 
      copy_template_id, status, version, 
      create_time, update_time, create_user_id, 
      update_user_id, tenant_id, platform_id
      )
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, 
      #{configType,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{ifPrivate,jdbcType=BIT}, 
      #{logoUrl,jdbcType=VARCHAR}, #{projectId,jdbcType=BIGINT}, #{categoryId,jdbcType=INTEGER}, 
      #{copyTemplateId,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{version,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=VARCHAR}, 
      #{updateUserId,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.Template">
    insert into template
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="configType != null">
        config_type,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="ifPrivate != null">
        if_private,
      </if>
      <if test="logoUrl != null">
        logo_url,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="copyTemplateId != null">
        copy_template_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="configType != null">
        #{configType,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="ifPrivate != null">
        #{ifPrivate,jdbcType=BIT},
      </if>
      <if test="logoUrl != null">
        #{logoUrl,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="copyTemplateId != null">
        #{copyTemplateId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.TemplateExample" resultType="java.lang.Long">
    select count(*) from template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update template
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.configType != null">
        config_type = #{record.configType,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.ifPrivate != null">
        if_private = #{record.ifPrivate,jdbcType=BIT},
      </if>
      <if test="record.logoUrl != null">
        logo_url = #{record.logoUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.categoryId != null">
        category_id = #{record.categoryId,jdbcType=INTEGER},
      </if>
      <if test="record.copyTemplateId != null">
        copy_template_id = #{record.copyTemplateId,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update template
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      code = #{record.code,jdbcType=VARCHAR},
      config_type = #{record.configType,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      if_private = #{record.ifPrivate,jdbcType=BIT},
      logo_url = #{record.logoUrl,jdbcType=VARCHAR},
      project_id = #{record.projectId,jdbcType=BIGINT},
      category_id = #{record.categoryId,jdbcType=INTEGER},
      copy_template_id = #{record.copyTemplateId,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.Template">
    update template
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="configType != null">
        config_type = #{configType,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="ifPrivate != null">
        if_private = #{ifPrivate,jdbcType=BIT},
      </if>
      <if test="logoUrl != null">
        logo_url = #{logoUrl,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="copyTemplateId != null">
        copy_template_id = #{copyTemplateId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.Template">
    update template
    set name = #{name,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      config_type = #{configType,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      if_private = #{ifPrivate,jdbcType=BIT},
      logo_url = #{logoUrl,jdbcType=VARCHAR},
      project_id = #{projectId,jdbcType=BIGINT},
      category_id = #{categoryId,jdbcType=INTEGER},
      copy_template_id = #{copyTemplateId,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      version = #{version,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="getProjectTemplateListForPage" resultType="com.haoys.user.domain.vo.ecrf.TemplateVo">
    SELECT
      id, name, code, config_type configType, description,
      if_private ifPrivate, logo_url logoUrl,
      project_id projectId, category_id categoryId,
      status, create_time createTime, update_time updateTime,
      create_user_id createUserId, update_user_id updateUserId
    FROM template WHERE  1=1 AND config_type = '1' and status = '0'
    <if test="templateName != null and templateName != ''">
        AND `name` like concat('%', #{templateName}, '%')
    </if>
    order by create_time desc

  </select>

  <select id="getTemplateBaseInfoByTemplateName" resultMap="BaseResultMap">
    select * from template inner join template_form_config on template_form_config.template_id = template.id
    where template_form_config.project_id = #{projectId} and template.name = #{templateName} and template.status = '0'
  </select>

</mapper>