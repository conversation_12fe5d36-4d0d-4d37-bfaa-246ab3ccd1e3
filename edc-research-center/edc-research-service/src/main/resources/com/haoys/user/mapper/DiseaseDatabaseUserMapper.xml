<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.DiseaseDatabaseUserMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.DiseaseDatabaseUser">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="database_id" jdbcType="BIGINT" property="databaseId" />
    <result column="database_name" jdbcType="VARCHAR" property="databaseName" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="validate_start_date" jdbcType="TIMESTAMP" property="validateStartDate" />
    <result column="validate_end_date" jdbcType="TIMESTAMP" property="validateEndDate" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, database_id, database_name, user_id, validate_start_date, validate_end_date, 
    create_user_id, create_time
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.DiseaseDatabaseUserExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from disease_database_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from disease_database_user
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from disease_database_user
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.DiseaseDatabaseUserExample">
    delete from disease_database_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.DiseaseDatabaseUser">
    insert into disease_database_user (id, database_id, database_name, 
      user_id, validate_start_date, validate_end_date, 
      create_user_id, create_time)
    values (#{id,jdbcType=BIGINT}, #{databaseId,jdbcType=BIGINT}, #{databaseName,jdbcType=VARCHAR}, 
      #{userId,jdbcType=BIGINT}, #{validateStartDate,jdbcType=TIMESTAMP}, #{validateEndDate,jdbcType=TIMESTAMP}, 
      #{createUserId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.DiseaseDatabaseUser">
    insert into disease_database_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="databaseId != null">
        database_id,
      </if>
      <if test="databaseName != null">
        database_name,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="validateStartDate != null">
        validate_start_date,
      </if>
      <if test="validateEndDate != null">
        validate_end_date,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="databaseId != null">
        #{databaseId,jdbcType=BIGINT},
      </if>
      <if test="databaseName != null">
        #{databaseName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="validateStartDate != null">
        #{validateStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="validateEndDate != null">
        #{validateEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.DiseaseDatabaseUserExample" resultType="java.lang.Long">
    select count(*) from disease_database_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update disease_database_user
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.databaseId != null">
        database_id = #{record.databaseId,jdbcType=BIGINT},
      </if>
      <if test="record.databaseName != null">
        database_name = #{record.databaseName,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.validateStartDate != null">
        validate_start_date = #{record.validateStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.validateEndDate != null">
        validate_end_date = #{record.validateEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update disease_database_user
    set id = #{record.id,jdbcType=BIGINT},
      database_id = #{record.databaseId,jdbcType=BIGINT},
      database_name = #{record.databaseName,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=BIGINT},
      validate_start_date = #{record.validateStartDate,jdbcType=TIMESTAMP},
      validate_end_date = #{record.validateEndDate,jdbcType=TIMESTAMP},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.DiseaseDatabaseUser">
    update disease_database_user
    <set>
      <if test="databaseId != null">
        database_id = #{databaseId,jdbcType=BIGINT},
      </if>
      <if test="databaseName != null">
        database_name = #{databaseName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="validateStartDate != null">
        validate_start_date = #{validateStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="validateEndDate != null">
        validate_end_date = #{validateEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.DiseaseDatabaseUser">
    update disease_database_user
    set database_id = #{databaseId,jdbcType=BIGINT},
      database_name = #{databaseName,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      validate_start_date = #{validateStartDate,jdbcType=TIMESTAMP},
      validate_end_date = #{validateEndDate,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getDiseaseDatabaseUser" resultMap="BaseResultMap">
    select * from disease_database_user where database_id = #{dataBaseId} and user_id = #{userId} limit 1
  </select>

  <delete id="deleteDiseaseDatabaseUserByUserId">
    delete from disease_database_user where user_id = #{systemUserId}
  </delete>

  <select id="getDiseaseDatabaseListByUserId" resultType="com.haoys.user.model.DiseaseDatabaseUser">
    select * from disease_database_user where user_id = #{systemUserId}
  </select>

  <select id="getDiseaseDatabaseAuth" resultType="com.haoys.user.model.DiseaseDatabaseAuth">
      select * from disease_database_auth where user_id = #{systemUserId}
  </select>
</mapper>