<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectVisitUserMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectVisitUser">
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="testee_id" jdbcType="BIGINT" property="testeeId" />
    <result column="testee_config" jdbcType="VARCHAR" property="testeeConfig" />
    <result column="testee_code" jdbcType="VARCHAR" property="testeeCode" />
    <result column="visit_card_no" jdbcType="VARCHAR" property="visitCardNo" />
    <result column="owner_org_id" jdbcType="VARCHAR" property="ownerOrgId" />
    <result column="owner_org_name" jdbcType="VARCHAR" property="ownerOrgName" />
    <result column="owner_doctor_id" jdbcType="VARCHAR" property="ownerDoctorId" />
    <result column="informed_date" jdbcType="TIMESTAMP" property="informedDate" />
    <result column="resource_form_id" jdbcType="BIGINT" property="resourceFormId" />
    <result column="self_record" jdbcType="BIT" property="selfRecord" />
    <result column="bind_result" jdbcType="BIT" property="bindResult" />
    <result column="bind_time" jdbcType="TIMESTAMP" property="bindTime" />
    <result column="review_flag" jdbcType="BIT" property="reviewFlag" />
    <result column="review_status" jdbcType="VARCHAR" property="reviewStatus" />
    <result column="review_user_id" jdbcType="VARCHAR" property="reviewUserId" />
    <result column="review_time" jdbcType="TIMESTAMP" property="reviewTime" />
    <result column="bind_resource" jdbcType="VARCHAR" property="bindResource" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="research_status" jdbcType="VARCHAR" property="researchStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    project_id, testee_id, testee_config, testee_code, visit_card_no, owner_org_id, owner_org_name, 
    owner_doctor_id, informed_date, resource_form_id, self_record, bind_result, bind_time, 
    review_flag, review_status, review_user_id, review_time, bind_resource, status, research_status, 
    create_time, create_user_id, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectVisitUserExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_visit_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectVisitUserExample">
    delete from project_visit_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectVisitUser">
    insert into project_visit_user (project_id, testee_id, testee_config, 
      testee_code, visit_card_no, owner_org_id, 
      owner_org_name, owner_doctor_id, informed_date, 
      resource_form_id, self_record, bind_result, 
      bind_time, review_flag, review_status, 
      review_user_id, review_time, bind_resource, 
      status, research_status, create_time, 
      create_user_id, tenant_id, platform_id
      )
    values (#{projectId,jdbcType=BIGINT}, #{testeeId,jdbcType=BIGINT}, #{testeeConfig,jdbcType=VARCHAR}, 
      #{testeeCode,jdbcType=VARCHAR}, #{visitCardNo,jdbcType=VARCHAR}, #{ownerOrgId,jdbcType=VARCHAR}, 
      #{ownerOrgName,jdbcType=VARCHAR}, #{ownerDoctorId,jdbcType=VARCHAR}, #{informedDate,jdbcType=TIMESTAMP}, 
      #{resourceFormId,jdbcType=BIGINT}, #{selfRecord,jdbcType=BIT}, #{bindResult,jdbcType=BIT}, 
      #{bindTime,jdbcType=TIMESTAMP}, #{reviewFlag,jdbcType=BIT}, #{reviewStatus,jdbcType=VARCHAR}, 
      #{reviewUserId,jdbcType=VARCHAR}, #{reviewTime,jdbcType=TIMESTAMP}, #{bindResource,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{researchStatus,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createUserId,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectVisitUser">
    insert into project_visit_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        project_id,
      </if>
      <if test="testeeId != null">
        testee_id,
      </if>
      <if test="testeeConfig != null">
        testee_config,
      </if>
      <if test="testeeCode != null">
        testee_code,
      </if>
      <if test="visitCardNo != null">
        visit_card_no,
      </if>
      <if test="ownerOrgId != null">
        owner_org_id,
      </if>
      <if test="ownerOrgName != null">
        owner_org_name,
      </if>
      <if test="ownerDoctorId != null">
        owner_doctor_id,
      </if>
      <if test="informedDate != null">
        informed_date,
      </if>
      <if test="resourceFormId != null">
        resource_form_id,
      </if>
      <if test="selfRecord != null">
        self_record,
      </if>
      <if test="bindResult != null">
        bind_result,
      </if>
      <if test="bindTime != null">
        bind_time,
      </if>
      <if test="reviewFlag != null">
        review_flag,
      </if>
      <if test="reviewStatus != null">
        review_status,
      </if>
      <if test="reviewUserId != null">
        review_user_id,
      </if>
      <if test="reviewTime != null">
        review_time,
      </if>
      <if test="bindResource != null">
        bind_resource,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="researchStatus != null">
        research_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="testeeConfig != null">
        #{testeeConfig,jdbcType=VARCHAR},
      </if>
      <if test="testeeCode != null">
        #{testeeCode,jdbcType=VARCHAR},
      </if>
      <if test="visitCardNo != null">
        #{visitCardNo,jdbcType=VARCHAR},
      </if>
      <if test="ownerOrgId != null">
        #{ownerOrgId,jdbcType=VARCHAR},
      </if>
      <if test="ownerOrgName != null">
        #{ownerOrgName,jdbcType=VARCHAR},
      </if>
      <if test="ownerDoctorId != null">
        #{ownerDoctorId,jdbcType=VARCHAR},
      </if>
      <if test="informedDate != null">
        #{informedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="resourceFormId != null">
        #{resourceFormId,jdbcType=BIGINT},
      </if>
      <if test="selfRecord != null">
        #{selfRecord,jdbcType=BIT},
      </if>
      <if test="bindResult != null">
        #{bindResult,jdbcType=BIT},
      </if>
      <if test="bindTime != null">
        #{bindTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reviewFlag != null">
        #{reviewFlag,jdbcType=BIT},
      </if>
      <if test="reviewStatus != null">
        #{reviewStatus,jdbcType=VARCHAR},
      </if>
      <if test="reviewUserId != null">
        #{reviewUserId,jdbcType=VARCHAR},
      </if>
      <if test="reviewTime != null">
        #{reviewTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bindResource != null">
        #{bindResource,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="researchStatus != null">
        #{researchStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectVisitUserExample" resultType="java.lang.Long">
    select count(*) from project_visit_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_visit_user
    <set>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.testeeId != null">
        testee_id = #{record.testeeId,jdbcType=BIGINT},
      </if>
      <if test="record.testeeConfig != null">
        testee_config = #{record.testeeConfig,jdbcType=VARCHAR},
      </if>
      <if test="record.testeeCode != null">
        testee_code = #{record.testeeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.visitCardNo != null">
        visit_card_no = #{record.visitCardNo,jdbcType=VARCHAR},
      </if>
      <if test="record.ownerOrgId != null">
        owner_org_id = #{record.ownerOrgId,jdbcType=VARCHAR},
      </if>
      <if test="record.ownerOrgName != null">
        owner_org_name = #{record.ownerOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.ownerDoctorId != null">
        owner_doctor_id = #{record.ownerDoctorId,jdbcType=VARCHAR},
      </if>
      <if test="record.informedDate != null">
        informed_date = #{record.informedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.resourceFormId != null">
        resource_form_id = #{record.resourceFormId,jdbcType=BIGINT},
      </if>
      <if test="record.selfRecord != null">
        self_record = #{record.selfRecord,jdbcType=BIT},
      </if>
      <if test="record.bindResult != null">
        bind_result = #{record.bindResult,jdbcType=BIT},
      </if>
      <if test="record.bindTime != null">
        bind_time = #{record.bindTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reviewFlag != null">
        review_flag = #{record.reviewFlag,jdbcType=BIT},
      </if>
      <if test="record.reviewStatus != null">
        review_status = #{record.reviewStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.reviewUserId != null">
        review_user_id = #{record.reviewUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.reviewTime != null">
        review_time = #{record.reviewTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.bindResource != null">
        bind_resource = #{record.bindResource,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.researchStatus != null">
        research_status = #{record.researchStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_visit_user
    set project_id = #{record.projectId,jdbcType=BIGINT},
      testee_id = #{record.testeeId,jdbcType=BIGINT},
      testee_config = #{record.testeeConfig,jdbcType=VARCHAR},
      testee_code = #{record.testeeCode,jdbcType=VARCHAR},
      visit_card_no = #{record.visitCardNo,jdbcType=VARCHAR},
      owner_org_id = #{record.ownerOrgId,jdbcType=VARCHAR},
      owner_org_name = #{record.ownerOrgName,jdbcType=VARCHAR},
      owner_doctor_id = #{record.ownerDoctorId,jdbcType=VARCHAR},
      informed_date = #{record.informedDate,jdbcType=TIMESTAMP},
      resource_form_id = #{record.resourceFormId,jdbcType=BIGINT},
      self_record = #{record.selfRecord,jdbcType=BIT},
      bind_result = #{record.bindResult,jdbcType=BIT},
      bind_time = #{record.bindTime,jdbcType=TIMESTAMP},
      review_flag = #{record.reviewFlag,jdbcType=BIT},
      review_status = #{record.reviewStatus,jdbcType=VARCHAR},
      review_user_id = #{record.reviewUserId,jdbcType=VARCHAR},
      review_time = #{record.reviewTime,jdbcType=TIMESTAMP},
      bind_resource = #{record.bindResource,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      research_status = #{record.researchStatus,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>


  <select id="testeeStatist" resultType="com.haoys.user.domain.param.testee.ProjectTesteeStatistVo">
    select
      SUM(research_status=10000000501) AS screenNum,
      SUM(research_status=10000000502) AS screenNotNum,
      SUM(research_status=10000000503) AS discussNum,
      SUM(research_status=10000000504) AS discussCloseNum,
      SUM(research_status=10000000505) AS suspendNum
    from
        project_visit_user
    where project_id=#{projectId}
      and owner_org_id=#{orgId}
      and status=0
  </select>

  <select id="testeeStatistByDate" resultType="java.lang.Integer">
    select
      count(0)
    from
      project_visit_user
    where project_id=#{projectId}
      and owner_org_id=#{orgId}
      and status=0
      and DATE_FORMAT(create_time,'%Y-%m-%d') = DATE_FORMAT(#{createTime},'%Y-%m-%d')
  </select>

  <select id="getProjectVisitUserInfo" resultMap="BaseResultMap">
    select * from project_visit_user where project_id = #{projectId} and testee_id = #{testeeId} and status = '0'
    <if test="ownerOrgId != null">
     and owner_org_id = #{ownerOrgId}
    </if>
  </select>

  <select id="getMaxtesteeCode" resultType="java.lang.String">
    select
    max(
		trim(
			LEADING '0'
			FROM
				trim(LEADING #{prefix} FROM testee_code)
		)
	)
    from
        project_visit_user
    where project_id=#{projectId}
      and owner_org_id=#{orgId}
      and testee_code like concat(#{prefix}, '%')
  </select>

  <select id="getMaxTesteeCodeForBoRui" resultType="java.lang.String">
    select max(testee_code) from project_visit_user where project_id = #{projectId,jdbcType=BIGINT}
  </select>

  <select id="getMobileProjectVisitUser" resultMap="BaseResultMap">
    select * from project_visit_user where project_id = #{projectId} and testee_id = #{testeeId}
    <if test="ownerOrgId != null">
      and owner_org_id = #{ownerOrgId}
    </if>
  </select>

  <select id="getProjectTesteeUserByUserId" resultMap="BaseResultMap">
    select project_visit_user.* from project_visit_user inner join project_testee_info on project_testee_info.id = project_visit_user.testee_id
    where project_visit_user.project_id = #{projectId}
    and project_testee_info.user_id = #{userId} and project_testee_info.tenant_id = #{tenantId}
    and project_testee_info.platform_id = #{platformId}
  </select>

  <select id="getProjectDeleteVisitUserInfo" resultMap="BaseResultMap">
    select * from project_visit_user where project_id = #{projectId} and testee_id = #{testeeId} and owner_org_id = #{ownerOrgId} and status = '1'
  </select>
</mapper>