<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.RctsRandomizedParamsConfigMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.RctsRandomizedParamsConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="project_org_id" jdbcType="BIGINT" property="projectOrgId" />
    <result column="join_group_limit" jdbcType="INTEGER" property="joinGroupLimit" />
    <result column="randomized_type" jdbcType="VARCHAR" property="randomizedType" />
    <result column="randomized_method" jdbcType="VARCHAR" property="randomizedMethod" />
    <result column="randomized_rule" jdbcType="VARCHAR" property="randomizedRule" />
    <result column="group_size" jdbcType="INTEGER" property="groupSize" />
    <result column="randomized_frefix" jdbcType="VARCHAR" property="randomizedFrefix" />
    <result column="randomized_digit" jdbcType="INTEGER" property="randomizedDigit" />
    <result column="concat_org_code" jdbcType="BIT" property="concatOrgCode" />
    <result column="randomized_seed" jdbcType="INTEGER" property="randomizedSeed" />
    <result column="randomized_group_config" jdbcType="VARCHAR" property="randomizedGroupConfig" />
    <result column="randomized_layer_config" jdbcType="VARCHAR" property="randomizedLayerConfig" />
    <result column="enabled" jdbcType="BIT" property="enabled" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="expand" jdbcType="VARCHAR" property="expand" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, project_org_id, join_group_limit, randomized_type, randomized_method, 
    randomized_rule, group_size, randomized_frefix, randomized_digit, concat_org_code, 
    randomized_seed, randomized_group_config, randomized_layer_config, enabled, status, 
    expand, create_time, update_time, create_user_id, update_user_id, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.RctsRandomizedParamsConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from rcts_randomized_params_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from rcts_randomized_params_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from rcts_randomized_params_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.RctsRandomizedParamsConfigExample">
    delete from rcts_randomized_params_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.RctsRandomizedParamsConfig">
    insert into rcts_randomized_params_config (id, project_id, project_org_id, 
      join_group_limit, randomized_type, randomized_method, 
      randomized_rule, group_size, randomized_frefix, 
      randomized_digit, concat_org_code, randomized_seed, 
      randomized_group_config, randomized_layer_config, 
      enabled, status, expand, 
      create_time, update_time, create_user_id, 
      update_user_id, tenant_id, platform_id
      )
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{projectOrgId,jdbcType=BIGINT}, 
      #{joinGroupLimit,jdbcType=INTEGER}, #{randomizedType,jdbcType=VARCHAR}, #{randomizedMethod,jdbcType=VARCHAR}, 
      #{randomizedRule,jdbcType=VARCHAR}, #{groupSize,jdbcType=INTEGER}, #{randomizedFrefix,jdbcType=VARCHAR}, 
      #{randomizedDigit,jdbcType=INTEGER}, #{concatOrgCode,jdbcType=BIT}, #{randomizedSeed,jdbcType=INTEGER}, 
      #{randomizedGroupConfig,jdbcType=VARCHAR}, #{randomizedLayerConfig,jdbcType=VARCHAR},
      #{enabled,jdbcType=BIT}, #{status,jdbcType=VARCHAR}, #{expand,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=VARCHAR}, 
      #{updateUserId,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.RctsRandomizedParamsConfig">
    insert into rcts_randomized_params_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="projectOrgId != null">
        project_org_id,
      </if>
      <if test="joinGroupLimit != null">
        join_group_limit,
      </if>
      <if test="randomizedType != null">
        randomized_type,
      </if>
      <if test="randomizedMethod != null">
        randomized_method,
      </if>
      <if test="randomizedRule != null">
        randomized_rule,
      </if>
      <if test="groupSize != null">
        group_size,
      </if>
      <if test="randomizedFrefix != null">
        randomized_frefix,
      </if>
      <if test="randomizedDigit != null">
        randomized_digit,
      </if>
      <if test="concatOrgCode != null">
        concat_org_code,
      </if>
      <if test="randomizedSeed != null">
        randomized_seed,
      </if>
      <if test="randomizedGroupConfig != null">
        randomized_group_config,
      </if>
      <if test="randomizedLayerConfig != null">
        randomized_layer_config,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="expand != null">
        expand,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="projectOrgId != null">
        #{projectOrgId,jdbcType=BIGINT},
      </if>
      <if test="joinGroupLimit != null">
        #{joinGroupLimit,jdbcType=INTEGER},
      </if>
      <if test="randomizedType != null">
        #{randomizedType,jdbcType=VARCHAR},
      </if>
      <if test="randomizedMethod != null">
        #{randomizedMethod,jdbcType=VARCHAR},
      </if>
      <if test="randomizedRule != null">
        #{randomizedRule,jdbcType=VARCHAR},
      </if>
      <if test="groupSize != null">
        #{groupSize,jdbcType=INTEGER},
      </if>
      <if test="randomizedFrefix != null">
        #{randomizedFrefix,jdbcType=VARCHAR},
      </if>
      <if test="randomizedDigit != null">
        #{randomizedDigit,jdbcType=INTEGER},
      </if>
      <if test="concatOrgCode != null">
        #{concatOrgCode,jdbcType=BIT},
      </if>
      <if test="randomizedSeed != null">
        #{randomizedSeed,jdbcType=INTEGER},
      </if>
      <if test="randomizedGroupConfig != null">
        #{randomizedGroupConfig,jdbcType=VARCHAR},
      </if>
      <if test="randomizedLayerConfig != null">
        #{randomizedLayerConfig,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=BIT},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        #{expand,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.RctsRandomizedParamsConfigExample" resultType="java.lang.Long">
    select count(*) from rcts_randomized_params_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update rcts_randomized_params_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.projectOrgId != null">
        project_org_id = #{record.projectOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.joinGroupLimit != null">
        join_group_limit = #{record.joinGroupLimit,jdbcType=INTEGER},
      </if>
      <if test="record.randomizedType != null">
        randomized_type = #{record.randomizedType,jdbcType=VARCHAR},
      </if>
      <if test="record.randomizedMethod != null">
        randomized_method = #{record.randomizedMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.randomizedRule != null">
        randomized_rule = #{record.randomizedRule,jdbcType=VARCHAR},
      </if>
      <if test="record.groupSize != null">
        group_size = #{record.groupSize,jdbcType=INTEGER},
      </if>
      <if test="record.randomizedFrefix != null">
        randomized_frefix = #{record.randomizedFrefix,jdbcType=VARCHAR},
      </if>
      <if test="record.randomizedDigit != null">
        randomized_digit = #{record.randomizedDigit,jdbcType=INTEGER},
      </if>
      <if test="record.concatOrgCode != null">
        concat_org_code = #{record.concatOrgCode,jdbcType=BIT},
      </if>
      <if test="record.randomizedSeed != null">
        randomized_seed = #{record.randomizedSeed,jdbcType=INTEGER},
      </if>
      <if test="record.randomizedGroupConfig != null">
        randomized_group_config = #{record.randomizedGroupConfig,jdbcType=VARCHAR},
      </if>
      <if test="record.randomizedLayerConfig != null">
        randomized_layer_config = #{record.randomizedLayerConfig,jdbcType=VARCHAR},
      </if>
      <if test="record.enabled != null">
        enabled = #{record.enabled,jdbcType=BIT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.expand != null">
        expand = #{record.expand,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update rcts_randomized_params_config
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      project_org_id = #{record.projectOrgId,jdbcType=BIGINT},
      join_group_limit = #{record.joinGroupLimit,jdbcType=INTEGER},
      randomized_type = #{record.randomizedType,jdbcType=VARCHAR},
      randomized_method = #{record.randomizedMethod,jdbcType=VARCHAR},
      randomized_rule = #{record.randomizedRule,jdbcType=VARCHAR},
      group_size = #{record.groupSize,jdbcType=INTEGER},
      randomized_frefix = #{record.randomizedFrefix,jdbcType=VARCHAR},
      randomized_digit = #{record.randomizedDigit,jdbcType=INTEGER},
      concat_org_code = #{record.concatOrgCode,jdbcType=BIT},
      randomized_seed = #{record.randomizedSeed,jdbcType=INTEGER},
      randomized_group_config = #{record.randomizedGroupConfig,jdbcType=VARCHAR},
      randomized_layer_config = #{record.randomizedLayerConfig,jdbcType=VARCHAR},
      enabled = #{record.enabled,jdbcType=BIT},
      status = #{record.status,jdbcType=VARCHAR},
      expand = #{record.expand,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.RctsRandomizedParamsConfig">
    update rcts_randomized_params_config
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="projectOrgId != null">
        project_org_id = #{projectOrgId,jdbcType=BIGINT},
      </if>
      <if test="joinGroupLimit != null">
        join_group_limit = #{joinGroupLimit,jdbcType=INTEGER},
      </if>
      <if test="randomizedType != null">
        randomized_type = #{randomizedType,jdbcType=VARCHAR},
      </if>
      <if test="randomizedMethod != null">
        randomized_method = #{randomizedMethod,jdbcType=VARCHAR},
      </if>
      <if test="randomizedRule != null">
        randomized_rule = #{randomizedRule,jdbcType=VARCHAR},
      </if>
      <if test="groupSize != null">
        group_size = #{groupSize,jdbcType=INTEGER},
      </if>
      <if test="randomizedFrefix != null">
        randomized_frefix = #{randomizedFrefix,jdbcType=VARCHAR},
      </if>
      <if test="randomizedDigit != null">
        randomized_digit = #{randomizedDigit,jdbcType=INTEGER},
      </if>
      <if test="concatOrgCode != null">
        concat_org_code = #{concatOrgCode,jdbcType=BIT},
      </if>
      <if test="randomizedSeed != null">
        randomized_seed = #{randomizedSeed,jdbcType=INTEGER},
      </if>
      <if test="randomizedGroupConfig != null">
        randomized_group_config = #{randomizedGroupConfig,jdbcType=VARCHAR},
      </if>
      <if test="randomizedLayerConfig != null">
        randomized_layer_config = #{randomizedLayerConfig,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null">
        enabled = #{enabled,jdbcType=BIT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        expand = #{expand,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.RctsRandomizedParamsConfig">
    update rcts_randomized_params_config
    set project_id = #{projectId,jdbcType=BIGINT},
      project_org_id = #{projectOrgId,jdbcType=BIGINT},
      join_group_limit = #{joinGroupLimit,jdbcType=INTEGER},
      randomized_type = #{randomizedType,jdbcType=VARCHAR},
      randomized_method = #{randomizedMethod,jdbcType=VARCHAR},
      randomized_rule = #{randomizedRule,jdbcType=VARCHAR},
      group_size = #{groupSize,jdbcType=INTEGER},
      randomized_frefix = #{randomizedFrefix,jdbcType=VARCHAR},
      randomized_digit = #{randomizedDigit,jdbcType=INTEGER},
      concat_org_code = #{concatOrgCode,jdbcType=BIT},
      randomized_seed = #{randomizedSeed,jdbcType=INTEGER},
      randomized_group_config = #{randomizedGroupConfig,jdbcType=VARCHAR},
      randomized_layer_config = #{randomizedLayerConfig,jdbcType=VARCHAR},
      enabled = #{enabled,jdbcType=BIT},
      status = #{status,jdbcType=VARCHAR},
      expand = #{expand,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="getRandomizedParamsConfig" resultType="com.haoys.user.domain.vo.rcts.RandomizedParamsVo">
    select *
    from rcts_randomized_params_config
    where status = #{status} and  project_id = #{projectId} limit 1
  </select>

</mapper>