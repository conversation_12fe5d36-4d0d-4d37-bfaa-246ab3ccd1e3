<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.TemplateVariableViewConfigMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.TemplateVariableViewConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="variable_option_id" jdbcType="BIGINT" property="variableOptionId" />
    <result column="template_id" jdbcType="BIGINT" property="templateId" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="form_detail_id" jdbcType="BIGINT" property="formDetailId" />
    <result column="form_table_id" jdbcType="BIGINT" property="formTableId" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="view_config_result" jdbcType="VARCHAR" property="viewConfigResult" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="extands" jdbcType="VARCHAR" property="extands" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, variable_option_id, template_id, project_id, form_id, form_detail_id, form_table_id, 
    group_id, label, view_config_result, create_user_id, update_user_id, create_time, 
    update_time, extands, status, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.TemplateVariableViewConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from template_variable_view_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from template_variable_view_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from template_variable_view_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.TemplateVariableViewConfigExample">
    delete from template_variable_view_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.TemplateVariableViewConfig">
    insert into template_variable_view_config (id, variable_option_id, template_id, 
      project_id, form_id, form_detail_id, 
      form_table_id, group_id, label, 
      view_config_result, create_user_id, update_user_id, 
      create_time, update_time, extands, 
      status, tenant_id, platform_id
      )
    values (#{id,jdbcType=BIGINT}, #{variableOptionId,jdbcType=BIGINT}, #{templateId,jdbcType=BIGINT}, 
      #{projectId,jdbcType=BIGINT}, #{formId,jdbcType=BIGINT}, #{formDetailId,jdbcType=BIGINT}, 
      #{formTableId,jdbcType=BIGINT}, #{groupId,jdbcType=BIGINT}, #{label,jdbcType=VARCHAR}, 
      #{viewConfigResult,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, #{updateUserId,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{extands,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.TemplateVariableViewConfig">
    insert into template_variable_view_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="variableOptionId != null">
        variable_option_id,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="formDetailId != null">
        form_detail_id,
      </if>
      <if test="formTableId != null">
        form_table_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="label != null">
        label,
      </if>
      <if test="viewConfigResult != null">
        view_config_result,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="extands != null">
        extands,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="variableOptionId != null">
        #{variableOptionId,jdbcType=BIGINT},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="formDetailId != null">
        #{formDetailId,jdbcType=BIGINT},
      </if>
      <if test="formTableId != null">
        #{formTableId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="label != null">
        #{label,jdbcType=VARCHAR},
      </if>
      <if test="viewConfigResult != null">
        #{viewConfigResult,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extands != null">
        #{extands,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.TemplateVariableViewConfigExample" resultType="java.lang.Long">
    select count(*) from template_variable_view_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update template_variable_view_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.variableOptionId != null">
        variable_option_id = #{record.variableOptionId,jdbcType=BIGINT},
      </if>
      <if test="record.templateId != null">
        template_id = #{record.templateId,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=BIGINT},
      </if>
      <if test="record.formDetailId != null">
        form_detail_id = #{record.formDetailId,jdbcType=BIGINT},
      </if>
      <if test="record.formTableId != null">
        form_table_id = #{record.formTableId,jdbcType=BIGINT},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=BIGINT},
      </if>
      <if test="record.label != null">
        label = #{record.label,jdbcType=VARCHAR},
      </if>
      <if test="record.viewConfigResult != null">
        view_config_result = #{record.viewConfigResult,jdbcType=VARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extands != null">
        extands = #{record.extands,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update template_variable_view_config
    set id = #{record.id,jdbcType=BIGINT},
      variable_option_id = #{record.variableOptionId,jdbcType=BIGINT},
      template_id = #{record.templateId,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      form_id = #{record.formId,jdbcType=BIGINT},
      form_detail_id = #{record.formDetailId,jdbcType=BIGINT},
      form_table_id = #{record.formTableId,jdbcType=BIGINT},
      group_id = #{record.groupId,jdbcType=BIGINT},
      label = #{record.label,jdbcType=VARCHAR},
      view_config_result = #{record.viewConfigResult,jdbcType=VARCHAR},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      extands = #{record.extands,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.TemplateVariableViewConfig">
    update template_variable_view_config
    <set>
      <if test="variableOptionId != null">
        variable_option_id = #{variableOptionId,jdbcType=BIGINT},
      </if>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=BIGINT},
      </if>
      <if test="formDetailId != null">
        form_detail_id = #{formDetailId,jdbcType=BIGINT},
      </if>
      <if test="formTableId != null">
        form_table_id = #{formTableId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="label != null">
        label = #{label,jdbcType=VARCHAR},
      </if>
      <if test="viewConfigResult != null">
        view_config_result = #{viewConfigResult,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extands != null">
        extands = #{extands,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.TemplateVariableViewConfig">
    update template_variable_view_config
    set variable_option_id = #{variableOptionId,jdbcType=BIGINT},
      template_id = #{templateId,jdbcType=BIGINT},
      project_id = #{projectId,jdbcType=BIGINT},
      form_id = #{formId,jdbcType=BIGINT},
      form_detail_id = #{formDetailId,jdbcType=BIGINT},
      form_table_id = #{formTableId,jdbcType=BIGINT},
      group_id = #{groupId,jdbcType=BIGINT},
      label = #{label,jdbcType=VARCHAR},
      view_config_result = #{viewConfigResult,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      extands = #{extands,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!--根据optionId查询表单联动控制变量规则列表-->
  <select id="getTemplateFormVariableListByOptionId" resultType="com.haoys.user.domain.vo.ecrf.TemplateFormVariableViewConfigVo">
    select <include refid="Base_Column_List" /> from template_variable_view_config
    where project_id = #{projectId} and form_id = #{formId} and variable_option_id = #{optionValueId} and status = '0'
  </select>

  <select id="getTemplateFormVariableListByOptionIds" resultType="com.haoys.user.domain.vo.ecrf.TemplateFormVariableViewConfigVo">
    select
      template_variable_view_config.variable_option_id,
      template_variable_view_config.project_id,template_variable_view_config.form_id,
      template_variable_view_config.form_detail_id,template_variable_view_config.form_table_id,
      template_variable_view_config.label,max(template_variable_view_config.view_config_result) view_config_result
    from template_variable_view_config
    inner join template_variable_view_base on template_variable_view_base.id = template_variable_view_config.variable_option_id
    where template_variable_view_config.project_id = #{projectId} and template_variable_view_config.form_id = #{formId}
    and template_variable_view_base.option_value in (${optionValueIds}) and template_variable_view_config.status = '0'
    group by template_variable_view_config.form_detail_id
  </select>

  <delete id="deleteTemplateVariableViewResultConfig">
    delete from template_variable_view_config where variable_option_id = #{variableOptionValueId}
  </delete>

  <select id="getTemplateVariableViewConfigListByDetailId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from template_variable_view_config
    where project_id = #{projectId} and form_id = #{formId} and form_detail_id = #{formDetailId}
  </select>
</mapper>