<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.RctsRandomizedBlindRecordMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.RctsRandomizedBlindRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="project_org_id" jdbcType="BIGINT" property="projectOrgId" />
    <result column="batch_no" jdbcType="INTEGER" property="batchNo" />
    <result column="batch_item_no" jdbcType="INTEGER" property="batchItemNo" />
    <result column="randomized_number" jdbcType="VARCHAR" property="randomizedNumber" />
    <result column="join_group_name" jdbcType="VARCHAR" property="joinGroupName" />
    <result column="randomized_time" jdbcType="TIMESTAMP" property="randomizedTime" />
    <result column="bind_testee_id" jdbcType="VARCHAR" property="bindTesteeId" />
    <result column="bind_randomized_time" jdbcType="TIMESTAMP" property="bindRandomizedTime" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="expand" jdbcType="VARCHAR" property="expand" />
    <result column="data_from" jdbcType="VARCHAR" property="dataFrom" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, project_org_id, batch_no, batch_item_no, randomized_number, join_group_name, 
    randomized_time, bind_testee_id, bind_randomized_time, status, expand, data_from, 
    create_time, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.RctsRandomizedBlindRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from rcts_randomized_blind_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from rcts_randomized_blind_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from rcts_randomized_blind_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.RctsRandomizedBlindRecordExample">
    delete from rcts_randomized_blind_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.RctsRandomizedBlindRecord">
    insert into rcts_randomized_blind_record (id, project_id, project_org_id, 
      batch_no, batch_item_no, randomized_number, 
      join_group_name, randomized_time, bind_testee_id, 
      bind_randomized_time, status, expand, 
      data_from, create_time, tenant_id, 
      platform_id)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{projectOrgId,jdbcType=BIGINT}, 
      #{batchNo,jdbcType=INTEGER}, #{batchItemNo,jdbcType=INTEGER}, #{randomizedNumber,jdbcType=VARCHAR}, 
      #{joinGroupName,jdbcType=VARCHAR}, #{randomizedTime,jdbcType=TIMESTAMP}, #{bindTesteeId,jdbcType=VARCHAR}, 
      #{bindRandomizedTime,jdbcType=TIMESTAMP}, #{status,jdbcType=VARCHAR}, #{expand,jdbcType=VARCHAR}, 
      #{dataFrom,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{tenantId,jdbcType=VARCHAR}, 
      #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.RctsRandomizedBlindRecord">
    insert into rcts_randomized_blind_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="projectOrgId != null">
        project_org_id,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="batchItemNo != null">
        batch_item_no,
      </if>
      <if test="randomizedNumber != null">
        randomized_number,
      </if>
      <if test="joinGroupName != null">
        join_group_name,
      </if>
      <if test="randomizedTime != null">
        randomized_time,
      </if>
      <if test="bindTesteeId != null">
        bind_testee_id,
      </if>
      <if test="bindRandomizedTime != null">
        bind_randomized_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="expand != null">
        expand,
      </if>
      <if test="dataFrom != null">
        data_from,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="projectOrgId != null">
        #{projectOrgId,jdbcType=BIGINT},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=INTEGER},
      </if>
      <if test="batchItemNo != null">
        #{batchItemNo,jdbcType=INTEGER},
      </if>
      <if test="randomizedNumber != null">
        #{randomizedNumber,jdbcType=VARCHAR},
      </if>
      <if test="joinGroupName != null">
        #{joinGroupName,jdbcType=VARCHAR},
      </if>
      <if test="randomizedTime != null">
        #{randomizedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bindTesteeId != null">
        #{bindTesteeId,jdbcType=VARCHAR},
      </if>
      <if test="bindRandomizedTime != null">
        #{bindRandomizedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        #{expand,jdbcType=VARCHAR},
      </if>
      <if test="dataFrom != null">
        #{dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.RctsRandomizedBlindRecordExample" resultType="java.lang.Long">
    select count(*) from rcts_randomized_blind_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update rcts_randomized_blind_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.projectOrgId != null">
        project_org_id = #{record.projectOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=INTEGER},
      </if>
      <if test="record.batchItemNo != null">
        batch_item_no = #{record.batchItemNo,jdbcType=INTEGER},
      </if>
      <if test="record.randomizedNumber != null">
        randomized_number = #{record.randomizedNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.joinGroupName != null">
        join_group_name = #{record.joinGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.randomizedTime != null">
        randomized_time = #{record.randomizedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.bindTesteeId != null">
        bind_testee_id = #{record.bindTesteeId,jdbcType=VARCHAR},
      </if>
      <if test="record.bindRandomizedTime != null">
        bind_randomized_time = #{record.bindRandomizedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.expand != null">
        expand = #{record.expand,jdbcType=VARCHAR},
      </if>
      <if test="record.dataFrom != null">
        data_from = #{record.dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update rcts_randomized_blind_record
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      project_org_id = #{record.projectOrgId,jdbcType=BIGINT},
      batch_no = #{record.batchNo,jdbcType=INTEGER},
      batch_item_no = #{record.batchItemNo,jdbcType=INTEGER},
      randomized_number = #{record.randomizedNumber,jdbcType=VARCHAR},
      join_group_name = #{record.joinGroupName,jdbcType=VARCHAR},
      randomized_time = #{record.randomizedTime,jdbcType=TIMESTAMP},
      bind_testee_id = #{record.bindTesteeId,jdbcType=VARCHAR},
      bind_randomized_time = #{record.bindRandomizedTime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=VARCHAR},
      expand = #{record.expand,jdbcType=VARCHAR},
      data_from = #{record.dataFrom,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.RctsRandomizedBlindRecord">
    update rcts_randomized_blind_record
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="projectOrgId != null">
        project_org_id = #{projectOrgId,jdbcType=BIGINT},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=INTEGER},
      </if>
      <if test="batchItemNo != null">
        batch_item_no = #{batchItemNo,jdbcType=INTEGER},
      </if>
      <if test="randomizedNumber != null">
        randomized_number = #{randomizedNumber,jdbcType=VARCHAR},
      </if>
      <if test="joinGroupName != null">
        join_group_name = #{joinGroupName,jdbcType=VARCHAR},
      </if>
      <if test="randomizedTime != null">
        randomized_time = #{randomizedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bindTesteeId != null">
        bind_testee_id = #{bindTesteeId,jdbcType=VARCHAR},
      </if>
      <if test="bindRandomizedTime != null">
        bind_randomized_time = #{bindRandomizedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        expand = #{expand,jdbcType=VARCHAR},
      </if>
      <if test="dataFrom != null">
        data_from = #{dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.RctsRandomizedBlindRecord">
    update rcts_randomized_blind_record
    set project_id = #{projectId,jdbcType=BIGINT},
      project_org_id = #{projectOrgId,jdbcType=BIGINT},
      batch_no = #{batchNo,jdbcType=INTEGER},
      batch_item_no = #{batchItemNo,jdbcType=INTEGER},
      randomized_number = #{randomizedNumber,jdbcType=VARCHAR},
      join_group_name = #{joinGroupName,jdbcType=VARCHAR},
      randomized_time = #{randomizedTime,jdbcType=TIMESTAMP},
      bind_testee_id = #{bindTesteeId,jdbcType=VARCHAR},
      bind_randomized_time = #{bindRandomizedTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=VARCHAR},
      expand = #{expand,jdbcType=VARCHAR},
      data_from = #{dataFrom,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="getRandomizedBlindPage" resultType="com.haoys.user.domain.vo.rcts.RandomizedBlindRecordVo">
    SELECT br.id, br.project_id projectId, br.project_org_id projectOrgId, soi.org_name projectOrgName,
           br.batch_no batchNo, br.batch_item_no batchItemNo, br.randomized_number randomizedNumber,
           br.join_group_name joinGroupName, br.bind_testee_id bindTesteeId, br.expand,
           br.randomized_time randomizedTime, br.bind_randomized_time bindRandomizedTime,
           ti.research_status researchStatus
    FROM rcts_randomized_blind_record br LEFT JOIN rcts_testee_info ti ON br.bind_testee_id = ti.testee_id
    LEFT JOIN  project_org_info poi ON br.project_org_id = poi.id
    LEFT JOIN system_org_info soi ON poi.org_id = soi.org_id
    WHERE br.project_id = #{projectId}
    <if test="orgId != null">
      AND br.project_org_id = #{orgId}
    </if>
    <if test="batchNo != null">
      AND br.batch_no like concat('%',#{batchNo},'%')
    </if>
    <if test="randomizedNumber != null">
      AND br.randomized_number like concat('%',#{randomizedNumber},'%')
    </if>
  </select>

  <select id="getProjectRandomizedConfigNumber" resultType="com.haoys.user.model.RctsRandomizedBlindRecord">
    SELECT br.*, ti.research_status researchStatus FROM rcts_randomized_blind_record br LEFT JOIN rcts_testee_info ti ON br.bind_testee_id = ti.testee_id where br.project_id = #{projectId} and br.bind_testee_id = #{bindTesteeId} and br.status = '0'
  </select>

  <select id="getBlindId" resultType="string">
    SELECT id FROM rcts_randomized_blind_record  WHERE project_id = #{projectId}
    AND project_org_id = #{projectOrgId}
    AND STATUS = '0' AND (bind_testee_id IS NULL OR bind_testee_id = '')
    <foreach item="expand" index="index" collection="expands" >
      and expand like concat('%',#{expand},'%')
    </foreach>
    limit 0, 1
  </select>

</mapper>