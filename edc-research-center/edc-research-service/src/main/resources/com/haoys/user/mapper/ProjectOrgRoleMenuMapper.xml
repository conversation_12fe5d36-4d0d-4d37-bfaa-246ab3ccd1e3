<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectOrgRoleMenuMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectOrgRoleMenu">
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="role_id" jdbcType="BIGINT" property="roleId" />
    <result column="menu_id" jdbcType="BIGINT" property="menuId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    project_id, org_id, role_id, menu_id, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectOrgRoleMenuExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_org_role_menu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectOrgRoleMenuExample">
    delete from project_org_role_menu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectOrgRoleMenu">
    insert into project_org_role_menu (project_id, org_id, role_id, 
      menu_id, tenant_id, platform_id
      )
    values (#{projectId,jdbcType=BIGINT}, #{orgId,jdbcType=BIGINT}, #{roleId,jdbcType=BIGINT}, 
      #{menuId,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectOrgRoleMenu">
    insert into project_org_role_menu
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        project_id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
      <if test="menuId != null">
        menu_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=BIGINT},
      </if>
      <if test="menuId != null">
        #{menuId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectOrgRoleMenuExample" resultType="java.lang.Long">
    select count(*) from project_org_role_menu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_org_role_menu
    <set>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.roleId != null">
        role_id = #{record.roleId,jdbcType=BIGINT},
      </if>
      <if test="record.menuId != null">
        menu_id = #{record.menuId,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_org_role_menu
    set project_id = #{record.projectId,jdbcType=BIGINT},
      org_id = #{record.orgId,jdbcType=BIGINT},
      role_id = #{record.roleId,jdbcType=BIGINT},
      menu_id = #{record.menuId,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>


  <delete id="deleteByRoleId">
    delete from project_org_role_menu where role_id = #{roleId}
  </delete>

  <select id="selectProjectOrgMenusListByRoleId" resultType="java.lang.String">
    select menu_id from project_org_role_menu where role_id=#{roleId}
  </select>

  <insert id="batchSaveInsertProjectOrgRoleMenuList">
    insert into project_org_role_menu (project_id,org_id, role_id, menu_id, tenant_id, platform_id) values
    <foreach item="item" index="index" collection="projectOrgRoleMenuList" separator=",">
      (#{item.projectId,jdbcType=BIGINT}, #{item.orgId,jdbcType=BIGINT}, #{item.roleId,jdbcType=BIGINT}, #{item.menuId,jdbcType=BIGINT},
      #{item.tenantId,jdbcType=VARCHAR}, #{item.platformId,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <insert id="batchSaveProjectOrgRoleMenuList">
    insert into project_org_role_menu(project_id, role_id, org_id, menu_id, tenant_id, platform_id) values
    <foreach item="menuId" index="index" collection="menuIds" separator=",">
      (#{projectId},#{projectOrgRoleId},#{projectOrgId}, #{menuId},#{tenantId}, #{platformId})
    </foreach>
  </insert>

</mapper>