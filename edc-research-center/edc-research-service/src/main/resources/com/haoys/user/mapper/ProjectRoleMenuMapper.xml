<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectRoleMenuMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectRoleMenu">
    <id column="role_id" jdbcType="BIGINT" property="roleId" />
    <id column="menu_id" jdbcType="BIGINT" property="menuId" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    role_id, menu_id, project_id, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectRoleMenuExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_role_menu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from project_role_menu
    where role_id = #{roleId,jdbcType=BIGINT}
      and menu_id = #{menuId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from project_role_menu
    where role_id = #{roleId,jdbcType=BIGINT}
      and menu_id = #{menuId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectRoleMenuExample">
    delete from project_role_menu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectRoleMenu">
    insert into project_role_menu (role_id, menu_id, project_id,
      tenant_id, platform_id)
    values (#{roleId,jdbcType=BIGINT}, #{menuId,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT},
      #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectRoleMenu">
    insert into project_role_menu
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="roleId != null">
        role_id,
      </if>
      <if test="menuId != null">
        menu_id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="roleId != null">
        #{roleId,jdbcType=BIGINT},
      </if>
      <if test="menuId != null">
        #{menuId,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectRoleMenuExample" resultType="java.lang.Long">
    select count(*) from project_role_menu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_role_menu
    <set>
      <if test="record.roleId != null">
        role_id = #{record.roleId,jdbcType=BIGINT},
      </if>
      <if test="record.menuId != null">
        menu_id = #{record.menuId,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_role_menu
    set role_id = #{record.roleId,jdbcType=BIGINT},
      menu_id = #{record.menuId,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectRoleMenu">
    update project_role_menu
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where role_id = #{roleId,jdbcType=BIGINT}
      and menu_id = #{menuId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectRoleMenu">
    update project_role_menu
    set project_id = #{projectId,jdbcType=BIGINT},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where role_id = #{roleId,jdbcType=BIGINT}
      and menu_id = #{menuId,jdbcType=BIGINT}
  </update>

  <!--批量插入项目角色菜单信息-->
  <insert id="batchSaveProjectRoleMenuList">
    insert into project_role_menu(project_id, role_id, menu_id, tenant_id, platform_id) values
    <foreach item="menuId" index="index" collection="menuIds" separator=",">
      (#{projectId}, #{roleId}, #{menuId}, #{tenantId}, #{platformId})
    </foreach>
  </insert>

  <!--根据项目id和角色id查询项目角色-->
  <delete id="deleteProjectRoleMenuByRoleId" parameterType="Long">
        delete from project_role_menu where role_id = #{roleId}
    </delete>


  <select id="selectList" parameterType="java.lang.Long" resultMap="BaseResultMap">
		select role_id,menu_id from project_role_menu where role_id = #{roleId}
	</select>

  <select id="selectProjectMenuIdsByProjectRoleId" resultType="java.lang.String">
    select menu_id from project_role_menu where role_id=#{roleId}
  </select>

  <select id="selectByRoleId" resultType="java.lang.String">
  select menu_id from project_role_menu where role_id=#{roleId}
  </select>
</mapper>
