<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.TemplateFormVariableFunMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.TemplateFormVariableFun">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="fun_name" jdbcType="VARCHAR" property="funName" />
    <result column="fun" jdbcType="VARCHAR" property="fun" />
    <result column="fun_example" jdbcType="VARCHAR" property="funExample" />
    <result column="fun_desc" jdbcType="VARCHAR" property="funDesc" />
    <result column="fun_type" jdbcType="INTEGER" property="funType" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, fun_name, fun, fun_example, fun_desc, fun_type, status
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.TemplateFormVariableFunExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from template_form_variable_fun
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from template_form_variable_fun
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from template_form_variable_fun
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.TemplateFormVariableFunExample">
    delete from template_form_variable_fun
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.TemplateFormVariableFun">
    insert into template_form_variable_fun (id, fun_name, fun, 
      fun_example, fun_desc, fun_type, 
      status)
    values (#{id,jdbcType=INTEGER}, #{funName,jdbcType=VARCHAR}, #{fun,jdbcType=VARCHAR}, 
      #{funExample,jdbcType=VARCHAR}, #{funDesc,jdbcType=VARCHAR}, #{funType,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.TemplateFormVariableFun">
    insert into template_form_variable_fun
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="funName != null">
        fun_name,
      </if>
      <if test="fun != null">
        fun,
      </if>
      <if test="funExample != null">
        fun_example,
      </if>
      <if test="funDesc != null">
        fun_desc,
      </if>
      <if test="funType != null">
        fun_type,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="funName != null">
        #{funName,jdbcType=VARCHAR},
      </if>
      <if test="fun != null">
        #{fun,jdbcType=VARCHAR},
      </if>
      <if test="funExample != null">
        #{funExample,jdbcType=VARCHAR},
      </if>
      <if test="funDesc != null">
        #{funDesc,jdbcType=VARCHAR},
      </if>
      <if test="funType != null">
        #{funType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.TemplateFormVariableFunExample" resultType="java.lang.Long">
    select count(*) from template_form_variable_fun
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update template_form_variable_fun
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.funName != null">
        fun_name = #{record.funName,jdbcType=VARCHAR},
      </if>
      <if test="record.fun != null">
        fun = #{record.fun,jdbcType=VARCHAR},
      </if>
      <if test="record.funExample != null">
        fun_example = #{record.funExample,jdbcType=VARCHAR},
      </if>
      <if test="record.funDesc != null">
        fun_desc = #{record.funDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.funType != null">
        fun_type = #{record.funType,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update template_form_variable_fun
    set id = #{record.id,jdbcType=INTEGER},
      fun_name = #{record.funName,jdbcType=VARCHAR},
      fun = #{record.fun,jdbcType=VARCHAR},
      fun_example = #{record.funExample,jdbcType=VARCHAR},
      fun_desc = #{record.funDesc,jdbcType=VARCHAR},
      fun_type = #{record.funType,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.TemplateFormVariableFun">
    update template_form_variable_fun
    <set>
      <if test="funName != null">
        fun_name = #{funName,jdbcType=VARCHAR},
      </if>
      <if test="fun != null">
        fun = #{fun,jdbcType=VARCHAR},
      </if>
      <if test="funExample != null">
        fun_example = #{funExample,jdbcType=VARCHAR},
      </if>
      <if test="funDesc != null">
        fun_desc = #{funDesc,jdbcType=VARCHAR},
      </if>
      <if test="funType != null">
        fun_type = #{funType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.TemplateFormVariableFun">
    update template_form_variable_fun
    set fun_name = #{funName,jdbcType=VARCHAR},
      fun = #{fun,jdbcType=VARCHAR},
      fun_example = #{funExample,jdbcType=VARCHAR},
      fun_desc = #{funDesc,jdbcType=VARCHAR},
      fun_type = #{funType,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>