<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectAnnouncementMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectAnnouncement">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="file_ids" jdbcType="VARCHAR" property="fileIds" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="system_type" jdbcType="VARCHAR" property="systemType" />
    <result column="project_org_id" jdbcType="BIGINT" property="projectOrgId" />
    <result column="project_org_name" jdbcType="VARCHAR" property="projectOrgName" />
    <result column="seal_flag" jdbcType="BIT" property="sealFlag" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, title, content, file_ids, status, type, system_type, project_org_id, 
    project_org_name, seal_flag, create_user, create_time, update_user, update_time, 
    tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectAnnouncementExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_announcement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_announcement
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_announcement
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectAnnouncementExample">
    delete from project_announcement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectAnnouncement">
    insert into project_announcement (id, project_id, title, 
      content, file_ids, status, 
      type, system_type, project_org_id, 
      project_org_name, seal_flag, create_user, 
      create_time, update_user, update_time, 
      tenant_id, platform_id)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{title,jdbcType=VARCHAR}, 
      #{content,jdbcType=VARCHAR}, #{fileIds,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{type,jdbcType=VARCHAR}, #{systemType,jdbcType=VARCHAR}, #{projectOrgId,jdbcType=BIGINT}, 
      #{projectOrgName,jdbcType=VARCHAR}, #{sealFlag,jdbcType=BIT}, #{createUser,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectAnnouncement">
    insert into project_announcement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="fileIds != null">
        file_ids,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="systemType != null">
        system_type,
      </if>
      <if test="projectOrgId != null">
        project_org_id,
      </if>
      <if test="projectOrgName != null">
        project_org_name,
      </if>
      <if test="sealFlag != null">
        seal_flag,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="fileIds != null">
        #{fileIds,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="systemType != null">
        #{systemType,jdbcType=VARCHAR},
      </if>
      <if test="projectOrgId != null">
        #{projectOrgId,jdbcType=BIGINT},
      </if>
      <if test="projectOrgName != null">
        #{projectOrgName,jdbcType=VARCHAR},
      </if>
      <if test="sealFlag != null">
        #{sealFlag,jdbcType=BIT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectAnnouncementExample" resultType="java.lang.Long">
    select count(*) from project_announcement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_announcement
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.fileIds != null">
        file_ids = #{record.fileIds,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.systemType != null">
        system_type = #{record.systemType,jdbcType=VARCHAR},
      </if>
      <if test="record.projectOrgId != null">
        project_org_id = #{record.projectOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.projectOrgName != null">
        project_org_name = #{record.projectOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.sealFlag != null">
        seal_flag = #{record.sealFlag,jdbcType=BIT},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_announcement
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      title = #{record.title,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=VARCHAR},
      file_ids = #{record.fileIds,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      system_type = #{record.systemType,jdbcType=VARCHAR},
      project_org_id = #{record.projectOrgId,jdbcType=BIGINT},
      project_org_name = #{record.projectOrgName,jdbcType=VARCHAR},
      seal_flag = #{record.sealFlag,jdbcType=BIT},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectAnnouncement">
    update project_announcement
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="fileIds != null">
        file_ids = #{fileIds,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="systemType != null">
        system_type = #{systemType,jdbcType=VARCHAR},
      </if>
      <if test="projectOrgId != null">
        project_org_id = #{projectOrgId,jdbcType=BIGINT},
      </if>
      <if test="projectOrgName != null">
        project_org_name = #{projectOrgName,jdbcType=VARCHAR},
      </if>
      <if test="sealFlag != null">
        seal_flag = #{sealFlag,jdbcType=BIT},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectAnnouncement">
    update project_announcement
    set project_id = #{projectId,jdbcType=BIGINT},
      title = #{title,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      file_ids = #{fileIds,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      system_type = #{systemType,jdbcType=VARCHAR},
      project_org_id = #{projectOrgId,jdbcType=BIGINT},
      project_org_name = #{projectOrgName,jdbcType=VARCHAR},
      seal_flag = #{sealFlag,jdbcType=BIT},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

    <select id="selectProjectAnnouncementList" parameterType="com.haoys.user.model.ProjectAnnouncement" resultType="com.haoys.user.domain.vo.project.ProjectAnnouncementVo">
        select
        project_announcement.id,
        project_announcement.title,
        project_announcement.content,
        project_announcement.file_ids fileIds,
        project_announcement.status,
        project_announcement.type,
        project_announcement.system_type,
        project_announcement.project_org_id projectOrgId,
        system_org_info.org_name projectOrgName,
        project_announcement.project_id projectId,
        project_announcement.update_user updateUser,
        project_announcement.update_time updateTime,
        system_user_info.username userName
        from project_announcement
        LEFT JOIN project_org_info on project_announcement.project_org_id = project_org_info.id
        LEFT JOIN system_org_info on system_org_info.org_id = project_org_info.org_id
        LEFT JOIN system_user_info on system_user_info.id = project_announcement.update_user

        <where>
            AND project_announcement.project_id = #{projectId}
            <if test="status != null and status != ''">
                AND project_announcement.status = #{status}
            </if>
            <if test="type != null and type != ''">
                AND project_announcement.type = #{type}
            </if>
            <if test="systemType != null and systemType != ''">
                AND project_announcement.system_type = #{systemType}
            </if>
            <if test="projectOrgId != null and projectOrgId != ''">
                AND (project_announcement.project_org_id = #{projectOrgId} or project_announcement.project_org_id = '-1')
            </if>
            <if test="projectOrgName != null and projectOrgName != ''">
                AND system_org_info.org_name like concat('%', #{projectOrgName}, '%')
            </if>

        </where>
        order by project_announcement.create_time desc
    </select>
</mapper>