<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectPrescriptionMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectPrescription">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sample_id" jdbcType="BIGINT" property="sampleId" />
    <result column="patient_name" jdbcType="VARCHAR" property="patientName" />
    <result column="birthday" jdbcType="DATE" property="birthday" />
    <result column="age" jdbcType="INTEGER" property="age" />
    <result column="diagnostic_desc" jdbcType="VARCHAR" property="diagnosticDesc" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="cotent_ext" jdbcType="VARCHAR" property="cotentExt" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, sample_id, patient_name, birthday, age, diagnostic_desc, content, cotent_ext, 
    status, create_user_id, create_time, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectPrescriptionExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_prescription
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_prescription
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_prescription
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectPrescriptionExample">
    delete from project_prescription
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectPrescription">
    insert into project_prescription (id, sample_id, patient_name, 
      birthday, age, diagnostic_desc, 
      content, cotent_ext, status, 
      create_user_id, create_time, platform_id
      )
    values (#{id,jdbcType=BIGINT}, #{sampleId,jdbcType=BIGINT}, #{patientName,jdbcType=VARCHAR}, 
      #{birthday,jdbcType=DATE}, #{age,jdbcType=INTEGER}, #{diagnosticDesc,jdbcType=VARCHAR}, 
      #{content,jdbcType=VARCHAR}, #{cotentExt,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{createUserId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{platformId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectPrescription">
    insert into project_prescription
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sampleId != null">
        sample_id,
      </if>
      <if test="patientName != null">
        patient_name,
      </if>
      <if test="birthday != null">
        birthday,
      </if>
      <if test="age != null">
        age,
      </if>
      <if test="diagnosticDesc != null">
        diagnostic_desc,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="cotentExt != null">
        cotent_ext,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sampleId != null">
        #{sampleId,jdbcType=BIGINT},
      </if>
      <if test="patientName != null">
        #{patientName,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=DATE},
      </if>
      <if test="age != null">
        #{age,jdbcType=INTEGER},
      </if>
      <if test="diagnosticDesc != null">
        #{diagnosticDesc,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="cotentExt != null">
        #{cotentExt,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectPrescriptionExample" resultType="java.lang.Long">
    select count(*) from project_prescription
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_prescription
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.sampleId != null">
        sample_id = #{record.sampleId,jdbcType=BIGINT},
      </if>
      <if test="record.patientName != null">
        patient_name = #{record.patientName,jdbcType=VARCHAR},
      </if>
      <if test="record.birthday != null">
        birthday = #{record.birthday,jdbcType=DATE},
      </if>
      <if test="record.age != null">
        age = #{record.age,jdbcType=INTEGER},
      </if>
      <if test="record.diagnosticDesc != null">
        diagnostic_desc = #{record.diagnosticDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.cotentExt != null">
        cotent_ext = #{record.cotentExt,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_prescription
    set id = #{record.id,jdbcType=BIGINT},
      sample_id = #{record.sampleId,jdbcType=BIGINT},
      patient_name = #{record.patientName,jdbcType=VARCHAR},
      birthday = #{record.birthday,jdbcType=DATE},
      age = #{record.age,jdbcType=INTEGER},
      diagnostic_desc = #{record.diagnosticDesc,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=VARCHAR},
      cotent_ext = #{record.cotentExt,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectPrescription">
    update project_prescription
    <set>
      <if test="sampleId != null">
        sample_id = #{sampleId,jdbcType=BIGINT},
      </if>
      <if test="patientName != null">
        patient_name = #{patientName,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        birthday = #{birthday,jdbcType=DATE},
      </if>
      <if test="age != null">
        age = #{age,jdbcType=INTEGER},
      </if>
      <if test="diagnosticDesc != null">
        diagnostic_desc = #{diagnosticDesc,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="cotentExt != null">
        cotent_ext = #{cotentExt,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectPrescription">
    update project_prescription
    set sample_id = #{sampleId,jdbcType=BIGINT},
      patient_name = #{patientName,jdbcType=VARCHAR},
      birthday = #{birthday,jdbcType=DATE},
      age = #{age,jdbcType=INTEGER},
      diagnostic_desc = #{diagnosticDesc,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      cotent_ext = #{cotentExt,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>