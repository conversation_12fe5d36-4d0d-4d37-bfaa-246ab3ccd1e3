<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectFormAuditMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectFormAudit">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="plan_id" jdbcType="BIGINT" property="planId" />
    <result column="visit_id" jdbcType="BIGINT" property="visitId" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="testee_id" jdbcType="BIGINT" property="testeeId" />
    <result column="audit_user_id" jdbcType="BIGINT" property="auditUserId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="audit_status" jdbcType="VARCHAR" property="auditStatus" />
    <result column="submit_user_id" jdbcType="BIGINT" property="submitUserId" />
    <result column="submit_time" jdbcType="TIMESTAMP" property="submitTime" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, plan_id, visit_id, form_id, testee_id, audit_user_id, status, audit_status,
    submit_user_id, submit_time, audit_time, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectFormAuditExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_form_audit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from project_form_audit
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_form_audit
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectFormAuditExample">
    delete from project_form_audit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectFormAudit">
    insert into project_form_audit (id, project_id, plan_id,
      visit_id, form_id, testee_id,
      audit_user_id, status, audit_status,
      submit_user_id, submit_time, audit_time,
      tenant_id, platform_id)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{planId,jdbcType=BIGINT},
      #{visitId,jdbcType=BIGINT}, #{formId,jdbcType=BIGINT}, #{testeeId,jdbcType=BIGINT},
      #{auditUserId,jdbcType=BIGINT}, #{status,jdbcType=VARCHAR}, #{auditStatus,jdbcType=VARCHAR},
      #{submitUserId,jdbcType=BIGINT}, #{submitTime,jdbcType=TIMESTAMP}, #{auditTime,jdbcType=TIMESTAMP},
      #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectFormAudit">
    insert into project_form_audit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="planId != null">
        plan_id,
      </if>
      <if test="visitId != null">
        visit_id,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="testeeId != null">
        testee_id,
      </if>
      <if test="auditUserId != null">
        audit_user_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="submitUserId != null">
        submit_user_id,
      </if>
      <if test="submitTime != null">
        submit_time,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        #{visitId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="auditUserId != null">
        #{auditUserId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="submitUserId != null">
        #{submitUserId,jdbcType=BIGINT},
      </if>
      <if test="submitTime != null">
        #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectFormAuditExample" resultType="java.lang.Long">
    select count(*) from project_form_audit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_form_audit
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.planId != null">
        plan_id = #{record.planId,jdbcType=BIGINT},
      </if>
      <if test="record.visitId != null">
        visit_id = #{record.visitId,jdbcType=BIGINT},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=BIGINT},
      </if>
      <if test="record.testeeId != null">
        testee_id = #{record.testeeId,jdbcType=BIGINT},
      </if>
      <if test="record.auditUserId != null">
        audit_user_id = #{record.auditUserId,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.submitUserId != null">
        submit_user_id = #{record.submitUserId,jdbcType=BIGINT},
      </if>
      <if test="record.submitTime != null">
        submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.auditTime != null">
        audit_time = #{record.auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_form_audit
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      plan_id = #{record.planId,jdbcType=BIGINT},
      visit_id = #{record.visitId,jdbcType=BIGINT},
      form_id = #{record.formId,jdbcType=BIGINT},
      testee_id = #{record.testeeId,jdbcType=BIGINT},
      audit_user_id = #{record.auditUserId,jdbcType=BIGINT},
      status = #{record.status,jdbcType=VARCHAR},
      audit_status = #{record.auditStatus,jdbcType=VARCHAR},
      submit_user_id = #{record.submitUserId,jdbcType=BIGINT},
      submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
      audit_time = #{record.auditTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectFormAudit">
    update project_form_audit
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        plan_id = #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        visit_id = #{visitId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        testee_id = #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="auditUserId != null">
        audit_user_id = #{auditUserId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="submitUserId != null">
        submit_user_id = #{submitUserId,jdbcType=BIGINT},
      </if>
      <if test="submitTime != null">
        submit_time = #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectFormAudit">
    update project_form_audit
    set project_id = #{projectId,jdbcType=BIGINT},
      plan_id = #{planId,jdbcType=BIGINT},
      visit_id = #{visitId,jdbcType=BIGINT},
      form_id = #{formId,jdbcType=BIGINT},
      testee_id = #{testeeId,jdbcType=BIGINT},
      audit_user_id = #{auditUserId,jdbcType=BIGINT},
      status = #{status,jdbcType=VARCHAR},
      audit_status = #{auditStatus,jdbcType=VARCHAR},
      submit_user_id = #{submitUserId,jdbcType=BIGINT},
      submit_time = #{submitTime,jdbcType=TIMESTAMP},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
