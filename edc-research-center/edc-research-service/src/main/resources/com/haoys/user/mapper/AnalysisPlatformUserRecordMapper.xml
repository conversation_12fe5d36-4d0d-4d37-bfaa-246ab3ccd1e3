<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.AnalysisPlatformUserRecordMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.AnalysisPlatformUserRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="deploy_env" jdbcType="VARCHAR" property="deployEnv" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, user_name, mobile, deploy_env, create_user_id, create_time, tenant_id, 
    platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.AnalysisPlatformUserRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from analysis_platform_user_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from analysis_platform_user_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from analysis_platform_user_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.AnalysisPlatformUserRecordExample">
    delete from analysis_platform_user_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.AnalysisPlatformUserRecord">
    insert into analysis_platform_user_record (id, user_id, user_name, 
      mobile, deploy_env, create_user_id, 
      create_time, tenant_id, platform_id
      )
    values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{userName,jdbcType=VARCHAR}, 
      #{mobile,jdbcType=VARCHAR}, #{deployEnv,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.AnalysisPlatformUserRecord">
    insert into analysis_platform_user_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="deployEnv != null">
        deploy_env,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="deployEnv != null">
        #{deployEnv,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.AnalysisPlatformUserRecordExample" resultType="java.lang.Long">
    select count(*) from analysis_platform_user_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update analysis_platform_user_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.userName != null">
        user_name = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.mobile != null">
        mobile = #{record.mobile,jdbcType=VARCHAR},
      </if>
      <if test="record.deployEnv != null">
        deploy_env = #{record.deployEnv,jdbcType=VARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update analysis_platform_user_record
    set id = #{record.id,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      user_name = #{record.userName,jdbcType=VARCHAR},
      mobile = #{record.mobile,jdbcType=VARCHAR},
      deploy_env = #{record.deployEnv,jdbcType=VARCHAR},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.AnalysisPlatformUserRecord">
    update analysis_platform_user_record
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="deployEnv != null">
        deploy_env = #{deployEnv,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.AnalysisPlatformUserRecord">
    update analysis_platform_user_record
    set user_id = #{userId,jdbcType=BIGINT},
      user_name = #{userName,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      deploy_env = #{deployEnv,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getPlatformUserAccountByUserName" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from analysis_platform_user_record
    where user_name = #{username} and platform_id = #{systemPlatformId}
  </select>
</mapper>