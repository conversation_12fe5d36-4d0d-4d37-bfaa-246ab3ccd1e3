<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectOrgRoleMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectOrgRole">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="project_org_id" jdbcType="BIGINT" property="projectOrgId" />
    <result column="resource_role_id" jdbcType="BIGINT" property="resourceRoleId" />
    <result column="role_name" jdbcType="VARCHAR" property="roleName" />
    <result column="ename" jdbcType="VARCHAR" property="ename" />
    <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, org_id, project_org_id, resource_role_id, role_name, ename, org_code, 
    description, status, create_user_id, create_time, update_user_id, update_time, tenant_id, 
    platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectOrgRoleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_org_role
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_org_role
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_org_role
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectOrgRoleExample">
    delete from project_org_role
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectOrgRole">
    insert into project_org_role (id, project_id, org_id, 
      project_org_id, resource_role_id, role_name, 
      ename, org_code, description, 
      status, create_user_id, create_time, 
      update_user_id, update_time, tenant_id, 
      platform_id)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{orgId,jdbcType=BIGINT}, 
      #{projectOrgId,jdbcType=BIGINT}, #{resourceRoleId,jdbcType=BIGINT}, #{roleName,jdbcType=VARCHAR}, 
      #{ename,jdbcType=VARCHAR}, #{orgCode,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateUserId,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{tenantId,jdbcType=VARCHAR}, 
      #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectOrgRole">
    insert into project_org_role
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="projectOrgId != null">
        project_org_id,
      </if>
      <if test="resourceRoleId != null">
        resource_role_id,
      </if>
      <if test="roleName != null">
        role_name,
      </if>
      <if test="ename != null">
        ename,
      </if>
      <if test="orgCode != null">
        org_code,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="projectOrgId != null">
        #{projectOrgId,jdbcType=BIGINT},
      </if>
      <if test="resourceRoleId != null">
        #{resourceRoleId,jdbcType=BIGINT},
      </if>
      <if test="roleName != null">
        #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="ename != null">
        #{ename,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectOrgRoleExample" resultType="java.lang.Long">
    select count(*) from project_org_role
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_org_role
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.projectOrgId != null">
        project_org_id = #{record.projectOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.resourceRoleId != null">
        resource_role_id = #{record.resourceRoleId,jdbcType=BIGINT},
      </if>
      <if test="record.roleName != null">
        role_name = #{record.roleName,jdbcType=VARCHAR},
      </if>
      <if test="record.ename != null">
        ename = #{record.ename,jdbcType=VARCHAR},
      </if>
      <if test="record.orgCode != null">
        org_code = #{record.orgCode,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_org_role
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      org_id = #{record.orgId,jdbcType=BIGINT},
      project_org_id = #{record.projectOrgId,jdbcType=BIGINT},
      resource_role_id = #{record.resourceRoleId,jdbcType=BIGINT},
      role_name = #{record.roleName,jdbcType=VARCHAR},
      ename = #{record.ename,jdbcType=VARCHAR},
      org_code = #{record.orgCode,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectOrgRole">
    update project_org_role
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="projectOrgId != null">
        project_org_id = #{projectOrgId,jdbcType=BIGINT},
      </if>
      <if test="resourceRoleId != null">
        resource_role_id = #{resourceRoleId,jdbcType=BIGINT},
      </if>
      <if test="roleName != null">
        role_name = #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="ename != null">
        ename = #{ename,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        org_code = #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectOrgRole">
    update project_org_role
    set project_id = #{projectId,jdbcType=BIGINT},
      org_id = #{orgId,jdbcType=BIGINT},
      project_org_id = #{projectOrgId,jdbcType=BIGINT},
      resource_role_id = #{resourceRoleId,jdbcType=BIGINT},
      role_name = #{roleName,jdbcType=VARCHAR},
      ename = #{ename,jdbcType=VARCHAR},
      org_code = #{orgCode,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="selectProjectOrgRoleList" resultType="com.haoys.user.domain.entity.ProjectRoleQuery">
    select
    <include refid="Base_Column_List" />
    from project_org_role
    where 1=1
    <if test="id != null and id != ''">
      and id=#{id}
    </if>
    <if test="projectId != null and projectId != ''">
      and project_id = #{projectId}
    </if>
    <if test="orgId != null and orgId != ''">
      and org_Id = #{orgId}
    </if>
    <if test="name != null and name != ''">
      and name like concat('%', #{name}, '%')
    </if>
    <if test="enname != null and enname != ''">
      and enname like concat('%', #{enname}, '%')
    </if>
    <if test="status != null and status != ''">
      and status=#{status}
    </if>
    <if test="status == null or status == ''">
      and status in (0,1)
    </if>
    order by create_time desc
  </select>


  <select id="getProjectRoleInfoByRoleNameOrEnName" resultType="com.haoys.user.model.ProjectOrgRole">
    select
    <include refid="Base_Column_List" />
    from project_org_role where project_id = #{projectId} and project_org_id = #{projectOrgId} and (role_name=#{roleName} or ename=#{enname}) limit 1
  </select>


  <insert id="batchInsert">
    insert into project_org_role(id,project_id,org_id, role_name, ename,
    status, create_user_id, create_time,
    update_user_id, update_time, tenant_id,
    platform_id) values
    <foreach item="item" index="index" collection="list" separator=",">
      (#{item.id},#{item.projectId},#{item.orgId},#{item.roleName},
      #{item.ename},#{item.status},#{item.createUserId},
      #{item.createTime},#{item.updateUserId}, #{item.updateTime},
      #{item.tenantId},#{item.platformId})
    </foreach>
  </insert>


  <delete id="deleteProjectOrgRoleById">
    delete from project_org_role  where id=#{roleId}
  </delete>

  <!--根据项目id、项目研究中心id、角色名称查询角色基本信息-->
  <select id="getProjectOrgRoleByProjectOrgIdAndRoleName" resultMap="BaseResultMap">
    select * from project_org_role where project_id = #{projectId} and project_org_id = #{projectOrgId} and role_name = #{projectRoleName}
  </select>

  <select id="getProjectOrgRoleByProjectOrgCode" resultMap="BaseResultMap">
    select * from project_org_role where project_id = #{projectId} and org_code = #{projectOrgCode}
  </select>


  <select id="getProjectOrgRoleListByUserId" resultType="com.haoys.user.domain.vo.auth.ProjectRoleVo">
    SELECT
        project_org_role.id roleId, project_org_role.role_name orgRoleName,
        project_org_role.org_code orgRoleCode, project_org_role.ename enname
    FROM project_org_role INNER JOIN project_org_user_role ON project_org_user_role.role_id = project_org_role.id
    WHERE project_org_role.project_id = #{projectId}
    AND project_org_role.org_id = #{orgId}
    AND project_org_user_role.user_id = #{userId}
  </select>

  <select id="getProjectOrgRoleByOrgRoleCode" resultMap="BaseResultMap">
    select * from project_org_role where project_id = #{projectId} and project_org_id = #{projectOrgId} and ename = #{code}
  </select>


</mapper>