<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectUserInfoMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectUserInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="pa_role" jdbcType="BIT" property="paRole" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="active_status" jdbcType="BIT" property="activeStatus" />
    <result column="reject_status" jdbcType="BIT" property="rejectStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, user_id, pa_role, status, active_status, reject_status, create_time,
    tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectUserInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_user_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from project_user_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_user_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectUserInfoExample">
    delete from project_user_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectUserInfo">
    insert into project_user_info (id, project_id, user_id,
      pa_role, status, active_status,
      reject_status, create_time, tenant_id,
      platform_id)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT},
      #{paRole,jdbcType=BIT}, #{status,jdbcType=VARCHAR}, #{activeStatus,jdbcType=BIT},
      #{rejectStatus,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}, #{tenantId,jdbcType=VARCHAR},
      #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectUserInfo">
    insert into project_user_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="paRole != null">
        pa_role,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="activeStatus != null">
        active_status,
      </if>
      <if test="rejectStatus != null">
        reject_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="paRole != null">
        #{paRole,jdbcType=BIT},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="activeStatus != null">
        #{activeStatus,jdbcType=BIT},
      </if>
      <if test="rejectStatus != null">
        #{rejectStatus,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectUserInfoExample" resultType="java.lang.Long">
    select count(*) from project_user_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_user_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.paRole != null">
        pa_role = #{record.paRole,jdbcType=BIT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.activeStatus != null">
        active_status = #{record.activeStatus,jdbcType=BIT},
      </if>
      <if test="record.rejectStatus != null">
        reject_status = #{record.rejectStatus,jdbcType=BIT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_user_info
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      pa_role = #{record.paRole,jdbcType=BIT},
      status = #{record.status,jdbcType=VARCHAR},
      active_status = #{record.activeStatus,jdbcType=BIT},
      reject_status = #{record.rejectStatus,jdbcType=BIT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectUserInfo">
    update project_user_info
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="paRole != null">
        pa_role = #{paRole,jdbcType=BIT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="activeStatus != null">
        active_status = #{activeStatus,jdbcType=BIT},
      </if>
      <if test="rejectStatus != null">
        reject_status = #{rejectStatus,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectUserInfo">
    update project_user_info
    set project_id = #{projectId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      pa_role = #{paRole,jdbcType=BIT},
      status = #{status,jdbcType=VARCHAR},
      active_status = #{activeStatus,jdbcType=BIT},
      reject_status = #{rejectStatus,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!--查询项目用户列表-->
  <select id="selectProjectUserListForPage" parameterType="com.haoys.user.domain.dto.ProjectUserParam" resultType="com.haoys.user.domain.vo.project.ProjectUserVo">
    select temp.* from (
    <if test="lockStatus == null or lockStatus==0 ">
    ( SELECT
        system_user_info.id,
        system_user_info.username,
        system_user_info.real_name realName,
        system_user_info.mobile,
        system_user_info.email,
        system_user_info.status,
        system_user_info.active_status activeStatus,
        system_user_info.user_type registerType,
        system_user_info.department departmentId,
        system_user_info.positional positionalId,
        NULL as  projectOrgIds,
        NULL as  projectOrgRoleIds,
        system_user_info.create_time createtime,
        GROUP_CONCAT(project_role.name) roleName,
        GROUP_CONCAT(project_role.enname) roleCode,
        if(project.create_user = system_user_info.id, -1, 0) AS sortField,
        0 as lockStatus
      FROM system_user_info
      INNER JOIN project_user_role ON project_user_role.user_id = system_user_info.id
      INNER JOIN project_role  ON project_role.id = project_user_role.role_id
      INNER JOIN project ON project.id = project_role.project_id <!--AND project.create_user = system_user_info.id-->
      WHERE  1 = 1 and system_user_info.seal_flag = 0 and project_user_role.project_id = #{projectId}
      <if test="accountName !=null and accountName != ''">
        and (
        system_user_info.username like concat('', #{accountName}, '%')
        or system_user_info.real_name like concat('%', #{accountName}, '%')
        or system_user_info.mobile like concat('%', #{aseMobile}, '%')
        or system_user_info.email like concat('%', #{accountName}, '%')
        )
      </if>
      <if test="status !=null and status != '' ">
        and system_user_info.status = #{status}
      </if>
      <if test="activeStatus !=null and activeStatus != '' ">
        and system_user_info.active_status = #{activeStatus}
      </if>
        AND (system_user_info.default_admin != TRUE OR system_user_info.default_admin IS NULL)
        GROUP BY system_user_info.id
    )
    UNION ALL
    </if>
    (SELECT
      system_user_info.id,
      system_user_info.username,
      system_user_info.real_name realName,
      system_user_info.mobile,
      system_user_info.email,
      project_user_info.status,
      project_user_info.active_status activeStatus,
      system_user_info.user_type registerType,
      system_user_info.department departmentId,
      system_user_info.positional positionalId,
      GROUP_CONCAT(project_user_org.org_id ORDER BY project_user_org.create_time DESC) projectOrgIds,
      GROUP_CONCAT(project_org_role.id ORDER BY project_org_role.create_time DESC) projectOrgRoleIds,
      project_user_org.create_time createtime,
      GROUP_CONCAT(project_org_role.role_name) as roleName,
      GROUP_CONCAT(project_org_role.ename) as roleCode,
      1 as sortField,
      system_tenant_user.lock_status lockStatus
    FROM system_user_info
    INNER JOIN project_user_info ON project_user_info.user_id = system_user_info.id
    inner join system_tenant_user on system_tenant_user.user_id = system_user_info.id
    <!--AND (project_user_info.pa_role != true or project_user_info.pa_role is null)-->
    LEFT JOIN project_user_org ON project_user_org.user_id = system_user_info.id AND project_user_org.project_id = project_user_info.project_id
    LEFT JOIN system_org_info ON system_org_info.org_id = project_user_org.org_id
    LEFT JOIN project_org_user_role ON project_org_user_role.user_id = project_user_org.user_id
    LEFT JOIN project_org_role ON project_org_role.id = project_org_user_role.role_id and project_org_role.org_code = project_user_org.org_code
    WHERE project_user_info.project_id = #{projectId} and system_user_info.seal_flag = 0
    and system_tenant_user.platform_id = 1 and project_user_info.user_id != #{createUserId}
    and (project_user_info.pa_role is null  or project_user_info.pa_role = false)
    <if test="accountName !=null and accountName != ''">
      and (
        system_user_info.username like concat('', #{accountName}, '%')
        or system_user_info.real_name like concat('%', #{accountName}, '%')
        or system_user_info.mobile like concat('%', #{aseMobile}, '%')
        or system_user_info.email like concat('%', #{accountName}, '%')
      )
    </if>
    <if test="projectOrgId !=null and projectOrgId != ''">
      AND (project_org_role.project_org_id = #{projectOrgId} or system_tenant_user.owner_total_auth=true)
    </if>
    <if test="lockStatus!=null">
      AND system_tenant_user.lock_status = #{lockStatus}
    </if>
    <if test="status !=null and status != '' ">
      and system_user_info.status = #{status}
    </if>
    <if test="activeStatus !=null and activeStatus != '' ">
      and system_user_info.active_status = #{activeStatus}
    </if>
    AND ( system_user_info.default_admin != TRUE OR system_user_info.default_admin IS NULL )
    GROUP BY project_user_org.project_id, system_user_info.id
    )) temp ORDER BY sortField asc, activeStatus ASC,STATUS DESC,createTime DESC
  </select>

  <!--查询项目用户基本信息-->
  <select id="getProjectUserInfo" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from project_user_info
    where project_id = #{projectId} and user_id = #{systemUserId}
  </select>

  <!--获取当前登录人受邀请的信息-->
  <select id="selectProjectUserListAndUserId" resultType="com.haoys.user.domain.vo.project.ProjectUserVo">
    SELECT
      system_user_info.id,
      system_user_info.username,
      system_user_info.real_name realName,
      system_user_info.mobile,
      system_user_info.email,
      project_user_info.status,
      project_user_info.project_id as projectId,
      project_user_info.reject_status as rejectStatus,
      project_user_info.active_status activeStatus,
      system_user_info.user_type registerType,
      project_user_info.create_time createtime,
      project.name as projectName
    FROM system_user_info
    INNER JOIN project_user_info ON project_user_info.user_id = system_user_info.id
    LEFT JOIN project_user_org ON project_user_org.user_id = system_user_info.id AND project_user_org.project_id = project_user_info.project_id
    LEFT JOIN project ON project.id = project_user_info.project_id
    WHERE project_user_info.status = '0' and project_user_info.active_status = '0' and project_user_info.reject_status=0 and project_user_info.user_id = #{userId}
    GROUP BY project_user_org.project_id, project_user_org.user_id
    ORDER by project_user_info.create_time DESC

  </select>

  <delete id="deleteProjectUserInfo">
    delete from project_user_info where project_id = #{projectId} and user_id = #{userId}
  </delete>

  <select id="getProjectUserListByUserId" resultMap="BaseResultMap">
    select
      project_user_info.*
    from project_user_info
    inner join project on project.id = project_user_info.project_id
    where project_user_info.user_id = #{systemUserId}
    and project.status = '0'
  </select>

</mapper>
