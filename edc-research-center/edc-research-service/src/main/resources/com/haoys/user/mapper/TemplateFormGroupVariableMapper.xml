<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.TemplateFormGroupVariableMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.TemplateFormGroupVariable">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="plan_id" jdbcType="BIGINT" property="planId" />
    <result column="visit_id" jdbcType="BIGINT" property="visitId" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="resource_group_id" jdbcType="BIGINT" property="resourceGroupId" />
    <result column="resource_variable_id" jdbcType="BIGINT" property="resourceVariableId" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="testee_id" jdbcType="BIGINT" property="testeeId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, plan_id, visit_id, form_id, resource_group_id, resource_variable_id, 
    group_name, group_id, testee_id, sort, status, create_time, create_user_id, update_time, 
    update_user_id, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.TemplateFormGroupVariableExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from template_form_group_variable
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from template_form_group_variable
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from template_form_group_variable
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.TemplateFormGroupVariableExample">
    delete from template_form_group_variable
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.TemplateFormGroupVariable">
    insert into template_form_group_variable (id, project_id, plan_id, 
      visit_id, form_id, resource_group_id, 
      resource_variable_id, group_name, group_id, 
      testee_id, sort, status, 
      create_time, create_user_id, update_time, 
      update_user_id, tenant_id, platform_id
      )
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{planId,jdbcType=BIGINT}, 
      #{visitId,jdbcType=BIGINT}, #{formId,jdbcType=BIGINT}, #{resourceGroupId,jdbcType=BIGINT}, 
      #{resourceVariableId,jdbcType=BIGINT}, #{groupName,jdbcType=VARCHAR}, #{groupId,jdbcType=BIGINT}, 
      #{testeeId,jdbcType=BIGINT}, #{sort,jdbcType=INTEGER}, #{status,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateUserId,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.TemplateFormGroupVariable">
    insert into template_form_group_variable
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="planId != null">
        plan_id,
      </if>
      <if test="visitId != null">
        visit_id,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="resourceGroupId != null">
        resource_group_id,
      </if>
      <if test="resourceVariableId != null">
        resource_variable_id,
      </if>
      <if test="groupName != null">
        group_name,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="testeeId != null">
        testee_id,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        #{visitId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="resourceGroupId != null">
        #{resourceGroupId,jdbcType=BIGINT},
      </if>
      <if test="resourceVariableId != null">
        #{resourceVariableId,jdbcType=BIGINT},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.TemplateFormGroupVariableExample" resultType="java.lang.Long">
    select count(*) from template_form_group_variable
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update template_form_group_variable
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.planId != null">
        plan_id = #{record.planId,jdbcType=BIGINT},
      </if>
      <if test="record.visitId != null">
        visit_id = #{record.visitId,jdbcType=BIGINT},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=BIGINT},
      </if>
      <if test="record.resourceGroupId != null">
        resource_group_id = #{record.resourceGroupId,jdbcType=BIGINT},
      </if>
      <if test="record.resourceVariableId != null">
        resource_variable_id = #{record.resourceVariableId,jdbcType=BIGINT},
      </if>
      <if test="record.groupName != null">
        group_name = #{record.groupName,jdbcType=VARCHAR},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=BIGINT},
      </if>
      <if test="record.testeeId != null">
        testee_id = #{record.testeeId,jdbcType=BIGINT},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update template_form_group_variable
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      plan_id = #{record.planId,jdbcType=BIGINT},
      visit_id = #{record.visitId,jdbcType=BIGINT},
      form_id = #{record.formId,jdbcType=BIGINT},
      resource_group_id = #{record.resourceGroupId,jdbcType=BIGINT},
      resource_variable_id = #{record.resourceVariableId,jdbcType=BIGINT},
      group_name = #{record.groupName,jdbcType=VARCHAR},
      group_id = #{record.groupId,jdbcType=BIGINT},
      testee_id = #{record.testeeId,jdbcType=BIGINT},
      sort = #{record.sort,jdbcType=INTEGER},
      status = #{record.status,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.TemplateFormGroupVariable">
    update template_form_group_variable
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        plan_id = #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        visit_id = #{visitId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=BIGINT},
      </if>
      <if test="resourceGroupId != null">
        resource_group_id = #{resourceGroupId,jdbcType=BIGINT},
      </if>
      <if test="resourceVariableId != null">
        resource_variable_id = #{resourceVariableId,jdbcType=BIGINT},
      </if>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        testee_id = #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.TemplateFormGroupVariable">
    update template_form_group_variable
    set project_id = #{projectId,jdbcType=BIGINT},
      plan_id = #{planId,jdbcType=BIGINT},
      visit_id = #{visitId,jdbcType=BIGINT},
      form_id = #{formId,jdbcType=BIGINT},
      resource_group_id = #{resourceGroupId,jdbcType=BIGINT},
      resource_variable_id = #{resourceVariableId,jdbcType=BIGINT},
      group_name = #{groupName,jdbcType=VARCHAR},
      group_id = #{groupId,jdbcType=BIGINT},
      testee_id = #{testeeId,jdbcType=BIGINT},
      sort = #{sort,jdbcType=INTEGER},
      status = #{status,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <delete id="deleteTemplateFormGroupVariableByGroupId">
      update template_form_group_variable set status=#{validStatus} ,update_time=sysdate(),update_user_id=#{userId} where group_id=#{groupId}
  </delete>

  <select id="getTemplateGroupInfoByBaseVariableId" resultMap="BaseResultMap">
    SELECT
        *
    FROM template_form_group_variable
    WHERE project_id = #{projectId}
    AND plan_id = #{planId}
    AND visit_id = #{visitId} AND form_id = #{formId}
    AND resource_variable_id = #{resourceVariableId}
    AND resource_group_id = #{resourceGroupId}
    <if test="testeeGroupId != null and testeeGroupId !=''">
      AND group_id = #{testeeGroupId}
    </if>
    AND testee_id = #{testeeId}
    and status=0
  </select>

  <select id="getProjectTesteeFormGroupListByFormId" resultType="com.haoys.user.domain.vo.ecrf.TemplateFormGroupVariableVo">
    SELECT
      group_name groupName,group_id groupId, resource_group_id resourceGroupId
    FROM template_form_group_variable
    WHERE
      project_id = #{projectId} AND plan_id = #{planId} AND visit_id = #{visitId} AND form_id = #{formId}
      <if test="variableGroupTableId != null and variableGroupTableId !=''">
        AND template_form_group_variable.resource_variable_id = #{variableGroupTableId}
      </if>
    <if test="variableTableId != null and variableTableId !=''">
      AND template_form_group_variable.id = #{variableTableId}
    </if>
      AND testee_id = #{testeeId} AND status = '0'
    GROUP BY group_id ORDER BY create_time ASC
  </select>

</mapper>