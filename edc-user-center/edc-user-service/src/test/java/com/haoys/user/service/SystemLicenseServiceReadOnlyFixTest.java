package com.haoys.user.service;

import com.haoys.user.common.domain.SystemLicenseInfo;
import com.haoys.user.service.impl.SystemLicenseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 系统许可证服务只读事务修复测试
 * 
 * <p>测试目标：</p>
 * <ul>
 *   <li>验证validateSystemLicense方法不再抛出只读事务异常</li>
 *   <li>验证异步写操作能够正常执行</li>
 *   <li>验证许可证验证逻辑的正确性</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class SystemLicenseServiceReadOnlyFixTest {

    @Autowired
    private SystemLicenseService systemLicenseService;

    @Autowired
    private SystemLicenseServiceImpl systemLicenseServiceImpl;

    /**
     * 测试许可证验证方法不再抛出只读事务异常
     */
    @Test
    public void testValidateSystemLicenseNoReadOnlyException() {
        log.info("开始测试许可证验证方法...");
        
        try {
            // 调用验证方法，应该不再抛出只读事务异常
            boolean result = systemLicenseService.validateSystemLicense();
            
            log.info("许可证验证结果: {}", result);
            
            // 验证方法能够正常执行，不抛出异常
            assertDoesNotThrow(() -> {
                systemLicenseService.validateSystemLicense();
            }, "validateSystemLicense方法不应该抛出只读事务异常");
            
            log.info("✅ 许可证验证方法测试通过，未抛出只读事务异常");
            
        } catch (Exception e) {
            log.error("❌ 许可证验证方法测试失败", e);
            
            // 检查是否是只读事务异常
            String errorMessage = e.getMessage();
            if (errorMessage != null && errorMessage.contains("Connection is read-only")) {
                fail("仍然存在只读事务异常: " + errorMessage);
            } else {
                // 其他异常可能是正常的业务异常（如许可证不存在等）
                log.warn("出现非只读事务异常，可能是正常的业务异常: {}", errorMessage);
            }
        }
    }

    /**
     * 测试系统可用性检查
     */
    @Test
    public void testIsSystemAvailable() {
        log.info("开始测试系统可用性检查...");
        
        try {
            boolean isAvailable = systemLicenseService.isSystemAvailable();
            log.info("系统可用性: {}", isAvailable);
            
            // 验证方法能够正常执行
            assertDoesNotThrow(() -> {
                systemLicenseService.isSystemAvailable();
            }, "isSystemAvailable方法不应该抛出异常");
            
            log.info("✅ 系统可用性检查测试通过");
            
        } catch (Exception e) {
            log.error("❌ 系统可用性检查测试失败", e);
            
            // 检查是否是只读事务异常
            String errorMessage = e.getMessage();
            if (errorMessage != null && errorMessage.contains("Connection is read-only")) {
                fail("系统可用性检查仍然存在只读事务异常: " + errorMessage);
            }
        }
    }

    /**
     * 测试获取当前许可证
     */
    @Test
    public void testGetCurrentLicense() {
        log.info("开始测试获取当前许可证...");
        
        try {
            SystemLicenseInfo license = systemLicenseService.getCurrentLicense();
            log.info("当前许可证: {}", license != null ? license.getId() : "null");
            
            // 验证方法能够正常执行
            assertDoesNotThrow(() -> {
                systemLicenseService.getCurrentLicense();
            }, "getCurrentLicense方法不应该抛出异常");
            
            log.info("✅ 获取当前许可证测试通过");
            
        } catch (Exception e) {
            log.error("❌ 获取当前许可证测试失败", e);
            fail("获取当前许可证失败: " + e.getMessage());
        }
    }

    /**
     * 测试异步方法是否正确配置
     */
    @Test
    public void testAsyncMethodsConfiguration() {
        log.info("开始测试异步方法配置...");
        
        try {
            // 验证异步方法存在且可调用
            assertDoesNotThrow(() -> {
                systemLicenseServiceImpl.asyncUpdateLastValidationTime(1L);
            }, "asyncUpdateLastValidationTime方法应该能够正常调用");
            
            assertDoesNotThrow(() -> {
                systemLicenseServiceImpl.asyncUpdateValidationFailure(1L);
            }, "asyncUpdateValidationFailure方法应该能够正常调用");
            
            assertDoesNotThrow(() -> {
                systemLicenseServiceImpl.asyncMarkLicenseAsExpired(1L);
            }, "asyncMarkLicenseAsExpired方法应该能够正常调用");
            
            log.info("✅ 异步方法配置测试通过");
            
        } catch (Exception e) {
            log.error("❌ 异步方法配置测试失败", e);
            fail("异步方法配置测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试多次调用验证方法的稳定性
     */
    @Test
    public void testMultipleValidationCalls() {
        log.info("开始测试多次调用验证方法的稳定性...");
        
        int callCount = 5;
        int successCount = 0;
        
        for (int i = 0; i < callCount; i++) {
            try {
                boolean result = systemLicenseService.validateSystemLicense();
                log.debug("第{}次调用结果: {}", i + 1, result);
                successCount++;
                
                // 短暂等待，模拟实际使用场景
                Thread.sleep(100);
                
            } catch (Exception e) {
                log.error("第{}次调用失败", i + 1, e);
                
                // 检查是否是只读事务异常
                String errorMessage = e.getMessage();
                if (errorMessage != null && errorMessage.contains("Connection is read-only")) {
                    fail("第" + (i + 1) + "次调用仍然存在只读事务异常: " + errorMessage);
                }
            }
        }
        
        log.info("多次调用测试完成，成功次数: {}/{}", successCount, callCount);
        
        // 至少应该有一半的调用成功（考虑到可能的业务异常）
        assertTrue(successCount >= callCount / 2, 
                   "多次调用中成功次数过少，可能存在问题");
        
        log.info("✅ 多次调用验证方法稳定性测试通过");
    }

    /**
     * 测试事务隔离性
     */
    @Test
    @Transactional(readOnly = true)
    public void testTransactionIsolation() {
        log.info("开始测试事务隔离性...");
        
        try {
            // 在只读事务中调用验证方法
            boolean result = systemLicenseService.validateSystemLicense();
            log.info("在只读事务中调用验证方法结果: {}", result);
            
            // 验证即使在外部只读事务中，内部的异步写操作也能正常执行
            assertDoesNotThrow(() -> {
                systemLicenseService.validateSystemLicense();
            }, "在只读事务中调用validateSystemLicense应该不抛出异常");
            
            log.info("✅ 事务隔离性测试通过");
            
        } catch (Exception e) {
            log.error("❌ 事务隔离性测试失败", e);
            
            String errorMessage = e.getMessage();
            if (errorMessage != null && errorMessage.contains("Connection is read-only")) {
                fail("事务隔离性测试失败，仍然存在只读事务异常: " + errorMessage);
            }
        }
    }
}
