package com.haoys.user.domain.param.system;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class ActiveUserParam {
    
    @ApiModelProperty(value = "系统用户id")
    private Long id;
    
    @NotEmpty(message = "用户名不能为空")
    @ApiModelProperty(value = "用户名")
    private String username;
    
    @ApiModelProperty(value = "验证码")
    private String verificationCode;
    
    @ApiModelProperty(value = "注册渠道")
    private String registerType;
    
    @ApiModelProperty(value = "登录来源")
    private String loginSource;
    
    @ApiModelProperty(value = "邮箱")
    private String email;
    
    @ApiModelProperty(value = "用户注册来源")
    private String userType;
    
    @ApiModelProperty(value = "用户姓名")
    private String realName;
    
    @ApiModelProperty(value = "联系方式-手机号")
    private String mobile;
    
    @ApiModelProperty(value = "密码")
    private String password;
    
    @ApiModelProperty(value = "科室id-参照科室管理")
    private String department;
    
    @ApiModelProperty(value = "单位名称")
    private String departmentName;
    
    @ApiModelProperty(value = "职称id-参照字典")
    private String positional;
    
    @ApiModelProperty(value = "企业部门")
    private String enterprise;
    
    @ApiModelProperty(value = "地址")
    private String address;
    
    @ApiModelProperty(value = "绑定项目")
    private String projectId;
    
    @ApiModelProperty(value = "注册来源")
    private String registerFrom;
    
    @ApiModelProperty(value = "企业租户id")
    private String tenantId;
    
    @ApiModelProperty(value = "平台id")
    private String platformId;
    
}
