package com.haoys.user.mapper;

import com.haoys.user.domain.param.system.SystemUserInfoParam;
import com.haoys.user.domain.vo.project.SystemUserExtendVo;
import com.haoys.user.model.SystemUserInfo;
import com.haoys.user.model.SystemUserInfoExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface SystemUserInfoMapper {

    long countByExample(SystemUserInfoExample example);

    int deleteByExample(SystemUserInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SystemUserInfo record);

    int insertSelective(SystemUserInfo record);

    List<SystemUserInfo> selectByExample(SystemUserInfoExample example);

    SystemUserInfo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SystemUserInfo record, @Param("example") SystemUserInfoExample example);

    int updateByExample(@Param("record") SystemUserInfo record, @Param("example") SystemUserInfoExample example);

    int updateByPrimaryKeySelective(SystemUserInfo record);

    int updateByPrimaryKey(SystemUserInfo record);


    /**
     * 查询系统用户列表
     * @param params
     * @return
     */
    List<SystemUserExtendVo> getSystemUserList(Map<String, Object> params);

    /**
     * 通过用户名查询用户
     * @param accountName 账户名
     * @param aseMobile 加密后的手机号串
     * @return 用户对象信息
     */
    SystemUserInfo getSystemUserInfoByAccountName(String accountName,String aseMobile);

    /**
     * 根据用户名查询用户基本信息
     * @param userName
     * @return
     */
    SystemUserInfo getUserInfoByUserName(String userName);

    /**
     * 校验手机号码是否唯一
     * @param mobile 手机号码
     * @return 结果
     */
    SystemUserInfo checkMobileExists(String mobile);

    /**
     * 校验邮箱是否存在
     * @param email
     * @return
     */
    SystemUserInfo checkEmailExists(String email);

    /**
     * 通过手机号码查询用户
     * @param mobile 手机号码
     * @return 用户对象信息
     */
    SystemUserInfo getUserByMoblie(String mobile);


    /**
     * 根据userId删除系统用户
     * @param userId
     */
    void deleteUserById(String userId);


    SystemUserInfo getSystemUserByAccountNameOrEmail(String username);

    /**
     * 统计部门人数
     * @param departmentId
     * @return
     */
    int selectCountUser(Long departmentId, String platformId, String tenantId);


    int updateByIdForSealFlag(Long userId);

    /**
     * 统计外部用户人数
     * @return
     */
    int selectCountOutUser(String tenantId, String platformId, String userType);

    List<SystemUserInfo> selectSystemUserListForPage(SystemUserInfoParam systemUserInfoParam);

    List<SystemUserInfo> selectJoinSystemTenantUser(SystemUserInfoParam systemUserInfoParam);


}
