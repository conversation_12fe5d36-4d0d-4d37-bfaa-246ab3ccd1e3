package com.haoys.user.domain.vo.rcts;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/11 9:53
 */
@Data
public class TesteeInfoVo {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "主键id")
    private Long id;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "参与者编号")
    private String testeeCode;

    @ApiModelProperty(value = "姓名缩写")
    private String acronym;

    @ApiModelProperty(value = "随机号")
    private String randomizedNumber;

    @ApiModelProperty(value = "研究分组名称")
    private String joinGroupName;

    @ApiModelProperty(value = "随机时间")
    private Date randomizedTime;

    @ApiModelProperty(value = "研究状态")
    private String researchStatus;


}
