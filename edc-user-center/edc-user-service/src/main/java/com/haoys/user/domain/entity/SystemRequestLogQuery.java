package com.haoys.user.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 操作日志记录表 oper_log
 */
@Data
public class SystemRequestLogQuery implements Serializable {

    @ApiModelProperty(value = "日志主键")
    private Long id;

    @ApiModelProperty(value = "模块标题")
    private String requestName;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /** 业务类型数组 */
    private String[] businessTypes;

    @ApiModelProperty(value = "方法名称")
    private String methodName;

    @ApiModelProperty(value = "请求方式")
    private String requestMethod;

    @ApiModelProperty(value = "操作类别")
    private String operatorType;

    @ApiModelProperty(value = "是否项目日志0/1")
    private Boolean projectRecordLog;

    @ApiModelProperty(value = "操作人员")
    private String userName;

    @ApiModelProperty(value = "请求URL")
    private String requestUrl;

    @ApiModelProperty(value = "主机地址")
    private String requestIp;

    @ApiModelProperty(value = "操作地点")
    private String location;

    @ApiModelProperty(value = "返回参数")
    private String responseResult;

    @ApiModelProperty(value = "操作状态（0正常 1异常）")
    private Integer status;

    @ApiModelProperty(value = "操作时间")
    private Date createTime;

    @ApiModelProperty(value = "请求参数")
    private String requestParam;

    @ApiModelProperty(value = "错误消息")
    private String errorMessage;

    @ApiModelProperty(value = "开始时间")
    private String beginTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "项目编码")
    private String code;

    @ApiModelProperty(value = "项目名称")
    private String name;

    @ApiModelProperty(value = "操作用户")
    private String realName;

    @ApiModelProperty(value = "数据来源")
    private String dataFrom;
}
