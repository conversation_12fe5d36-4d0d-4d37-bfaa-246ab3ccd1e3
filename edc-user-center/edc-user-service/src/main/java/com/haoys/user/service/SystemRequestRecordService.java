package com.haoys.user.service;

import com.haoys.user.model.SystemRequestRecord;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 系统访问日志记录服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
public interface SystemRequestRecordService {
    
    /**
     * 保存系统访问日志记录
     * 
     * @param record 日志记录
     * @return 是否成功
     */
    boolean saveRecord(SystemRequestRecord record);
    
    /**
     * 批量保存系统访问日志记录
     * 
     * @param records 日志记录列表
     * @return 是否成功
     */
    boolean batchSaveRecords(List<SystemRequestRecord> records);
    
    /**
     * 根据ID查询日志记录
     * 
     * @param id 主键ID
     * @return 日志记录
     */
    SystemRequestRecord getRecordById(Long id);
    
    /**
     * 根据链路追踪ID查询日志记录
     * 
     * @param traceId 链路追踪ID
     * @return 日志记录列表
     */
    List<SystemRequestRecord> getRecordsByTraceId(String traceId);
    
    /**
     * 根据会话ID查询日志记录
     * 
     * @param sessionId 会话ID
     * @return 日志记录列表
     */
    List<SystemRequestRecord> getRecordsBySessionId(String sessionId);
    
    /**
     * 分页查询日志记录
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param userName 用户名（可选）
     * @param requestUrl 请求URL（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param isSuccess 是否成功（可选）
     * @return 分页结果
     */
    Map<String, Object> getRecordsByPage(int pageNum, int pageSize, String userName, 
                                        String requestUrl, Date startTime, Date endTime, Boolean isSuccess);
    
    /**
     * 根据用户查询日志记录
     * 
     * @param userId 用户ID
     * @param userName 用户名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 日志记录列表
     */
    List<SystemRequestRecord> getRecordsByUser(Long userId, String userName, 
                                              Date startTime, Date endTime, int limit);
    
    /**
     * 根据IP地址查询日志记录
     * 
     * @param requestIp IP地址
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 日志记录列表
     */
    List<SystemRequestRecord> getRecordsByIp(String requestIp, Date startTime, Date endTime, int limit);
    
    /**
     * 查询异常日志记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 异常日志记录列表
     */
    List<SystemRequestRecord> getErrorRecords(Date startTime, Date endTime, int limit);
    
    /**
     * 查询慢请求日志记录
     * 
     * @param minResponseTime 最小响应时间
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 慢请求日志记录列表
     */
    List<SystemRequestRecord> getSlowRecords(Long minResponseTime, Date startTime, Date endTime, int limit);
    
    /**
     * 获取访问量统计
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param groupBy 分组字段（hour, day, month）
     * @return 统计结果
     */
    List<Map<String, Object>> getAccessStatistics(Date startTime, Date endTime, String groupBy);
    
    /**
     * 获取热点URL统计
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 热点URL统计
     */
    List<Map<String, Object>> getHotUrlStatistics(Date startTime, Date endTime, int limit);
    
    /**
     * 获取用户访问统计
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 用户访问统计
     */
    List<Map<String, Object>> getUserStatistics(Date startTime, Date endTime, int limit);
    
    /**
     * 获取IP访问统计
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return IP访问统计
     */
    List<Map<String, Object>> getIpStatistics(Date startTime, Date endTime, int limit);
    
    /**
     * 获取响应时间分布统计
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 响应时间分布统计
     */
    List<Map<String, Object>> getResponseTimeDistribution(Date startTime, Date endTime);
    
    /**
     * 获取设备类型分布统计
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 设备类型分布统计
     */
    List<Map<String, Object>> getDeviceTypeDistribution(Date startTime, Date endTime);
    
    /**
     * 获取浏览器分布统计
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 浏览器分布统计
     */
    List<Map<String, Object>> getBrowserDistribution(Date startTime, Date endTime);
    
    /**
     * 删除过期日志记录
     * 
     * @param retentionDays 保留天数
     * @return 删除的记录数
     */
    int deleteExpiredRecords(int retentionDays);
    
    /**
     * 根据ID列表删除日志记录
     * 
     * @param ids ID列表
     * @return 删除的记录数
     */
    int deleteRecordsByIds(List<Long> ids);
    
    /**
     * 清空所有日志记录
     * 
     * @return 删除的记录数
     */
    int deleteAllRecords();
    
    /**
     * 获取表统计信息
     * 
     * @return 表统计信息
     */
    Map<String, Object> getTableStatistics();
    
    /**
     * 优化表
     */
    void optimizeTable();
    
    /**
     * 获取系统监控数据
     * 
     * @return 监控数据
     */
    Map<String, Object> getSystemMonitorData();
    
    /**
     * 获取实时统计数据
     * 
     * @return 实时统计数据
     */
    Map<String, Object> getRealTimeStatistics();
    
    /**
     * 导出日志记录
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param userName 用户名（可选）
     * @param requestUrl 请求URL（可选）
     * @return 导出文件路径
     */
    String exportRecords(Date startTime, Date endTime, String userName, String requestUrl);

    /**
     * 清理过期的日志记录
     *
     * @return 清理的记录数量
     */
    int cleanExpiredRecords();

    /**
     * 刷新统计缓存
     */
    void refreshStatisticsCache();

    /**
     * 清理缓存
     */
    void cleanupCache();

    /**
     * 健康检查
     *
     * @return 健康状态
     */
    boolean checkHealth();

    /**
     * 生成报告
     *
     * @return 报告数据
     */
    Map<String, Object> generateReport();

    /**
     * 获取需要归档的记录
     *
     * @param beforeDate 归档截止日期
     * @param batchSize 批次大小
     * @return 需要归档的记录列表
     */
    List<SystemRequestRecord> getRecordsForArchive(Date beforeDate, int batchSize);

    /**
     * 删除已归档的记录
     *
     * @param beforeDate 归档截止日期
     * @param batchSize 批次大小
     * @return 删除的记录数
     */
    int deleteArchivedRecords(Date beforeDate, int batchSize);

    /**
     * 获取错误率统计
     *
     * @param hours 统计小时数
     * @return 错误率统计数据
     */
    Map<String, Object> getErrorRateStats(int hours);
}
