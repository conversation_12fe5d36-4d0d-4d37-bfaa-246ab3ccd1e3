package com.haoys.user.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SystemUserInfoBackExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public SystemUserInfoBackExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andUsernameIsNull() {
            addCriterion("username is null");
            return (Criteria) this;
        }

        public Criteria andUsernameIsNotNull() {
            addCriterion("username is not null");
            return (Criteria) this;
        }

        public Criteria andUsernameEqualTo(String value) {
            addCriterion("username =", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameNotEqualTo(String value) {
            addCriterion("username <>", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameGreaterThan(String value) {
            addCriterion("username >", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("username >=", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameLessThan(String value) {
            addCriterion("username <", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameLessThanOrEqualTo(String value) {
            addCriterion("username <=", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameLike(String value) {
            addCriterion("username like", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameNotLike(String value) {
            addCriterion("username not like", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameIn(List<String> values) {
            addCriterion("username in", values, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameNotIn(List<String> values) {
            addCriterion("username not in", values, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameBetween(String value1, String value2) {
            addCriterion("username between", value1, value2, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameNotBetween(String value1, String value2) {
            addCriterion("username not between", value1, value2, "username");
            return (Criteria) this;
        }

        public Criteria andPasswordIsNull() {
            addCriterion("password is null");
            return (Criteria) this;
        }

        public Criteria andPasswordIsNotNull() {
            addCriterion("password is not null");
            return (Criteria) this;
        }

        public Criteria andPasswordEqualTo(String value) {
            addCriterion("password =", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordNotEqualTo(String value) {
            addCriterion("password <>", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordGreaterThan(String value) {
            addCriterion("password >", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordGreaterThanOrEqualTo(String value) {
            addCriterion("password >=", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordLessThan(String value) {
            addCriterion("password <", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordLessThanOrEqualTo(String value) {
            addCriterion("password <=", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordLike(String value) {
            addCriterion("password like", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordNotLike(String value) {
            addCriterion("password not like", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordIn(List<String> values) {
            addCriterion("password in", values, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordNotIn(List<String> values) {
            addCriterion("password not in", values, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordBetween(String value1, String value2) {
            addCriterion("password between", value1, value2, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordNotBetween(String value1, String value2) {
            addCriterion("password not between", value1, value2, "password");
            return (Criteria) this;
        }

        public Criteria andIconIsNull() {
            addCriterion("icon is null");
            return (Criteria) this;
        }

        public Criteria andIconIsNotNull() {
            addCriterion("icon is not null");
            return (Criteria) this;
        }

        public Criteria andIconEqualTo(String value) {
            addCriterion("icon =", value, "icon");
            return (Criteria) this;
        }

        public Criteria andIconNotEqualTo(String value) {
            addCriterion("icon <>", value, "icon");
            return (Criteria) this;
        }

        public Criteria andIconGreaterThan(String value) {
            addCriterion("icon >", value, "icon");
            return (Criteria) this;
        }

        public Criteria andIconGreaterThanOrEqualTo(String value) {
            addCriterion("icon >=", value, "icon");
            return (Criteria) this;
        }

        public Criteria andIconLessThan(String value) {
            addCriterion("icon <", value, "icon");
            return (Criteria) this;
        }

        public Criteria andIconLessThanOrEqualTo(String value) {
            addCriterion("icon <=", value, "icon");
            return (Criteria) this;
        }

        public Criteria andIconLike(String value) {
            addCriterion("icon like", value, "icon");
            return (Criteria) this;
        }

        public Criteria andIconNotLike(String value) {
            addCriterion("icon not like", value, "icon");
            return (Criteria) this;
        }

        public Criteria andIconIn(List<String> values) {
            addCriterion("icon in", values, "icon");
            return (Criteria) this;
        }

        public Criteria andIconNotIn(List<String> values) {
            addCriterion("icon not in", values, "icon");
            return (Criteria) this;
        }

        public Criteria andIconBetween(String value1, String value2) {
            addCriterion("icon between", value1, value2, "icon");
            return (Criteria) this;
        }

        public Criteria andIconNotBetween(String value1, String value2) {
            addCriterion("icon not between", value1, value2, "icon");
            return (Criteria) this;
        }

        public Criteria andEmailIsNull() {
            addCriterion("email is null");
            return (Criteria) this;
        }

        public Criteria andEmailIsNotNull() {
            addCriterion("email is not null");
            return (Criteria) this;
        }

        public Criteria andEmailEqualTo(String value) {
            addCriterion("email =", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotEqualTo(String value) {
            addCriterion("email <>", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThan(String value) {
            addCriterion("email >", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThanOrEqualTo(String value) {
            addCriterion("email >=", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLessThan(String value) {
            addCriterion("email <", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLessThanOrEqualTo(String value) {
            addCriterion("email <=", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLike(String value) {
            addCriterion("email like", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotLike(String value) {
            addCriterion("email not like", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailIn(List<String> values) {
            addCriterion("email in", values, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotIn(List<String> values) {
            addCriterion("email not in", values, "email");
            return (Criteria) this;
        }

        public Criteria andEmailBetween(String value1, String value2) {
            addCriterion("email between", value1, value2, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotBetween(String value1, String value2) {
            addCriterion("email not between", value1, value2, "email");
            return (Criteria) this;
        }

        public Criteria andUserTypeIsNull() {
            addCriterion("user_type is null");
            return (Criteria) this;
        }

        public Criteria andUserTypeIsNotNull() {
            addCriterion("user_type is not null");
            return (Criteria) this;
        }

        public Criteria andUserTypeEqualTo(String value) {
            addCriterion("user_type =", value, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeNotEqualTo(String value) {
            addCriterion("user_type <>", value, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeGreaterThan(String value) {
            addCriterion("user_type >", value, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeGreaterThanOrEqualTo(String value) {
            addCriterion("user_type >=", value, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeLessThan(String value) {
            addCriterion("user_type <", value, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeLessThanOrEqualTo(String value) {
            addCriterion("user_type <=", value, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeLike(String value) {
            addCriterion("user_type like", value, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeNotLike(String value) {
            addCriterion("user_type not like", value, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeIn(List<String> values) {
            addCriterion("user_type in", values, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeNotIn(List<String> values) {
            addCriterion("user_type not in", values, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeBetween(String value1, String value2) {
            addCriterion("user_type between", value1, value2, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeNotBetween(String value1, String value2) {
            addCriterion("user_type not between", value1, value2, "userType");
            return (Criteria) this;
        }

        public Criteria andRealNameIsNull() {
            addCriterion("real_name is null");
            return (Criteria) this;
        }

        public Criteria andRealNameIsNotNull() {
            addCriterion("real_name is not null");
            return (Criteria) this;
        }

        public Criteria andRealNameEqualTo(String value) {
            addCriterion("real_name =", value, "realName");
            return (Criteria) this;
        }

        public Criteria andRealNameNotEqualTo(String value) {
            addCriterion("real_name <>", value, "realName");
            return (Criteria) this;
        }

        public Criteria andRealNameGreaterThan(String value) {
            addCriterion("real_name >", value, "realName");
            return (Criteria) this;
        }

        public Criteria andRealNameGreaterThanOrEqualTo(String value) {
            addCriterion("real_name >=", value, "realName");
            return (Criteria) this;
        }

        public Criteria andRealNameLessThan(String value) {
            addCriterion("real_name <", value, "realName");
            return (Criteria) this;
        }

        public Criteria andRealNameLessThanOrEqualTo(String value) {
            addCriterion("real_name <=", value, "realName");
            return (Criteria) this;
        }

        public Criteria andRealNameLike(String value) {
            addCriterion("real_name like", value, "realName");
            return (Criteria) this;
        }

        public Criteria andRealNameNotLike(String value) {
            addCriterion("real_name not like", value, "realName");
            return (Criteria) this;
        }

        public Criteria andRealNameIn(List<String> values) {
            addCriterion("real_name in", values, "realName");
            return (Criteria) this;
        }

        public Criteria andRealNameNotIn(List<String> values) {
            addCriterion("real_name not in", values, "realName");
            return (Criteria) this;
        }

        public Criteria andRealNameBetween(String value1, String value2) {
            addCriterion("real_name between", value1, value2, "realName");
            return (Criteria) this;
        }

        public Criteria andRealNameNotBetween(String value1, String value2) {
            addCriterion("real_name not between", value1, value2, "realName");
            return (Criteria) this;
        }

        public Criteria andNickNameIsNull() {
            addCriterion("nick_name is null");
            return (Criteria) this;
        }

        public Criteria andNickNameIsNotNull() {
            addCriterion("nick_name is not null");
            return (Criteria) this;
        }

        public Criteria andNickNameEqualTo(String value) {
            addCriterion("nick_name =", value, "nickName");
            return (Criteria) this;
        }

        public Criteria andNickNameNotEqualTo(String value) {
            addCriterion("nick_name <>", value, "nickName");
            return (Criteria) this;
        }

        public Criteria andNickNameGreaterThan(String value) {
            addCriterion("nick_name >", value, "nickName");
            return (Criteria) this;
        }

        public Criteria andNickNameGreaterThanOrEqualTo(String value) {
            addCriterion("nick_name >=", value, "nickName");
            return (Criteria) this;
        }

        public Criteria andNickNameLessThan(String value) {
            addCriterion("nick_name <", value, "nickName");
            return (Criteria) this;
        }

        public Criteria andNickNameLessThanOrEqualTo(String value) {
            addCriterion("nick_name <=", value, "nickName");
            return (Criteria) this;
        }

        public Criteria andNickNameLike(String value) {
            addCriterion("nick_name like", value, "nickName");
            return (Criteria) this;
        }

        public Criteria andNickNameNotLike(String value) {
            addCriterion("nick_name not like", value, "nickName");
            return (Criteria) this;
        }

        public Criteria andNickNameIn(List<String> values) {
            addCriterion("nick_name in", values, "nickName");
            return (Criteria) this;
        }

        public Criteria andNickNameNotIn(List<String> values) {
            addCriterion("nick_name not in", values, "nickName");
            return (Criteria) this;
        }

        public Criteria andNickNameBetween(String value1, String value2) {
            addCriterion("nick_name between", value1, value2, "nickName");
            return (Criteria) this;
        }

        public Criteria andNickNameNotBetween(String value1, String value2) {
            addCriterion("nick_name not between", value1, value2, "nickName");
            return (Criteria) this;
        }

        public Criteria andMobileIsNull() {
            addCriterion("mobile is null");
            return (Criteria) this;
        }

        public Criteria andMobileIsNotNull() {
            addCriterion("mobile is not null");
            return (Criteria) this;
        }

        public Criteria andMobileEqualTo(String value) {
            addCriterion("mobile =", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileNotEqualTo(String value) {
            addCriterion("mobile <>", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileGreaterThan(String value) {
            addCriterion("mobile >", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileGreaterThanOrEqualTo(String value) {
            addCriterion("mobile >=", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileLessThan(String value) {
            addCriterion("mobile <", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileLessThanOrEqualTo(String value) {
            addCriterion("mobile <=", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileLike(String value) {
            addCriterion("mobile like", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileNotLike(String value) {
            addCriterion("mobile not like", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileIn(List<String> values) {
            addCriterion("mobile in", values, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileNotIn(List<String> values) {
            addCriterion("mobile not in", values, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileBetween(String value1, String value2) {
            addCriterion("mobile between", value1, value2, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileNotBetween(String value1, String value2) {
            addCriterion("mobile not between", value1, value2, "mobile");
            return (Criteria) this;
        }

        public Criteria andThirdOpenIdIsNull() {
            addCriterion("third_open_id is null");
            return (Criteria) this;
        }

        public Criteria andThirdOpenIdIsNotNull() {
            addCriterion("third_open_id is not null");
            return (Criteria) this;
        }

        public Criteria andThirdOpenIdEqualTo(String value) {
            addCriterion("third_open_id =", value, "thirdOpenId");
            return (Criteria) this;
        }

        public Criteria andThirdOpenIdNotEqualTo(String value) {
            addCriterion("third_open_id <>", value, "thirdOpenId");
            return (Criteria) this;
        }

        public Criteria andThirdOpenIdGreaterThan(String value) {
            addCriterion("third_open_id >", value, "thirdOpenId");
            return (Criteria) this;
        }

        public Criteria andThirdOpenIdGreaterThanOrEqualTo(String value) {
            addCriterion("third_open_id >=", value, "thirdOpenId");
            return (Criteria) this;
        }

        public Criteria andThirdOpenIdLessThan(String value) {
            addCriterion("third_open_id <", value, "thirdOpenId");
            return (Criteria) this;
        }

        public Criteria andThirdOpenIdLessThanOrEqualTo(String value) {
            addCriterion("third_open_id <=", value, "thirdOpenId");
            return (Criteria) this;
        }

        public Criteria andThirdOpenIdLike(String value) {
            addCriterion("third_open_id like", value, "thirdOpenId");
            return (Criteria) this;
        }

        public Criteria andThirdOpenIdNotLike(String value) {
            addCriterion("third_open_id not like", value, "thirdOpenId");
            return (Criteria) this;
        }

        public Criteria andThirdOpenIdIn(List<String> values) {
            addCriterion("third_open_id in", values, "thirdOpenId");
            return (Criteria) this;
        }

        public Criteria andThirdOpenIdNotIn(List<String> values) {
            addCriterion("third_open_id not in", values, "thirdOpenId");
            return (Criteria) this;
        }

        public Criteria andThirdOpenIdBetween(String value1, String value2) {
            addCriterion("third_open_id between", value1, value2, "thirdOpenId");
            return (Criteria) this;
        }

        public Criteria andThirdOpenIdNotBetween(String value1, String value2) {
            addCriterion("third_open_id not between", value1, value2, "thirdOpenId");
            return (Criteria) this;
        }

        public Criteria andBpdUserIdIsNull() {
            addCriterion("bpd_user_id is null");
            return (Criteria) this;
        }

        public Criteria andBpdUserIdIsNotNull() {
            addCriterion("bpd_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andBpdUserIdEqualTo(String value) {
            addCriterion("bpd_user_id =", value, "bpdUserId");
            return (Criteria) this;
        }

        public Criteria andBpdUserIdNotEqualTo(String value) {
            addCriterion("bpd_user_id <>", value, "bpdUserId");
            return (Criteria) this;
        }

        public Criteria andBpdUserIdGreaterThan(String value) {
            addCriterion("bpd_user_id >", value, "bpdUserId");
            return (Criteria) this;
        }

        public Criteria andBpdUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("bpd_user_id >=", value, "bpdUserId");
            return (Criteria) this;
        }

        public Criteria andBpdUserIdLessThan(String value) {
            addCriterion("bpd_user_id <", value, "bpdUserId");
            return (Criteria) this;
        }

        public Criteria andBpdUserIdLessThanOrEqualTo(String value) {
            addCriterion("bpd_user_id <=", value, "bpdUserId");
            return (Criteria) this;
        }

        public Criteria andBpdUserIdLike(String value) {
            addCriterion("bpd_user_id like", value, "bpdUserId");
            return (Criteria) this;
        }

        public Criteria andBpdUserIdNotLike(String value) {
            addCriterion("bpd_user_id not like", value, "bpdUserId");
            return (Criteria) this;
        }

        public Criteria andBpdUserIdIn(List<String> values) {
            addCriterion("bpd_user_id in", values, "bpdUserId");
            return (Criteria) this;
        }

        public Criteria andBpdUserIdNotIn(List<String> values) {
            addCriterion("bpd_user_id not in", values, "bpdUserId");
            return (Criteria) this;
        }

        public Criteria andBpdUserIdBetween(String value1, String value2) {
            addCriterion("bpd_user_id between", value1, value2, "bpdUserId");
            return (Criteria) this;
        }

        public Criteria andBpdUserIdNotBetween(String value1, String value2) {
            addCriterion("bpd_user_id not between", value1, value2, "bpdUserId");
            return (Criteria) this;
        }

        public Criteria andNoteIsNull() {
            addCriterion("note is null");
            return (Criteria) this;
        }

        public Criteria andNoteIsNotNull() {
            addCriterion("note is not null");
            return (Criteria) this;
        }

        public Criteria andNoteEqualTo(String value) {
            addCriterion("note =", value, "note");
            return (Criteria) this;
        }

        public Criteria andNoteNotEqualTo(String value) {
            addCriterion("note <>", value, "note");
            return (Criteria) this;
        }

        public Criteria andNoteGreaterThan(String value) {
            addCriterion("note >", value, "note");
            return (Criteria) this;
        }

        public Criteria andNoteGreaterThanOrEqualTo(String value) {
            addCriterion("note >=", value, "note");
            return (Criteria) this;
        }

        public Criteria andNoteLessThan(String value) {
            addCriterion("note <", value, "note");
            return (Criteria) this;
        }

        public Criteria andNoteLessThanOrEqualTo(String value) {
            addCriterion("note <=", value, "note");
            return (Criteria) this;
        }

        public Criteria andNoteLike(String value) {
            addCriterion("note like", value, "note");
            return (Criteria) this;
        }

        public Criteria andNoteNotLike(String value) {
            addCriterion("note not like", value, "note");
            return (Criteria) this;
        }

        public Criteria andNoteIn(List<String> values) {
            addCriterion("note in", values, "note");
            return (Criteria) this;
        }

        public Criteria andNoteNotIn(List<String> values) {
            addCriterion("note not in", values, "note");
            return (Criteria) this;
        }

        public Criteria andNoteBetween(String value1, String value2) {
            addCriterion("note between", value1, value2, "note");
            return (Criteria) this;
        }

        public Criteria andNoteNotBetween(String value1, String value2) {
            addCriterion("note not between", value1, value2, "note");
            return (Criteria) this;
        }

        public Criteria andRoleDescIsNull() {
            addCriterion("role_desc is null");
            return (Criteria) this;
        }

        public Criteria andRoleDescIsNotNull() {
            addCriterion("role_desc is not null");
            return (Criteria) this;
        }

        public Criteria andRoleDescEqualTo(String value) {
            addCriterion("role_desc =", value, "roleDesc");
            return (Criteria) this;
        }

        public Criteria andRoleDescNotEqualTo(String value) {
            addCriterion("role_desc <>", value, "roleDesc");
            return (Criteria) this;
        }

        public Criteria andRoleDescGreaterThan(String value) {
            addCriterion("role_desc >", value, "roleDesc");
            return (Criteria) this;
        }

        public Criteria andRoleDescGreaterThanOrEqualTo(String value) {
            addCriterion("role_desc >=", value, "roleDesc");
            return (Criteria) this;
        }

        public Criteria andRoleDescLessThan(String value) {
            addCriterion("role_desc <", value, "roleDesc");
            return (Criteria) this;
        }

        public Criteria andRoleDescLessThanOrEqualTo(String value) {
            addCriterion("role_desc <=", value, "roleDesc");
            return (Criteria) this;
        }

        public Criteria andRoleDescLike(String value) {
            addCriterion("role_desc like", value, "roleDesc");
            return (Criteria) this;
        }

        public Criteria andRoleDescNotLike(String value) {
            addCriterion("role_desc not like", value, "roleDesc");
            return (Criteria) this;
        }

        public Criteria andRoleDescIn(List<String> values) {
            addCriterion("role_desc in", values, "roleDesc");
            return (Criteria) this;
        }

        public Criteria andRoleDescNotIn(List<String> values) {
            addCriterion("role_desc not in", values, "roleDesc");
            return (Criteria) this;
        }

        public Criteria andRoleDescBetween(String value1, String value2) {
            addCriterion("role_desc between", value1, value2, "roleDesc");
            return (Criteria) this;
        }

        public Criteria andRoleDescNotBetween(String value1, String value2) {
            addCriterion("role_desc not between", value1, value2, "roleDesc");
            return (Criteria) this;
        }

        public Criteria andTokenValueIsNull() {
            addCriterion("token_value is null");
            return (Criteria) this;
        }

        public Criteria andTokenValueIsNotNull() {
            addCriterion("token_value is not null");
            return (Criteria) this;
        }

        public Criteria andTokenValueEqualTo(String value) {
            addCriterion("token_value =", value, "tokenValue");
            return (Criteria) this;
        }

        public Criteria andTokenValueNotEqualTo(String value) {
            addCriterion("token_value <>", value, "tokenValue");
            return (Criteria) this;
        }

        public Criteria andTokenValueGreaterThan(String value) {
            addCriterion("token_value >", value, "tokenValue");
            return (Criteria) this;
        }

        public Criteria andTokenValueGreaterThanOrEqualTo(String value) {
            addCriterion("token_value >=", value, "tokenValue");
            return (Criteria) this;
        }

        public Criteria andTokenValueLessThan(String value) {
            addCriterion("token_value <", value, "tokenValue");
            return (Criteria) this;
        }

        public Criteria andTokenValueLessThanOrEqualTo(String value) {
            addCriterion("token_value <=", value, "tokenValue");
            return (Criteria) this;
        }

        public Criteria andTokenValueLike(String value) {
            addCriterion("token_value like", value, "tokenValue");
            return (Criteria) this;
        }

        public Criteria andTokenValueNotLike(String value) {
            addCriterion("token_value not like", value, "tokenValue");
            return (Criteria) this;
        }

        public Criteria andTokenValueIn(List<String> values) {
            addCriterion("token_value in", values, "tokenValue");
            return (Criteria) this;
        }

        public Criteria andTokenValueNotIn(List<String> values) {
            addCriterion("token_value not in", values, "tokenValue");
            return (Criteria) this;
        }

        public Criteria andTokenValueBetween(String value1, String value2) {
            addCriterion("token_value between", value1, value2, "tokenValue");
            return (Criteria) this;
        }

        public Criteria andTokenValueNotBetween(String value1, String value2) {
            addCriterion("token_value not between", value1, value2, "tokenValue");
            return (Criteria) this;
        }

        public Criteria andExpireTimeIsNull() {
            addCriterion("expire_time is null");
            return (Criteria) this;
        }

        public Criteria andExpireTimeIsNotNull() {
            addCriterion("expire_time is not null");
            return (Criteria) this;
        }

        public Criteria andExpireTimeEqualTo(Date value) {
            addCriterion("expire_time =", value, "expireTime");
            return (Criteria) this;
        }

        public Criteria andExpireTimeNotEqualTo(Date value) {
            addCriterion("expire_time <>", value, "expireTime");
            return (Criteria) this;
        }

        public Criteria andExpireTimeGreaterThan(Date value) {
            addCriterion("expire_time >", value, "expireTime");
            return (Criteria) this;
        }

        public Criteria andExpireTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("expire_time >=", value, "expireTime");
            return (Criteria) this;
        }

        public Criteria andExpireTimeLessThan(Date value) {
            addCriterion("expire_time <", value, "expireTime");
            return (Criteria) this;
        }

        public Criteria andExpireTimeLessThanOrEqualTo(Date value) {
            addCriterion("expire_time <=", value, "expireTime");
            return (Criteria) this;
        }

        public Criteria andExpireTimeIn(List<Date> values) {
            addCriterion("expire_time in", values, "expireTime");
            return (Criteria) this;
        }

        public Criteria andExpireTimeNotIn(List<Date> values) {
            addCriterion("expire_time not in", values, "expireTime");
            return (Criteria) this;
        }

        public Criteria andExpireTimeBetween(Date value1, Date value2) {
            addCriterion("expire_time between", value1, value2, "expireTime");
            return (Criteria) this;
        }

        public Criteria andExpireTimeNotBetween(Date value1, Date value2) {
            addCriterion("expire_time not between", value1, value2, "expireTime");
            return (Criteria) this;
        }

        public Criteria andLoginCodeIsNull() {
            addCriterion("login_code is null");
            return (Criteria) this;
        }

        public Criteria andLoginCodeIsNotNull() {
            addCriterion("login_code is not null");
            return (Criteria) this;
        }

        public Criteria andLoginCodeEqualTo(String value) {
            addCriterion("login_code =", value, "loginCode");
            return (Criteria) this;
        }

        public Criteria andLoginCodeNotEqualTo(String value) {
            addCriterion("login_code <>", value, "loginCode");
            return (Criteria) this;
        }

        public Criteria andLoginCodeGreaterThan(String value) {
            addCriterion("login_code >", value, "loginCode");
            return (Criteria) this;
        }

        public Criteria andLoginCodeGreaterThanOrEqualTo(String value) {
            addCriterion("login_code >=", value, "loginCode");
            return (Criteria) this;
        }

        public Criteria andLoginCodeLessThan(String value) {
            addCriterion("login_code <", value, "loginCode");
            return (Criteria) this;
        }

        public Criteria andLoginCodeLessThanOrEqualTo(String value) {
            addCriterion("login_code <=", value, "loginCode");
            return (Criteria) this;
        }

        public Criteria andLoginCodeLike(String value) {
            addCriterion("login_code like", value, "loginCode");
            return (Criteria) this;
        }

        public Criteria andLoginCodeNotLike(String value) {
            addCriterion("login_code not like", value, "loginCode");
            return (Criteria) this;
        }

        public Criteria andLoginCodeIn(List<String> values) {
            addCriterion("login_code in", values, "loginCode");
            return (Criteria) this;
        }

        public Criteria andLoginCodeNotIn(List<String> values) {
            addCriterion("login_code not in", values, "loginCode");
            return (Criteria) this;
        }

        public Criteria andLoginCodeBetween(String value1, String value2) {
            addCriterion("login_code between", value1, value2, "loginCode");
            return (Criteria) this;
        }

        public Criteria andLoginCodeNotBetween(String value1, String value2) {
            addCriterion("login_code not between", value1, value2, "loginCode");
            return (Criteria) this;
        }

        public Criteria andSealFlagIsNull() {
            addCriterion("seal_flag is null");
            return (Criteria) this;
        }

        public Criteria andSealFlagIsNotNull() {
            addCriterion("seal_flag is not null");
            return (Criteria) this;
        }

        public Criteria andSealFlagEqualTo(Boolean value) {
            addCriterion("seal_flag =", value, "sealFlag");
            return (Criteria) this;
        }

        public Criteria andSealFlagNotEqualTo(Boolean value) {
            addCriterion("seal_flag <>", value, "sealFlag");
            return (Criteria) this;
        }

        public Criteria andSealFlagGreaterThan(Boolean value) {
            addCriterion("seal_flag >", value, "sealFlag");
            return (Criteria) this;
        }

        public Criteria andSealFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("seal_flag >=", value, "sealFlag");
            return (Criteria) this;
        }

        public Criteria andSealFlagLessThan(Boolean value) {
            addCriterion("seal_flag <", value, "sealFlag");
            return (Criteria) this;
        }

        public Criteria andSealFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("seal_flag <=", value, "sealFlag");
            return (Criteria) this;
        }

        public Criteria andSealFlagIn(List<Boolean> values) {
            addCriterion("seal_flag in", values, "sealFlag");
            return (Criteria) this;
        }

        public Criteria andSealFlagNotIn(List<Boolean> values) {
            addCriterion("seal_flag not in", values, "sealFlag");
            return (Criteria) this;
        }

        public Criteria andSealFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("seal_flag between", value1, value2, "sealFlag");
            return (Criteria) this;
        }

        public Criteria andSealFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("seal_flag not between", value1, value2, "sealFlag");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andActiveStatusIsNull() {
            addCriterion("active_status is null");
            return (Criteria) this;
        }

        public Criteria andActiveStatusIsNotNull() {
            addCriterion("active_status is not null");
            return (Criteria) this;
        }

        public Criteria andActiveStatusEqualTo(Boolean value) {
            addCriterion("active_status =", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusNotEqualTo(Boolean value) {
            addCriterion("active_status <>", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusGreaterThan(Boolean value) {
            addCriterion("active_status >", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusGreaterThanOrEqualTo(Boolean value) {
            addCriterion("active_status >=", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusLessThan(Boolean value) {
            addCriterion("active_status <", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusLessThanOrEqualTo(Boolean value) {
            addCriterion("active_status <=", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusIn(List<Boolean> values) {
            addCriterion("active_status in", values, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusNotIn(List<Boolean> values) {
            addCriterion("active_status not in", values, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusBetween(Boolean value1, Boolean value2) {
            addCriterion("active_status between", value1, value2, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusNotBetween(Boolean value1, Boolean value2) {
            addCriterion("active_status not between", value1, value2, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andDepartmentIsNull() {
            addCriterion("department is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIsNotNull() {
            addCriterion("department is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentEqualTo(String value) {
            addCriterion("department =", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotEqualTo(String value) {
            addCriterion("department <>", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentGreaterThan(String value) {
            addCriterion("department >", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentGreaterThanOrEqualTo(String value) {
            addCriterion("department >=", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLessThan(String value) {
            addCriterion("department <", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLessThanOrEqualTo(String value) {
            addCriterion("department <=", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLike(String value) {
            addCriterion("department like", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotLike(String value) {
            addCriterion("department not like", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentIn(List<String> values) {
            addCriterion("department in", values, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotIn(List<String> values) {
            addCriterion("department not in", values, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentBetween(String value1, String value2) {
            addCriterion("department between", value1, value2, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotBetween(String value1, String value2) {
            addCriterion("department not between", value1, value2, "department");
            return (Criteria) this;
        }

        public Criteria andPositionalIsNull() {
            addCriterion("positional is null");
            return (Criteria) this;
        }

        public Criteria andPositionalIsNotNull() {
            addCriterion("positional is not null");
            return (Criteria) this;
        }

        public Criteria andPositionalEqualTo(String value) {
            addCriterion("positional =", value, "positional");
            return (Criteria) this;
        }

        public Criteria andPositionalNotEqualTo(String value) {
            addCriterion("positional <>", value, "positional");
            return (Criteria) this;
        }

        public Criteria andPositionalGreaterThan(String value) {
            addCriterion("positional >", value, "positional");
            return (Criteria) this;
        }

        public Criteria andPositionalGreaterThanOrEqualTo(String value) {
            addCriterion("positional >=", value, "positional");
            return (Criteria) this;
        }

        public Criteria andPositionalLessThan(String value) {
            addCriterion("positional <", value, "positional");
            return (Criteria) this;
        }

        public Criteria andPositionalLessThanOrEqualTo(String value) {
            addCriterion("positional <=", value, "positional");
            return (Criteria) this;
        }

        public Criteria andPositionalLike(String value) {
            addCriterion("positional like", value, "positional");
            return (Criteria) this;
        }

        public Criteria andPositionalNotLike(String value) {
            addCriterion("positional not like", value, "positional");
            return (Criteria) this;
        }

        public Criteria andPositionalIn(List<String> values) {
            addCriterion("positional in", values, "positional");
            return (Criteria) this;
        }

        public Criteria andPositionalNotIn(List<String> values) {
            addCriterion("positional not in", values, "positional");
            return (Criteria) this;
        }

        public Criteria andPositionalBetween(String value1, String value2) {
            addCriterion("positional between", value1, value2, "positional");
            return (Criteria) this;
        }

        public Criteria andPositionalNotBetween(String value1, String value2) {
            addCriterion("positional not between", value1, value2, "positional");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIsNull() {
            addCriterion("enterprise is null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIsNotNull() {
            addCriterion("enterprise is not null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseEqualTo(String value) {
            addCriterion("enterprise =", value, "enterprise");
            return (Criteria) this;
        }

        public Criteria andEnterpriseNotEqualTo(String value) {
            addCriterion("enterprise <>", value, "enterprise");
            return (Criteria) this;
        }

        public Criteria andEnterpriseGreaterThan(String value) {
            addCriterion("enterprise >", value, "enterprise");
            return (Criteria) this;
        }

        public Criteria andEnterpriseGreaterThanOrEqualTo(String value) {
            addCriterion("enterprise >=", value, "enterprise");
            return (Criteria) this;
        }

        public Criteria andEnterpriseLessThan(String value) {
            addCriterion("enterprise <", value, "enterprise");
            return (Criteria) this;
        }

        public Criteria andEnterpriseLessThanOrEqualTo(String value) {
            addCriterion("enterprise <=", value, "enterprise");
            return (Criteria) this;
        }

        public Criteria andEnterpriseLike(String value) {
            addCriterion("enterprise like", value, "enterprise");
            return (Criteria) this;
        }

        public Criteria andEnterpriseNotLike(String value) {
            addCriterion("enterprise not like", value, "enterprise");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIn(List<String> values) {
            addCriterion("enterprise in", values, "enterprise");
            return (Criteria) this;
        }

        public Criteria andEnterpriseNotIn(List<String> values) {
            addCriterion("enterprise not in", values, "enterprise");
            return (Criteria) this;
        }

        public Criteria andEnterpriseBetween(String value1, String value2) {
            addCriterion("enterprise between", value1, value2, "enterprise");
            return (Criteria) this;
        }

        public Criteria andEnterpriseNotBetween(String value1, String value2) {
            addCriterion("enterprise not between", value1, value2, "enterprise");
            return (Criteria) this;
        }

        public Criteria andAddressIsNull() {
            addCriterion("address is null");
            return (Criteria) this;
        }

        public Criteria andAddressIsNotNull() {
            addCriterion("address is not null");
            return (Criteria) this;
        }

        public Criteria andAddressEqualTo(String value) {
            addCriterion("address =", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotEqualTo(String value) {
            addCriterion("address <>", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressGreaterThan(String value) {
            addCriterion("address >", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressGreaterThanOrEqualTo(String value) {
            addCriterion("address >=", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLessThan(String value) {
            addCriterion("address <", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLessThanOrEqualTo(String value) {
            addCriterion("address <=", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLike(String value) {
            addCriterion("address like", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotLike(String value) {
            addCriterion("address not like", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressIn(List<String> values) {
            addCriterion("address in", values, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotIn(List<String> values) {
            addCriterion("address not in", values, "address");
            return (Criteria) this;
        }

        public Criteria andAddressBetween(String value1, String value2) {
            addCriterion("address between", value1, value2, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotBetween(String value1, String value2) {
            addCriterion("address not between", value1, value2, "address");
            return (Criteria) this;
        }

        public Criteria andProjectCountLimitIsNull() {
            addCriterion("project_count_limit is null");
            return (Criteria) this;
        }

        public Criteria andProjectCountLimitIsNotNull() {
            addCriterion("project_count_limit is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCountLimitEqualTo(Integer value) {
            addCriterion("project_count_limit =", value, "projectCountLimit");
            return (Criteria) this;
        }

        public Criteria andProjectCountLimitNotEqualTo(Integer value) {
            addCriterion("project_count_limit <>", value, "projectCountLimit");
            return (Criteria) this;
        }

        public Criteria andProjectCountLimitGreaterThan(Integer value) {
            addCriterion("project_count_limit >", value, "projectCountLimit");
            return (Criteria) this;
        }

        public Criteria andProjectCountLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("project_count_limit >=", value, "projectCountLimit");
            return (Criteria) this;
        }

        public Criteria andProjectCountLimitLessThan(Integer value) {
            addCriterion("project_count_limit <", value, "projectCountLimit");
            return (Criteria) this;
        }

        public Criteria andProjectCountLimitLessThanOrEqualTo(Integer value) {
            addCriterion("project_count_limit <=", value, "projectCountLimit");
            return (Criteria) this;
        }

        public Criteria andProjectCountLimitIn(List<Integer> values) {
            addCriterion("project_count_limit in", values, "projectCountLimit");
            return (Criteria) this;
        }

        public Criteria andProjectCountLimitNotIn(List<Integer> values) {
            addCriterion("project_count_limit not in", values, "projectCountLimit");
            return (Criteria) this;
        }

        public Criteria andProjectCountLimitBetween(Integer value1, Integer value2) {
            addCriterion("project_count_limit between", value1, value2, "projectCountLimit");
            return (Criteria) this;
        }

        public Criteria andProjectCountLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("project_count_limit not between", value1, value2, "projectCountLimit");
            return (Criteria) this;
        }

        public Criteria andProjectTesteeLimitIsNull() {
            addCriterion("project_testee_limit is null");
            return (Criteria) this;
        }

        public Criteria andProjectTesteeLimitIsNotNull() {
            addCriterion("project_testee_limit is not null");
            return (Criteria) this;
        }

        public Criteria andProjectTesteeLimitEqualTo(Integer value) {
            addCriterion("project_testee_limit =", value, "projectTesteeLimit");
            return (Criteria) this;
        }

        public Criteria andProjectTesteeLimitNotEqualTo(Integer value) {
            addCriterion("project_testee_limit <>", value, "projectTesteeLimit");
            return (Criteria) this;
        }

        public Criteria andProjectTesteeLimitGreaterThan(Integer value) {
            addCriterion("project_testee_limit >", value, "projectTesteeLimit");
            return (Criteria) this;
        }

        public Criteria andProjectTesteeLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("project_testee_limit >=", value, "projectTesteeLimit");
            return (Criteria) this;
        }

        public Criteria andProjectTesteeLimitLessThan(Integer value) {
            addCriterion("project_testee_limit <", value, "projectTesteeLimit");
            return (Criteria) this;
        }

        public Criteria andProjectTesteeLimitLessThanOrEqualTo(Integer value) {
            addCriterion("project_testee_limit <=", value, "projectTesteeLimit");
            return (Criteria) this;
        }

        public Criteria andProjectTesteeLimitIn(List<Integer> values) {
            addCriterion("project_testee_limit in", values, "projectTesteeLimit");
            return (Criteria) this;
        }

        public Criteria andProjectTesteeLimitNotIn(List<Integer> values) {
            addCriterion("project_testee_limit not in", values, "projectTesteeLimit");
            return (Criteria) this;
        }

        public Criteria andProjectTesteeLimitBetween(Integer value1, Integer value2) {
            addCriterion("project_testee_limit between", value1, value2, "projectTesteeLimit");
            return (Criteria) this;
        }

        public Criteria andProjectTesteeLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("project_testee_limit not between", value1, value2, "projectTesteeLimit");
            return (Criteria) this;
        }

        public Criteria andCompanyOwnerUserIsNull() {
            addCriterion("company_owner_user is null");
            return (Criteria) this;
        }

        public Criteria andCompanyOwnerUserIsNotNull() {
            addCriterion("company_owner_user is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyOwnerUserEqualTo(Boolean value) {
            addCriterion("company_owner_user =", value, "companyOwnerUser");
            return (Criteria) this;
        }

        public Criteria andCompanyOwnerUserNotEqualTo(Boolean value) {
            addCriterion("company_owner_user <>", value, "companyOwnerUser");
            return (Criteria) this;
        }

        public Criteria andCompanyOwnerUserGreaterThan(Boolean value) {
            addCriterion("company_owner_user >", value, "companyOwnerUser");
            return (Criteria) this;
        }

        public Criteria andCompanyOwnerUserGreaterThanOrEqualTo(Boolean value) {
            addCriterion("company_owner_user >=", value, "companyOwnerUser");
            return (Criteria) this;
        }

        public Criteria andCompanyOwnerUserLessThan(Boolean value) {
            addCriterion("company_owner_user <", value, "companyOwnerUser");
            return (Criteria) this;
        }

        public Criteria andCompanyOwnerUserLessThanOrEqualTo(Boolean value) {
            addCriterion("company_owner_user <=", value, "companyOwnerUser");
            return (Criteria) this;
        }

        public Criteria andCompanyOwnerUserIn(List<Boolean> values) {
            addCriterion("company_owner_user in", values, "companyOwnerUser");
            return (Criteria) this;
        }

        public Criteria andCompanyOwnerUserNotIn(List<Boolean> values) {
            addCriterion("company_owner_user not in", values, "companyOwnerUser");
            return (Criteria) this;
        }

        public Criteria andCompanyOwnerUserBetween(Boolean value1, Boolean value2) {
            addCriterion("company_owner_user between", value1, value2, "companyOwnerUser");
            return (Criteria) this;
        }

        public Criteria andCompanyOwnerUserNotBetween(Boolean value1, Boolean value2) {
            addCriterion("company_owner_user not between", value1, value2, "companyOwnerUser");
            return (Criteria) this;
        }

        public Criteria andDefaultAdminIsNull() {
            addCriterion("default_admin is null");
            return (Criteria) this;
        }

        public Criteria andDefaultAdminIsNotNull() {
            addCriterion("default_admin is not null");
            return (Criteria) this;
        }

        public Criteria andDefaultAdminEqualTo(Boolean value) {
            addCriterion("default_admin =", value, "defaultAdmin");
            return (Criteria) this;
        }

        public Criteria andDefaultAdminNotEqualTo(Boolean value) {
            addCriterion("default_admin <>", value, "defaultAdmin");
            return (Criteria) this;
        }

        public Criteria andDefaultAdminGreaterThan(Boolean value) {
            addCriterion("default_admin >", value, "defaultAdmin");
            return (Criteria) this;
        }

        public Criteria andDefaultAdminGreaterThanOrEqualTo(Boolean value) {
            addCriterion("default_admin >=", value, "defaultAdmin");
            return (Criteria) this;
        }

        public Criteria andDefaultAdminLessThan(Boolean value) {
            addCriterion("default_admin <", value, "defaultAdmin");
            return (Criteria) this;
        }

        public Criteria andDefaultAdminLessThanOrEqualTo(Boolean value) {
            addCriterion("default_admin <=", value, "defaultAdmin");
            return (Criteria) this;
        }

        public Criteria andDefaultAdminIn(List<Boolean> values) {
            addCriterion("default_admin in", values, "defaultAdmin");
            return (Criteria) this;
        }

        public Criteria andDefaultAdminNotIn(List<Boolean> values) {
            addCriterion("default_admin not in", values, "defaultAdmin");
            return (Criteria) this;
        }

        public Criteria andDefaultAdminBetween(Boolean value1, Boolean value2) {
            addCriterion("default_admin between", value1, value2, "defaultAdmin");
            return (Criteria) this;
        }

        public Criteria andDefaultAdminNotBetween(Boolean value1, Boolean value2) {
            addCriterion("default_admin not between", value1, value2, "defaultAdmin");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeIsNull() {
            addCriterion("identity_type is null");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeIsNotNull() {
            addCriterion("identity_type is not null");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeEqualTo(String value) {
            addCriterion("identity_type =", value, "identityType");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeNotEqualTo(String value) {
            addCriterion("identity_type <>", value, "identityType");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeGreaterThan(String value) {
            addCriterion("identity_type >", value, "identityType");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeGreaterThanOrEqualTo(String value) {
            addCriterion("identity_type >=", value, "identityType");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeLessThan(String value) {
            addCriterion("identity_type <", value, "identityType");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeLessThanOrEqualTo(String value) {
            addCriterion("identity_type <=", value, "identityType");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeLike(String value) {
            addCriterion("identity_type like", value, "identityType");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeNotLike(String value) {
            addCriterion("identity_type not like", value, "identityType");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeIn(List<String> values) {
            addCriterion("identity_type in", values, "identityType");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeNotIn(List<String> values) {
            addCriterion("identity_type not in", values, "identityType");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeBetween(String value1, String value2) {
            addCriterion("identity_type between", value1, value2, "identityType");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeNotBetween(String value1, String value2) {
            addCriterion("identity_type not between", value1, value2, "identityType");
            return (Criteria) this;
        }

        public Criteria andRegisterFromIsNull() {
            addCriterion("register_from is null");
            return (Criteria) this;
        }

        public Criteria andRegisterFromIsNotNull() {
            addCriterion("register_from is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterFromEqualTo(String value) {
            addCriterion("register_from =", value, "registerFrom");
            return (Criteria) this;
        }

        public Criteria andRegisterFromNotEqualTo(String value) {
            addCriterion("register_from <>", value, "registerFrom");
            return (Criteria) this;
        }

        public Criteria andRegisterFromGreaterThan(String value) {
            addCriterion("register_from >", value, "registerFrom");
            return (Criteria) this;
        }

        public Criteria andRegisterFromGreaterThanOrEqualTo(String value) {
            addCriterion("register_from >=", value, "registerFrom");
            return (Criteria) this;
        }

        public Criteria andRegisterFromLessThan(String value) {
            addCriterion("register_from <", value, "registerFrom");
            return (Criteria) this;
        }

        public Criteria andRegisterFromLessThanOrEqualTo(String value) {
            addCriterion("register_from <=", value, "registerFrom");
            return (Criteria) this;
        }

        public Criteria andRegisterFromLike(String value) {
            addCriterion("register_from like", value, "registerFrom");
            return (Criteria) this;
        }

        public Criteria andRegisterFromNotLike(String value) {
            addCriterion("register_from not like", value, "registerFrom");
            return (Criteria) this;
        }

        public Criteria andRegisterFromIn(List<String> values) {
            addCriterion("register_from in", values, "registerFrom");
            return (Criteria) this;
        }

        public Criteria andRegisterFromNotIn(List<String> values) {
            addCriterion("register_from not in", values, "registerFrom");
            return (Criteria) this;
        }

        public Criteria andRegisterFromBetween(String value1, String value2) {
            addCriterion("register_from between", value1, value2, "registerFrom");
            return (Criteria) this;
        }

        public Criteria andRegisterFromNotBetween(String value1, String value2) {
            addCriterion("register_from not between", value1, value2, "registerFrom");
            return (Criteria) this;
        }

        public Criteria andLoginDeviceIsNull() {
            addCriterion("login_device is null");
            return (Criteria) this;
        }

        public Criteria andLoginDeviceIsNotNull() {
            addCriterion("login_device is not null");
            return (Criteria) this;
        }

        public Criteria andLoginDeviceEqualTo(String value) {
            addCriterion("login_device =", value, "loginDevice");
            return (Criteria) this;
        }

        public Criteria andLoginDeviceNotEqualTo(String value) {
            addCriterion("login_device <>", value, "loginDevice");
            return (Criteria) this;
        }

        public Criteria andLoginDeviceGreaterThan(String value) {
            addCriterion("login_device >", value, "loginDevice");
            return (Criteria) this;
        }

        public Criteria andLoginDeviceGreaterThanOrEqualTo(String value) {
            addCriterion("login_device >=", value, "loginDevice");
            return (Criteria) this;
        }

        public Criteria andLoginDeviceLessThan(String value) {
            addCriterion("login_device <", value, "loginDevice");
            return (Criteria) this;
        }

        public Criteria andLoginDeviceLessThanOrEqualTo(String value) {
            addCriterion("login_device <=", value, "loginDevice");
            return (Criteria) this;
        }

        public Criteria andLoginDeviceLike(String value) {
            addCriterion("login_device like", value, "loginDevice");
            return (Criteria) this;
        }

        public Criteria andLoginDeviceNotLike(String value) {
            addCriterion("login_device not like", value, "loginDevice");
            return (Criteria) this;
        }

        public Criteria andLoginDeviceIn(List<String> values) {
            addCriterion("login_device in", values, "loginDevice");
            return (Criteria) this;
        }

        public Criteria andLoginDeviceNotIn(List<String> values) {
            addCriterion("login_device not in", values, "loginDevice");
            return (Criteria) this;
        }

        public Criteria andLoginDeviceBetween(String value1, String value2) {
            addCriterion("login_device between", value1, value2, "loginDevice");
            return (Criteria) this;
        }

        public Criteria andLoginDeviceNotBetween(String value1, String value2) {
            addCriterion("login_device not between", value1, value2, "loginDevice");
            return (Criteria) this;
        }

        public Criteria andParentUserIsNull() {
            addCriterion("parent_user is null");
            return (Criteria) this;
        }

        public Criteria andParentUserIsNotNull() {
            addCriterion("parent_user is not null");
            return (Criteria) this;
        }

        public Criteria andParentUserEqualTo(Long value) {
            addCriterion("parent_user =", value, "parentUser");
            return (Criteria) this;
        }

        public Criteria andParentUserNotEqualTo(Long value) {
            addCriterion("parent_user <>", value, "parentUser");
            return (Criteria) this;
        }

        public Criteria andParentUserGreaterThan(Long value) {
            addCriterion("parent_user >", value, "parentUser");
            return (Criteria) this;
        }

        public Criteria andParentUserGreaterThanOrEqualTo(Long value) {
            addCriterion("parent_user >=", value, "parentUser");
            return (Criteria) this;
        }

        public Criteria andParentUserLessThan(Long value) {
            addCriterion("parent_user <", value, "parentUser");
            return (Criteria) this;
        }

        public Criteria andParentUserLessThanOrEqualTo(Long value) {
            addCriterion("parent_user <=", value, "parentUser");
            return (Criteria) this;
        }

        public Criteria andParentUserIn(List<Long> values) {
            addCriterion("parent_user in", values, "parentUser");
            return (Criteria) this;
        }

        public Criteria andParentUserNotIn(List<Long> values) {
            addCriterion("parent_user not in", values, "parentUser");
            return (Criteria) this;
        }

        public Criteria andParentUserBetween(Long value1, Long value2) {
            addCriterion("parent_user between", value1, value2, "parentUser");
            return (Criteria) this;
        }

        public Criteria andParentUserNotBetween(Long value1, Long value2) {
            addCriterion("parent_user not between", value1, value2, "parentUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNull() {
            addCriterion("create_user is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNotNull() {
            addCriterion("create_user is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserEqualTo(String value) {
            addCriterion("create_user =", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotEqualTo(String value) {
            addCriterion("create_user <>", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThan(String value) {
            addCriterion("create_user >", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThanOrEqualTo(String value) {
            addCriterion("create_user >=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThan(String value) {
            addCriterion("create_user <", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThanOrEqualTo(String value) {
            addCriterion("create_user <=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLike(String value) {
            addCriterion("create_user like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotLike(String value) {
            addCriterion("create_user not like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserIn(List<String> values) {
            addCriterion("create_user in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotIn(List<String> values) {
            addCriterion("create_user not in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserBetween(String value1, String value2) {
            addCriterion("create_user between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotBetween(String value1, String value2) {
            addCriterion("create_user not between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNull() {
            addCriterion("update_user is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNotNull() {
            addCriterion("update_user is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserEqualTo(String value) {
            addCriterion("update_user =", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotEqualTo(String value) {
            addCriterion("update_user <>", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThan(String value) {
            addCriterion("update_user >", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThanOrEqualTo(String value) {
            addCriterion("update_user >=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThan(String value) {
            addCriterion("update_user <", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThanOrEqualTo(String value) {
            addCriterion("update_user <=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLike(String value) {
            addCriterion("update_user like", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotLike(String value) {
            addCriterion("update_user not like", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIn(List<String> values) {
            addCriterion("update_user in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotIn(List<String> values) {
            addCriterion("update_user not in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserBetween(String value1, String value2) {
            addCriterion("update_user between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotBetween(String value1, String value2) {
            addCriterion("update_user not between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andLastModifyPwdTimeIsNull() {
            addCriterion("last_modify_pwd_time is null");
            return (Criteria) this;
        }

        public Criteria andLastModifyPwdTimeIsNotNull() {
            addCriterion("last_modify_pwd_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastModifyPwdTimeEqualTo(Date value) {
            addCriterion("last_modify_pwd_time =", value, "lastModifyPwdTime");
            return (Criteria) this;
        }

        public Criteria andLastModifyPwdTimeNotEqualTo(Date value) {
            addCriterion("last_modify_pwd_time <>", value, "lastModifyPwdTime");
            return (Criteria) this;
        }

        public Criteria andLastModifyPwdTimeGreaterThan(Date value) {
            addCriterion("last_modify_pwd_time >", value, "lastModifyPwdTime");
            return (Criteria) this;
        }

        public Criteria andLastModifyPwdTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("last_modify_pwd_time >=", value, "lastModifyPwdTime");
            return (Criteria) this;
        }

        public Criteria andLastModifyPwdTimeLessThan(Date value) {
            addCriterion("last_modify_pwd_time <", value, "lastModifyPwdTime");
            return (Criteria) this;
        }

        public Criteria andLastModifyPwdTimeLessThanOrEqualTo(Date value) {
            addCriterion("last_modify_pwd_time <=", value, "lastModifyPwdTime");
            return (Criteria) this;
        }

        public Criteria andLastModifyPwdTimeIn(List<Date> values) {
            addCriterion("last_modify_pwd_time in", values, "lastModifyPwdTime");
            return (Criteria) this;
        }

        public Criteria andLastModifyPwdTimeNotIn(List<Date> values) {
            addCriterion("last_modify_pwd_time not in", values, "lastModifyPwdTime");
            return (Criteria) this;
        }

        public Criteria andLastModifyPwdTimeBetween(Date value1, Date value2) {
            addCriterion("last_modify_pwd_time between", value1, value2, "lastModifyPwdTime");
            return (Criteria) this;
        }

        public Criteria andLastModifyPwdTimeNotBetween(Date value1, Date value2) {
            addCriterion("last_modify_pwd_time not between", value1, value2, "lastModifyPwdTime");
            return (Criteria) this;
        }

        public Criteria andLoginTimeIsNull() {
            addCriterion("login_time is null");
            return (Criteria) this;
        }

        public Criteria andLoginTimeIsNotNull() {
            addCriterion("login_time is not null");
            return (Criteria) this;
        }

        public Criteria andLoginTimeEqualTo(Date value) {
            addCriterion("login_time =", value, "loginTime");
            return (Criteria) this;
        }

        public Criteria andLoginTimeNotEqualTo(Date value) {
            addCriterion("login_time <>", value, "loginTime");
            return (Criteria) this;
        }

        public Criteria andLoginTimeGreaterThan(Date value) {
            addCriterion("login_time >", value, "loginTime");
            return (Criteria) this;
        }

        public Criteria andLoginTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("login_time >=", value, "loginTime");
            return (Criteria) this;
        }

        public Criteria andLoginTimeLessThan(Date value) {
            addCriterion("login_time <", value, "loginTime");
            return (Criteria) this;
        }

        public Criteria andLoginTimeLessThanOrEqualTo(Date value) {
            addCriterion("login_time <=", value, "loginTime");
            return (Criteria) this;
        }

        public Criteria andLoginTimeIn(List<Date> values) {
            addCriterion("login_time in", values, "loginTime");
            return (Criteria) this;
        }

        public Criteria andLoginTimeNotIn(List<Date> values) {
            addCriterion("login_time not in", values, "loginTime");
            return (Criteria) this;
        }

        public Criteria andLoginTimeBetween(Date value1, Date value2) {
            addCriterion("login_time between", value1, value2, "loginTime");
            return (Criteria) this;
        }

        public Criteria andLoginTimeNotBetween(Date value1, Date value2) {
            addCriterion("login_time not between", value1, value2, "loginTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}