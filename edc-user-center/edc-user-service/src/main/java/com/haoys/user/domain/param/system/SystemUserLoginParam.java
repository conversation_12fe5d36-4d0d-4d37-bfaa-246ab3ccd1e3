package com.haoys.user.domain.param.system;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * 用户登录参数
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SystemUserLoginParam {

    @NotEmpty(message = "用户名不能为空")
    @ApiModelProperty(value = "用户名",required = true)
    private String username;

    @NotEmpty(message = "登录密码不能为空")
    @ApiModelProperty(value = "密码",required = true)
    private String password;

    @ApiModelProperty(value = "行为滑动验证码二次验证码")
    private String captchaVerification = "";

    @NotEmpty(message = "登录来源不能为空")
    @ApiModelProperty(value = "登录来源 协作平台edc-project-manage 数据专病平台disease-rdr 患者端edc-h5 医护端edc-medical-personnel ",required = true)
    private String loginSource = "edc-project-manage";

    @NotEmpty(message = "登录单位设置不能为空")
    @ApiModelProperty(value = "企业租户id",required = true)
    private String tenantId = "";

    @NotEmpty(message = "登录系统设置不能为空")
    @ApiModelProperty(value = "系统平台id",required = true)
    private String platformId = "";

}
