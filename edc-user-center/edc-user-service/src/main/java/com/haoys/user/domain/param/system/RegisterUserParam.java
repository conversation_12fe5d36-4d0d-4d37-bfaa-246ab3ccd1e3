package com.haoys.user.domain.param.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@Data
public class RegisterUserParam {

    @NotEmpty(message = "请输入用户名")
    @ApiModelProperty(value = "用户名")
    private String username;

    @NotEmpty(message = "请输入密码")
    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "用户注册方式 邮箱/手机号")
    private String registerType;

    @ApiModelProperty(value = "用户姓名")
    @NotEmpty(message = "请输入用户姓名")
    private String realName;

    @ApiModelProperty(value = "联系方式-手机号")
    private String mobile;

    @ApiModelProperty(value = "单位")
    //@NotEmpty(message = "请输入用户所在单位")
    private String department;
    
    @ApiModelProperty(value = "出生生日")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date birthday;
    
    @ApiModelProperty(value = "性别 男/女")
    private String gender;

    @ApiModelProperty(value = "创建者")
    private String createUser;

    @ApiModelProperty(value = "手机验证码-如果输入需要验证")
    private String verificationCode;
    
    @NotEmpty(message = "登录来源不能为空")
    @ApiModelProperty(value = "登录来源 协作平台edc-project-manage 数据专病平台disease-rdr 患者端edc-h5 医护端edc-medical-personnel 统一用户平台unified-user-platform",required = true)
    private String loginSource = "edc";

    @ApiModelProperty(value = "企业租户id")
    private String tenantId = "1";

    @ApiModelProperty(value = "平台id")
    private String platformId = "1";
}
