package com.haoys.user.domain.param.system;

import com.haoys.user.common.annotation.Excel;
import lombok.Data;

@Data
public class SystemUserExcelParam {
    
    @Excel(name = "区域")
    private String area;
    
    @Excel(name = "组")
    private String groupInfo;
    
    @Excel(name = "项目志愿者")
    private String projectVolunteer;
    
    @Excel(name = "志愿者手机号码")
    private String projectVolunteerPhone;
    
    @Excel(name = "研究者姓名")
    private String name;
    
    @Excel(name = "医院")
    private String hospital;
    
    @Excel(name = "科室")
    private String dept;
    
    @Excel(name = "职称")
    private String positional;
    
    @Excel(name = "执业资格证号")
    private String certificate;
    
    @Excel(name = "研究者手机号")
    private String telPhone;
    
}
