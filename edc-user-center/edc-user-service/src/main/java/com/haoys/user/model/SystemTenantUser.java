package com.haoys.user.model;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class SystemTenantUser implements Serializable {
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "系统用户id")
    private Long userId;

    @ApiModelProperty(value = "是否为企业管理员账号0/1")
    private Boolean systemAccount;

    @ApiModelProperty(value = "数据状态 0/1")
    private String status;

    @ApiModelProperty(value = "锁定状态状态 0/1")
    private Boolean lockStatus;

    @ApiModelProperty(value = "锁定时间")
    private Long lockTime;

    @ApiModelProperty(value = "企业身份激活状态 0/1")
    private Boolean activeStatus;

    @ApiModelProperty(value = "工作单位-参照系统中心id")
    private String department;

    @ApiModelProperty(value = "企业部门-参照表department")
    private String enterprise;

    @ApiModelProperty(value = "职称-参照字典")
    private String positional;

    @ApiModelProperty(value = "是否为企业管理员角色")
    private Boolean companyOwnerUser;

    @ApiModelProperty(value = "是否授权全部项目")
    private Boolean ownerTotalAuth;

    @ApiModelProperty(value = "系统来源")
    private String dataFrom;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Boolean getSystemAccount() {
        return systemAccount;
    }

    public void setSystemAccount(Boolean systemAccount) {
        this.systemAccount = systemAccount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getLockStatus() {
        return lockStatus;
    }

    public void setLockStatus(Boolean lockStatus) {
        this.lockStatus = lockStatus;
    }

    public Long getLockTime() {
        return lockTime;
    }

    public void setLockTime(Long lockTime) {
        this.lockTime = lockTime;
    }

    public Boolean getActiveStatus() {
        return activeStatus;
    }

    public void setActiveStatus(Boolean activeStatus) {
        this.activeStatus = activeStatus;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getEnterprise() {
        return enterprise;
    }

    public void setEnterprise(String enterprise) {
        this.enterprise = enterprise;
    }

    public String getPositional() {
        return positional;
    }

    public void setPositional(String positional) {
        this.positional = positional;
    }

    public Boolean getCompanyOwnerUser() {
        return companyOwnerUser;
    }

    public void setCompanyOwnerUser(Boolean companyOwnerUser) {
        this.companyOwnerUser = companyOwnerUser;
    }

    public Boolean getOwnerTotalAuth() {
        return ownerTotalAuth;
    }

    public void setOwnerTotalAuth(Boolean ownerTotalAuth) {
        this.ownerTotalAuth = ownerTotalAuth;
    }

    public String getDataFrom() {
        return dataFrom;
    }

    public void setDataFrom(String dataFrom) {
        this.dataFrom = dataFrom;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", systemAccount=").append(systemAccount);
        sb.append(", status=").append(status);
        sb.append(", lockStatus=").append(lockStatus);
        sb.append(", lockTime=").append(lockTime);
        sb.append(", activeStatus=").append(activeStatus);
        sb.append(", department=").append(department);
        sb.append(", enterprise=").append(enterprise);
        sb.append(", positional=").append(positional);
        sb.append(", companyOwnerUser=").append(companyOwnerUser);
        sb.append(", ownerTotalAuth=").append(ownerTotalAuth);
        sb.append(", dataFrom=").append(dataFrom);
        sb.append(", createTime=").append(createTime);
        sb.append(", tenantId=").append(tenantId);
        sb.append(", platformId=").append(platformId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}