package com.haoys.user.mapper;

import com.haoys.user.model.SystemAuthorizationInfo;
import com.haoys.user.model.SystemAuthorizationInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SystemAuthorizationInfoMapper {
    long countByExample(SystemAuthorizationInfoExample example);

    int deleteByExample(SystemAuthorizationInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SystemAuthorizationInfo record);

    int insertSelective(SystemAuthorizationInfo record);

    List<SystemAuthorizationInfo> selectByExample(SystemAuthorizationInfoExample example);

    SystemAuthorizationInfo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SystemAuthorizationInfo record, @Param("example") SystemAuthorizationInfoExample example);

    int updateByExample(@Param("record") SystemAuthorizationInfo record, @Param("example") SystemAuthorizationInfoExample example);

    int updateByPrimaryKeySelective(SystemAuthorizationInfo record);

    int updateByPrimaryKey(SystemAuthorizationInfo record);
}