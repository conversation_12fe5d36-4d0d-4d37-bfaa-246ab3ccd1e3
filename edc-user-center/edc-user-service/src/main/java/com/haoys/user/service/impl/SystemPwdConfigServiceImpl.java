package com.haoys.user.service.impl;

import com.haoys.user.common.bussiness.RedisKeyContants;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.domain.param.system.SystemPwdConfigParam;
import com.haoys.user.mapper.SystemPwdConfigMapper;
import com.haoys.user.model.SystemPwdConfig;
import com.haoys.user.model.SystemPwdConfigExample;
import com.haoys.user.service.SystemPwdConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SystemPwdConfigServiceImpl implements SystemPwdConfigService {

    @Autowired
    private SystemPwdConfigMapper systemPwdConfigMapper;

    @Autowired
    private RedisTemplateService redisTemplateService;

    @Override
    public SystemPwdConfig getOne() {
        return systemPwdConfigMapper.selectByPrimaryKey(1);
    }

    @Override
    public List<SystemPwdConfig> getConfig(){
        SystemPwdConfigExample example = new SystemPwdConfigExample();
        SystemPwdConfigExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(SecurityUtils.getSystemTenantId());
        return systemPwdConfigMapper.selectByExample(example);
    }
    @Override
    public int updateByPrimaryKeySelective(SystemPwdConfigParam systemPwdConfigParam){
        SystemPwdConfig config = getOne();
        if (config!=null){
            systemPwdConfigParam.setId(config.getId());
            systemPwdConfigParam.setTenantId(SecurityUtils.getSystemTenantId());
            systemPwdConfigParam.setPlatformId(SecurityUtils.getSystemPlatformId());
            BeanUtils.copyProperties(systemPwdConfigParam, config);
            redisTemplateService.set(RedisKeyContants.PWD_CONFIG,config);
            return systemPwdConfigMapper.updateByPrimaryKeySelective(config);
        }else {
            config = new SystemPwdConfig();
            config.setId(1);
            config.setTenantId(SecurityUtils.getSystemTenantId());
            config.setPlatformId(SecurityUtils.getSystemPlatformId());
            redisTemplateService.set(RedisKeyContants.PWD_CONFIG,config);
            return systemPwdConfigMapper.insert(config);
        }
    }

}
