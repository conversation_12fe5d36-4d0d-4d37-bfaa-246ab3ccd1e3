package com.haoys.user.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.dto.DepartmentParam;
import com.haoys.user.domain.dto.DepartmentQuery;
import com.haoys.user.domain.vo.project.SysDepartmentVo;
import com.haoys.user.mapper.SystemDepartmentMapper;
import com.haoys.user.model.SystemDepartment;
import com.haoys.user.model.SystemDepartmentExample;
import com.haoys.user.model.SystemOrgInfo;
import com.haoys.user.service.SystemDepartmentService;
import com.haoys.user.service.SystemOrgInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class SystemDepartmentServiceImpl extends BaseService implements SystemDepartmentService {

    @Autowired
    private SystemDepartmentMapper systemDepartmentMapper;
    @Autowired
    private SystemOrgInfoService systemOrgInfoService;

    @Override
    public CommonPage<SysDepartmentVo> getDepartmentListForPage(DepartmentQuery departmentQuery) {
        List<SysDepartmentVo> dataList = new ArrayList();
        Page<Object> page = PageHelper.startPage(departmentQuery.getPageNum(), departmentQuery.getPageSize());
        SystemDepartmentExample example = new SystemDepartmentExample();
        SystemDepartmentExample.Criteria criteria = example.createCriteria();
        if(StringUtils.isNotEmpty(departmentQuery.getName())){
            criteria.andNameLike("%" +departmentQuery.getName()+ "%");
        }
        if(departmentQuery.getOrgId() != null){
            criteria.andOrgIdEqualTo(departmentQuery.getOrgId());
        }
        criteria.andStatusEqualTo("0");
        example.setOrderByClause("create_time desc");
        List<SystemDepartment> systemDepartmentList = systemDepartmentMapper.selectByExample(example);
        for (SystemDepartment systemDepartment : systemDepartmentList) {
            SysDepartmentVo sysDepartmentVo = new SysDepartmentVo();
            sysDepartmentVo.setId(systemDepartment.getId().toString());
            sysDepartmentVo.setOrgId(systemDepartment.getOrgId().toString());
            sysDepartmentVo.setOrgName(systemDepartment.getOrgName());
            sysDepartmentVo.setName(systemDepartment.getName());
            sysDepartmentVo.setCreateTime(systemDepartment.getCreateTime());
            dataList.add(sysDepartmentVo);
        }
        return commonPageListWrapper(departmentQuery.getPageSize(), departmentQuery.getPageNum(), page, dataList);
    }

    @Override
    public SysDepartmentVo getDepartment(Long id) {
        SystemDepartment systemDepartment = systemDepartmentMapper.selectByPrimaryKey(id);
        if(systemDepartment == null){return null;}
        SysDepartmentVo sysDepartmentVo = new SysDepartmentVo();
        sysDepartmentVo.setOrgId(systemDepartment.getOrgId().toString());
        sysDepartmentVo.setName(systemDepartment.getName());
        sysDepartmentVo.setOrgName(systemDepartment.getOrgName());
        sysDepartmentVo.setCreateTime(systemDepartment.getCreateTime());
        sysDepartmentVo.setStatus(systemDepartment.getStatus());
        return sysDepartmentVo;
    }

    @Override
    public String saveDepartment(DepartmentParam departmentParam) {
        SystemDepartment systemDepartment = new SystemDepartment();
        Boolean message = checkDepartmentNameResult(departmentParam);
        if(message){return BusinessConfig.PROJECT_DEPARTMENT_RECORD_FOUND;}
        if(departmentParam.getId() == null){
            BeanUtils.copyProperties(departmentParam, systemDepartment);
            systemDepartment.setId(SnowflakeIdWorker.getUuid());
            systemDepartment.setCreateUser(departmentParam.getCreateUser());
            systemDepartment.setCreateTime(new Date());
            SystemOrgInfo systemOrgInfo = systemOrgInfoService.selectSystemOrgInfoByOrgId(systemDepartment.getOrgId().toString());
            if(systemOrgInfo != null){
                systemDepartment.setOrgName(systemOrgInfo.getOrgName());
            }
            systemDepartmentMapper.insertSelective(systemDepartment);
        }else{
            systemDepartment = systemDepartmentMapper.selectByPrimaryKey(departmentParam.getId());
            BeanUtils.copyProperties(departmentParam, systemDepartment);
            systemDepartment.setUpdateTime(new Date());
            systemDepartment.setUpdateUser(departmentParam.getCreateUser());
            systemDepartmentMapper.updateByPrimaryKey(systemDepartment);
        }
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    private Boolean checkDepartmentNameResult(DepartmentParam departmentParam) {
        SystemDepartment systemDepartment = systemDepartmentMapper.selectByPrimaryKey(departmentParam.getId());
        if(systemDepartment == null || !systemDepartment.getName().equals(departmentParam.getName())){
            SystemDepartmentExample example = new SystemDepartmentExample();
            SystemDepartmentExample.Criteria criteria = example.createCriteria();
            criteria.andNameEqualTo(departmentParam.getName());
            criteria.andOrgIdEqualTo(departmentParam.getOrgId());
            List<SystemDepartment> systemDepartments = systemDepartmentMapper.selectByExample(example);
            return systemDepartments.size() >0;
        }
        return false;
    }

    @Override
    public String deleteDepartment(Long id) {
        SystemDepartment systemDepartment = systemDepartmentMapper.selectByPrimaryKey(id);
        if(systemDepartment == null){return BusinessConfig.RETURN_MESSAGE_RECORD_NOT_FOUND;}
        systemDepartment.setStatus("1");
        systemDepartmentMapper.updateByPrimaryKey(systemDepartment);
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    @Override
    public String deleteByPrimaryKey(Long id){
        systemDepartmentMapper.deleteByPrimaryKey(id);
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    /**
     * 根据企业id获取部门集合
     * @param example
     * @return
     */
    @Override
    public List<SystemDepartment> selectByExample(SystemDepartmentExample example){
        return systemDepartmentMapper.selectByExample(example);
    }

    @Override
    public int insert(SystemDepartment record){
        return systemDepartmentMapper.insert(record);

    }

    @Override
    public SystemDepartment selectByPrimaryKey(Long id){
        return systemDepartmentMapper.selectByPrimaryKey(id);
    }
    @Override
    public int updateByPrimaryKey(SystemDepartment record){
        return systemDepartmentMapper.updateByPrimaryKey(record);

    }


}
