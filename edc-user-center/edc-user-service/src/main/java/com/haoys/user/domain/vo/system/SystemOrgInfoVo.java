package com.haoys.user.domain.vo.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class SystemOrgInfoVo {


    @JsonFormat(shape=JsonFormat.Shape.STRING)
    private Long orgId;

    private String orgName;

    private String aName1;

    private String aName2;

    private String contact;

    private String tel;

    private String address;

    private String zip;

    private String passwd;

    private String operator;

    private String status;

    private String orgType;

    private String orgCode;

    @ApiModelProperty(value = "省code")
    private Long provinceCode;

    @ApiModelProperty(value = "市code")
    private Long cityCode;

    @ApiModelProperty(value = "县code")
    private Long countyCode;

    private String provinceName;

    private String cityName;

    private String countyName;

    private String city;

    private String regionCode;

    private Integer isAuth;

    private Date createTime;

    private Date updateTime;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;
}
