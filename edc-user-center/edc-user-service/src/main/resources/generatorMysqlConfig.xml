<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <properties resource="generator.properties"/>
    <context id="MySqlContext" targetRuntime="MyBatis3" defaultModelType="flat">
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>
        <property name="javaFileEncoding" value="UTF-8"/>
        <!-- 为模型生成序列化方法-->
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>
        <!-- 为生成的Java模型创建一个toString方法 -->
        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>
        <!--生成mapper.xml时覆盖原文件-->
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>
        <commentGenerator type="com.haoys.user.Comment">
            <!-- 是否去除自动生成的注释 true：是 ： false:否 -->
            <property name="suppressAllComments" value="true"/>
            <property name="suppressDate" value="true"/>
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>

        <jdbcConnection driverClass="${jdbc.driverClass}"
                        connectionURL="${jdbc.connectionURL}"
                        userId="${jdbc.userId}"
                        password="${jdbc.password}">
            <!--解决mysql驱动升级到8.0后不生成指定数据库代码的问题-->
            <property name="nullCatalogMeansCurrent" value="true"/>
        </jdbcConnection>

        <javaModelGenerator targetPackage="com.haoys.user.model"
                            targetProject="/Users/<USER>/source_code/edc-research-master/edc-user-center/edc-user-service/src/main/java"/>

        <sqlMapGenerator targetPackage="com.haoys.user.mapper"
                         targetProject="/Users/<USER>/source_code/edc-research-master/edc-user-center/edc-user-service/src/main/resources"/>

        <javaClientGenerator targetPackage="com.haoys.user.mapper" type="XMLMAPPER"
                             targetProject="/Users/<USER>/source_code/edc-research-master/edc-user-center/edc-user-service/src/main/java"/>


        <!--生成全部表tableName设为%-->
        <!--        <table tableName="%template%">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->

        <!--<table tableName='system_tenant' domainObjectName='SystemTenant'>
            <columnOverride column="tenant_introduce" property="tenantIntroduce" jdbcType="VARCHAR"></columnOverride>
        </table>-->

        <!--<table tableName='system_tenant_user' domainObjectName='SystemTenantUser'/>-->
        <!--<table tableName='platform_system_menu' domainObjectName='PlatformSystemMenu'/>-->
        <!--        <table tableName='system_tenant_user' domainObjectName='SystemTenantUser'/>-->
        <!--        <table tableName='system_file_info' domainObjectName='SystemFileInfo'/>-->
        <!--        <table tableName='disease_database_user' domainObjectName='DiseaseDatabaseUser'/>-->
        <!--        <table tableName='disease_database_auth' domainObjectName='DiseaseDatabaseAuth'/>-->
        <!--<table tableName='system_chat_record' domainObjectName='SystemChatRecord'>
            <columnOverride column="content" property="content" jdbcType="VARCHAR"></columnOverride>
        </table>-->

        <!--系统日志记录相关-->
        <!--<table tableName='system_exception_log' domainObjectName='SystemExceptionLog'>
            <columnOverride column="exception_message" property="exceptionMessage" jdbcType="VARCHAR"></columnOverride>
            <columnOverride column="stack_trace_message" property="stackTraceMessage" jdbcType="VARCHAR"></columnOverride>
            <columnOverride column="description" property="description" jdbcType="VARCHAR"></columnOverride>
        </table>-->
        <!--<table tableName='system_point_log' domainObjectName='SystemPointLog'>
            <columnOverride column="content" property="content" jdbcType="VARCHAR"></columnOverride>
        </table>-->
        <!--<table tableName='send_request_record' domainObjectName='SendRequestRecord'/>-->
        <!--<table tableName='system_login_log' domainObjectName='SystemLoginLog'/>-->
        <!--<table tableName='system_request_log' domainObjectName='SystemRequestLog'>
            <columnOverride column="request_param" property="requestParam" jdbcType="VARCHAR"></columnOverride>
            <columnOverride column="response_result" property="responseResult" jdbcType="VARCHAR"></columnOverride>
            <columnOverride column="error_message" property="errorMessage" jdbcType="VARCHAR"></columnOverride>
        </table>-->

        <!--系统用户相关-->
<!--        <table tableName='dictionary' domainObjectName='Dictionary'/>-->
        <!--<table tableName='system_user_info' domainObjectName='SystemUserInfo'/>-->
        <!--<table tableName='system_org_info' domainObjectName='SystemOrgInfo'/>-->
        <!--<table tableName='system_user_org' domainObjectName='SystemUserOrg'/>-->
        <!--<table tableName='system_department' domainObjectName='SystemDepartment'/>-->
        <!--<table tableName='system_role' domainObjectName='SystemRole'/>-->
<!--        <table tableName='system_menu' domainObjectName='SystemMenu'/>-->
        <!--<table tableName='system_user_role' domainObjectName='SystemUserRole'/>-->
        <!--<table tableName='system_role_menu' domainObjectName='SystemRoleMenu'/>-->
        <!--<table tableName='system_resource' domainObjectName='SystemResource'/>
        <table tableName='system_resource_category' domainObjectName='SystemResourceCategory'/>-->

        <!--系统发送记录-->
        <!--<table tableName='send_message_record' domainObjectName='SendMessageRecord'>
            <columnOverride column="return_data" property="returnData" jdbcType="VARCHAR"></columnOverride>
        </table>-->

        <table tableName='system_authorization_info' domainObjectName='SystemAuthorizationInfo'/>

    </context>
</generatorConfiguration>
