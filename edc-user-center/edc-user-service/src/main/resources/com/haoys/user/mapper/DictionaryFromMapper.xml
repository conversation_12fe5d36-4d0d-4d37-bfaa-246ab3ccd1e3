<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.DictionaryFromMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.DictionaryFrom">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dictionary_id" jdbcType="BIGINT" property="dictionaryId" />
    <result column="dictionary_name" jdbcType="VARCHAR" property="dictionaryName" />
    <result column="dictionary_en_name" jdbcType="VARCHAR" property="dictionaryEnName" />
    <result column="dictionary_code" jdbcType="VARCHAR" property="dictionaryCode" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, dictionary_id, dictionary_name, dictionary_en_name, dictionary_code, create_user, 
    create_time, update_user, update_time, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.DictionaryFromExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from dictionary_from
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from dictionary_from
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from dictionary_from
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.DictionaryFromExample">
    delete from dictionary_from
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.DictionaryFrom">
    insert into dictionary_from (id, dictionary_id, dictionary_name, 
      dictionary_en_name, dictionary_code, create_user, 
      create_time, update_user, update_time, 
      tenant_id, platform_id)
    values (#{id,jdbcType=BIGINT}, #{dictionaryId,jdbcType=BIGINT}, #{dictionaryName,jdbcType=VARCHAR}, 
      #{dictionaryEnName,jdbcType=VARCHAR}, #{dictionaryCode,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.DictionaryFrom">
    insert into dictionary_from
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="dictionaryId != null">
        dictionary_id,
      </if>
      <if test="dictionaryName != null">
        dictionary_name,
      </if>
      <if test="dictionaryEnName != null">
        dictionary_en_name,
      </if>
      <if test="dictionaryCode != null">
        dictionary_code,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="dictionaryId != null">
        #{dictionaryId,jdbcType=BIGINT},
      </if>
      <if test="dictionaryName != null">
        #{dictionaryName,jdbcType=VARCHAR},
      </if>
      <if test="dictionaryEnName != null">
        #{dictionaryEnName,jdbcType=VARCHAR},
      </if>
      <if test="dictionaryCode != null">
        #{dictionaryCode,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.DictionaryFromExample" resultType="java.lang.Long">
    select count(*) from dictionary_from
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update dictionary_from
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.dictionaryId != null">
        dictionary_id = #{record.dictionaryId,jdbcType=BIGINT},
      </if>
      <if test="record.dictionaryName != null">
        dictionary_name = #{record.dictionaryName,jdbcType=VARCHAR},
      </if>
      <if test="record.dictionaryEnName != null">
        dictionary_en_name = #{record.dictionaryEnName,jdbcType=VARCHAR},
      </if>
      <if test="record.dictionaryCode != null">
        dictionary_code = #{record.dictionaryCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update dictionary_from
    set id = #{record.id,jdbcType=BIGINT},
      dictionary_id = #{record.dictionaryId,jdbcType=BIGINT},
      dictionary_name = #{record.dictionaryName,jdbcType=VARCHAR},
      dictionary_en_name = #{record.dictionaryEnName,jdbcType=VARCHAR},
      dictionary_code = #{record.dictionaryCode,jdbcType=VARCHAR},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.DictionaryFrom">
    update dictionary_from
    <set>
      <if test="dictionaryId != null">
        dictionary_id = #{dictionaryId,jdbcType=BIGINT},
      </if>
      <if test="dictionaryName != null">
        dictionary_name = #{dictionaryName,jdbcType=VARCHAR},
      </if>
      <if test="dictionaryEnName != null">
        dictionary_en_name = #{dictionaryEnName,jdbcType=VARCHAR},
      </if>
      <if test="dictionaryCode != null">
        dictionary_code = #{dictionaryCode,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.DictionaryFrom">
    update dictionary_from
    set dictionary_id = #{dictionaryId,jdbcType=BIGINT},
      dictionary_name = #{dictionaryName,jdbcType=VARCHAR},
      dictionary_en_name = #{dictionaryEnName,jdbcType=VARCHAR},
      dictionary_code = #{dictionaryCode,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>