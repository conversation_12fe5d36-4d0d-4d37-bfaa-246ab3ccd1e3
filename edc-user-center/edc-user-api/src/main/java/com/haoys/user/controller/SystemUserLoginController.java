package com.haoys.user.controller;

import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;
import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.annotation.NoRepeatSubmit;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.bussiness.RedisKeyContants;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.common.core.domain.model.LoginUserInfo;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.common.util.DesensitizeUtil;
import com.haoys.user.common.util.EncryptUtils;
import com.haoys.user.common.util.MessageUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.system.ExternalRegisterUserParam;
import com.haoys.user.domain.param.system.SystemUserLoginParam;
import com.haoys.user.domain.param.system.UserVerificationCodeParam;
import com.haoys.user.domain.vo.auth.SystemMenuVo;
import com.haoys.user.domain.vo.project.SystemUserInfoExtendVo;
import com.haoys.user.domain.vo.system.SystemUserInfoVo;
import com.haoys.user.domain.vo.system.SystemUserLoginVo;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.manager.AsyncTaskManager;
import com.haoys.user.manager.factory.AsyncTaskFactory;
import com.haoys.user.model.SystemTenantUser;
import com.haoys.user.security.web.service.SystemUserLoginService;
import com.haoys.user.security.web.service.SystemUserTokenService;
import com.haoys.user.service.OrganizationService;
import com.haoys.user.service.SystemMenuService;
import com.haoys.user.service.SystemRoleService;
import com.haoys.user.service.SystemTenantUserService;
import com.haoys.user.service.SystemUserInfoService;
import com.haoys.user.service.TenantInformationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.security.Principal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Api(tags = "用户登录管理")
@RestController
@RequestMapping("/admin")
public class SystemUserLoginController extends BaseController {

    @Value("${jwt.tokenHeader}")
    private String tokenHeader;
    @Value("${jwt.tokenHead}")
    private String tokenHead;

    @Autowired
    private SystemUserLoginService systemUserLoginService;
    @Autowired
    private SystemUserInfoService systemUserInfoService;
    @Autowired
    private RedisTemplateService redisTemplateService;
    @Autowired
    private SystemMenuService systemMenuService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private SystemRoleService systemRoleService;
    @Autowired
    private SystemUserTokenService systemUserTokenService;
    @Autowired
    private CaptchaService captchaService;
    @Autowired
    private SystemTenantUserService systemTenantUserService;
    @Autowired
    private TenantInformationService tenantInformationService;

    @NoRepeatSubmit
    @ApiOperation(value = "使用用户名和密码获取token")
    @Log(title = "使用用户名和密码登录", businessType = BusinessType.OTHER)
    @PostMapping("/login")
    public CommonResult login(HttpServletRequest request, HttpServletResponse response, @Validated @RequestBody SystemUserLoginParam systemUserLoginParam) {
        List<String> loginSourceList = Arrays.asList(Constants.USER_LOGIN_RESOURCE_4, Constants.USER_LOGIN_RESOURCE_5);
        if(systemUserLoginParam.getPlatformId() != null && !loginSourceList.contains(systemUserLoginParam.getLoginSource())){
            CaptchaVO captchaVO = new CaptchaVO();
            captchaVO.setCaptchaVerification(systemUserLoginParam.getCaptchaVerification());
            ResponseModel captchaResponse = captchaService.verification(captchaVO);
            if(!captchaResponse.isSuccess()){
                return CommonResult.failed("验证码错误");
            }
        }
        SystemUserLoginVo systemUserLoginVo = new SystemUserLoginVo();
        try {
            SystemUserInfoVo systemUserInfo = systemUserInfoService.getSystemUserInfoByAccountName(systemUserLoginParam.getUsername());
            if(systemUserInfo == null){return CommonResult.failed("登录用户: " + systemUserLoginParam.getUsername() + "不存在");}
            // 企业系统管理员
            if(systemUserInfo.getDefaultAdmin()){
                systemUserLoginVo.setSystemUserActiveStatus(systemUserInfo.getActiveStatus());
            }else{
                String loginPlatformId = getTenantUserFromLoginSource(systemUserLoginParam.getLoginSource());
                SystemTenantUser systemTenantUser = systemTenantUserService.getSystemTenantUserByUserId(systemUserInfo.getId().toString(), systemUserLoginParam.getTenantId(), loginPlatformId);
                Boolean activeStatus = systemTenantUser != null && systemTenantUser.getActiveStatus();
                systemUserLoginVo.setSystemUserActiveStatus(activeStatus);
                if(systemTenantUser == null){
                    return CommonResult.validateFailed("平台登录用户: " + systemUserLoginParam.getUsername() + "不存在");
                }
                if(!activeStatus){
                    return CommonResult.validateFailed("平台登录用户: " + systemUserLoginParam.getUsername() + "未完成确认激活");
                }
                systemUserLoginVo.setTenantId(systemTenantUser.getTenantId());
                systemUserLoginVo.setPlatformId(systemTenantUser.getPlatformId());
                systemUserLoginVo.setLoginSource(systemTenantUser.getDataFrom());
            }
            String password = EncryptUtils.decrypt(systemUserLoginParam.getPassword(), Constants.USER_LOGIN_KEY);
            String token = systemUserLoginService.getSystemUserLoginTokenInfo(systemUserLoginParam.getUsername(), password, systemUserLoginParam.getTenantId(), systemUserLoginParam.getPlatformId());
            systemUserLoginVo.setToken(token);
            redisTemplateService.del(RedisKeyContants.EDC_LOGIN_USER_COUNT.concat(systemUserInfo.getUsername()));

            // 登录成功后，将userId设置到header属性X-User-Id和session中
            String userId = systemUserInfo.getId().toString();

            // 设置到响应头中
            response.setHeader("X-User-Id", userId);
            response.setHeader("X-User-Name", systemUserInfo.getUsername());
            response.setHeader("X-Real-Name", systemUserInfo.getRealName() != null ? systemUserInfo.getRealName() : systemUserInfo.getUsername());

            // 设置到session中
            HttpSession session = request.getSession();
            session.setAttribute("userId", userId);
            session.setAttribute("userName", systemUserInfo.getUsername());
            session.setAttribute("realName", systemUserInfo.getRealName() != null ? systemUserInfo.getRealName() : systemUserInfo.getUsername());

            log.info("用户登录成功 - userId: {}, userName: {}, 已设置到header和session中", userId, systemUserInfo.getUsername());

        } catch (Exception e) {
            log.error(e.getMessage());
            AsyncTaskManager.ownerTask().execute(AsyncTaskFactory.recordSystemloginLogInfo(request, systemUserLoginParam.getUsername(), Constants.LOGIN_FAIL, MessageUtils.message("user.not.exists"), Constants.SYSTEM_LOG_LOGIN));
            return CommonResult.failed(e.getMessage());
        }
        return CommonResult.success(systemUserLoginVo);
    }


    @ApiOperation(value = "根据token判断是否已经过期")
    @GetMapping("/checkToken")
    public CommonResult checkToken(String token,boolean isEmail) {
        if (isEmail){
            if (StringUtils.isNotBlank(token)){
                String key =Constants.ACT_TOKEN_KEY+token;
                Object obj = redisTemplateService.get(key);
                if (obj != null) {
                    return CommonResult.success(obj);
                }else {
                    return CommonResult.failed("您的链接失效了，请查看最新邮件或联系管理员");
                }
            }
        }else {
            if (StringUtils.isNotBlank(token)) {
                // 手机号登录
                Object loginUserValue = redisTemplateService.get(token);
                if (loginUserValue != null) {
                    LoginUserInfo loginUserInfo = (LoginUserInfo) loginUserValue;
                    SystemUserInfoExtendVo systemUserInfo = systemUserInfoService.getSystemUserInfoByUserId(loginUserInfo.getId().toString());
                    String mobile = systemUserInfo.getMobile();
                    systemUserInfo.setMobile(DesensitizeUtil.aesDecrypt(mobile));
                    return CommonResult.success(systemUserInfo);
                }else {
                    return CommonResult.failed("请重新登录");
                }
            }
        }
        return CommonResult.failed("token为空");
    }


    @ApiOperation(value = "查询用户账号登录状态")
    @GetMapping("/getUserLoginResult")
    public CommonResult getUserLoginResult(String accountName) {
        String key = Constants.LOGIN_GLOBAL_KEY + accountName;
        String systemUserInfoValue = redisTemplateService.get(key) == null ? "" : redisTemplateService.get(key).toString();
        if(StringUtils.isNotEmpty(systemUserInfoValue)){
            return CommonResult.success(true);
        }
        return CommonResult.success(null);
    }
    
    @ApiOperation(value = "查询用户激活状态")
    @GetMapping("/getSystemUserActiveStatus")
    public CommonResult getSystemUserActiveStatus(String accountName) {
        SystemUserInfoVo systemUserInfo = systemUserInfoService.getSystemUserInfoByAccountName(accountName);
        if(systemUserInfo != null){
            boolean systemUserActive = systemUserInfo.getActiveStatus() != null && systemUserInfo.getActiveStatus();
            return CommonResult.success(systemUserActive);
        }
        return CommonResult.success(false);
    }

    @ApiOperation(value = "刷新token")
    @GetMapping("/refreshToken")
    public CommonResult refreshToken(HttpServletRequest request) {
        String token = request.getHeader(tokenHeader);
        String refreshToken = systemUserLoginService.refreshToken(token);
        if (refreshToken == null) {
            return CommonResult.failed("token过期");
        }
        Map<String, String> tokenMap = new HashMap<>();
        tokenMap.put("token", tokenHead.concat("").concat(token));
        return CommonResult.success(tokenMap);
    }

    @NoRepeatSubmit
    @ApiOperation(value = "手机验证码登录")
    @Log(title = "使用手机号和验证码登录", businessType = BusinessType.OTHER)
    @PostMapping("/verificationCodeLogin")
    public CommonResult verificationCodeLogin(HttpServletRequest request, HttpServletResponse response, @Validated @RequestBody UserVerificationCodeParam userVerificationCodeParam) {
        SystemUserInfoVo systemUserInfoVo = systemUserInfoService.getSystemUserInfoByAccountName(userVerificationCodeParam.getMobile());
        if(systemUserInfoVo == null){
            return CommonResult.failed(ResultCode.SYSTEM_USER_MOBILE_NOT_FOUND.getMessage());
        }
        systemUserLoginService.checkVerificationCode(userVerificationCodeParam.getMobile(), userVerificationCodeParam.getCode());
        String loginPlatformId = getTenantUserFromLoginSource(userVerificationCodeParam.getLoginSource());
        SystemTenantUser systemTenantUser = systemTenantUserService.getSystemTenantUserByUserId(systemUserInfoVo.getId().toString(), userVerificationCodeParam.getTenantId(), loginPlatformId);
        if(systemTenantUser == null){
            return CommonResult.validateFailed("当前平台登录手机号: " + userVerificationCodeParam.getMobile() + "不存在");
        }
        String token = systemUserLoginService.getSystemLoginTokenByMobile(userVerificationCodeParam.getMobile(), userVerificationCodeParam.getTenantId(), userVerificationCodeParam.getPlatformId());
        //移动端自注册不校验是否激活
        if(systemUserInfoVo.getRegisterFrom().equals(Constants.USER_TYPE_VALUE_04)){
            systemUserInfoVo.setActiveStatus(true);
        }
        systemUserInfoVo.setActiveStatus(systemTenantUser.getActiveStatus());
        Map<String, Object> tokenMap = new HashMap<>();
        tokenMap.put("token", token);
        tokenMap.put("systemUserActiveStatus", systemUserInfoVo.getActiveStatus());
        redisTemplateService.del(RedisKeyContants.SEND_MOBILE_MESSAGE_CODE + userVerificationCodeParam.getMobile());

        // 登录成功后，将userId设置到header属性X-User-Id和session中
        String userId = systemUserInfoVo.getId().toString();

        // 设置到响应头中
        response.setHeader("X-User-Id", userId);
        response.setHeader("X-User-Name", systemUserInfoVo.getUsername());
        response.setHeader("X-Real-Name", systemUserInfoVo.getRealName() != null ? systemUserInfoVo.getRealName() : systemUserInfoVo.getUsername());

        // 设置到session中
        HttpSession session = request.getSession();
        session.setAttribute("userId", userId);
        session.setAttribute("userName", systemUserInfoVo.getUsername());
        session.setAttribute("realName", systemUserInfoVo.getRealName() != null ? systemUserInfoVo.getRealName() : systemUserInfoVo.getUsername());

        log.info("手机验证码登录成功 - userId: {}, userName: {}, 已设置到header和session中", userId, systemUserInfoVo.getUsername());

        return CommonResult.success(tokenMap);
    }
    
    
    @NoRepeatSubmit
    @ApiOperation(value = "统一平台用户登录")
    @PostMapping("/externalSystemUserLogin")
    @Log(title = "统一平台完善注册用户", businessType = BusinessType.INSERT)
    public CommonResult externalSystemUserLogin(@RequestParam String ticket, @RequestParam(defaultValue = "unified-user-platform") String loginSource) {
        if(!Constants.USER_LOGIN_RESOURCE_6.equals(loginSource)){
            return CommonResult.failed("登录来源未授权");
        }
        Map<String, Object> tokenMap = new HashMap<>();
        CommonResult commonResult = systemUserInfoService.registerExternalSystemUserInfo(ticket, loginSource);
        if(ResultCode.SUCCESS.getCode() != commonResult.getCode()){
            return commonResult;
        }
        
        if(commonResult.getData() != null){
            ExternalRegisterUserParam externalRegisterUserParam = (ExternalRegisterUserParam) commonResult.getData();
            tokenMap.put("loginSource", loginSource);
            tokenMap.put("loginMobile", externalRegisterUserParam.getMobileNumber());
            String accessToken = RandomStringUtils.randomAlphanumeric(64);
            redisTemplateService.set(Constants.LOGIN_ACCESS_TOKEN_KEY.concat(externalRegisterUserParam.getMobileNumber()), accessToken, 1800);
            tokenMap.put("accessToken", accessToken);
        }
        return CommonResult.success(tokenMap);
    }
    
    
    @NoRepeatSubmit
    @ApiOperation(value = "科研项目管理用户登录")
    @PostMapping("/researchProjectUserLogin")
    @Log(title = "科研项目管理用户", businessType = BusinessType.INSERT)
    public CommonResult researchProjectUserLogin(@RequestParam String mobile, @RequestParam(defaultValue = "research-project-manage") String loginSource) {
        if(!Constants.USER_LOGIN_RESOURCE_6.equals(loginSource)){
            return CommonResult.failed("登录来源未授权");
        }
        Map<String, Object> tokenMap = new HashMap<>();
        CommonResult commonResult = systemUserInfoService.registerResearchProjectUserUserInfo(mobile, loginSource);
        if(ResultCode.SUCCESS.getCode() != commonResult.getCode()){
            return commonResult;
        }
        if(commonResult.getData() != null){
            ExternalRegisterUserParam externalRegisterUserParam = (ExternalRegisterUserParam) commonResult.getData();
            tokenMap.put("loginSource", loginSource);
            tokenMap.put("loginMobile", externalRegisterUserParam.getMobileNumber());
            String accessToken = RandomStringUtils.randomAlphanumeric(64);
            redisTemplateService.set(Constants.LOGIN_ACCESS_TOKEN_KEY.concat(externalRegisterUserParam.getMobileNumber()), accessToken, 1800);
            tokenMap.put("accessToken", accessToken);
        }
        return CommonResult.success(tokenMap);
    }
    
    @ApiOperation(value = "获取统一登录后的token")
    @GetMapping("/getUserLoginTokenByAccessToken")
    public CommonResult getUserLoginTokenByAccessToken(@RequestParam String accessToken, @RequestParam String loginMobile, @RequestParam(defaultValue = "unified-user-platform") String loginSource) {
        Object accessToeknValue = redisTemplateService.get(Constants.LOGIN_ACCESS_TOKEN_KEY.concat(loginMobile));
        if(accessToeknValue != null && !accessToeknValue.equals(accessToken)){
            return CommonResult.failed("accessToken错误");
        }
        String loginTenantId = getTenantIdFromLoginSource();
        String loginPlatformId = getPlatformIdFromLoginSource(loginSource);
        //String aesMobile = DesensitizeUtil.aesEncrypt(loginMobile);
        String token = systemUserLoginService.getSystemLoginTokenByMobile(loginMobile, loginTenantId, loginPlatformId);
        redisTemplateService.del(RedisKeyContants.SEND_MOBILE_MESSAGE_CODE + loginMobile);
        redisTemplateService.del(Constants.LOGIN_ACCESS_TOKEN_KEY.concat(loginMobile));
        return CommonResult.success(token);
    }


    @ApiOperation(value = "根据token获取用户信息")
    @GetMapping("/getUserInfoByToken")
    public CommonResult getUserInfoByToken(HttpServletRequest request) {
        LoginUserInfo loginUserInfo = systemUserTokenService.getSystemLoginUserInfo(request);
        return CommonResult.success(loginUserInfo);
    }
    
    @ApiOperation(value = "根据token获取用户信息-健康管理系统")
    @GetMapping("/getSystemUserInfoByTokenValue")
    public CommonResult getSystemUserInfoByTokenValue(String token) {
        LoginUserInfo loginUserInfo = systemUserTokenService.getSystemUserInfoByTokenValue(token);
        if(loginUserInfo == null){ return CommonResult.unauthorized("token失效");}
        return CommonResult.success(loginUserInfo);
    }

    @ApiOperation(value = "获取当前登录用户信息")
    @GetMapping("/getCurrentLoginUserInfo")
    public CommonResult getCurrentLoginUserInfo(Principal principal, String groupName, String loginSource) {
        if(principal == null){
            return CommonResult.success(null);
        }
        Map<String, Object> data = new HashMap<>();
        LoginUserInfo loginUserInfo = SecurityUtils.getLoginUser();
        //BaseSystemUser baseSystemUser = loginUserInfo.getBaseSystemUser();
        data.put("loginUserInfo",loginUserInfo);
        data.put("roles",systemRoleService.selectRolePermissionByUserId(loginUserInfo.getId()));
        data.put("menuList", systemMenuService.getSystemUserMenuList(loginUserInfo.getId().toString(), getUserLoginSourceType(loginSource), groupName, true));
        SystemUserInfoVo systemUserInfo = systemUserInfoService.getSystemUserInfoByAccountName(loginUserInfo.getUsername());
        if(StringUtils.isNotBlank(systemUserInfo.getMobile())){systemUserInfo.setMobile(DesensitizeUtil.aesDecrypt(systemUserInfo.getMobile()));}
        data.put("systemUserInfo",systemUserInfo);
        // 企业管理员显示全部Tab菜单； 其他角色用户按照配置显示
        List<SystemMenuVo> systemPlatformMenuList = systemMenuService.getSystemPlatformMenuNameList(loginUserInfo.getId().toString(), "", SecurityUtils.getSystemTenantId());
        data.put("systemPlatformMenuList",systemPlatformMenuList);
        String systemTenantId = SecurityUtils.getSystemTenantId();
        Map<String, Object> tenantInfo = tenantInformationService.getTenantInfoById(systemTenantId);
        data.put("systemTenant",tenantInfo);
        return CommonResult.success(data);
    }

    private String getUserLoginSourceType(String loginSource) {
        if(Constants.USER_LOGIN_RESOURCE_1.equals(loginSource)){
            return Constants.SYSTEM_MENU_TYPE_02;
        }
        if(Constants.USER_LOGIN_RESOURCE_2.equals(loginSource)){
            return Constants.SYSTEM_MENU_TYPE_01;
        }
        if(Constants.USER_LOGIN_RESOURCE_3.equals(loginSource)){
            return Constants.SYSTEM_MENU_TYPE_07;
        }
        return Constants.SYSTEM_MENU_TYPE_02;
    }
}
