package com.haoys.user.controller;

import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.bussiness.RedisKeyContants;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.common.util.DesensitizeUtil;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.system.SystemUserAuthParam;
import com.haoys.user.domain.vo.system.SystemDepartmentVo;
import com.haoys.user.domain.vo.system.SystemUserAuthVo;
import com.haoys.user.domain.vo.system.SystemUserInfoVo;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.model.SystemRole;
import com.haoys.user.model.SystemTenantUser;
import com.haoys.user.model.SystemUserInfo;
import com.haoys.user.service.SystemRoleService;
import com.haoys.user.service.SystemTenantUserService;
import com.haoys.user.service.SystemUserInfoService;
import com.haoys.user.service.TenantDepartmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Api(tags = "企业部门用户管理")
@RestController
@RequestMapping("/tenantDepartment")
public class TenantUserManageController extends BaseController {
    @Autowired
    private SystemTenantUserService tenantUserService;
    @Autowired
    private SystemRoleService systemRoleService;
    @Autowired
    private TenantDepartmentService tenantDepartmentService;
    @Autowired
    private SystemUserInfoService systemUserInfoService;
    @Autowired
    private RedisTemplateService redisTemplateService;

    @ApiOperation("获取部门tree")
    @GetMapping("/departmenList")
    public CommonResult<Map<String, Object>> list() {
        Map<String, Object> dateMap = tenantDepartmentService.getDepartmentList(SecurityUtils.getSystemTenantId());
        return CommonResult.success(dateMap);
    }


    @ApiOperation(value = "添加部门")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "name", value = "部门名称", dataType = "string",required = true),
        @ApiImplicitParam(name = "personCharge", value = "负责人", dataType = "string"),
        @ApiImplicitParam(name = "description", value = "部门描述", dataType = "string")
    })
    @Log(title = "添加部门", businessType = BusinessType.INSERT)
    @RequestMapping(value = "/addDepartmen", method = RequestMethod.POST)
    public CommonResult addDepartmen(@RequestParam(value="name") String name,String personCharge, String description) {
        int count = tenantDepartmentService.addDepartment(name, personCharge, description);
        if(count == -1){
            return CommonResult.failed("部门名称不可重复");
        }
        return returnResult(count);
    }

    @ApiOperation(value = "编辑部门")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "name", value = "部门名称", dataType = "string",required = true),
        @ApiImplicitParam(name = "personCharge", value = "负责人", dataType = "string"),
        @ApiImplicitParam(name = "description", value = "部门描述", dataType = "string")
    })
    @Log(title = "编辑部门", businessType = BusinessType.UPDATE)
    @RequestMapping(value = "/editDepartmen/{id}", method = RequestMethod.POST)
    public CommonResult editDepartment(@PathVariable Long id, @RequestParam(value="name") String name,
                                     @RequestParam(required = false, value="personCharge") String personCharge,
                                     @RequestParam(required = false, value="description") String description) {
        return returnResult(tenantDepartmentService.editDepartment(id, name, personCharge, description));
    }

    @ApiOperation("删除企业部门")
    //@PreAuthorize("@ss.hasPermi('system:role:remove')")
    @Log(title = "删除企业部门", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{id}")
    public CommonResult<Object> delete(@PathVariable Long id) {
        CustomResult data = new CustomResult();
        String message = tenantDepartmentService.deleteDepartment(id);
        if (!BusinessConfig.RETURN_MESSAGE_DEFAULT.equals(message)) {
            return CommonResult.failed(message);
        }
        return CommonResult.success(data.getData());
    }


    @ApiOperation("查询企业部门用户")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "企业部门id", dataType = "string"),
        @ApiImplicitParam(name = "realName", value = "用户姓名", dataType = "string"),
        @ApiImplicitParam(name = "username", value = "用户名", dataType = "string"),
        @ApiImplicitParam(name = "status", value = "启用状态0/1", dataType = "int"),
        @ApiImplicitParam(name = "activeStatus", value = "激活状态", dataType = "boolean"),
    })
    @GetMapping("/getTenantUserListForPage")
    public CommonResult<CommonPage<SystemUserInfoVo>> getTenantUserListForPage(@RequestParam(value="enterpriseId") String enterpriseId,
                                                                               @RequestParam(value="realName",required=false) String realName,
                                                                               @RequestParam(value="username",required=false) String username,
                                                                               @RequestParam(value="status",required=false) Integer status,
                                                                               @RequestParam(value="activeStatus",required=false) Boolean activeStatus,
                                                                               @RequestParam(value="lockStatus",required=false) Boolean lockStatus,
                                                                               @RequestParam(value="pageNum",defaultValue="1") Integer pageNum,
                                                                               @RequestParam(value="pageSize",defaultValue="10") Integer pageSize) {
        CommonPage<SystemUserInfoVo> list = tenantDepartmentService.getTenantUserListForPage(enterpriseId, realName, username, status, activeStatus,lockStatus, pageNum, pageSize);
        return CommonResult.success(list);

    }
    

    @ApiOperation("企业部门用户启用、停用")
    @Log(title = "人员状态", businessType = BusinessType.UPDATE)
    @PostMapping("/editStatus/{id}")
    public CommonResult editStatus(@PathVariable Long id) {
        return returnResult(tenantDepartmentService.editDepartmentUserStatus(id));
    }

    @Log(title = "企业部门-添加和修改用户", businessType = BusinessType.INSERT)
    @ApiOperation("企业部门-添加和修改用户")
    @PostMapping("/saveSystemUser")
    public CommonResult saveSystemUser(@RequestBody SystemUserAuthParam systemUserAuthParam){
        systemUserAuthParam.setCreateUserId(getUserId());
        CustomResult customResult = tenantDepartmentService.saveSystemUserAndProjectUser(systemUserAuthParam);
        return CommonResult.success(customResult);
    }

    @ApiOperation("查询企业用户信息")
    @PostMapping("/getSystemUserInfo")
    public CommonResult getSystemUserInfo(String systemUserId){
        SystemUserAuthVo systemUserAuthVo = tenantDepartmentService.getSystemUserInfo(systemUserId);
        return CommonResult.success(systemUserAuthVo);
    }

    @ApiOperation("查询企业部门")
    @GetMapping("/getDepartmenDrop")
    public CommonResult<List<SystemDepartmentVo>> getDepartmenDrop() {
        List<SystemDepartmentVo> sdv = tenantDepartmentService.getDepartmentDrop(SecurityUtils.getSystemTenantId());
        return CommonResult.success(sdv);
    }

    @ApiOperation("查询企业角色列表")
    @GetMapping("/roleList")
    public CommonResult<List<SystemRole>> roleList() {
        List<SystemRole> roleList = systemRoleService.getSystemRoleList();
        return CommonResult.success(roleList);
    }

    @ApiOperation("删除企业用户")
    @Log(title = "删除企业用户", businessType = BusinessType.DELETE)
    @GetMapping("remove/{userId}")
    public CommonResult deleteSystemUser(@PathVariable("userId") String userId){
        CustomResult customResult = tenantDepartmentService.deleteSystemUserById(userId);
        if (!BusinessConfig.RETURN_MESSAGE_DEFAULT.equals(customResult.getMessage())) {
            return CommonResult.failed(customResult.getMessage());
        }
        return CommonResult.success(customResult.getMessage());

    }

    @Log(title = "解除锁定", businessType = BusinessType.DELETE)
    @ApiOperation("解除锁定")
    @GetMapping("unlock/{userId}")
    public CommonResult deleteUnlock(@PathVariable("userId") String userId){
        SystemUserInfo systemUserInfo = systemUserInfoService.getUserBaseInfo(Long.valueOf(userId));
        if(systemUserInfo != null){
            redisTemplateService.del(RedisKeyContants.EDC_LOGIN_USER_COUNT.concat(systemUserInfo.getUsername()));
            redisTemplateService.del(RedisKeyContants.EDC_LOGIN_USER_LOCK.concat(systemUserInfo.getUsername()));

            if(StringUtils.isNotEmpty(systemUserInfo.getMobile())){
                redisTemplateService.del(RedisKeyContants.SEND_MOBILE_MESSAGE_CODE + DesensitizeUtil.aesDecrypt(systemUserInfo.getMobile()));
                redisTemplateService.del(RedisKeyContants.EDC_LOGIN_USER_LOCK.concat(DesensitizeUtil.aesDecrypt(systemUserInfo.getMobile())));
            }
        }
        SystemTenantUser tenantUser = tenantUserService.getSystemTenantUserByUserId(userId);
        if (tenantUser!=null) {
            tenantUser.setLockStatus(false);
            tenantUser.setLockTime(null);
            tenantUserService.updateSystemTenantUser(tenantUser);
        }
        return CommonResult.success("");
    }
}
