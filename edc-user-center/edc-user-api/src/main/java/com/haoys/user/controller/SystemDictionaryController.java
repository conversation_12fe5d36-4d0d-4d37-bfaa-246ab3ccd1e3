package com.haoys.user.controller;


import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.bean.BeanUtils;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.dto.SystemDictionaryParam;
import com.haoys.user.domain.param.dict.DictParamQuery;
import com.haoys.user.domain.vo.system.DictionaryVo;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.model.Dictionary;
import com.haoys.user.model.DictionaryFrom;
import com.haoys.user.service.DictionaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api(tags = "系统字典管理", value = "DictionaryController")
@RequestMapping("/dictionary")
public class SystemDictionaryController extends BaseController {

    @Autowired
    private DictionaryService dictionaryService;

    @ApiOperation("查询字典分页列表")
    //@PreAuthorize("@ss.hasPermi('system:dict:list')")
    @PostMapping(value = "/getDictionaryListForPage")
    public CommonResult<CommonPage<DictionaryVo>> getDictionaryListForPage(@RequestBody DictParamQuery param) {
        CommonPage<DictionaryVo> dictionaryList = dictionaryService.getDictionaryListForPage(param);
        return CommonResult.success(dictionaryList);
    }

    @ApiOperation(value = "新增系统字典")
    @RequestMapping(value = "/createDictionary", method = RequestMethod.POST)
    public CommonResult<Object> createDictionary(@Validated @RequestBody SystemDictionaryParam systemDictionaryParam) {
        return dictionaryService.saveDictionary(systemDictionaryParam);
    }

    @ApiOperation(value = "编辑-查询字典信息")
    @RequestMapping(value = "/getDictionaryInfo", method = RequestMethod.GET)
    public CommonResult<Dictionary> getDictionaryInfo(String id) {
        return CommonResult.success(dictionaryService.getDictionaryInfo(id));
    }


    @ApiOperation(value = "查询指定字典明细列表-不分页")
    @GetMapping(value = "/getDictionaryListByParentId")
    public CommonResult<List<DictionaryVo>> getDictionaryListByParentId(String parentId, String parentCode, String name) {
        List<Dictionary> dataList = dictionaryService.getDictionaryListByParentId(parentId, parentCode, name);
        List<DictionaryVo> dictionaryVoList = new ArrayList<>();
        for(Dictionary dl : dataList){
            DictionaryVo vo = new DictionaryVo();
            BeanUtils.copyProperties(dl, vo);
            dictionaryVoList.add(vo);
        }
        return CommonResult.success(dictionaryVoList);
    }
    
    @ApiOperation(value = "根据多个父级id查询字典明细列表-不分页")
    @GetMapping(value = "/getDictionaryListByParentIds")
    public CommonResult<Map<String,Object>> getDictionaryListByParentIds(String parentIds, String parentCodes, String name) {
        Map<String,Object> dataMap = new HashMap<>();
        if(StringUtils.isEmpty(parentIds)){
            String[] parentIdArray = parentIds.split(",");
            for (String parentId : parentIdArray) {
                List<DictionaryVo> dictionaryVoList = new ArrayList<>();
                List<Dictionary> dataList = dictionaryService.getDictionaryListByParentId(parentId, parentCodes, name);
                for(Dictionary dictionary : dataList){
                    DictionaryVo dictionaryVo = new DictionaryVo();
                    BeanUtils.copyProperties(dictionary, dictionaryVo);
                    dictionaryVoList.add(dictionaryVo);
                }
                dataMap.put(parentId,dictionaryVoList);
            }
        }
        return CommonResult.success(dataMap);
    }

    @ApiOperation(value = "修改系统字典")
    //@PreAuthorize("@ss.hasPermi('system:dict:edit')")
    @RequestMapping(value = "/upDictionary", method = RequestMethod.POST)
    public CommonResult<Object> upDictionary(@Validated @RequestBody SystemDictionaryParam systemDictionaryParam) {
        return dictionaryService.updateDictionary(systemDictionaryParam);
    }

    @ApiOperation(value = "根据id删除字典信息")
    @RequestMapping(value = "/removeById", method = RequestMethod.GET)
    public CommonResult<Object> removeSystemDictionaryById(String dictId) {
        return dictionaryService.removeSystemDictionaryById(dictId);
    }

    @ApiOperation(value = "根据字典id启用/停用")
    @GetMapping(value = "/enableOrUnable")
    public CommonResult<Object> enableOrUnable(String id,String status) {
        return dictionaryService.enableOrUnable(id,status);
    }

    @ApiOperation("引用字典插入信息")
    //@PreAuthorize("@ss.hasPermi('system:role:add')")
    @Log(title = "引用字典插入信息", businessType = BusinessType.INSERT)
    @PostMapping("/saveDictionaryFrom")
    public CommonResult saveDictionaryFrom(@RequestParam(value="id") Long dictionaryId) {
        DictionaryFrom dictionaryFrom = new DictionaryFrom();
        dictionaryFrom.setDictionaryId(dictionaryId);
        dictionaryFrom.setCreateUser(getUserId());
        return returnResult(dictionaryService.saveDictionaryFrom(dictionaryFrom));
    }

}
