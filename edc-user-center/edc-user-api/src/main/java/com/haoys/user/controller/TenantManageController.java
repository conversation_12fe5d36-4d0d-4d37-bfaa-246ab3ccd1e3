package com.haoys.user.controller;

import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.domain.param.system.SystemTenantParam;
import com.haoys.user.domain.vo.UploadFileResultVo;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.service.ProjectTesteeFileService;
import com.haoys.user.service.TenantInformationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Api(tags = "企业信息")
@RestController
@RequestMapping("/tenantInformation")
public class TenantManageController extends BaseController {

    @Autowired
    private TenantInformationService tenantInformationService;

    @Autowired
    private ProjectTesteeFileService projectTesteeFileService;

    @ApiOperation("查询企业信息")
    @GetMapping("/getTenantInfoById")
    public CommonResult<Map<String, Object>> getTenantInfoById(String tenantId) {
        String systemTenantId = SecurityUtils.getSystemTenantId();
        return CommonResult.success(tenantInformationService.getTenantInfoById(systemTenantId));
    }

    @ApiOperation("企业logo上传")
    @Log(title = "企业logo上传", businessType = BusinessType.INSERT)
    @PostMapping(value = "/uploadTenantLogoFile")
    public CommonResult uploadTenantLogoFile(@RequestParam("file") MultipartFile file, String tenantId) throws IOException {
        long size = file.getSize();
        if(size >= 1048576*10) {
            return CommonResult.failed(ResultCode.TENANT_INFORMATION_LOGO_SIZE.getMessage());
        }
        List<UploadFileResultVo> uploadFileResultVo = projectTesteeFileService.saveUploadSystemFileResource(file, null, tenantId);
        return CommonResult.success(uploadFileResultVo);
    }

    @ApiOperation("修改企业信息")
    @Log(title = "修改企业信息", businessType = BusinessType.UPDATE)
    @PostMapping("/editTenantInformation")
    public CommonResult update(@RequestBody SystemTenantParam systemTenantParam) {
        tenantInformationService.updateSystemTenantInfo(getUserId(), systemTenantParam);
        return CommonResult.success(null);
    }

}
