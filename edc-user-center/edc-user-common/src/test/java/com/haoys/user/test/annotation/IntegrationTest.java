package com.haoys.user.test.annotation;

import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.lang.annotation.*;

/**
 * 集成测试注解
 * 
 * <p>用于标记集成测试类，自动配置测试环境</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-25
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE",
    "spring.datasource.driver-class-name=org.h2.Driver",
    "spring.datasource.username=sa",
    "spring.datasource.password=",
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "spring.jpa.show-sql=false",
    "spring.redis.host=localhost",
    "spring.redis.port=6379",
    "spring.redis.database=1",
    "logging.level.com.haoys=DEBUG",
    "logging.level.org.springframework.security=DEBUG"
})
public @interface IntegrationTest {
    /**
     * 是否启用Web环境
     */
    boolean webEnvironment() default true;
    
    /**
     * 是否启用数据库
     */
    boolean database() default true;
    
    /**
     * 是否启用Redis
     */
    boolean redis() default true;
}
