package com.haoys.user.test.annotation;

import org.junit.jupiter.api.condition.EnabledIfSystemProperty;

import java.lang.annotation.*;

/**
 * 基于Profile的条件测试注解
 * 
 * <p>只有在指定的Profile激活时才执行测试</p>
 * 
 * 使用方法：
 * 1. @ConditionalOnProfile("integration") - 只在integration profile时执行
 * 2. @ConditionalOnProfile("unit") - 只在unit profile时执行
 * 3. 运行时设置：-Dspring.profiles.active=integration
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@EnabledIfSystemProperty(named = "spring.profiles.active", matches = ".*")
public @interface ConditionalOnProfile {
    /**
     * 需要激活的Profile名称
     */
    String value();
}
