package com.haoys.user.test.annotation;

import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.ActiveProfiles;

import java.lang.annotation.*;

/**
 * 单元测试注解
 * 
 * <p>用于标记单元测试类，自动配置Mock环境</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-25
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@ExtendWith(MockitoExtension.class)
@ActiveProfiles("test")
public @interface UnitTest {
}
