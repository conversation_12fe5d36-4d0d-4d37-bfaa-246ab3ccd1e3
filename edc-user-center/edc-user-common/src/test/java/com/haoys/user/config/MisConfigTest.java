package com.haoys.user.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 文件上传配置测试类
 * 
 * <p>测试文件上传路径配置的正确性和可靠性</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@DisplayName("文件上传配置测试")
class MisConfigTest {

    private MisConfig misConfig;

    @BeforeEach
    void setUp() {
        misConfig = new MisConfig();
        // 模拟配置注入
        ReflectionTestUtils.setField(misConfig, "uploadFolder", "/webserver/upload/");
    }

    @Test
    @DisplayName("测试获取文件上传根路径")
    void testGetProfile() {
        String profile = misConfig.getProfile();
        assertEquals("/webserver/upload/", profile, "文件上传根路径应该正确");
    }

    @Test
    @DisplayName("测试获取导入文件路径")
    void testGetImportPath() {
        String importPath = misConfig.getImportPath();
        assertEquals("/webserver/upload/import/", importPath, "导入文件路径应该正确");
        assertTrue(importPath.endsWith("/"), "路径应该以斜杠结尾");
    }

    @Test
    @DisplayName("测试获取下载访问路径")
    void testGetDownloadPath() {
        String downloadPath = misConfig.getDownloadPath();
        assertEquals("/file/", downloadPath, "下载访问路径应该正确");
    }

    @Test
    @DisplayName("测试获取普通文件上传路径")
    void testGetUploadPath() {
        String uploadPath = misConfig.getUploadPath();
        assertEquals("/webserver/upload/upload/", uploadPath, "普通文件上传路径应该正确");
        assertTrue(uploadPath.contains("upload"), "路径应该包含upload目录");
    }

    @Test
    @DisplayName("测试获取临时文件路径")
    void testGetTempPath() {
        String tempPath = misConfig.getTempPath();
        assertEquals("/webserver/upload/temp/", tempPath, "临时文件路径应该正确");
        assertTrue(tempPath.contains("temp"), "路径应该包含temp目录");
    }

    @Test
    @DisplayName("测试获取用户头像路径")
    void testGetAvatarPath() {
        String avatarPath = misConfig.getAvatarPath();
        assertEquals("/webserver/upload/avatar/", avatarPath, "用户头像路径应该正确");
        assertTrue(avatarPath.contains("avatar"), "路径应该包含avatar目录");
    }

    @Test
    @DisplayName("测试获取项目文件路径")
    void testGetProjectPath() {
        String projectPath = misConfig.getProjectPath();
        assertEquals("/webserver/upload/project/", projectPath, "项目文件路径应该正确");
        assertTrue(projectPath.contains("project"), "路径应该包含project目录");
    }

    @Test
    @DisplayName("测试不同配置值的路径生成")
    void testDifferentConfigValues() {
        // 测试不同的配置值
        String[] testPaths = {
            "/data/files/",
            "/opt/upload/",
            "/home/<USER>/uploads/",
            "C:\\uploads\\"  // Windows路径
        };

        for (String testPath : testPaths) {
            MisConfig testConfig = new MisConfig();
            ReflectionTestUtils.setField(testConfig, "uploadFolder", testPath);

            assertEquals(testPath, testConfig.getProfile(), 
                "配置路径应该正确设置: " + testPath);
            assertEquals(testPath + "import/", testConfig.getImportPath(), 
                "导入路径应该正确生成: " + testPath);
            assertEquals(testPath + "upload/", testConfig.getUploadPath(), 
                "上传路径应该正确生成: " + testPath);
        }
    }

    @Test
    @DisplayName("测试空配置值处理")
    void testEmptyConfigValue() {
        MisConfig emptyConfig = new MisConfig();
        ReflectionTestUtils.setField(emptyConfig, "uploadFolder", "");

        String profile = emptyConfig.getProfile();
        assertEquals("", profile, "空配置应该返回空字符串");

        String importPath = emptyConfig.getImportPath();
        assertEquals("import/", importPath, "空配置的导入路径应该只包含子目录");
    }

    @Test
    @DisplayName("测试null配置值处理")
    void testNullConfigValue() {
        MisConfig nullConfig = new MisConfig();
        ReflectionTestUtils.setField(nullConfig, "uploadFolder", null);

        assertThrows(NullPointerException.class, () -> {
            nullConfig.getProfile();
        }, "null配置应该抛出异常或有适当的处理");
    }

    @Test
    @DisplayName("测试路径拼接的正确性")
    void testPathConcatenation() {
        // 测试各种路径格式的拼接
        String[] basePaths = {
            "/webserver/upload",      // 无尾部斜杠
            "/webserver/upload/",     // 有尾部斜杠
            "/webserver/upload//",    // 多个斜杠
        };

        for (String basePath : basePaths) {
            MisConfig testConfig = new MisConfig();
            ReflectionTestUtils.setField(testConfig, "uploadFolder", basePath);

            String importPath = testConfig.getImportPath();
            String uploadPath = testConfig.getUploadPath();

            // 验证路径拼接没有双斜杠问题（除非是有意的）
            assertFalse(importPath.contains("//import"), 
                "路径拼接不应该产生双斜杠: " + importPath);
            assertFalse(uploadPath.contains("//upload"), 
                "路径拼接不应该产生双斜杠: " + uploadPath);
        }
    }

    @Test
    @DisplayName("测试路径安全性")
    void testPathSecurity() {
        // 测试潜在的路径遍历攻击
        String[] maliciousPaths = {
            "../../../etc/",
            "..\\..\\windows\\",
            "/etc/../home/",
            "C:\\..\\..\\windows\\"
        };

        for (String maliciousPath : maliciousPaths) {
            MisConfig testConfig = new MisConfig();
            ReflectionTestUtils.setField(testConfig, "uploadFolder", maliciousPath);

            String profile = testConfig.getProfile();
            assertEquals(maliciousPath, profile, 
                "配置应该按原样返回（安全检查应该在使用时进行）");
        }
    }

    @Test
    @DisplayName("测试配置的一致性")
    void testConfigConsistency() {
        // 验证所有路径方法都基于同一个根路径
        String baseProfile = misConfig.getProfile();

        assertTrue(misConfig.getImportPath().startsWith(baseProfile), 
            "导入路径应该基于根路径");
        assertTrue(misConfig.getUploadPath().startsWith(baseProfile), 
            "上传路径应该基于根路径");
        assertTrue(misConfig.getTempPath().startsWith(baseProfile), 
            "临时路径应该基于根路径");
        assertTrue(misConfig.getAvatarPath().startsWith(baseProfile), 
            "头像路径应该基于根路径");
        assertTrue(misConfig.getProjectPath().startsWith(baseProfile), 
            "项目路径应该基于根路径");
    }

    @Test
    @DisplayName("测试路径的唯一性")
    void testPathUniqueness() {
        // 验证不同类型的路径是唯一的
        String[] paths = {
            misConfig.getImportPath(),
            misConfig.getUploadPath(),
            misConfig.getTempPath(),
            misConfig.getAvatarPath(),
            misConfig.getProjectPath()
        };

        for (int i = 0; i < paths.length; i++) {
            for (int j = i + 1; j < paths.length; j++) {
                assertNotEquals(paths[i], paths[j], 
                    "不同类型的路径应该是唯一的: " + paths[i] + " vs " + paths[j]);
            }
        }
    }
}
