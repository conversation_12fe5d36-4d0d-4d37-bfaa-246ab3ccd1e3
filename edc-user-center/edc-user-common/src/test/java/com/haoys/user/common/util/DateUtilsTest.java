package com.haoys.user.common.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;

/**
 * DateUtils 测试类
 * 测试 parseToDate 方法的健壮性和准确性
 */
public class DateUtilsTest {

    @Test
    public void testParseToDate_NullInput() {
        // 测试null输入
        assertNull(DateUtils.parseToDate(null));
    }

    @Test
    public void testParseToDate_DateTypes() {
        // 测试Date类型
        Date now = new Date();
        assertEquals(now, DateUtils.parseToDate(now));

        // 测试SQL Date类型
        java.sql.Date sqlDate = new java.sql.Date(System.currentTimeMillis());
        assertNotNull(DateUtils.parseToDate(sqlDate));

        // 测试Timestamp类型
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        assertNotNull(DateUtils.parseToDate(timestamp));
    }

    @Test
    public void testParseToDate_CalendarType() {
        // 测试Calendar类型
        Calendar calendar = Calendar.getInstance();
        Date result = DateUtils.parseToDate(calendar);
        assertNotNull(result);
        assertEquals(calendar.getTime(), result);
    }

    @Test
    public void testParseToDate_Java8TimeTypes() {
        // 测试LocalDate类型
        LocalDate localDate = LocalDate.now();
        Date result = DateUtils.parseToDate(localDate);
        assertNotNull(result);

        // 测试LocalDateTime类型
        LocalDateTime localDateTime = LocalDateTime.now();
        Date result2 = DateUtils.parseToDate(localDateTime);
        assertNotNull(result2);
    }

    @Test
    public void testParseToDate_NumberTypes() {
        // 测试有效的时间戳
        long validTimestamp = System.currentTimeMillis();
        Date result = DateUtils.parseToDate(validTimestamp);
        assertNotNull(result);
        assertEquals(validTimestamp, result.getTime());

        // 测试Integer类型的时间戳
        Integer intTimestamp = (int) (System.currentTimeMillis() / 1000);
        Date result2 = DateUtils.parseToDate(intTimestamp * 1000L);
        assertNotNull(result2);

        // 测试无效的时间戳（超出范围）
        long invalidTimestamp = 5000000000000L; // 超出2100年
        assertNull(DateUtils.parseToDate(invalidTimestamp));
    }

    @Test
    public void testParseToDate_StringTypes() {
        // 测试各种字符串格式
        assertNotNull(DateUtils.parseToDate("2023-12-25"));
        assertNotNull(DateUtils.parseToDate("2023-12-25 15:30:45"));
        assertNotNull(DateUtils.parseToDate("2023/12/25"));
        assertNotNull(DateUtils.parseToDate("2023.12.25"));
        assertNotNull(DateUtils.parseToDate("20231225"));
        assertNotNull(DateUtils.parseToDate("2023年12月25日"));

        // 测试时间戳字符串
        String timestampStr = String.valueOf(System.currentTimeMillis());
        assertNotNull(DateUtils.parseToDate(timestampStr));

        // 测试无效字符串
        assertNull(DateUtils.parseToDate("invalid date"));
        assertNull(DateUtils.parseToDate(""));
        assertNull(DateUtils.parseToDate("   "));
    }

    @Test
    public void testParseToDate_EdgeCases() {
        // 测试边界情况
        assertNull(DateUtils.parseToDate("2023-13-01")); // 无效月份
        assertNull(DateUtils.parseToDate("2023-02-30")); // 无效日期
        assertNull(DateUtils.parseToDate("1800-01-01")); // 超出年份范围
        assertNull(DateUtils.parseToDate("2200-01-01")); // 超出年份范围

        // 测试有效的边界日期
        assertNotNull(DateUtils.parseToDate("1900-01-01"));
        assertNotNull(DateUtils.parseToDate("2100-01-01")); // 2100年在我们的有效范围内
        assertNotNull(DateUtils.parseToDate("2099-12-31"));
    }

    @Test
    public void testIsValidDateObject() {
        // 测试简化版本的方法
        assertTrue(DateUtils.isValidDateObject(new Date()));
        assertTrue(DateUtils.isValidDateObject("2023-12-25"));
        assertTrue(DateUtils.isValidDateObject(System.currentTimeMillis()));
        
        assertFalse(DateUtils.isValidDateObject(null));
        assertFalse(DateUtils.isValidDateObject("invalid"));
        assertFalse(DateUtils.isValidDateObject(""));
    }

    @Test
    public void testParseToDate_VariousObjects() {
        // 测试其他类型的对象
        assertNull(DateUtils.parseToDate(new Object()));
        assertNull(DateUtils.parseToDate(Boolean.TRUE));
        
        // 测试可以转换为字符串的对象
        StringBuilder sb = new StringBuilder("2023-12-25");
        assertNotNull(DateUtils.parseToDate(sb));
    }

    @Test
    public void testParseToDate_ISO8601Formats() {
        // 测试ISO 8601格式
        assertNotNull(DateUtils.parseToDate("2023-12-25T15:30:45"));
        assertNotNull(DateUtils.parseToDate("2023-12-25T15:30:45.123"));
        // 注意：带时区的ISO格式可能需要额外处理
    }

    @Test
    public void testParseToDate_Performance() {
        // 简单的性能测试
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < 1000; i++) {
            DateUtils.parseToDate("2023-12-25");
        }
        long endTime = System.currentTimeMillis();
        
        // 确保性能在合理范围内（1000次调用应该在1秒内完成）
        assertTrue(endTime - startTime < 1000, "Performance test failed");
    }
}
