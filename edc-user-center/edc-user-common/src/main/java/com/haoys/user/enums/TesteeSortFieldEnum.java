package com.haoys.user.enums;

public enum TesteeSortFieldEnum {

    TESTEE_SORT_FIELD_01("testeecode","project_visit_user.testee_code"),
    TESTEE_SORT_FIELD_02("realName","project_testee_info.real_name"),
    TESTEE_SORT_FIELD_03("ownerOrgName","project_visit_user.owner_org_name"),
    TESTEE_SORT_FIELD_04("ownerDoctor","project_visit_user.owner_doctor_id"),
    TESTEE_SORT_FIELD_05("create_time","project_testee_info.create_time"),
    TESTEE_SORT_FIELD_06("visit_create_time","project_visit_user.create_time");

    public static TesteeSortFieldEnum getByCode(String value) {
        for (TesteeSortFieldEnum item : TesteeSortFieldEnum.values()) {
            if (item.name.equals(value)) {
                return item;
            }
        }
        return null;
    }
    /**
     * 通过 value 获取 label
     */
    public static String getTextByCode(String value) {
        TesteeSortFieldEnum byCode = getByCode(value);
        if (byCode == null) {
            return "";
        }
        return byCode.getValue();
    }

    private String name;
    private String value;

    TesteeSortFieldEnum(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
