package com.haoys.user.common.util;

import java.util.Calendar;

/**
 * 验证码
 * <AUTHOR>
 * @date 2021/11/23
 */
public class MsgCodeUtil {

    /**
     * 根据年日，生成6位数字
     * @return
     */
    public static String getYearDayCode(){
        Calendar rightNow  =  Calendar.getInstance();
        Integer year = rightNow.get(Calendar.YEAR);
        Integer day = rightNow.get(rightNow.DAY_OF_MONTH);
        StringBuffer buffer = new StringBuffer();

        if (day < 10){
            buffer.append("0");
        }
        buffer.append(year);
        buffer.append(day);
        return buffer.toString();
    }

    public static void main(String[] args) {
        String yearDayCode = getYearDayCode();
        System.out.println(DateUtil.getCurrentDate().replace("-",""));

    }

}
