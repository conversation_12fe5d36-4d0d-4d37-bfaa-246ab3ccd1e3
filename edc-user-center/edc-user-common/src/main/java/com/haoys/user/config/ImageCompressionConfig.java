package com.haoys.user.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 图片压缩配置类
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "image.compression")
public class ImageCompressionConfig {

    /**
     * 是否启用图片压缩
     */
    private boolean enabled = true;

    /**
     * 是否启用图片压缩缓存
     */
    private boolean cacheEnabled = true;

    /**
     * 压缩质量 (0.0-1.0)
     */
    private float quality = 0.85f;

    /**
     * 最大宽度
     */
    private int maxWidth = 1920;

    /**
     * 最大高度
     */
    private int maxHeight = 1080;

    /**
     * 最大文件大小 (字节)
     */
    private long maxFileSize = 2 * 1024 * 1024; // 2MB

    /**
     * 压缩阈值，小于此大小的文件不进行压缩 (字节)
     * 默认1MB，低于此大小不使用缓存
     */
    private long compressionThreshold = 1024 * 1024; // 1MB

    /**
     * 支持的图片格式
     */
    private String[] supportedFormats = {"jpg", "jpeg", "png", "bmp", "gif"};

    /**
     * 是否保持原始格式
     */
    private boolean keepOriginalFormat = false;

    /**
     * 默认输出格式（当不保持原始格式时使用）
     */
    private String defaultOutputFormat = "jpg";

    /**
     * 是否启用渐进式JPEG
     */
    private boolean progressiveJpeg = true;

    /**
     * 检查文件扩展名是否支持压缩
     * 
     * @param extension 文件扩展名
     * @return true-支持，false-不支持
     */
    public boolean isSupportedFormat(String extension) {
        if (extension == null || extension.trim().isEmpty()) {
            return false;
        }
        
        String lowerExt = extension.toLowerCase();
        for (String format : supportedFormats) {
            if (format.equalsIgnoreCase(lowerExt)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查文件大小是否需要压缩
     *
     * @param fileSize 文件大小（字节）
     * @return true-需要压缩，false-不需要压缩
     */
    public boolean needsCompression(long fileSize) {
        return enabled && fileSize > compressionThreshold;
    }

    /**
     * 检查是否需要使用缓存
     *
     * @param fileSize 文件大小（字节）
     * @return true-需要缓存，false-不需要缓存
     */
    public boolean needsCache(long fileSize) {
        return cacheEnabled && fileSize >= compressionThreshold;
    }

    /**
     * 获取输出格式
     * 
     * @param originalExtension 原始文件扩展名
     * @return 输出格式
     */
    public String getOutputFormat(String originalExtension) {
        if (keepOriginalFormat && isSupportedFormat(originalExtension)) {
            return originalExtension.toLowerCase();
        }
        return defaultOutputFormat;
    }
}
