package com.haoys.user.common.bussiness;

public class RedisKeyContants {

    public static final String SEND_MOBILE_MESSAGE_CODE = "send_mobile_code:";
    public static final String SEND_EMAIL_MESSAGE_CODE = "send_email_code:";

    //用户登录次数计数  redisKey 前缀
    public static String EDC_LOGIN_USER_COUNT = "edc-login-user-count:";
    //用户登录是否被锁定    默认30分钟
    public static String EDC_LOGIN_USER_LOCK = "edc-login-user-lock:";

    public static final String EXPORT_EXCEL = "exportExcel";
    public static final String ERRORDATAEXCEL = "errorDataExcel";

    public static final String PROJECT_TESTEE_VARIABLE = "project_testee_variable:";
    public static final String PROJECT_TEMPLATE_VARIABLE = "project_template_variable:";
    public static final String PROJECT_TEMPLATE_VISIT_CONFIG = "project_template_visit_config:";


    /**
     * 患者端参与者导出搜索记录保存信息。
     */
    public static final String EXPORT_SEARCH_LOG = "EXPORT_SEARCH_LOG";

    public static final String SEARCH_PROJECT_ORG_KEY = "project_org_id:";

    /**
     * 密码策略再redis中的key
     */
    public static final String PWD_CONFIG = "PWD_CONFIG";

    /**
     * 导出管理用户选在导出的文件id再redis中的key
     */
    public static final String USER_DOWN = "USER_DOWN_";

}
