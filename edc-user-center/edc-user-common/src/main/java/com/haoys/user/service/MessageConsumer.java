package com.haoys.user.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.data.redis.connection.stream.RecordId;
import org.springframework.data.redis.connection.stream.StreamOffset;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StreamOperations;
import org.springframework.data.redis.stream.StreamListener;
import org.springframework.data.redis.stream.StreamMessageListenerContainer;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class MessageConsumer {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    public void consumeMessages(String streamKey, String groupName, String consumerName) {
        StreamOperations<String, Object, Object> streamOperations = redisTemplate.opsForStream();
        StreamMessageListenerContainer<String, MapRecord<String, String, String>> container = StreamMessageListenerContainer.create(redisTemplate.getConnectionFactory());
        // 启动容器，监听消息
        container.receive(StreamOffset.fromStart(streamKey), new StreamListener<String, MapRecord<String, String, String>>() {
            @Override
            public void onMessage(MapRecord<String, String, String> message) {
                //MapBackedRecord{recordId=1713346198181-0, kvMap={msg=噶是的阿萨德噶是的噶是的噶手打干撒搭嘎噶是的噶事, id=1}}
                // 处理接收到的消息
                System.out.println("Received message: " + message);
                
                RecordId recordId = message.getId();
                System.out.println("Received recordId: " + recordId);
                
                Map<String, String> messageValue = message.getValue();
                System.out.println("Received messageValue: " + messageValue);
                // 确认消息已经处理
                //message.acknowledge();
                String stream = message.getStream();
                log.info("stream: {}", stream);
                streamOperations.acknowledge(stream, groupName, recordId);
                streamOperations.delete(stream, recordId);
            }
        });
        container.start();
    }
}

