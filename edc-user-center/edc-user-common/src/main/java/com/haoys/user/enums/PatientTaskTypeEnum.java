package com.haoys.user.enums;

public enum PatientTaskTypeEnum {

    /**访视任务类型描述*/
    PROJECT_TASK_01("011001","表单"),
    PROJECT_TASK_02("011002","问卷"),
    PROJECT_TASK_03("011003","图片"),
    PROJECT_TASK_04("011004","不良事件"),
    PROJECT_TASK_05("011005","合并用药");

    private String name;
    private String value;

    PatientTaskTypeEnum(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public String getValue() {
        return value;
    }

    public static String getValue(String code) {
        for (PatientTaskTypeEnum ele : values()) {
            if(ele.getName().equals(code)) return ele.getValue();
        }
        return null;
    }

}
