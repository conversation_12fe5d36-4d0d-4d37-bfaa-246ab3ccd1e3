package com.haoys.user.util;

import cn.hutool.core.util.StrUtil;
import com.haoys.user.domain.vo.SecureTokenVo;
import com.haoys.user.service.SecureTokenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 安全Token工具类
 * 提供简化的Token验证和使用方法
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
@Slf4j
@Component
public class SecureTokenUtil {
    
    @Autowired
    private SecureTokenService secureTokenService;
    
    /**
     * 验证AccessToken是否有效
     * 
     * @param accessToken 访问令牌
     * @return 是否有效
     */
    public boolean isValidAccessToken(String accessToken) {
        if (StrUtil.isBlank(accessToken)) {
            return false;
        }
        
        try {
            SecureTokenVo.ValidateResponse response = secureTokenService.validateAccessToken(accessToken);
            return response != null && Boolean.TRUE.equals(response.getValid());
        } catch (Exception e) {
            log.warn("验证AccessToken异常: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取AccessToken关联的用户ID
     * 
     * @param accessToken 访问令牌
     * @return 用户ID，无效时返回null
     */
    public String getUserIdFromToken(String accessToken) {
        if (StrUtil.isBlank(accessToken)) {
            return null;
        }
        
        try {
            SecureTokenVo.ValidateResponse response = secureTokenService.validateAccessToken(accessToken);
            if (response != null && Boolean.TRUE.equals(response.getValid())) {
                return response.getUserId();
            }
        } catch (Exception e) {
            log.warn("从AccessToken获取用户ID异常: {}", e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 获取AccessToken的剩余有效时间（秒）
     * 
     * @param accessToken 访问令牌
     * @return 剩余时间，无效时返回0
     */
    public Long getRemainingTime(String accessToken) {
        if (StrUtil.isBlank(accessToken)) {
            return 0L;
        }
        
        try {
            SecureTokenVo.ValidateResponse response = secureTokenService.validateAccessToken(accessToken);
            if (response != null && Boolean.TRUE.equals(response.getValid())) {
                return response.getRemainingTime();
            }
        } catch (Exception e) {
            log.warn("获取AccessToken剩余时间异常: {}", e.getMessage());
        }
        
        return 0L;
    }
    
    /**
     * 获取AccessToken的扩展信息
     * 
     * @param accessToken 访问令牌
     * @return 扩展信息，无效时返回null
     */
    public String getExtraInfo(String accessToken) {
        if (StrUtil.isBlank(accessToken)) {
            return null;
        }
        
        try {
            SecureTokenVo.ValidateResponse response = secureTokenService.validateAccessToken(accessToken);
            if (response != null && Boolean.TRUE.equals(response.getValid())) {
                return response.getExtraInfo();
            }
        } catch (Exception e) {
            log.warn("从AccessToken获取扩展信息异常: {}", e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 检查AccessToken是否即将过期（剩余时间少于指定秒数）
     * 
     * @param accessToken 访问令牌
     * @param thresholdSeconds 阈值秒数
     * @return 是否即将过期
     */
    public boolean isTokenExpiringSoon(String accessToken, long thresholdSeconds) {
        Long remainingTime = getRemainingTime(accessToken);
        return remainingTime != null && remainingTime > 0 && remainingTime < thresholdSeconds;
    }
}
