//package com.haoys.mis.participle;
//
//import com.alibaba.fastjson.JSON;
//import com.haoys.mis.common.util.StringUtils;
////import org.apache.commons.lang.ArrayUtils;
////import org.wltea.analyzer.cfg.Configuration;
////import org.wltea.analyzer.cfg.DefaultConfig;
////import org.wltea.analyzer.core.IKSegmenter;
////import org.wltea.analyzer.core.Lexeme;
//
//import java.io.IOException;
//import java.io.StringReader;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.Collections;
//import java.util.Comparator;
//import java.util.HashMap;
//import java.util.HashSet;
//import java.util.List;
//import java.util.Map;
//import java.util.PriorityQueue;
//import java.util.Set;
//import java.util.TreeSet;
//import java.util.concurrent.ConcurrentHashMap;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//
//public class OCRParticipleUtil {
//
//    /**
//     * 全文本词频统计
//     * @param content  文本内容
//     * @param useSmart 是否使用 smart
//     * @return          词，词频
//     * @throws IOException
//     */
//    public static Map<String, Integer> countTermFrequency(String content, Boolean useSmart) throws IOException {
//        // 输出结果 Map
//        Map<String, Integer> frequencies = new HashMap<>();
//        if (StringUtils.isBlank(content)) {
//            return frequencies;
//        }
//
//        Configuration instance = DefaultConfig.getInstance();
//        instance.setUseSmart(useSmart);
//
//        //DefaultConfig conf = new DefaultConfig();
//        //conf.setUseSmart(useSmart);
//        // 使用 IKSegmenter 初始化文本信息并加载词典
//        IKSegmenter ikSegmenter = new IKSegmenter(new StringReader(content), instance);
//        Lexeme lexeme;
//        while ((lexeme = ikSegmenter.next()) != null) {
//            if (lexeme.getLexemeText().length() > 1) {// 过滤单字，也可以过滤其他内容，如数字和单纯符号等内容
//                final String term = lexeme.getLexemeText();
//                // Map 累加操作
//                frequencies.compute(term, (k, v) -> {
//                    if (v == null) {
//                        v = 1;
//                    } else {
//                        v += 1;
//                    }
//                    return v;
//                });
//            }
//        }
//        return frequencies;
//    }
//
//
//    /**
//     * 文本列表词频和词文档频率统计
//     * @param docs     文档列表
//     * @param useSmart 是否使用只能分词
//     * @return          词频列表 词-[词频,文档频率]
//     * @throws IOException
//     */
//    public static Map<String, Integer[]> countTFDF(List<String> docs, boolean useSmart) throws IOException {
//        // 输出结果 Map
//        Map<String, Integer[]> frequencies = new HashMap<>();
//        for (String doc : docs) {
//            if (StringUtils.isBlank(doc)) {
//                continue;
//            }
//            Configuration instance = DefaultConfig.getInstance();
//            instance.setUseSmart(useSmart);
//
//            //DefaultConfig conf = new DefaultConfig();
//            //conf.setUseSmart(useSmart);
//
//            // 使用 IKSegmenter 初始化文本信息并加载词典
//            IKSegmenter ikSegmenter = new IKSegmenter(new StringReader(doc), instance);
//            Lexeme lexeme;
//            // 用于文档频率统计的 Set
//            Set<String> terms = new HashSet<>();
//            while ((lexeme = ikSegmenter.next()) != null) {
//                if (lexeme.getLexemeText().length() > 1) {
//                    final String text = lexeme.getLexemeText();
//                    // 进行词频统计
//                    frequencies.compute(text, (k, v) -> {
//                        if (v == null) {
//                            v = new Integer[]{1, 0};
//                        } else {
//                            v[0] += 1;
//                        }
//                        return v;
//                    });
//                    terms.add(text);
//                }
//            }
//            // 进行文档频率统计：无需初始化 Map，统计词频后 Map 里面必有该词记录
//            for (String term : terms) {
//                frequencies.get(term)[1] += 1;
//            }
//        }
//        return frequencies;
//    }
//
//    /**
//     * 按出现次数，从高到低排序取 TopN
//     * @param data 词和排序数字对应的 Map
//     * @param topN 词云展示的 topN
//     * @return 前 N 个词和排序值
//     */
//    public static List<Map.Entry<String, Integer>> order(Map<String, Integer> data, int topN) {
//        PriorityQueue<Map.Entry<String, Integer>> priorityQueue = new PriorityQueue<>(data.size(), new Comparator<Map.Entry<String, Integer>>() {
//            @Override
//            public int compare(Map.Entry<String, Integer> o1, Map.Entry<String, Integer> o2) {
//                return o2.getValue().compareTo(o1.getValue());
//            }
//        });
//        for (Map.Entry<String, Integer> entry : data.entrySet()) {
//            priorityQueue.add(entry);
//        }
//        //TODO 当前100词频一致时(概率极低)的处理办法，if( list(0).value == list(99).value ){xxx}
//        List<Map.Entry<String, Integer>> list = new ArrayList<>();
//        //统计结果队列size和topN值取较小值列表
//        int size = priorityQueue.size() <= topN ? priorityQueue.size() : topN;
//        for (int i = 0; i < size; i++) {
//            list.add(priorityQueue.remove());
//        }
//        return list;
//    }
//
//
//    public static void main(String[] args) throws IOException {
//        String targetContent = "生地,山药,山茱萸,茯苓,泽泻,丹皮,赤芍,白芍,酒黄精,玉竹,生黄芪,生甘草,黄连,醋柴胡,生黄芩,桑寄生,续断,麦冬,五味子,瞿麦,金樱子";
//        //targetContent = "头晕,气短,关节痛,膝关节痛,胸闷,尿频,脾肾亏虚证,甲状腺结节,心悸,肺结节病头晕,气短,关节痛,膝关节痛,胸闷,动脉粥样硬化并高血脂症,口干,电解质紊乱,胁痛颈椎病,椎基底动脉供血不足,眩晕,不寐病,肢体麻木,肝阳上亢证,肝郁气滞证水肿,关节痛,高血糖,高脂血症,高尿酸血症,脾肾亏虚证,甲状腺结节,颈椎病2型糖尿病,糖尿病性周围血管病,腰痛,糖尿病,脾肾两亏证,气血失调证,心悸,头痛,肺胃热盛证,乏力,颤病肺结节病,房颤,湿热内蕴证,痰瘀互结证,高脂血症,前列腺肥大,尿频胃胀,呃逆,失眠,脾胃失调证,肝郁气滞证,气血失调证,脂肪肝肝功能异常,乏力,失眠,脂肪肝,胆囊息肉,气血失调证,脾肾两虚证,肝郁气滞证听力减退,气血失调证,脾肾亏虚证,眩晕,尿频,腰痛,高血糖失眠,月经不调,心肾不交证,气血失调证,肝脾不调证,心悸,关节痛,颈椎病心悸,淋巴结肿大,胸闷,气短,肝郁气滞证,气血失调证,腹胀,胁痛,甲状腺结节,肺结节病,气血不足证2型糖尿病,高脂血症,冠心病支架术后,肾功能不全,脾肾亏虚证,气血失调证,动脉狭窄糖尿病,胃肠功能紊乱,脾肾两虚证,气血失调证,腰痛,胃胀,肝郁气滞证,蛋白尿舌痛,肝郁气滞证,不寐病,呃逆病,心火上炎证乏力,月经不调,胃胀,脾肾两虚证,肝郁气滞证,气血失调证乏力,月经不调,胃胀,脾肾两虚证,肝郁气滞证,气血失调证高血糖,2型糖尿病,蛋白尿,高尿酸血症,阴虚内热证,胸闷,肝郁气滞证,气血失调证,心悸泌尿道感染,湿疹,瘙痒症,脾肾亏虚证,气血失调证,尿频,湿热内蕴证,失眠,腹胀,糖尿病,湿热下注证甲状腺功能亢进症,甲状腺功能低下,甲状腺结节,心悸,口腔溃疡,肝郁气滞证,乳腺增生,月经不调,室早,胸闷,气短2型糖尿病,糖尿病,乏力,腰痛,胁痛,蛋白尿,泌尿道感染,子宫肌瘤,脾肾不足证,气血失调证,关节痛,不寐病,肝功能异常失眠,肾结石,胆囊结石,气血失调证,肝肾亏虚证,脾胃不和证,肝郁气滞证,胸闷,肺结节病？心悸,乏力,不寐病,肝郁气滞证,气血失调证三叉神经痛,失眠,肝郁气滞证,气血失调证,胸闷,气短,乳腺增生,甲状腺结节,子宫肌瘤2型糖尿病,尿频,泌尿道感染,抑郁症,肢体麻木,颈椎病,脑梗塞,气虚血瘀证,脾肾两虚证胸闷,气短,心律失常,糖尿病肾病,支气管扩张,水肿,痰瘀阻络证,待查4,脾肾两虚证,气血失调证血糖水平升高,尿频,肝肾亏虚证,气血失调证,蛋白尿月经过少,月经不调,脾肾两虚证,气血失调证,子宫肌瘤,乳腺增生,甲状腺结节头晕,乏力,脾肾亏虚证,胃胀,肝郁气滞证,脾胃失调证糖尿病肾病,支气管扩张,水肿,痰瘀阻络证,待查4,脾肾两虚证,气血失调证心悸,乏力,不寐病,肝郁气滞证,气血失调证乏力,月经不调,胃胀,脾肾两虚证,肝郁气滞证,气血失调证乏力,月经不调,胃胀,脾肾两虚证,肝郁气滞证,气血失调证2型糖尿病,糖尿病性周围血管病,腰痛,糖尿病,脾肾两亏证,气血失调证,心悸,头痛,肺胃热盛证,乏力,颤病,尿频,泌尿道感染胃痛,便秘,腹胀,肝胃不调证,脾胃失调证,肝郁气滞证月经不调,甲状腺术后,乳腺术后,肝郁气滞证,气血失调证,脾肾两虚证失眠,胃胀病,心悸,气血失调证,脾肾亏虚证,胸闷,脾肾两虚证,焦虑状态,肝郁气滞证糖尿病,失眠,蛋白尿,颤病,脾肾亏虚证,气血失调证,肝郁气滞证月经不调,脾肾亏虚证,气血失调证,肝郁气滞证,子宫肌瘤,卵巢囊肿,乏力湿疹,胸闷,气短,心悸,湿热内蕴证,胃胀病,肝脾气滞证瘙痒,肥胖,脂肪肝,脾胃失调证,湿疹,湿热内蕴证,代谢综合征,肥胖病不寐病,呃逆病,脾肾两虚证,肝郁气滞证,心肾不交证尿频,泌尿道感染,乏力,胃脘痛,不寐病,关节痛,肝郁气滞证,气血失调证,肝郁脾虚证2型糖尿病,乏力,气短,遗尿,肺肾两虚证,气血失调证,蛋白尿,腰痛胃胀,呃逆,失眠,脾胃失调证,肝郁气滞证,气血失调证,脂肪肝2型糖尿病,蛋白尿,泌尿道感染,失眠,脾肾两虚证,气血失调证,不寐病门脉高压,白癜风,脾大,血小板减少症,脾肾亏虚证,气血失调证胃胀,不寐病,肝郁气滞证,气血失调证,心肾不交证";
//        targetContent = "肝郁气滞证,脾肾两虚证,气血失调证,湿疹,呃逆,湿热内蕴证,湿疹,湿热内蕴证,不寐病,肝郁气滞证,气血失调证,胃痛,呃逆,呃逆病,不寐病,肝郁气滞证,气血失调证,湿疹,湿热内蕴证,蛋白尿,气血失调证,腹胀,脾肾亏虚证,尿频,湿热内蕴证,糖尿病,2型糖尿病,蛋白尿,脾肾两虚证,气血失调证,尿频,腰痛,糖尿病,2型糖尿病,乏力,蛋白尿,气血失调证,脾肾亏虚证,糖尿病,2型糖尿病,不寐病,肝郁气滞证,脾肾两虚证,气血失调证,胃胀病,胃胀,脾肾两虚证,气血失调证,高尿酸血症,乏力,脾肾两虚证,水肿,气血失调证,尿频,乳腺增生,肝郁气滞证,气血失调证,胃胀病,胃胀,不寐病,肝郁气滞证,气血失调证,胃胀病,胃胀,肝功能异常,蛋白尿,脾胃失调证,气血失调证,胃胀病,胃胀,颤病,乏力,肺胃热盛证,气血失调证,腰痛,糖尿病,头痛,糖尿病性周围血管病,心悸,2型糖尿病,失眠,乏力,肝郁气滞证,脾肾两虚证,水肿,气血失调证,脾肾亏虚证,胁痛,胃胀病,胃胀,胸闷,气短,湿疹,湿热内蕴证,胃胀病,胃胀,心悸,肝脾气滞证,气血失调证,高脂血症,糖尿病,2型糖尿病,不寐病,便秘,肝郁气滞证,气血失调证,乏力,蛋白尿,气血失调证,脾肾亏虚证,糖尿病,2型糖尿病,胃肠功能紊乱,蛋白尿,肝郁气滞证,脾肾两虚证,气血失调证,腰痛,糖尿病,高尿酸血症,蛋白尿,水肿,气血失调证,腹胀,尿频,高脂血症,糖尿病,2型糖尿病,乏力,脾肾亏虚证,痰瘀阻络证,糖尿病,2型糖尿病,胃肠功能紊乱,失眠,气血失调证,头痛,动脉狭窄,肝脾气滞证,失眠,肝郁气滞证,心肾不交证,脾肾亏虚证,不寐病,乏力,脾肾两虚证,心肾不交证,气血失调证,高脂血症,心悸,颤病,失眠,气短,肝郁气滞证,水肿,高脂血症,肾结石,胸闷,失眠,肝郁气滞证,颈椎病,气血失调证,尿频,糖尿病,肺结节病,头痛,2型糖尿病,不寐病,便秘,肝郁气滞证,气血失调证,呃逆,胃胀病,呃逆病,胃胀,头晕,气短,关节痛,膝关节痛,胸闷,尿频,脾肾亏虚证,甲状腺结节,心悸,肺结节病头晕,气短,关节痛,膝关节痛,胸闷,动脉粥样硬化并高血脂症,口干,电解质紊乱,胁痛颈椎病,椎基底动脉供血不足,眩晕,不寐病,肢体麻木,肝阳上亢证,肝郁气滞证水肿,关节痛,高血糖,高脂血症,高尿酸血症,脾肾亏虚证,甲状腺结节,颈椎病2型糖尿病,糖尿病性周围血管病,腰痛,糖尿病,脾肾两亏证,气血失调证,心悸,头痛,肺胃热盛证,乏力,颤病肺结节病,房颤,湿热内蕴证,痰瘀互结证,高脂血症,前列腺肥大,尿频胃胀,呃逆,失眠,脾胃失调证,肝郁气滞证,气血失调证,脂肪肝肝功能异常,乏力,失眠,脂肪肝,胆囊息肉,气血失调证,脾肾两虚证,肝郁气滞证听力减退,气血失调证,脾肾亏虚证,眩晕,尿频,腰痛,高血糖失眠,月经不调,心肾不交证,气血失调证,肝脾不调证,心悸,关节痛,颈椎病心悸,淋巴结肿大,胸闷,气短,肝郁气滞证,气血失调证,腹胀,胁痛,甲状腺结节,肺结节病,气血不足证2型糖尿病,高脂血症,冠心病支架术后,肾功能不全,脾肾亏虚证,气血失调证,动脉狭窄糖尿病,胃肠功能紊乱,脾肾两虚证,气血失调证,腰痛,胃胀,肝郁气滞证,蛋白尿舌痛,肝郁气滞证,不寐病,呃逆病,心火上炎证乏力,月经不调,胃胀,脾肾两虚证,肝郁气滞证,气血失调证乏力,月经不调,胃胀,脾肾两虚证,肝郁气滞证,气血失调证高血糖,2型糖尿病,蛋白尿,高尿酸血症,阴虚内热证,胸闷,肝郁气滞证,气血失调证,心悸泌尿道感染,湿疹,瘙痒症,脾肾亏虚证,气血失调证,尿频,湿热内蕴证,失眠,腹胀,糖尿病,湿热下注证甲状腺功能亢进症,甲状腺功能低下,甲状腺结节,心悸,口腔溃疡,肝郁气滞证,乳腺增生,月经不调,室早,胸闷,气短2型糖尿病,糖尿病,乏力,腰痛,胁痛,蛋白尿,泌尿道感染,子宫肌瘤,脾肾不足证,气血失调证,关节痛,不寐病,肝功能异常失眠,肾结石,胆囊结石,气血失调证,肝肾亏虚证,脾胃不和证,肝郁气滞证,胸闷,肺结节病？心悸,乏力,不寐病,肝郁气滞证,气血失调证三叉神经痛,失眠,肝郁气滞证,气血失调证,胸闷,气短,乳腺增生,甲状腺结节,子宫肌瘤2型糖尿病,尿频,泌尿道感染,抑郁症,肢体麻木,颈椎病,脑梗塞,气虚血瘀证,脾肾两虚证胸闷,气短,心律失常,糖尿病肾病,支气管扩张,水肿,痰瘀阻络证,待查4,脾肾两虚证,气血失调证血糖水平升高,尿频,肝肾亏虚证,气血失调证,蛋白尿月经过少,月经不调,脾肾两虚证,气血失调证,子宫肌瘤,乳腺增生,甲状腺结节头晕,乏力,脾肾亏虚证,胃胀,肝郁气滞证,脾胃失调证糖尿病肾病,支气管扩张,水肿,痰瘀阻络证,待查4,脾肾两虚证,气血失调证心悸,乏力,不寐病,肝郁气滞证,气血失调证乏力,月经不调,胃胀,脾肾两虚证,肝郁气滞证,气血失调证乏力,月经不调,胃胀,脾肾两虚证,肝郁气滞证,气血失调证2型糖尿病,糖尿病性周围血管病,腰痛,糖尿病,脾肾两亏证,气血失调证,心悸,头痛,肺胃热盛证,乏力,颤病,尿频,泌尿道感染胃痛,便秘,腹胀,肝胃不调证,脾胃失调证,肝郁气滞证月经不调,甲状腺术后,乳腺术后,肝郁气滞证,气血失调证,脾肾两虚证失眠,胃胀病,心悸,气血失调证,脾肾亏虚证,胸闷,脾肾两虚证,焦虑状态,肝郁气滞证糖尿病,失眠,蛋白尿,颤病,脾肾亏虚证,气血失调证,肝郁气滞证月经不调,脾肾亏虚证,气血失调证,肝郁气滞证,子宫肌瘤,卵巢囊肿,乏力湿疹,胸闷,气短,心悸,湿热内蕴证,胃胀病,肝脾气滞证瘙痒,肥胖,脂肪肝,脾胃失调证,湿疹,湿热内蕴证,代谢综合征,肥胖病不寐病,呃逆病,脾肾两虚证,肝郁气滞证,心肾不交证尿频,泌尿道感染,乏力,胃脘痛,不寐病,关节痛,肝郁气滞证,气血失调证,肝郁脾虚证2型糖尿病,乏力,气短,遗尿,肺肾两虚证,气血失调证,蛋白尿,腰痛胃胀,呃逆,失眠,脾胃失调证,肝郁气滞证,气血失调证,脂肪肝2型糖尿病,蛋白尿,泌尿道感染,失眠,脾肾两虚证,气血失调证,不寐病门脉高压,白癜风,脾大,血小板减少症,脾肾亏虚证,气血失调证胃胀,不寐病,肝郁气滞证,气血失调证,心肾不交证";
//        List<String> docs = Arrays.asList(targetContent.split(","));
//        Map<String, Integer[]> resultMap = OCRParticipleUtil.countTFDF(docs, true);
//        System.out.println("targetContent: " + JSON.toJSONString(resultMap));
//        Map<String, Integer> termFrequency = OCRParticipleUtil.countTermFrequency(targetContent, true);
//
//
//        //自定义比较器
//        Comparator<Map.Entry<String, Integer>> valCmp = (o1, o2) -> {
//            // TODO Auto-generated method stub
//            return o2.getValue()-o1.getValue();  // 降序排序，如果想升序就反过来
//        };
//        //将map转成List，map的一组key，value对应list一个存储空间
//        List<Map.Entry<String, Integer>> list = new ArrayList<Map.Entry<String,Integer>>(termFrequency.entrySet()); //传入maps实体
//        Collections.sort(list,valCmp); // 注意此处Collections 是java.util包下面的,传入List和自定义的valCmp比较器
//
//        System.out.println("targetContent result: " + JSON.toJSONString(list));
//        System.out.println("targetContent result: " + list);
//
//
//
//
//
//        targetContent = "藿香,五味子,麦冬,党参,紫丹参,薤白,干姜,肉桂,鸡血藤,生黄芪,川牛膝,白术,盐杜仲,桑寄生,续断,白芍,赤芍,丹皮,泽泻,茯苓,山茱萸,山药,生地,藿香,五味子,麦冬,党参,紫丹参,薤白,干姜,肉桂,鸡血藤,生黄芪,川牛膝,白术,盐杜仲,桑寄生,续断,白芍,赤芍,丹皮,泽泻,茯苓,山茱萸,山药,生地,钩藤,天麻,川芎,葛根,菊花,赤芍,丹皮,白芍,地龙,生甘草,夏枯草,醋柴胡,枳壳,郁金,石菖蒲,远志,枣仁,佛手,木瓜,伸筋草,五味子,麦冬,党参,生黄芪,松节,佛手,海风藤,葛根,石斛,酒黄精,白术,桑寄生,续断,白芍,丹皮,泽泻,山茱萸,山药,秦艽,防风,瓜蒌,生黄芪,生地,肉桂,茯苓,葛根,钩藤,天麻,酒黄精,石斛,川芎,五味子,麦冬,党参,白术,白芍,丹皮,泽泻,山茱萸,山药,醋柴胡,玉竹,薤白,路路通,炙黄芪,葛根,藿香,合欢花,五味子,麦冬,党参,紫丹参,川芎,丹皮,白芍,陈皮,香附,郁金,柴胡,枳壳,赤芍,佩兰,路路通,紫丹参,薤白,藿香,砂仁,枣仁,远志,石菖蒲,鸡内金,生甘草,降香,川芎,白芍,赤芍,生黄芩,川楝子,陈皮,青皮,香附,郁金,枳壳,柴胡,生黄芪,藿香,鸡内金,生山楂,五味子,麦冬,党参,旱莲草,鸡血藤,桑寄生,白芍,丹皮,泽泻,茯苓,山茱萸,山药,茵陈,生黄芪,盐补骨脂,赤芍,续断,生地,合欢花,地龙,白茅根,枸杞子,黄柏,醋柴胡,枳壳,茯苓,生黄芪,金钱草,蒲公英,藿香,川牛膝,菊花,党参,葛根,川芎,知母,白术,白芍,干姜,泽泻,山茱萸,山药,地龙,合欢花,栀子,桑叶,肉桂,地骨皮,干姜,赤芍,白茅根,远志,麦冬,生桑皮,五味子,党参,枣仁,石菖蒲,合欢皮,薤白,生甘草,川芎,丹皮,白芍,生黄芩,郁金,枳实,柴胡,黄柏,茯苓,麦冬,柴胡,枳壳,郁金,香附,陈皮,生黄芩,白芍,丹皮,川芎,生甘草,薤白,紫丹参,路路通,党参,五味子,生黄芪,地丁,丝瓜络,桃仁,红花,生黄芪,熟地,赤芍,苍术,葛根,石斛,酒黄精,五味子,党参,香附,郁金,枳壳,醋柴胡,白术,盐杜仲,桑寄生,续断,白芍,丹皮,泽泻,茯苓,山茱萸,山药,石菖蒲,远志,枣仁,肉桂,生黄芪,菊花,生地,川牛膝,浮小麦,石斛,玉竹,酒黄精,枣仁,党参,葛根,苍术,干姜,白术,白芍,赤芍,丹皮,泽泻,茯苓,山茱萸,山药,白茅根,鸡血藤,栀子,钩藤,天麻,合欢花,赤芍,生黄芩,藿香,莲子心,路路通,薤白,生甘草,川芎,陈皮,香附,枳壳,柴胡,紫丹参,丹皮,白芍,生地,山药,山茱萸,茯苓,泽泻,丹皮,赤芍,白芍,续断,桑寄生,盐杜仲,白术,党参,麦冬,五味子,合欢花,石菖蒲,远志,枣仁,枣仁,石菖蒲,合欢皮,五味子,党参,苍术,盐杜仲,桑寄生,续断,白芍,赤芍,丹皮,泽泻,茯苓,山茱萸,山药,远志,麦冬,生地,生黄芪,路路通,薤白,枣仁,远志,石菖蒲,干姜,藿香,生黄芪,五味子,麦冬,党参,白术,赤芍,泽泻,茯苓,山茱萸,山药,生地,紫丹参,白芍,丹皮,莲子心,生山楂,鸡内金,枣仁,远志,石菖蒲,栀子,白茅根,赤芍,土茯苓,地丁,蒲公英,金钱草,仙鹤草,瞿麦,白术,泽泻,茯苓,山茱萸,山药,白芍,丹皮,黄柏,生藕节,生黄芪,地丁,蒲公英,合欢花,五味子,麦冬,党参,路路通,生甘草,川芎,生黄芩,陈皮,香附,枳壳,柴胡,紫丹参,丹皮,赤芍,莲子心,白茅根,白茅根,仙鹤草,金钱草,瞿麦,生黄芪,桑叶,远志,地骨皮,石菖蒲,浮小麦,枣仁,藿香,赤芍,白术,泽泻,山茱萸,山药,佩兰,白芍,丹皮,黄连,肉桂,生山楂,合欢花,藿香,薤白,茯苓,枣仁,远志,石菖蒲,干姜,生黄芪,五味子,麦冬,党参,白术,续断,赤芍,泽泻,山茱萸,山药,生地,紫丹参,白芍,丹皮,枳壳,醋柴胡,薤白,生黄芪,党参,白术,泽泻,茯苓,山茱萸,山药,生地,紫丹参,白芍,丹皮,麦冬,五味子,藿香,黄连,薤白,枣仁,远志,石菖蒲,全蝎,钩藤,生甘草,川芎,赤芍,生黄芩,陈皮,香附,郁金,枳壳,柴胡,紫丹参,丹皮,白芍,黄柏,白茅根,赤芍,鸡血藤,生黄芪,藿香,石斛,地龙,佛手,钩藤,天麻,葛根,玉竹,酒黄精,白术,盐杜仲,桑寄生,续断,泽泻,茯苓,山茱萸,山药,生地,白芍,丹皮,海风藤,桑枝,杏仁,炙麻黄,薤白,猪苓,旱莲草,盐补骨脂,藿香,玉竹,石斛,酒黄精,白术,盐杜仲,续断,赤芍,泽泻,茯苓,山茱萸,山药,生地,生黄芪,紫丹参,白芍,丹皮,地龙,枣仁,远志,石菖蒲,肉桂,生黄芪,麦冬,生地,蒲公英,川牛膝,赤芍,干姜,党参,葛根,玉竹,白术,泽泻,山茱萸,山药,吴茱萸,白芍,丹皮,生地,山药,山茱萸,茯苓,泽泻,丹皮,赤芍,白芍,续断,桑寄生,盐杜仲,白术,党参,麦冬,五味子,生黄芪,当归,合欢花,生甘草,合欢花,鸡血藤,川牛膝,薤白,五味子,麦冬,党参,生甘草,川芎,生黄芩,陈皮,青皮,香附,郁金,枳壳,柴胡,紫丹参,白芍,吴茱萸,木瓜,伸筋草,生地,山药,山茱萸,茯苓,泽泻,丹皮,赤芍,白芍,续断,桑寄生,盐杜仲,白术,酒黄精,石斛,玉竹,藿香,盐补骨脂,旱莲草,猪苓,白茅根,薤白,紫丹参,生黄芪,瓜蒌,炙麻黄,杏仁,生地,山药,山茱萸,茯苓,泽泻,丹皮,赤芍,白芍,白术,党参,生黄芪,薤白,紫丹参,合欢花,醋柴胡,枳壳,生黄芪,生地,麦冬,合欢皮,五味子,党参,苍术,盐杜仲,桑寄生,续断,白芍,赤芍,丹皮,泽泻,茯苓,山茱萸,山药,玫瑰花,薄荷,川牛膝,干姜,干姜,薄荷,玫瑰花,生黄芪,生地,麦冬,合欢皮,五味子,党参,苍术,盐杜仲,桑寄生,续断,赤芍,泽泻,茯苓,山茱萸,山药,川牛膝,当归,葛根,藿香,玉竹,瓜蒌,生黄芪,生地,肉桂,茯苓,葛根,酒黄精,石斛,川芎,五味子,麦冬,党参,泽泻,山茱萸,山药,茵陈,白术,白芍,丹皮,天麻,钩藤,浮小麦,白茅根,瓜蒌,麻黄根,合欢花,生黄芪,浮小麦,地丁,生甘草,川芎,生黄芩,川楝子,青皮,香附,郁金,柴胡,丹皮,赤芍,白芍,陈皮,枳实,郁李仁,生山楂,薄荷,元参,白茅根,地丁,鸡内金,生山楂,茯苓,泽泻,山茱萸,山药,黄柏,白术,白芍,丹皮,蒲公英,赤芍,蜜桑白皮,白鲜皮,藿香,肉桂,生黄芪,生地,五味子,麦冬,党参,防风,远志,川牛膝,浮小麦,枳实,醋柴胡,枣仁,干姜,泽泻,茯苓,山茱萸,山药,白术,白芍,地骨皮,瓜蒌,五味子,麦冬,党参,生山楂,醋柴胡,伸筋草,木瓜,盐补骨脂,藿香,石斛,玉竹,酒黄精,泽泻,茯苓,山茱萸,山药,生地,生黄芪,白术,白芍,赤芍,丹皮,生黄芩,佩兰,合欢花,鸡内金,路路通,薤白,石斛,蜜桑白皮,川芎,柴胡,薄荷,菊花,黑芝麻,五味子,麦冬,党参,泽泻,山茱萸,山药,紫丹参,白术,白芍,丹皮,蒲公英,地丁,鸡血藤,松节,苦参,远志,枣仁,川牛膝,郁金,柴胡,蜜桑白皮,藿香,白茅根,川芎,地丁,生甘草,黄柏,连翘,枳实,丹皮,白芍,金银花,白鲜皮,茯苓,玉竹,生地,蜜桑白皮,泽泻,山茱萸,山药,黄柏,蒲公英,金银花,白术,白芍,丹皮,白鲜皮,葛根,酒黄精,乌梅,黄连,黄柏,黑附子,干姜,桂枝,细辛,花椒,党参,当归,盐补骨脂,鸡血藤,生地,葛根,升麻,益智仁,佛手,海风藤,路路通,酒黄精,藿香,郁金,半枝莲,薤白,五味子,麦冬,党参,泽泻,山茱萸,山药,旱莲草,紫丹参,白芍,生黄芪,松节,川牛膝,路路通,藿香,砂仁,枣仁,远志,鸡内金,生甘草,降香,川芎,生黄芩,川楝子,青皮,香附,郁金,枳壳,柴胡,生黄芪,白芍,赤芍,陈皮,丝瓜络,生地,山药,山茱萸,茯苓,泽泻,丹皮,赤芍,白芍,续断,白术,石斛,玉竹,酒黄精,石菖蒲,远志,枣仁,醋柴胡,白鲜皮,合欢花,佛手,郁金,枳壳,蜜桑白皮,藿香,白茅根,凌霄花,玫瑰花,川芎,生甘草,白芍,赤芍,金银花,连翘,蒲公英,炒槐花,土茯苓,柴胡,枳壳,郁金,香附,陈皮,川楝子,生黄芩,赤芍,白芍,丹皮,川芎,生甘草,薤白,紫丹参,党参,麦冬,五味子,生黄芪,石菖蒲,远志,枣仁,合欢花,升麻,益智仁";
//        String s = "  藿香(克)×15克,五味子(克)×10克,麦冬(克)×10克,党参(克)×10克,紫丹参(克)×30克,薤白(克)×10克,干姜(克)×30克,肉桂(克)×10克,鸡血藤(克)×30克,生黄芪(克)×30克,川牛膝(克)×10克,白术(克)×15克,盐杜仲(克)×30克,桑寄生(克)×30克,续断(克)×10克,白芍(克)×15克,赤芍(克)×15克,丹皮(克)×15克,泽泻(克)×10克,茯苓(克)×15克,山茱萸(克)×10克,山药(克)×15克,生地(克)×15克  石斛20克,藿香(克)×15克,五味子(克)×10克,麦冬(克)×10克,党参(克)×10克,紫丹参(克)×30克,薤白(克)×10克,干姜(克)×30克,肉桂(克)×10克,鸡血藤(克)×30克,生黄芪(克)×30克,川牛膝(克)×10克,白术(克)×15克,盐杜仲(克)×30克,桑寄生(克)×30克,续断(克)×10克,白芍(克)×15克,赤芍(克)×15克,丹皮(克)×15克,泽泻(克)×10克,茯苓(克)×15克,山茱萸(克)×10克,山药(克)×15克,生地(克)×15克,钩藤(克)×15克,天麻(克)×15克,川芎(克)×10克,葛根(克)×20克,菊花(克)×10克,赤芍(克)×15克,丹皮(克)×15克,白芍(克)×15克,地龙(克)×10克,生甘草(克)×10克,夏枯草(克)×10克,醋柴胡(克)×10克,枳壳(克)×10克,郁金(克)×10克,石菖蒲(克)×10克,远志(克)×10克,枣仁(克)×30克,佛手(克)×10克,木瓜(克)×10克,伸筋草(克)×10克,五味子(克)×10克,麦冬(克)×10克,党参(克)×15克,生黄芪(克)×30克,松节(克)×10克,佛手(克)×10克,海风藤(克)×30克,葛根(克)×20克,石斛(克)×20克,酒黄精(克)×20克,白术(克)×15克,桑寄生(克)×30克,续断(克)×10克,白芍(克)×15克,丹皮(克)×15克,泽泻(克)×10克,山茱萸(克)×10克,山药(克)×15克,秦艽(克)×10克,防风(克)×10克,瓜蒌(克)×30克,生黄芪(克)×30克,生地(克)×20克,肉桂(克)×6克,茯苓(克)×10克,葛根(克)×30克,钩藤(克)×15克,天麻(克)×15克,酒黄精(克)×30克,石斛(克)×30克,川芎(克)×10克,五味子(克)×10克,麦冬(克)×10克,党参(克)×20克,白术(克)×15克,白芍(克)×30克,丹皮(克)×15克,泽泻(克)×10克,山茱萸(克)×10克,山药(克)×15克,醋柴胡(克)×10克,玉竹(克)×20克,薤白(克)×10克,路路通(克)×10克,炙黄芪(克)×30克,葛根(克)×15克,藿香(克)×20克,合欢花(克)×20克,五味子(克)×15克,麦冬(克)×20克,党参(克)×20克,紫丹参(克)×20克,川芎(克)×10克,丹皮(克)×10克,白芍(克)×10克,陈皮(克)×10克,香附(克)×10克,郁金(克)×10克,柴胡(克)×10克,枳壳(克)×6克,赤芍(克)×10克,佩兰(克)×15克,路路通(克)×10克,紫丹参(克)×30克,薤白(克)×10克,藿香(克)×10克,砂仁(克)×6克,枣仁(克)×20克,远志(克)×12克,石菖蒲(克)×12克,鸡内金(克)×15克,生甘草(克)×10克,降香(克)×10克,川芎(克)×10克,白芍(克)×10克,赤芍(克)×10克,生黄芩(克)×10克,川楝子(克)×10克,陈皮(克)×10克,青皮(克)×10克,香附(克)×10克,郁金(克)×10克,枳壳(克)×6克,柴胡(克)×10克,生黄芪(克)×30克,藿香(克)×10克,鸡内金(克)×20克,生山楂(克)×10克,五味子(克)×10克,麦冬(克)×10克,党参(克)×10克,旱莲草(克)×10克,鸡血藤(克)×30克,桑寄生(克)×30克,白芍(克)×15克,丹皮(克)×15克,泽泻(克)×10克,茯苓(克)×15克,山茱萸(克)×10克,山药(克)×15克,茵陈(克)×20克,生黄芪(克)×30克,盐补骨脂(克)×10克,赤芍(克)×15克,续断(克)×10克,生地(克)×15克,合欢花(克)×20克,地龙(克)×10克,白茅根(克)×30克,枸杞子(克)×10克,黄柏(克)×15克,醋柴胡(克)×10克,枳壳(克)×10克,茯苓(克)×10克,生黄芪(克)×30克,金钱草(克)×30克,蒲公英(克)×30克,藿香(克)×15克,川牛膝(克)×10克,菊花(克)×10克,党参(克)×20克,葛根(克)×20克,川芎(克)×10克,知母(克)×20克,白术(克)×15克,白芍(克)×15克,干姜(克)×20克,泽泻(克)×10克,山茱萸(克)×10克,山药(克)×15克,地龙(克)×10克,合欢花(克)×20克,栀子(克)×10克,桑叶(克)×10克,肉桂(克)×10克,地骨皮(克)×10克,干姜(克)×30克,赤芍(克)×15克,白茅根(克)×30克,远志(克)×12克,麦冬(克)×10克,生桑皮(克)×10克,五味子(克)×10克,党参(克)×15克,枣仁(克)×30克,石菖蒲(克)×12克,合欢皮(克)×10克,薤白(克)×12克,生甘草(克)×10克,川芎(克)×10克,丹皮(克)×15克,白芍(克)×15克,生黄芩(克)×10克,郁金(克)×10克,枳实(克)×10克,柴胡(克)×10克,黄柏(克)×10克,茯苓(克)×15克,麦冬(克)×10克,柴胡(克)×10克,枳壳(克)×10克,郁金(克)×10克,香附(克)×10克,陈皮(克)×10克,生黄芩(克)×10克,白芍(克)×15克,丹皮(克)×15克,川芎(克)×10克,生甘草(克)×10克,薤白(克)×12克,紫丹参(克)×30克,路路通(克)×10克,党参(克)×20克,五味子(克)×10克,生黄芪(克)×30克,地丁(克)×30克,丝瓜络(克)×6克,桃仁(克)×10克,红花(克)×10克,生黄芪(克)×40克,熟地(克)×10克,赤芍(克)×10克,苍术(克)×15克,葛根(克)×15克,石斛(克)×10克,酒黄精(克)×10克,五味子(克)×12克,党参(克)×20克,香附(克)×10克,郁金(克)×10克,枳壳(克)×6克,醋柴胡(克)×10克,白术(克)×15克,盐杜仲(克)×30克,桑寄生(克)×30克,续断(克)×10克,白芍(克)×10克,丹皮(克)×10克,泽泻(克)×6克,茯苓(克)×15克,山茱萸(克)×6克,山药(克)×10克,石菖蒲(克)×10克,远志(克)×10克,枣仁(克)×30克,肉桂(克)×10克,生黄芪(克)×30克,菊花(克)×10克,生地(克)×10克,川牛膝(克)×10克,浮小麦(克)×30克,石斛(克)×10克,玉竹(克)×10克,酒黄精(克)×10克,枣仁(克)×20克,党参(克)×15克,葛根(克)×10克,苍术(克)×15克,干姜(克)×20克,白术(克)×15克,白芍(克)×10克,赤芍(克)×10克,丹皮(克)×10克,泽泻(克)×5克,茯苓(克)×15克,山茱萸(克)×5克,山药(克)×10克,白茅根(克)×30克,鸡血藤(克)×30克,栀子(克)×10克,钩藤(克)×15克,天麻(克)×15克,合欢花(克)×20克,赤芍(克)×15克,生黄芩(克)×10克,藿香(克)×10克,莲子心(克)×12克,路路通(克)×10克,薤白(克)×10克,生甘草(克)×10克,川芎(克)×10克,陈皮(克)×10克,香附(克)×10克,枳壳(克)×10克,柴胡(克)×10克,紫丹参(克)×30克,丹皮(克)×10克,白芍(克)×15克,生地(克)×10克,山药(克)×10克,山茱萸(克)×6克,茯苓(克)×15克,泽泻(克)×6克,丹皮(克)×15克,赤芍(克)×15克,白芍(克)×15克,续断(克)×10克,桑寄生(克)×30克,盐杜仲(克)×30克,白术(克)×15克,党参(克)×10克,麦冬(克)×10克,五味子(克)×10克,合欢花(克)×20克,石菖蒲(克)×10克,远志(克)×10克,枣仁(克)×30克,枣仁(克)×30克,石菖蒲(克)×10克,合欢皮(克)×20克,五味子(克)×10克,党参(克)×10克,苍术(克)×15克,盐杜仲(克)×30克,桑寄生(克)×30克,续断(克)×10克,白芍(克)×15克,赤芍(克)×15克,丹皮(克)×15克,泽泻(克)×6克,茯苓(克)×15克,山茱萸(克)×6克,山药(克)×10克,远志(克)×10克,麦冬(克)×10克,生地(克)×10克,生黄芪(克)×30克,路路通(克)×10克,薤白(克)×12克,枣仁(克)×30克,远志(克)×12克,石菖蒲(克)×12克,干姜(克)×20克,藿香(克)×15克,生黄芪(克)×30克,五味子(克)×10克,麦冬(克)×10克,党参(克)×15克,白术(克)×15克,赤芍(克)×15克,泽泻(克)×6克,茯苓(克)×15克,山茱萸(克)×6克,山药(克)×10克,生地(克)×10克,紫丹参(克)×20克,白芍(克)×15克,丹皮(克)×15克,莲子心(克)×10克,生山楂(克)×10克,鸡内金(克)×10克,枣仁(克)×20克,远志(克)×10克,石菖蒲(克)×10克,栀子(克)×10克,白茅根(克)×20克,赤芍(克)×10克,土茯苓(克)×20克,地丁(克)×20克,蒲公英(克)×20克,金钱草(克)×30克,仙鹤草(克)×15克,瞿麦(克)×12克,白术(克)×15克,泽泻(克)×6克,茯苓(克)×15克,山茱萸(克)×6克,山药(克)×10克,白芍(克)×10克,丹皮(克)×10克,黄柏(克)×10克,生藕节(克)×10克,生黄芪(克)×30克,地丁(克)×30克,蒲公英(克)×30克,合欢花(克)×20克,五味子(克)×10克,麦冬(克)×10克,党参(克)×20克,路路通(克)×10克,生甘草(克)×10克,川芎(克)×10克,生黄芩(克)×10克,陈皮(克)×10克,香附(克)×10克,枳壳(克)×10克,柴胡(克)×10克,紫丹参(克)×20克,丹皮(克)×15克,赤芍(克)×15克,莲子心(克)×10克,白茅根(克)×30克,白茅根(克)×30克,仙鹤草(克)×15克,金钱草(克)×30克,瞿麦(克)×15克,生黄芪(克)×30克,桑叶(克)×12克,远志(克)×15克,地骨皮(克)×10克,石菖蒲(克)×15克,浮小麦(克)×50克,枣仁(克)×30克,藿香(克)×20克,赤芍(克)×15克,白术(克)×15克,泽泻(克)×10克,山茱萸(克)×10克,山药(克)×15克,佩兰(克)×15克,白芍(克)×15克,丹皮(克)×15克,黄连(克)×6克,肉桂(克)×10克,生山楂(克)×10克,合欢花(克)×20克,藿香(克)×15克,薤白(克)×15克,茯苓(克)×15克,枣仁(克)×30克,远志(克)×12克,石菖蒲(克)×12克,干姜(克)×20克,生黄芪(克)×30克,五味子(克)×10克,麦冬(克)×10克,党参(克)×15克,白术(克)×15克,续断(克)×10克,赤芍(克)×10克,泽泻(克)×6克,山茱萸(克)×6克,山药(克)×10克,生地(克)×10克,紫丹参(克)×30克,白芍(克)×10克,丹皮(克)×10克,枳壳(克)×10克,醋柴胡(克)×10克,薤白(克)×10克,生黄芪(克)×30克,党参(克)×10克,白术(克)×15克,泽泻(克)×10克,茯苓(克)×15克,山茱萸(克)×10克,山药(克)×15克,生地(克)×15克,紫丹参(克)×20克,白芍(克)×15克,丹皮(克)×15克,麦冬(克)×10克,五味子(克)×10克,藿香(克)×10克,黄连(克)×6克,薤白(克)×10克,枣仁(克)×30克,远志(克)×10克,石菖蒲(克)×10克,全蝎(克)×6克,钩藤(克)×15克,生甘草(克)×10克,川芎(克)×10克,赤芍(克)×15克,生黄芩(克)×10克,陈皮(克)×10克,香附(克)×10克,郁金(克)×10克,枳壳(克)×10克,柴胡(克)×10克,紫丹参(克)×20克,丹皮(克)×15克,白芍(克)×15克,黄柏(克)×10克,白茅根(克)×30克,赤芍(克)×15克,鸡血藤(克)×30克,生黄芪(克)×30克,藿香(克)×20克,石斛(克)×20克,地龙(克)×10克,佛手(克)×10克,钩藤(克)×15克,天麻(克)×15克,葛根(克)×20克,玉竹(克)×20克,酒黄精(克)×20克,白术(克)×15克,盐杜仲(克)×30克,桑寄生(克)×30克,续断(克)×10克,泽泻(克)×10克,茯苓(克)×15克,山茱萸(克)×10克,山药(克)×15克,生地(克)×15克,白芍(克)×15克,丹皮(克)×15克,海风藤(克)×30克,桑枝(克)×10克,杏仁(克)×10克,炙麻黄(克)×6克,薤白(克)×10克,猪苓(克)×10克,旱莲草(克)×10克,盐补骨脂(克)×10克,藿香(克)×20克,玉竹(克)×20克,石斛(克)×20克,酒黄精(克)×20克,白术(克)×15克,盐杜仲(克)×30克,续断(克)×10克,赤芍(克)×15克,泽泻(克)×10克,茯苓(克)×15克,山茱萸(克)×10克,山药(克)×15克,生地(克)×15克,生黄芪(克)×30克,紫丹参(克)×30克,白芍(克)×15克,丹皮(克)×15克,地龙(克)×10克,枣仁(克)×20克,远志(克)×10克,石菖蒲(克)×10克,肉桂(克)×6克,生黄芪(克)×30克,麦冬(克)×20克,生地(克)×30克,蒲公英(克)×30克,川牛膝(克)×10克,赤芍(克)×15克,干姜(克)×20克,党参(克)×20克,葛根(克)×15克,玉竹(克)×30克,白术(克)×15克,泽泻(克)×10克,山茱萸(克)×10克,山药(克)×15克,吴茱萸(克)×3克,白芍(克)×15克,丹皮(克)×10克,生地(克)×15克,山药(克)×15克,山茱萸(克)×10克,茯苓(克)×15克,泽泻(克)×10克,丹皮(克)×15克,赤芍(克)×15克,白芍(克)×15克,续断(克)×10克,桑寄生(克)×30克,盐杜仲(克)×30克,白术(克)×15克,党参(克)×10克,麦冬(克)×10克,五味子(克)×10克,生黄芪(克)×30克,当归(克)×6克,合欢花(克)×15克,生甘草(克)×10克,合欢花(克)×20克,鸡血藤(克)×30克,川牛膝(克)×10克,薤白(克)×10克,五味子(克)×10克,麦冬(克)×10克,党参(克)×10克,生甘草(克)×10克,川芎(克)×10克,生黄芩(克)×10克,陈皮(克)×10克,青皮(克)×10克,香附(克)×10克,郁金(克)×10克,枳壳(克)×10克,柴胡(克)×10克,紫丹参(克)×15克,白芍(克)×15克,吴茱萸(克)×3克,木瓜(克)×10克,伸筋草(克)×10克,生地(克)×15克,山药(克)×15克,山茱萸(克)×10克,茯苓(克)×15克,泽泻(克)×10克,丹皮(克)×15克,赤芍(克)×15克,白芍(克)×15克,续断(克)×10克,桑寄生(克)×30克,盐杜仲(克)×30克,白术(克)×15克,酒黄精(克)×20克,石斛(克)×20克,玉竹(克)×20克,藿香(克)×20克,盐补骨脂(克)×10克,旱莲草(克)×10克,猪苓(克)×30克,白茅根(克)×30克,薤白(克)×10克,紫丹参(克)×30克,生黄芪(克)×30克,瓜蒌(克)×30克,炙麻黄(克)×6克,杏仁(克)×10克,生地(克)×15克,山药(克)×15克,山茱萸(克)×10克,茯苓(克)×15克,泽泻(克)×10克,丹皮(克)×15克,赤芍(克)×15克,白芍(克)×15克,白术(克)×15克,党参(克)×10克,生黄芪(克)×30克,薤白(克)×10克,紫丹参(克)×10克,合欢花(克)×15克,醋柴胡(克)×10克,枳壳(克)×10克,生黄芪(克)×30克,生地(克)×10克,麦冬(克)×10克,合欢皮(克)×15克,五味子(克)×10克,党参(克)×10克,苍术(克)×15克,盐杜仲(克)×30克,桑寄生(克)×30克,续断(克)×10克,白芍(克)×15克,赤芍(克)×15克,丹皮(克)×15克,泽泻(克)×6克,茯苓(克)×15克,山茱萸(克)×6克,山药(克)×10克,玫瑰花(克)×10克,薄荷(克)×6克,川牛膝(克)×10克,干姜(克)×20克,干姜(克)×20克,薄荷(克)×6克,玫瑰花(克)×10克,生黄芪(克)×30克,生地(克)×10克,麦冬(克)×10克,合欢皮(克)×15克,五味子(克)×10克,党参(克)×10克,苍术(克)×15克,盐杜仲(克)×20克,桑寄生(克)×20克,续断(克)×10克,赤芍(克)×15克,泽泻(克)×6克,茯苓(克)×15克,山茱萸(克)×6克,山药(克)×10克,川牛膝(克)×10克,当归(克)×6克,葛根(克)×10克,藿香(克)×10克,玉竹(克)×20克,瓜蒌(克)×30克,生黄芪(克)×30克,生地(克)×20克,肉桂(克)×6克,茯苓(克)×10克,葛根(克)×30克,酒黄精(克)×30克,石斛(克)×30克,川芎(克)×10克,五味子(克)×12克,麦冬(克)×10克,党参(克)×20克,泽泻(克)×10克,山茱萸(克)×10克,山药(克)×15克,茵陈(克)×20克,白术(克)×15克,白芍(克)×15克,丹皮(克)×10克,天麻(克)×15克,钩藤(克)×15克,浮小麦(克)×30克,白茅根(克)×15克,瓜蒌(克)×30克,麻黄根(克)×10克,合欢花(克)×20克,生黄芪(克)×30克,浮小麦(克)×30克,地丁(克)×30克,生甘草(克)×10克,川芎(克)×10克,生黄芩(克)×10克,川楝子(克)×10克,青皮(克)×10克,香附(克)×10克,郁金(克)×10克,柴胡(克)×10克,丹皮(克)×10克,赤芍(克)×10克,白芍(克)×30克,陈皮(克)×10克,枳实(克)×10克,郁李仁(克)×6克,生山楂(克)×10克,薄荷(克)×6克,元参(克)×10克,白茅根(克)×30克,地丁(克)×30克,鸡内金(克)×10克,生山楂(克)×10克,茯苓(克)×10克,泽泻(克)×10克,山茱萸(克)×10克,山药(克)×15克,黄柏(克)×10克,白术(克)×15克,白芍(克)×15克,丹皮(克)×15克,蒲公英(克)×30克,赤芍(克)×15克,蜜桑白皮(克)×10克,白鲜皮(克)×10克,藿香(克)×10克,肉桂(克)×12克,生黄芪(克)×50克,生地(克)×20克,五味子(克)×10克,麦冬(克)×15克,党参(克)×30克,防风(克)×10克,远志(克)×12克,川牛膝(克)×10克,浮小麦(克)×50克,枳实(克)×10克,醋柴胡(克)×10克,枣仁(克)×30克,干姜(克)×30克,泽泻(克)×10克,茯苓(克)×15克,山茱萸(克)×10克,山药(克)×15克,白术(克)×15克,白芍(克)×30克,地骨皮(克)×10克,瓜蒌(克)×30克,五味子(克)×10克,麦冬(克)×10克,党参(克)×10克,生山楂(克)×10克,醋柴胡(克)×10克,伸筋草(克)×10克,木瓜(克)×10克,盐补骨脂(克)×10克,藿香(克)×20克,石斛(克)×20克,玉竹(克)×20克,酒黄精(克)×20克,泽泻(克)×6克,茯苓(克)×15克,山茱萸(克)×6克,山药(克)×10克,生地(克)×10克,生黄芪(克)×40克,白术(克)×15克,白芍(克)×10克,赤芍(克)×10克,丹皮(克)×10克,生黄芩(克)×10克,佩兰(克)×15克,合欢花(克)×20克,鸡内金(克)×20克,路路通(克)×10克,薤白(克)×10克,石斛(克)×20克,蜜桑白皮(克)×10克,川芎(克)×10克,柴胡(克)×10克,薄荷(克)×6克,菊花(克)×10克,黑芝麻(克)×30克,五味子(克)×10克,麦冬(克)×20克,党参(克)×30克,泽泻(克)×6克,山茱萸(克)×6克,山药(克)×15克,紫丹参(克)×15克,白术(克)×15克,白芍(克)×10克,丹皮(克)×10克,蒲公英(克)×30克,地丁(克)×30克,鸡血藤(克)×30克,松节(克)×10克,苦参(克)×10克,远志(克)×10克,枣仁(克)×15克,川牛膝(克)×12克,郁金(克)×10克,柴胡(克)×10克,蜜桑白皮(克)×20克,藿香(克)×10克,白茅根(克)×30克,川芎(克)×10克,地丁(克)×30克,生甘草(克)×10克,黄柏(克)×10克,连翘(克)×15克,枳实(克)×19克,丹皮(克)×15克,白芍(克)×15克,金银花(克)×15克,白鲜皮(克)×10克,茯苓(克)×10克,玉竹(克)×15克,生地(克)×10克,蜜桑白皮(克)×10克,泽泻(克)×3克,山茱萸(克)×5克,山药(克)×10克,黄柏(克)×10克,蒲公英(克)×20克,金银花(克)×10克,白术(克)×10克,白芍(克)×10克,丹皮(克)×10克,白鲜皮(克)×10克,葛根(克)×10克,酒黄精(克)×10克,乌梅(克)×6克,黄连(克)×6克,黄柏(克)×10克,黑附子(克)×10克,干姜(克)×10克,桂枝(克)×10克,细辛(克)×3克,花椒(克)×3克,党参(克)×10克,当归(克)×10克,盐补骨脂(克)×10克,鸡血藤(克)×30克,生地(克)×15克,葛根(克)×30克,升麻(克)×15克,益智仁(克)×15克,佛手(克)×12克,海风藤(克)×30克,路路通(克)×10克,酒黄精(克)×20克,藿香(克)×10克,郁金(克)×10克,半枝莲(克)×10克,薤白(克)×10克,五味子(克)×10克,麦冬(克)×10克,党参(克)×15克,泽泻(克)×6克,山茱萸(克)×6克,山药(克)×15克,旱莲草(克)×10克,紫丹参(克)×30克,白芍(克)×10克,生黄芪(克)×30克,松节(克)×10克,川牛膝(克)×10克,路路通(克)×10克,藿香(克)×10克,砂仁(克)×6克,枣仁(克)×15克,远志(克)×12克,鸡内金(克)×10克,生甘草(克)×10克,降香(克)×10克,川芎(克)×10克,生黄芩(克)×10克,川楝子(克)×10克,青皮(克)×10克,香附(克)×10克,郁金(克)×10克,枳壳(克)×6克,柴胡(克)×10克,生黄芪(克)×30克,白芍(克)×10克,赤芍(克)×10克,陈皮(克)×10克,丝瓜络(克)×6克,生地(克)×10克,山药(克)×10克,山茱萸(克)×10克,茯苓(克)×10克,泽泻(克)×10克,丹皮(克)×8克,赤芍(克)×10克,白芍(克)×10克,续断(克)×10克,白术(克)×10克,石斛(克)×10克,玉竹(克)×10克,酒黄精(克)×10克,石菖蒲(克)×10克,远志(克)×10克,枣仁(克)×5克,醋柴胡(克)×10克,白鲜皮(克)×10克,合欢花(克)×20克,佛手(克)×10克,郁金(克)×10克,枳壳(克)×10克,蜜桑白皮(克)×10克,藿香(克)×10克,白茅根(克)×30克,凌霄花(克)×10克,玫瑰花(克)×10克,川芎(克)×10克,生甘草(克)×10克,白芍(克)×10克,赤芍(克)×10克,金银花(克)×15克,连翘(克)×15克,蒲公英(克)×30克,炒槐花(克)×20克,土茯苓(克)×30克,柴胡(克)×10克,枳壳(克)×10克,郁金(克)×10克,香附(克)×10克,陈皮(克)×10克,川楝子(克)×10克,生黄芩(克)×10克,赤芍(克)×15克,白芍(克)×15克,丹皮(克)×15克,川芎(克)×10克,生甘草(克)×10克,薤白(克)×10克,紫丹参(克)×6克,党参(克)×10克,麦冬(克)×10克,五味子(克)×10克,生黄芪(克)×30克,石菖蒲(克)×10克,远志(克)×10克,枣仁(克)×30克,合欢花(克)×20克,升麻(克)×10克,益智仁(克)×10克";
//        termFrequency = OCRParticipleUtil.countTermFrequency(targetContent, true);
//        System.out.println(JSON.toJSONString(termFrequency));
//        System.out.println( "targetContent===" + termFrequency);
//        termFrequency = OCRParticipleUtil.countTermFrequency(s, true);
//        System.out.println( "s====" + termFrequency);
//
//        //targetContent = "膝关节积液 甲状腺结节 乳腺结节 子宫肌瘤子宫切除术后7年,膝关节积液 甲状腺结节 乳腺结节 子宫切除术后,腰椎间盘膨出，高血压,高血糖血糖最高12、高脂血症、高尿酸血症、胃肠息肉,糖尿病5年，现服二甲双胍及拜糖平 高脂血症 甲状腺结节 颈椎病椎管狭窄 鼻炎,房颤10余年，手术效果不佳。高脂血症、低血压90-100/52-60mmHg 右肺尖肺大泡 白内障,轻度脂肪肝,胆囊息肉 高脂血症 肝功能异常高尿酸血症 睾丸囊肿脂肪肝,颈部斑块 高脂血症 颈椎病 右耳无听力";
//        targetContent = "服药后头晕气短关节痛减轻，大便干稀不调，耳鸣，下肢冷上身汗出，纳可，小便可，睡眠可。口干减轻反复头晕气短关节痛3年余，大便干稀不调。头蒙，上午头脑不清楚，失眠，入睡困难，容易醒，纳可，呃逆，二便调，右大拇指食指中指麻木，脾气急四肢指、趾间关节疼痛减轻，晨僵，足趾肿胀减轻，下肢凉、畏寒减轻，纳可，眠可，二便可。乏力减，头痛，背发凉减，左肩左胳膊凉减轻，怕凉。自觉咽中有异物感，痰少，耳鸣减轻，夜间凌晨左右口干减轻。右足趾麻木。记忆力下降。头蒙。晚7点困，睡眠安，便秘发现肺结节6年,目前左肺上叶磨玻璃结节6mm，左肺下叶结节10mm。胃脘部胀满，呃逆，运动后略缓解，情绪急躁易怒好转，无反酸烧心，矢气少，纳食可，夜眠差好转，入睡困难好转，早醒，时有心慌，小便调，大便不成形。近期体重无增减。眼干减乏力好转，入睡困难，早醒。睡眠晚，ED听力差10余年，治疗后好转，近1周突然出现听力下降，纳眠可，二便调。患者10余年前脸部汗出冷水洗脸后出现脸部色红,面部不出汗，面部胀感减轻，面部痒消面干减，时有心烦，周身乏力好转，晚上怕冷，眠浅易醒，偶感心慌减轻，偶感腰酸，纳差，大便费力量2-3日一行，小便调。心悸减,查24动态心电图室早心脏彩超左心大心脏冠脉硬化左肺胸膜下结节肝左叶异常强化灶双颈部多发低回声结节左大2.2*0.7cm右大2.7*0.8cm无压痛之前咽痛服用消炎药，纳可二便调睡眠安.患者血糖升高10余年现注射甘精胰岛素15u、度拉糖肽并口服二甲双胍达格列净治疗血糖控制尚可，小便可，大便干，多梦觉少。反酸好转，气短患者糖尿病5年，口服药物格列美脲达格列净治疗两年，糖化血红蛋白7.7，头汗好转，双下肢发凉减轻，眠稍好转，易疲倦，便不成形，次数多，小便正常。服药后舌痛消失，舌尖麻，舌尖火热感偶有，曾于北京中医院诊断为灼口综合征，舌苔时厚时无，纳可，二便调，睡眠尚可，生气后导致，喜叹息，呃逆偶有，胸闷心悸消失，气短，足跟痛好转。乏力2年，偶有胸闷，生产后经量减少，两颊色斑，饮食可，睡眠欠佳，多梦，大便不成形，小便可。乏力2年，偶有胸闷，生产后经量减少，两颊色斑，饮食可，睡眠欠佳，多梦，大便不成形，小便可。发现血糖升高1月余，口干口渴明显减轻，口苦好转，口腔溃疡消失，心悸胸闷气短，二便可，入睡困难，舌部不适。湿疹消失，头部躯干明显减轻；太阳穴头隐痛减轻，入睡困难，易疲劳，梦多，纳可，反酸偶有，腹胀，大便不成形日2次；矢气多好转，排后胀减；小便排出困难减轻、尿痛减轻。怕热甲亢复发一月余，现用优甲乐及赛治，心悸振作平躺加重，服药后好转，舌边下嘴唇口腔溃疡,咽喉痛。纳可，二便调，睡眠可夜眠差减轻，入睡困难，乏力好转，耳鸣，大便调，纳食可,膝关节痛无。气短.口苦，出汗,尿频好转，下肢水肿减轻。睡眠较前好转，胸闷好转，时有放射至肩痛，无胸痛，纳食可，小便调，大便溏黏脾虚。心悸减，乏力，生气易怒，纳可，胃胀呃逆反酸好转，二便调，失眠好转，口苦反复左侧面部疼痛2次消失，呈电击样，白天发作，卡马西平减量，二便可，眠差。口苦好转，牙龈面部口疮好转糖尿病近2年，曾服用二甲双胍，后因腹泻停用，改用卡格列净西格列汀及胰岛素治疗，空腹血糖15，餐后血糖9，二便可，眠可，右手木感明显。吞咽不适。鼻塞气短，尿频糖尿病肾病3年余，现用二甲双胍及胰岛素，近期空腹血糖7.5，餐后血糖10，下肢水肿，喘息。胸闷心悸气短发现血糖异常2个月。无口干多饮多食，无消瘦，納可，眠可，大便干缓解，1天一行，小便调。服药后2小时饥饿无心悸，末次月经6.4月经量少，乏力月经量少1年，行经1天，经前乳胀，无痛经，末次月经7.2-7.4，纳可，二便调，睡眠安，脾气急反复乏力减轻，间断头晕减轻，气短，二便可，睡眠可，肾彩超泥沙样结石，右小腿外侧疼痛不适明显减轻糖尿病肾病3年余，现用二甲双胍及胰岛素，近期空腹血糖10-11，餐后血糖15-17，下肢水肿，喘息。心悸乏力，生气易怒，纳可，胃胀呃逆反酸，二便调，失眠乏力同前，生产后经量减少，两颊色斑，饮食可，睡眠欠佳，多梦，大便不成形，小便可。乏力同前，生产后经量减少，两颊色斑，饮食可，睡眠欠佳，多梦，大便不成形，小便可。口苦足部凉乏力减，头痛，背发凉减，左肩左胳膊凉减轻，怕凉。自觉咽中有异物感，痰少，耳鸣减轻，夜间凌晨左右口干减轻。右足趾麻木。记忆力下降。头蒙减轻。晚7点困，睡眠安，便秘好转胃胀呃逆好转，呃逆后自觉舒畅，胃痛2次，无夜间痛。无反酸烧心。大便2天一次，黏。月经后期半年余，胸闷气短服药消失，睡眠可。大便干排便费力消失，额头面下颌处皮疹反复。胃部不适减轻。4月前自服通便药后双足凉，药后症轻，畏寒，乏力重，无疼痛，心悸减轻，恐惧感减轻，无头晕，胃胀烧灼感好转，入睡困难好转，药物助眠。后背热，大便头干口苦消失，口涩，嘴颤，头晕走路踩棉花感，乏力，口中辛辣感消失，纳呆减，二便可，安眠药助眠。烧心月经不调7个月，1月至今每月一行，服药后怕冷消，怕热，下肢沉重减轻，经行双乳胀痛，乏力脱发减轻，经前期头痛，记忆力下降，易疲劳，面部斑减轻。睡眠尚可，困倦，二便调。饭后胃顶好转，右眼角麦粒肿四肢湿疹，服药后没新发，上肢明显减少，下肢仍有，瘙痒消失，脓点消失，胸闷气短心悸好转，胃胀减轻，大便可，小便可，眠浅入睡困难好转，右足跟疼痛减轻。患者6月余前发现后颈部黑棘皮样改变，药后色黑减轻，皮肤麻手减，发痒减，平素性情急躁，黑棘皮症状反复，颈部及腋下、腹股沟处皮肤色黑且瘙痒。大便成形。小便可。患者诉服药后胸闷心悸气短均明显减轻，无胸痛，头昏蒙改善，无头痛，乏力，腹胀、反酸，饮食较前改善，入睡困难，大小便调。口苦，2点醒，胸闷呃逆，爱生气乏力，爬楼后喘，胃痛，反酸，无呃逆，尿频，口苦，多梦，睡眠轻，目眩，纳可，大便调咳嗽消失，遗尿夜间如厕不能控制较前明显减轻，乏力减轻，气短减，饮食可，眠尚可，大便调。肩部疼痛减轻。胃脘部胀满呃逆缓解，情绪急躁易怒好转，无反酸烧心，矢气少，纳食可，夜眠差好转，入睡困难好转，早醒，左胸不适，小便调，大便饮酒后不成形。近期体重无增减。眼胀干好转入睡困难30余年，伴多梦，纳呆，头晕，大便不畅，小便可。患者双手、面部、腰部皮肤色素减退，面部及后背皮疹、色红，性情急躁，嗳气呃逆减轻，腹胀，排气少，纳可，眠尚可，大便成形或不成形，每日2-3次。手足心热，脾气急，纳食可，大便每日一行，大便软，咳嗽遗尿，失眠，末次月经6.24，小腹坠痛，胃胀";
//        termFrequency = OCRParticipleUtil.countTermFrequency(targetContent, true);
//
//
//
//        Map<String, Integer> countMap = new HashMap<>();
//        Set<String> dataSet = new TreeSet<>();
//        String content = "头晕,气短,关节痛,膝关节痛,胸闷,尿频,脾肾亏虚证,甲状腺结节,心悸,肺结节病,头晕,气短,关节痛,膝关节痛,胸闷,动脉粥样硬化并高血脂症,口干,电解质紊乱,胁痛,颈椎病,椎基底动脉供血不足,眩晕,不寐病,肢体麻木,肝阳上亢证,肝郁气滞证,水肿,关节痛,高血糖,高脂血症,高尿酸血症,脾肾亏虚证,甲状腺结节,颈椎病,2型糖尿病,糖尿病性周围血管病,腰痛,糖尿病,脾肾两亏证,气血失调证,心悸,头痛,肺胃热盛证,乏力,颤病,肺结节病,房颤,湿热内蕴证,痰瘀互结证,高脂血症,前列腺肥大,尿频,胃胀,呃逆,失眠,脾胃失调证,肝郁气滞证,气血失调证,脂肪肝,肝功能异常,乏力,失眠,脂肪肝,胆囊息肉,气血失调证,脾肾两虚证,肝郁气滞证,听力减退,气血失调证,脾肾亏虚证,眩晕,尿频,腰痛,高血糖,失眠,月经不调,心肾不交证,气血失调证,肝脾不调证,心悸,关节痛,颈椎病,心悸,淋巴结肿大,胸闷,气短,肝郁气滞证,气血失调证,腹胀,胁痛,甲状腺结节,肺结节病,气血不足证,2型糖尿病,高脂血症,冠心病支架术后,肾功能不全,脾肾亏虚证,气血失调证,动脉狭窄,糖尿病,胃肠功能紊乱,脾肾两虚证,气血失调证,腰痛,胃胀,肝郁气滞证,蛋白尿,舌痛,肝郁气滞证,不寐病,呃逆病,心火上炎证,乏力,月经不调,胃胀,脾肾两虚证,肝郁气滞证,气血失调证,乏力,月经不调,胃胀,脾肾两虚证,肝郁气滞证,气血失调证,高血糖,2型糖尿病,蛋白尿,高尿酸血症,阴虚内热证,胸闷,肝郁气滞证,气血失调证,心悸,泌尿道感染,湿疹,瘙痒症,脾肾亏虚证,气血失调证,尿频,湿热内蕴证,失眠,腹胀,糖尿病,湿热下注证,甲状腺功能亢进症,甲状腺功能低下,甲状腺结节,心悸,口腔溃疡,肝郁气滞证,乳腺增生,月经不调,室早,胸闷,气短,2型糖尿病,糖尿病,乏力,腰痛,胁痛,蛋白尿,泌尿道感染,子宫肌瘤,脾肾不足证,气血失调证,关节痛,不寐病,肝功能异常,失眠,肾结石,胆囊结石,气血失调证,肝肾亏虚证,脾胃不和证,肝郁气滞证,胸闷,肺结节病,心悸,乏力,不寐病,肝郁气滞证,气血失调证,三叉神经痛,失眠,肝郁气滞证,气血失调证,胸闷,气短,乳腺增生,甲状腺结节,子宫肌瘤,2型糖尿病,尿频,泌尿道感染,抑郁症,肢体麻木,颈椎病,脑梗塞,气虚血瘀证,脾肾两虚证,胸闷,气短,心律失常,糖尿病肾病,支气管扩张,水肿,痰瘀阻络证,待查4,脾肾两虚证,气血失调证,血糖水平升高,尿频,肝肾亏虚证,气血失调证,蛋白尿,月经过少,月经不调,脾肾两虚证,气血失调证,子宫肌瘤,乳腺增生,甲状腺结节,头晕,乏力,脾肾亏虚证,胃胀,肝郁气滞证,脾胃失调证,糖尿病肾病,支气管扩张,水肿,痰瘀阻络证,待查4,脾肾两虚证,气血失调证,心悸,乏力,不寐病,肝郁气滞证,气血失调证,乏力,月经不调,胃胀,脾肾两虚证,肝郁气滞证,气血失调证,乏力,月经不调,胃胀,脾肾两虚证,肝郁气滞证,气血失调证,2型糖尿病,糖尿病性周围血管病,腰痛,糖尿病,脾肾两亏证,气血失调证,心悸,头痛,肺胃热盛证,乏力,颤病,尿频,泌尿道感染,胃痛,便秘,腹胀,肝胃不调证,脾胃失调证,肝郁气滞证,月经不调,甲状腺术后,乳腺术后,肝郁气滞证,气血失调证,脾肾两虚证,失眠,胃胀病,心悸,气血失调证,脾肾亏虚证,胸闷,脾肾两虚证,焦虑状态,肝郁气滞证,糖尿病,失眠,蛋白尿,颤病,脾肾亏虚证,气血失调证,肝郁气滞证,月经不调,脾肾亏虚证,气血失调证,肝郁气滞证,子宫肌瘤,卵巢囊肿,乏力,湿疹,胸闷,气短,心悸,湿热内蕴证,胃胀病,肝脾气滞证,瘙痒,肥胖,脂肪肝,脾胃失调证,湿疹,湿热内蕴证,代谢综合征,肥胖病,不寐病,呃逆病,脾肾两虚证,肝郁气滞证,心肾不交证,尿频,泌尿道感染,乏力,胃脘痛,不寐病,关节痛,肝郁气滞证,气血失调证,肝郁脾虚证,2型糖尿病,乏力,气短,遗尿,肺肾两虚证,气血失调证,蛋白尿,腰痛,胃胀,呃逆,失眠,脾胃失调证,肝郁气滞证,气血失调证,脂肪肝,2型糖尿病,蛋白尿,泌尿道感染,失眠,脾肾两虚证,气血失调证,不寐病,门脉高压,白癜风,脾大,血小板减少症,脾肾亏虚证,气血失调证,胃胀,不寐病,肝郁气滞证,气血失调证,心肾不交证";
//        content = "肝郁气滞证,脾肾两虚证,气血失调证,湿疹,呃逆,湿热内蕴证,湿疹,湿热内蕴证,不寐病,肝郁气滞证,气血失调证,胃痛,呃逆,呃逆病,不寐病,肝郁气滞证,气血失调证,湿疹,湿热内蕴证,蛋白尿,气血失调证,腹胀,脾肾亏虚证,尿频,湿热内蕴证,糖尿病,2型糖尿病,蛋白尿,脾肾两虚证,气血失调证,尿频,腰痛,糖尿病,2型糖尿病,乏力,蛋白尿,气血失调证,脾肾亏虚证,糖尿病,2型糖尿病,不寐病,肝郁气滞证,脾肾两虚证,气血失调证,胃胀病,胃胀,脾肾两虚证,气血失调证,高尿酸血症,乏力,脾肾两虚证,水肿,气血失调证,尿频,乳腺增生,肝郁气滞证,气血失调证,胃胀病,胃胀,不寐病,肝郁气滞证,气血失调证,胃胀病,胃胀,肝功能异常,蛋白尿,脾胃失调证,气血失调证,胃胀病,胃胀,颤病,乏力,肺胃热盛证,气血失调证,腰痛,糖尿病,头痛,糖尿病性周围血管病,心悸,2型糖尿病,失眠,乏力,肝郁气滞证,脾肾两虚证,水肿,气血失调证,脾肾亏虚证,胁痛,胃胀病,胃胀,胸闷,气短,湿疹,湿热内蕴证,胃胀病,胃胀,心悸,肝脾气滞证,气血失调证,高脂血症,糖尿病,2型糖尿病,不寐病,便秘,肝郁气滞证,气血失调证,乏力,蛋白尿,气血失调证,脾肾亏虚证,糖尿病,2型糖尿病,胃肠功能紊乱,蛋白尿,肝郁气滞证,脾肾两虚证,气血失调证,腰痛,糖尿病,高尿酸血症,蛋白尿,水肿,气血失调证,腹胀,尿频,高脂血症,糖尿病,2型糖尿病,乏力,脾肾亏虚证,痰瘀阻络证,糖尿病,2型糖尿病,胃肠功能紊乱,失眠,气血失调证,头痛,动脉狭窄,肝脾气滞证,失眠,肝郁气滞证,心肾不交证,脾肾亏虚证,不寐病,乏力,脾肾两虚证,心肾不交证,气血失调证,高脂血症,心悸,颤病,失眠,气短,肝郁气滞证,水肿,高脂血症,肾结石,胸闷,失眠,肝郁气滞证,颈椎病,气血失调证,尿频,糖尿病,肺结节病,头痛,2型糖尿病,不寐病,便秘,肝郁气滞证,气血失调证,呃逆,胃胀病,呃逆病,胃胀,头晕,气短,关节痛,膝关节痛,胸闷,尿频,脾肾亏虚证,甲状腺结节,心悸,肺结节病头晕,气短,关节痛,膝关节痛,胸闷,动脉粥样硬化并高血脂症,口干,电解质紊乱,胁痛颈椎病,椎基底动脉供血不足,眩晕,不寐病,肢体麻木,肝阳上亢证,肝郁气滞证水肿,关节痛,高血糖,高脂血症,高尿酸血症,脾肾亏虚证,甲状腺结节,颈椎病2型糖尿病,糖尿病性周围血管病,腰痛,糖尿病,脾肾两亏证,气血失调证,心悸,头痛,肺胃热盛证,乏力,颤病肺结节病,房颤,湿热内蕴证,痰瘀互结证,高脂血症,前列腺肥大,尿频胃胀,呃逆,失眠,脾胃失调证,肝郁气滞证,气血失调证,脂肪肝肝功能异常,乏力,失眠,脂肪肝,胆囊息肉,气血失调证,脾肾两虚证,肝郁气滞证听力减退,气血失调证,脾肾亏虚证,眩晕,尿频,腰痛,高血糖失眠,月经不调,心肾不交证,气血失调证,肝脾不调证,心悸,关节痛,颈椎病心悸,淋巴结肿大,胸闷,气短,肝郁气滞证,气血失调证,腹胀,胁痛,甲状腺结节,肺结节病,气血不足证2型糖尿病,高脂血症,冠心病支架术后,肾功能不全,脾肾亏虚证,气血失调证,动脉狭窄糖尿病,胃肠功能紊乱,脾肾两虚证,气血失调证,腰痛,胃胀,肝郁气滞证,蛋白尿舌痛,肝郁气滞证,不寐病,呃逆病,心火上炎证乏力,月经不调,胃胀,脾肾两虚证,肝郁气滞证,气血失调证乏力,月经不调,胃胀,脾肾两虚证,肝郁气滞证,气血失调证高血糖,2型糖尿病,蛋白尿,高尿酸血症,阴虚内热证,胸闷,肝郁气滞证,气血失调证,心悸泌尿道感染,湿疹,瘙痒症,脾肾亏虚证,气血失调证,尿频,湿热内蕴证,失眠,腹胀,糖尿病,湿热下注证甲状腺功能亢进症,甲状腺功能低下,甲状腺结节,心悸,口腔溃疡,肝郁气滞证,乳腺增生,月经不调,室早,胸闷,气短2型糖尿病,糖尿病,乏力,腰痛,胁痛,蛋白尿,泌尿道感染,子宫肌瘤,脾肾不足证,气血失调证,关节痛,不寐病,肝功能异常失眠,肾结石,胆囊结石,气血失调证,肝肾亏虚证,脾胃不和证,肝郁气滞证,胸闷,肺结节病？心悸,乏力,不寐病,肝郁气滞证,气血失调证三叉神经痛,失眠,肝郁气滞证,气血失调证,胸闷,气短,乳腺增生,甲状腺结节,子宫肌瘤2型糖尿病,尿频,泌尿道感染,抑郁症,肢体麻木,颈椎病,脑梗塞,气虚血瘀证,脾肾两虚证胸闷,气短,心律失常,糖尿病肾病,支气管扩张,水肿,痰瘀阻络证,待查4,脾肾两虚证,气血失调证血糖水平升高,尿频,肝肾亏虚证,气血失调证,蛋白尿月经过少,月经不调,脾肾两虚证,气血失调证,子宫肌瘤,乳腺增生,甲状腺结节头晕,乏力,脾肾亏虚证,胃胀,肝郁气滞证,脾胃失调证糖尿病肾病,支气管扩张,水肿,痰瘀阻络证,待查4,脾肾两虚证,气血失调证心悸,乏力,不寐病,肝郁气滞证,气血失调证乏力,月经不调,胃胀,脾肾两虚证,肝郁气滞证,气血失调证乏力,月经不调,胃胀,脾肾两虚证,肝郁气滞证,气血失调证2型糖尿病,糖尿病性周围血管病,腰痛,糖尿病,脾肾两亏证,气血失调证,心悸,头痛,肺胃热盛证,乏力,颤病,尿频,泌尿道感染胃痛,便秘,腹胀,肝胃不调证,脾胃失调证,肝郁气滞证月经不调,甲状腺术后,乳腺术后,肝郁气滞证,气血失调证,脾肾两虚证失眠,胃胀病,心悸,气血失调证,脾肾亏虚证,胸闷,脾肾两虚证,焦虑状态,肝郁气滞证糖尿病,失眠,蛋白尿,颤病,脾肾亏虚证,气血失调证,肝郁气滞证月经不调,脾肾亏虚证,气血失调证,肝郁气滞证,子宫肌瘤,卵巢囊肿,乏力湿疹,胸闷,气短,心悸,湿热内蕴证,胃胀病,肝脾气滞证瘙痒,肥胖,脂肪肝,脾胃失调证,湿疹,湿热内蕴证,代谢综合征,肥胖病不寐病,呃逆病,脾肾两虚证,肝郁气滞证,心肾不交证尿频,泌尿道感染,乏力,胃脘痛,不寐病,关节痛,肝郁气滞证,气血失调证,肝郁脾虚证2型糖尿病,乏力,气短,遗尿,肺肾两虚证,气血失调证,蛋白尿,腰痛胃胀,呃逆,失眠,脾胃失调证,肝郁气滞证,气血失调证,脂肪肝2型糖尿病,蛋白尿,泌尿道感染,失眠,脾肾两虚证,气血失调证,不寐病门脉高压,白癜风,脾大,血小板减少症,脾肾亏虚证,气血失调证胃胀,不寐病,肝郁气滞证,气血失调证,心肾不交证";
//
//        String[] strings = content.split(",");
//        for (String value : strings) {
//            dataSet.add(value);
//        }
//        for (String value : dataSet) {
//            int number = appearNumber(content, value);
//            countMap.put(value, number);
//        }
//
//        //自定义比较器
//        Comparator<Map.Entry<String, Integer>> valCmp1 = (o1, o2) -> {
//            return o2.getValue()-o1.getValue();  // 降序排序，如果想升序就反过来
//        };
//        //将map转成List，map的一组key，value对应list一个存储空间
//        List<Map.Entry<String, Integer>> dataList = new ArrayList<Map.Entry<String,Integer>>(countMap.entrySet()); //传入maps实体
//        Collections.sort(dataList,valCmp1); // 注意此处Collections 是java.util包下面的,传入List和自定义的valCmp比较器
//
//        System.out.println("targetContent dataList result: " + JSON.toJSONString(dataList));
//        System.out.println("targetContent dataList result: " + dataList);
//    }
//
//    public static List<Map.Entry<String, Integer>> querySymptomAndMedicineCount(String text) {
//        Map<String, Integer> countMap = new HashMap<>();
//        Set<String> dataSet = new TreeSet<>();
//        String[] strings = text.split(",");
//        for (String value : strings) {
//            dataSet.add(value);
//        }
//        for (String value : dataSet) {
//            int number = appearNumberExpand(text, value);
//            countMap.put(value, number);
//        }
//        //自定义比较器
//        Comparator<Map.Entry<String, Integer>> valCmp1 = (o1, o2) -> {
//            return o2.getValue()-o1.getValue();  // 降序排序，如果想升序就反过来
//        };
//        //将map转成List，map的一组key，value对应list一个存储空间
//        List<Map.Entry<String, Integer>> dataList = new ArrayList<>(countMap.entrySet()); //传入maps实体
//        Collections.sort(dataList,valCmp1); // 注意此处Collections 是java.util包下面的,传入List和自定义的valCmp比较器
//        return dataList;
//    }
//
//
//    public static int appearNumber(String srcText, String findText) {
//           int count = 0;
//        Pattern p = Pattern.compile(findText);
//        Matcher m = p.matcher(srcText);
//        while (m.find()) {
//            count++;
//            int i = m.start();
//            //System.out.println("第"+count+"次,位置："+i);
//        }
//        return count;
//    }
//
//    public static int appearNumberExpand(String srcText, String findText) {
//        Map<String,Integer> dataMap = new ConcurrentHashMap<String,Integer>();
//        String[] contentArr = srcText.split(",");
//        //遍历字符串
//        for (int i = 0; i < contentArr.length; i++) {
//            String key = contentArr[i];
//            //拿得到的字符串作为键到hashmap集合中去找对应的值，看起返回值
//            Integer value = dataMap.get(key);
//
//
//            if(value==null) {
//                //.如果返回值为null，说明该字符在hashmap集合中不存在，就把该字符作为键，1作为值储存
//                dataMap.put(key, 1);
//            }else {
//                //.如果返回值不为null，说明该字符串在hashmap中存在，把该值加1，然后重新该字符串和对应的值
//                value++;
//                dataMap.put(key, value);
//            }
//        }
//        return dataMap.get(findText) == null ? 0 : dataMap.get(findText);
//    }
//}
