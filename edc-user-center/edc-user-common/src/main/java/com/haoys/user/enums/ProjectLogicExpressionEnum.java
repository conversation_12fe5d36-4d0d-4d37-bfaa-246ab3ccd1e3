package com.haoys.user.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * @description :逻辑条件
 **/
@Getter
@AllArgsConstructor
public enum ProjectLogicExpressionEnum {

    ALL(1, "全部"),
    ANY(2, "任意");


    private Integer value;

    private String desc;


    /**
     * 枚举入参注解
     */
    public static ProjectLogicExpressionEnum getByValue(Integer value) {
        for (ProjectLogicExpressionEnum typeEnum : values()) {
            if (typeEnum.getValue().equals(value)) {
                return typeEnum;
            }
        }
        return null;
    }
}
