package com.haoys.user.common.thirdservice;

public class PlatformBussinessConfig {

    /**同步平台名称*/
    public static final String RESEACHER_PLATFORM_NAME = "科研平台统计分析";

    /**同步用户数据*/
    public static final String RESEACHER_REQUEST_SYNC_USER_URL = "/api/user/syncSysUser";

    /**同步用户样本数据*/
    public static final String RESEACHER_REQUEST_SYNC_DATA_URL = "/api/user/dataset/syncUserData";

    /**获取第三方统计分析平台验证码*/
    public static final String RESEACHER_REQUEST_CODE_DATA_URL = "/getVerifyCode";

    /**获取登录跳转地址*/
    public static final String RESEACHER_REQUEST_REDIRECT_URL = "/login";

    /**同步用户默认密码*/
    public static final String RESEACHER_DEFAULT_PASSWORD = "123456";

    public static final String RESEACHER_USER_TYPE_TEST = "shiyong";
    public static final String RESEACHER_USER_TYPE_PRODUCT = "linchuang";

}
