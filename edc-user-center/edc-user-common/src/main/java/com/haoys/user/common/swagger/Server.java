package com.haoys.user.common.swagger;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.nio.channels.AsynchronousCloseException;
import java.nio.channels.ServerSocketChannel;
import java.nio.channels.SocketChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Server {
    private static final Logger logger = LoggerFactory.getLogger(Server.class);
    private ServerSocketChannel serverSocketChannel;
    
    public void start(int port) {
        try {
            serverSocketChannel = ServerSocketChannel.open();
            serverSocketChannel.bind(new InetSocketAddress(port));
            logger.info("Server started on port {}", port);
            
            while (true) {
                try {
                    SocketChannel clientChannel = serverSocketChannel.accept();
                    if (clientChannel != null) {
                        logger.info("Accepted connection from {}", clientChannel.getRemoteAddress());
                        // 处理客户端连接
                    }
                } catch (AsynchronousCloseException e) {
                    logger.error("Server socket channel was asynchronously closed", e);
                    // 重新打开通道或采取其他措施
                    break;
                } catch (IOException e) {
                    logger.error("Error accepting connection", e);
                }
            }
        } catch (IOException e) {
            logger.error("Failed to start server", e);
        } finally {
            closeServerSocketChannel();
        }
    }
    
    private void closeServerSocketChannel() {
        if (serverSocketChannel != null && serverSocketChannel.isOpen()) {
            try {
                serverSocketChannel.close();
                logger.info("Server socket channel closed");
            } catch (IOException e) {
                logger.error("Failed to close server socket channel", e);
            }
        }
    }
    
    public void stop() {
        closeServerSocketChannel();
    }
    
    public static void main(String[] args) {
        Server server = new Server();
        server.start(8080);
    }
}
