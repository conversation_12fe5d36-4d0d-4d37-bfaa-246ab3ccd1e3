package com.haoys.user.common.util;

import lombok.extern.slf4j.Slf4j;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;

/**
 * 内存监控工具类
 * 用于监控JVM内存使用情况，特别是在文件上传等内存密集型操作中
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-11
 */
@Slf4j
public class MemoryMonitorUtil {
    
    private static final MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
    private static final long MB = 1024 * 1024;
    
    /**
     * 获取当前内存使用情况
     * 
     * @return 内存使用信息字符串
     */
    public static String getCurrentMemoryInfo() {
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();
        
        StringBuilder sb = new StringBuilder();
        sb.append("堆内存: ");
        sb.append("已用=").append(heapUsage.getUsed() / MB).append("MB, ");
        sb.append("已提交=").append(heapUsage.getCommitted() / MB).append("MB, ");
        sb.append("最大=").append(heapUsage.getMax() / MB).append("MB; ");
        
        sb.append("非堆内存: ");
        sb.append("已用=").append(nonHeapUsage.getUsed() / MB).append("MB, ");
        sb.append("已提交=").append(nonHeapUsage.getCommitted() / MB).append("MB");
        
        return sb.toString();
    }
    
    /**
     * 获取堆内存使用率
     * 
     * @return 使用率百分比
     */
    public static double getHeapMemoryUsagePercent() {
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        return (double) heapUsage.getUsed() / heapUsage.getMax() * 100;
    }
    
    /**
     * 检查内存使用是否超过阈值
     * 
     * @param threshold 阈值百分比（0-100）
     * @return 是否超过阈值
     */
    public static boolean isMemoryUsageHigh(double threshold) {
        return getHeapMemoryUsagePercent() > threshold;
    }
    
    /**
     * 记录当前内存使用情况到日志
     * 
     * @param operation 操作描述
     */
    public static void logMemoryUsage(String operation) {
        String memoryInfo = getCurrentMemoryInfo();
        double usagePercent = getHeapMemoryUsagePercent();
        
        if (usagePercent > 80) {
            log.warn("{}时内存使用率较高: {:.2f}% - {}", operation, usagePercent, memoryInfo);
        } else if (usagePercent > 60) {
            log.info("{}时内存使用情况: {:.2f}% - {}", operation, usagePercent, memoryInfo);
        } else {
            log.debug("{}时内存使用情况: {:.2f}% - {}", operation, usagePercent, memoryInfo);
        }
    }
    
    /**
     * 建议垃圾回收（仅在内存使用率高时）
     * 
     * @param threshold 触发GC的内存使用率阈值
     */
    public static void suggestGCIfNeeded(double threshold) {
        if (isMemoryUsageHigh(threshold)) {
            log.info("内存使用率超过{}%，建议进行垃圾回收", threshold);
            System.gc();
            
            // 等待一小段时间让GC完成
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            logMemoryUsage("GC后");
        }
    }
    
    /**
     * 获取可用内存大小（MB）
     * 
     * @return 可用内存大小
     */
    public static long getAvailableMemoryMB() {
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        return (heapUsage.getMax() - heapUsage.getUsed()) / MB;
    }
    
    /**
     * 检查是否有足够内存处理指定大小的文件
     * 
     * @param fileSizeBytes 文件大小（字节）
     * @param safetyFactor 安全系数（建议2-3倍）
     * @return 是否有足够内存
     */
    public static boolean hasEnoughMemoryForFile(long fileSizeBytes, double safetyFactor) {
        long requiredMemoryMB = (long) (fileSizeBytes * safetyFactor / MB);
        long availableMemoryMB = getAvailableMemoryMB();
        
        boolean hasEnough = availableMemoryMB >= requiredMemoryMB;
        
        if (!hasEnough) {
            log.warn("内存不足以处理文件: 需要{}MB, 可用{}MB", requiredMemoryMB, availableMemoryMB);
        }
        
        return hasEnough;
    }
    
    /**
     * 内存使用情况详细报告
     * 
     * @return 详细的内存报告
     */
    public static String getDetailedMemoryReport() {
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();
        
        StringBuilder report = new StringBuilder();
        report.append("=== JVM内存使用详细报告 ===\n");
        
        // 堆内存
        report.append("堆内存:\n");
        report.append("  初始大小: ").append(heapUsage.getInit() / MB).append(" MB\n");
        report.append("  已使用: ").append(heapUsage.getUsed() / MB).append(" MB\n");
        report.append("  已提交: ").append(heapUsage.getCommitted() / MB).append(" MB\n");
        report.append("  最大大小: ").append(heapUsage.getMax() / MB).append(" MB\n");
        report.append("  使用率: ").append(String.format("%.2f", getHeapMemoryUsagePercent())).append("%\n");
        
        // 非堆内存
        report.append("非堆内存:\n");
        report.append("  初始大小: ").append(nonHeapUsage.getInit() / MB).append(" MB\n");
        report.append("  已使用: ").append(nonHeapUsage.getUsed() / MB).append(" MB\n");
        report.append("  已提交: ").append(nonHeapUsage.getCommitted() / MB).append(" MB\n");
        if (nonHeapUsage.getMax() > 0) {
            report.append("  最大大小: ").append(nonHeapUsage.getMax() / MB).append(" MB\n");
        }
        
        // 系统信息
        Runtime runtime = Runtime.getRuntime();
        report.append("系统内存:\n");
        report.append("  总内存: ").append(runtime.totalMemory() / MB).append(" MB\n");
        report.append("  空闲内存: ").append(runtime.freeMemory() / MB).append(" MB\n");
        report.append("  最大内存: ").append(runtime.maxMemory() / MB).append(" MB\n");
        
        return report.toString();
    }
}
