package com.haoys.user.config;


import com.haoys.user.common.constants.Constants;

import java.util.HashMap;
import java.util.Map;

/**
 * 定义平台基础和业务配置项
 */
public class PlatformBusinessConfig {
    public static Map<String,Boolean> supportSystemUserLoginCaptchaMap = new HashMap<>();

    public Boolean supportSystemUserLoginCaptcha(String platformId){
        supportSystemUserLoginCaptchaMap.put(Constants.SYSTEM_PLATFORMID_EPR, true);
        return supportSystemUserLoginCaptchaMap.get(platformId);
    }





}
