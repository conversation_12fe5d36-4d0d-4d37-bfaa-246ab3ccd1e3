package com.haoys.user.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.stream.ObjectRecord;
import org.springframework.data.redis.connection.stream.RecordId;
import org.springframework.data.redis.connection.stream.StreamRecords;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

@Service
public class MessageSenderService {
    
    @Autowired
    private ReactiveRedisTemplate<String, String> reactiveRedisTemplate;
    
    // 添加重试机制，最多重试3次，每次间隔1秒
    //@Retryable(maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public Mono<RecordId> sendMessage(String stream, String message, long delayMillis, int priority) {
        Map<String, String> map = new HashMap<>();
        map.put("message", message);
        ObjectRecord<String, String> record = StreamRecords.objectBacked(message).withStreamKey(stream);
        
        // 延迟发送消息
        Mono<ObjectRecord<String, String>> delayedRecordMono = Mono.just(record).delayElement(Duration.ofMillis(delayMillis));
        
        // 发送消息
        return delayedRecordMono.flatMap(delayedRecord -> reactiveRedisTemplate.opsForStream().add(delayedRecord));
    }
}

