package com.haoys.user.common.pdf;


import com.lowagie.text.Document;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Element;
import com.lowagie.text.Font;
import com.lowagie.text.FontFactory;
import com.lowagie.text.Image;
import com.lowagie.text.Phrase;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.ColumnText;
import com.lowagie.text.pdf.PdfContentByte;
import com.lowagie.text.pdf.PdfGState;
import com.lowagie.text.pdf.PdfPageEventHelper;
import com.lowagie.text.pdf.PdfWriter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.IOUtils;
import org.springframework.core.io.ClassPathResource;

import java.awt.*;
import java.io.IOException;
import java.io.InputStream;

/**
 * 设置页面附加属性
 *
 * <AUTHOR>
 */
@Slf4j
public class PDFBuilder extends PdfPageEventHelper {

    /**
     * 页眉描述
     */
    private String header = "";

    /**
     * 水印图片地址
     */
    private String waterImgPath;

    /**
     * 水印内容
     */
    private String waterContent;

    /**
     * 文档字体大小，页脚页眉最好和文本大小一致
     */
    private int presentFontSize = 12;

    // 基础字体对象
    private BaseFont bf = null;

    // 利用基础字体生成的字体对象，一般用于生成中文文字
    private Font fontDetail = null;
    
    private static final Font FONT = FontFactory.getFont(FontFactory.HELVETICA, 10);

    /**
     * Creates a new instance of PdfReportM1HeaderFooter 无参构造方法.
     */
    public PDFBuilder() {

    }

    public PDFBuilder(String fontFilePath, String waterImgPath, String waterContent) {
        try {
            if (StringUtils.isNotBlank(fontFilePath)) {
                // 设置文字中文字体
                this.bf = BaseFont.createFont(fontFilePath + ",1", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED, false);
                this.fontDetail = new Font(this.bf, presentFontSize, Font.NORMAL);
            } else {
                //设置分页页眉页脚字体
                if (bf == null) {
                    bf = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", false);
                }
                if (fontDetail == null) {
                    fontDetail = new Font(bf, presentFontSize, Font.NORMAL); // 数据体字体
                }
            }
        } catch (IOException | DocumentException e) {
            e.printStackTrace();
        }
        this.waterImgPath = waterImgPath;
        this.waterContent = waterContent;
    }

    public void setHeader(String header) {
        this.header = header;
    }

    public void setPresentFontSize(int presentFontSize) {
        this.presentFontSize = presentFontSize;
    }

    /**
     * 文档打开时
     */
    @Override
    public void onOpenDocument(PdfWriter writer, Document document) {
    }

    /**
     * 关闭每页的时候，写入页眉，写入'第几页'这几个字。
     *
     * @see com.itextpdf.text.pdf.PdfPageEventHelper#onEndPage(com.itextpdf.text.pdf.PdfWriter,
     * com.itextpdf.text.Document)
     */
    @Override
    public void onEndPage(PdfWriter writer, Document document) {
        // 只在有实际内容的页面添加页眉页脚和水印
        if (hasPageContent(writer, document)) {
            this.addPageHeaderFooter(writer, document);
            this.addWatermark(writer, document);
        }
    }

    /**
     * 检查页面是否有实际内容
     * @param writer PDF写入器
     * @param document 文档对象
     * @return 是否有内容
     */
    private boolean hasPageContent(PdfWriter writer, Document document) {
        // 简单的内容检查：如果页面高度使用率很低，可能是空白页
        return true; // 暂时返回true，后续可以根据需要优化
    }
    
    // 添加页眉页脚
    private void addPageHeaderFooter(PdfWriter writer, Document document) {
        int currentPageNumber = writer.getPageNumber();

        // 封面页（第1页）不添加页眉页脚
        if (currentPageNumber <= 1) {
            return;
        }

        // 计算实际页码（排除封面页）
        int displayPageNumber = currentPageNumber - 1;

        // 获取页面尺寸
        Rectangle rect = document.getPageSize();
        float width = rect.getWidth();
        float height = rect.getHeight();

        try {
            // 1. 添加页眉（编号左对齐，姓名右对齐）
            if (StringUtils.isNotBlank(header)) {
                addAlignedHeader(writer, document, header, width, height);

                // 添加页眉横线
                PdfContentByte pdfContentByte = writer.getDirectContent();
                pdfContentByte.moveTo(document.left(), height - 40);
                pdfContentByte.lineTo(document.right(), height - 40);
                pdfContentByte.stroke();
            }

            // 2. 添加页脚（页码）
            String footerText = "第 " + displayPageNumber + " 页";
            Phrase footer = new Phrase(footerText, fontDetail);

            // 计算页脚文本宽度
            float footerWidth = bf.getWidthPoint(footerText, presentFontSize);

            // 居中显示页脚
            ColumnText.showTextAligned(
                writer.getDirectContent(),
                Element.ALIGN_CENTER,
                footer,
                width / 2,
                document.bottom() + 15,
                0
            );

        } catch (Exception e) {
            // 记录错误但不影响PDF生成
            log.warn("添加页眉页脚时出错: {}", e.getMessage());
        }
    }

    /**
     * 添加两边对齐的页眉（编号左对齐，姓名右对齐）
     * @param writer PDF写入器
     * @param document 文档对象
     * @param header 页眉内容，格式：编号:xxx 姓名:xxx
     * @param width 页面宽度
     * @param height 页面高度
     */
    private void addAlignedHeader(PdfWriter writer, Document document, String header, float width, float height) {
        try {
            log.debug("处理页眉对齐，原始header: {}", header);

            // 检查字体对象
            if (fontDetail == null) {
                log.warn("字体对象fontDetail为null，无法显示页眉");
                return;
            }

            // 解析页眉内容，分离编号和姓名
            String leftText = "";  // 编号部分
            String rightText = ""; // 姓名部分

            // 解析header字符串，格式：编号:xxx 姓名:xxx
            if (header.contains("编号:") && header.contains("姓名:")) {
                // 查找"姓名:"的位置
                int nameIndex = header.indexOf("姓名:");
                if (nameIndex > 0) {
                    // 分离编号和姓名部分
                    leftText = header.substring(0, nameIndex).trim();  // 编号:xxx（去掉末尾空格）
                    rightText = header.substring(nameIndex).trim();    // 姓名:xxx
                } else {
                    // 如果找不到姓名位置，尝试用空格分割
                    String[] parts = header.split("\\s+");
                    for (int i = 0; i < parts.length; i++) {
                        if (parts[i].startsWith("编号:")) {
                            leftText = parts[i];
                        } else if (parts[i].startsWith("姓名:")) {
                            rightText = parts[i];
                        }
                    }
                }

                log.debug("解析结果 - 左侧: '{}', 右侧: '{}'", leftText, rightText);
            } else {
                log.debug("页眉格式不匹配，使用居中显示");
                // 如果格式不匹配，使用原来的居中显示方式
                ColumnText.showTextAligned(
                    writer.getDirectContent(),
                    Element.ALIGN_CENTER,
                    new Phrase(header, fontDetail),
                    width / 2,
                    height - 30,
                    0
                );
                return;
            }

            // 计算页面边距
            float leftMargin = 50;  // 固定左边距
            float rightMargin = width - 50;  // 固定右边距

            log.debug("页面宽度: {}, 左边距: {}, 右边距: {}", width, leftMargin, rightMargin);

            // 左对齐显示编号
            if (StringUtils.isNotBlank(leftText)) {
                ColumnText.showTextAligned(
                    writer.getDirectContent(),
                    Element.ALIGN_LEFT,
                    new Phrase(leftText, fontDetail),
                    leftMargin,
                    height - 30,
                    0
                );
                log.debug("已添加左对齐编号: {}", leftText);
            }

            // 右对齐显示姓名
            if (StringUtils.isNotBlank(rightText)) {
                ColumnText.showTextAligned(
                    writer.getDirectContent(),
                    Element.ALIGN_RIGHT,
                    new Phrase(rightText, fontDetail),
                    rightMargin,
                    height - 30,
                    0
                );
                log.debug("已添加右对齐姓名: {}", rightText);
            }

        } catch (Exception e) {
            // 如果解析失败，使用原来的居中显示方式
            log.warn("页眉对齐处理失败，使用默认居中显示: {}", e.getMessage());
            ColumnText.showTextAligned(
                writer.getDirectContent(),
                Element.ALIGN_CENTER,
                new Phrase(header, fontDetail),
                width / 2,
                height - 30,
                0
            );
        }
    }

    // 添加水印
    public void addWatermark(PdfWriter writer, Document document) {
        try {
            // 水印图片
            Image waterImg = null;
            if (StringUtils.isNotBlank(waterImgPath)) {
                InputStream inputStream = new ClassPathResource(waterImgPath).getInputStream();
                waterImg = Image.getInstance(IOUtils.toByteArray(inputStream));
            }
            PdfContentByte pdfContent = writer.getDirectContent();
            // 开始写入水印
            pdfContent.beginText();
            // 设置水印透明度
            PdfGState gs = new PdfGState();
            // 水印透明度
            gs.setFillOpacity(1f);
            pdfContent.setGState(gs);
            // 文字水印
            pdfContent.setColorFill(Color.LIGHT_GRAY);
            // 字体大小
            pdfContent.setFontAndSize(bf, presentFontSize);
            // showTextAligned 方法的参数分别是（文字对齐方式，位置内容，输出水印X轴位置，Y轴位置，旋转角度）
            pdfContent.showTextAligned(Element.ALIGN_LEFT, waterContent, document.right() - 170, document.bottom() + 15, 0);

            int length = waterContent.length();
            // 图片水印
            if (waterImg != null) {
                // 设置坐标 绝对位置 X Y
                waterImg.setAbsolutePosition(document.right() - (180 + length), document.bottom() + 10);
                // 设置旋转弧度
                waterImg.setRotation(0);// 旋转 弧度
                // 设置旋转角度
                waterImg.setRotationDegrees(0);// 旋转 角度
                // 设置等比缩放
                waterImg.scaleAbsolute(20, 20);// 自定义大小
                // 写入图片水印
                pdfContent.addImage(waterImg);
            }
            // 结束写入水印
            pdfContent.endText();
        } catch (IOException | DocumentException e) {
            e.printStackTrace();
        }
    }

    /**
     * 关闭文档时
     */
    public void onCloseDocument(PdfWriter writer, Document document) {
    }
}
