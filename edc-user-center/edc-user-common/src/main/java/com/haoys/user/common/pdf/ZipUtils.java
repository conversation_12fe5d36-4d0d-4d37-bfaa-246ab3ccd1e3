package com.haoys.user.common.pdf;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * ZIP文件压缩工具类
 * <AUTHOR>
 * @describe 压缩多个文件
 * @date 2023/07/18 18:10
 * @updated 2025/07/14 修复资源管理和异常处理问题
 */
@Slf4j
public class ZipUtils {

    /**
     * 批量文件压缩到ZIP - 修复版本
     * @param byteList 文件名和字节流的映射
     * @param byteOutPutStream 输出流
     * @throws RuntimeException 当压缩过程出现错误时
     */
    public static void batchFileToZIP(Map<String, ByteArrayOutputStream> byteList, ByteArrayOutputStream byteOutPutStream) {
        // 参数验证
        if (byteList == null || byteList.isEmpty()) {
            log.warn("输入的文件列表为空，无法创建ZIP文件");
            return;
        }

        if (byteOutPutStream == null) {
            throw new IllegalArgumentException("输出流不能为空");
        }

        // 使用try-with-resources确保资源正确释放
        try (ZipOutputStream zipOutputStream = new ZipOutputStream(byteOutPutStream)) {
            log.info("开始创建ZIP文件，包含 {} 个文件", byteList.size());

            for (Map.Entry<String, ByteArrayOutputStream> entry : byteList.entrySet()) {
                String fileName = entry.getKey();
                ByteArrayOutputStream fileData = entry.getValue();

                // 验证文件数据
                if (fileName == null || fileName.trim().isEmpty()) {
                    log.warn("跳过空文件名的条目");
                    continue;
                }

                if (fileData == null || fileData.size() == 0) {
                    log.warn("跳过空文件数据的条目: {}", fileName);
                    continue;
                }

                try {
                    // 创建ZIP条目
                    zipOutputStream.putNextEntry(new ZipEntry(fileName));
                    // 写入文件数据
                    zipOutputStream.write(fileData.toByteArray());
                    // 关闭当前条目 - 修复：每个条目都要关闭
                    zipOutputStream.closeEntry();

                    log.debug("成功添加文件到ZIP: {} (大小: {} bytes)", fileName, fileData.size());
                } catch (IOException e) {
                    log.error("添加文件到ZIP时发生错误: {}", fileName, e);
                    throw new RuntimeException("压缩文件失败: " + fileName, e);
                }
            }

            // 确保所有数据都写入
            zipOutputStream.flush();
            log.info("ZIP文件创建完成，总大小: {} bytes", byteOutPutStream.size());

        } catch (IOException e) {
            log.error("创建ZIP文件时发生IO错误", e);
            throw new RuntimeException("ZIP文件创建失败", e);
        }
    }

    /**
     * 下载ZIP文件 - 修复版本
     * @param byteFileMap 文件名和字节流的映射
     * @return ZIP文件的字节流，如果输入为空则返回null
     */
    public static ByteArrayOutputStream downZipFile(Map<String, ByteArrayOutputStream> byteFileMap) {
        if (byteFileMap == null || byteFileMap.isEmpty()) {
            log.warn("输入的文件映射为空，返回null");
            return null;
        }

        try {
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            batchFileToZIP(byteFileMap, byteArrayOutputStream);

            if (byteArrayOutputStream.size() == 0) {
                log.warn("生成的ZIP文件为空");
                return null;
            }

            log.info("ZIP文件生成成功，大小: {} bytes", byteArrayOutputStream.size());
            return byteArrayOutputStream;

        } catch (Exception e) {
            log.error("生成ZIP文件失败", e);
            return null;
        }
    }
}

