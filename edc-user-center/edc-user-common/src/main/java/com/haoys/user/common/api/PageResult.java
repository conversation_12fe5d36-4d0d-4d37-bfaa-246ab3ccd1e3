package com.haoys.user.common.api;

import java.util.List;

/**
 * 分页结果封装类
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
public class PageResult<T> {
    
    /**
     * 当前页码
     */
    private Integer pageNum;
    
    /**
     * 每页数量
     */
    private Integer pageSize;
    
    /**
     * 总页数
     */
    private Integer totalPage;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 数据列表
     */
    private List<T> list;
    
    /**
     * 是否有下一页
     */
    private Boolean hasNextPage;
    
    /**
     * 是否有上一页
     */
    private Boolean hasPreviousPage;
    
    /**
     * 创建空结果
     */
    public static <T> PageResult<T> empty() {
        PageResult<T> result = new PageResult<>();
        result.setPageNum(1);
        result.setPageSize(0);
        result.setTotalPage(0);
        result.setTotal(0L);
        result.setHasNextPage(false);
        result.setHasPreviousPage(false);
        return result;
    }
    
    /**
     * 创建成功结果
     */
    public static <T> PageResult<T> success(List<T> list, Long total, Integer pageNum, Integer pageSize) {
        PageResult<T> result = new PageResult<>();
        result.setList(list);
        result.setTotal(total);
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);
        
        // 计算总页数
        if (pageSize > 0) {
            int totalPage = (int) ((total + pageSize - 1) / pageSize);
            result.setTotalPage(totalPage);
            result.setHasNextPage(pageNum < totalPage);
            result.setHasPreviousPage(pageNum > 1);
        } else {
            result.setTotalPage(0);
            result.setHasNextPage(false);
            result.setHasPreviousPage(false);
        }
        
        return result;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(Integer totalPage) {
        this.totalPage = totalPage;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    public Boolean getHasNextPage() {
        return hasNextPage;
    }

    public void setHasNextPage(Boolean hasNextPage) {
        this.hasNextPage = hasNextPage;
    }

    public Boolean getHasPreviousPage() {
        return hasPreviousPage;
    }

    public void setHasPreviousPage(Boolean hasPreviousPage) {
        this.hasPreviousPage = hasPreviousPage;
    }
}
