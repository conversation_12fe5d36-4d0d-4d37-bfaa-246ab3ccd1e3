package com.haoys.user.common.ocr;


import com.haoys.user.common.constants.SystemTokenConstant;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.GeneralAccurateOCRRequest;
import com.tencentcloudapi.ocr.v20181119.models.GeneralAccurateOCRResponse;
import com.tencentcloudapi.ocr.v20181119.models.RecognizeTableAccurateOCRRequest;
import com.tencentcloudapi.ocr.v20181119.models.RecognizeTableAccurateOCRResponse;
import com.tencentcloudapi.ocr.v20181119.models.SmartStructuralOCRRequest;
import com.tencentcloudapi.ocr.v20181119.models.SmartStructuralOCRResponse;
import com.tencentcloudapi.ocr.v20181119.models.SmartStructuralProRequest;
import com.tencentcloudapi.ocr.v20181119.models.SmartStructuralProResponse;
import com.tencentcloudapi.tmt.v20180321.TmtClient;
import com.tencentcloudapi.tmt.v20180321.models.TextTranslateRequest;
import com.tencentcloudapi.tmt.v20180321.models.TextTranslateResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TencentAIService {


    public static final Credential CRED = new Credential(SystemTokenConstant.SECRET_ID, SystemTokenConstant.SECRET_KEY);

    /**
     * 表格识别
     * @param imageUrl
     * @param filePath
     * @return
     */
    public String recognizeTableAccurateOCR(String imageUrl, String filePath) {
        try{
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("ocr.tencentcloudapi.com");
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            OcrClient client = new OcrClient(CRED, "ap-beijing", clientProfile);
            RecognizeTableAccurateOCRRequest req = new RecognizeTableAccurateOCRRequest();
            //req.setImageUrl("http://hysh-dev.haoyisheng.com.cn/805936322727514112/medical_report_detection/407c1189849b418d965741bc1b9b49f0.png");
            //req.setImageUrl(imageUrl);
            req.setImageBase64(ImageBase64Util.getImageString(filePath));
            RecognizeTableAccurateOCRResponse resp = client.RecognizeTableAccurateOCR(req);
            return RecognizeTableAccurateOCRResponse.toJsonString(resp);
        } catch (TencentCloudSDKException e) {
            log.error("recognizeTableAccurateOCR:{}, message:{}, errorCode:{}",e.getStackTrace(), e.getMessage(), e.getErrorCode());
        }
        return null;
    }


    /**
     * 智能结构化
     * @param imageUrl
     * @param filePath
     * @return
     */
    public String smartStructuralOCR(String imageUrl, String filePath) {
        try{
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("ocr.tencentcloudapi.com");
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            OcrClient client = new OcrClient(CRED, "ap-beijing", clientProfile);
            SmartStructuralOCRRequest req = new SmartStructuralOCRRequest();
            //req.setImageUrl(imageUrl);
            req.setImageBase64(ImageBase64Util.getImageString(filePath));
            req.setReturnFullText(true);
            SmartStructuralOCRResponse resp = client.SmartStructuralOCR(req);
            return SmartStructuralOCRResponse.toJsonString(resp);
        } catch (TencentCloudSDKException e) {
            log.error("smartStructuralOCR:{}, message:{}, errorCode:{}",e.getStackTrace(), e.getMessage(), e.getErrorCode());
        }
        return null;
    }
    
    public String smartStructuralProOCR(String imageUrl, String filePath) {
        try{
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("ocr.tencentcloudapi.com");
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            OcrClient client = new OcrClient(CRED, "ap-beijing", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            SmartStructuralProRequest req = new SmartStructuralProRequest();
            req.setImageBase64(ImageBase64Util.getImageString(filePath));
            req.setReturnFullText(true);
            // 返回的resp是一个SmartStructuralProResponse的实例，与请求对象对应
            SmartStructuralProResponse resp = client.SmartStructuralPro(req);
            return SmartStructuralProResponse.toJsonString(resp);
        } catch (TencentCloudSDKException e) {
            log.error("smartStructuralProOCR:{}, message:{}, errorCode:{}",e.getStackTrace(), e.getMessage(), e.getErrorCode());
        }
        return null;
    }


    public String generalAccurateOCR(String imageUrl, String filePath) {
        try{
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("ocr.tencentcloudapi.com");
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            OcrClient client = new OcrClient(CRED, "ap-beijing", clientProfile);
            GeneralAccurateOCRRequest req = new GeneralAccurateOCRRequest();
            req.setImageBase64(ImageBase64Util.getImageString(filePath));
            GeneralAccurateOCRResponse resp = client.GeneralAccurateOCR(req);
            return GeneralAccurateOCRResponse.toJsonString(resp);
        } catch (TencentCloudSDKException e) {
            log.error("generalAccurateOCR:{}, message:{}, errorCode:{}",e.getStackTrace(), e.getMessage(), e.getErrorCode());
        }
        return null;
    }
    
    
    
    public String translateText(String sourceText) {
        try{
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("tmt.tencentcloudapi.com");
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            TmtClient client = new TmtClient(CRED, "ap-beijing", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            TextTranslateRequest req = new TextTranslateRequest();
            req.setSource("en");
            req.setTarget("zh");
            req.setProjectId(1326448L);
            req.setSourceText(sourceText);
            TextTranslateResponse resp = client.TextTranslate(req);
            String targetText = resp.getTargetText();
            return targetText;
            // {"TargetText":"了解乳腺癌治疗选择可以帮助家庭医生在癌症治疗期间和治疗后照顾患者。...含有Anthracycline和紫杉醇的化疗方案对乳腺癌有效。",
            // "Source":"en","Target":"zh","RequestId":"7d0c3368-7521-4961-9876-e7da10862222"}
        } catch (TencentCloudSDKException e) {
            log.error("translateText:{}, message:{}, errorCode:{}",e.getStackTrace(), e.getMessage(), e.getErrorCode());
        }
        return null;
    }
}
