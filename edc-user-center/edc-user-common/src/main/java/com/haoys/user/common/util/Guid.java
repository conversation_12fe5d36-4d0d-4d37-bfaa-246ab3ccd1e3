package com.haoys.user.common.util;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

public class Guid {

    private static AtomicInteger COUNTER = new AtomicInteger();

    private static Set<Long> TIME_SET = new HashSet<>();

    public String get() {
        StringBuffer now = new StringBuffer(new SimpleDateFormat(
                "yyyyMMddHHmmssSSS").format(new Date()));
        int n = (int) (Math.random() * 90000.0D + 10000.0D);
        return now.append(n).toString();
    }

    public static Long getId() {
        String now = new SimpleDateFormat("yyyyMMdd").format(new Date());
        Long time = Long.valueOf(now);

        if (!TIME_SET.contains(time)) {
            COUNTER.set(0);
            TIME_SET.clear();
            TIME_SET.add(time);
        }

        Long id = (time * 10000) + COUNTER.addAndGet(1);
        return id;
    }

    public static void main(String[] args) {
        for (int i = 0; i < 10; i++) {
            System.out.println(getId());
        }

    }
}
