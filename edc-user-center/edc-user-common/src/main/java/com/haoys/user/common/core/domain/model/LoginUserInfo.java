package com.haoys.user.common.core.domain.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.haoys.user.common.core.domain.entity.BaseSystemUser;
import com.haoys.user.common.util.StringUtils;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


public class LoginUserInfo implements UserDetails {

    private Long id;
    
    private String userName;
    
    private String password;
    
    private String realName;
    
    private String loginCode;

    private Long loginTimeLong;

    private Long expireTime;
    
    private Date expireTimeDate;
    
    private String token;

    private String loginUserTokenValue;

    private Boolean otherDeviceLogin = false;

    private String loginMessage = "";
    
    List<String> systemUserRoleList = new ArrayList<>();

    private Set<String> permissions;

    private Boolean activeStatus = false;

    private Boolean updatePwdWarn = false;
    
    private Date lastModifyPwdTime;

    private String tenantId;

    private String platformId;

    public LoginUserInfo(){}

    public LoginUserInfo(BaseSystemUser baseSystemUser, Set<String> permissions){
        this.id = baseSystemUser.getId();
        this.userName = baseSystemUser.getUsername();
        this.realName = baseSystemUser.getRealName();
        this.password = baseSystemUser.getPassword();
        this.activeStatus = baseSystemUser.getActiveStatus();
        this.permissions = permissions;
        this.lastModifyPwdTime = baseSystemUser.getLastModifyPwdTime();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public boolean isAdmin() {
        return isAdmin(this.id);
    }
    
    public static boolean isAdmin(Long userId) {
        return userId != null && 1L == userId;
    }
    
    public boolean systemAdminUser(){
        return systemAdmin("");
    }
    
    public static boolean systemAdmin(String userType){
        return StringUtils.isNotEmpty(userType) && "1".equals(userType);
    }
    
    @Override
    public String getUsername() {
        return userName;
    }
    
    @JSONField(serialize = false)
    @Override
    public String getPassword() {
        return this.password;
    }
    
    public String getRealName() {
        return realName;
    }
    
    public void setRealName(String realName) {
        this.realName = realName;
    }
    
    @JSONField(serialize = false)
    @Override
    public boolean isAccountNonExpired()
    {
        return true;
    }

    @JSONField(serialize = false)
    @Override
    public boolean isAccountNonLocked()
    {
        return true;
    }

    @JSONField(serialize = false)
    @Override
    public boolean isCredentialsNonExpired()
    {
        return true;
    }

    @JSONField(serialize = false)
    @Override
    public boolean isEnabled()
    {
        return true;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return permissions.stream()
                .map(permission ->new SimpleGrantedAuthority(permission))
                .collect(Collectors.toList());
    }

    public Long getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Long expireTime) {
        this.expireTime = expireTime;
    }
    
    public Date getExpireTimeDate() {
        return expireTimeDate;
    }
    
    public void setExpireTimeDate(Date expireTimeDate) {
        this.expireTimeDate = expireTimeDate;
    }
    
    public Set<String> getPermissions() {
        return permissions;
    }

    public void setPermissions(Set<String> permissions) {
        this.permissions = permissions;
    }

    public String getLoginUserTokenValue() {
        return loginUserTokenValue;
    }

    public void setLoginUserTokenValue(String loginUserTokenValue) {
        this.loginUserTokenValue = loginUserTokenValue;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Long getLoginTimeLong() {
        return loginTimeLong;
    }

    public void setLoginTimeLong(Long loginTimeLong) {
        this.loginTimeLong = loginTimeLong;
    }

    public Boolean getOtherDeviceLogin() {
        return otherDeviceLogin;
    }

    public void setOtherDeviceLogin(Boolean otherDeviceLogin) {
        this.otherDeviceLogin = otherDeviceLogin;
    }

    public String getLoginMessage() {
        return loginMessage;
    }

    public void setLoginMessage(String loginMessage) {
        this.loginMessage = loginMessage;
    }
    
    public List<String> getSystemUserRoleList() {
        return systemUserRoleList;
    }
    
    public void setSystemUserRoleList(List<String> systemUserRoleList) {
        this.systemUserRoleList = systemUserRoleList;
    }
    
    public Boolean getActiveStatus() {
        return activeStatus;
    }

    public void setActiveStatus(Boolean activeStatus) {
        this.activeStatus = activeStatus;
    }

    public String getLoginCode() {
        return loginCode;
    }

    public void setLoginCode(String loginCode) {
        this.loginCode = loginCode;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public Boolean getUpdatePwdWarn() {
        return updatePwdWarn;
    }

    public void setUpdatePwdWarn(Boolean updatePwdWarn) {
        this.updatePwdWarn = updatePwdWarn;
    }
    
    public Date getLastModifyPwdTime() {
        return lastModifyPwdTime;
    }
    
    public void setUserName(String userName) {
        this.userName = userName;
    }
    
    public void setLastModifyPwdTime(Date lastModifyPwdTime) {
        this.lastModifyPwdTime = lastModifyPwdTime;
    }
}
