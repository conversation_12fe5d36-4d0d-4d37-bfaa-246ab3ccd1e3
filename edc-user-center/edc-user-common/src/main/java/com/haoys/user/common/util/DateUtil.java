package com.haoys.user.common.util;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.TimeZone;

public class DateUtil {

    private final static TimeZone TIMEZONE = TimeZone.getTimeZone("GMT+08:00");
    public final static String DEFAULTFORMAT = "yyyy-MM-dd";
    public final static String DATEFORMAT = "yyyy年MM月dd日";
    public final static String MONTHFORMAT = "yyyy-MM";
    public final static String YEARFORMAT = "yyyy";
    public final static String SECONDFORMAT = "yyyy-MM-dd HH:mm:ss";
    public final static String SMSDATE = "yyyyMMddHHmmss";
    public final static String DEFAULTFORMAT_01 = "yyyy/MM/dd";

    /**
     * 数字常量 -3~3
     */
    public static final Integer NUMBER_U3 = -3;
    public static final Integer NUMBER_U2 = -2;
    public static final Integer NUMBER_U1 = -1;
    public static final Integer NUMBER_ZERO = 0;
    public static final Integer NUMBER_ONE = 1;
    public static final Integer NUMBER_TWO = 2;
    public static final Integer NUMBER_TREE = 3;
    public static final Integer NUMBER_FOUR = 4;
    public static final Integer NUMBER_FIVE = 5;
    public static final Integer NUMBER_SIX = 6;
    public static final Integer NUMBER_SEVEN = 7;
    public static final Integer NUMBER_EIGHT = 8;
    public static final Integer NUMBER_NINE = 9;
    public static final Integer NUMBER_TEN = 10;


    private static final Log log = LogFactory.getLog(DateUtil.class);

    /**
     * @param msel 毫秒时间
     * @return 毫秒时间 所对应的"yyyy-MM-dd HH:mm"格式的日期
     */

    public static String formatTime(long msel) {
        Date date = new Date(msel);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        formatter.setTimeZone(TIMEZONE);
        return formatter.format(date);
    }

    public static String formatTime2(long msel) {
        Date date = new Date(msel);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy年MM月dd日");
        formatter.setTimeZone(TIMEZONE);
        return formatter.format(date);
    }

    /**
     * @param msel 毫秒时间
     * @return 毫秒时间 所对应的"yyyy-MM-dd"格式的日期
     */
    public static String formatDate(long msel) {
        Date date = new Date(msel);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        formatter.setTimeZone(TIMEZONE);
        return formatter.format(date);
    }

    public static String formatDate2String(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        formatter.setTimeZone(TIMEZONE);
        return formatter.format(date);
    }

    public static String formatDate2String(Date date, String format) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        formatter.setTimeZone(TIMEZONE);
        return formatter.format(date);
    }

    public static String formatDate2(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        formatter.setTimeZone(TIMEZONE);
        return formatter.format(date);
    }

    /**
     * @param strDate
     * @param strOldFormat
     * @return
     */
    public static String formatDate(String strDate, String strOldFormat, String strNewFormat) {
        Date date = getAutoParseDate(strDate, strOldFormat);
        SimpleDateFormat formatter = new SimpleDateFormat(strNewFormat);
        formatter.setTimeZone(TIMEZONE);
        return formatter.format(date);
    }

    /**
     * 得到系统当前的年
     *
     * @return
     */
    public static int getCurrentYear() {
        GregorianCalendar calendar = new GregorianCalendar();
        return calendar.get(Calendar.YEAR);
    }

    /**
     * 得到系统当前的月份
     *
     * @return
     */
    public static int getCurrentMonth() {
        GregorianCalendar calendar = new GregorianCalendar();
        return calendar.get(Calendar.MONTH) + 1;
    }

    /**
     * 得到系统当前的日期
     *
     * @return
     */
    public static int getCurrentDay() {
        GregorianCalendar calendar = new GregorianCalendar();
        return calendar.get(Calendar.DAY_OF_MONTH);
    }

    public static long getCurrentMsel() {
        GregorianCalendar calendar = new GregorianCalendar();
        return calendar.getTimeInMillis();
    }

    /**
     * 得到系统的当前时间 YYYYMMDD
     *
     * @return
     */
    public static int getThisday() {
        StringBuffer sb = new StringBuffer(8);
        sb.append(getCurrentYear());
        int iMonth = getCurrentMonth();
        if (String.valueOf(iMonth).length() == 1) {
            sb.append("0" + iMonth);
        } else {
            sb.append(iMonth);
        }
        int iDay = getCurrentDay();
        if (String.valueOf(iDay).length() == 1) {
            sb.append("0" + iDay);
        } else {
            sb.append(iDay);
        }
        return new Integer(sb.toString()).intValue();
    }

    public static int getDayDef(int year, int month, int day) {
        StringBuffer sb = new StringBuffer(8);
        sb.append(year);
        int iMonth = month;
        int iDay = day;
        if (String.valueOf(iMonth).length() == 1) {
            sb.append("0" + iMonth);
        } else {
            sb.append(iMonth);
        }
        if (String.valueOf(iDay).length() == 1) {
            sb.append("0" + iDay);
        } else {
            sb.append(iDay);
        }
        return new Integer(sb.toString()).intValue();
    }

    public static int getDayDef(Calendar cal) {
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH);
        int day = cal.get(Calendar.DAY_OF_MONTH);
        return getDayDef(year, month, day);
    }

    /**
     * 将一个字符串的日期描述转换为java.util.Date对象
     *
     * @param strDate 字符串的日期描述
     * @param format  字符串的日期格式，比如:“yyyy-MM-dd HH:mm”
     * @return 字符串转换的日期对象java.util.Date
     */
    public static Date getAutoParseDate(String strDate, String format) {
        if (strDate == null || strDate.trim().isEmpty()) {
            return null;
        }
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        formatter.setTimeZone(TIMEZONE);
        Date date;
        try {
            date = formatter.parse(strDate);
        } catch (ParseException e) {
            date = null;
        }
        return date;
    }

    /**
     * 将一个字符串的日期描述转换为java.util.Date对象
     *
     * @param strDate 支持"yyyy"和"yyyy-MM-dd"格式
     * @return
     */
    public static Date getAutoParseDate(String strDate) {
        Date destDate = null;
        if (strDate != null && !strDate.trim().isEmpty()) {
            if (strDate.trim().length() == NUMBER_FOUR) {
                destDate = DateUtil.getAutoParseDate(strDate, DateUtil.YEARFORMAT);
            } else if (strDate.trim().length() == NUMBER_TEN) {
                destDate = DateUtil.getAutoParseDate(strDate, DateUtil.DEFAULTFORMAT);
            } else {
                destDate = DateUtil.getAutoParseDate(strDate, DateUtil.SECONDFORMAT);
            }
        }
        return destDate;
    }

    public static long getMsel(int i) {
        String original = "20040501";
        if (i > 0) {
            original = Integer.toString(i);
        }
        StringBuffer temp = new StringBuffer();
        temp.append(original).append("000000");
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmm");
        long rs = 0;
        Date date;
        try {
            date = formatter.parse(temp.toString());
        } catch (ParseException e) {
            date = new Date();
        }
        long l = date.getTime();
        return l;
    }

    /**
     * 将字符串型的时间转换为毫秒数，格式默认为yyyy-MM-dd HH:mm
     *
     * @param strDate
     * @return
     */
    public static long getMsel(String strDate) {
        Date date = getAutoParseDate(strDate, "yyyy-MM-dd HH:mm");
        if (date == null) {
            return 0L;
        } else {
            return date.getTime();
        }
    }

    /**
     * 将字符串型的时间转换为毫秒数，格式默认为yyyyMMdd
     *
     * @param strDate
     * @return long
     */
    public static long getMselByFormat(String strDate) {
        Date date = getAutoParseDate(strDate, "yyyyMMdd");
        if (date == null) {
            return 0L;
        } else {
            return date.getTime();
        }
    }

    public static long getMsel(String strDate, String format) {
        Date date = getAutoParseDate(strDate, format);
        if (date == null) {
            return 0L;
        } else {
            return date.getTime();
        }
    }

    /**
     * @param year  年
     * @param month 月
     * @param day   日
     * @return 毫秒数
     */
    public static long getTimeperiodMsel(int year, int month, int day) {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.set(year, month, day, 0, 0, 0);
        return calendar.getTime().getTime();
    }

    /**
     * @param year  年
     * @param month 月
     * @param day   日
     * @return 毫秒数
     */
    public static long getTimeperiodMselInit(int year, int month, int day) {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.set(year, month, day, 23, 59, 59);
        return calendar.getTime().getTime();
    }

    /**
     * @return 当天指定格式的日期
     */
    public static String getCurrentDate() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        return formatter.format(new Date());
    }

    public static String getCurrentTime() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        return formatter.format(new Date());
    }

    public static String getCurrentdate() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        return formatter.format(new Date());
    }

    public static String getCurrenttime() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return formatter.format(new Date());
    }

    /**
     * @param day 推移的天数
     * @return 以当前时间加天数的日期
     */
    public static String addCurrentDate(int day) {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        calendar.add(GregorianCalendar.DAY_OF_MONTH, day);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return format.format(calendar.getTime());
    }

    /**
     * @param day 推移的天数
     * @return date时间加天数的日期
     */
    public static String addDate(String date, int day) {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(getAutoParseDate(date, "yyyy-MM-dd"));
        calendar.add(GregorianCalendar.DAY_OF_MONTH, day);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return format.format(calendar.getTime());
    }

    public static void get7dayFormCur() {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        calendar.add(GregorianCalendar.DAY_OF_MONTH, 3);
        int i = calendar.get(GregorianCalendar.DAY_OF_WEEK);
    }

    /**
     * 得到本周的第一天
     *
     * @return
     */
    public static int getCurrentFirstWeekDay() {
        GregorianCalendar cal = new GregorianCalendar();
        cal.setTime(new Date());
        int day = cal.getMinimum(GregorianCalendar.DAY_OF_WEEK);
        return day;
    }

    /**
     * 得到本周的最后一天
     *
     * @return
     */
    public static int getCurrentLastWeekDay() {
        GregorianCalendar cal = new GregorianCalendar();
        cal.setTime(new Date());
        int day = cal.getMaximum(GregorianCalendar.DAY_OF_WEEK);
        return day;
    }

    /**
     * 得到当月的第一天
     *
     * @return
     */
    public static String getCurrentFirstDay4Month() {
        GregorianCalendar cal = new GregorianCalendar();
        cal.setTime(new Date());
        int day = cal.getMinimum(GregorianCalendar.DAY_OF_MONTH);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        return formatter.format(day);
    }

    /**
     * 得到当月的第一天
     *
     * @return
     */
    public static int getCurrentFirstMonthDay() {
        GregorianCalendar cal = new GregorianCalendar();
        cal.setTime(new Date());
        int day = cal.getMinimum(GregorianCalendar.DAY_OF_MONTH);
        return day;
    }

    /**
     * 得到当月的最后一天
     *
     * @return
     */
    public static String getCurrentLastDay4Month() {
        GregorianCalendar cal = new GregorianCalendar();
        cal.setTime(new Date());
        int day = cal.getMaximum(GregorianCalendar.DAY_OF_MONTH);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        return formatter.format(day);
    }

    /**
     * 获取本月开始日期
     *
     * @return String
     **/
    public static String getMonthStart() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, 0);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        Date time = cal.getTime();
        return new SimpleDateFormat("yyyy-MM-dd").format(time) + " 00:00:00";
    }

    /**
     * 获取本月最后一天
     *
     * @return String
     **/
    public static String getMonthEnd() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        Date time = cal.getTime();
        return new SimpleDateFormat("yyyy-MM-dd").format(time) + " 23:59:59";
    }

    /**
     * 得到当月的最后一天
     *
     * @return
     */
    public static int getCurrentLastMonthDay() {
        GregorianCalendar cal = new GregorianCalendar();
        cal.setTime(new Date());
        int day = cal.getMaximum(GregorianCalendar.DAY_OF_MONTH);
        return day;
    }

    /**
     * 将指定的毫秒转换为YYYYMMDD的格式
     *
     * @param msel
     * @return
     */
    public static int formatMsel(long msel) {
        Date date = new Date(msel);
        GregorianCalendar cal = new GregorianCalendar();
        cal.setTime(date);
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1;
        int day = cal.get(Calendar.DAY_OF_MONTH);
        StringBuffer sb = new StringBuffer();
        sb.append(year);
        if (String.valueOf(month).length() == 1) {
            sb.append("0" + month);
        } else {
            sb.append(month);
        }
        if (String.valueOf(day).length() == 1) {
            sb.append("0" + day);
        } else {
            sb.append(day);
        }
        return new Integer(sb.toString()).intValue();
    }

    public static String getTomorrow(String strDate) throws Exception {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        Date date = formatter.parse(strDate);
        long temp = date.getTime() + 24 * 3600 * 1000;
        return formatDate(temp);
    }

    public static String getYesterday() {
        Date nowDate = new Date();
        long temp = nowDate.getTime() - 24 * 3600 * 1000;
        return formatDate(temp);
    }

    public static String getCurMonday() {
        GregorianCalendar calendar = new GregorianCalendar();

        calendar.set(GregorianCalendar.DAY_OF_WEEK, GregorianCalendar.MONDAY);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        return formatter.format(calendar.getTime());
    }

    public static String getCurSunday() {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.set(GregorianCalendar.DAY_OF_WEEK, GregorianCalendar.SUNDAY);
        calendar.add(GregorianCalendar.DAY_OF_MONTH, 7);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        return formatter.format(calendar.getTime());
    }

    public static int getCurrentDay4Week() {
        GregorianCalendar calendar = new GregorianCalendar();
        return calendar.get(Calendar.DAY_OF_WEEK);
    }

    public static long[] getDayFromNow(Date dateIn) {
        long[] ret = new long[0];

        if (dateIn != null) {
            ret = new long[3];
            long dateInL = dateIn.getTime();
            Date nowDate = new Date();
            long nowDateL = nowDate.getTime();

            long space = 0;
            if (nowDateL > dateInL) {
                space = nowDateL - dateInL;
            } else {
                space = dateInL - nowDateL;
            }

            long min = space / (60 * 1000);
            long day = min / (24 * 60);
            long hour = (min - day * 24 * 60) / 60;
            min = min - day * 24 * 60 - hour * 60;

            ret[0] = day;
            ret[1] = hour;
            ret[2] = min;
            // ret=day+"天"+hour+"小时"+min+"分钟";
        }

        return ret;
    }

    /**
     * 所给日期按照指定格式转换为字符串
     *
     * @param date
     * @param format
     * @return String
     */
    public static String date2String(Date date, String format) {
        if (date == null) {
            return "";
        }
        if (format == null) {
            format = "yyyy-MM-dd";
        } else if ("".equals(format)) {
            format = "yyyy-MM-dd";
        }
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        return formatter.format(date);
    }

    public static String getFirstDay4Quarter(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int month = calendar.get(Calendar.MONTH) + 1;
        String firstMonthOfQuarter = "";
        if (month > 0 && month < NUMBER_FOUR) {
            firstMonthOfQuarter = "01";
        } else if (month > NUMBER_TREE && month < NUMBER_SEVEN) {
            firstMonthOfQuarter = "04";
        } else if (month > NUMBER_SIX && month < NUMBER_TEN) {
            firstMonthOfQuarter = "07";
        } else {
            firstMonthOfQuarter = "10";
        }
        return getCurrentYear() + "-" + firstMonthOfQuarter + "-" + "01";
    }

    public static String getLastDay4Quarter(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int month = calendar.get(Calendar.MONTH) + 1;
        String firstMonthOfQuarter = "";
        if (month > 0 && month < NUMBER_FOUR) {
            firstMonthOfQuarter = "03";
        } else if (month > NUMBER_TREE && month < NUMBER_SEVEN) {
            firstMonthOfQuarter = "06";
        } else if (month > NUMBER_SIX && month < NUMBER_TEN) {
            firstMonthOfQuarter = "09";
        } else {
            firstMonthOfQuarter = "12";
        }
        return getCurrentYear() + "-" + firstMonthOfQuarter + "-" + getLastDay4Month(Integer.parseInt(firstMonthOfQuarter));
    }

    /**
     * 得到当月的第一天
     *
     * @return
     */
    public static int getFirstDay4Month(int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MONTH, month - 1);
        return calendar.getActualMinimum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 得到指定月的最后一天
     *
     * @return
     */
    public static int getLastDay4Month(int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MONTH, month - 1);
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 得到指定日所在周的第一天
     *
     * @return
     */
    public static String getMonday4Week(Date date) {
        GregorianCalendar cal = new GregorianCalendar();
        cal.setTime(date);
        cal.set(GregorianCalendar.DAY_OF_WEEK, GregorianCalendar.MONDAY);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        return formatter.format(cal.getTime());
    }

    /**
     * 得到指定日所在周的最后一天
     *
     * @return
     */
    public static String getSunday4Week(Date date) {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.set(GregorianCalendar.DAY_OF_WEEK, GregorianCalendar.SUNDAY);
        calendar.add(GregorianCalendar.DAY_OF_MONTH, 7);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        return formatter.format(calendar.getTime());
    }

    /**
     * 得到指定月的第一天
     *
     * @return
     */
    public static String getFirstDay4Month(Date date) {
        GregorianCalendar cal = new GregorianCalendar();
        cal.setTime(date);
        int day = getFirstDay4Month(cal.get(GregorianCalendar.MONTH) + 1);
        cal.set(GregorianCalendar.DAY_OF_MONTH, day);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        return formatter.format(cal.getTime());
    }

    /**
     * 得到指定月的最后一天
     *
     * @return
     */
    public static String getLastDay4Month(Date date) {
        GregorianCalendar cal = new GregorianCalendar();
        cal.setTime(date);
        int day = getLastDay4Month(cal.get(GregorianCalendar.MONTH) + 1);
        cal.set(GregorianCalendar.DAY_OF_MONTH, day);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        return formatter.format(cal.getTime());
    }

    /**
     * 得到指定年的第一天
     *
     * @return
     */
    public static String getFirstDay4Year(Date date) {
        GregorianCalendar cal = new GregorianCalendar();
        cal.setTime(date);
        int day = cal.getMinimum(GregorianCalendar.DAY_OF_YEAR);
        cal.set(GregorianCalendar.MONTH, 0);
        cal.set(GregorianCalendar.DAY_OF_MONTH, day);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        return formatter.format(cal.getTime());
    }

    /**
     * 得到指定年的最后一天
     *
     * @return
     */
    public static String getLastDay4Year(Date date) {
        GregorianCalendar cal = new GregorianCalendar();
        cal.setTime(date);
        cal.set(GregorianCalendar.MONTH, 11);
        int day = cal.getMaximum(GregorianCalendar.DAY_OF_MONTH);
        cal.set(GregorianCalendar.DAY_OF_MONTH, day);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        return formatter.format(cal.getTime());
    }

    public static int getLastYear() {
        GregorianCalendar calendar = new GregorianCalendar();
        return calendar.get(Calendar.YEAR) - 1;
    }

    public static int getLastLastYear() {
        GregorianCalendar calendar = new GregorianCalendar();
        return calendar.get(Calendar.YEAR) - 2;
    }

    /**
     * 提前给定日期的号
     *
     * @param date
     * @return
     */
    public static int getDayOfDate(Date date) {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 得到n年后的今天
     *
     * @param n
     * @return
     */
    public static Date getAfterYearOfDate(int n) {
        Calendar calendar = Calendar.getInstance();
        Date date = new Date(System.currentTimeMillis());
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, +n);
        date = calendar.getTime();
        return date;
    }

    /**
     * 根据用户生日计算年龄
     */
    public static int getAgeByBirthday(Date birthday) {
        if (birthday == null) {
            return 0;
        }
        Calendar cal = Calendar.getInstance();
        if (cal.before(birthday)) {
            throw new IllegalArgumentException("The birthDay is before Now.");
        }
        int yearNow = cal.get(Calendar.YEAR);
        int monthNow = cal.get(Calendar.MONTH) + 1;
        int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);
        cal.setTime(birthday);
        int yearBirth = cal.get(Calendar.YEAR);
        int monthBirth = cal.get(Calendar.MONTH) + 1;
        int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);
        int age = yearNow - yearBirth;
        if (monthNow <= monthBirth) {
            if (monthNow == monthBirth) {
                if (dayOfMonthNow < dayOfMonthBirth) {
                    age--;
                }
            } else {
                age--;
            }
        }
        return age;
    }

    /**
     * 验证字符串是否是日期类型
     *
     * @param str
     * @param formatStr
     * @return boolean    返回类型
     */
    public static boolean isValidDate(String str, String formatStr) {
        boolean convertSuccess = true;
        SimpleDateFormat format = new SimpleDateFormat(formatStr);
        try {
            format.setLenient(false);
            format.parse(str);
        } catch (ParseException e) {
            // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
            convertSuccess = false;
        }
        return convertSuccess;
    }

    /**
     * @param @return 参数说明
     * @return List<Integer>    返回类型
     * @throws
     * @Title: getYear
     * @Description: 获取年份
     */
    public static List<Integer> getYear() {
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
        String sdate = sdf.format(date);
        int today = Integer.parseInt(sdate);
        int before = today - 10;
        int after = today + 10;
        List<Integer> dateList = new ArrayList<Integer>();
        for (int i = before; i <= after; i++) {
            dateList.add(i);
        }
        return dateList;
    }

    public static List<Integer> getYear(int years) {
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
        String sdate = sdf.format(date);
        int today = Integer.parseInt(sdate);
        int before = today - years;
        int after = today + 1;
        List<Integer> dateList = new ArrayList<Integer>();
        for (int i = before; i <= after; i++) {
            dateList.add(i);
        }
        return dateList;
    }

    /**
     * 根据指定的日期字符串获取对应的星期几
     */
    public static String getWeekDay(String date) {
        Date sd = getAutoParseDate(date, "yyyy-MM-dd");
        String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(sd);

        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0) {
            w = 0;
        }

        return weekDays[w];
    }

    /**
     * 判断两个时间点间隔多少
     *
     * @param dayform
     * @param dayto
     * @return
     */
    public static Integer diffDays(Date dayform, Date dayto) {
        Calendar aCalendar = Calendar.getInstance();
        Calendar bCalendar = Calendar.getInstance();
        aCalendar.setTime(dayform);
        bCalendar.setTime(dayto);
        int days = 0;
        while (aCalendar.before(bCalendar)) {
            days++;
            aCalendar.add(Calendar.DAY_OF_YEAR, 1);
        }
        return days;
    }

    /**
     * @param beginDate
     * @param endDate
     * @return 参数说明
     * @Description: 两个时间的相差天数
     */
    public static int getIntervalDays(Date beginDate, Date endDate) {
        if (null == beginDate || null == endDate) {
            return -1;
        }
        long intervalMilli = endDate.getTime() - beginDate.getTime();
        return (int) (intervalMilli / (24 * 60 * 60 * 1000));
    }


    /***
     * 得到指定日期的前一天
     * @param date
     * @return
     */
    public static Date getBeforeDate(Date date) {
        Calendar cl = Calendar.getInstance();
        cl.setTime(date);
        int day = cl.get(Calendar.DATE);
        cl.set(Calendar.DATE, day - 1);
        return cl.getTime();
    }

    /**
     * 得到几天前的时间
     *
     * @param d
     * @param day
     * @return
     */
    public static Date getDateBefore(Date d, int day) {
        Calendar now = Calendar.getInstance();
        now.setTime(d);
        now.set(Calendar.DATE, now.get(Calendar.DATE) - day);
        return now.getTime();
    }

    /**
     * 得到几天后的时间
     *
     * @param d
     * @param day
     * @return
     */
    public static Date getDateAfter(Date d, int day) {
        Calendar now = Calendar.getInstance();
        now.setTime(d);
        now.set(Calendar.DATE, now.get(Calendar.DATE) + day);
        return now.getTime();
    }

    /**
     * 判断时间是否在时间段内
     *
     * @param nowTime
     * @param beginTime
     * @param endTime
     * @return
     */
    public static boolean belongCalendar(Date nowTime, Date beginTime, Date endTime) {
        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(beginTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }

    public static boolean compareDate(String date1, String date2) {
        DateFormat df = new SimpleDateFormat(SECONDFORMAT);
        try {
            Date dt1 = df.parse(date1);
            Date dt2 = df.parse(date2);
            if (dt1.getTime() > dt2.getTime()) {
                return true;
            } else if (dt1.getTime() < dt2.getTime()) {
                return false;
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return false;
    }

    public static boolean compareDateTime(Date date1, Date date2) {
        if (date1.getTime() > date2.getTime()) {
            return true;
        } else if (date1.getTime() < date2.getTime()) {
            return false;
        }
        return false;
    }

    /**
     * 获取距离当前时间几个月之前的时间
     *
     * @param monthNums
     * @return
     */
    public static String getBeforeDateByMonthNumbers(Integer monthNums) {
        Date today = new Date();
        Date dBefore = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(today);
        calendar.add(Calendar.MONTH, -monthNums);
        dBefore = calendar.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String defaultStartDate = sdf.format(dBefore);
        return defaultStartDate;
    }

    public static long hhmmssToSec(String time) {
        if (StringUtils.isBlank(time)) {
            return 0;
        }

        try {
            time = time.replace("\r", "").replace("\n", "").replace(" ", "");
            String[] my = time.split(":");
            int hour = Integer.parseInt(my[0]);
            int min = Integer.parseInt(my[1]);
            int sec = Integer.parseInt(my[2]);
            long totalSec = hour * 3600 + min * 60 + sec;
            return totalSec;
        } catch (Exception e) {
            return 0;
        }
    }

    public static String secTohhmmss(String sec) {
        int s60 = 60;
        int s99 = 99;
        sec = sec.split("\\.")[0];
        Long time = Math.round(Double.parseDouble(sec));
        String timeStr = null;
        long hour = 0;
        long minute = 0;
        long second = 0;
        if (time <= 0) {
            return "00:00:00";
        } else {
            minute = time / s60;
            if (minute < s60) {
                second = time % s60;
                timeStr = "00:" + unitFormat(minute) + ":" + unitFormat(second);
            } else {
                hour = minute / s60;
                if (hour > s99) {
                    return "99:59:59";
                }
                minute = minute % s60;
                second = time - hour * 3600 - minute * s60;
                timeStr = unitFormat(hour) + ":" + unitFormat(minute) + ":" + unitFormat(second);
            }
        }
        return timeStr;
    }

    public static String secTohhmmss(long time) {
        int s60 = 60;
        int s99 = 99;
        String timeStr = null;
        long hour = 0;
        long minute = 0;
        long second = 0;
        if (time <= 0) {
            return "00:00:00";
        } else {
            minute = time / s60;
            if (minute < s60) {
                second = time % s60;
                timeStr = "00:" + unitFormat(minute) + ":" + unitFormat(second);
            } else {
                hour = minute / s60;
                if (hour > s99) {
                    return "99:59:59";
                }
                minute = minute % 60;
                second = time - hour * 3600 - minute * s60;
                timeStr = unitFormat(hour) + ":" + unitFormat(minute) + ":" + unitFormat(second);
            }
        }
        return timeStr;
    }

    private static String unitFormat(long i) {
        String retStr = null;
        int s10 = 10;
        if (i >= 0 && i < s10) {
            retStr = "0" + Long.toString(i);
        } else {
            retStr = "" + i;
        }
        return retStr;
    }

    public static Date getCustomDateValue(int seconds) {
        Calendar c = new GregorianCalendar();
        Date date = new Date();
        c.setTime(date);//设置参数时间
        c.add(Calendar.SECOND, seconds);//把日期往前SECOND 秒.整数往后推,负数往前移动
        date = c.getTime(); //这个时间就是日期往后推一天的结果
        return date;
    }

    /**
     * 取得当月天数
     */
    public static int getCurrentMonthLastDay() {
        Calendar a = Calendar.getInstance();
        a.set(Calendar.DATE, 1);//把日期设置为当月第一天
        a.roll(Calendar.DATE, -1);//日期回滚一天，也就是最后一天
        int maxDate = a.get(Calendar.DATE);
        return maxDate;
    }

    /**
     * 得到指定月的天数
     */
    public static int getMonthLastDay(int year, int month) {
        Calendar a = Calendar.getInstance();
        a.set(Calendar.YEAR, year);
        a.set(Calendar.MONTH, month - 1);
        a.set(Calendar.DATE, 1);//把日期设置为当月第一天
        a.roll(Calendar.DATE, -1);//日期回滚一天，也就是最后一天
        int maxDate = a.get(Calendar.DATE);
        return maxDate;
    }

    public static void main(String[] args) throws ParseException {
        int currentMonthLastDay = getCurrentMonthLastDay();
        System.out.println(currentMonthLastDay);


        /*Date followUpRealTime = DateUtil.getDate("2023-02-04", DateUtil.DEFAULTFORMAT);
        Date nextFollowDateTime = DateUtil.getDateAfter(followUpRealTime, 28);
        System.out.println(nextFollowDateTime);*/

        Date followUpRealTime = DateUtil.getAutoParseDate("2023-02-07", DateUtil.DEFAULTFORMAT);
        Date nextFollowDateTime = DateUtil.getDateAfter(followUpRealTime, 5);
        Date preFollowDateTime = DateUtil.getDateAfter(followUpRealTime, -1);

        System.out.println(DateUtil.formatDate2String(preFollowDateTime));
        System.out.println(DateUtil.formatDate2String(nextFollowDateTime));
        List<String> totalCount = new ArrayList<>();
        totalCount.add("2023-02-01");
        totalCount.add("2023-02-02");
        totalCount.add("2023-02-03");
        //totalCount.add("2023-02-04");
        totalCount.add("2023-02-05");
        int persistentDay = PersistentDayUtil.getMaxUninterruptedDaysByOneZero(totalCount);
        System.out.println(persistentDay);

        Date data = DateUtil.getAutoParseDate("2023-02-07", DateUtil.SECONDFORMAT);

        //

        System.out.println(DateUtil.formatDate2String(data));




    }


}
