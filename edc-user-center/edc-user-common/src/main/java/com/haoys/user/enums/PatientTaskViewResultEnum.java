package com.haoys.user.enums;

public enum PatientTaskViewResultEnum {

    /**患者端任务匹配结果code */
    PATIENT_TASK_VIEW_CODE_01("600001","未找到符合规则的方案"),
    PATIENT_TASK_VIEW_CODE_02("600002","参与者还未录入方案规则中的表单信息"),
    PATIENT_TASK_VIEW_CODE_03("600003","与方案前置条件不匹配"),
    PATIENT_TASK_VIEW_CODE_04("600004","参与者未完成绑定"),
    PATIENT_TASK_VIEW_CODE_05("600005","参与者绑定未审核"),
    PATIENT_TASK_VIEW_CODE_06("600006","参与者绑定不通过");

    private String code;
    private String value;

    PatientTaskViewResultEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }




}
