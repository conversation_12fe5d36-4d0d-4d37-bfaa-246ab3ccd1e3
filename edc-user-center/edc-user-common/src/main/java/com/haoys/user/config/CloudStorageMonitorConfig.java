package com.haoys.user.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 云存储性能监控配置
 * 
 * <p>用于配置云存储服务的性能监控参数</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "cloud.storage.monitor")
public class CloudStorageMonitorConfig {

    /**
     * 是否启用性能监控
     */
    private boolean enabled = true;

    /**
     * 是否启用详细日志
     */
    private boolean detailLogEnabled = true;

    /**
     * 慢上传阈值（毫秒）
     */
    private long slowUploadThreshold = 5000;

    /**
     * 大文件阈值（字节）
     */
    private long largeFileThreshold = 10 * 1024 * 1024; // 10MB

    /**
     * 是否启用上传重试
     */
    private boolean retryEnabled = true;

    /**
     * 最大重试次数
     */
    private int maxRetryCount = 3;

    /**
     * 重试间隔基数（毫秒）
     */
    private long retryIntervalBase = 1000;

    /**
     * 连接超时时间（秒）
     */
    private int connectTimeout = 30;

    /**
     * 读取超时时间（秒）
     */
    private int readTimeout = 60;

    /**
     * 写入超时时间（秒）
     */
    private int writeTimeout = 60;

    /**
     * 是否启用性能统计
     */
    private boolean statsEnabled = true;

    /**
     * 统计窗口大小（最近N次上传）
     */
    private int statsWindowSize = 100;

    /**
     * 是否启用告警
     */
    private boolean alertEnabled = true;

    /**
     * 错误率告警阈值（百分比）
     */
    private double errorRateThreshold = 10.0;

    /**
     * 平均响应时间告警阈值（毫秒）
     */
    private long avgResponseTimeThreshold = 10000;
}
