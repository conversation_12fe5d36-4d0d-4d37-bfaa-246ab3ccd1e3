package com.haoys.user.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 安全应用配置实体类
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
@Data
@Accessors(chain = true)
public class SecureAppConfig implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 应用密钥
     */
    private String appSecret;
    
    /**
     * 应用名称
     */
    private String appName;
    
    /**
     * 应用描述
     */
    private String appDescription;
    
    /**
     * 环境标识(dev,test,feature,prod)
     */
    private String environment;
    
    /**
     * 状态(0:禁用,1:启用)
     */
    private Integer status;
    
    /**
     * Code有效期(秒)
     */
    private Integer codeExpiration;
    
    /**
     * AccessToken有效期(秒)
     */
    private Integer accessTokenExpiration;
    
    /**
     * 每日最大请求次数
     */
    private Integer maxDailyRequests;
    
    /**
     * 允许的IP地址列表(JSON格式)
     */
    private String allowedIps;
    
    /**
     * 回调地址
     */
    private String webhookUrl;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建用户ID
     */
    private Long createUserId;
    
    /**
     * 更新用户ID
     */
    private Long updateUserId;
    
    /**
     * 租户ID
     */
    private Long tenantId;
    
    /**
     * 平台ID
     */
    private Long platformId;
}
