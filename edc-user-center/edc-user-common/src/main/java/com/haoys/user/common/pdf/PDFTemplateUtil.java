package com.haoys.user.common.pdf;

import com.lowagie.text.pdf.BaseFont;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateExceptionHandler;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Entities;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.io.StringWriter;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;


@Slf4j
public class PDFTemplateUtil {

    /**
     * 通过模板导出pdf文件
     * @param header
     * @param bodyHtmlContent  数据
     * @param templateFileName 模板文件名
     * @throws Exception
     */
    public static ByteArrayOutputStream createPDF(String header, Map<String,Object> bodyHtmlContent, String templateFileName) throws Exception {
        // 创建一个FreeMarker实例, 负责管理FreeMarker模板的Configuration实例
        Configuration cfg = new Configuration(Configuration.VERSION_2_3_32);
        // 指定FreeMarker模板文件的位置
        cfg.setClassForTemplateLoading(PDFTemplateUtil.class,"/templates");
        ITextRendererO renderer = new ITextRendererO();
        OutputStream out = new ByteArrayOutputStream();
        try {
            String fontFilePath ="/static/fonts/simsun.ttc";
            // 设置 css中 的字体样式（暂时仅支持宋体和黑体） 必须，不然中文不显示
            renderer.getFontResolver().addFont(fontFilePath, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            // 设置模板的编码格式
            cfg.setEncoding(Locale.CHINA, "UTF-8");
            cfg.setOutputEncoding("UTF-8");
            // 获取模板文件
            Template template = cfg.getTemplate(templateFileName, "UTF-8");
            cfg.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
            StringWriter writer = new StringWriter();
            // 将数据输出到html中
            template.process(bodyHtmlContent, writer);
            writer.flush();
            
            String html = writer.toString();

            // 清理和优化HTML内容
            Document doc = Jsoup.parse(html);
            doc.outputSettings().syntax(Document.OutputSettings.Syntax.xml);
            doc.outputSettings().escapeMode(Entities.EscapeMode.xhtml);

            // 移除末尾的强制分页标签，防止产生空白页
            String htmlContent = doc.html();

            // 移除HTML末尾的page-break-after标签
            htmlContent = htmlContent.replaceAll("<div\\s+style\\s*=\\s*['\"]page-break-after:\\s*always;['\"]\\s*>\\s*</div>\\s*(?=</body>|</html>|$)", "");

            // 移除可能导致空白页的多余空白和换行
            String cleanHtml = htmlContent
                .replaceAll("\\s+", " ")  // 将多个空白字符替换为单个空格
                .replaceAll(">\\s+<", "><")  // 移除标签间的空白
                .trim();

            // 验证HTML内容
            if (cleanHtml.length() < 100) {
                log.warn("HTML内容可能过短，可能导致PDF生成问题。内容长度: {}", cleanHtml.length());
            }

            // 检查是否包含可能导致分页问题的元素
            int pageBreakCount = (cleanHtml.split("page-break-after:\\s*always").length - 1);
            if (pageBreakCount > 0) {
                //log.info("检测到 {} 个强制分页标签", pageBreakCount);
            }

            // 检查是否移除了末尾的分页标签
            if (!htmlContent.equals(doc.html())) {
                log.info("已移除HTML末尾的强制分页标签，防止产生空白页");
            }

            //log.info("处理后的HTML内容长度: {}", cleanHtml.length());
            //log.debug("html内容:\r\n{}", cleanHtml);

            renderer.setDocumentFromString(cleanHtml);
            // 设置模板中的图片路径 （这里的images在resources目录下） 模板中img标签src路径需要相对路径加图片名 如<img src="images/xh.jpg"/>
            String url = Objects.requireNonNull(PDFTemplateUtil.class.getClassLoader().getResource("static")).toURI().toString();
            renderer.getSharedContext().setBaseURL(url);
            renderer.layout();
//            renderer.createPDF(out, fontFilePath, "/images/MyQRCode.png", "SRC科研协作平台");
            renderer.createPDF(out, fontFilePath, null, "", header);
            renderer.finishPDF();
            out.flush();
            return (ByteArrayOutputStream)out;
        }catch (Exception e){
            log.error("pdf生成失败", e.getStackTrace());
            throw e;
        }
        finally {
            if(out != null){
                out.close();
            }
        }
    }
    
    
}
