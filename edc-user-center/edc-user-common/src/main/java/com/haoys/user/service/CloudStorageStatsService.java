package com.haoys.user.service;

import com.haoys.user.config.CloudStorageMonitorConfig;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 云存储性能统计服务
 * 
 * <p>收集和分析云存储操作的性能数据</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CloudStorageStatsService {

    private final CloudStorageMonitorConfig config;
    
    // 统计数据
    private final AtomicLong totalUploads = new AtomicLong(0);
    private final AtomicLong successfulUploads = new AtomicLong(0);
    private final AtomicLong failedUploads = new AtomicLong(0);
    private final AtomicLong totalUploadTime = new AtomicLong(0);
    private final AtomicLong totalUploadSize = new AtomicLong(0);
    
    // 最近的上传记录（用于滑动窗口统计）
    private final ConcurrentLinkedQueue<UploadRecord> recentUploads = new ConcurrentLinkedQueue<>();

    /**
     * 记录上传操作
     */
    public void recordUpload(String storageType, String path, long fileSize, long duration, boolean success, String error) {
        if (!config.isStatsEnabled()) {
            return;
        }

        totalUploads.incrementAndGet();
        
        if (success) {
            successfulUploads.incrementAndGet();
            totalUploadTime.addAndGet(duration);
            totalUploadSize.addAndGet(fileSize);
        } else {
            failedUploads.incrementAndGet();
        }

        // 添加到滑动窗口
        UploadRecord record = new UploadRecord(storageType, path, fileSize, duration, success, error, System.currentTimeMillis());
        recentUploads.offer(record);
        
        // 保持窗口大小
        while (recentUploads.size() > config.getStatsWindowSize()) {
            recentUploads.poll();
        }

        // 检查是否需要告警
        checkAlerts();

        // 记录慢上传
        if (duration > config.getSlowUploadThreshold()) {
            log.warn("检测到慢上传 - 存储类型: {}, 路径: {}, 大小: {}KB, 耗时: {}ms", 
                storageType, path, fileSize / 1024, duration);
        }
    }

    /**
     * 获取统计信息
     */
    public StorageStats getStats() {
        long total = totalUploads.get();
        long successful = successfulUploads.get();
        long failed = failedUploads.get();
        
        double successRate = total > 0 ? (double) successful / total * 100 : 0;
        double errorRate = total > 0 ? (double) failed / total * 100 : 0;
        long avgUploadTime = successful > 0 ? totalUploadTime.get() / successful : 0;
        long avgFileSize = successful > 0 ? totalUploadSize.get() / successful : 0;
        
        return new StorageStats(total, successful, failed, successRate, errorRate, 
            avgUploadTime, avgFileSize, recentUploads.size());
    }

    /**
     * 检查告警条件
     */
    private void checkAlerts() {
        if (!config.isAlertEnabled()) {
            return;
        }

        StorageStats stats = getStats();
        
        // 错误率告警
        if (stats.getErrorRate() > config.getErrorRateThreshold() && stats.getTotalUploads() >= 10) {
            log.warn("云存储错误率过高 - 错误率: {:.2f}%, 阈值: {:.2f}%, 总上传数: {}", 
                stats.getErrorRate(), config.getErrorRateThreshold(), stats.getTotalUploads());
        }
        
        // 平均响应时间告警
        if (stats.getAvgUploadTime() > config.getAvgResponseTimeThreshold() && stats.getSuccessfulUploads() >= 10) {
            log.warn("云存储平均响应时间过长 - 平均时间: {}ms, 阈值: {}ms, 成功上传数: {}", 
                stats.getAvgUploadTime(), config.getAvgResponseTimeThreshold(), stats.getSuccessfulUploads());
        }
    }

    /**
     * 重置统计数据
     */
    public void resetStats() {
        totalUploads.set(0);
        successfulUploads.set(0);
        failedUploads.set(0);
        totalUploadTime.set(0);
        totalUploadSize.set(0);
        recentUploads.clear();
        
        log.info("云存储统计数据已重置");
    }

    /**
     * 上传记录
     */
    @Data
    public static class UploadRecord {
        private final String storageType;
        private final String path;
        private final long fileSize;
        private final long duration;
        private final boolean success;
        private final String error;
        private final long timestamp;
    }

    /**
     * 存储统计信息
     */
    @Data
    public static class StorageStats {
        private final long totalUploads;
        private final long successfulUploads;
        private final long failedUploads;
        private final double successRate;
        private final double errorRate;
        private final long avgUploadTime;
        private final long avgFileSize;
        private final int recentRecordsCount;
    }
}
