package com.haoys.user.storge.cloud;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.haoys.user.exception.StorageException;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 阿里云OSS存储服务
 *
 * <p>优化版本，支持详细的性能监控日志</p>
 *
 * <AUTHOR>
 * @version 2.0.0
 */
@Slf4j
public class AliyunOssStorageService extends OssStorageService {

    private OSS client;

    public AliyunOssStorageService(OssStorageConfig config) {
        this.config = config;
        //初始化
        init();
        log.info("阿里云OSS存储服务初始化完成 - Endpoint: {}, Bucket: {}",
            config.getEndpoint(), config.getBucketName());
    }

    private void init() {
        try {
            client = new OSSClientBuilder()
                    .build(config.getEndpoint(), config.getAccessKeyId(), config.getAccessKeySecret());
            log.debug("阿里云OSS客户端初始化成功");
        } catch (Exception e) {
            log.error("阿里云OSS客户端初始化失败", e);
            throw new StorageException("阿里云OSS客户端初始化失败", e);
        }
    }

    @Override
    public String upload(InputStream inputStream, String path) {
        long startTime = System.currentTimeMillis();
        long fileSize = 0;

        try {
            // 获取文件大小（如果可能）
            try {
                fileSize = inputStream.available();
            } catch (IOException e) {
                log.debug("无法获取输入流大小: {}", e.getMessage());
            }

            log.info("开始上传文件到阿里云OSS - 路径: {}, 预估大小: {}KB", path, fileSize / 1024);

            client.putObject(config.getBucketName(), path, inputStream);

            long duration = System.currentTimeMillis() - startTime;
            log.info("阿里云OSS文件上传成功 - 路径: {}, 大小: {}KB, 耗时: {}ms",
                path, fileSize / 1024, duration);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("阿里云OSS文件上传失败 - 路径: {}, 大小: {}KB, 耗时: {}ms, 错误: {}",
                path, fileSize / 1024, duration, e.getMessage(), e);
            throw new StorageException("上传文件到阿里云OSS失败: " + e.getMessage(), e);
        }

        return config.getDomain() + "/" + path;
    }

    @Override
    public String upload(byte[] data, String path) {
        long startTime = System.currentTimeMillis();
        long fileSize = data.length;

        try {
            log.info("开始上传字节数组到阿里云OSS - 路径: {}, 大小: {}KB", path, fileSize / 1024);

            client.putObject(config.getBucketName(), path, new ByteArrayInputStream(data));

            long duration = System.currentTimeMillis() - startTime;
            log.info("阿里云OSS字节数组上传成功 - 路径: {}, 大小: {}KB, 耗时: {}ms",
                path, fileSize / 1024, duration);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("阿里云OSS字节数组上传失败 - 路径: {}, 大小: {}KB, 耗时: {}ms, 错误: {}",
                path, fileSize / 1024, duration, e.getMessage(), e);
            throw new StorageException("上传字节数组到阿里云OSS失败: " + e.getMessage(), e);
        }

        return config.getDomain() + "/" + path;
    }


    @Override
    public InputStream download(String path) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始从阿里云OSS下载文件 - 路径: {}", path);

            // ossObject包含文件所在的存储空间名称、文件名称、文件元信息以及一个输入流。
            OSSObject ossObject = client.getObject(config.getBucketName(), path);

            long duration = System.currentTimeMillis() - startTime;
            long fileSize = ossObject.getObjectMetadata().getContentLength();

            log.info("阿里云OSS文件下载成功 - 路径: {}, 大小: {}KB, 耗时: {}ms",
                path, fileSize / 1024, duration);

            return ossObject.getObjectContent();

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("阿里云OSS文件下载失败 - 路径: {}, 耗时: {}ms, 错误: {}",
                path, duration, e.getMessage(), e);
            throw new StorageException("从阿里云OSS下载文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void delete(String path) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始删除阿里云OSS文件 - 路径: {}", path);

            client.deleteObject(config.getBucketName(), path);

            long duration = System.currentTimeMillis() - startTime;
            log.info("阿里云OSS文件删除成功 - 路径: {}, 耗时: {}ms", path, duration);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("阿里云OSS文件删除失败 - 路径: {}, 耗时: {}ms, 错误: {}",
                path, duration, e.getMessage(), e);
            throw new StorageException("删除阿里云OSS文件失败: " + e.getMessage(), e);
        }
    }
}
