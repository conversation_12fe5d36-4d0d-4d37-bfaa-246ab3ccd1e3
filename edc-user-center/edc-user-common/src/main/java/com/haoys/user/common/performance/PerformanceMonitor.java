package com.haoys.user.common.performance;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 性能监控工具类
 * 用于监控和分析方法执行性能
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class PerformanceMonitor {

    private final Map<String, PerformanceMetrics> metricsMap = new ConcurrentHashMap<>();

    /**
     * 开始监控
     * 
     * @param operationName 操作名称
     * @return 监控上下文
     */
    public PerformanceContext startMonitoring(String operationName) {
        return new PerformanceContext(operationName, System.currentTimeMillis());
    }

    /**
     * 结束监控并记录性能指标
     * 
     * @param context 监控上下文
     */
    public void endMonitoring(PerformanceContext context) {
        long endTime = System.currentTimeMillis();
        long duration = endTime - context.getStartTime();
        
        recordMetrics(context.getOperationName(), duration);
        
        if (duration > 1000) { // 超过1秒的操作记录警告
            log.warn("慢操作检测: {} 耗时 {}ms", context.getOperationName(), duration);
        } else {
            log.debug("操作完成: {} 耗时 {}ms", context.getOperationName(), duration);
        }
    }

    /**
     * 记录性能指标
     * 
     * @param operationName 操作名称
     * @param duration 执行时间
     */
    private void recordMetrics(String operationName, long duration) {
        PerformanceMetrics metrics = metricsMap.computeIfAbsent(operationName, 
            k -> new PerformanceMetrics());
        
        metrics.addExecution(duration);
    }

    /**
     * 获取性能统计信息
     * 
     * @param operationName 操作名称
     * @return 性能指标
     */
    public PerformanceMetrics getMetrics(String operationName) {
        return metricsMap.get(operationName);
    }

    /**
     * 获取所有性能统计信息
     * 
     * @return 所有性能指标
     */
    public Map<String, PerformanceMetrics> getAllMetrics() {
        return new ConcurrentHashMap<>(metricsMap);
    }

    /**
     * 清除性能统计信息
     */
    public void clearMetrics() {
        metricsMap.clear();
    }

    /**
     * 打印性能报告
     */
    public void printPerformanceReport() {
        log.info("=== 性能监控报告 ===");
        metricsMap.forEach((operationName, metrics) -> {
            log.info("操作: {} | 执行次数: {} | 平均耗时: {}ms | 最大耗时: {}ms | 最小耗时: {}ms | 总耗时: {}ms",
                operationName,
                metrics.getExecutionCount(),
                metrics.getAverageTime(),
                metrics.getMaxTime(),
                metrics.getMinTime(),
                metrics.getTotalTime()
            );
        });
        log.info("=== 性能监控报告结束 ===");
    }

    /**
     * 监控上下文
     */
    public static class PerformanceContext {
        private final String operationName;
        private final long startTime;

        public PerformanceContext(String operationName, long startTime) {
            this.operationName = operationName;
            this.startTime = startTime;
        }

        public String getOperationName() {
            return operationName;
        }

        public long getStartTime() {
            return startTime;
        }

        public long getElapsedTime() {
            return System.currentTimeMillis() - startTime;
        }
    }

    /**
     * 性能指标
     */
    public static class PerformanceMetrics {
        private final AtomicLong executionCount = new AtomicLong(0);
        private final AtomicLong totalTime = new AtomicLong(0);
        private volatile long maxTime = 0;
        private volatile long minTime = Long.MAX_VALUE;

        public void addExecution(long duration) {
            executionCount.incrementAndGet();
            totalTime.addAndGet(duration);
            
            // 更新最大值
            if (duration > maxTime) {
                maxTime = duration;
            }
            
            // 更新最小值
            if (duration < minTime) {
                minTime = duration;
            }
        }

        public long getExecutionCount() {
            return executionCount.get();
        }

        public long getTotalTime() {
            return totalTime.get();
        }

        public long getAverageTime() {
            long count = executionCount.get();
            return count > 0 ? totalTime.get() / count : 0;
        }

        public long getMaxTime() {
            return maxTime;
        }

        public long getMinTime() {
            return minTime == Long.MAX_VALUE ? 0 : minTime;
        }
    }

    /**
     * 性能监控注解处理器（可以配合AOP使用）
     */
    public static class PerformanceMonitorAspect {
        
        private final PerformanceMonitor performanceMonitor;

        public PerformanceMonitorAspect(PerformanceMonitor performanceMonitor) {
            this.performanceMonitor = performanceMonitor;
        }

        /**
         * 监控方法执行性能
         * 
         * @param methodName 方法名
         * @param operation 要执行的操作
         * @return 操作结果
         */
        public <T> T monitor(String methodName, java.util.function.Supplier<T> operation) {
            PerformanceContext context = performanceMonitor.startMonitoring(methodName);
            try {
                return operation.get();
            } finally {
                performanceMonitor.endMonitoring(context);
            }
        }

        /**
         * 监控无返回值的方法执行性能
         * 
         * @param methodName 方法名
         * @param operation 要执行的操作
         */
        public void monitor(String methodName, Runnable operation) {
            PerformanceContext context = performanceMonitor.startMonitoring(methodName);
            try {
                operation.run();
            } finally {
                performanceMonitor.endMonitoring(context);
            }
        }
    }

    /**
     * 获取性能监控切面
     * 
     * @return 性能监控切面
     */
    public PerformanceMonitorAspect getAspect() {
        return new PerformanceMonitorAspect(this);
    }

    /**
     * 数据库查询性能监控
     */
    public static class DatabaseQueryMonitor {
        
        private final PerformanceMonitor performanceMonitor;

        public DatabaseQueryMonitor(PerformanceMonitor performanceMonitor) {
            this.performanceMonitor = performanceMonitor;
        }

        /**
         * 监控数据库查询性能
         * 
         * @param queryName 查询名称
         * @param query 查询操作
         * @return 查询结果
         */
        public <T> T monitorQuery(String queryName, java.util.function.Supplier<T> query) {
            String operationName = "DB_QUERY_" + queryName;
            PerformanceContext context = performanceMonitor.startMonitoring(operationName);
            try {
                T result = query.get();
                
                // 记录查询结果大小（如果是集合）
                if (result instanceof java.util.Collection) {
                    int size = ((java.util.Collection<?>) result).size();
                    log.debug("数据库查询 {} 返回 {} 条记录", queryName, size);
                }
                
                return result;
            } finally {
                performanceMonitor.endMonitoring(context);
            }
        }
    }

    /**
     * 获取数据库查询监控器
     * 
     * @return 数据库查询监控器
     */
    public DatabaseQueryMonitor getDatabaseMonitor() {
        return new DatabaseQueryMonitor(this);
    }

    /**
     * PDF生成性能监控
     */
    public static class PdfGenerationMonitor {
        
        private final PerformanceMonitor performanceMonitor;

        public PdfGenerationMonitor(PerformanceMonitor performanceMonitor) {
            this.performanceMonitor = performanceMonitor;
        }

        /**
         * 监控PDF生成性能
         * 
         * @param testeeCode 参与者编号
         * @param pdfGenerator PDF生成操作
         * @return PDF生成结果
         */
        public <T> T monitorPdfGeneration(String testeeCode, java.util.function.Supplier<T> pdfGenerator) {
            String operationName = "PDF_GENERATION_" + testeeCode;
            PerformanceContext context = performanceMonitor.startMonitoring(operationName);
            try {
                return pdfGenerator.get();
            } finally {
                performanceMonitor.endMonitoring(context);
            }
        }
    }

    /**
     * 获取PDF生成监控器
     * 
     * @return PDF生成监控器
     */
    public PdfGenerationMonitor getPdfMonitor() {
        return new PdfGenerationMonitor(this);
    }
}
