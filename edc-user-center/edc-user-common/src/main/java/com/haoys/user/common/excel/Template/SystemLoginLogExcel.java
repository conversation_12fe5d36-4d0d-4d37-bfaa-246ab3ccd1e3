package com.haoys.user.common.excel.Template;

import com.haoys.user.common.excel.ExcelExport;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class SystemLoginLogExcel implements Serializable {
    @ExcelExport(value = "操作时间", sort = 1)
    private String loginTime;
    @ExcelExport(value = "操作类型", sort = 2)
    private String operateType;
    @ExcelExport(value = "用户名", sort = 3)
    private String userName;
    @ExcelExport(value = "操作用户", sort = 4)
    private String realName;
    @ExcelExport(value = "单位", sort = 5)
    private String orgName;
    @ExcelExport(value = "登录IP", sort = 6)
    private String requestIp;
    @ExcelExport(value = "登录地点", sort = 7)
    private String location;
    @ExcelExport(value = "是否成功", sort = 8)
    private String status;
    @ExcelExport(value = "代码", sort = 9)
    private String code;



}