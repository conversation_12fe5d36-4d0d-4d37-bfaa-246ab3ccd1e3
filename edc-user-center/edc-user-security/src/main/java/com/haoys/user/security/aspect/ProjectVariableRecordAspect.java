package com.haoys.user.security.aspect;


import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.haoys.user.common.annotation.ProjectVariableRecordFlag;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.context.RequestProjectTesteeContext;
import com.haoys.user.common.ip.RequestIpUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.participant.ProjectParticipantViewConfigParam;
import com.haoys.user.domain.param.project.ProjectCheckDataParam;
import com.haoys.user.domain.param.project.ProjectTesteeParam;
import com.haoys.user.domain.param.project.ProjectTesteeResultParam;
import com.haoys.user.domain.param.project.ProjectTesteeTableParam;
import com.haoys.user.domain.param.testee.ProjectTesteeDeleteRowRecord;
import com.haoys.user.domain.vo.auth.ProjectRoleVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeTableVo;
import com.haoys.user.domain.vo.project.ProjectVisitVo;
import com.haoys.user.enums.ProjectCheckEnum;
import com.haoys.user.model.FlowPlan;
import com.haoys.user.model.ProjectTesteeResult;
import com.haoys.user.model.ProjectTesteeTable;
import com.haoys.user.model.ProjectVisitUser;
import com.haoys.user.model.TemplateFormConfig;
import com.haoys.user.service.FlowPlanService;
import com.haoys.user.service.ProjectRoleService;
import com.haoys.user.service.ProjectTesteeCheckService;
import com.haoys.user.service.ProjectTesteeInfoService;
import com.haoys.user.service.ProjectTesteeResultService;
import com.haoys.user.service.ProjectTesteeTableService;
import com.haoys.user.service.ProjectVisitConfigService;
import com.haoys.user.service.TemplateConfigService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Aspect
@Component
@Order(1)
@Slf4j
public class ProjectVariableRecordAspect {

    @Autowired
    private ProjectRoleService projectRoleService;
    @Autowired
    private TemplateConfigService templateConfigService;
    @Autowired
    private ProjectTesteeResultService projectTesteeResultService;
    @Autowired
    private ProjectTesteeTableService projectTesteeTableService;
    @Autowired
    private ProjectTesteeCheckService projectTesteeCheckService;
    @Autowired
    private FlowPlanService flowPlanService;
    @Autowired
    private ProjectVisitConfigService projectVisitConfigService;
    @Autowired
    private ProjectTesteeInfoService projectTesteeInfoService;

    @Pointcut(value = "@annotation(com.haoys.user.common.annotation.ProjectVariableRecordFlag)")
    public void pointcut(){}

    @Before(value = "pointcut()")
    public void before(JoinPoint thisJoinPoint) throws Exception {
        MethodSignature signature = (MethodSignature) thisJoinPoint.getSignature();
        Method method = signature.getMethod();
        String[] parameterNames = signature.getParameterNames();
        Class<?>[] parameterTypes = method.getParameterTypes();
        Class targetClass = null;
        if(parameterTypes.length >0){
            targetClass = Class.forName(parameterTypes[0].getName());
        }
        String parameterName = parameterNames[0];
        Map<String, Object> fieldsName = getFieldsName(thisJoinPoint);
        Object object = fieldsName.get(parameterName);

        if(BusinessConfig.PARAMTER_OBJECT_TYPE_01.equals(targetClass.getTypeName())) {
            ProjectTesteeTableParam projectTesteeTableParam = JSON.parseObject(JSON.toJSONString(object), ProjectTesteeTableParam.class);
            List<ProjectTesteeTableParam.TableRowData> tableRowDataList = projectTesteeTableParam.getTableRowDataList();
            for (ProjectTesteeTableParam.TableRowData tableRowData : tableRowDataList) {
                if(tableRowData.getTesteeResultId() != null){
                    ProjectTesteeTable tableRowFieldRecord = projectTesteeTableService.getProjectTesteeTableRowRecordByTableId(tableRowData.getTesteeResultId());
                    if(tableRowFieldRecord != null){
                        tableRowData.setFieldValue(tableRowFieldRecord.getFieldValue());
                    }
                }
            }
            RequestProjectTesteeContext.set(JSON.toJSONString(projectTesteeTableParam));
        }

        if(BusinessConfig.PARAMTER_OBJECT_TYPE_02.equals(targetClass.getTypeName())){
            ProjectTesteeResultParam projectTesteeResultParam = JSON.parseObject(JSON.toJSONString(object), ProjectTesteeResultParam.class);
            List<ProjectTesteeResultParam.TesteeFormResultValue> dataList = projectTesteeResultParam.getDataList();
            for (ProjectTesteeResultParam.TesteeFormResultValue testeeFormResultValue : dataList) {
                if(testeeFormResultValue.getTesteeResultId() != null){
                    ProjectTesteeResult projectTesteeResult = projectTesteeResultService.getTesteeFormDetailResult(testeeFormResultValue.getTesteeResultId());
                    testeeFormResultValue.setFieldValue(projectTesteeResult.getFieldValue());
                }
            }
            RequestProjectTesteeContext.set(JSON.toJSONString(projectTesteeResultParam));
        }
        if(BusinessConfig.PARAMTER_OBJECT_TYPE_04.equals(targetClass.getTypeName())){
            ProjectParticipantViewConfigParam projectParticipantViewConfigParam = JSON.parseObject(JSON.toJSONString(object), ProjectParticipantViewConfigParam.class);
            List<ProjectParticipantViewConfigParam.FormVariableConfigVo> dataList = projectParticipantViewConfigParam.getFormVariableConfigArrayList();
            for (ProjectParticipantViewConfigParam.FormVariableConfigVo formVariableConfigVo : dataList) {
                if(formVariableConfigVo.getTesteeResultId() != null){
                    ProjectTesteeResult projectTesteeResult = projectTesteeResultService.getTesteeFormDetailResult(formVariableConfigVo.getTesteeResultId());
                    formVariableConfigVo.setValue(projectTesteeResult.getFieldValue());
                    formVariableConfigVo.setTextValue(projectTesteeResult.getFieldText());
                }
            }
            RequestProjectTesteeContext.set(JSON.toJSONString(projectParticipantViewConfigParam));
        }
    }

    @AfterReturning(returning = "returnValue",pointcut = "pointcut()")
    public void proceed(JoinPoint thisJoinPoint, Object returnValue) throws Exception {
        MethodSignature signature = (MethodSignature) thisJoinPoint.getSignature();
        Method method = signature.getMethod();
        ProjectVariableRecordFlag projectVariableRecordFlag = method.getAnnotation(ProjectVariableRecordFlag.class);
        String[] parameterNames = signature.getParameterNames();
        String name = signature.getMethod().getName();
        String methodName = thisJoinPoint.getSignature().getName();
        //log.info("name: {}, methodName: {}", name, methodName);
        Class<?>[] parameterTypes = method.getParameterTypes();
        Class targetClass = null;
        //log.info(JSON.toJSONString(parameterTypes));
        if(parameterTypes.length >0){
            targetClass = Class.forName(parameterTypes[0].getName());
        }
        Object newInstance = targetClass.newInstance();
        //log.info("newInstance: {}", newInstance);
        String ObjectId = "";
        if(returnValue != null){
            CommonResult commonResult = JSON.parseObject(JSON.toJSONString(returnValue), CommonResult.class);
            ObjectId = commonResult.getData() == null ? "" : commonResult.getData().toString();
            log.info("returnValue: {}, ObjectId: {}", returnValue, ObjectId);
            if(ResultCode.SUCCESS.getCode()!=(commonResult.getCode())){
                return;
            }
        }
        //获取到请求参数
        Map<String, Object> fieldsName = getFieldsName(thisJoinPoint);
        ProjectCheckDataParam projectCheckDataParam = new ProjectCheckDataParam();
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        projectCheckDataParam.setVisitIp(RequestIpUtils.getIpAddress(request));
        String systemTenantId = SecurityUtils.getSystemTenantId();
        String systemPlatformId = SecurityUtils.getSystemPlatformId();
        log.info("systemTenantId: {}, systemPlatformId: {}", systemTenantId, systemPlatformId);
        projectCheckDataParam.setTenantId(systemTenantId);
        projectCheckDataParam.setPlatformId(systemPlatformId);
        // 普通表单形式
        if(BusinessConfig.PARAMTER_TYPE_01.equals(projectVariableRecordFlag.paramterType())){
            if(BusinessConfig.PROJECT_VISIT_CRF_FORM.equals(projectVariableRecordFlag.formType())){
                log.info("RequestParam testee config form init. . . ");
            }
            if(BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(projectVariableRecordFlag.formType())){
                log.info("RequestParam testee config table init. . . ");
                ProjectTesteeDeleteRowRecord projectTesteeDeleteRowRecord = JSON.parseObject(JSON.toJSONString(fieldsName), ProjectTesteeDeleteRowRecord.class);
                if(projectTesteeDeleteRowRecord == null){return;}
                log.info("RequestParam fieldsName:{}", JSON.toJSON(projectTesteeDeleteRowRecord));
                projectCheckDataParam.setProjectId(NumberUtil.parseLong(projectTesteeDeleteRowRecord.getProjectId()));
                FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectTesteeDeleteRowRecord.getProjectId().toString());
                if(flowPlanInfo != null){
                    projectCheckDataParam.setPlanId(flowPlanInfo.getId());
                    projectCheckDataParam.setPlanName(flowPlanInfo.getPlanName());
                }
                projectCheckDataParam.setVisitId(NumberUtil.parseLong(projectTesteeDeleteRowRecord.getVisitId()));
                ProjectVisitVo projectVisitBaseConfigVo = projectVisitConfigService.getProjectVisitBaseConfigByVisitId(projectTesteeDeleteRowRecord.getVisitId());
                if(projectVisitBaseConfigVo != null){
                    projectCheckDataParam.setVisitName(projectVisitBaseConfigVo.getVisitName());
                }
                projectCheckDataParam.setFormId(NumberUtil.parseLong(projectTesteeDeleteRowRecord.getFormId()));
                TemplateFormConfig templateFormConfigInfo = templateConfigService.getTemplateFormConfigById(NumberUtil.parseLong(projectTesteeDeleteRowRecord.getFormId()));
                if(templateFormConfigInfo != null){
                    projectCheckDataParam.setFormName(templateFormConfigInfo.getFormName());
                }
                projectCheckDataParam.setTesteeId(NumberUtil.parseLong(projectTesteeDeleteRowRecord.getTesteeId()));
                ProjectVisitUser projectTesteeUserInfo = projectTesteeInfoService.getProjectTesteeUserInfo(projectTesteeDeleteRowRecord.getProjectId(), projectTesteeDeleteRowRecord.getProjectOrgId(), projectTesteeDeleteRowRecord.getTesteeId());
                String testeeCode = projectTesteeUserInfo.getTesteeCode();
                projectCheckDataParam.setTesteeCode(testeeCode);
                // 查询当前表格记录
                String rowNumber = projectTesteeDeleteRowRecord.getRowNumber();
                String testeeGroupId = projectTesteeDeleteRowRecord.getTesteeGroupId();
                String projectId = projectTesteeDeleteRowRecord.getProjectId();
                String planId = projectTesteeDeleteRowRecord.getPlanId();
                String visitId = projectTesteeDeleteRowRecord.getVisitId();
                String formId = projectTesteeDeleteRowRecord.getFormId();
                String testeeId = projectTesteeDeleteRowRecord.getTesteeId();
                List<ProjectTesteeTableVo> projectTesteeFormDetailTableRow = projectTesteeTableService.getProjectTesteeTableRowRecordForRowNumber(projectId, planId, visitId, formId, testeeId, rowNumber, testeeGroupId);
                for (ProjectTesteeTableVo projectTesteeTableVo : projectTesteeFormDetailTableRow) {
                    projectCheckDataParam.setVariableId(projectTesteeTableVo.getFormDetailId());
                    projectCheckDataParam.setVariableTableId(projectTesteeTableVo.getId());
                    projectCheckDataParam.setVariableType(projectTesteeTableVo.getType());
                    projectCheckDataParam.setVariableName(projectTesteeTableVo.getLabel());
                    projectCheckDataParam.setVariableResultId(NumberUtil.parseLong(projectTesteeTableVo.getTesteeResultId()));
                    String tableFieldValue = projectTesteeTableVo.getFieldValue() == null ? "" : projectTesteeTableVo.getFieldValue();
                    if(StringUtils.isEmpty(tableFieldValue)){continue;}
                    projectCheckDataParam.setInputValue(tableFieldValue);
                    projectCheckDataParam.setOriginalValue(tableFieldValue);
                    projectCheckDataParam.setFieldText(projectTesteeTableVo.getFieldText());
                    projectCheckDataParam.setUnitValue(projectTesteeTableVo.getUnitValue());
                    projectCheckDataParam.setUnitText(projectTesteeTableVo.getUnitText());
                    projectCheckDataParam.setType(ProjectCheckEnum.PROJECT_CHECK_STATUS_02.getName());
                    saveProjectCheckData(projectCheckDataParam);
                }
            }
        }
        // JSON对象形式
        if(BusinessConfig.PARAMTER_TYPE_02.equals(projectVariableRecordFlag.paramterType())){
            if(parameterNames.length >0){
                String parameterName = parameterNames[0];
                Object object = fieldsName.get(parameterName);
                if(BusinessConfig.PARAMTER_OBJECT_TYPE_01.equals(targetClass.getTypeName())){
                    ProjectTesteeTableParam projectTesteeTableParam = JSON.parseObject(JSON.toJSONString(object), ProjectTesteeTableParam.class);
                    projectCheckDataParam.setProjectId(projectTesteeTableParam.getProjectId());
                    if(projectTesteeTableParam.getPlanId() == null){
                        FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectCheckDataParam.getProjectId().toString());
                        if(flowPlanInfo != null){
                            projectCheckDataParam.setPlanId(flowPlanInfo.getId());
                            projectCheckDataParam.setPlanName(flowPlanInfo.getPlanName());
                        }
                    }else{
                        projectCheckDataParam.setPlanId(projectTesteeTableParam.getPlanId());
                    }
                    projectCheckDataParam.setVisitId(projectTesteeTableParam.getVisitId());
                    ProjectVisitVo projectVisitBaseConfigVo = projectVisitConfigService.getProjectVisitBaseConfigByVisitId(projectTesteeTableParam.getVisitId().toString());
                    if(projectVisitBaseConfigVo != null){
                        projectCheckDataParam.setVisitName(projectVisitBaseConfigVo.getVisitName());
                    }
                    projectCheckDataParam.setFormId(projectTesteeTableParam.getFormId());
                    TemplateFormConfig templateFormConfigInfo = templateConfigService.getTemplateFormConfigById(projectTesteeTableParam.getFormId());
                    if(templateFormConfigInfo != null){
                        projectCheckDataParam.setFormName(templateFormConfigInfo.getFormName());
                    }
                    projectCheckDataParam.setTesteeId(projectTesteeTableParam.getTesteeId());

                    TemplateFormConfig templateFormConfig = templateConfigService.getTemplateFormConfigById(projectTesteeTableParam.getFormId());
                    if(templateFormConfig != null){
                        projectCheckDataParam.setFormName(templateFormConfig.getFormName());
                    }

                    ProjectVisitUser projectTesteeUserInfo = projectTesteeInfoService.getProjectTesteeUserInfo(projectTesteeTableParam.getProjectId().toString(), projectTesteeTableParam.getProjectOrgId().toString(), projectTesteeTableParam.getTesteeId().toString());
                    String testeeCode = projectTesteeUserInfo.getTesteeCode();
                    projectCheckDataParam.setTesteeCode(testeeCode);
                    List<ProjectTesteeTableParam.TableRowData> tableRowDataList = projectTesteeTableParam.getTableRowDataList();
                    for (ProjectTesteeTableParam.TableRowData testeeFormTableValue : tableRowDataList) {
                        projectCheckDataParam.setVariableId(testeeFormTableValue.getFormDetailId());
                        projectCheckDataParam.setVariableTableId(testeeFormTableValue.getFormTableId());
                        projectCheckDataParam.setVariableType(testeeFormTableValue.getType());
                        projectCheckDataParam.setVariableName(testeeFormTableValue.getLabel());
                        projectCheckDataParam.setVariableResultId(testeeFormTableValue.getTesteeResultId());
                        String testeeFormResultTableFieldValue = testeeFormTableValue.getFieldValue() == null ? "" : testeeFormTableValue.getFieldValue();
                        if(StringUtils.isEmpty(testeeFormResultTableFieldValue)){continue;}
                        projectCheckDataParam.setInputValue(testeeFormResultTableFieldValue);
                        projectCheckDataParam.setFieldText(testeeFormTableValue.getFieldText());
                        projectCheckDataParam.setUnitValue(testeeFormTableValue.getUnitValue());
                        projectCheckDataParam.setUnitText(testeeFormTableValue.getUnitText());
                        if (testeeFormTableValue.getTesteeResultId() == null) {
                            projectCheckDataParam.setType(ProjectCheckEnum.PROJECT_CHECK_STATUS_01.getName());
                            if(projectTesteeTableParam.isOcrRecord()){
                                projectCheckDataParam.setType(ProjectCheckEnum.PROJECT_CHECK_STATUS_02.getName());
                            }
                            saveProjectCheckData(projectCheckDataParam);
                        } else {
                            ProjectTesteeTableParam projectTesteeTableVo = JSON.parseObject(RequestProjectTesteeContext.get(), ProjectTesteeTableParam.class);
                            List<ProjectTesteeTableParam.TableRowData> originalTableRowDataList = projectTesteeTableVo.getTableRowDataList();
                            for (ProjectTesteeTableParam.TableRowData originalTableRowFieldRecord : originalTableRowDataList) {
                                if (ObjectUtil.equal(originalTableRowFieldRecord.getTesteeResultId(), testeeFormTableValue.getTesteeResultId())) {
                                    String tableRowFieldRecordValue = originalTableRowFieldRecord.getFieldValue() == null ? "" : originalTableRowFieldRecord.getFieldValue();
                                    String originalTextValue = originalTableRowFieldRecord.getFieldText();
                                    if(StringUtils.isEmpty(originalTextValue)){originalTextValue = tableRowFieldRecordValue;}
                                    projectCheckDataParam.setOriginalValue(originalTextValue);
                                    projectCheckDataParam.setFieldText(testeeFormTableValue.getFieldText());
                                    String testeeFormTableInputValue = testeeFormTableValue.getFieldValue() == null ? "" : testeeFormTableValue.getFieldValue();
                                    if(tableRowFieldRecordValue.equals(testeeFormTableInputValue)){
                                        continue;
                                    }
                                    if(StringUtils.isEmpty(tableRowFieldRecordValue)){
                                        projectCheckDataParam.setType(ProjectCheckEnum.PROJECT_CHECK_STATUS_01.getName());
                                    }else{
                                        projectCheckDataParam.setType(ProjectCheckEnum.PROJECT_CHECK_STATUS_03.getName());
                                    }
                                    saveProjectCheckData(projectCheckDataParam);
                                }
                            }
                        }
                    }
                }
                if(BusinessConfig.PARAMTER_OBJECT_TYPE_02.equals(targetClass.getTypeName())){
                    ProjectTesteeResultParam projectTesteeResultParam = JSON.parseObject(JSON.toJSONString(object), ProjectTesteeResultParam.class);
                    projectCheckDataParam.setProjectId(projectTesteeResultParam.getProjectId());
                    // 需求变更为多版本方案时请注意动态获取方案id
                    //projectCheckDataParam.setPlanId(projectTesteeResultParam.getPlanId());
                    FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectCheckDataParam.getProjectId().toString());
                    if(flowPlanInfo != null){
                        projectCheckDataParam.setPlanId(flowPlanInfo.getId());
                        projectCheckDataParam.setPlanName(flowPlanInfo.getPlanName());
                    }
                    projectCheckDataParam.setVisitId(projectTesteeResultParam.getVisitId());
                    ProjectVisitVo projectVisitBaseConfigVo = projectVisitConfigService.getProjectVisitBaseConfigByVisitId(projectTesteeResultParam.getVisitId().toString());
                    if(projectVisitBaseConfigVo != null){
                        projectCheckDataParam.setVisitName(projectVisitBaseConfigVo.getVisitName());
                    }
                    projectCheckDataParam.setFormId(projectTesteeResultParam.getFormId());
                    TemplateFormConfig templateFormConfigInfo = templateConfigService.getTemplateFormConfigById(projectTesteeResultParam.getFormId());
                    if(templateFormConfigInfo != null){
                        projectCheckDataParam.setFormName(templateFormConfigInfo.getFormName());
                    }
                    projectCheckDataParam.setTesteeId(projectTesteeResultParam.getTesteeId());
                    ProjectVisitUser projectTesteeUserInfo = projectTesteeInfoService.getProjectTesteeUserInfo(projectTesteeResultParam.getProjectId().toString(), projectTesteeResultParam.getProjectOrgId().toString(), projectTesteeResultParam.getTesteeId().toString());
                    String testeeCode = projectTesteeUserInfo.getTesteeCode();
                    projectCheckDataParam.setTesteeCode(testeeCode);

                    List<ProjectTesteeResultParam.TesteeFormResultValue> testeeResultParamDataList = projectTesteeResultParam.getDataList();
                    for (ProjectTesteeResultParam.TesteeFormResultValue testeeFormResultValue : testeeResultParamDataList) {
                        projectCheckDataParam.setVariableId(testeeFormResultValue.getFormDetailId());
                        projectCheckDataParam.setVariableName(testeeFormResultValue.getLabel());
                        projectCheckDataParam.setVariableType(testeeFormResultValue.getType());
                        projectCheckDataParam.setVariableResultId(testeeFormResultValue.getTesteeResultId());
                        String testeeFormResultFieldValue = testeeFormResultValue.getFieldValue() == null ? "" : testeeFormResultValue.getFieldValue().toString();
                        if(StringUtils.isEmpty(testeeFormResultFieldValue)){continue;}
                        projectCheckDataParam.setInputValue(testeeFormResultFieldValue);
                        projectCheckDataParam.setFieldText(testeeFormResultValue.getFieldText());
                        projectCheckDataParam.setUnitValue(testeeFormResultValue.getUnitValue());
                        projectCheckDataParam.setUnitText(testeeFormResultValue.getUnitText());
                        if(testeeFormResultValue.getTesteeResultId() == null){
                            projectCheckDataParam.setType(ProjectCheckEnum.PROJECT_CHECK_STATUS_01.getName());
                            saveProjectCheckData(projectCheckDataParam);
                        }else{
                            ProjectTesteeResultParam projectTesteeResultVo = JSON.parseObject(RequestProjectTesteeContext.get(), ProjectTesteeResultParam.class);
                            List<ProjectTesteeResultParam.TesteeFormResultValue> originalTesteeResultDataList = projectTesteeResultVo.getDataList();
                            for (ProjectTesteeResultParam.TesteeFormResultValue originalProjectTesteeResult : originalTesteeResultDataList) {
                                if(ObjectUtil.equal(originalProjectTesteeResult.getTesteeResultId(), testeeFormResultValue.getTesteeResultId())){
                                    String originalProjectTesteeResultValue = originalProjectTesteeResult.getFieldValue() == null ? "" : originalProjectTesteeResult.getFieldValue();
                                    String originalTextValue = originalProjectTesteeResult.getFieldText();
                                    if(StringUtils.isEmpty(originalTextValue)){originalTextValue = originalProjectTesteeResultValue;}
                                    projectCheckDataParam.setOriginalValue(originalTextValue);
                                    String testeeFormResultInputValue = testeeFormResultValue.getFieldValue() == null ? "" : testeeFormResultValue.getFieldValue();
                                    if(originalProjectTesteeResultValue.equals(testeeFormResultInputValue)){
                                        continue;
                                    }
                                    projectCheckDataParam.setFieldText(testeeFormResultValue.getFieldText());
                                    if(StringUtils.isEmpty(originalProjectTesteeResultValue)){
                                        projectCheckDataParam.setType(ProjectCheckEnum.PROJECT_CHECK_STATUS_01.getName());
                                    }else{
                                        projectCheckDataParam.setType(ProjectCheckEnum.PROJECT_CHECK_STATUS_03.getName());
                                    }
                                    saveProjectCheckData(projectCheckDataParam);
                                }
                            }
                        }
                    }
                }
                if(BusinessConfig.PARAMTER_OBJECT_TYPE_03.equals(targetClass.getTypeName())){
                    ProjectTesteeParam projectTesteeParam = JSON.parseObject(JSON.toJSONString(object), ProjectTesteeParam.class);
                    log.info("projectTesteeParam:{}", JSON.toJSON(projectTesteeParam));
                }
                if(BusinessConfig.PARAMTER_OBJECT_TYPE_04.equals(targetClass.getTypeName())){
                    ProjectParticipantViewConfigParam projectParticipantViewConfigParam = JSON.parseObject(JSON.toJSONString(object), ProjectParticipantViewConfigParam.class);
                    log.info("projectParticipantViewConfigParam:{}", JSON.toJSON(projectParticipantViewConfigParam));
                    projectParticipantViewConfigParam.getFormVariableConfigArrayList().forEach(participantViewConfig -> {
                        if(StringUtils.isEmpty(participantViewConfig.getValue())){
                            return;
                        }
                        projectCheckDataParam.setProjectId(NumberUtil.parseLong(projectParticipantViewConfigParam.getProjectId()));
                        FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectCheckDataParam.getProjectId().toString());
                        if(flowPlanInfo != null){
                            projectCheckDataParam.setPlanId(flowPlanInfo.getId());
                            projectCheckDataParam.setPlanName(flowPlanInfo.getPlanName());
                        }
                        projectCheckDataParam.setFormId(participantViewConfig.getFormId());
                        TemplateFormConfig templateFormConfigInfo = templateConfigService.getTemplateFormConfigById(participantViewConfig.getFormId());
                        if(templateFormConfigInfo != null){
                            projectCheckDataParam.setFormName(templateFormConfigInfo.getFormName());
                        }
                        projectCheckDataParam.setTesteeId(participantViewConfig.getId());
                        projectCheckDataParam.setTesteeCode(projectParticipantViewConfigParam.getTesteeCode());
                        projectCheckDataParam.setCustomTestsee(true);
                        projectCheckDataParam.setVariableId(participantViewConfig.getId());
                        projectCheckDataParam.setVariableName(participantViewConfig.getLabel());
                        projectCheckDataParam.setVariableType(participantViewConfig.getType());
                        projectCheckDataParam.setInputValue(participantViewConfig.getValue());
                        projectCheckDataParam.setFieldText(participantViewConfig.getTextValue());
                        projectCheckDataParam.setUnitValue(participantViewConfig.getUnitValue());
                        projectCheckDataParam.setUnitText(participantViewConfig.getUnitText());
                        if(participantViewConfig.getTesteeResultId() == null){
                            projectCheckDataParam.setType(ProjectCheckEnum.PROJECT_CHECK_STATUS_01.getName());
                        }else{
                            projectCheckDataParam.setVariableResultId(participantViewConfig.getId());
                            projectCheckDataParam.setType(ProjectCheckEnum.PROJECT_CHECK_STATUS_03.getName());
                            String projectParticipantViewConfigParamValue = RequestProjectTesteeContext.get();
                            ProjectParticipantViewConfigParam projectParticipantViewConfig = JSON.parseObject(projectParticipantViewConfigParamValue, ProjectParticipantViewConfigParam.class);
                            log.info("projectParticipantViewConfig:{}", JSON.toJSON(projectParticipantViewConfig));
                            for (ProjectParticipantViewConfigParam.FormVariableConfigVo formVariableConfigVo : projectParticipantViewConfig.getFormVariableConfigArrayList()) {
                                if(ObjectUtil.equal(formVariableConfigVo.getTesteeResultId(),participantViewConfig.getTesteeResultId())){
                                    if(formVariableConfigVo.getValue().equals(participantViewConfig.getValue())){
                                        return;
                                    }
                                    String textValue = formVariableConfigVo.getTextValue();
                                    if(StringUtils.isEmpty(textValue)){textValue = formVariableConfigVo.getValue();}
                                    projectCheckDataParam.setOriginalValue(textValue);
                                }
                            }
                        }
                        saveProjectCheckData(projectCheckDataParam);
                    });
                }
            }
        }
        RequestProjectTesteeContext.clear();
        //Object result = thisJoinPoint.proceed();
        //return result;
    }

    /**
     * 添加日志稽查轨迹
     * @param projectCheckDataParam
     */
    private void saveProjectCheckData(ProjectCheckDataParam projectCheckDataParam) {
        List<ProjectRoleVo> roleList = projectRoleService.getProjectRoleListByUserId(projectCheckDataParam.getProjectId().toString(), SecurityUtils.getUserId().toString());
        if(roleList != null && roleList.size() >0){
            projectCheckDataParam.setProjectRoleCode(roleList.get(0).getEnname());
        }
        projectCheckDataParam.setCreateUserId(SecurityUtils.getUserId().toString());
        projectCheckDataParam.setCreateUserName(SecurityUtils.getUserName());
        projectCheckDataParam.setDataFrom("EDC");
        projectTesteeCheckService.saveProjectCheckRequestLog(projectCheckDataParam);
    }

    /**
     * 获取参数列表
     * @param joinPoint
     * @return
     * @throws ClassNotFoundException
     * @throws NoSuchMethodException
     */
    private static Map<String, Object> getFieldsName(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        ParameterNameDiscoverer pnd = new DefaultParameterNameDiscoverer();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        String[] parameterNames = pnd.getParameterNames(method);
        Map<String, Object> paramMap = new HashMap<>(32);
        for (int i = 0; i < parameterNames.length; i++) {
            paramMap.put(parameterNames[i], args[i]);
        }
        return paramMap;
    }


}
