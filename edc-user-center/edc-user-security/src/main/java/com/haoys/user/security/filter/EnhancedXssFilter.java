package com.haoys.user.security.filter;

import com.haoys.user.common.filter.HTMLFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.IOException;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 增强的XSS过滤器
 * 
 * <p>提供全面的XSS攻击防护，包括：</p>
 * <ul>
 *   <li>HTML标签过滤</li>
 *   <li>JavaScript代码检测</li>
 *   <li>事件处理器过滤</li>
 *   <li>URL编码检测</li>
 *   <li>可配置的白名单</li>
 * </ul>
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
@Slf4j
@Component
public class EnhancedXssFilter implements Filter {

    /**
     * 是否启用XSS过滤
     */
    @Value("${xss.enabled:true}")
    private boolean enabled;

    /**
     * 排除的URL模式
     */
    @Value("${xss.excludes:/api/upload/**,/api/file/**}")
    private String excludes;

    /**
     * 匹配的URL模式
     */
    @Value("${xss.urlPatterns:/api/**}")
    private String urlPatterns;

    /**
     * 严格模式（启用更严格的XSS检测）
     */
    @Value("${xss.strictMode:true}")
    private boolean strictMode;

    /**
     * 最大输入长度限制
     */
    @Value("${xss.maxInputLength:10000}")
    private int maxInputLength;

    /**
     * HTML过滤器
     */
    private HTMLFilter htmlFilter;

    /**
     * 排除URL模式列表
     */
    private List<Pattern> excludePatterns;

    /**
     * 匹配URL模式列表
     */
    private List<Pattern> includePatterns;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        if (!enabled) {
            log.info("XSS过滤器已禁用");
            return;
        }

        // 初始化HTML过滤器
        initHtmlFilter();

        // 初始化URL模式
        initUrlPatterns();

        log.info("XSS过滤器初始化完成 - 严格模式: {}, 最大输入长度: {}", strictMode, maxInputLength);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        if (!enabled) {
            chain.doFilter(request, response);
            return;
        }

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        String requestURI = httpRequest.getRequestURI();

        // 检查是否需要过滤
        if (shouldFilter(requestURI)) {
            log.debug("对请求进行XSS过滤: {}", requestURI);
            XssHttpServletRequestWrapper wrappedRequest = 
                new XssHttpServletRequestWrapper(httpRequest, htmlFilter, strictMode, maxInputLength);
            chain.doFilter(wrappedRequest, response);
        } else {
            log.debug("跳过XSS过滤: {}", requestURI);
            chain.doFilter(request, response);
        }
    }

    @Override
    public void destroy() {
        log.info("XSS过滤器销毁");
    }

    /**
     * 初始化HTML过滤器
     */
    private void initHtmlFilter() {
        Map<String, Object> config = new HashMap<>();

        // 允许的HTML标签
        Map<String, List<String>> allowedTags = new HashMap<>();
        allowedTags.put("p", Arrays.asList("class", "id", "style"));
        allowedTags.put("br", Collections.emptyList());
        allowedTags.put("strong", Arrays.asList("class"));
        allowedTags.put("em", Arrays.asList("class"));
        allowedTags.put("u", Arrays.asList("class"));
        allowedTags.put("i", Arrays.asList("class"));
        allowedTags.put("b", Arrays.asList("class"));
        allowedTags.put("ul", Arrays.asList("class"));
        allowedTags.put("ol", Arrays.asList("class"));
        allowedTags.put("li", Arrays.asList("class"));

        config.put("vAllowed", allowedTags);
        config.put("vSelfClosingTags", new String[]{"br", "hr", "img"});
        config.put("vNeedClosingTags", new String[]{"p", "strong", "em", "u", "i", "b", "ul", "ol", "li"});
        config.put("vDisallowed", new String[]{"script", "object", "embed", "applet", "form", "iframe", "frameset"});
        config.put("vAllowedProtocols", new String[]{"http", "https", "mailto"});
        config.put("vProtocolAtts", new String[]{"src", "href"});
        config.put("vRemoveBlanks", new String[]{"script", "style"});
        config.put("vAllowedEntities", new String[]{"amp", "gt", "lt", "quot", "apos"});
        config.put("stripComment", true);
        config.put("encodeQuotes", true);

        // 使用带配置的构造函数
        htmlFilter = new HTMLFilter(config);
    }

    /**
     * 初始化URL模式
     */
    private void initUrlPatterns() {
        // 初始化排除模式
        excludePatterns = new ArrayList<>();
        if (excludes != null && !excludes.trim().isEmpty()) {
            String[] excludeArray = excludes.split(",");
            for (String exclude : excludeArray) {
                String pattern = exclude.trim().replace("**", ".*").replace("*", "[^/]*");
                excludePatterns.add(Pattern.compile(pattern));
            }
        }

        // 初始化包含模式
        includePatterns = new ArrayList<>();
        if (urlPatterns != null && !urlPatterns.trim().isEmpty()) {
            String[] includeArray = urlPatterns.split(",");
            for (String include : includeArray) {
                String pattern = include.trim().replace("**", ".*").replace("*", "[^/]*");
                includePatterns.add(Pattern.compile(pattern));
            }
        }
    }

    /**
     * 判断是否需要过滤
     */
    private boolean shouldFilter(String requestURI) {
        // 检查排除模式
        for (Pattern pattern : excludePatterns) {
            if (pattern.matcher(requestURI).matches()) {
                return false;
            }
        }

        // 检查包含模式
        if (includePatterns.isEmpty()) {
            return true; // 如果没有配置包含模式，默认过滤所有请求
        }

        for (Pattern pattern : includePatterns) {
            if (pattern.matcher(requestURI).matches()) {
                return true;
            }
        }

        return false;
    }

    /**
     * XSS请求包装器
     */
    public static class XssHttpServletRequestWrapper extends HttpServletRequestWrapper {

        private final HTMLFilter htmlFilter;
        private final boolean strictMode;
        private final int maxInputLength;

        /**
         * XSS攻击模式
         */
        private static final Pattern[] XSS_PATTERNS = {
            Pattern.compile("<script[^>]*>.*?</script>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL),
            Pattern.compile("javascript:", Pattern.CASE_INSENSITIVE),
            Pattern.compile("vbscript:", Pattern.CASE_INSENSITIVE),
            Pattern.compile("onload\\s*=", Pattern.CASE_INSENSITIVE),
            Pattern.compile("onerror\\s*=", Pattern.CASE_INSENSITIVE),
            Pattern.compile("onclick\\s*=", Pattern.CASE_INSENSITIVE),
            Pattern.compile("onmouseover\\s*=", Pattern.CASE_INSENSITIVE),
            Pattern.compile("onfocus\\s*=", Pattern.CASE_INSENSITIVE),
            Pattern.compile("onblur\\s*=", Pattern.CASE_INSENSITIVE),
            Pattern.compile("eval\\s*\\(", Pattern.CASE_INSENSITIVE),
            Pattern.compile("expression\\s*\\(", Pattern.CASE_INSENSITIVE),
            Pattern.compile("alert\\s*\\(", Pattern.CASE_INSENSITIVE),
            Pattern.compile("confirm\\s*\\(", Pattern.CASE_INSENSITIVE),
            Pattern.compile("prompt\\s*\\(", Pattern.CASE_INSENSITIVE)
        };

        public XssHttpServletRequestWrapper(HttpServletRequest request, HTMLFilter htmlFilter, 
                                          boolean strictMode, int maxInputLength) {
            super(request);
            this.htmlFilter = htmlFilter;
            this.strictMode = strictMode;
            this.maxInputLength = maxInputLength;
        }

        @Override
        public String getParameter(String name) {
            String value = super.getParameter(name);
            return cleanXss(value);
        }

        @Override
        public String[] getParameterValues(String name) {
            String[] values = super.getParameterValues(name);
            if (values != null) {
                for (int i = 0; i < values.length; i++) {
                    values[i] = cleanXss(values[i]);
                }
            }
            return values;
        }

        @Override
        public Map<String, String[]> getParameterMap() {
            Map<String, String[]> parameterMap = super.getParameterMap();
            Map<String, String[]> cleanMap = new HashMap<>();
            
            for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
                String[] values = entry.getValue();
                if (values != null) {
                    String[] cleanValues = new String[values.length];
                    for (int i = 0; i < values.length; i++) {
                        cleanValues[i] = cleanXss(values[i]);
                    }
                    cleanMap.put(entry.getKey(), cleanValues);
                } else {
                    cleanMap.put(entry.getKey(), null);
                }
            }
            
            return cleanMap;
        }

        @Override
        public String getHeader(String name) {
            String value = super.getHeader(name);
            return cleanXss(value);
        }

        /**
         * 清理XSS攻击代码
         */
        private String cleanXss(String value) {
            if (value == null || value.trim().isEmpty()) {
                return value;
            }

            // 长度检查
            if (value.length() > maxInputLength) {
                log.warn("输入长度超过限制: {} > {}", value.length(), maxInputLength);
                value = value.substring(0, maxInputLength);
            }

            // 严格模式下进行模式匹配检查
            if (strictMode) {
                for (Pattern pattern : XSS_PATTERNS) {
                    if (pattern.matcher(value).find()) {
                        log.warn("检测到XSS攻击模式: {}", pattern.pattern());
                        value = pattern.matcher(value).replaceAll("");
                    }
                }
            }

            // 使用HTML过滤器清理
            if (htmlFilter != null) {
                value = htmlFilter.filter(value);
            }

            return value;
        }
    }
}
