package com.haoys.user.security.config;

import com.google.common.util.concurrent.RateLimiter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
public class RateLimiterConfig {
    
    @Bean
    public RateLimiter rateLimiter() {
        // 每秒生成 5 个令牌
        return RateLimiter.create(1.0, 1, TimeUnit.SECONDS);
    }
}
