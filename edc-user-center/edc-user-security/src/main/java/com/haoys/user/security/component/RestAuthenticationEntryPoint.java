package com.haoys.user.security.component;

import com.alibaba.fastjson.JSON;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.util.ServletUtils;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;

public class RestAuthenticationEntryPoint implements AuthenticationEntryPoint, Serializable {

    private static final long serialVersionUID = 4633261011339027756L;

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) {
        ServletUtils.renderString(response, JSON.toJSONString(CommonResult.unauthorized(authException.getMessage())));
    }
}
