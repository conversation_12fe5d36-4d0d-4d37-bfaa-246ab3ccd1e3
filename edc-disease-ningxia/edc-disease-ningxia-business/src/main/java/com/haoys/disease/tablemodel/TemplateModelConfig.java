package com.haoys.disease.tablemodel;

public class TemplateModelConfig {

    public static final String PATIENT_MODEL_SCHEME_NAME = "disease_data_center";

    /**基本表单模型定义*/
    public static final String PATIENT_MODEL_CODE_01 = "patient_base_info";
    public static final String PATIENT_MODEL_CODE_02 = "patient_diagnosis_info";
    public static final String PATIENT_MODEL_CODE_03 = "patient_document_info";
    public static final String PATIENT_MODEL_CODE_04 = "patient_emr_info";
    public static final String PATIENT_MODEL_CODE_05 = "patient_inspection_apply";
    public static final String PATIENT_MODEL_CODE_06 = "patient_inspection_report";
    public static final String PATIENT_MODEL_CODE_07 = "patient_medication_info";
    public static final String PATIENT_MODEL_CODE_08 = "patient_visit_info";
    public static final String PATIENT_MODEL_CODE_09 = "patient_vitalsign_info";

    public static final String PATIENT_MODEL_DEFINE = "patient_model_define";
    public static final String PATIENT_MODEL_VARIABLE = "patient_model_variable";
    public static final String PATIENT_MODEL_RECORD = "patient_variable_record";



}
