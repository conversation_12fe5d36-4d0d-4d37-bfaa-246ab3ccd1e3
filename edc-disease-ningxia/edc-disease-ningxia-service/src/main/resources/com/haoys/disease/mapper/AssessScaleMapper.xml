<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.disease.mapper.AssessScaleMapper">
  <resultMap id="BaseResultMap" type="com.haoys.disease.model.AssessScale">
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="pkid" jdbcType="VARCHAR" property="pkid" />
    <result column="nihss_score" jdbcType="DOUBLE" property="nihssScore" />
    <result column="mrs_score" jdbcType="DOUBLE" property="mrsScore" />
    <result column="essen_score" jdbcType="DOUBLE" property="essenScore" />
    <result column="water_swallow_test_grade" jdbcType="VARCHAR" property="waterSwallowTestGrade" />
    <result column="left_upper_limb_myodynamia_grade" jdbcType="VARCHAR" property="leftUpperLimbMyodynamiaGrade" />
    <result column="left_lower_limb_myodynamia_grade" jdbcType="VARCHAR" property="leftLowerLimbMyodynamiaGrade" />
    <result column="right_upper_limb_myodynamia_grade" jdbcType="VARCHAR" property="rightUpperLimbMyodynamiaGrade" />
    <result column="right_lower_limb_myodynamia_grade" jdbcType="VARCHAR" property="rightLowerLimbMyodynamiaGrade" />
    <result column="assess_datetime" jdbcType="TIMESTAMP" property="assessDatetime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "patient_sn", "visit_sn", "pkid", "nihss_score", "mrs_score", "essen_score", "water_swallow_test_grade", 
    "left_upper_limb_myodynamia_grade", "left_lower_limb_myodynamia_grade", "right_upper_limb_myodynamia_grade", 
    "right_lower_limb_myodynamia_grade", "assess_datetime"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.disease.model.AssessScaleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."assess_scale"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.haoys.disease.model.AssessScaleExample">
    delete from "public"."assess_scale"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.disease.model.AssessScale">
    insert into "public"."assess_scale" ("patient_sn", "visit_sn", "pkid", 
      "nihss_score", "mrs_score", "essen_score", 
      "water_swallow_test_grade", "left_upper_limb_myodynamia_grade", 
      "left_lower_limb_myodynamia_grade", "right_upper_limb_myodynamia_grade", 
      "right_lower_limb_myodynamia_grade", "assess_datetime"
      )
    values (#{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR}, #{pkid,jdbcType=VARCHAR}, 
      #{nihssScore,jdbcType=DOUBLE}, #{mrsScore,jdbcType=DOUBLE}, #{essenScore,jdbcType=DOUBLE}, 
      #{waterSwallowTestGrade,jdbcType=VARCHAR}, #{leftUpperLimbMyodynamiaGrade,jdbcType=VARCHAR}, 
      #{leftLowerLimbMyodynamiaGrade,jdbcType=VARCHAR}, #{rightUpperLimbMyodynamiaGrade,jdbcType=VARCHAR}, 
      #{rightLowerLimbMyodynamiaGrade,jdbcType=VARCHAR}, #{assessDatetime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.disease.model.AssessScale">
    insert into "public"."assess_scale"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="pkid != null">
        "pkid",
      </if>
      <if test="nihssScore != null">
        "nihss_score",
      </if>
      <if test="mrsScore != null">
        "mrs_score",
      </if>
      <if test="essenScore != null">
        "essen_score",
      </if>
      <if test="waterSwallowTestGrade != null">
        "water_swallow_test_grade",
      </if>
      <if test="leftUpperLimbMyodynamiaGrade != null">
        "left_upper_limb_myodynamia_grade",
      </if>
      <if test="leftLowerLimbMyodynamiaGrade != null">
        "left_lower_limb_myodynamia_grade",
      </if>
      <if test="rightUpperLimbMyodynamiaGrade != null">
        "right_upper_limb_myodynamia_grade",
      </if>
      <if test="rightLowerLimbMyodynamiaGrade != null">
        "right_lower_limb_myodynamia_grade",
      </if>
      <if test="assessDatetime != null">
        "assess_datetime",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="pkid != null">
        #{pkid,jdbcType=VARCHAR},
      </if>
      <if test="nihssScore != null">
        #{nihssScore,jdbcType=DOUBLE},
      </if>
      <if test="mrsScore != null">
        #{mrsScore,jdbcType=DOUBLE},
      </if>
      <if test="essenScore != null">
        #{essenScore,jdbcType=DOUBLE},
      </if>
      <if test="waterSwallowTestGrade != null">
        #{waterSwallowTestGrade,jdbcType=VARCHAR},
      </if>
      <if test="leftUpperLimbMyodynamiaGrade != null">
        #{leftUpperLimbMyodynamiaGrade,jdbcType=VARCHAR},
      </if>
      <if test="leftLowerLimbMyodynamiaGrade != null">
        #{leftLowerLimbMyodynamiaGrade,jdbcType=VARCHAR},
      </if>
      <if test="rightUpperLimbMyodynamiaGrade != null">
        #{rightUpperLimbMyodynamiaGrade,jdbcType=VARCHAR},
      </if>
      <if test="rightLowerLimbMyodynamiaGrade != null">
        #{rightLowerLimbMyodynamiaGrade,jdbcType=VARCHAR},
      </if>
      <if test="assessDatetime != null">
        #{assessDatetime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.disease.model.AssessScaleExample" resultType="java.lang.Long">
    select count(*) from "public"."assess_scale"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."assess_scale"
    <set>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.pkid != null">
        "pkid" = #{record.pkid,jdbcType=VARCHAR},
      </if>
      <if test="record.nihssScore != null">
        "nihss_score" = #{record.nihssScore,jdbcType=DOUBLE},
      </if>
      <if test="record.mrsScore != null">
        "mrs_score" = #{record.mrsScore,jdbcType=DOUBLE},
      </if>
      <if test="record.essenScore != null">
        "essen_score" = #{record.essenScore,jdbcType=DOUBLE},
      </if>
      <if test="record.waterSwallowTestGrade != null">
        "water_swallow_test_grade" = #{record.waterSwallowTestGrade,jdbcType=VARCHAR},
      </if>
      <if test="record.leftUpperLimbMyodynamiaGrade != null">
        "left_upper_limb_myodynamia_grade" = #{record.leftUpperLimbMyodynamiaGrade,jdbcType=VARCHAR},
      </if>
      <if test="record.leftLowerLimbMyodynamiaGrade != null">
        "left_lower_limb_myodynamia_grade" = #{record.leftLowerLimbMyodynamiaGrade,jdbcType=VARCHAR},
      </if>
      <if test="record.rightUpperLimbMyodynamiaGrade != null">
        "right_upper_limb_myodynamia_grade" = #{record.rightUpperLimbMyodynamiaGrade,jdbcType=VARCHAR},
      </if>
      <if test="record.rightLowerLimbMyodynamiaGrade != null">
        "right_lower_limb_myodynamia_grade" = #{record.rightLowerLimbMyodynamiaGrade,jdbcType=VARCHAR},
      </if>
      <if test="record.assessDatetime != null">
        "assess_datetime" = #{record.assessDatetime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."assess_scale"
    set "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "pkid" = #{record.pkid,jdbcType=VARCHAR},
      "nihss_score" = #{record.nihssScore,jdbcType=DOUBLE},
      "mrs_score" = #{record.mrsScore,jdbcType=DOUBLE},
      "essen_score" = #{record.essenScore,jdbcType=DOUBLE},
      "water_swallow_test_grade" = #{record.waterSwallowTestGrade,jdbcType=VARCHAR},
      "left_upper_limb_myodynamia_grade" = #{record.leftUpperLimbMyodynamiaGrade,jdbcType=VARCHAR},
      "left_lower_limb_myodynamia_grade" = #{record.leftLowerLimbMyodynamiaGrade,jdbcType=VARCHAR},
      "right_upper_limb_myodynamia_grade" = #{record.rightUpperLimbMyodynamiaGrade,jdbcType=VARCHAR},
      "right_lower_limb_myodynamia_grade" = #{record.rightLowerLimbMyodynamiaGrade,jdbcType=VARCHAR},
      "assess_datetime" = #{record.assessDatetime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>