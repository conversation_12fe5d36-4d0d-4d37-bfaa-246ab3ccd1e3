<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.disease.mapper.AnticoagulantTherapyMapper">
  <resultMap id="BaseResultMap" type="com.haoys.disease.model.AnticoagulantTherapy">
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="pkid" jdbcType="VARCHAR" property="pkid" />
    <result column="generic_name" jdbcType="VARCHAR" property="genericName" />
    <result column="drug_name" jdbcType="VARCHAR" property="drugName" />
    <result column="start_datetime" jdbcType="TIMESTAMP" property="startDatetime" />
    <result column="end_datetime" jdbcType="TIMESTAMP" property="endDatetime" />
    <result column="dosage" jdbcType="VARCHAR" property="dosage" />
    <result column="dosage_units" jdbcType="VARCHAR" property="dosageUnits" />
    <result column="frequency" jdbcType="VARCHAR" property="frequency" />
    <result column="administration_route" jdbcType="VARCHAR" property="administrationRoute" />
    <result column="order_class" jdbcType="VARCHAR" property="orderClass" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "patient_sn", "visit_sn", "pkid", "generic_name", "drug_name", "start_datetime", 
    "end_datetime", "dosage", "dosage_units", "frequency", "administration_route", "order_class"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.disease.model.AnticoagulantTherapyExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."anticoagulant_therapy"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.haoys.disease.model.AnticoagulantTherapyExample">
    delete from "public"."anticoagulant_therapy"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.disease.model.AnticoagulantTherapy">
    insert into "public"."anticoagulant_therapy" ("patient_sn", "visit_sn", "pkid", 
      "generic_name", "drug_name", "start_datetime", 
      "end_datetime", "dosage", "dosage_units", 
      "frequency", "administration_route", "order_class"
      )
    values (#{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR}, #{pkid,jdbcType=VARCHAR}, 
      #{genericName,jdbcType=VARCHAR}, #{drugName,jdbcType=VARCHAR}, #{startDatetime,jdbcType=TIMESTAMP}, 
      #{endDatetime,jdbcType=TIMESTAMP}, #{dosage,jdbcType=VARCHAR}, #{dosageUnits,jdbcType=VARCHAR}, 
      #{frequency,jdbcType=VARCHAR}, #{administrationRoute,jdbcType=VARCHAR}, #{orderClass,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.disease.model.AnticoagulantTherapy">
    insert into "public"."anticoagulant_therapy"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="pkid != null">
        "pkid",
      </if>
      <if test="genericName != null">
        "generic_name",
      </if>
      <if test="drugName != null">
        "drug_name",
      </if>
      <if test="startDatetime != null">
        "start_datetime",
      </if>
      <if test="endDatetime != null">
        "end_datetime",
      </if>
      <if test="dosage != null">
        "dosage",
      </if>
      <if test="dosageUnits != null">
        "dosage_units",
      </if>
      <if test="frequency != null">
        "frequency",
      </if>
      <if test="administrationRoute != null">
        "administration_route",
      </if>
      <if test="orderClass != null">
        "order_class",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="pkid != null">
        #{pkid,jdbcType=VARCHAR},
      </if>
      <if test="genericName != null">
        #{genericName,jdbcType=VARCHAR},
      </if>
      <if test="drugName != null">
        #{drugName,jdbcType=VARCHAR},
      </if>
      <if test="startDatetime != null">
        #{startDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="endDatetime != null">
        #{endDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="dosage != null">
        #{dosage,jdbcType=VARCHAR},
      </if>
      <if test="dosageUnits != null">
        #{dosageUnits,jdbcType=VARCHAR},
      </if>
      <if test="frequency != null">
        #{frequency,jdbcType=VARCHAR},
      </if>
      <if test="administrationRoute != null">
        #{administrationRoute,jdbcType=VARCHAR},
      </if>
      <if test="orderClass != null">
        #{orderClass,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.disease.model.AnticoagulantTherapyExample" resultType="java.lang.Long">
    select count(*) from "public"."anticoagulant_therapy"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."anticoagulant_therapy"
    <set>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.pkid != null">
        "pkid" = #{record.pkid,jdbcType=VARCHAR},
      </if>
      <if test="record.genericName != null">
        "generic_name" = #{record.genericName,jdbcType=VARCHAR},
      </if>
      <if test="record.drugName != null">
        "drug_name" = #{record.drugName,jdbcType=VARCHAR},
      </if>
      <if test="record.startDatetime != null">
        "start_datetime" = #{record.startDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endDatetime != null">
        "end_datetime" = #{record.endDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dosage != null">
        "dosage" = #{record.dosage,jdbcType=VARCHAR},
      </if>
      <if test="record.dosageUnits != null">
        "dosage_units" = #{record.dosageUnits,jdbcType=VARCHAR},
      </if>
      <if test="record.frequency != null">
        "frequency" = #{record.frequency,jdbcType=VARCHAR},
      </if>
      <if test="record.administrationRoute != null">
        "administration_route" = #{record.administrationRoute,jdbcType=VARCHAR},
      </if>
      <if test="record.orderClass != null">
        "order_class" = #{record.orderClass,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."anticoagulant_therapy"
    set "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "pkid" = #{record.pkid,jdbcType=VARCHAR},
      "generic_name" = #{record.genericName,jdbcType=VARCHAR},
      "drug_name" = #{record.drugName,jdbcType=VARCHAR},
      "start_datetime" = #{record.startDatetime,jdbcType=TIMESTAMP},
      "end_datetime" = #{record.endDatetime,jdbcType=TIMESTAMP},
      "dosage" = #{record.dosage,jdbcType=VARCHAR},
      "dosage_units" = #{record.dosageUnits,jdbcType=VARCHAR},
      "frequency" = #{record.frequency,jdbcType=VARCHAR},
      "administration_route" = #{record.administrationRoute,jdbcType=VARCHAR},
      "order_class" = #{record.orderClass,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>