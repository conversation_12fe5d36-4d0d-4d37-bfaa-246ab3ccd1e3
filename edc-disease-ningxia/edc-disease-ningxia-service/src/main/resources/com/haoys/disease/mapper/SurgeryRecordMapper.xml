<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.disease.mapper.SurgeryRecordMapper">
  <resultMap id="BaseResultMap" type="com.haoys.disease.model.SurgeryRecord">
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="pkid" jdbcType="VARCHAR" property="pkid" />
    <result column="operation_begin_time" jdbcType="TIMESTAMP" property="operationBeginTime" />
    <result column="operation_name" jdbcType="VARCHAR" property="operationName" />
    <result column="diag_preoperation" jdbcType="VARCHAR" property="diagPreoperation" />
    <result column="diag_postoperation" jdbcType="VARCHAR" property="diagPostoperation" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "patient_sn", "visit_sn", "pkid", "operation_begin_time", "operation_name", "diag_preoperation", 
    "diag_postoperation"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.disease.model.SurgeryRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."surgery_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.haoys.disease.model.SurgeryRecordExample">
    delete from "public"."surgery_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.disease.model.SurgeryRecord">
    insert into "public"."surgery_record" ("patient_sn", "visit_sn", "pkid", 
      "operation_begin_time", "operation_name", "diag_preoperation", 
      "diag_postoperation")
    values (#{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR}, #{pkid,jdbcType=VARCHAR}, 
      #{operationBeginTime,jdbcType=TIMESTAMP}, #{operationName,jdbcType=VARCHAR}, #{diagPreoperation,jdbcType=VARCHAR}, 
      #{diagPostoperation,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.disease.model.SurgeryRecord">
    insert into "public"."surgery_record"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="pkid != null">
        "pkid",
      </if>
      <if test="operationBeginTime != null">
        "operation_begin_time",
      </if>
      <if test="operationName != null">
        "operation_name",
      </if>
      <if test="diagPreoperation != null">
        "diag_preoperation",
      </if>
      <if test="diagPostoperation != null">
        "diag_postoperation",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="pkid != null">
        #{pkid,jdbcType=VARCHAR},
      </if>
      <if test="operationBeginTime != null">
        #{operationBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationName != null">
        #{operationName,jdbcType=VARCHAR},
      </if>
      <if test="diagPreoperation != null">
        #{diagPreoperation,jdbcType=VARCHAR},
      </if>
      <if test="diagPostoperation != null">
        #{diagPostoperation,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.disease.model.SurgeryRecordExample" resultType="java.lang.Long">
    select count(*) from "public"."surgery_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."surgery_record"
    <set>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.pkid != null">
        "pkid" = #{record.pkid,jdbcType=VARCHAR},
      </if>
      <if test="record.operationBeginTime != null">
        "operation_begin_time" = #{record.operationBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operationName != null">
        "operation_name" = #{record.operationName,jdbcType=VARCHAR},
      </if>
      <if test="record.diagPreoperation != null">
        "diag_preoperation" = #{record.diagPreoperation,jdbcType=VARCHAR},
      </if>
      <if test="record.diagPostoperation != null">
        "diag_postoperation" = #{record.diagPostoperation,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."surgery_record"
    set "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "pkid" = #{record.pkid,jdbcType=VARCHAR},
      "operation_begin_time" = #{record.operationBeginTime,jdbcType=TIMESTAMP},
      "operation_name" = #{record.operationName,jdbcType=VARCHAR},
      "diag_preoperation" = #{record.diagPreoperation,jdbcType=VARCHAR},
      "diag_postoperation" = #{record.diagPostoperation,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>