<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.disease.mapper.StkInfoMapper">
  <resultMap id="BaseResultMap" type="com.haoys.disease.model.StkInfo">
    <result column="pkid" jdbcType="VARCHAR" property="pkid" />
    <result column="part" jdbcType="VARCHAR" property="part" />
    <result column="side" jdbcType="VARCHAR" property="side" />
    <result column="is_old_stk" jdbcType="VARCHAR" property="isOldStk" />
    <result column="is_lacunar_stk" jdbcType="VARCHAR" property="isLacunarStk" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "pkid", "part", "side", "is_old_stk", "is_lacunar_stk", "patient_sn", "visit_sn"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.disease.model.StkInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."stk_info"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.haoys.disease.model.StkInfoExample">
    delete from "public"."stk_info"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.disease.model.StkInfo">
    insert into "public"."stk_info" ("pkid", "part", "side", 
      "is_old_stk", "is_lacunar_stk", "patient_sn", 
      "visit_sn")
    values (#{pkid,jdbcType=VARCHAR}, #{part,jdbcType=VARCHAR}, #{side,jdbcType=VARCHAR}, 
      #{isOldStk,jdbcType=VARCHAR}, #{isLacunarStk,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, 
      #{visitSn,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.disease.model.StkInfo">
    insert into "public"."stk_info"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pkid != null">
        "pkid",
      </if>
      <if test="part != null">
        "part",
      </if>
      <if test="side != null">
        "side",
      </if>
      <if test="isOldStk != null">
        "is_old_stk",
      </if>
      <if test="isLacunarStk != null">
        "is_lacunar_stk",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pkid != null">
        #{pkid,jdbcType=VARCHAR},
      </if>
      <if test="part != null">
        #{part,jdbcType=VARCHAR},
      </if>
      <if test="side != null">
        #{side,jdbcType=VARCHAR},
      </if>
      <if test="isOldStk != null">
        #{isOldStk,jdbcType=VARCHAR},
      </if>
      <if test="isLacunarStk != null">
        #{isLacunarStk,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.disease.model.StkInfoExample" resultType="java.lang.Long">
    select count(*) from "public"."stk_info"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."stk_info"
    <set>
      <if test="record.pkid != null">
        "pkid" = #{record.pkid,jdbcType=VARCHAR},
      </if>
      <if test="record.part != null">
        "part" = #{record.part,jdbcType=VARCHAR},
      </if>
      <if test="record.side != null">
        "side" = #{record.side,jdbcType=VARCHAR},
      </if>
      <if test="record.isOldStk != null">
        "is_old_stk" = #{record.isOldStk,jdbcType=VARCHAR},
      </if>
      <if test="record.isLacunarStk != null">
        "is_lacunar_stk" = #{record.isLacunarStk,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."stk_info"
    set "pkid" = #{record.pkid,jdbcType=VARCHAR},
      "part" = #{record.part,jdbcType=VARCHAR},
      "side" = #{record.side,jdbcType=VARCHAR},
      "is_old_stk" = #{record.isOldStk,jdbcType=VARCHAR},
      "is_lacunar_stk" = #{record.isLacunarStk,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>