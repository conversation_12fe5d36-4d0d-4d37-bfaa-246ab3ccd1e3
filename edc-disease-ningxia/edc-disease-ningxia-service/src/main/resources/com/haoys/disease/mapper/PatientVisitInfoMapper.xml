<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.disease.mapper.PatientVisitInfoMapper">
  <resultMap id="BaseResultMap" type="com.haoys.disease.model.PatientVisitInfo">
    <result column="registerdttm" jdbcType="DATE" property="registerdttm" />
    <result column="patient_id" jdbcType="VARCHAR" property="patientId" />
    <result column="visit_serial_number" jdbcType="VARCHAR" property="visitSerialNumber" />
    <result column="visit_type" jdbcType="VARCHAR" property="visitType" />
    <result column="department_code" jdbcType="VARCHAR" property="departmentCode" />
    <result column="department_name" jdbcType="VARCHAR" property="departmentName" />
    <result column="ward_code" jdbcType="VARCHAR" property="wardCode" />
    <result column="ward_name" jdbcType="VARCHAR" property="wardName" />
    <result column="bed_code" jdbcType="VARCHAR" property="bedCode" />
    <result column="admitdttm" jdbcType="DATE" property="admitdttm" />
    <result column="dischargedttm" jdbcType="DATE" property="dischargedttm" />
    <result column="consultingmd_code" jdbcType="VARCHAR" property="consultingmdCode" />
    <result column="consultingmd_name" jdbcType="VARCHAR" property="consultingmdName" />
    <result column="attendingmd_code" jdbcType="VARCHAR" property="attendingmdCode" />
    <result column="attendingmd_name" jdbcType="VARCHAR" property="attendingmdName" />
    <result column="outcome" jdbcType="VARCHAR" property="outcome" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    registerdttm, patient_id, visit_serial_number, visit_type, department_code, department_name,
    ward_code, ward_name, bed_code, admitdttm, dischargedttm, consultingmd_code, consultingmd_name,
    attendingmd_code, attendingmd_name, outcome, create_time
  </sql>
  <select id="selectByExample" parameterType="com.haoys.disease.model.PatientVisitInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from patient_visit_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.haoys.disease.model.PatientVisitInfoExample">
    delete from patient_visit_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.disease.model.PatientVisitInfo">
    insert into patient_visit_info (registerdttm, patient_id, visit_serial_number,
      visit_type, department_code, department_name,
      ward_code, ward_name, bed_code,
      admitdttm, dischargedttm, consultingmd_code,
      consultingmd_name, attendingmd_code, attendingmd_name,
      outcome, create_time)
    values (#{registerdttm,jdbcType=DATE}, #{patientId,jdbcType=VARCHAR}, #{visitSerialNumber,jdbcType=VARCHAR},
      #{visitType,jdbcType=VARCHAR}, #{departmentCode,jdbcType=VARCHAR}, #{departmentName,jdbcType=VARCHAR},
      #{wardCode,jdbcType=VARCHAR}, #{wardName,jdbcType=VARCHAR}, #{bedCode,jdbcType=VARCHAR},
      #{admitdttm,jdbcType=DATE}, #{dischargedttm,jdbcType=DATE}, #{consultingmdCode,jdbcType=VARCHAR},
      #{consultingmdName,jdbcType=VARCHAR}, #{attendingmdCode,jdbcType=VARCHAR}, #{attendingmdName,jdbcType=VARCHAR},
      #{outcome,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.disease.model.PatientVisitInfo">
    insert into patient_visit_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="registerdttm != null">
        registerdttm,
      </if>
      <if test="patientId != null">
        patient_id,
      </if>
      <if test="visitSerialNumber != null">
        visit_serial_number,
      </if>
      <if test="visitType != null">
        visit_type,
      </if>
      <if test="departmentCode != null">
        department_code,
      </if>
      <if test="departmentName != null">
        department_name,
      </if>
      <if test="wardCode != null">
        ward_code,
      </if>
      <if test="wardName != null">
        ward_name,
      </if>
      <if test="bedCode != null">
        bed_code,
      </if>
      <if test="admitdttm != null">
        admitdttm,
      </if>
      <if test="dischargedttm != null">
        dischargedttm,
      </if>
      <if test="consultingmdCode != null">
        consultingmd_code,
      </if>
      <if test="consultingmdName != null">
        consultingmd_name,
      </if>
      <if test="attendingmdCode != null">
        attendingmd_code,
      </if>
      <if test="attendingmdName != null">
        attendingmd_name,
      </if>
      <if test="outcome != null">
        outcome,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="registerdttm != null">
        #{registerdttm,jdbcType=DATE},
      </if>
      <if test="patientId != null">
        #{patientId,jdbcType=VARCHAR},
      </if>
      <if test="visitSerialNumber != null">
        #{visitSerialNumber,jdbcType=VARCHAR},
      </if>
      <if test="visitType != null">
        #{visitType,jdbcType=VARCHAR},
      </if>
      <if test="departmentCode != null">
        #{departmentCode,jdbcType=VARCHAR},
      </if>
      <if test="departmentName != null">
        #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="wardCode != null">
        #{wardCode,jdbcType=VARCHAR},
      </if>
      <if test="wardName != null">
        #{wardName,jdbcType=VARCHAR},
      </if>
      <if test="bedCode != null">
        #{bedCode,jdbcType=VARCHAR},
      </if>
      <if test="admitdttm != null">
        #{admitdttm,jdbcType=DATE},
      </if>
      <if test="dischargedttm != null">
        #{dischargedttm,jdbcType=DATE},
      </if>
      <if test="consultingmdCode != null">
        #{consultingmdCode,jdbcType=VARCHAR},
      </if>
      <if test="consultingmdName != null">
        #{consultingmdName,jdbcType=VARCHAR},
      </if>
      <if test="attendingmdCode != null">
        #{attendingmdCode,jdbcType=VARCHAR},
      </if>
      <if test="attendingmdName != null">
        #{attendingmdName,jdbcType=VARCHAR},
      </if>
      <if test="outcome != null">
        #{outcome,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.disease.model.PatientVisitInfoExample" resultType="java.lang.Long">
    select count(*) from patient_visit_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update patient_visit_info
    <set>
      <if test="record.registerdttm != null">
        registerdttm = #{record.registerdttm,jdbcType=DATE},
      </if>
      <if test="record.patientId != null">
        patient_id = #{record.patientId,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSerialNumber != null">
        visit_serial_number = #{record.visitSerialNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.visitType != null">
        visit_type = #{record.visitType,jdbcType=VARCHAR},
      </if>
      <if test="record.departmentCode != null">
        department_code = #{record.departmentCode,jdbcType=VARCHAR},
      </if>
      <if test="record.departmentName != null">
        department_name = #{record.departmentName,jdbcType=VARCHAR},
      </if>
      <if test="record.wardCode != null">
        ward_code = #{record.wardCode,jdbcType=VARCHAR},
      </if>
      <if test="record.wardName != null">
        ward_name = #{record.wardName,jdbcType=VARCHAR},
      </if>
      <if test="record.bedCode != null">
        bed_code = #{record.bedCode,jdbcType=VARCHAR},
      </if>
      <if test="record.admitdttm != null">
        admitdttm = #{record.admitdttm,jdbcType=DATE},
      </if>
      <if test="record.dischargedttm != null">
        dischargedttm = #{record.dischargedttm,jdbcType=DATE},
      </if>
      <if test="record.consultingmdCode != null">
        consultingmd_code = #{record.consultingmdCode,jdbcType=VARCHAR},
      </if>
      <if test="record.consultingmdName != null">
        consultingmd_name = #{record.consultingmdName,jdbcType=VARCHAR},
      </if>
      <if test="record.attendingmdCode != null">
        attendingmd_code = #{record.attendingmdCode,jdbcType=VARCHAR},
      </if>
      <if test="record.attendingmdName != null">
        attendingmd_name = #{record.attendingmdName,jdbcType=VARCHAR},
      </if>
      <if test="record.outcome != null">
        outcome = #{record.outcome,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update patient_visit_info
    set registerdttm = #{record.registerdttm,jdbcType=DATE},
      patient_id = #{record.patientId,jdbcType=VARCHAR},
      visit_serial_number = #{record.visitSerialNumber,jdbcType=VARCHAR},
      visit_type = #{record.visitType,jdbcType=VARCHAR},
      department_code = #{record.departmentCode,jdbcType=VARCHAR},
      department_name = #{record.departmentName,jdbcType=VARCHAR},
      ward_code = #{record.wardCode,jdbcType=VARCHAR},
      ward_name = #{record.wardName,jdbcType=VARCHAR},
      bed_code = #{record.bedCode,jdbcType=VARCHAR},
      admitdttm = #{record.admitdttm,jdbcType=DATE},
      dischargedttm = #{record.dischargedttm,jdbcType=DATE},
      consultingmd_code = #{record.consultingmdCode,jdbcType=VARCHAR},
      consultingmd_name = #{record.consultingmdName,jdbcType=VARCHAR},
      attendingmd_code = #{record.attendingmdCode,jdbcType=VARCHAR},
      attendingmd_name = #{record.attendingmdName,jdbcType=VARCHAR},
      outcome = #{record.outcome,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <select id="selectPatientVisitList" resultType="com.haoys.disease.domain.vo.PatientVisitVo">
    SELECT
      vi.registerdttm as registerdttm,
      vi.department_code as departmentCode,
      vi.department_name as departmentName,
      di.dxicd10desc as dxicd10desc,
      di.dxdesc as dxdesc
    FROM
      patient_visit_info vi
        join patient_diagnosis_info di on di.visit_serial_number=vi.visit_serial_number
    where vi.patient_id=#{patientId}

    order by vi.registerdttm
  </select>

  <select id="getPatientVisitInfo" resultType="com.haoys.disease.domain.vo.PatientVisitVo">
    select * from patient_visit_info where patient_id = #{patientId}
  </select>

  <select id="getPatientVisitSerialNumber" resultMap="BaseResultMap">
    select * from patient_visit_info where patient_id = #{patientId} and visit_serial_number =#{visitSerialNumber}
  </select>

  <select id="countQuarter" resultType="java.lang.Long">
    select count(0) from patient_diagnosis_info
    where 1=1
    and MONTH(dxdttm) in
    <foreach collection="months" item="month" separator="," open="(" close=")">
      #{month}
    </foreach>
  </select>

  <select id="countPgSqlQuarter" resultType="java.lang.Long">
    select count(0) from patient_diagnosis_info
    where 1=1
    and  date_part( 'months', dxdttm )  in
    <foreach collection="months" item="month" separator="," open="(" close=")">
      #{month}
    </foreach>
  </select>

  <select id="countVisitInfoByYear" resultType="java.util.Map">
    select
        count(0) as num,
        MONTH (registerdttm) as month
    from patient_visit_info
    where
        YEAR (registerdttm)=#{year}
    <if test="visitType!=null">
        and visit_type=#{visitType}
    </if>
    group by month
  </select>
  <select id="countPatientByYear" resultType="java.util.Map">
    select count(0) as num,  month
    from (
      select
      patient_id, MONTH (registerdttm) as month
      from patient_visit_info
      where YEAR (registerdttm)=#{year} group by patient_id, month
      ) t group by month
  </select>

  <select id="countPgSqlPatientByYear" resultType="java.util.Map">
    select count(0) as num, CAST(month AS INTEGER) as month
    from (
      select
      patient_id, date_part( 'months', registerdttm ) as month
      from patient_visit_info
      where  date_part( 'year', registerdttm )=#{year} group by patient_id, month
      ) t group by month
  </select>

  <select id="countPgSqlVisitInfoByYear" resultType="java.util.Map">
    select count(0) as num, CAST(month AS INTEGER) as month
    from (
      select
      patient_id, date_part( 'months', registerdttm ) as month
      from patient_visit_info
      where  date_part( 'year', registerdttm )=#{year} group by patient_id, month
      ) t group by month
  </select>

</mapper>
