<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.disease.mapper.PatientAnalysisDatasetMapper">
  <resultMap id="BaseResultMap" type="com.haoys.disease.model.PatientAnalysisDataset">
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="disease_type" jdbcType="VARCHAR" property="diseaseType" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="batch_code" jdbcType="VARCHAR" property="batchCode" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="diagnostic_result" jdbcType="VARCHAR" property="diagnosticResult" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="enabled" jdbcType="BIT" property="enabled" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, disease_type, title, batch_code, description, diagnostic_result, create_user_id, 
    status, enabled, create_time
  </sql>
  <select id="selectByExample" parameterType="com.haoys.disease.model.PatientAnalysisDatasetExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from patient_analysis_dataset
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.haoys.disease.model.PatientAnalysisDatasetExample">
    delete from patient_analysis_dataset
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.disease.model.PatientAnalysisDataset">
    insert into patient_analysis_dataset (id, disease_type, title, 
      batch_code, description, diagnostic_result, 
      create_user_id, status, enabled, 
      create_time)
    values (#{id,jdbcType=VARCHAR}, #{diseaseType,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, 
      #{batchCode,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{diagnosticResult,jdbcType=VARCHAR}, 
      #{createUserId,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{enabled,jdbcType=BIT}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.disease.model.PatientAnalysisDataset">
    insert into patient_analysis_dataset
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="diseaseType != null">
        disease_type,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="batchCode != null">
        batch_code,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="diagnosticResult != null">
        diagnostic_result,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="diseaseType != null">
        #{diseaseType,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="batchCode != null">
        #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="diagnosticResult != null">
        #{diagnosticResult,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.disease.model.PatientAnalysisDatasetExample" resultType="java.lang.Long">
    select count(*) from patient_analysis_dataset
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update patient_analysis_dataset
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.diseaseType != null">
        disease_type = #{record.diseaseType,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.batchCode != null">
        batch_code = #{record.batchCode,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.diagnosticResult != null">
        diagnostic_result = #{record.diagnosticResult,jdbcType=VARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.enabled != null">
        enabled = #{record.enabled,jdbcType=BIT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update patient_analysis_dataset
    set id = #{record.id,jdbcType=VARCHAR},
      disease_type = #{record.diseaseType,jdbcType=VARCHAR},
      title = #{record.title,jdbcType=VARCHAR},
      batch_code = #{record.batchCode,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      diagnostic_result = #{record.diagnosticResult,jdbcType=VARCHAR},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      enabled = #{record.enabled,jdbcType=BIT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <delete id="deleteById">
    delete from patient_analysis_dataset where id = #{dataSetId,jdbcType=VARCHAR}
  </delete>

  <select id="selectByDataSetId" resultMap="BaseResultMap">
    select * from patient_analysis_dataset where id = #{dataSetId}
  </select>

  <select id="getPatientAnalysisDataSetForPage" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from patient_analysis_dataset
    where 1 = 1
    <if test="searchValue != null and searchValue != ''">
      and (batch_code = #{searchValue} or title like concat('%',#{searchValue},'%'))
    </if>
    <if test="enabledValue != null">
      and enabled = #{enabledValue,jdbcType=BIT}
    </if>
    and create_user_id = #{createUserId,jdbcType=VARCHAR}
    order by create_time desc
  </select>

  <select id="getPatientAnalysisDataSetTitle" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from patient_analysis_dataset where title = #{title} limit 1
  </select>

  <select id="getPatientAnalysisDataSetBatchCode" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from patient_analysis_dataset where batch_code = #{batchCode} limit 1
  </select>

</mapper>