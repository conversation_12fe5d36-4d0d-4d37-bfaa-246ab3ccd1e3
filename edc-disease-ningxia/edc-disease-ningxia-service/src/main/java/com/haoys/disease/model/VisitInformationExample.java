package com.haoys.disease.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class VisitInformationExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public VisitInformationExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andPkidIsNull() {
            addCriterion("\"pkid\" is null");
            return (Criteria) this;
        }

        public Criteria andPkidIsNotNull() {
            addCriterion("\"pkid\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkidEqualTo(String value) {
            addCriterion("\"pkid\" =", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotEqualTo(String value) {
            addCriterion("\"pkid\" <>", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThan(String value) {
            addCriterion("\"pkid\" >", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThanOrEqualTo(String value) {
            addCriterion("\"pkid\" >=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThan(String value) {
            addCriterion("\"pkid\" <", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThanOrEqualTo(String value) {
            addCriterion("\"pkid\" <=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLike(String value) {
            addCriterion("\"pkid\" like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotLike(String value) {
            addCriterion("\"pkid\" not like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidIn(List<String> values) {
            addCriterion("\"pkid\" in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotIn(List<String> values) {
            addCriterion("\"pkid\" not in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidBetween(String value1, String value2) {
            addCriterion("\"pkid\" between", value1, value2, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotBetween(String value1, String value2) {
            addCriterion("\"pkid\" not between", value1, value2, "pkid");
            return (Criteria) this;
        }

        public Criteria andVisitTypeIsNull() {
            addCriterion("\"visit_type\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitTypeIsNotNull() {
            addCriterion("\"visit_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitTypeEqualTo(String value) {
            addCriterion("\"visit_type\" =", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeNotEqualTo(String value) {
            addCriterion("\"visit_type\" <>", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeGreaterThan(String value) {
            addCriterion("\"visit_type\" >", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_type\" >=", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeLessThan(String value) {
            addCriterion("\"visit_type\" <", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeLessThanOrEqualTo(String value) {
            addCriterion("\"visit_type\" <=", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeLike(String value) {
            addCriterion("\"visit_type\" like", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeNotLike(String value) {
            addCriterion("\"visit_type\" not like", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeIn(List<String> values) {
            addCriterion("\"visit_type\" in", values, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeNotIn(List<String> values) {
            addCriterion("\"visit_type\" not in", values, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeBetween(String value1, String value2) {
            addCriterion("\"visit_type\" between", value1, value2, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeNotBetween(String value1, String value2) {
            addCriterion("\"visit_type\" not between", value1, value2, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDatetimeIsNull() {
            addCriterion("\"visit_or_admission_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDatetimeIsNotNull() {
            addCriterion("\"visit_or_admission_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDatetimeEqualTo(Date value) {
            addCriterion("\"visit_or_admission_datetime\" =", value, "visitOrAdmissionDatetime");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDatetimeNotEqualTo(Date value) {
            addCriterion("\"visit_or_admission_datetime\" <>", value, "visitOrAdmissionDatetime");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDatetimeGreaterThan(Date value) {
            addCriterion("\"visit_or_admission_datetime\" >", value, "visitOrAdmissionDatetime");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"visit_or_admission_datetime\" >=", value, "visitOrAdmissionDatetime");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDatetimeLessThan(Date value) {
            addCriterion("\"visit_or_admission_datetime\" <", value, "visitOrAdmissionDatetime");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"visit_or_admission_datetime\" <=", value, "visitOrAdmissionDatetime");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDatetimeIn(List<Date> values) {
            addCriterion("\"visit_or_admission_datetime\" in", values, "visitOrAdmissionDatetime");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDatetimeNotIn(List<Date> values) {
            addCriterion("\"visit_or_admission_datetime\" not in", values, "visitOrAdmissionDatetime");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"visit_or_admission_datetime\" between", value1, value2, "visitOrAdmissionDatetime");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"visit_or_admission_datetime\" not between", value1, value2, "visitOrAdmissionDatetime");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDeptIsNull() {
            addCriterion("\"visit_or_admission_dept\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDeptIsNotNull() {
            addCriterion("\"visit_or_admission_dept\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDeptEqualTo(String value) {
            addCriterion("\"visit_or_admission_dept\" =", value, "visitOrAdmissionDept");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDeptNotEqualTo(String value) {
            addCriterion("\"visit_or_admission_dept\" <>", value, "visitOrAdmissionDept");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDeptGreaterThan(String value) {
            addCriterion("\"visit_or_admission_dept\" >", value, "visitOrAdmissionDept");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDeptGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_or_admission_dept\" >=", value, "visitOrAdmissionDept");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDeptLessThan(String value) {
            addCriterion("\"visit_or_admission_dept\" <", value, "visitOrAdmissionDept");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDeptLessThanOrEqualTo(String value) {
            addCriterion("\"visit_or_admission_dept\" <=", value, "visitOrAdmissionDept");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDeptLike(String value) {
            addCriterion("\"visit_or_admission_dept\" like", value, "visitOrAdmissionDept");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDeptNotLike(String value) {
            addCriterion("\"visit_or_admission_dept\" not like", value, "visitOrAdmissionDept");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDeptIn(List<String> values) {
            addCriterion("\"visit_or_admission_dept\" in", values, "visitOrAdmissionDept");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDeptNotIn(List<String> values) {
            addCriterion("\"visit_or_admission_dept\" not in", values, "visitOrAdmissionDept");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDeptBetween(String value1, String value2) {
            addCriterion("\"visit_or_admission_dept\" between", value1, value2, "visitOrAdmissionDept");
            return (Criteria) this;
        }

        public Criteria andVisitOrAdmissionDeptNotBetween(String value1, String value2) {
            addCriterion("\"visit_or_admission_dept\" not between", value1, value2, "visitOrAdmissionDept");
            return (Criteria) this;
        }

        public Criteria andVisitOrAttendingDoctorIsNull() {
            addCriterion("\"visit_or_attending_doctor\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitOrAttendingDoctorIsNotNull() {
            addCriterion("\"visit_or_attending_doctor\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitOrAttendingDoctorEqualTo(String value) {
            addCriterion("\"visit_or_attending_doctor\" =", value, "visitOrAttendingDoctor");
            return (Criteria) this;
        }

        public Criteria andVisitOrAttendingDoctorNotEqualTo(String value) {
            addCriterion("\"visit_or_attending_doctor\" <>", value, "visitOrAttendingDoctor");
            return (Criteria) this;
        }

        public Criteria andVisitOrAttendingDoctorGreaterThan(String value) {
            addCriterion("\"visit_or_attending_doctor\" >", value, "visitOrAttendingDoctor");
            return (Criteria) this;
        }

        public Criteria andVisitOrAttendingDoctorGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_or_attending_doctor\" >=", value, "visitOrAttendingDoctor");
            return (Criteria) this;
        }

        public Criteria andVisitOrAttendingDoctorLessThan(String value) {
            addCriterion("\"visit_or_attending_doctor\" <", value, "visitOrAttendingDoctor");
            return (Criteria) this;
        }

        public Criteria andVisitOrAttendingDoctorLessThanOrEqualTo(String value) {
            addCriterion("\"visit_or_attending_doctor\" <=", value, "visitOrAttendingDoctor");
            return (Criteria) this;
        }

        public Criteria andVisitOrAttendingDoctorLike(String value) {
            addCriterion("\"visit_or_attending_doctor\" like", value, "visitOrAttendingDoctor");
            return (Criteria) this;
        }

        public Criteria andVisitOrAttendingDoctorNotLike(String value) {
            addCriterion("\"visit_or_attending_doctor\" not like", value, "visitOrAttendingDoctor");
            return (Criteria) this;
        }

        public Criteria andVisitOrAttendingDoctorIn(List<String> values) {
            addCriterion("\"visit_or_attending_doctor\" in", values, "visitOrAttendingDoctor");
            return (Criteria) this;
        }

        public Criteria andVisitOrAttendingDoctorNotIn(List<String> values) {
            addCriterion("\"visit_or_attending_doctor\" not in", values, "visitOrAttendingDoctor");
            return (Criteria) this;
        }

        public Criteria andVisitOrAttendingDoctorBetween(String value1, String value2) {
            addCriterion("\"visit_or_attending_doctor\" between", value1, value2, "visitOrAttendingDoctor");
            return (Criteria) this;
        }

        public Criteria andVisitOrAttendingDoctorNotBetween(String value1, String value2) {
            addCriterion("\"visit_or_attending_doctor\" not between", value1, value2, "visitOrAttendingDoctor");
            return (Criteria) this;
        }

        public Criteria andVisitAgeIsNull() {
            addCriterion("\"visit_age\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitAgeIsNotNull() {
            addCriterion("\"visit_age\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitAgeEqualTo(Integer value) {
            addCriterion("\"visit_age\" =", value, "visitAge");
            return (Criteria) this;
        }

        public Criteria andVisitAgeNotEqualTo(Integer value) {
            addCriterion("\"visit_age\" <>", value, "visitAge");
            return (Criteria) this;
        }

        public Criteria andVisitAgeGreaterThan(Integer value) {
            addCriterion("\"visit_age\" >", value, "visitAge");
            return (Criteria) this;
        }

        public Criteria andVisitAgeGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"visit_age\" >=", value, "visitAge");
            return (Criteria) this;
        }

        public Criteria andVisitAgeLessThan(Integer value) {
            addCriterion("\"visit_age\" <", value, "visitAge");
            return (Criteria) this;
        }

        public Criteria andVisitAgeLessThanOrEqualTo(Integer value) {
            addCriterion("\"visit_age\" <=", value, "visitAge");
            return (Criteria) this;
        }

        public Criteria andVisitAgeIn(List<Integer> values) {
            addCriterion("\"visit_age\" in", values, "visitAge");
            return (Criteria) this;
        }

        public Criteria andVisitAgeNotIn(List<Integer> values) {
            addCriterion("\"visit_age\" not in", values, "visitAge");
            return (Criteria) this;
        }

        public Criteria andVisitAgeBetween(Integer value1, Integer value2) {
            addCriterion("\"visit_age\" between", value1, value2, "visitAge");
            return (Criteria) this;
        }

        public Criteria andVisitAgeNotBetween(Integer value1, Integer value2) {
            addCriterion("\"visit_age\" not between", value1, value2, "visitAge");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayIsNull() {
            addCriterion("\"admission_way\" is null");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayIsNotNull() {
            addCriterion("\"admission_way\" is not null");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayEqualTo(String value) {
            addCriterion("\"admission_way\" =", value, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayNotEqualTo(String value) {
            addCriterion("\"admission_way\" <>", value, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayGreaterThan(String value) {
            addCriterion("\"admission_way\" >", value, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayGreaterThanOrEqualTo(String value) {
            addCriterion("\"admission_way\" >=", value, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayLessThan(String value) {
            addCriterion("\"admission_way\" <", value, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayLessThanOrEqualTo(String value) {
            addCriterion("\"admission_way\" <=", value, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayLike(String value) {
            addCriterion("\"admission_way\" like", value, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayNotLike(String value) {
            addCriterion("\"admission_way\" not like", value, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayIn(List<String> values) {
            addCriterion("\"admission_way\" in", values, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayNotIn(List<String> values) {
            addCriterion("\"admission_way\" not in", values, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayBetween(String value1, String value2) {
            addCriterion("\"admission_way\" between", value1, value2, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayNotBetween(String value1, String value2) {
            addCriterion("\"admission_way\" not between", value1, value2, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeIsNull() {
            addCriterion("\"discharge_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeIsNotNull() {
            addCriterion("\"discharge_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeEqualTo(Date value) {
            addCriterion("\"discharge_datetime\" =", value, "dischargeDatetime");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeNotEqualTo(Date value) {
            addCriterion("\"discharge_datetime\" <>", value, "dischargeDatetime");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeGreaterThan(Date value) {
            addCriterion("\"discharge_datetime\" >", value, "dischargeDatetime");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"discharge_datetime\" >=", value, "dischargeDatetime");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeLessThan(Date value) {
            addCriterion("\"discharge_datetime\" <", value, "dischargeDatetime");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"discharge_datetime\" <=", value, "dischargeDatetime");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeIn(List<Date> values) {
            addCriterion("\"discharge_datetime\" in", values, "dischargeDatetime");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeNotIn(List<Date> values) {
            addCriterion("\"discharge_datetime\" not in", values, "dischargeDatetime");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"discharge_datetime\" between", value1, value2, "dischargeDatetime");
            return (Criteria) this;
        }

        public Criteria andDischargeDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"discharge_datetime\" not between", value1, value2, "dischargeDatetime");
            return (Criteria) this;
        }

        public Criteria andDischargeDeptIsNull() {
            addCriterion("\"discharge_dept\" is null");
            return (Criteria) this;
        }

        public Criteria andDischargeDeptIsNotNull() {
            addCriterion("\"discharge_dept\" is not null");
            return (Criteria) this;
        }

        public Criteria andDischargeDeptEqualTo(String value) {
            addCriterion("\"discharge_dept\" =", value, "dischargeDept");
            return (Criteria) this;
        }

        public Criteria andDischargeDeptNotEqualTo(String value) {
            addCriterion("\"discharge_dept\" <>", value, "dischargeDept");
            return (Criteria) this;
        }

        public Criteria andDischargeDeptGreaterThan(String value) {
            addCriterion("\"discharge_dept\" >", value, "dischargeDept");
            return (Criteria) this;
        }

        public Criteria andDischargeDeptGreaterThanOrEqualTo(String value) {
            addCriterion("\"discharge_dept\" >=", value, "dischargeDept");
            return (Criteria) this;
        }

        public Criteria andDischargeDeptLessThan(String value) {
            addCriterion("\"discharge_dept\" <", value, "dischargeDept");
            return (Criteria) this;
        }

        public Criteria andDischargeDeptLessThanOrEqualTo(String value) {
            addCriterion("\"discharge_dept\" <=", value, "dischargeDept");
            return (Criteria) this;
        }

        public Criteria andDischargeDeptLike(String value) {
            addCriterion("\"discharge_dept\" like", value, "dischargeDept");
            return (Criteria) this;
        }

        public Criteria andDischargeDeptNotLike(String value) {
            addCriterion("\"discharge_dept\" not like", value, "dischargeDept");
            return (Criteria) this;
        }

        public Criteria andDischargeDeptIn(List<String> values) {
            addCriterion("\"discharge_dept\" in", values, "dischargeDept");
            return (Criteria) this;
        }

        public Criteria andDischargeDeptNotIn(List<String> values) {
            addCriterion("\"discharge_dept\" not in", values, "dischargeDept");
            return (Criteria) this;
        }

        public Criteria andDischargeDeptBetween(String value1, String value2) {
            addCriterion("\"discharge_dept\" between", value1, value2, "dischargeDept");
            return (Criteria) this;
        }

        public Criteria andDischargeDeptNotBetween(String value1, String value2) {
            addCriterion("\"discharge_dept\" not between", value1, value2, "dischargeDept");
            return (Criteria) this;
        }

        public Criteria andDischargeWayIsNull() {
            addCriterion("\"discharge_way\" is null");
            return (Criteria) this;
        }

        public Criteria andDischargeWayIsNotNull() {
            addCriterion("\"discharge_way\" is not null");
            return (Criteria) this;
        }

        public Criteria andDischargeWayEqualTo(String value) {
            addCriterion("\"discharge_way\" =", value, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andDischargeWayNotEqualTo(String value) {
            addCriterion("\"discharge_way\" <>", value, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andDischargeWayGreaterThan(String value) {
            addCriterion("\"discharge_way\" >", value, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andDischargeWayGreaterThanOrEqualTo(String value) {
            addCriterion("\"discharge_way\" >=", value, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andDischargeWayLessThan(String value) {
            addCriterion("\"discharge_way\" <", value, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andDischargeWayLessThanOrEqualTo(String value) {
            addCriterion("\"discharge_way\" <=", value, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andDischargeWayLike(String value) {
            addCriterion("\"discharge_way\" like", value, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andDischargeWayNotLike(String value) {
            addCriterion("\"discharge_way\" not like", value, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andDischargeWayIn(List<String> values) {
            addCriterion("\"discharge_way\" in", values, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andDischargeWayNotIn(List<String> values) {
            addCriterion("\"discharge_way\" not in", values, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andDischargeWayBetween(String value1, String value2) {
            addCriterion("\"discharge_way\" between", value1, value2, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andDischargeWayNotBetween(String value1, String value2) {
            addCriterion("\"discharge_way\" not between", value1, value2, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andInDaysIsNull() {
            addCriterion("\"in_days\" is null");
            return (Criteria) this;
        }

        public Criteria andInDaysIsNotNull() {
            addCriterion("\"in_days\" is not null");
            return (Criteria) this;
        }

        public Criteria andInDaysEqualTo(Integer value) {
            addCriterion("\"in_days\" =", value, "inDays");
            return (Criteria) this;
        }

        public Criteria andInDaysNotEqualTo(Integer value) {
            addCriterion("\"in_days\" <>", value, "inDays");
            return (Criteria) this;
        }

        public Criteria andInDaysGreaterThan(Integer value) {
            addCriterion("\"in_days\" >", value, "inDays");
            return (Criteria) this;
        }

        public Criteria andInDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"in_days\" >=", value, "inDays");
            return (Criteria) this;
        }

        public Criteria andInDaysLessThan(Integer value) {
            addCriterion("\"in_days\" <", value, "inDays");
            return (Criteria) this;
        }

        public Criteria andInDaysLessThanOrEqualTo(Integer value) {
            addCriterion("\"in_days\" <=", value, "inDays");
            return (Criteria) this;
        }

        public Criteria andInDaysIn(List<Integer> values) {
            addCriterion("\"in_days\" in", values, "inDays");
            return (Criteria) this;
        }

        public Criteria andInDaysNotIn(List<Integer> values) {
            addCriterion("\"in_days\" not in", values, "inDays");
            return (Criteria) this;
        }

        public Criteria andInDaysBetween(Integer value1, Integer value2) {
            addCriterion("\"in_days\" between", value1, value2, "inDays");
            return (Criteria) this;
        }

        public Criteria andInDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("\"in_days\" not between", value1, value2, "inDays");
            return (Criteria) this;
        }

        public Criteria andTotalCostsIsNull() {
            addCriterion("\"total_costs\" is null");
            return (Criteria) this;
        }

        public Criteria andTotalCostsIsNotNull() {
            addCriterion("\"total_costs\" is not null");
            return (Criteria) this;
        }

        public Criteria andTotalCostsEqualTo(Double value) {
            addCriterion("\"total_costs\" =", value, "totalCosts");
            return (Criteria) this;
        }

        public Criteria andTotalCostsNotEqualTo(Double value) {
            addCriterion("\"total_costs\" <>", value, "totalCosts");
            return (Criteria) this;
        }

        public Criteria andTotalCostsGreaterThan(Double value) {
            addCriterion("\"total_costs\" >", value, "totalCosts");
            return (Criteria) this;
        }

        public Criteria andTotalCostsGreaterThanOrEqualTo(Double value) {
            addCriterion("\"total_costs\" >=", value, "totalCosts");
            return (Criteria) this;
        }

        public Criteria andTotalCostsLessThan(Double value) {
            addCriterion("\"total_costs\" <", value, "totalCosts");
            return (Criteria) this;
        }

        public Criteria andTotalCostsLessThanOrEqualTo(Double value) {
            addCriterion("\"total_costs\" <=", value, "totalCosts");
            return (Criteria) this;
        }

        public Criteria andTotalCostsIn(List<Double> values) {
            addCriterion("\"total_costs\" in", values, "totalCosts");
            return (Criteria) this;
        }

        public Criteria andTotalCostsNotIn(List<Double> values) {
            addCriterion("\"total_costs\" not in", values, "totalCosts");
            return (Criteria) this;
        }

        public Criteria andTotalCostsBetween(Double value1, Double value2) {
            addCriterion("\"total_costs\" between", value1, value2, "totalCosts");
            return (Criteria) this;
        }

        public Criteria andTotalCostsNotBetween(Double value1, Double value2) {
            addCriterion("\"total_costs\" not between", value1, value2, "totalCosts");
            return (Criteria) this;
        }

        public Criteria andSelfPayIsNull() {
            addCriterion("\"self_pay\" is null");
            return (Criteria) this;
        }

        public Criteria andSelfPayIsNotNull() {
            addCriterion("\"self_pay\" is not null");
            return (Criteria) this;
        }

        public Criteria andSelfPayEqualTo(Double value) {
            addCriterion("\"self_pay\" =", value, "selfPay");
            return (Criteria) this;
        }

        public Criteria andSelfPayNotEqualTo(Double value) {
            addCriterion("\"self_pay\" <>", value, "selfPay");
            return (Criteria) this;
        }

        public Criteria andSelfPayGreaterThan(Double value) {
            addCriterion("\"self_pay\" >", value, "selfPay");
            return (Criteria) this;
        }

        public Criteria andSelfPayGreaterThanOrEqualTo(Double value) {
            addCriterion("\"self_pay\" >=", value, "selfPay");
            return (Criteria) this;
        }

        public Criteria andSelfPayLessThan(Double value) {
            addCriterion("\"self_pay\" <", value, "selfPay");
            return (Criteria) this;
        }

        public Criteria andSelfPayLessThanOrEqualTo(Double value) {
            addCriterion("\"self_pay\" <=", value, "selfPay");
            return (Criteria) this;
        }

        public Criteria andSelfPayIn(List<Double> values) {
            addCriterion("\"self_pay\" in", values, "selfPay");
            return (Criteria) this;
        }

        public Criteria andSelfPayNotIn(List<Double> values) {
            addCriterion("\"self_pay\" not in", values, "selfPay");
            return (Criteria) this;
        }

        public Criteria andSelfPayBetween(Double value1, Double value2) {
            addCriterion("\"self_pay\" between", value1, value2, "selfPay");
            return (Criteria) this;
        }

        public Criteria andSelfPayNotBetween(Double value1, Double value2) {
            addCriterion("\"self_pay\" not between", value1, value2, "selfPay");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}