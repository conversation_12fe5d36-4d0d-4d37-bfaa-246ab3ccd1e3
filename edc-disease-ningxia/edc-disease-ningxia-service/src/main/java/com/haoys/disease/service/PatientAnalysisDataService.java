package com.haoys.disease.service;

import com.haoys.disease.domain.param.PatientAnalysisDatasetParam;
import com.haoys.disease.domain.param.PatientAnalysisRecordParam;
import com.haoys.disease.domain.wrapper.PatientBaseWrapper;
import com.haoys.disease.model.PatientAnalysisDataset;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;

import java.util.List;

public interface PatientAnalysisDataService {


    CustomResult savePatientAnalysisDataSet(PatientAnalysisDatasetParam patientAnalysisDatasetParam);

    CustomResult savePatientAnalysisRecord(PatientAnalysisRecordParam patientAnalysisRecordParam);

    CustomResult removePatientAnalysisDataSet(String dataSetId);

    /**
     * 查询数据集分页列表
     * @param code
     * @param enabled
     * @param pageNum
     * @param pageSize
     * @return
     */
    CommonPage<PatientAnalysisDataset> getPatientAnalysisDataSetForPage(String code, String enabled, Integer pageNum, Integer pageSize);

    CustomResult modifyPatientAnalysisDataSet(String dataSetId, String enabled);

    List<PatientBaseWrapper> getPatientDataSetBaseInfoForPage(String batchCode, List<String> patientIds);

    List<String> getPatientAnalysisRecordData(String batchCode);

    /**
     * 删除数据集中的数据项
     * @param recordId 数据项id的集合
     * @return
     */
    CommonResult removePatientAnalysisRecord(List<String> recordId);
    
    PatientAnalysisDataset selectByPrimaryKey(String dataSetId);
}
