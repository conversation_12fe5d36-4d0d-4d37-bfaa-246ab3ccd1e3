package com.haoys.disease.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.haoys.disease.ConstantsModel;
import com.haoys.disease.domain.emums.PatientSexEnum;
import com.haoys.disease.domain.emums.PatientVisitTypeEnum;
import com.haoys.disease.domain.param.CountPatientToAgeParam;
import com.haoys.disease.domain.param.CountPatientToAgeWrapperParam;
import com.haoys.disease.domain.vo.CountPatientToAgeVo;
import com.haoys.disease.domain.vo.CountPatientToAreaVo;
import com.haoys.disease.domain.vo.CountPatientToYearVo;
import com.haoys.disease.domain.vo.DataOverViewVo;
import com.haoys.disease.service.DataOverviewService;
import com.haoys.disease.service.PatientRecordService;
import com.haoys.disease.service.PatientVisitInfoService;
import com.haoys.user.common.api.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@DS("disease_nxzbk")
@Service
public class DataOverviewServiceImpl implements DataOverviewService {

    @Autowired
    private PatientRecordService patientRecordService;
    @Autowired
    private PatientVisitInfoService patientVisitInfoService;

    @Override
    public CommonResult<DataOverViewVo> totalPatient() {
        DataOverViewVo overViewVo = new DataOverViewVo();
        // 统计入库总病例人数
        long countPatient = patientRecordService.countPatient(null);
        overViewVo.setCountPatient(countPatient);
        // 统计门诊就诊人数
        long countOutpatient = patientVisitInfoService.countPatient(PatientVisitTypeEnum.V100001.getCode());
        overViewVo.setCountOutpatient(countOutpatient);
        // 统计住院就诊人数
        long countVisits = patientVisitInfoService.countPatient(PatientVisitTypeEnum.V100002.getCode());
        overViewVo.setCountVisits(countVisits);
        // 统计患者性别信息
        DataOverViewVo.PatientSexCount sexCount = getPatientSexCount(countPatient);
        overViewVo.setPatientSexCount(sexCount);
        // 统计季节患者
        return CommonResult.success(overViewVo);
    }

    /**
     * 统计患者性别信息
     * @param countPatient
     * @return
     */
    private DataOverViewVo.PatientSexCount getPatientSexCount(long countPatient) {
        // 统计患者性别信息
        DataOverViewVo.PatientSexCount sexCount = new DataOverViewVo.PatientSexCount();
        // 1.获取男性的数量
        long countManPatient = patientRecordService.countPatient(PatientSexEnum.MAN.getCode());
        sexCount.setCountManPatient(countManPatient);
        // 2.获取女性的数量 总数减去男性的数量
        long countWomanPatient= countPatient -countManPatient;
        sexCount.setCountWomanPatient(countWomanPatient);
        // 3.男性的占比
        BigDecimal manPatientProportion = BigDecimal.ZERO;
        // 4.女性的占比
        BigDecimal womanPatientProportion =  BigDecimal.ZERO;
        if (countPatient >0 && countManPatient>0){
            // 男性患者数量乘100除以总数，求的占比
            manPatientProportion= new BigDecimal(countManPatient*100).divide(new BigDecimal(countPatient), 2, RoundingMode.HALF_UP);
        }
        if (countPatient >0){

            // 100-男性的占比，为女性的占比
            womanPatientProportion= new BigDecimal(100).subtract(manPatientProportion);
        }
        sexCount.setManPatientProportion(manPatientProportion);
        sexCount.setWomanPatientProportion(womanPatientProportion);
        return sexCount;
    }

    /**
     * 季节性患者就诊人数
     */
    public CommonResult<DataOverViewVo.QuarterPatientCount> countQuarterPatient(){
        DataOverViewVo.QuarterPatientCount quarterPatientCount = new DataOverViewVo.QuarterPatientCount();
        // 以阳历3～5月为春季,6～8月为夏季,9～11月为秋季,12月～来年2月为冬季
        return CommonResult.success(quarterPatientCount);
    }
    
    @Override
    public CommonResult<List<Map<String, Object>>> getPatientAgeDistributionStatTable() {
        return patientRecordService.getPatientAgeDistributionStatTable();
    }
    
    @Override
    public CommonResult<List<Map<String, Object>>> getPatientMedicalTreatmentStatTable(String fieldName) {
        return patientRecordService.getPatientMedicalTreatmentStatTable(fieldName);
    }
    
    @Override
    public CommonResult<Map<String, Object>> getPatientUseDrugStatTable(String fieldNames) {
        /*Map<String, Object> dataMap = new HashMap<>();
        String[] fieldNamesArr = fieldNames.split(",");
        for (String fieldName : fieldNamesArr) {
            CommonResult<List<Map<String, Object>>> patientMedicalTreatmentStatTable = patientRecordService.getPatientMedicalTreatmentStatTable(fieldName);
            List<Map<String, Object>> dataList = patientMedicalTreatmentStatTable.getData();
            long count = dataList.stream().collect(Collectors.summingLong((m -> (long) m.get("count"))));
            dataMap.put(fieldName, String.valueOf(count));
        }*/
        Map<String, Object> dataMap = patientRecordService.getPatientUseDrugStatTable();
        return CommonResult.success(dataMap);
    }
    
    
    /**
     * 最近一年患者趋势图
     * @param year 年度
     * @return
     */
    public CountPatientToYearVo countPatientToYear(String year){
        CountPatientToYearVo vo = new CountPatientToYearVo();
        // 就诊人次
        List<Long> visitData = new ArrayList<>(12);
        Map<Integer, Map<String, Long>> map0 = patientVisitInfoService.countPgSqlVisitInfoByYear(year,null);
        extracted(visitData, map0);
        vo.setVisitData(visitData);
        // 门诊人次
        List<Long> inHospData = new ArrayList<>(12);
        Map<Integer, Map<String, Long>> map1 = patientVisitInfoService.countPgSqlVisitInfoByYear(year,ConstantsModel.PATIENT_VISIT_CODE_02);
        extracted(inHospData, map1);
        vo.setInHospData(inHospData);
        // 患者人数
        List<Long> patientData = new ArrayList<>(12);
        Map<Integer, Map<String, Long>> map3 = patientVisitInfoService.countPgSqlPatientByYear(year);
        extracted(patientData, map3);
        vo.setPatientData(patientData);
        return vo;
    }

    /**
     * 按照一年12月组装数据
     * @param data
     * @param map
     */
    private static void extracted(List<Long> data, Map<Integer, Map<String, Long>> map) {
        for (int i = 1; i <13 ; i++) {
            Map<String, Long> map1 = map.get(i);
            if (map1==null){
                data.add(0L);
            }else {
                data.add(map1.get("num"));
            }
        }
    }


    /**
     * 患者年龄段分布图
     * @param countPatientToAgeWrapperParam 年龄分布集合
     * @return
     */
    @Override
    public CommonResult<List<CountPatientToAgeParam>> countPatientToAge(CountPatientToAgeWrapperParam countPatientToAgeWrapperParam) {
        List<CountPatientToAgeParam> params = countPatientToAgeWrapperParam.getParams();
        if (CollectionUtil.isNotEmpty(params)){
            List<CountPatientToAgeVo> list = patientRecordService.countPgSqlPatientToAge();
            if (CollectionUtil.isNotEmpty(list)){
                Long total = 0L;
                for (CountPatientToAgeVo countPatientToAgeVo : list) {
                    total+=countPatientToAgeVo.getNum();
                }

                for (CountPatientToAgeVo vo : list) {
                    if (vo.getAge()!=null){
                        int age = vo.getAge();
                        for (CountPatientToAgeParam param : params) {
                            if (age>=param.getStartAge() && age<= param.getEndAge()){
                                param.setNum(param.getNum()+vo.getNum());
                                break;
                            }
                        }
                    }
                }
                if (total>0){
                    Long finalTotal = total;
                    params.forEach(p->p.setProportion(new BigDecimal(p.getNum()*100).divide(new BigDecimal(finalTotal), 2, RoundingMode.HALF_UP)));
                }
            }
            return CommonResult.success(params);
        }
        return CommonResult.success(new ArrayList<>());
    }


    @Override
    public CommonResult<List<CountPatientToAreaVo>> countPatientToAre() {
        List<CountPatientToAreaVo> list = patientRecordService.countPgSqlPatientToAre();
        return CommonResult.success(list);
    }


}
