package com.haoys.disease.mapper;

import com.haoys.disease.model.NonDrugOrder;
import com.haoys.disease.model.NonDrugOrderExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface NonDrugOrderMapper {
    long countByExample(NonDrugOrderExample example);

    int deleteByExample(NonDrugOrderExample example);

    int insert(NonDrugOrder record);

    int insertSelective(NonDrugOrder record);

    List<NonDrugOrder> selectByExample(NonDrugOrderExample example);

    int updateByExampleSelective(@Param("record") NonDrugOrder record, @Param("example") NonDrugOrderExample example);

    int updateByExample(@Param("record") NonDrugOrder record, @Param("example") NonDrugOrderExample example);
}