package com.haoys.disease.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class NonDrugOrderExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public NonDrugOrderExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andPkidIsNull() {
            addCriterion("\"pkid\" is null");
            return (Criteria) this;
        }

        public Criteria andPkidIsNotNull() {
            addCriterion("\"pkid\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkidEqualTo(String value) {
            addCriterion("\"pkid\" =", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotEqualTo(String value) {
            addCriterion("\"pkid\" <>", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThan(String value) {
            addCriterion("\"pkid\" >", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThanOrEqualTo(String value) {
            addCriterion("\"pkid\" >=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThan(String value) {
            addCriterion("\"pkid\" <", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThanOrEqualTo(String value) {
            addCriterion("\"pkid\" <=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLike(String value) {
            addCriterion("\"pkid\" like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotLike(String value) {
            addCriterion("\"pkid\" not like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidIn(List<String> values) {
            addCriterion("\"pkid\" in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotIn(List<String> values) {
            addCriterion("\"pkid\" not in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidBetween(String value1, String value2) {
            addCriterion("\"pkid\" between", value1, value2, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotBetween(String value1, String value2) {
            addCriterion("\"pkid\" not between", value1, value2, "pkid");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDatetimeIsNull() {
            addCriterion("\"order_create_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDatetimeIsNotNull() {
            addCriterion("\"order_create_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDatetimeEqualTo(Date value) {
            addCriterion("\"order_create_datetime\" =", value, "orderCreateDatetime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDatetimeNotEqualTo(Date value) {
            addCriterion("\"order_create_datetime\" <>", value, "orderCreateDatetime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDatetimeGreaterThan(Date value) {
            addCriterion("\"order_create_datetime\" >", value, "orderCreateDatetime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"order_create_datetime\" >=", value, "orderCreateDatetime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDatetimeLessThan(Date value) {
            addCriterion("\"order_create_datetime\" <", value, "orderCreateDatetime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"order_create_datetime\" <=", value, "orderCreateDatetime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDatetimeIn(List<Date> values) {
            addCriterion("\"order_create_datetime\" in", values, "orderCreateDatetime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDatetimeNotIn(List<Date> values) {
            addCriterion("\"order_create_datetime\" not in", values, "orderCreateDatetime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"order_create_datetime\" between", value1, value2, "orderCreateDatetime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"order_create_datetime\" not between", value1, value2, "orderCreateDatetime");
            return (Criteria) this;
        }

        public Criteria andOrderStopDatetimeIsNull() {
            addCriterion("\"order_stop_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderStopDatetimeIsNotNull() {
            addCriterion("\"order_stop_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStopDatetimeEqualTo(Date value) {
            addCriterion("\"order_stop_datetime\" =", value, "orderStopDatetime");
            return (Criteria) this;
        }

        public Criteria andOrderStopDatetimeNotEqualTo(Date value) {
            addCriterion("\"order_stop_datetime\" <>", value, "orderStopDatetime");
            return (Criteria) this;
        }

        public Criteria andOrderStopDatetimeGreaterThan(Date value) {
            addCriterion("\"order_stop_datetime\" >", value, "orderStopDatetime");
            return (Criteria) this;
        }

        public Criteria andOrderStopDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"order_stop_datetime\" >=", value, "orderStopDatetime");
            return (Criteria) this;
        }

        public Criteria andOrderStopDatetimeLessThan(Date value) {
            addCriterion("\"order_stop_datetime\" <", value, "orderStopDatetime");
            return (Criteria) this;
        }

        public Criteria andOrderStopDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"order_stop_datetime\" <=", value, "orderStopDatetime");
            return (Criteria) this;
        }

        public Criteria andOrderStopDatetimeIn(List<Date> values) {
            addCriterion("\"order_stop_datetime\" in", values, "orderStopDatetime");
            return (Criteria) this;
        }

        public Criteria andOrderStopDatetimeNotIn(List<Date> values) {
            addCriterion("\"order_stop_datetime\" not in", values, "orderStopDatetime");
            return (Criteria) this;
        }

        public Criteria andOrderStopDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"order_stop_datetime\" between", value1, value2, "orderStopDatetime");
            return (Criteria) this;
        }

        public Criteria andOrderStopDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"order_stop_datetime\" not between", value1, value2, "orderStopDatetime");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNull() {
            addCriterion("\"order_type\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNotNull() {
            addCriterion("\"order_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeEqualTo(String value) {
            addCriterion("\"order_type\" =", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotEqualTo(String value) {
            addCriterion("\"order_type\" <>", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThan(String value) {
            addCriterion("\"order_type\" >", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThanOrEqualTo(String value) {
            addCriterion("\"order_type\" >=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThan(String value) {
            addCriterion("\"order_type\" <", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThanOrEqualTo(String value) {
            addCriterion("\"order_type\" <=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLike(String value) {
            addCriterion("\"order_type\" like", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotLike(String value) {
            addCriterion("\"order_type\" not like", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIn(List<String> values) {
            addCriterion("\"order_type\" in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotIn(List<String> values) {
            addCriterion("\"order_type\" not in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeBetween(String value1, String value2) {
            addCriterion("\"order_type\" between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotBetween(String value1, String value2) {
            addCriterion("\"order_type\" not between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTextIsNull() {
            addCriterion("\"order_text\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderTextIsNotNull() {
            addCriterion("\"order_text\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTextEqualTo(String value) {
            addCriterion("\"order_text\" =", value, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTextNotEqualTo(String value) {
            addCriterion("\"order_text\" <>", value, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTextGreaterThan(String value) {
            addCriterion("\"order_text\" >", value, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTextGreaterThanOrEqualTo(String value) {
            addCriterion("\"order_text\" >=", value, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTextLessThan(String value) {
            addCriterion("\"order_text\" <", value, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTextLessThanOrEqualTo(String value) {
            addCriterion("\"order_text\" <=", value, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTextLike(String value) {
            addCriterion("\"order_text\" like", value, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTextNotLike(String value) {
            addCriterion("\"order_text\" not like", value, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTextIn(List<String> values) {
            addCriterion("\"order_text\" in", values, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTextNotIn(List<String> values) {
            addCriterion("\"order_text\" not in", values, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTextBetween(String value1, String value2) {
            addCriterion("\"order_text\" between", value1, value2, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTextNotBetween(String value1, String value2) {
            addCriterion("\"order_text\" not between", value1, value2, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderNumIsNull() {
            addCriterion("\"order_num\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderNumIsNotNull() {
            addCriterion("\"order_num\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNumEqualTo(String value) {
            addCriterion("\"order_num\" =", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotEqualTo(String value) {
            addCriterion("\"order_num\" <>", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumGreaterThan(String value) {
            addCriterion("\"order_num\" >", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumGreaterThanOrEqualTo(String value) {
            addCriterion("\"order_num\" >=", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumLessThan(String value) {
            addCriterion("\"order_num\" <", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumLessThanOrEqualTo(String value) {
            addCriterion("\"order_num\" <=", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumLike(String value) {
            addCriterion("\"order_num\" like", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotLike(String value) {
            addCriterion("\"order_num\" not like", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumIn(List<String> values) {
            addCriterion("\"order_num\" in", values, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotIn(List<String> values) {
            addCriterion("\"order_num\" not in", values, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumBetween(String value1, String value2) {
            addCriterion("\"order_num\" between", value1, value2, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotBetween(String value1, String value2) {
            addCriterion("\"order_num\" not between", value1, value2, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumUnitsIsNull() {
            addCriterion("\"order_num_units\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderNumUnitsIsNotNull() {
            addCriterion("\"order_num_units\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNumUnitsEqualTo(String value) {
            addCriterion("\"order_num_units\" =", value, "orderNumUnits");
            return (Criteria) this;
        }

        public Criteria andOrderNumUnitsNotEqualTo(String value) {
            addCriterion("\"order_num_units\" <>", value, "orderNumUnits");
            return (Criteria) this;
        }

        public Criteria andOrderNumUnitsGreaterThan(String value) {
            addCriterion("\"order_num_units\" >", value, "orderNumUnits");
            return (Criteria) this;
        }

        public Criteria andOrderNumUnitsGreaterThanOrEqualTo(String value) {
            addCriterion("\"order_num_units\" >=", value, "orderNumUnits");
            return (Criteria) this;
        }

        public Criteria andOrderNumUnitsLessThan(String value) {
            addCriterion("\"order_num_units\" <", value, "orderNumUnits");
            return (Criteria) this;
        }

        public Criteria andOrderNumUnitsLessThanOrEqualTo(String value) {
            addCriterion("\"order_num_units\" <=", value, "orderNumUnits");
            return (Criteria) this;
        }

        public Criteria andOrderNumUnitsLike(String value) {
            addCriterion("\"order_num_units\" like", value, "orderNumUnits");
            return (Criteria) this;
        }

        public Criteria andOrderNumUnitsNotLike(String value) {
            addCriterion("\"order_num_units\" not like", value, "orderNumUnits");
            return (Criteria) this;
        }

        public Criteria andOrderNumUnitsIn(List<String> values) {
            addCriterion("\"order_num_units\" in", values, "orderNumUnits");
            return (Criteria) this;
        }

        public Criteria andOrderNumUnitsNotIn(List<String> values) {
            addCriterion("\"order_num_units\" not in", values, "orderNumUnits");
            return (Criteria) this;
        }

        public Criteria andOrderNumUnitsBetween(String value1, String value2) {
            addCriterion("\"order_num_units\" between", value1, value2, "orderNumUnits");
            return (Criteria) this;
        }

        public Criteria andOrderNumUnitsNotBetween(String value1, String value2) {
            addCriterion("\"order_num_units\" not between", value1, value2, "orderNumUnits");
            return (Criteria) this;
        }

        public Criteria andOrderClassIsNull() {
            addCriterion("\"order_class\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderClassIsNotNull() {
            addCriterion("\"order_class\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderClassEqualTo(String value) {
            addCriterion("\"order_class\" =", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassNotEqualTo(String value) {
            addCriterion("\"order_class\" <>", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassGreaterThan(String value) {
            addCriterion("\"order_class\" >", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassGreaterThanOrEqualTo(String value) {
            addCriterion("\"order_class\" >=", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassLessThan(String value) {
            addCriterion("\"order_class\" <", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassLessThanOrEqualTo(String value) {
            addCriterion("\"order_class\" <=", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassLike(String value) {
            addCriterion("\"order_class\" like", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassNotLike(String value) {
            addCriterion("\"order_class\" not like", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassIn(List<String> values) {
            addCriterion("\"order_class\" in", values, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassNotIn(List<String> values) {
            addCriterion("\"order_class\" not in", values, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassBetween(String value1, String value2) {
            addCriterion("\"order_class\" between", value1, value2, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassNotBetween(String value1, String value2) {
            addCriterion("\"order_class\" not between", value1, value2, "orderClass");
            return (Criteria) this;
        }

        public Criteria andFrequencyIsNull() {
            addCriterion("\"frequency\" is null");
            return (Criteria) this;
        }

        public Criteria andFrequencyIsNotNull() {
            addCriterion("\"frequency\" is not null");
            return (Criteria) this;
        }

        public Criteria andFrequencyEqualTo(String value) {
            addCriterion("\"frequency\" =", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyNotEqualTo(String value) {
            addCriterion("\"frequency\" <>", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyGreaterThan(String value) {
            addCriterion("\"frequency\" >", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyGreaterThanOrEqualTo(String value) {
            addCriterion("\"frequency\" >=", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyLessThan(String value) {
            addCriterion("\"frequency\" <", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyLessThanOrEqualTo(String value) {
            addCriterion("\"frequency\" <=", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyLike(String value) {
            addCriterion("\"frequency\" like", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyNotLike(String value) {
            addCriterion("\"frequency\" not like", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyIn(List<String> values) {
            addCriterion("\"frequency\" in", values, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyNotIn(List<String> values) {
            addCriterion("\"frequency\" not in", values, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyBetween(String value1, String value2) {
            addCriterion("\"frequency\" between", value1, value2, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyNotBetween(String value1, String value2) {
            addCriterion("\"frequency\" not between", value1, value2, "frequency");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDeptIsNull() {
            addCriterion("\"order_create_dept\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDeptIsNotNull() {
            addCriterion("\"order_create_dept\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDeptEqualTo(String value) {
            addCriterion("\"order_create_dept\" =", value, "orderCreateDept");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDeptNotEqualTo(String value) {
            addCriterion("\"order_create_dept\" <>", value, "orderCreateDept");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDeptGreaterThan(String value) {
            addCriterion("\"order_create_dept\" >", value, "orderCreateDept");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDeptGreaterThanOrEqualTo(String value) {
            addCriterion("\"order_create_dept\" >=", value, "orderCreateDept");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDeptLessThan(String value) {
            addCriterion("\"order_create_dept\" <", value, "orderCreateDept");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDeptLessThanOrEqualTo(String value) {
            addCriterion("\"order_create_dept\" <=", value, "orderCreateDept");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDeptLike(String value) {
            addCriterion("\"order_create_dept\" like", value, "orderCreateDept");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDeptNotLike(String value) {
            addCriterion("\"order_create_dept\" not like", value, "orderCreateDept");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDeptIn(List<String> values) {
            addCriterion("\"order_create_dept\" in", values, "orderCreateDept");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDeptNotIn(List<String> values) {
            addCriterion("\"order_create_dept\" not in", values, "orderCreateDept");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDeptBetween(String value1, String value2) {
            addCriterion("\"order_create_dept\" between", value1, value2, "orderCreateDept");
            return (Criteria) this;
        }

        public Criteria andOrderCreateDeptNotBetween(String value1, String value2) {
            addCriterion("\"order_create_dept\" not between", value1, value2, "orderCreateDept");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}