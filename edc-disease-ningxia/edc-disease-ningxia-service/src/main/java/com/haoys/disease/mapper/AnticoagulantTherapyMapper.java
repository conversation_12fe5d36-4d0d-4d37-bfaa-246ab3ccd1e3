package com.haoys.disease.mapper;

import com.haoys.disease.model.AnticoagulantTherapy;
import com.haoys.disease.model.AnticoagulantTherapyExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AnticoagulantTherapyMapper {
    long countByExample(AnticoagulantTherapyExample example);

    int deleteByExample(AnticoagulantTherapyExample example);

    int insert(AnticoagulantTherapy record);

    int insertSelective(AnticoagulantTherapy record);

    List<AnticoagulantTherapy> selectByExample(AnticoagulantTherapyExample example);

    int updateByExampleSelective(@Param("record") AnticoagulantTherapy record, @Param("example") AnticoagulantTherapyExample example);

    int updateByExample(@Param("record") AnticoagulantTherapy record, @Param("example") AnticoagulantTherapyExample example);
}