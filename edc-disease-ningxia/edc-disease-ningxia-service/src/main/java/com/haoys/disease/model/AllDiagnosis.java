package com.haoys.disease.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

public class AllDiagnosis implements Serializable {
    @ApiModelProperty(value = "患者标识")
    private String patientSn;

    @ApiModelProperty(value = "就诊标识")
    private String visitSn;

    private String pkid;

    private String diagnosisDate;

    @ApiModelProperty(value = "诊断类型")
    private String diagnosisType;

    @ApiModelProperty(value = "诊断名称")
    private String diagnosisName;

    @ApiModelProperty(value = "诊断顺位")
    private Integer diagnosisOrderNo;

    private static final long serialVersionUID = 1L;

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }

    public String getVisitSn() {
        return visitSn;
    }

    public void setVisitSn(String visitSn) {
        this.visitSn = visitSn;
    }

    public String getPkid() {
        return pkid;
    }

    public void setPkid(String pkid) {
        this.pkid = pkid;
    }

    public String getDiagnosisDate() {
        return diagnosisDate;
    }

    public void setDiagnosisDate(String diagnosisDate) {
        this.diagnosisDate = diagnosisDate;
    }

    public String getDiagnosisType() {
        return diagnosisType;
    }

    public void setDiagnosisType(String diagnosisType) {
        this.diagnosisType = diagnosisType;
    }

    public String getDiagnosisName() {
        return diagnosisName;
    }

    public void setDiagnosisName(String diagnosisName) {
        this.diagnosisName = diagnosisName;
    }

    public Integer getDiagnosisOrderNo() {
        return diagnosisOrderNo;
    }

    public void setDiagnosisOrderNo(Integer diagnosisOrderNo) {
        this.diagnosisOrderNo = diagnosisOrderNo;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", patientSn=").append(patientSn);
        sb.append(", visitSn=").append(visitSn);
        sb.append(", pkid=").append(pkid);
        sb.append(", diagnosisDate=").append(diagnosisDate);
        sb.append(", diagnosisType=").append(diagnosisType);
        sb.append(", diagnosisName=").append(diagnosisName);
        sb.append(", diagnosisOrderNo=").append(diagnosisOrderNo);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}