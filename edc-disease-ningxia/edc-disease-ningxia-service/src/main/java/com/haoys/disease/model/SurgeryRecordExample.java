package com.haoys.disease.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SurgeryRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public SurgeryRecordExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andPkidIsNull() {
            addCriterion("\"pkid\" is null");
            return (Criteria) this;
        }

        public Criteria andPkidIsNotNull() {
            addCriterion("\"pkid\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkidEqualTo(String value) {
            addCriterion("\"pkid\" =", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotEqualTo(String value) {
            addCriterion("\"pkid\" <>", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThan(String value) {
            addCriterion("\"pkid\" >", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThanOrEqualTo(String value) {
            addCriterion("\"pkid\" >=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThan(String value) {
            addCriterion("\"pkid\" <", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThanOrEqualTo(String value) {
            addCriterion("\"pkid\" <=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLike(String value) {
            addCriterion("\"pkid\" like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotLike(String value) {
            addCriterion("\"pkid\" not like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidIn(List<String> values) {
            addCriterion("\"pkid\" in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotIn(List<String> values) {
            addCriterion("\"pkid\" not in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidBetween(String value1, String value2) {
            addCriterion("\"pkid\" between", value1, value2, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotBetween(String value1, String value2) {
            addCriterion("\"pkid\" not between", value1, value2, "pkid");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeIsNull() {
            addCriterion("\"operation_begin_time\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeIsNotNull() {
            addCriterion("\"operation_begin_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeEqualTo(Date value) {
            addCriterion("\"operation_begin_time\" =", value, "operationBeginTime");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeNotEqualTo(Date value) {
            addCriterion("\"operation_begin_time\" <>", value, "operationBeginTime");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeGreaterThan(Date value) {
            addCriterion("\"operation_begin_time\" >", value, "operationBeginTime");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"operation_begin_time\" >=", value, "operationBeginTime");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeLessThan(Date value) {
            addCriterion("\"operation_begin_time\" <", value, "operationBeginTime");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"operation_begin_time\" <=", value, "operationBeginTime");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeIn(List<Date> values) {
            addCriterion("\"operation_begin_time\" in", values, "operationBeginTime");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeNotIn(List<Date> values) {
            addCriterion("\"operation_begin_time\" not in", values, "operationBeginTime");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeBetween(Date value1, Date value2) {
            addCriterion("\"operation_begin_time\" between", value1, value2, "operationBeginTime");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"operation_begin_time\" not between", value1, value2, "operationBeginTime");
            return (Criteria) this;
        }

        public Criteria andOperationNameIsNull() {
            addCriterion("\"operation_name\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationNameIsNotNull() {
            addCriterion("\"operation_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationNameEqualTo(String value) {
            addCriterion("\"operation_name\" =", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameNotEqualTo(String value) {
            addCriterion("\"operation_name\" <>", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameGreaterThan(String value) {
            addCriterion("\"operation_name\" >", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"operation_name\" >=", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameLessThan(String value) {
            addCriterion("\"operation_name\" <", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameLessThanOrEqualTo(String value) {
            addCriterion("\"operation_name\" <=", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameLike(String value) {
            addCriterion("\"operation_name\" like", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameNotLike(String value) {
            addCriterion("\"operation_name\" not like", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameIn(List<String> values) {
            addCriterion("\"operation_name\" in", values, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameNotIn(List<String> values) {
            addCriterion("\"operation_name\" not in", values, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameBetween(String value1, String value2) {
            addCriterion("\"operation_name\" between", value1, value2, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameNotBetween(String value1, String value2) {
            addCriterion("\"operation_name\" not between", value1, value2, "operationName");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationIsNull() {
            addCriterion("\"diag_preoperation\" is null");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationIsNotNull() {
            addCriterion("\"diag_preoperation\" is not null");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationEqualTo(String value) {
            addCriterion("\"diag_preoperation\" =", value, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationNotEqualTo(String value) {
            addCriterion("\"diag_preoperation\" <>", value, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationGreaterThan(String value) {
            addCriterion("\"diag_preoperation\" >", value, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationGreaterThanOrEqualTo(String value) {
            addCriterion("\"diag_preoperation\" >=", value, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationLessThan(String value) {
            addCriterion("\"diag_preoperation\" <", value, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationLessThanOrEqualTo(String value) {
            addCriterion("\"diag_preoperation\" <=", value, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationLike(String value) {
            addCriterion("\"diag_preoperation\" like", value, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationNotLike(String value) {
            addCriterion("\"diag_preoperation\" not like", value, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationIn(List<String> values) {
            addCriterion("\"diag_preoperation\" in", values, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationNotIn(List<String> values) {
            addCriterion("\"diag_preoperation\" not in", values, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationBetween(String value1, String value2) {
            addCriterion("\"diag_preoperation\" between", value1, value2, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationNotBetween(String value1, String value2) {
            addCriterion("\"diag_preoperation\" not between", value1, value2, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationIsNull() {
            addCriterion("\"diag_postoperation\" is null");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationIsNotNull() {
            addCriterion("\"diag_postoperation\" is not null");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationEqualTo(String value) {
            addCriterion("\"diag_postoperation\" =", value, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationNotEqualTo(String value) {
            addCriterion("\"diag_postoperation\" <>", value, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationGreaterThan(String value) {
            addCriterion("\"diag_postoperation\" >", value, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationGreaterThanOrEqualTo(String value) {
            addCriterion("\"diag_postoperation\" >=", value, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationLessThan(String value) {
            addCriterion("\"diag_postoperation\" <", value, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationLessThanOrEqualTo(String value) {
            addCriterion("\"diag_postoperation\" <=", value, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationLike(String value) {
            addCriterion("\"diag_postoperation\" like", value, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationNotLike(String value) {
            addCriterion("\"diag_postoperation\" not like", value, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationIn(List<String> values) {
            addCriterion("\"diag_postoperation\" in", values, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationNotIn(List<String> values) {
            addCriterion("\"diag_postoperation\" not in", values, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationBetween(String value1, String value2) {
            addCriterion("\"diag_postoperation\" between", value1, value2, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationNotBetween(String value1, String value2) {
            addCriterion("\"diag_postoperation\" not between", value1, value2, "diagPostoperation");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}