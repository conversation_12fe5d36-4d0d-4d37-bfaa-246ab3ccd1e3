package com.haoys.disease.mapper;

import com.haoys.disease.model.DrugOrder;
import com.haoys.disease.model.DrugOrderExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DrugOrderMapper {
    long countByExample(DrugOrderExample example);

    int deleteByExample(DrugOrderExample example);

    int insert(DrugOrder record);

    int insertSelective(DrugOrder record);

    List<DrugOrder> selectByExample(DrugOrderExample example);

    int updateByExampleSelective(@Param("record") DrugOrder record, @Param("example") DrugOrderExample example);

    int updateByExample(@Param("record") DrugOrder record, @Param("example") DrugOrderExample example);
}