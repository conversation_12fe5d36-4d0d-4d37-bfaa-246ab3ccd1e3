package com.haoys.disease.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class PatientVisitInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public PatientVisitInfoExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andRegisterdttmIsNull() {
            addCriterion("registerdttm is null");
            return (Criteria) this;
        }

        public Criteria andRegisterdttmIsNotNull() {
            addCriterion("registerdttm is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterdttmEqualTo(Date value) {
            addCriterionForJDBCDate("registerdttm =", value, "registerdttm");
            return (Criteria) this;
        }

        public Criteria andRegisterdttmNotEqualTo(Date value) {
            addCriterionForJDBCDate("registerdttm <>", value, "registerdttm");
            return (Criteria) this;
        }

        public Criteria andRegisterdttmGreaterThan(Date value) {
            addCriterionForJDBCDate("registerdttm >", value, "registerdttm");
            return (Criteria) this;
        }

        public Criteria andRegisterdttmGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("registerdttm >=", value, "registerdttm");
            return (Criteria) this;
        }

        public Criteria andRegisterdttmLessThan(Date value) {
            addCriterionForJDBCDate("registerdttm <", value, "registerdttm");
            return (Criteria) this;
        }

        public Criteria andRegisterdttmLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("registerdttm <=", value, "registerdttm");
            return (Criteria) this;
        }

        public Criteria andRegisterdttmIn(List<Date> values) {
            addCriterionForJDBCDate("registerdttm in", values, "registerdttm");
            return (Criteria) this;
        }

        public Criteria andRegisterdttmNotIn(List<Date> values) {
            addCriterionForJDBCDate("registerdttm not in", values, "registerdttm");
            return (Criteria) this;
        }

        public Criteria andRegisterdttmBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("registerdttm between", value1, value2, "registerdttm");
            return (Criteria) this;
        }

        public Criteria andRegisterdttmNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("registerdttm not between", value1, value2, "registerdttm");
            return (Criteria) this;
        }

        public Criteria andPatientIdIsNull() {
            addCriterion("patient_id is null");
            return (Criteria) this;
        }

        public Criteria andPatientIdIsNotNull() {
            addCriterion("patient_id is not null");
            return (Criteria) this;
        }

        public Criteria andPatientIdEqualTo(String value) {
            addCriterion("patient_id =", value, "patientId");
            return (Criteria) this;
        }

        public Criteria andPatientIdNotEqualTo(String value) {
            addCriterion("patient_id <>", value, "patientId");
            return (Criteria) this;
        }

        public Criteria andPatientIdGreaterThan(String value) {
            addCriterion("patient_id >", value, "patientId");
            return (Criteria) this;
        }

        public Criteria andPatientIdGreaterThanOrEqualTo(String value) {
            addCriterion("patient_id >=", value, "patientId");
            return (Criteria) this;
        }

        public Criteria andPatientIdLessThan(String value) {
            addCriterion("patient_id <", value, "patientId");
            return (Criteria) this;
        }

        public Criteria andPatientIdLessThanOrEqualTo(String value) {
            addCriterion("patient_id <=", value, "patientId");
            return (Criteria) this;
        }

        public Criteria andPatientIdLike(String value) {
            addCriterion("patient_id like", value, "patientId");
            return (Criteria) this;
        }

        public Criteria andPatientIdNotLike(String value) {
            addCriterion("patient_id not like", value, "patientId");
            return (Criteria) this;
        }

        public Criteria andPatientIdIn(List<String> values) {
            addCriterion("patient_id in", values, "patientId");
            return (Criteria) this;
        }

        public Criteria andPatientIdNotIn(List<String> values) {
            addCriterion("patient_id not in", values, "patientId");
            return (Criteria) this;
        }

        public Criteria andPatientIdBetween(String value1, String value2) {
            addCriterion("patient_id between", value1, value2, "patientId");
            return (Criteria) this;
        }

        public Criteria andPatientIdNotBetween(String value1, String value2) {
            addCriterion("patient_id not between", value1, value2, "patientId");
            return (Criteria) this;
        }

        public Criteria andVisitSerialNumberIsNull() {
            addCriterion("visit_serial_number is null");
            return (Criteria) this;
        }

        public Criteria andVisitSerialNumberIsNotNull() {
            addCriterion("visit_serial_number is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSerialNumberEqualTo(String value) {
            addCriterion("visit_serial_number =", value, "visitSerialNumber");
            return (Criteria) this;
        }

        public Criteria andVisitSerialNumberNotEqualTo(String value) {
            addCriterion("visit_serial_number <>", value, "visitSerialNumber");
            return (Criteria) this;
        }

        public Criteria andVisitSerialNumberGreaterThan(String value) {
            addCriterion("visit_serial_number >", value, "visitSerialNumber");
            return (Criteria) this;
        }

        public Criteria andVisitSerialNumberGreaterThanOrEqualTo(String value) {
            addCriterion("visit_serial_number >=", value, "visitSerialNumber");
            return (Criteria) this;
        }

        public Criteria andVisitSerialNumberLessThan(String value) {
            addCriterion("visit_serial_number <", value, "visitSerialNumber");
            return (Criteria) this;
        }

        public Criteria andVisitSerialNumberLessThanOrEqualTo(String value) {
            addCriterion("visit_serial_number <=", value, "visitSerialNumber");
            return (Criteria) this;
        }

        public Criteria andVisitSerialNumberLike(String value) {
            addCriterion("visit_serial_number like", value, "visitSerialNumber");
            return (Criteria) this;
        }

        public Criteria andVisitSerialNumberNotLike(String value) {
            addCriterion("visit_serial_number not like", value, "visitSerialNumber");
            return (Criteria) this;
        }

        public Criteria andVisitSerialNumberIn(List<String> values) {
            addCriterion("visit_serial_number in", values, "visitSerialNumber");
            return (Criteria) this;
        }

        public Criteria andVisitSerialNumberNotIn(List<String> values) {
            addCriterion("visit_serial_number not in", values, "visitSerialNumber");
            return (Criteria) this;
        }

        public Criteria andVisitSerialNumberBetween(String value1, String value2) {
            addCriterion("visit_serial_number between", value1, value2, "visitSerialNumber");
            return (Criteria) this;
        }

        public Criteria andVisitSerialNumberNotBetween(String value1, String value2) {
            addCriterion("visit_serial_number not between", value1, value2, "visitSerialNumber");
            return (Criteria) this;
        }

        public Criteria andVisitTypeIsNull() {
            addCriterion("visit_type is null");
            return (Criteria) this;
        }

        public Criteria andVisitTypeIsNotNull() {
            addCriterion("visit_type is not null");
            return (Criteria) this;
        }

        public Criteria andVisitTypeEqualTo(String value) {
            addCriterion("visit_type =", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeNotEqualTo(String value) {
            addCriterion("visit_type <>", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeGreaterThan(String value) {
            addCriterion("visit_type >", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeGreaterThanOrEqualTo(String value) {
            addCriterion("visit_type >=", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeLessThan(String value) {
            addCriterion("visit_type <", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeLessThanOrEqualTo(String value) {
            addCriterion("visit_type <=", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeLike(String value) {
            addCriterion("visit_type like", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeNotLike(String value) {
            addCriterion("visit_type not like", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeIn(List<String> values) {
            addCriterion("visit_type in", values, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeNotIn(List<String> values) {
            addCriterion("visit_type not in", values, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeBetween(String value1, String value2) {
            addCriterion("visit_type between", value1, value2, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeNotBetween(String value1, String value2) {
            addCriterion("visit_type not between", value1, value2, "visitType");
            return (Criteria) this;
        }

        public Criteria andDepartmentCodeIsNull() {
            addCriterion("department_code is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentCodeIsNotNull() {
            addCriterion("department_code is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentCodeEqualTo(String value) {
            addCriterion("department_code =", value, "departmentCode");
            return (Criteria) this;
        }

        public Criteria andDepartmentCodeNotEqualTo(String value) {
            addCriterion("department_code <>", value, "departmentCode");
            return (Criteria) this;
        }

        public Criteria andDepartmentCodeGreaterThan(String value) {
            addCriterion("department_code >", value, "departmentCode");
            return (Criteria) this;
        }

        public Criteria andDepartmentCodeGreaterThanOrEqualTo(String value) {
            addCriterion("department_code >=", value, "departmentCode");
            return (Criteria) this;
        }

        public Criteria andDepartmentCodeLessThan(String value) {
            addCriterion("department_code <", value, "departmentCode");
            return (Criteria) this;
        }

        public Criteria andDepartmentCodeLessThanOrEqualTo(String value) {
            addCriterion("department_code <=", value, "departmentCode");
            return (Criteria) this;
        }

        public Criteria andDepartmentCodeLike(String value) {
            addCriterion("department_code like", value, "departmentCode");
            return (Criteria) this;
        }

        public Criteria andDepartmentCodeNotLike(String value) {
            addCriterion("department_code not like", value, "departmentCode");
            return (Criteria) this;
        }

        public Criteria andDepartmentCodeIn(List<String> values) {
            addCriterion("department_code in", values, "departmentCode");
            return (Criteria) this;
        }

        public Criteria andDepartmentCodeNotIn(List<String> values) {
            addCriterion("department_code not in", values, "departmentCode");
            return (Criteria) this;
        }

        public Criteria andDepartmentCodeBetween(String value1, String value2) {
            addCriterion("department_code between", value1, value2, "departmentCode");
            return (Criteria) this;
        }

        public Criteria andDepartmentCodeNotBetween(String value1, String value2) {
            addCriterion("department_code not between", value1, value2, "departmentCode");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameIsNull() {
            addCriterion("department_name is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameIsNotNull() {
            addCriterion("department_name is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameEqualTo(String value) {
            addCriterion("department_name =", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotEqualTo(String value) {
            addCriterion("department_name <>", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameGreaterThan(String value) {
            addCriterion("department_name >", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameGreaterThanOrEqualTo(String value) {
            addCriterion("department_name >=", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameLessThan(String value) {
            addCriterion("department_name <", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameLessThanOrEqualTo(String value) {
            addCriterion("department_name <=", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameLike(String value) {
            addCriterion("department_name like", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotLike(String value) {
            addCriterion("department_name not like", value, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameIn(List<String> values) {
            addCriterion("department_name in", values, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotIn(List<String> values) {
            addCriterion("department_name not in", values, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameBetween(String value1, String value2) {
            addCriterion("department_name between", value1, value2, "departmentName");
            return (Criteria) this;
        }

        public Criteria andDepartmentNameNotBetween(String value1, String value2) {
            addCriterion("department_name not between", value1, value2, "departmentName");
            return (Criteria) this;
        }

        public Criteria andWardCodeIsNull() {
            addCriterion("ward_code is null");
            return (Criteria) this;
        }

        public Criteria andWardCodeIsNotNull() {
            addCriterion("ward_code is not null");
            return (Criteria) this;
        }

        public Criteria andWardCodeEqualTo(String value) {
            addCriterion("ward_code =", value, "wardCode");
            return (Criteria) this;
        }

        public Criteria andWardCodeNotEqualTo(String value) {
            addCriterion("ward_code <>", value, "wardCode");
            return (Criteria) this;
        }

        public Criteria andWardCodeGreaterThan(String value) {
            addCriterion("ward_code >", value, "wardCode");
            return (Criteria) this;
        }

        public Criteria andWardCodeGreaterThanOrEqualTo(String value) {
            addCriterion("ward_code >=", value, "wardCode");
            return (Criteria) this;
        }

        public Criteria andWardCodeLessThan(String value) {
            addCriterion("ward_code <", value, "wardCode");
            return (Criteria) this;
        }

        public Criteria andWardCodeLessThanOrEqualTo(String value) {
            addCriterion("ward_code <=", value, "wardCode");
            return (Criteria) this;
        }

        public Criteria andWardCodeLike(String value) {
            addCriterion("ward_code like", value, "wardCode");
            return (Criteria) this;
        }

        public Criteria andWardCodeNotLike(String value) {
            addCriterion("ward_code not like", value, "wardCode");
            return (Criteria) this;
        }

        public Criteria andWardCodeIn(List<String> values) {
            addCriterion("ward_code in", values, "wardCode");
            return (Criteria) this;
        }

        public Criteria andWardCodeNotIn(List<String> values) {
            addCriterion("ward_code not in", values, "wardCode");
            return (Criteria) this;
        }

        public Criteria andWardCodeBetween(String value1, String value2) {
            addCriterion("ward_code between", value1, value2, "wardCode");
            return (Criteria) this;
        }

        public Criteria andWardCodeNotBetween(String value1, String value2) {
            addCriterion("ward_code not between", value1, value2, "wardCode");
            return (Criteria) this;
        }

        public Criteria andWardNameIsNull() {
            addCriterion("ward_name is null");
            return (Criteria) this;
        }

        public Criteria andWardNameIsNotNull() {
            addCriterion("ward_name is not null");
            return (Criteria) this;
        }

        public Criteria andWardNameEqualTo(String value) {
            addCriterion("ward_name =", value, "wardName");
            return (Criteria) this;
        }

        public Criteria andWardNameNotEqualTo(String value) {
            addCriterion("ward_name <>", value, "wardName");
            return (Criteria) this;
        }

        public Criteria andWardNameGreaterThan(String value) {
            addCriterion("ward_name >", value, "wardName");
            return (Criteria) this;
        }

        public Criteria andWardNameGreaterThanOrEqualTo(String value) {
            addCriterion("ward_name >=", value, "wardName");
            return (Criteria) this;
        }

        public Criteria andWardNameLessThan(String value) {
            addCriterion("ward_name <", value, "wardName");
            return (Criteria) this;
        }

        public Criteria andWardNameLessThanOrEqualTo(String value) {
            addCriterion("ward_name <=", value, "wardName");
            return (Criteria) this;
        }

        public Criteria andWardNameLike(String value) {
            addCriterion("ward_name like", value, "wardName");
            return (Criteria) this;
        }

        public Criteria andWardNameNotLike(String value) {
            addCriterion("ward_name not like", value, "wardName");
            return (Criteria) this;
        }

        public Criteria andWardNameIn(List<String> values) {
            addCriterion("ward_name in", values, "wardName");
            return (Criteria) this;
        }

        public Criteria andWardNameNotIn(List<String> values) {
            addCriterion("ward_name not in", values, "wardName");
            return (Criteria) this;
        }

        public Criteria andWardNameBetween(String value1, String value2) {
            addCriterion("ward_name between", value1, value2, "wardName");
            return (Criteria) this;
        }

        public Criteria andWardNameNotBetween(String value1, String value2) {
            addCriterion("ward_name not between", value1, value2, "wardName");
            return (Criteria) this;
        }

        public Criteria andBedCodeIsNull() {
            addCriterion("bed_code is null");
            return (Criteria) this;
        }

        public Criteria andBedCodeIsNotNull() {
            addCriterion("bed_code is not null");
            return (Criteria) this;
        }

        public Criteria andBedCodeEqualTo(String value) {
            addCriterion("bed_code =", value, "bedCode");
            return (Criteria) this;
        }

        public Criteria andBedCodeNotEqualTo(String value) {
            addCriterion("bed_code <>", value, "bedCode");
            return (Criteria) this;
        }

        public Criteria andBedCodeGreaterThan(String value) {
            addCriterion("bed_code >", value, "bedCode");
            return (Criteria) this;
        }

        public Criteria andBedCodeGreaterThanOrEqualTo(String value) {
            addCriterion("bed_code >=", value, "bedCode");
            return (Criteria) this;
        }

        public Criteria andBedCodeLessThan(String value) {
            addCriterion("bed_code <", value, "bedCode");
            return (Criteria) this;
        }

        public Criteria andBedCodeLessThanOrEqualTo(String value) {
            addCriterion("bed_code <=", value, "bedCode");
            return (Criteria) this;
        }

        public Criteria andBedCodeLike(String value) {
            addCriterion("bed_code like", value, "bedCode");
            return (Criteria) this;
        }

        public Criteria andBedCodeNotLike(String value) {
            addCriterion("bed_code not like", value, "bedCode");
            return (Criteria) this;
        }

        public Criteria andBedCodeIn(List<String> values) {
            addCriterion("bed_code in", values, "bedCode");
            return (Criteria) this;
        }

        public Criteria andBedCodeNotIn(List<String> values) {
            addCriterion("bed_code not in", values, "bedCode");
            return (Criteria) this;
        }

        public Criteria andBedCodeBetween(String value1, String value2) {
            addCriterion("bed_code between", value1, value2, "bedCode");
            return (Criteria) this;
        }

        public Criteria andBedCodeNotBetween(String value1, String value2) {
            addCriterion("bed_code not between", value1, value2, "bedCode");
            return (Criteria) this;
        }

        public Criteria andAdmitdttmIsNull() {
            addCriterion("admitdttm is null");
            return (Criteria) this;
        }

        public Criteria andAdmitdttmIsNotNull() {
            addCriterion("admitdttm is not null");
            return (Criteria) this;
        }

        public Criteria andAdmitdttmEqualTo(Date value) {
            addCriterionForJDBCDate("admitdttm =", value, "admitdttm");
            return (Criteria) this;
        }

        public Criteria andAdmitdttmNotEqualTo(Date value) {
            addCriterionForJDBCDate("admitdttm <>", value, "admitdttm");
            return (Criteria) this;
        }

        public Criteria andAdmitdttmGreaterThan(Date value) {
            addCriterionForJDBCDate("admitdttm >", value, "admitdttm");
            return (Criteria) this;
        }

        public Criteria andAdmitdttmGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("admitdttm >=", value, "admitdttm");
            return (Criteria) this;
        }

        public Criteria andAdmitdttmLessThan(Date value) {
            addCriterionForJDBCDate("admitdttm <", value, "admitdttm");
            return (Criteria) this;
        }

        public Criteria andAdmitdttmLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("admitdttm <=", value, "admitdttm");
            return (Criteria) this;
        }

        public Criteria andAdmitdttmIn(List<Date> values) {
            addCriterionForJDBCDate("admitdttm in", values, "admitdttm");
            return (Criteria) this;
        }

        public Criteria andAdmitdttmNotIn(List<Date> values) {
            addCriterionForJDBCDate("admitdttm not in", values, "admitdttm");
            return (Criteria) this;
        }

        public Criteria andAdmitdttmBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("admitdttm between", value1, value2, "admitdttm");
            return (Criteria) this;
        }

        public Criteria andAdmitdttmNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("admitdttm not between", value1, value2, "admitdttm");
            return (Criteria) this;
        }

        public Criteria andDischargedttmIsNull() {
            addCriterion("dischargedttm is null");
            return (Criteria) this;
        }

        public Criteria andDischargedttmIsNotNull() {
            addCriterion("dischargedttm is not null");
            return (Criteria) this;
        }

        public Criteria andDischargedttmEqualTo(Date value) {
            addCriterionForJDBCDate("dischargedttm =", value, "dischargedttm");
            return (Criteria) this;
        }

        public Criteria andDischargedttmNotEqualTo(Date value) {
            addCriterionForJDBCDate("dischargedttm <>", value, "dischargedttm");
            return (Criteria) this;
        }

        public Criteria andDischargedttmGreaterThan(Date value) {
            addCriterionForJDBCDate("dischargedttm >", value, "dischargedttm");
            return (Criteria) this;
        }

        public Criteria andDischargedttmGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("dischargedttm >=", value, "dischargedttm");
            return (Criteria) this;
        }

        public Criteria andDischargedttmLessThan(Date value) {
            addCriterionForJDBCDate("dischargedttm <", value, "dischargedttm");
            return (Criteria) this;
        }

        public Criteria andDischargedttmLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("dischargedttm <=", value, "dischargedttm");
            return (Criteria) this;
        }

        public Criteria andDischargedttmIn(List<Date> values) {
            addCriterionForJDBCDate("dischargedttm in", values, "dischargedttm");
            return (Criteria) this;
        }

        public Criteria andDischargedttmNotIn(List<Date> values) {
            addCriterionForJDBCDate("dischargedttm not in", values, "dischargedttm");
            return (Criteria) this;
        }

        public Criteria andDischargedttmBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("dischargedttm between", value1, value2, "dischargedttm");
            return (Criteria) this;
        }

        public Criteria andDischargedttmNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("dischargedttm not between", value1, value2, "dischargedttm");
            return (Criteria) this;
        }

        public Criteria andConsultingmdCodeIsNull() {
            addCriterion("consultingmd_code is null");
            return (Criteria) this;
        }

        public Criteria andConsultingmdCodeIsNotNull() {
            addCriterion("consultingmd_code is not null");
            return (Criteria) this;
        }

        public Criteria andConsultingmdCodeEqualTo(String value) {
            addCriterion("consultingmd_code =", value, "consultingmdCode");
            return (Criteria) this;
        }

        public Criteria andConsultingmdCodeNotEqualTo(String value) {
            addCriterion("consultingmd_code <>", value, "consultingmdCode");
            return (Criteria) this;
        }

        public Criteria andConsultingmdCodeGreaterThan(String value) {
            addCriterion("consultingmd_code >", value, "consultingmdCode");
            return (Criteria) this;
        }

        public Criteria andConsultingmdCodeGreaterThanOrEqualTo(String value) {
            addCriterion("consultingmd_code >=", value, "consultingmdCode");
            return (Criteria) this;
        }

        public Criteria andConsultingmdCodeLessThan(String value) {
            addCriterion("consultingmd_code <", value, "consultingmdCode");
            return (Criteria) this;
        }

        public Criteria andConsultingmdCodeLessThanOrEqualTo(String value) {
            addCriterion("consultingmd_code <=", value, "consultingmdCode");
            return (Criteria) this;
        }

        public Criteria andConsultingmdCodeLike(String value) {
            addCriterion("consultingmd_code like", value, "consultingmdCode");
            return (Criteria) this;
        }

        public Criteria andConsultingmdCodeNotLike(String value) {
            addCriterion("consultingmd_code not like", value, "consultingmdCode");
            return (Criteria) this;
        }

        public Criteria andConsultingmdCodeIn(List<String> values) {
            addCriterion("consultingmd_code in", values, "consultingmdCode");
            return (Criteria) this;
        }

        public Criteria andConsultingmdCodeNotIn(List<String> values) {
            addCriterion("consultingmd_code not in", values, "consultingmdCode");
            return (Criteria) this;
        }

        public Criteria andConsultingmdCodeBetween(String value1, String value2) {
            addCriterion("consultingmd_code between", value1, value2, "consultingmdCode");
            return (Criteria) this;
        }

        public Criteria andConsultingmdCodeNotBetween(String value1, String value2) {
            addCriterion("consultingmd_code not between", value1, value2, "consultingmdCode");
            return (Criteria) this;
        }

        public Criteria andConsultingmdNameIsNull() {
            addCriterion("consultingmd_name is null");
            return (Criteria) this;
        }

        public Criteria andConsultingmdNameIsNotNull() {
            addCriterion("consultingmd_name is not null");
            return (Criteria) this;
        }

        public Criteria andConsultingmdNameEqualTo(String value) {
            addCriterion("consultingmd_name =", value, "consultingmdName");
            return (Criteria) this;
        }

        public Criteria andConsultingmdNameNotEqualTo(String value) {
            addCriterion("consultingmd_name <>", value, "consultingmdName");
            return (Criteria) this;
        }

        public Criteria andConsultingmdNameGreaterThan(String value) {
            addCriterion("consultingmd_name >", value, "consultingmdName");
            return (Criteria) this;
        }

        public Criteria andConsultingmdNameGreaterThanOrEqualTo(String value) {
            addCriterion("consultingmd_name >=", value, "consultingmdName");
            return (Criteria) this;
        }

        public Criteria andConsultingmdNameLessThan(String value) {
            addCriterion("consultingmd_name <", value, "consultingmdName");
            return (Criteria) this;
        }

        public Criteria andConsultingmdNameLessThanOrEqualTo(String value) {
            addCriterion("consultingmd_name <=", value, "consultingmdName");
            return (Criteria) this;
        }

        public Criteria andConsultingmdNameLike(String value) {
            addCriterion("consultingmd_name like", value, "consultingmdName");
            return (Criteria) this;
        }

        public Criteria andConsultingmdNameNotLike(String value) {
            addCriterion("consultingmd_name not like", value, "consultingmdName");
            return (Criteria) this;
        }

        public Criteria andConsultingmdNameIn(List<String> values) {
            addCriterion("consultingmd_name in", values, "consultingmdName");
            return (Criteria) this;
        }

        public Criteria andConsultingmdNameNotIn(List<String> values) {
            addCriterion("consultingmd_name not in", values, "consultingmdName");
            return (Criteria) this;
        }

        public Criteria andConsultingmdNameBetween(String value1, String value2) {
            addCriterion("consultingmd_name between", value1, value2, "consultingmdName");
            return (Criteria) this;
        }

        public Criteria andConsultingmdNameNotBetween(String value1, String value2) {
            addCriterion("consultingmd_name not between", value1, value2, "consultingmdName");
            return (Criteria) this;
        }

        public Criteria andAttendingmdCodeIsNull() {
            addCriterion("attendingmd_code is null");
            return (Criteria) this;
        }

        public Criteria andAttendingmdCodeIsNotNull() {
            addCriterion("attendingmd_code is not null");
            return (Criteria) this;
        }

        public Criteria andAttendingmdCodeEqualTo(String value) {
            addCriterion("attendingmd_code =", value, "attendingmdCode");
            return (Criteria) this;
        }

        public Criteria andAttendingmdCodeNotEqualTo(String value) {
            addCriterion("attendingmd_code <>", value, "attendingmdCode");
            return (Criteria) this;
        }

        public Criteria andAttendingmdCodeGreaterThan(String value) {
            addCriterion("attendingmd_code >", value, "attendingmdCode");
            return (Criteria) this;
        }

        public Criteria andAttendingmdCodeGreaterThanOrEqualTo(String value) {
            addCriterion("attendingmd_code >=", value, "attendingmdCode");
            return (Criteria) this;
        }

        public Criteria andAttendingmdCodeLessThan(String value) {
            addCriterion("attendingmd_code <", value, "attendingmdCode");
            return (Criteria) this;
        }

        public Criteria andAttendingmdCodeLessThanOrEqualTo(String value) {
            addCriterion("attendingmd_code <=", value, "attendingmdCode");
            return (Criteria) this;
        }

        public Criteria andAttendingmdCodeLike(String value) {
            addCriterion("attendingmd_code like", value, "attendingmdCode");
            return (Criteria) this;
        }

        public Criteria andAttendingmdCodeNotLike(String value) {
            addCriterion("attendingmd_code not like", value, "attendingmdCode");
            return (Criteria) this;
        }

        public Criteria andAttendingmdCodeIn(List<String> values) {
            addCriterion("attendingmd_code in", values, "attendingmdCode");
            return (Criteria) this;
        }

        public Criteria andAttendingmdCodeNotIn(List<String> values) {
            addCriterion("attendingmd_code not in", values, "attendingmdCode");
            return (Criteria) this;
        }

        public Criteria andAttendingmdCodeBetween(String value1, String value2) {
            addCriterion("attendingmd_code between", value1, value2, "attendingmdCode");
            return (Criteria) this;
        }

        public Criteria andAttendingmdCodeNotBetween(String value1, String value2) {
            addCriterion("attendingmd_code not between", value1, value2, "attendingmdCode");
            return (Criteria) this;
        }

        public Criteria andAttendingmdNameIsNull() {
            addCriterion("attendingmd_name is null");
            return (Criteria) this;
        }

        public Criteria andAttendingmdNameIsNotNull() {
            addCriterion("attendingmd_name is not null");
            return (Criteria) this;
        }

        public Criteria andAttendingmdNameEqualTo(String value) {
            addCriterion("attendingmd_name =", value, "attendingmdName");
            return (Criteria) this;
        }

        public Criteria andAttendingmdNameNotEqualTo(String value) {
            addCriterion("attendingmd_name <>", value, "attendingmdName");
            return (Criteria) this;
        }

        public Criteria andAttendingmdNameGreaterThan(String value) {
            addCriterion("attendingmd_name >", value, "attendingmdName");
            return (Criteria) this;
        }

        public Criteria andAttendingmdNameGreaterThanOrEqualTo(String value) {
            addCriterion("attendingmd_name >=", value, "attendingmdName");
            return (Criteria) this;
        }

        public Criteria andAttendingmdNameLessThan(String value) {
            addCriterion("attendingmd_name <", value, "attendingmdName");
            return (Criteria) this;
        }

        public Criteria andAttendingmdNameLessThanOrEqualTo(String value) {
            addCriterion("attendingmd_name <=", value, "attendingmdName");
            return (Criteria) this;
        }

        public Criteria andAttendingmdNameLike(String value) {
            addCriterion("attendingmd_name like", value, "attendingmdName");
            return (Criteria) this;
        }

        public Criteria andAttendingmdNameNotLike(String value) {
            addCriterion("attendingmd_name not like", value, "attendingmdName");
            return (Criteria) this;
        }

        public Criteria andAttendingmdNameIn(List<String> values) {
            addCriterion("attendingmd_name in", values, "attendingmdName");
            return (Criteria) this;
        }

        public Criteria andAttendingmdNameNotIn(List<String> values) {
            addCriterion("attendingmd_name not in", values, "attendingmdName");
            return (Criteria) this;
        }

        public Criteria andAttendingmdNameBetween(String value1, String value2) {
            addCriterion("attendingmd_name between", value1, value2, "attendingmdName");
            return (Criteria) this;
        }

        public Criteria andAttendingmdNameNotBetween(String value1, String value2) {
            addCriterion("attendingmd_name not between", value1, value2, "attendingmdName");
            return (Criteria) this;
        }

        public Criteria andOutcomeIsNull() {
            addCriterion("outcome is null");
            return (Criteria) this;
        }

        public Criteria andOutcomeIsNotNull() {
            addCriterion("outcome is not null");
            return (Criteria) this;
        }

        public Criteria andOutcomeEqualTo(String value) {
            addCriterion("outcome =", value, "outcome");
            return (Criteria) this;
        }

        public Criteria andOutcomeNotEqualTo(String value) {
            addCriterion("outcome <>", value, "outcome");
            return (Criteria) this;
        }

        public Criteria andOutcomeGreaterThan(String value) {
            addCriterion("outcome >", value, "outcome");
            return (Criteria) this;
        }

        public Criteria andOutcomeGreaterThanOrEqualTo(String value) {
            addCriterion("outcome >=", value, "outcome");
            return (Criteria) this;
        }

        public Criteria andOutcomeLessThan(String value) {
            addCriterion("outcome <", value, "outcome");
            return (Criteria) this;
        }

        public Criteria andOutcomeLessThanOrEqualTo(String value) {
            addCriterion("outcome <=", value, "outcome");
            return (Criteria) this;
        }

        public Criteria andOutcomeLike(String value) {
            addCriterion("outcome like", value, "outcome");
            return (Criteria) this;
        }

        public Criteria andOutcomeNotLike(String value) {
            addCriterion("outcome not like", value, "outcome");
            return (Criteria) this;
        }

        public Criteria andOutcomeIn(List<String> values) {
            addCriterion("outcome in", values, "outcome");
            return (Criteria) this;
        }

        public Criteria andOutcomeNotIn(List<String> values) {
            addCriterion("outcome not in", values, "outcome");
            return (Criteria) this;
        }

        public Criteria andOutcomeBetween(String value1, String value2) {
            addCriterion("outcome between", value1, value2, "outcome");
            return (Criteria) this;
        }

        public Criteria andOutcomeNotBetween(String value1, String value2) {
            addCriterion("outcome not between", value1, value2, "outcome");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}