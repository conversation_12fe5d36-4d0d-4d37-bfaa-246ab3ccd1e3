package com.haoys.disease.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ThrombolyticTherapyExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ThrombolyticTherapyExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andPkidIsNull() {
            addCriterion("\"pkid\" is null");
            return (Criteria) this;
        }

        public Criteria andPkidIsNotNull() {
            addCriterion("\"pkid\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkidEqualTo(String value) {
            addCriterion("\"pkid\" =", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotEqualTo(String value) {
            addCriterion("\"pkid\" <>", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThan(String value) {
            addCriterion("\"pkid\" >", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThanOrEqualTo(String value) {
            addCriterion("\"pkid\" >=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThan(String value) {
            addCriterion("\"pkid\" <", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThanOrEqualTo(String value) {
            addCriterion("\"pkid\" <=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLike(String value) {
            addCriterion("\"pkid\" like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotLike(String value) {
            addCriterion("\"pkid\" not like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidIn(List<String> values) {
            addCriterion("\"pkid\" in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotIn(List<String> values) {
            addCriterion("\"pkid\" not in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidBetween(String value1, String value2) {
            addCriterion("\"pkid\" between", value1, value2, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotBetween(String value1, String value2) {
            addCriterion("\"pkid\" not between", value1, value2, "pkid");
            return (Criteria) this;
        }

        public Criteria andGenericNameIsNull() {
            addCriterion("\"generic_name\" is null");
            return (Criteria) this;
        }

        public Criteria andGenericNameIsNotNull() {
            addCriterion("\"generic_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andGenericNameEqualTo(String value) {
            addCriterion("\"generic_name\" =", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameNotEqualTo(String value) {
            addCriterion("\"generic_name\" <>", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameGreaterThan(String value) {
            addCriterion("\"generic_name\" >", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"generic_name\" >=", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameLessThan(String value) {
            addCriterion("\"generic_name\" <", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameLessThanOrEqualTo(String value) {
            addCriterion("\"generic_name\" <=", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameLike(String value) {
            addCriterion("\"generic_name\" like", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameNotLike(String value) {
            addCriterion("\"generic_name\" not like", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameIn(List<String> values) {
            addCriterion("\"generic_name\" in", values, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameNotIn(List<String> values) {
            addCriterion("\"generic_name\" not in", values, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameBetween(String value1, String value2) {
            addCriterion("\"generic_name\" between", value1, value2, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameNotBetween(String value1, String value2) {
            addCriterion("\"generic_name\" not between", value1, value2, "genericName");
            return (Criteria) this;
        }

        public Criteria andDrugNameIsNull() {
            addCriterion("\"drug_name\" is null");
            return (Criteria) this;
        }

        public Criteria andDrugNameIsNotNull() {
            addCriterion("\"drug_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andDrugNameEqualTo(String value) {
            addCriterion("\"drug_name\" =", value, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugNameNotEqualTo(String value) {
            addCriterion("\"drug_name\" <>", value, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugNameGreaterThan(String value) {
            addCriterion("\"drug_name\" >", value, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"drug_name\" >=", value, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugNameLessThan(String value) {
            addCriterion("\"drug_name\" <", value, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugNameLessThanOrEqualTo(String value) {
            addCriterion("\"drug_name\" <=", value, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugNameLike(String value) {
            addCriterion("\"drug_name\" like", value, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugNameNotLike(String value) {
            addCriterion("\"drug_name\" not like", value, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugNameIn(List<String> values) {
            addCriterion("\"drug_name\" in", values, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugNameNotIn(List<String> values) {
            addCriterion("\"drug_name\" not in", values, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugNameBetween(String value1, String value2) {
            addCriterion("\"drug_name\" between", value1, value2, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugNameNotBetween(String value1, String value2) {
            addCriterion("\"drug_name\" not between", value1, value2, "drugName");
            return (Criteria) this;
        }

        public Criteria andStartDatetimeIsNull() {
            addCriterion("\"start_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andStartDatetimeIsNotNull() {
            addCriterion("\"start_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andStartDatetimeEqualTo(Date value) {
            addCriterion("\"start_datetime\" =", value, "startDatetime");
            return (Criteria) this;
        }

        public Criteria andStartDatetimeNotEqualTo(Date value) {
            addCriterion("\"start_datetime\" <>", value, "startDatetime");
            return (Criteria) this;
        }

        public Criteria andStartDatetimeGreaterThan(Date value) {
            addCriterion("\"start_datetime\" >", value, "startDatetime");
            return (Criteria) this;
        }

        public Criteria andStartDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"start_datetime\" >=", value, "startDatetime");
            return (Criteria) this;
        }

        public Criteria andStartDatetimeLessThan(Date value) {
            addCriterion("\"start_datetime\" <", value, "startDatetime");
            return (Criteria) this;
        }

        public Criteria andStartDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"start_datetime\" <=", value, "startDatetime");
            return (Criteria) this;
        }

        public Criteria andStartDatetimeIn(List<Date> values) {
            addCriterion("\"start_datetime\" in", values, "startDatetime");
            return (Criteria) this;
        }

        public Criteria andStartDatetimeNotIn(List<Date> values) {
            addCriterion("\"start_datetime\" not in", values, "startDatetime");
            return (Criteria) this;
        }

        public Criteria andStartDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"start_datetime\" between", value1, value2, "startDatetime");
            return (Criteria) this;
        }

        public Criteria andStartDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"start_datetime\" not between", value1, value2, "startDatetime");
            return (Criteria) this;
        }

        public Criteria andEndDatetimeIsNull() {
            addCriterion("\"end_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andEndDatetimeIsNotNull() {
            addCriterion("\"end_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andEndDatetimeEqualTo(Date value) {
            addCriterion("\"end_datetime\" =", value, "endDatetime");
            return (Criteria) this;
        }

        public Criteria andEndDatetimeNotEqualTo(Date value) {
            addCriterion("\"end_datetime\" <>", value, "endDatetime");
            return (Criteria) this;
        }

        public Criteria andEndDatetimeGreaterThan(Date value) {
            addCriterion("\"end_datetime\" >", value, "endDatetime");
            return (Criteria) this;
        }

        public Criteria andEndDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"end_datetime\" >=", value, "endDatetime");
            return (Criteria) this;
        }

        public Criteria andEndDatetimeLessThan(Date value) {
            addCriterion("\"end_datetime\" <", value, "endDatetime");
            return (Criteria) this;
        }

        public Criteria andEndDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"end_datetime\" <=", value, "endDatetime");
            return (Criteria) this;
        }

        public Criteria andEndDatetimeIn(List<Date> values) {
            addCriterion("\"end_datetime\" in", values, "endDatetime");
            return (Criteria) this;
        }

        public Criteria andEndDatetimeNotIn(List<Date> values) {
            addCriterion("\"end_datetime\" not in", values, "endDatetime");
            return (Criteria) this;
        }

        public Criteria andEndDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"end_datetime\" between", value1, value2, "endDatetime");
            return (Criteria) this;
        }

        public Criteria andEndDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"end_datetime\" not between", value1, value2, "endDatetime");
            return (Criteria) this;
        }

        public Criteria andDosageIsNull() {
            addCriterion("\"dosage\" is null");
            return (Criteria) this;
        }

        public Criteria andDosageIsNotNull() {
            addCriterion("\"dosage\" is not null");
            return (Criteria) this;
        }

        public Criteria andDosageEqualTo(String value) {
            addCriterion("\"dosage\" =", value, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageNotEqualTo(String value) {
            addCriterion("\"dosage\" <>", value, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageGreaterThan(String value) {
            addCriterion("\"dosage\" >", value, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageGreaterThanOrEqualTo(String value) {
            addCriterion("\"dosage\" >=", value, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageLessThan(String value) {
            addCriterion("\"dosage\" <", value, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageLessThanOrEqualTo(String value) {
            addCriterion("\"dosage\" <=", value, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageLike(String value) {
            addCriterion("\"dosage\" like", value, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageNotLike(String value) {
            addCriterion("\"dosage\" not like", value, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageIn(List<String> values) {
            addCriterion("\"dosage\" in", values, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageNotIn(List<String> values) {
            addCriterion("\"dosage\" not in", values, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageBetween(String value1, String value2) {
            addCriterion("\"dosage\" between", value1, value2, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageNotBetween(String value1, String value2) {
            addCriterion("\"dosage\" not between", value1, value2, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsIsNull() {
            addCriterion("\"dosage_units\" is null");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsIsNotNull() {
            addCriterion("\"dosage_units\" is not null");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsEqualTo(String value) {
            addCriterion("\"dosage_units\" =", value, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsNotEqualTo(String value) {
            addCriterion("\"dosage_units\" <>", value, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsGreaterThan(String value) {
            addCriterion("\"dosage_units\" >", value, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsGreaterThanOrEqualTo(String value) {
            addCriterion("\"dosage_units\" >=", value, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsLessThan(String value) {
            addCriterion("\"dosage_units\" <", value, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsLessThanOrEqualTo(String value) {
            addCriterion("\"dosage_units\" <=", value, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsLike(String value) {
            addCriterion("\"dosage_units\" like", value, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsNotLike(String value) {
            addCriterion("\"dosage_units\" not like", value, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsIn(List<String> values) {
            addCriterion("\"dosage_units\" in", values, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsNotIn(List<String> values) {
            addCriterion("\"dosage_units\" not in", values, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsBetween(String value1, String value2) {
            addCriterion("\"dosage_units\" between", value1, value2, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsNotBetween(String value1, String value2) {
            addCriterion("\"dosage_units\" not between", value1, value2, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andFrequencyIsNull() {
            addCriterion("\"frequency\" is null");
            return (Criteria) this;
        }

        public Criteria andFrequencyIsNotNull() {
            addCriterion("\"frequency\" is not null");
            return (Criteria) this;
        }

        public Criteria andFrequencyEqualTo(String value) {
            addCriterion("\"frequency\" =", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyNotEqualTo(String value) {
            addCriterion("\"frequency\" <>", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyGreaterThan(String value) {
            addCriterion("\"frequency\" >", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyGreaterThanOrEqualTo(String value) {
            addCriterion("\"frequency\" >=", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyLessThan(String value) {
            addCriterion("\"frequency\" <", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyLessThanOrEqualTo(String value) {
            addCriterion("\"frequency\" <=", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyLike(String value) {
            addCriterion("\"frequency\" like", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyNotLike(String value) {
            addCriterion("\"frequency\" not like", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyIn(List<String> values) {
            addCriterion("\"frequency\" in", values, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyNotIn(List<String> values) {
            addCriterion("\"frequency\" not in", values, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyBetween(String value1, String value2) {
            addCriterion("\"frequency\" between", value1, value2, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyNotBetween(String value1, String value2) {
            addCriterion("\"frequency\" not between", value1, value2, "frequency");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteIsNull() {
            addCriterion("\"administration_route\" is null");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteIsNotNull() {
            addCriterion("\"administration_route\" is not null");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteEqualTo(String value) {
            addCriterion("\"administration_route\" =", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteNotEqualTo(String value) {
            addCriterion("\"administration_route\" <>", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteGreaterThan(String value) {
            addCriterion("\"administration_route\" >", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteGreaterThanOrEqualTo(String value) {
            addCriterion("\"administration_route\" >=", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteLessThan(String value) {
            addCriterion("\"administration_route\" <", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteLessThanOrEqualTo(String value) {
            addCriterion("\"administration_route\" <=", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteLike(String value) {
            addCriterion("\"administration_route\" like", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteNotLike(String value) {
            addCriterion("\"administration_route\" not like", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteIn(List<String> values) {
            addCriterion("\"administration_route\" in", values, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteNotIn(List<String> values) {
            addCriterion("\"administration_route\" not in", values, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteBetween(String value1, String value2) {
            addCriterion("\"administration_route\" between", value1, value2, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteNotBetween(String value1, String value2) {
            addCriterion("\"administration_route\" not between", value1, value2, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andOrderClassIsNull() {
            addCriterion("\"order_class\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderClassIsNotNull() {
            addCriterion("\"order_class\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderClassEqualTo(String value) {
            addCriterion("\"order_class\" =", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassNotEqualTo(String value) {
            addCriterion("\"order_class\" <>", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassGreaterThan(String value) {
            addCriterion("\"order_class\" >", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassGreaterThanOrEqualTo(String value) {
            addCriterion("\"order_class\" >=", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassLessThan(String value) {
            addCriterion("\"order_class\" <", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassLessThanOrEqualTo(String value) {
            addCriterion("\"order_class\" <=", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassLike(String value) {
            addCriterion("\"order_class\" like", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassNotLike(String value) {
            addCriterion("\"order_class\" not like", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassIn(List<String> values) {
            addCriterion("\"order_class\" in", values, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassNotIn(List<String> values) {
            addCriterion("\"order_class\" not in", values, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassBetween(String value1, String value2) {
            addCriterion("\"order_class\" between", value1, value2, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassNotBetween(String value1, String value2) {
            addCriterion("\"order_class\" not between", value1, value2, "orderClass");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}