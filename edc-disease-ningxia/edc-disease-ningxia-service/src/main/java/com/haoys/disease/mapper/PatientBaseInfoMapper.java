package com.haoys.disease.mapper;

import com.haoys.disease.domain.param.PatientBaseQueryParam;
import com.haoys.disease.domain.vo.CountPatientDiseaseTypeVo;
import com.haoys.disease.domain.vo.CountPatientToAgeVo;
import com.haoys.disease.domain.vo.CountPatientToAreaVo;
import com.haoys.disease.domain.vo.PatientBaseVo;
import com.haoys.disease.domain.wrapper.PatientBaseWrapper;
import com.haoys.disease.model.PatientBaseInfo;
import com.haoys.disease.model.PatientBaseInfoExample;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

public interface PatientBaseInfoMapper {
    long countByExample(PatientBaseInfoExample example);

    int deleteByExample(PatientBaseInfoExample example);

    int insert(PatientBaseInfo record);

    int insertSelective(PatientBaseInfo record);

    List<PatientBaseInfo> selectByExample(PatientBaseInfoExample example);

    int updateByExampleSelective(@Param("record") PatientBaseInfo record, @Param("example") PatientBaseInfoExample example);

    int updateByExample(@Param("record") PatientBaseInfo record, @Param("example") PatientBaseInfoExample example);

    PatientBaseWrapper getPatientOwnerVisitDataWrapper(String patientId);

    List<PatientBaseVo> getPatientBaseInfoForPage(PatientBaseQueryParam patientBaseQueryParam);

    PatientBaseVo getPatientBaseInfo(String patientId);

    /**
     * 统计病种icd10 患者数量
     * @return
     */
    List<CountPatientDiseaseTypeVo> countPatientToDiseaseType();

    /**
     * 不同年龄段的统计
     * @return
     */
    List<CountPatientToAgeVo> countPatientToAge();

    /**
     * 患者区域分布图
     * @return
     */
    List<CountPatientToAreaVo> countPatientToAre();

    /**
     * 患者区域分布图(PostGreSQL数据库)
     * @return
     */
    List<CountPatientToAreaVo> countPgSqlPatientToAre();

    /**
     * 不同年龄段的统计((PostGreSQL数据库))
     * @return
     */
    List<CountPatientToAgeVo> countPgSqlPatientToAge();
    
    List<Map<String, Object>> getPatientAgeDistributionStatTable();
    
    List<Map<String, Object>> getPatientMedicalTreatmentStatTable(String fieldName);
    
    Map<String, Object> getPatientUseDrugStatTable();
}
