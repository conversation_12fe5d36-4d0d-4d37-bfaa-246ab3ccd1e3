package com.haoys.disease.model;

import java.util.ArrayList;
import java.util.List;

public class HemadostenosisInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public HemadostenosisInfoExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPkidIsNull() {
            addCriterion("\"pkid\" is null");
            return (Criteria) this;
        }

        public Criteria andPkidIsNotNull() {
            addCriterion("\"pkid\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkidEqualTo(String value) {
            addCriterion("\"pkid\" =", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotEqualTo(String value) {
            addCriterion("\"pkid\" <>", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThan(String value) {
            addCriterion("\"pkid\" >", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThanOrEqualTo(String value) {
            addCriterion("\"pkid\" >=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThan(String value) {
            addCriterion("\"pkid\" <", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThanOrEqualTo(String value) {
            addCriterion("\"pkid\" <=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLike(String value) {
            addCriterion("\"pkid\" like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotLike(String value) {
            addCriterion("\"pkid\" not like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidIn(List<String> values) {
            addCriterion("\"pkid\" in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotIn(List<String> values) {
            addCriterion("\"pkid\" not in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidBetween(String value1, String value2) {
            addCriterion("\"pkid\" between", value1, value2, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotBetween(String value1, String value2) {
            addCriterion("\"pkid\" not between", value1, value2, "pkid");
            return (Criteria) this;
        }

        public Criteria andSideIsNull() {
            addCriterion("\"side\" is null");
            return (Criteria) this;
        }

        public Criteria andSideIsNotNull() {
            addCriterion("\"side\" is not null");
            return (Criteria) this;
        }

        public Criteria andSideEqualTo(String value) {
            addCriterion("\"side\" =", value, "side");
            return (Criteria) this;
        }

        public Criteria andSideNotEqualTo(String value) {
            addCriterion("\"side\" <>", value, "side");
            return (Criteria) this;
        }

        public Criteria andSideGreaterThan(String value) {
            addCriterion("\"side\" >", value, "side");
            return (Criteria) this;
        }

        public Criteria andSideGreaterThanOrEqualTo(String value) {
            addCriterion("\"side\" >=", value, "side");
            return (Criteria) this;
        }

        public Criteria andSideLessThan(String value) {
            addCriterion("\"side\" <", value, "side");
            return (Criteria) this;
        }

        public Criteria andSideLessThanOrEqualTo(String value) {
            addCriterion("\"side\" <=", value, "side");
            return (Criteria) this;
        }

        public Criteria andSideLike(String value) {
            addCriterion("\"side\" like", value, "side");
            return (Criteria) this;
        }

        public Criteria andSideNotLike(String value) {
            addCriterion("\"side\" not like", value, "side");
            return (Criteria) this;
        }

        public Criteria andSideIn(List<String> values) {
            addCriterion("\"side\" in", values, "side");
            return (Criteria) this;
        }

        public Criteria andSideNotIn(List<String> values) {
            addCriterion("\"side\" not in", values, "side");
            return (Criteria) this;
        }

        public Criteria andSideBetween(String value1, String value2) {
            addCriterion("\"side\" between", value1, value2, "side");
            return (Criteria) this;
        }

        public Criteria andSideNotBetween(String value1, String value2) {
            addCriterion("\"side\" not between", value1, value2, "side");
            return (Criteria) this;
        }

        public Criteria andVesselNameIsNull() {
            addCriterion("\"vessel_name\" is null");
            return (Criteria) this;
        }

        public Criteria andVesselNameIsNotNull() {
            addCriterion("\"vessel_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andVesselNameEqualTo(String value) {
            addCriterion("\"vessel_name\" =", value, "vesselName");
            return (Criteria) this;
        }

        public Criteria andVesselNameNotEqualTo(String value) {
            addCriterion("\"vessel_name\" <>", value, "vesselName");
            return (Criteria) this;
        }

        public Criteria andVesselNameGreaterThan(String value) {
            addCriterion("\"vessel_name\" >", value, "vesselName");
            return (Criteria) this;
        }

        public Criteria andVesselNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"vessel_name\" >=", value, "vesselName");
            return (Criteria) this;
        }

        public Criteria andVesselNameLessThan(String value) {
            addCriterion("\"vessel_name\" <", value, "vesselName");
            return (Criteria) this;
        }

        public Criteria andVesselNameLessThanOrEqualTo(String value) {
            addCriterion("\"vessel_name\" <=", value, "vesselName");
            return (Criteria) this;
        }

        public Criteria andVesselNameLike(String value) {
            addCriterion("\"vessel_name\" like", value, "vesselName");
            return (Criteria) this;
        }

        public Criteria andVesselNameNotLike(String value) {
            addCriterion("\"vessel_name\" not like", value, "vesselName");
            return (Criteria) this;
        }

        public Criteria andVesselNameIn(List<String> values) {
            addCriterion("\"vessel_name\" in", values, "vesselName");
            return (Criteria) this;
        }

        public Criteria andVesselNameNotIn(List<String> values) {
            addCriterion("\"vessel_name\" not in", values, "vesselName");
            return (Criteria) this;
        }

        public Criteria andVesselNameBetween(String value1, String value2) {
            addCriterion("\"vessel_name\" between", value1, value2, "vesselName");
            return (Criteria) this;
        }

        public Criteria andVesselNameNotBetween(String value1, String value2) {
            addCriterion("\"vessel_name\" not between", value1, value2, "vesselName");
            return (Criteria) this;
        }

        public Criteria andSegmentNameIsNull() {
            addCriterion("\"segment_name\" is null");
            return (Criteria) this;
        }

        public Criteria andSegmentNameIsNotNull() {
            addCriterion("\"segment_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andSegmentNameEqualTo(String value) {
            addCriterion("\"segment_name\" =", value, "segmentName");
            return (Criteria) this;
        }

        public Criteria andSegmentNameNotEqualTo(String value) {
            addCriterion("\"segment_name\" <>", value, "segmentName");
            return (Criteria) this;
        }

        public Criteria andSegmentNameGreaterThan(String value) {
            addCriterion("\"segment_name\" >", value, "segmentName");
            return (Criteria) this;
        }

        public Criteria andSegmentNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"segment_name\" >=", value, "segmentName");
            return (Criteria) this;
        }

        public Criteria andSegmentNameLessThan(String value) {
            addCriterion("\"segment_name\" <", value, "segmentName");
            return (Criteria) this;
        }

        public Criteria andSegmentNameLessThanOrEqualTo(String value) {
            addCriterion("\"segment_name\" <=", value, "segmentName");
            return (Criteria) this;
        }

        public Criteria andSegmentNameLike(String value) {
            addCriterion("\"segment_name\" like", value, "segmentName");
            return (Criteria) this;
        }

        public Criteria andSegmentNameNotLike(String value) {
            addCriterion("\"segment_name\" not like", value, "segmentName");
            return (Criteria) this;
        }

        public Criteria andSegmentNameIn(List<String> values) {
            addCriterion("\"segment_name\" in", values, "segmentName");
            return (Criteria) this;
        }

        public Criteria andSegmentNameNotIn(List<String> values) {
            addCriterion("\"segment_name\" not in", values, "segmentName");
            return (Criteria) this;
        }

        public Criteria andSegmentNameBetween(String value1, String value2) {
            addCriterion("\"segment_name\" between", value1, value2, "segmentName");
            return (Criteria) this;
        }

        public Criteria andSegmentNameNotBetween(String value1, String value2) {
            addCriterion("\"segment_name\" not between", value1, value2, "segmentName");
            return (Criteria) this;
        }

        public Criteria andNarrowDegreeIsNull() {
            addCriterion("\"narrow_degree\" is null");
            return (Criteria) this;
        }

        public Criteria andNarrowDegreeIsNotNull() {
            addCriterion("\"narrow_degree\" is not null");
            return (Criteria) this;
        }

        public Criteria andNarrowDegreeEqualTo(String value) {
            addCriterion("\"narrow_degree\" =", value, "narrowDegree");
            return (Criteria) this;
        }

        public Criteria andNarrowDegreeNotEqualTo(String value) {
            addCriterion("\"narrow_degree\" <>", value, "narrowDegree");
            return (Criteria) this;
        }

        public Criteria andNarrowDegreeGreaterThan(String value) {
            addCriterion("\"narrow_degree\" >", value, "narrowDegree");
            return (Criteria) this;
        }

        public Criteria andNarrowDegreeGreaterThanOrEqualTo(String value) {
            addCriterion("\"narrow_degree\" >=", value, "narrowDegree");
            return (Criteria) this;
        }

        public Criteria andNarrowDegreeLessThan(String value) {
            addCriterion("\"narrow_degree\" <", value, "narrowDegree");
            return (Criteria) this;
        }

        public Criteria andNarrowDegreeLessThanOrEqualTo(String value) {
            addCriterion("\"narrow_degree\" <=", value, "narrowDegree");
            return (Criteria) this;
        }

        public Criteria andNarrowDegreeLike(String value) {
            addCriterion("\"narrow_degree\" like", value, "narrowDegree");
            return (Criteria) this;
        }

        public Criteria andNarrowDegreeNotLike(String value) {
            addCriterion("\"narrow_degree\" not like", value, "narrowDegree");
            return (Criteria) this;
        }

        public Criteria andNarrowDegreeIn(List<String> values) {
            addCriterion("\"narrow_degree\" in", values, "narrowDegree");
            return (Criteria) this;
        }

        public Criteria andNarrowDegreeNotIn(List<String> values) {
            addCriterion("\"narrow_degree\" not in", values, "narrowDegree");
            return (Criteria) this;
        }

        public Criteria andNarrowDegreeBetween(String value1, String value2) {
            addCriterion("\"narrow_degree\" between", value1, value2, "narrowDegree");
            return (Criteria) this;
        }

        public Criteria andNarrowDegreeNotBetween(String value1, String value2) {
            addCriterion("\"narrow_degree\" not between", value1, value2, "narrowDegree");
            return (Criteria) this;
        }

        public Criteria andNarrowRateIsNull() {
            addCriterion("\"narrow_rate\" is null");
            return (Criteria) this;
        }

        public Criteria andNarrowRateIsNotNull() {
            addCriterion("\"narrow_rate\" is not null");
            return (Criteria) this;
        }

        public Criteria andNarrowRateEqualTo(String value) {
            addCriterion("\"narrow_rate\" =", value, "narrowRate");
            return (Criteria) this;
        }

        public Criteria andNarrowRateNotEqualTo(String value) {
            addCriterion("\"narrow_rate\" <>", value, "narrowRate");
            return (Criteria) this;
        }

        public Criteria andNarrowRateGreaterThan(String value) {
            addCriterion("\"narrow_rate\" >", value, "narrowRate");
            return (Criteria) this;
        }

        public Criteria andNarrowRateGreaterThanOrEqualTo(String value) {
            addCriterion("\"narrow_rate\" >=", value, "narrowRate");
            return (Criteria) this;
        }

        public Criteria andNarrowRateLessThan(String value) {
            addCriterion("\"narrow_rate\" <", value, "narrowRate");
            return (Criteria) this;
        }

        public Criteria andNarrowRateLessThanOrEqualTo(String value) {
            addCriterion("\"narrow_rate\" <=", value, "narrowRate");
            return (Criteria) this;
        }

        public Criteria andNarrowRateLike(String value) {
            addCriterion("\"narrow_rate\" like", value, "narrowRate");
            return (Criteria) this;
        }

        public Criteria andNarrowRateNotLike(String value) {
            addCriterion("\"narrow_rate\" not like", value, "narrowRate");
            return (Criteria) this;
        }

        public Criteria andNarrowRateIn(List<String> values) {
            addCriterion("\"narrow_rate\" in", values, "narrowRate");
            return (Criteria) this;
        }

        public Criteria andNarrowRateNotIn(List<String> values) {
            addCriterion("\"narrow_rate\" not in", values, "narrowRate");
            return (Criteria) this;
        }

        public Criteria andNarrowRateBetween(String value1, String value2) {
            addCriterion("\"narrow_rate\" between", value1, value2, "narrowRate");
            return (Criteria) this;
        }

        public Criteria andNarrowRateNotBetween(String value1, String value2) {
            addCriterion("\"narrow_rate\" not between", value1, value2, "narrowRate");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}