package com.haoys.disease.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

public class StkInfo implements Serializable {
    private String pkid;

    @ApiModelProperty(value = "部位")
    private String part;

    @ApiModelProperty(value = "侧边")
    private String side;

    @ApiModelProperty(value = "是否为陈旧性梗塞灶")
    private String isOldStk;

    @ApiModelProperty(value = "是否为腔隙性梗塞灶")
    private String isLacunarStk;

    private String patientSn;

    private String visitSn;

    private static final long serialVersionUID = 1L;

    public String getPkid() {
        return pkid;
    }

    public void setPkid(String pkid) {
        this.pkid = pkid;
    }

    public String getPart() {
        return part;
    }

    public void setPart(String part) {
        this.part = part;
    }

    public String getSide() {
        return side;
    }

    public void setSide(String side) {
        this.side = side;
    }

    public String getIsOldStk() {
        return isOldStk;
    }

    public void setIsOldStk(String isOldStk) {
        this.isOldStk = isOldStk;
    }

    public String getIsLacunarStk() {
        return isLacunarStk;
    }

    public void setIsLacunarStk(String isLacunarStk) {
        this.isLacunarStk = isLacunarStk;
    }

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }

    public String getVisitSn() {
        return visitSn;
    }

    public void setVisitSn(String visitSn) {
        this.visitSn = visitSn;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", pkid=").append(pkid);
        sb.append(", part=").append(part);
        sb.append(", side=").append(side);
        sb.append(", isOldStk=").append(isOldStk);
        sb.append(", isLacunarStk=").append(isLacunarStk);
        sb.append(", patientSn=").append(patientSn);
        sb.append(", visitSn=").append(visitSn);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}