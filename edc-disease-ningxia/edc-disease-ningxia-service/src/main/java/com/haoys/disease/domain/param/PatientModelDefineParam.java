package com.haoys.disease.domain.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class PatientModelDefineParam {

    @ApiModelProperty(value = "表单来源标识")
    private String modelSourceId;

    @ApiModelProperty(value = "表单模型code-含基本定义表")
    private String modelCode;

    @ApiModelProperty(value = "表单模型名称")
    private String modelName;

    @ApiModelProperty(value = "是否自定义表单")
    private Boolean customModel;

    @ApiModelProperty(value = "字段排序")
    private Integer sort;

    @ApiModelProperty(value = "扩展字段")
    private String expand;

    @ApiModelProperty(value = "1-删除 0-正常")
    private Boolean deleted;

    @ApiModelProperty(value = "是否创建基本表单模型")
    private Boolean createBaseModelTable = false;

    @ApiModelProperty(value = "模型变量集合")
    private List<PatientModelVariableParam> modelVariableParamList = new ArrayList<>();




}
