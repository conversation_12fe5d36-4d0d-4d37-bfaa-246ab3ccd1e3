package com.haoys.disease.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class PatientModelDefine implements Serializable {
    @ApiModelProperty(value = "表单模型id")
    private String modelSourceId;

    @ApiModelProperty(value = "表单模型code")
    private String modelSourceCode;

    @ApiModelProperty(value = "表单模型名称")
    private String modelSourceName;

    @ApiModelProperty(value = "表单字段parentCode")
    private String parentCode;

    @ApiModelProperty(value = "是否自定义表单")
    private Short customModel;

    @ApiModelProperty(value = "表单模型缩写")
    private String abbreviateCode;

    @ApiModelProperty(value = "字段排序")
    private Integer sort;

    @ApiModelProperty(value = "启用、停用状态")
    private Boolean enabled;

    @ApiModelProperty(value = "扩展字段")
    private String expand;

    @ApiModelProperty(value = "创建时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private static final long serialVersionUID = 1L;

    public String getModelSourceId() {
        return modelSourceId;
    }

    public void setModelSourceId(String modelSourceId) {
        this.modelSourceId = modelSourceId;
    }

    public String getModelSourceCode() {
        return modelSourceCode;
    }

    public void setModelSourceCode(String modelSourceCode) {
        this.modelSourceCode = modelSourceCode;
    }

    public String getModelSourceName() {
        return modelSourceName;
    }

    public void setModelSourceName(String modelSourceName) {
        this.modelSourceName = modelSourceName;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public Short getCustomModel() {
        return customModel;
    }

    public void setCustomModel(Short customModel) {
        this.customModel = customModel;
    }

    public String getAbbreviateCode() {
        return abbreviateCode;
    }

    public void setAbbreviateCode(String abbreviateCode) {
        this.abbreviateCode = abbreviateCode;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getExpand() {
        return expand;
    }

    public void setExpand(String expand) {
        this.expand = expand;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", modelSourceId=").append(modelSourceId);
        sb.append(", modelSourceCode=").append(modelSourceCode);
        sb.append(", modelSourceName=").append(modelSourceName);
        sb.append(", parentCode=").append(parentCode);
        sb.append(", customModel=").append(customModel);
        sb.append(", abbreviateCode=").append(abbreviateCode);
        sb.append(", sort=").append(sort);
        sb.append(", enabled=").append(enabled);
        sb.append(", expand=").append(expand);
        sb.append(", createTime=").append(createTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}