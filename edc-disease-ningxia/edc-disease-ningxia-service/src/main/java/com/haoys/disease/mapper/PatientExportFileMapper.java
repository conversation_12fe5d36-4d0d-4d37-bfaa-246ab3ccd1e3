package com.haoys.disease.mapper;

import com.haoys.disease.model.PatientExportFile;
import com.haoys.disease.model.PatientExportFileExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface PatientExportFileMapper {
    long countByExample(PatientExportFileExample example);

    int deleteByExample(PatientExportFileExample example);

    int insert(PatientExportFile record);

    int insertSelective(PatientExportFile record);

    List<PatientExportFile> selectByExample(PatientExportFileExample example);

    int updateByExampleSelective(@Param("record") PatientExportFile record, @Param("example") PatientExportFileExample example);

    int updateByExample(@Param("record") PatientExportFile record, @Param("example") PatientExportFileExample example);
}