package com.haoys.disease.model;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class PatientVisitInfo implements Serializable {
    @ApiModelProperty(value = "就诊挂号日期")
    private Date registerdttm;

    @ApiModelProperty(value = "患者id")
    private String patientId;

    @ApiModelProperty(value = "就诊流水号")
    private String visitSerialNumber;

    @ApiModelProperty(value = "就诊类型 100001-门诊，100002住院，100003急诊，100004体检")
    private String visitType;

    @ApiModelProperty(value = "科室code")
    private String departmentCode;

    @ApiModelProperty(value = "科室名称")
    private String departmentName;

    @ApiModelProperty(value = "病区code")
    private String wardCode;

    @ApiModelProperty(value = "病区名称")
    private String wardName;

    @ApiModelProperty(value = "床位号/床位名称")
    private String bedCode;

    @ApiModelProperty(value = "住院日期")
    private Date admitdttm;

    @ApiModelProperty(value = "出院日期")
    private Date dischargedttm;

    @ApiModelProperty(value = "门诊医生code")
    private String consultingmdCode;

    @ApiModelProperty(value = "门诊医生名称")
    private String consultingmdName;

    @ApiModelProperty(value = "主治医生code")
    private String attendingmdCode;

    @ApiModelProperty(value = "主治医生代码名称")
    private String attendingmdName;

    @ApiModelProperty(value = "转归情况	病人就诊结局情况，好转、恶化、病亡等")
    private String outcome;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    private static final long serialVersionUID = 1L;

    public Date getRegisterdttm() {
        return registerdttm;
    }

    public void setRegisterdttm(Date registerdttm) {
        this.registerdttm = registerdttm;
    }

    public String getPatientId() {
        return patientId;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public String getVisitSerialNumber() {
        return visitSerialNumber;
    }

    public void setVisitSerialNumber(String visitSerialNumber) {
        this.visitSerialNumber = visitSerialNumber;
    }

    public String getVisitType() {
        return visitType;
    }

    public void setVisitType(String visitType) {
        this.visitType = visitType;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getWardCode() {
        return wardCode;
    }

    public void setWardCode(String wardCode) {
        this.wardCode = wardCode;
    }

    public String getWardName() {
        return wardName;
    }

    public void setWardName(String wardName) {
        this.wardName = wardName;
    }

    public String getBedCode() {
        return bedCode;
    }

    public void setBedCode(String bedCode) {
        this.bedCode = bedCode;
    }

    public Date getAdmitdttm() {
        return admitdttm;
    }

    public void setAdmitdttm(Date admitdttm) {
        this.admitdttm = admitdttm;
    }

    public Date getDischargedttm() {
        return dischargedttm;
    }

    public void setDischargedttm(Date dischargedttm) {
        this.dischargedttm = dischargedttm;
    }

    public String getConsultingmdCode() {
        return consultingmdCode;
    }

    public void setConsultingmdCode(String consultingmdCode) {
        this.consultingmdCode = consultingmdCode;
    }

    public String getConsultingmdName() {
        return consultingmdName;
    }

    public void setConsultingmdName(String consultingmdName) {
        this.consultingmdName = consultingmdName;
    }

    public String getAttendingmdCode() {
        return attendingmdCode;
    }

    public void setAttendingmdCode(String attendingmdCode) {
        this.attendingmdCode = attendingmdCode;
    }

    public String getAttendingmdName() {
        return attendingmdName;
    }

    public void setAttendingmdName(String attendingmdName) {
        this.attendingmdName = attendingmdName;
    }

    public String getOutcome() {
        return outcome;
    }

    public void setOutcome(String outcome) {
        this.outcome = outcome;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", registerdttm=").append(registerdttm);
        sb.append(", patientId=").append(patientId);
        sb.append(", visitSerialNumber=").append(visitSerialNumber);
        sb.append(", visitType=").append(visitType);
        sb.append(", departmentCode=").append(departmentCode);
        sb.append(", departmentName=").append(departmentName);
        sb.append(", wardCode=").append(wardCode);
        sb.append(", wardName=").append(wardName);
        sb.append(", bedCode=").append(bedCode);
        sb.append(", admitdttm=").append(admitdttm);
        sb.append(", dischargedttm=").append(dischargedttm);
        sb.append(", consultingmdCode=").append(consultingmdCode);
        sb.append(", consultingmdName=").append(consultingmdName);
        sb.append(", attendingmdCode=").append(attendingmdCode);
        sb.append(", attendingmdName=").append(attendingmdName);
        sb.append(", outcome=").append(outcome);
        sb.append(", createTime=").append(createTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}