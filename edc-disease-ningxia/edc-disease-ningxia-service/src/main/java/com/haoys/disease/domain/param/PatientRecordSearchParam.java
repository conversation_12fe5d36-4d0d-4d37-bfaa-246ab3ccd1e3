package com.haoys.disease.domain.param;

import com.haoys.user.common.core.domain.vo.BaseVo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class PatientRecordSearchParam extends BaseVo {

    private String dataFrom = "disease";
    private String searchType = "";
    private String dataBaseId = "";
    private String dataSetId = "";
    private String modelSourceCode;
    private String searchWord;
    private String querySegment = "";
    private String startDate = "";
    private String endDate = "";
    private Boolean exportPatientRecord = false;

    private List<SearcherModeRule> dataList = new ArrayList<>();

    private String sortField = "patientSn";
    private String sortType = "asc";
    
    private long totalCount;

    @Data
    public static class SearcherModeRule {
        // 取值 AND OR NOT
        private String operatorValue;
        private String modelSourceCode;
        private String variableCode;
        private String searchWord;
        private String variableType;
        // 取值 accurate fuzzy
        private String queryConfig;
    }

}
