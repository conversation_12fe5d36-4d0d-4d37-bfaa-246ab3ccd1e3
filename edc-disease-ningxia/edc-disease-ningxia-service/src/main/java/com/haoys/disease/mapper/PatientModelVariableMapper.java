package com.haoys.disease.mapper;

import com.haoys.disease.model.PatientModelVariable;
import com.haoys.disease.model.PatientModelVariableExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PatientModelVariableMapper {
    long countByExample(PatientModelVariableExample example);

    int deleteByExample(PatientModelVariableExample example);

    int deleteByPrimaryKey(String id);

    int insert(PatientModelVariable record);

    int insertSelective(PatientModelVariable record);

    List<PatientModelVariable> selectByExample(PatientModelVariableExample example);

    PatientModelVariable selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") PatientModelVariable record, @Param("example") PatientModelVariableExample example);

    int updateByExample(@Param("record") PatientModelVariable record, @Param("example") PatientModelVariableExample example);

    int updateByPrimaryKeySelective(PatientModelVariable record);

    int updateByPrimaryKey(PatientModelVariable record);
    
    void updatePatientModelVariable(List<PatientModelVariable> patientModelVariableList);
    
    void truncateTable();
}