package com.haoys.disease.domain.wrapper;

import com.haoys.disease.model.CerebralHemorrhageInfo;
import com.haoys.disease.model.HeadNeckImagingExam;
import com.haoys.disease.model.HemadostenosisInfo;
import com.haoys.disease.model.StkInfo;
import lombok.Data;

import java.util.List;

@Data
public class HeadNeckImagingExamWrapper extends HeadNeckImagingExam {
    private List<CerebralHemorrhageInfo> cerebralHemorrhageInfoList;
    private List<HemadostenosisInfo> hemadostenosisInfoList;
    private List<StkInfo> stkInfoList;
}
