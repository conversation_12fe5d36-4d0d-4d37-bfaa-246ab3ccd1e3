package com.haoys.disease.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AssessScaleExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public AssessScaleExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andPkidIsNull() {
            addCriterion("\"pkid\" is null");
            return (Criteria) this;
        }

        public Criteria andPkidIsNotNull() {
            addCriterion("\"pkid\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkidEqualTo(String value) {
            addCriterion("\"pkid\" =", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotEqualTo(String value) {
            addCriterion("\"pkid\" <>", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThan(String value) {
            addCriterion("\"pkid\" >", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThanOrEqualTo(String value) {
            addCriterion("\"pkid\" >=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThan(String value) {
            addCriterion("\"pkid\" <", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThanOrEqualTo(String value) {
            addCriterion("\"pkid\" <=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLike(String value) {
            addCriterion("\"pkid\" like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotLike(String value) {
            addCriterion("\"pkid\" not like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidIn(List<String> values) {
            addCriterion("\"pkid\" in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotIn(List<String> values) {
            addCriterion("\"pkid\" not in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidBetween(String value1, String value2) {
            addCriterion("\"pkid\" between", value1, value2, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotBetween(String value1, String value2) {
            addCriterion("\"pkid\" not between", value1, value2, "pkid");
            return (Criteria) this;
        }

        public Criteria andNihssScoreIsNull() {
            addCriterion("\"nihss_score\" is null");
            return (Criteria) this;
        }

        public Criteria andNihssScoreIsNotNull() {
            addCriterion("\"nihss_score\" is not null");
            return (Criteria) this;
        }

        public Criteria andNihssScoreEqualTo(Double value) {
            addCriterion("\"nihss_score\" =", value, "nihssScore");
            return (Criteria) this;
        }

        public Criteria andNihssScoreNotEqualTo(Double value) {
            addCriterion("\"nihss_score\" <>", value, "nihssScore");
            return (Criteria) this;
        }

        public Criteria andNihssScoreGreaterThan(Double value) {
            addCriterion("\"nihss_score\" >", value, "nihssScore");
            return (Criteria) this;
        }

        public Criteria andNihssScoreGreaterThanOrEqualTo(Double value) {
            addCriterion("\"nihss_score\" >=", value, "nihssScore");
            return (Criteria) this;
        }

        public Criteria andNihssScoreLessThan(Double value) {
            addCriterion("\"nihss_score\" <", value, "nihssScore");
            return (Criteria) this;
        }

        public Criteria andNihssScoreLessThanOrEqualTo(Double value) {
            addCriterion("\"nihss_score\" <=", value, "nihssScore");
            return (Criteria) this;
        }

        public Criteria andNihssScoreIn(List<Double> values) {
            addCriterion("\"nihss_score\" in", values, "nihssScore");
            return (Criteria) this;
        }

        public Criteria andNihssScoreNotIn(List<Double> values) {
            addCriterion("\"nihss_score\" not in", values, "nihssScore");
            return (Criteria) this;
        }

        public Criteria andNihssScoreBetween(Double value1, Double value2) {
            addCriterion("\"nihss_score\" between", value1, value2, "nihssScore");
            return (Criteria) this;
        }

        public Criteria andNihssScoreNotBetween(Double value1, Double value2) {
            addCriterion("\"nihss_score\" not between", value1, value2, "nihssScore");
            return (Criteria) this;
        }

        public Criteria andMrsScoreIsNull() {
            addCriterion("\"mrs_score\" is null");
            return (Criteria) this;
        }

        public Criteria andMrsScoreIsNotNull() {
            addCriterion("\"mrs_score\" is not null");
            return (Criteria) this;
        }

        public Criteria andMrsScoreEqualTo(Double value) {
            addCriterion("\"mrs_score\" =", value, "mrsScore");
            return (Criteria) this;
        }

        public Criteria andMrsScoreNotEqualTo(Double value) {
            addCriterion("\"mrs_score\" <>", value, "mrsScore");
            return (Criteria) this;
        }

        public Criteria andMrsScoreGreaterThan(Double value) {
            addCriterion("\"mrs_score\" >", value, "mrsScore");
            return (Criteria) this;
        }

        public Criteria andMrsScoreGreaterThanOrEqualTo(Double value) {
            addCriterion("\"mrs_score\" >=", value, "mrsScore");
            return (Criteria) this;
        }

        public Criteria andMrsScoreLessThan(Double value) {
            addCriterion("\"mrs_score\" <", value, "mrsScore");
            return (Criteria) this;
        }

        public Criteria andMrsScoreLessThanOrEqualTo(Double value) {
            addCriterion("\"mrs_score\" <=", value, "mrsScore");
            return (Criteria) this;
        }

        public Criteria andMrsScoreIn(List<Double> values) {
            addCriterion("\"mrs_score\" in", values, "mrsScore");
            return (Criteria) this;
        }

        public Criteria andMrsScoreNotIn(List<Double> values) {
            addCriterion("\"mrs_score\" not in", values, "mrsScore");
            return (Criteria) this;
        }

        public Criteria andMrsScoreBetween(Double value1, Double value2) {
            addCriterion("\"mrs_score\" between", value1, value2, "mrsScore");
            return (Criteria) this;
        }

        public Criteria andMrsScoreNotBetween(Double value1, Double value2) {
            addCriterion("\"mrs_score\" not between", value1, value2, "mrsScore");
            return (Criteria) this;
        }

        public Criteria andEssenScoreIsNull() {
            addCriterion("\"essen_score\" is null");
            return (Criteria) this;
        }

        public Criteria andEssenScoreIsNotNull() {
            addCriterion("\"essen_score\" is not null");
            return (Criteria) this;
        }

        public Criteria andEssenScoreEqualTo(Double value) {
            addCriterion("\"essen_score\" =", value, "essenScore");
            return (Criteria) this;
        }

        public Criteria andEssenScoreNotEqualTo(Double value) {
            addCriterion("\"essen_score\" <>", value, "essenScore");
            return (Criteria) this;
        }

        public Criteria andEssenScoreGreaterThan(Double value) {
            addCriterion("\"essen_score\" >", value, "essenScore");
            return (Criteria) this;
        }

        public Criteria andEssenScoreGreaterThanOrEqualTo(Double value) {
            addCriterion("\"essen_score\" >=", value, "essenScore");
            return (Criteria) this;
        }

        public Criteria andEssenScoreLessThan(Double value) {
            addCriterion("\"essen_score\" <", value, "essenScore");
            return (Criteria) this;
        }

        public Criteria andEssenScoreLessThanOrEqualTo(Double value) {
            addCriterion("\"essen_score\" <=", value, "essenScore");
            return (Criteria) this;
        }

        public Criteria andEssenScoreIn(List<Double> values) {
            addCriterion("\"essen_score\" in", values, "essenScore");
            return (Criteria) this;
        }

        public Criteria andEssenScoreNotIn(List<Double> values) {
            addCriterion("\"essen_score\" not in", values, "essenScore");
            return (Criteria) this;
        }

        public Criteria andEssenScoreBetween(Double value1, Double value2) {
            addCriterion("\"essen_score\" between", value1, value2, "essenScore");
            return (Criteria) this;
        }

        public Criteria andEssenScoreNotBetween(Double value1, Double value2) {
            addCriterion("\"essen_score\" not between", value1, value2, "essenScore");
            return (Criteria) this;
        }

        public Criteria andWaterSwallowTestGradeIsNull() {
            addCriterion("\"water_swallow_test_grade\" is null");
            return (Criteria) this;
        }

        public Criteria andWaterSwallowTestGradeIsNotNull() {
            addCriterion("\"water_swallow_test_grade\" is not null");
            return (Criteria) this;
        }

        public Criteria andWaterSwallowTestGradeEqualTo(String value) {
            addCriterion("\"water_swallow_test_grade\" =", value, "waterSwallowTestGrade");
            return (Criteria) this;
        }

        public Criteria andWaterSwallowTestGradeNotEqualTo(String value) {
            addCriterion("\"water_swallow_test_grade\" <>", value, "waterSwallowTestGrade");
            return (Criteria) this;
        }

        public Criteria andWaterSwallowTestGradeGreaterThan(String value) {
            addCriterion("\"water_swallow_test_grade\" >", value, "waterSwallowTestGrade");
            return (Criteria) this;
        }

        public Criteria andWaterSwallowTestGradeGreaterThanOrEqualTo(String value) {
            addCriterion("\"water_swallow_test_grade\" >=", value, "waterSwallowTestGrade");
            return (Criteria) this;
        }

        public Criteria andWaterSwallowTestGradeLessThan(String value) {
            addCriterion("\"water_swallow_test_grade\" <", value, "waterSwallowTestGrade");
            return (Criteria) this;
        }

        public Criteria andWaterSwallowTestGradeLessThanOrEqualTo(String value) {
            addCriterion("\"water_swallow_test_grade\" <=", value, "waterSwallowTestGrade");
            return (Criteria) this;
        }

        public Criteria andWaterSwallowTestGradeLike(String value) {
            addCriterion("\"water_swallow_test_grade\" like", value, "waterSwallowTestGrade");
            return (Criteria) this;
        }

        public Criteria andWaterSwallowTestGradeNotLike(String value) {
            addCriterion("\"water_swallow_test_grade\" not like", value, "waterSwallowTestGrade");
            return (Criteria) this;
        }

        public Criteria andWaterSwallowTestGradeIn(List<String> values) {
            addCriterion("\"water_swallow_test_grade\" in", values, "waterSwallowTestGrade");
            return (Criteria) this;
        }

        public Criteria andWaterSwallowTestGradeNotIn(List<String> values) {
            addCriterion("\"water_swallow_test_grade\" not in", values, "waterSwallowTestGrade");
            return (Criteria) this;
        }

        public Criteria andWaterSwallowTestGradeBetween(String value1, String value2) {
            addCriterion("\"water_swallow_test_grade\" between", value1, value2, "waterSwallowTestGrade");
            return (Criteria) this;
        }

        public Criteria andWaterSwallowTestGradeNotBetween(String value1, String value2) {
            addCriterion("\"water_swallow_test_grade\" not between", value1, value2, "waterSwallowTestGrade");
            return (Criteria) this;
        }

        public Criteria andLeftUpperLimbMyodynamiaGradeIsNull() {
            addCriterion("\"left_upper_limb_myodynamia_grade\" is null");
            return (Criteria) this;
        }

        public Criteria andLeftUpperLimbMyodynamiaGradeIsNotNull() {
            addCriterion("\"left_upper_limb_myodynamia_grade\" is not null");
            return (Criteria) this;
        }

        public Criteria andLeftUpperLimbMyodynamiaGradeEqualTo(String value) {
            addCriterion("\"left_upper_limb_myodynamia_grade\" =", value, "leftUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftUpperLimbMyodynamiaGradeNotEqualTo(String value) {
            addCriterion("\"left_upper_limb_myodynamia_grade\" <>", value, "leftUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftUpperLimbMyodynamiaGradeGreaterThan(String value) {
            addCriterion("\"left_upper_limb_myodynamia_grade\" >", value, "leftUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftUpperLimbMyodynamiaGradeGreaterThanOrEqualTo(String value) {
            addCriterion("\"left_upper_limb_myodynamia_grade\" >=", value, "leftUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftUpperLimbMyodynamiaGradeLessThan(String value) {
            addCriterion("\"left_upper_limb_myodynamia_grade\" <", value, "leftUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftUpperLimbMyodynamiaGradeLessThanOrEqualTo(String value) {
            addCriterion("\"left_upper_limb_myodynamia_grade\" <=", value, "leftUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftUpperLimbMyodynamiaGradeLike(String value) {
            addCriterion("\"left_upper_limb_myodynamia_grade\" like", value, "leftUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftUpperLimbMyodynamiaGradeNotLike(String value) {
            addCriterion("\"left_upper_limb_myodynamia_grade\" not like", value, "leftUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftUpperLimbMyodynamiaGradeIn(List<String> values) {
            addCriterion("\"left_upper_limb_myodynamia_grade\" in", values, "leftUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftUpperLimbMyodynamiaGradeNotIn(List<String> values) {
            addCriterion("\"left_upper_limb_myodynamia_grade\" not in", values, "leftUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftUpperLimbMyodynamiaGradeBetween(String value1, String value2) {
            addCriterion("\"left_upper_limb_myodynamia_grade\" between", value1, value2, "leftUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftUpperLimbMyodynamiaGradeNotBetween(String value1, String value2) {
            addCriterion("\"left_upper_limb_myodynamia_grade\" not between", value1, value2, "leftUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftLowerLimbMyodynamiaGradeIsNull() {
            addCriterion("\"left_lower_limb_myodynamia_grade\" is null");
            return (Criteria) this;
        }

        public Criteria andLeftLowerLimbMyodynamiaGradeIsNotNull() {
            addCriterion("\"left_lower_limb_myodynamia_grade\" is not null");
            return (Criteria) this;
        }

        public Criteria andLeftLowerLimbMyodynamiaGradeEqualTo(String value) {
            addCriterion("\"left_lower_limb_myodynamia_grade\" =", value, "leftLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftLowerLimbMyodynamiaGradeNotEqualTo(String value) {
            addCriterion("\"left_lower_limb_myodynamia_grade\" <>", value, "leftLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftLowerLimbMyodynamiaGradeGreaterThan(String value) {
            addCriterion("\"left_lower_limb_myodynamia_grade\" >", value, "leftLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftLowerLimbMyodynamiaGradeGreaterThanOrEqualTo(String value) {
            addCriterion("\"left_lower_limb_myodynamia_grade\" >=", value, "leftLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftLowerLimbMyodynamiaGradeLessThan(String value) {
            addCriterion("\"left_lower_limb_myodynamia_grade\" <", value, "leftLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftLowerLimbMyodynamiaGradeLessThanOrEqualTo(String value) {
            addCriterion("\"left_lower_limb_myodynamia_grade\" <=", value, "leftLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftLowerLimbMyodynamiaGradeLike(String value) {
            addCriterion("\"left_lower_limb_myodynamia_grade\" like", value, "leftLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftLowerLimbMyodynamiaGradeNotLike(String value) {
            addCriterion("\"left_lower_limb_myodynamia_grade\" not like", value, "leftLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftLowerLimbMyodynamiaGradeIn(List<String> values) {
            addCriterion("\"left_lower_limb_myodynamia_grade\" in", values, "leftLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftLowerLimbMyodynamiaGradeNotIn(List<String> values) {
            addCriterion("\"left_lower_limb_myodynamia_grade\" not in", values, "leftLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftLowerLimbMyodynamiaGradeBetween(String value1, String value2) {
            addCriterion("\"left_lower_limb_myodynamia_grade\" between", value1, value2, "leftLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andLeftLowerLimbMyodynamiaGradeNotBetween(String value1, String value2) {
            addCriterion("\"left_lower_limb_myodynamia_grade\" not between", value1, value2, "leftLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightUpperLimbMyodynamiaGradeIsNull() {
            addCriterion("\"right_upper_limb_myodynamia_grade\" is null");
            return (Criteria) this;
        }

        public Criteria andRightUpperLimbMyodynamiaGradeIsNotNull() {
            addCriterion("\"right_upper_limb_myodynamia_grade\" is not null");
            return (Criteria) this;
        }

        public Criteria andRightUpperLimbMyodynamiaGradeEqualTo(String value) {
            addCriterion("\"right_upper_limb_myodynamia_grade\" =", value, "rightUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightUpperLimbMyodynamiaGradeNotEqualTo(String value) {
            addCriterion("\"right_upper_limb_myodynamia_grade\" <>", value, "rightUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightUpperLimbMyodynamiaGradeGreaterThan(String value) {
            addCriterion("\"right_upper_limb_myodynamia_grade\" >", value, "rightUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightUpperLimbMyodynamiaGradeGreaterThanOrEqualTo(String value) {
            addCriterion("\"right_upper_limb_myodynamia_grade\" >=", value, "rightUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightUpperLimbMyodynamiaGradeLessThan(String value) {
            addCriterion("\"right_upper_limb_myodynamia_grade\" <", value, "rightUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightUpperLimbMyodynamiaGradeLessThanOrEqualTo(String value) {
            addCriterion("\"right_upper_limb_myodynamia_grade\" <=", value, "rightUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightUpperLimbMyodynamiaGradeLike(String value) {
            addCriterion("\"right_upper_limb_myodynamia_grade\" like", value, "rightUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightUpperLimbMyodynamiaGradeNotLike(String value) {
            addCriterion("\"right_upper_limb_myodynamia_grade\" not like", value, "rightUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightUpperLimbMyodynamiaGradeIn(List<String> values) {
            addCriterion("\"right_upper_limb_myodynamia_grade\" in", values, "rightUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightUpperLimbMyodynamiaGradeNotIn(List<String> values) {
            addCriterion("\"right_upper_limb_myodynamia_grade\" not in", values, "rightUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightUpperLimbMyodynamiaGradeBetween(String value1, String value2) {
            addCriterion("\"right_upper_limb_myodynamia_grade\" between", value1, value2, "rightUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightUpperLimbMyodynamiaGradeNotBetween(String value1, String value2) {
            addCriterion("\"right_upper_limb_myodynamia_grade\" not between", value1, value2, "rightUpperLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightLowerLimbMyodynamiaGradeIsNull() {
            addCriterion("\"right_lower_limb_myodynamia_grade\" is null");
            return (Criteria) this;
        }

        public Criteria andRightLowerLimbMyodynamiaGradeIsNotNull() {
            addCriterion("\"right_lower_limb_myodynamia_grade\" is not null");
            return (Criteria) this;
        }

        public Criteria andRightLowerLimbMyodynamiaGradeEqualTo(String value) {
            addCriterion("\"right_lower_limb_myodynamia_grade\" =", value, "rightLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightLowerLimbMyodynamiaGradeNotEqualTo(String value) {
            addCriterion("\"right_lower_limb_myodynamia_grade\" <>", value, "rightLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightLowerLimbMyodynamiaGradeGreaterThan(String value) {
            addCriterion("\"right_lower_limb_myodynamia_grade\" >", value, "rightLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightLowerLimbMyodynamiaGradeGreaterThanOrEqualTo(String value) {
            addCriterion("\"right_lower_limb_myodynamia_grade\" >=", value, "rightLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightLowerLimbMyodynamiaGradeLessThan(String value) {
            addCriterion("\"right_lower_limb_myodynamia_grade\" <", value, "rightLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightLowerLimbMyodynamiaGradeLessThanOrEqualTo(String value) {
            addCriterion("\"right_lower_limb_myodynamia_grade\" <=", value, "rightLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightLowerLimbMyodynamiaGradeLike(String value) {
            addCriterion("\"right_lower_limb_myodynamia_grade\" like", value, "rightLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightLowerLimbMyodynamiaGradeNotLike(String value) {
            addCriterion("\"right_lower_limb_myodynamia_grade\" not like", value, "rightLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightLowerLimbMyodynamiaGradeIn(List<String> values) {
            addCriterion("\"right_lower_limb_myodynamia_grade\" in", values, "rightLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightLowerLimbMyodynamiaGradeNotIn(List<String> values) {
            addCriterion("\"right_lower_limb_myodynamia_grade\" not in", values, "rightLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightLowerLimbMyodynamiaGradeBetween(String value1, String value2) {
            addCriterion("\"right_lower_limb_myodynamia_grade\" between", value1, value2, "rightLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andRightLowerLimbMyodynamiaGradeNotBetween(String value1, String value2) {
            addCriterion("\"right_lower_limb_myodynamia_grade\" not between", value1, value2, "rightLowerLimbMyodynamiaGrade");
            return (Criteria) this;
        }

        public Criteria andAssessDatetimeIsNull() {
            addCriterion("\"assess_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andAssessDatetimeIsNotNull() {
            addCriterion("\"assess_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andAssessDatetimeEqualTo(Date value) {
            addCriterion("\"assess_datetime\" =", value, "assessDatetime");
            return (Criteria) this;
        }

        public Criteria andAssessDatetimeNotEqualTo(Date value) {
            addCriterion("\"assess_datetime\" <>", value, "assessDatetime");
            return (Criteria) this;
        }

        public Criteria andAssessDatetimeGreaterThan(Date value) {
            addCriterion("\"assess_datetime\" >", value, "assessDatetime");
            return (Criteria) this;
        }

        public Criteria andAssessDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"assess_datetime\" >=", value, "assessDatetime");
            return (Criteria) this;
        }

        public Criteria andAssessDatetimeLessThan(Date value) {
            addCriterion("\"assess_datetime\" <", value, "assessDatetime");
            return (Criteria) this;
        }

        public Criteria andAssessDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"assess_datetime\" <=", value, "assessDatetime");
            return (Criteria) this;
        }

        public Criteria andAssessDatetimeIn(List<Date> values) {
            addCriterion("\"assess_datetime\" in", values, "assessDatetime");
            return (Criteria) this;
        }

        public Criteria andAssessDatetimeNotIn(List<Date> values) {
            addCriterion("\"assess_datetime\" not in", values, "assessDatetime");
            return (Criteria) this;
        }

        public Criteria andAssessDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"assess_datetime\" between", value1, value2, "assessDatetime");
            return (Criteria) this;
        }

        public Criteria andAssessDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"assess_datetime\" not between", value1, value2, "assessDatetime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}