package com.haoys.disease.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ExamRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ExamRecordExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andPkidIsNull() {
            addCriterion("\"pkid\" is null");
            return (Criteria) this;
        }

        public Criteria andPkidIsNotNull() {
            addCriterion("\"pkid\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkidEqualTo(String value) {
            addCriterion("\"pkid\" =", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotEqualTo(String value) {
            addCriterion("\"pkid\" <>", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThan(String value) {
            addCriterion("\"pkid\" >", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThanOrEqualTo(String value) {
            addCriterion("\"pkid\" >=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThan(String value) {
            addCriterion("\"pkid\" <", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThanOrEqualTo(String value) {
            addCriterion("\"pkid\" <=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLike(String value) {
            addCriterion("\"pkid\" like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotLike(String value) {
            addCriterion("\"pkid\" not like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidIn(List<String> values) {
            addCriterion("\"pkid\" in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotIn(List<String> values) {
            addCriterion("\"pkid\" not in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidBetween(String value1, String value2) {
            addCriterion("\"pkid\" between", value1, value2, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotBetween(String value1, String value2) {
            addCriterion("\"pkid\" not between", value1, value2, "pkid");
            return (Criteria) this;
        }

        public Criteria andReqDatetimeIsNull() {
            addCriterion("\"req_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andReqDatetimeIsNotNull() {
            addCriterion("\"req_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andReqDatetimeEqualTo(Date value) {
            addCriterion("\"req_datetime\" =", value, "reqDatetime");
            return (Criteria) this;
        }

        public Criteria andReqDatetimeNotEqualTo(Date value) {
            addCriterion("\"req_datetime\" <>", value, "reqDatetime");
            return (Criteria) this;
        }

        public Criteria andReqDatetimeGreaterThan(Date value) {
            addCriterion("\"req_datetime\" >", value, "reqDatetime");
            return (Criteria) this;
        }

        public Criteria andReqDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"req_datetime\" >=", value, "reqDatetime");
            return (Criteria) this;
        }

        public Criteria andReqDatetimeLessThan(Date value) {
            addCriterion("\"req_datetime\" <", value, "reqDatetime");
            return (Criteria) this;
        }

        public Criteria andReqDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"req_datetime\" <=", value, "reqDatetime");
            return (Criteria) this;
        }

        public Criteria andReqDatetimeIn(List<Date> values) {
            addCriterion("\"req_datetime\" in", values, "reqDatetime");
            return (Criteria) this;
        }

        public Criteria andReqDatetimeNotIn(List<Date> values) {
            addCriterion("\"req_datetime\" not in", values, "reqDatetime");
            return (Criteria) this;
        }

        public Criteria andReqDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"req_datetime\" between", value1, value2, "reqDatetime");
            return (Criteria) this;
        }

        public Criteria andReqDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"req_datetime\" not between", value1, value2, "reqDatetime");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeIsNull() {
            addCriterion("\"exam_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeIsNotNull() {
            addCriterion("\"exam_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeEqualTo(Date value) {
            addCriterion("\"exam_datetime\" =", value, "examDatetime");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeNotEqualTo(Date value) {
            addCriterion("\"exam_datetime\" <>", value, "examDatetime");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeGreaterThan(Date value) {
            addCriterion("\"exam_datetime\" >", value, "examDatetime");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"exam_datetime\" >=", value, "examDatetime");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeLessThan(Date value) {
            addCriterion("\"exam_datetime\" <", value, "examDatetime");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"exam_datetime\" <=", value, "examDatetime");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeIn(List<Date> values) {
            addCriterion("\"exam_datetime\" in", values, "examDatetime");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeNotIn(List<Date> values) {
            addCriterion("\"exam_datetime\" not in", values, "examDatetime");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"exam_datetime\" between", value1, value2, "examDatetime");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"exam_datetime\" not between", value1, value2, "examDatetime");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeIsNull() {
            addCriterion("\"report_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeIsNotNull() {
            addCriterion("\"report_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeEqualTo(Date value) {
            addCriterion("\"report_datetime\" =", value, "reportDatetime");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeNotEqualTo(Date value) {
            addCriterion("\"report_datetime\" <>", value, "reportDatetime");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeGreaterThan(Date value) {
            addCriterion("\"report_datetime\" >", value, "reportDatetime");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"report_datetime\" >=", value, "reportDatetime");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeLessThan(Date value) {
            addCriterion("\"report_datetime\" <", value, "reportDatetime");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"report_datetime\" <=", value, "reportDatetime");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeIn(List<Date> values) {
            addCriterion("\"report_datetime\" in", values, "reportDatetime");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeNotIn(List<Date> values) {
            addCriterion("\"report_datetime\" not in", values, "reportDatetime");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"report_datetime\" between", value1, value2, "reportDatetime");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"report_datetime\" not between", value1, value2, "reportDatetime");
            return (Criteria) this;
        }

        public Criteria andExamIdIsNull() {
            addCriterion("\"exam_id\" is null");
            return (Criteria) this;
        }

        public Criteria andExamIdIsNotNull() {
            addCriterion("\"exam_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andExamIdEqualTo(String value) {
            addCriterion("\"exam_id\" =", value, "examId");
            return (Criteria) this;
        }

        public Criteria andExamIdNotEqualTo(String value) {
            addCriterion("\"exam_id\" <>", value, "examId");
            return (Criteria) this;
        }

        public Criteria andExamIdGreaterThan(String value) {
            addCriterion("\"exam_id\" >", value, "examId");
            return (Criteria) this;
        }

        public Criteria andExamIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"exam_id\" >=", value, "examId");
            return (Criteria) this;
        }

        public Criteria andExamIdLessThan(String value) {
            addCriterion("\"exam_id\" <", value, "examId");
            return (Criteria) this;
        }

        public Criteria andExamIdLessThanOrEqualTo(String value) {
            addCriterion("\"exam_id\" <=", value, "examId");
            return (Criteria) this;
        }

        public Criteria andExamIdLike(String value) {
            addCriterion("\"exam_id\" like", value, "examId");
            return (Criteria) this;
        }

        public Criteria andExamIdNotLike(String value) {
            addCriterion("\"exam_id\" not like", value, "examId");
            return (Criteria) this;
        }

        public Criteria andExamIdIn(List<String> values) {
            addCriterion("\"exam_id\" in", values, "examId");
            return (Criteria) this;
        }

        public Criteria andExamIdNotIn(List<String> values) {
            addCriterion("\"exam_id\" not in", values, "examId");
            return (Criteria) this;
        }

        public Criteria andExamIdBetween(String value1, String value2) {
            addCriterion("\"exam_id\" between", value1, value2, "examId");
            return (Criteria) this;
        }

        public Criteria andExamIdNotBetween(String value1, String value2) {
            addCriterion("\"exam_id\" not between", value1, value2, "examId");
            return (Criteria) this;
        }

        public Criteria andExamClassIsNull() {
            addCriterion("\"exam_class\" is null");
            return (Criteria) this;
        }

        public Criteria andExamClassIsNotNull() {
            addCriterion("\"exam_class\" is not null");
            return (Criteria) this;
        }

        public Criteria andExamClassEqualTo(String value) {
            addCriterion("\"exam_class\" =", value, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamClassNotEqualTo(String value) {
            addCriterion("\"exam_class\" <>", value, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamClassGreaterThan(String value) {
            addCriterion("\"exam_class\" >", value, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamClassGreaterThanOrEqualTo(String value) {
            addCriterion("\"exam_class\" >=", value, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamClassLessThan(String value) {
            addCriterion("\"exam_class\" <", value, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamClassLessThanOrEqualTo(String value) {
            addCriterion("\"exam_class\" <=", value, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamClassLike(String value) {
            addCriterion("\"exam_class\" like", value, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamClassNotLike(String value) {
            addCriterion("\"exam_class\" not like", value, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamClassIn(List<String> values) {
            addCriterion("\"exam_class\" in", values, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamClassNotIn(List<String> values) {
            addCriterion("\"exam_class\" not in", values, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamClassBetween(String value1, String value2) {
            addCriterion("\"exam_class\" between", value1, value2, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamClassNotBetween(String value1, String value2) {
            addCriterion("\"exam_class\" not between", value1, value2, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamNameIsNull() {
            addCriterion("\"exam_name\" is null");
            return (Criteria) this;
        }

        public Criteria andExamNameIsNotNull() {
            addCriterion("\"exam_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andExamNameEqualTo(String value) {
            addCriterion("\"exam_name\" =", value, "examName");
            return (Criteria) this;
        }

        public Criteria andExamNameNotEqualTo(String value) {
            addCriterion("\"exam_name\" <>", value, "examName");
            return (Criteria) this;
        }

        public Criteria andExamNameGreaterThan(String value) {
            addCriterion("\"exam_name\" >", value, "examName");
            return (Criteria) this;
        }

        public Criteria andExamNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"exam_name\" >=", value, "examName");
            return (Criteria) this;
        }

        public Criteria andExamNameLessThan(String value) {
            addCriterion("\"exam_name\" <", value, "examName");
            return (Criteria) this;
        }

        public Criteria andExamNameLessThanOrEqualTo(String value) {
            addCriterion("\"exam_name\" <=", value, "examName");
            return (Criteria) this;
        }

        public Criteria andExamNameLike(String value) {
            addCriterion("\"exam_name\" like", value, "examName");
            return (Criteria) this;
        }

        public Criteria andExamNameNotLike(String value) {
            addCriterion("\"exam_name\" not like", value, "examName");
            return (Criteria) this;
        }

        public Criteria andExamNameIn(List<String> values) {
            addCriterion("\"exam_name\" in", values, "examName");
            return (Criteria) this;
        }

        public Criteria andExamNameNotIn(List<String> values) {
            addCriterion("\"exam_name\" not in", values, "examName");
            return (Criteria) this;
        }

        public Criteria andExamNameBetween(String value1, String value2) {
            addCriterion("\"exam_name\" between", value1, value2, "examName");
            return (Criteria) this;
        }

        public Criteria andExamNameNotBetween(String value1, String value2) {
            addCriterion("\"exam_name\" not between", value1, value2, "examName");
            return (Criteria) this;
        }

        public Criteria andExamFindingIsNull() {
            addCriterion("\"exam_finding\" is null");
            return (Criteria) this;
        }

        public Criteria andExamFindingIsNotNull() {
            addCriterion("\"exam_finding\" is not null");
            return (Criteria) this;
        }

        public Criteria andExamFindingEqualTo(String value) {
            addCriterion("\"exam_finding\" =", value, "examFinding");
            return (Criteria) this;
        }

        public Criteria andExamFindingNotEqualTo(String value) {
            addCriterion("\"exam_finding\" <>", value, "examFinding");
            return (Criteria) this;
        }

        public Criteria andExamFindingGreaterThan(String value) {
            addCriterion("\"exam_finding\" >", value, "examFinding");
            return (Criteria) this;
        }

        public Criteria andExamFindingGreaterThanOrEqualTo(String value) {
            addCriterion("\"exam_finding\" >=", value, "examFinding");
            return (Criteria) this;
        }

        public Criteria andExamFindingLessThan(String value) {
            addCriterion("\"exam_finding\" <", value, "examFinding");
            return (Criteria) this;
        }

        public Criteria andExamFindingLessThanOrEqualTo(String value) {
            addCriterion("\"exam_finding\" <=", value, "examFinding");
            return (Criteria) this;
        }

        public Criteria andExamFindingLike(String value) {
            addCriterion("\"exam_finding\" like", value, "examFinding");
            return (Criteria) this;
        }

        public Criteria andExamFindingNotLike(String value) {
            addCriterion("\"exam_finding\" not like", value, "examFinding");
            return (Criteria) this;
        }

        public Criteria andExamFindingIn(List<String> values) {
            addCriterion("\"exam_finding\" in", values, "examFinding");
            return (Criteria) this;
        }

        public Criteria andExamFindingNotIn(List<String> values) {
            addCriterion("\"exam_finding\" not in", values, "examFinding");
            return (Criteria) this;
        }

        public Criteria andExamFindingBetween(String value1, String value2) {
            addCriterion("\"exam_finding\" between", value1, value2, "examFinding");
            return (Criteria) this;
        }

        public Criteria andExamFindingNotBetween(String value1, String value2) {
            addCriterion("\"exam_finding\" not between", value1, value2, "examFinding");
            return (Criteria) this;
        }

        public Criteria andExamConclusionIsNull() {
            addCriterion("\"exam_conclusion\" is null");
            return (Criteria) this;
        }

        public Criteria andExamConclusionIsNotNull() {
            addCriterion("\"exam_conclusion\" is not null");
            return (Criteria) this;
        }

        public Criteria andExamConclusionEqualTo(String value) {
            addCriterion("\"exam_conclusion\" =", value, "examConclusion");
            return (Criteria) this;
        }

        public Criteria andExamConclusionNotEqualTo(String value) {
            addCriterion("\"exam_conclusion\" <>", value, "examConclusion");
            return (Criteria) this;
        }

        public Criteria andExamConclusionGreaterThan(String value) {
            addCriterion("\"exam_conclusion\" >", value, "examConclusion");
            return (Criteria) this;
        }

        public Criteria andExamConclusionGreaterThanOrEqualTo(String value) {
            addCriterion("\"exam_conclusion\" >=", value, "examConclusion");
            return (Criteria) this;
        }

        public Criteria andExamConclusionLessThan(String value) {
            addCriterion("\"exam_conclusion\" <", value, "examConclusion");
            return (Criteria) this;
        }

        public Criteria andExamConclusionLessThanOrEqualTo(String value) {
            addCriterion("\"exam_conclusion\" <=", value, "examConclusion");
            return (Criteria) this;
        }

        public Criteria andExamConclusionLike(String value) {
            addCriterion("\"exam_conclusion\" like", value, "examConclusion");
            return (Criteria) this;
        }

        public Criteria andExamConclusionNotLike(String value) {
            addCriterion("\"exam_conclusion\" not like", value, "examConclusion");
            return (Criteria) this;
        }

        public Criteria andExamConclusionIn(List<String> values) {
            addCriterion("\"exam_conclusion\" in", values, "examConclusion");
            return (Criteria) this;
        }

        public Criteria andExamConclusionNotIn(List<String> values) {
            addCriterion("\"exam_conclusion\" not in", values, "examConclusion");
            return (Criteria) this;
        }

        public Criteria andExamConclusionBetween(String value1, String value2) {
            addCriterion("\"exam_conclusion\" between", value1, value2, "examConclusion");
            return (Criteria) this;
        }

        public Criteria andExamConclusionNotBetween(String value1, String value2) {
            addCriterion("\"exam_conclusion\" not between", value1, value2, "examConclusion");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}