package com.haoys.disease.domain.param;

import com.haoys.disease.domain.dto.CustomSearchDto;
import com.haoys.disease.domain.dto.CustomSearchHeadDto;
import com.haoys.user.common.core.domain.vo.BaseVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SearchExportParam extends BaseVo {

    @ApiModelProperty(value = "搜索配置id")
    private String searchId;

    @ApiModelProperty(value = "导出名称")
    private String fileName;

    @ApiModelProperty(value = "导出类型")
    private String exportType;

    @ApiModelProperty(value = "搜索条件")
    List<CustomSearchDto> searchList;

    @ApiModelProperty(value = "导出字段集合")
    List<CustomSearchHeadDto> exportList;

    @ApiModelProperty(value = "搜索条件")
    List<String> patientIds;

}



