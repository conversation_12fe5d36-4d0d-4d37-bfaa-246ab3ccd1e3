package com.haoys.disease.mapper;

import com.haoys.disease.model.CerebralHemorrhageInfo;
import com.haoys.disease.model.CerebralHemorrhageInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CerebralHemorrhageInfoMapper {
    long countByExample(CerebralHemorrhageInfoExample example);

    int deleteByExample(CerebralHemorrhageInfoExample example);

    int insert(CerebralHemorrhageInfo record);

    int insertSelective(CerebralHemorrhageInfo record);

    List<CerebralHemorrhageInfo> selectByExample(CerebralHemorrhageInfoExample example);

    int updateByExampleSelective(@Param("record") CerebralHemorrhageInfo record, @Param("example") CerebralHemorrhageInfoExample example);

    int updateByExample(@Param("record") CerebralHemorrhageInfo record, @Param("example") CerebralHemorrhageInfoExample example);
}