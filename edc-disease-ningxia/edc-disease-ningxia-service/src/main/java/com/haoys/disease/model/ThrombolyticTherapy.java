package com.haoys.disease.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class ThrombolyticTherapy implements Serializable {
    @ApiModelProperty(value = "患者标识")
    private String patientSn;

    @ApiModelProperty(value = "就诊标识")
    private String visitSn;

    private String pkid;

    @ApiModelProperty(value = "药品通用名")
    private String genericName;

    @ApiModelProperty(value = "药物名称")
    private String drugName;

    @ApiModelProperty(value = "治疗开始时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDatetime;

    @ApiModelProperty(value = "治疗结束时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDatetime;

    @ApiModelProperty(value = "单次剂量")
    private String dosage;

    @ApiModelProperty(value = "剂量单位")
    private String dosageUnits;

    @ApiModelProperty(value = "用药频次")
    private String frequency;

    @ApiModelProperty(value = "给药途径")
    private String administrationRoute;

    @ApiModelProperty(value = "医嘱类别")
    private String orderClass;

    private static final long serialVersionUID = 1L;

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }

    public String getVisitSn() {
        return visitSn;
    }

    public void setVisitSn(String visitSn) {
        this.visitSn = visitSn;
    }

    public String getPkid() {
        return pkid;
    }

    public void setPkid(String pkid) {
        this.pkid = pkid;
    }

    public String getGenericName() {
        return genericName;
    }

    public void setGenericName(String genericName) {
        this.genericName = genericName;
    }

    public String getDrugName() {
        return drugName;
    }

    public void setDrugName(String drugName) {
        this.drugName = drugName;
    }

    public Date getStartDatetime() {
        return startDatetime;
    }

    public void setStartDatetime(Date startDatetime) {
        this.startDatetime = startDatetime;
    }

    public Date getEndDatetime() {
        return endDatetime;
    }

    public void setEndDatetime(Date endDatetime) {
        this.endDatetime = endDatetime;
    }

    public String getDosage() {
        return dosage;
    }

    public void setDosage(String dosage) {
        this.dosage = dosage;
    }

    public String getDosageUnits() {
        return dosageUnits;
    }

    public void setDosageUnits(String dosageUnits) {
        this.dosageUnits = dosageUnits;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public String getAdministrationRoute() {
        return administrationRoute;
    }

    public void setAdministrationRoute(String administrationRoute) {
        this.administrationRoute = administrationRoute;
    }

    public String getOrderClass() {
        return orderClass;
    }

    public void setOrderClass(String orderClass) {
        this.orderClass = orderClass;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", patientSn=").append(patientSn);
        sb.append(", visitSn=").append(visitSn);
        sb.append(", pkid=").append(pkid);
        sb.append(", genericName=").append(genericName);
        sb.append(", drugName=").append(drugName);
        sb.append(", startDatetime=").append(startDatetime);
        sb.append(", endDatetime=").append(endDatetime);
        sb.append(", dosage=").append(dosage);
        sb.append(", dosageUnits=").append(dosageUnits);
        sb.append(", frequency=").append(frequency);
        sb.append(", administrationRoute=").append(administrationRoute);
        sb.append(", orderClass=").append(orderClass);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}