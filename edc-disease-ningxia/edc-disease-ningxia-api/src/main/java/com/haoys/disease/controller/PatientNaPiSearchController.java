package com.haoys.disease.controller;


import com.haoys.disease.domain.dto.PatientNaPiSearchDto;
import com.haoys.disease.domain.param.SearchExportParam;
import com.haoys.disease.domain.param.SearchParam;
import com.haoys.disease.model.PatientExportFile;
import com.haoys.disease.model.PatientNaPiSearch;
import com.haoys.disease.service.PatientExportFileService;
import com.haoys.disease.service.PatientNaPiSearchV1Service;
import com.haoys.disease.service.PatientNaPiSearchV2Service;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.core.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Api(tags = "专病库数据库-纳排搜索条件")
@RequestMapping("/naPiSearch")
public class PatientNaPiSearchController extends BaseController {

    @Autowired
    private PatientNaPiSearchV1Service naPiSearchService;

    @Autowired
    private PatientExportFileService patientExportFileService;

    @Autowired
    private PatientNaPiSearchV2Service naPiSearchV2Service;

    /**
     * 搜索下拉列表
     *
     * @return
     */
    @ApiOperation("纳排-搜索下拉列表")
    @GetMapping(value = "searchList")
    /**
     * diseaseType 病种
     */
    public CommonResult<List<PatientNaPiSearch>> searchList() {
        return naPiSearchService.searchList();
    }



    /**
     * 纳排-列表
     *
     * @return
     */
    @ApiOperation("纳排-列表")
    @PostMapping(value = "list")
    public CommonResult list(@RequestBody SearchParam param) {
        return naPiSearchV2Service.list(param);
    }


    /**
     * 保存/更新搜索条件
     *
     * @return
     */
    @ApiOperation("纳排-保存/更新搜索条件")
    @PostMapping(value = "su")
    public CommonResult<Object> su(@RequestBody PatientNaPiSearchDto dto) {
        return naPiSearchService.saveOrUpdateSearch(dto);
    }


    /**
     * 删除搜索条件
     *
     * @return
     */
    @ApiOperation("纳排-删除搜索条件")
    @GetMapping(value = "del")
    public CommonResult<Object> del(String id) {
        return naPiSearchService.remove(id);
    }


    /**
     * 删除搜索条件
     *
     * @return
     */
    @ApiOperation("纳排-getWhere")
    @GetMapping(value = "getWhere")
    public CommonResult<String> getWhere(String id) {
        PatientNaPiSearch search = naPiSearchService.getById(id);
        if (search==null){
            return CommonResult.failed();
        }
        return CommonResult.success(naPiSearchService.getWhere(search));
    }


    /**
     * 导出
     *
     * @return
     */
    @ApiOperation("纳排-export")
    @PostMapping(value = "export")
    public CommonResult<String> export(@RequestBody SearchExportParam exportParam) {
        naPiSearchService.export(exportParam);
        return CommonResult.success("");
    }


    /**
     * 下载列表
     *
     * @return
     */
    @ApiOperation("纳排-下载列表")
    @GetMapping(value = "exportList")
    public CommonResult<List<PatientExportFile>> exportList() {
        List<PatientExportFile> list = patientExportFileService.exportList();
        return CommonResult.success(list);
    }

    /**
     * 下载列表
     *
     * @return
     */
    @ApiOperation("纳排-下载删除")
    @GetMapping(value = "removeFile")
    public CommonResult<Object> removeFile(String id) {
        return patientExportFileService.removeFile(id);
    }


    /**
     * 下载列表
     *
     * @return
     */
    @ApiOperation("纳排-已下载")
    @GetMapping(value = "alreadyDown")
    public CommonResult<Object> alreadyDown(String id) {
        return patientExportFileService.alreadyDown(id);
    }
}
