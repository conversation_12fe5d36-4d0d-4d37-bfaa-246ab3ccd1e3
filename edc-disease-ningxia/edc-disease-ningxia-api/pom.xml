<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>disease-ningxia</groupId>
        <artifactId>edc-disease-ningxia</artifactId>
        <version>1.0.0</version>
    </parent>

    <groupId>com.haoys.edc</groupId>
    <artifactId>edc-disease-ningxia-api</artifactId>
    
    <packaging>jar</packaging>

    <name>edc-disease-ningxia-api</name>
    <description>edc-disease-project</description>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.haoys.edc</groupId>
            <artifactId>edc-disease-ningxia-service</artifactId>
            
        </dependency>
        <dependency>
            <groupId>com.haoys.edc</groupId>
            <artifactId>edc-disease-ningxia-business</artifactId>
            
        </dependency>
        <dependency>
            <groupId>com.haoys.edc</groupId>
            <artifactId>edc-user-security</artifactId>
            
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>