package com.haoys.rdr;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RdrModelFormDefine {

    /**表单模型定义*/
    PATIENT_MODEL_CODE_01("patients","患者登记信息"),
    PATIENT_MODEL_CODE_02("pathology_report","病理"),
    PATIENT_MODEL_CODE_03("mr_homepage","病案首页"),
    PATIENT_MODEL_CODE_04("mr_homepage_fee","病案首页费用"),
    PATIENT_MODEL_CODE_05("emr_admission_record","入院记录"),

    PATIENT_MODEL_CODE_06("inp_visit","住院患者主记录"),
    PATIENT_MODEL_CODE_07("inp_order","住院医嘱"),
    PATIENT_MODEL_CODE_08("exam_report","检查报告"),
    PATIENT_MODEL_CODE_09("emr_oper_record","手术记录"),
    PATIENT_MODEL_CODE_10("emr_first_course","首次病程记录"),
    PATIENT_MODEL_CODE_11("emr_discharge_record","出院记录"),
    PATIENT_MODEL_CODE_12("emr_course_record","病程记录"),
    PATIENT_MODEL_CODE_13("lab_master","检验主记录"),
    PATIENT_MODEL_CODE_14("lab_result","检验结果");

    private String code;
    private String name;





}
