package com.haoys.rdr.controller;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.elasticsearch.TreeSearchParam;
import com.haoys.rdr.service.RdrPatientTreeSearchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Map;

@RestController
@Api(tags = "检索-导出功能")
@RequestMapping("/rdr-export")
public class RdrPatientExportController extends BaseController {

    @Resource
    private RdrPatientTreeSearchService treeSearchService;


    @ApiOperation("导出功能")
    @PostMapping(value = "export")
    public CommonResult<Map<String, Object>> export(@RequestBody TreeSearchParam param) throws IOException {
        return treeSearchService.export(param);
    }

}
