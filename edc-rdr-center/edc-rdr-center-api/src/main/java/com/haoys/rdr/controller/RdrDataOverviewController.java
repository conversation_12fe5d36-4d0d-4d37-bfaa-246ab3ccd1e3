package com.haoys.rdr.controller;

import com.haoys.user.common.api.CommonResult;
import com.haoys.rdr.domain.param.CountPatientToAgeParam;
import com.haoys.rdr.domain.param.CountPatientToAgeWrapperParam;
import com.haoys.rdr.domain.vo.CountPatientDiseaseTypeVo;
import com.haoys.rdr.domain.vo.CountPatientToAreaVo;
import com.haoys.rdr.domain.vo.DataOverViewVo;
import com.haoys.rdr.service.RdrDataOverviewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Api(tags = "数据中心-数据概览")
@RequestMapping("/rdr-dataOverview")
public class RdrDataOverviewController {

    @Autowired
    private RdrDataOverviewService rdrDataOverviewService;

    @ApiOperation("患者统计:入库总病例人数、门诊就诊人数，住院就诊人数，患者性别比例")
    @RequestMapping(value = "/totalPatient", method = RequestMethod.GET)
    public CommonResult<DataOverViewVo> totalPatient(String databaseId){
        return rdrDataOverviewService.getTotalPatientOverView(databaseId);
    }

    @ApiOperation("病种统计")
    @RequestMapping(value = "/getCountPatientToDiseaseType", method = RequestMethod.GET)
    public CommonResult<List<CountPatientDiseaseTypeVo>> countPatientToDiseaseType(String databaseId){
        return CommonResult.success(rdrDataOverviewService.countPatientToDiseaseType(databaseId));
    }


//    @ApiOperation("药物治疗统计")
//    @GetMapping(value = "/getCountMedication")
//    public CommonResult<List<CountMedicationInfoVo>> countMedication(){
//        return rdrDataOverviewService.countMedication();
//    }

    @ApiOperation("季节性患者就诊人数")
    @GetMapping(value = "/getCountQuarterPatient")
    public CommonResult<DataOverViewVo.QuarterPatientCount> countQuarterPatient(){
        return rdrDataOverviewService.countQuarterPatient();
    }

    @ApiOperation("患者年龄段分布图")
    @PostMapping(value = "/getCountPatientToAge")
    public CommonResult<List<CountPatientToAgeParam>> countPatientToAge(@RequestBody CountPatientToAgeWrapperParam countPatientToAgeWrapperParam){
        return rdrDataOverviewService.countPatientToAge(countPatientToAgeWrapperParam);
    }

    @ApiOperation("患者区域分布图")
    @GetMapping(value = "/getCountPatientToAre")
    public CommonResult<List<CountPatientToAreaVo>> countPatientToAre(){
        return rdrDataOverviewService.countPatientToAre();
    }

    @ApiOperation("最近一年患者趋势图")
    @RequestMapping(value = "/getCountPatientToYear", method = RequestMethod.GET)
    public CommonResult<DataOverViewVo.CountPatientToYearVo> countPatientToYear(String databaseId,String year){
        return CommonResult.success(rdrDataOverviewService.countPatientToYear(databaseId,year));
    }
}
