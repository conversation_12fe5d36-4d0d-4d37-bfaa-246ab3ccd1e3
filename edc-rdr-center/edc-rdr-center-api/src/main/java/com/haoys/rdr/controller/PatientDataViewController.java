package com.haoys.rdr.controller;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.rdr.domain.vo.PatientDataVo;
import com.haoys.rdr.service.RdrPatientModelRecordService;
import com.haoys.rdr.service.RdrPatientRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Api(tags = "数据中心-患者360视图管理")
@RequestMapping("/patientDataView")
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class PatientDataViewController extends BaseController {

    private final RdrPatientModelRecordService rdrPatientModelRecordService;
    private final RdrPatientRecordService rdrPatientRecordService;

    /**
     * 入院记录视图
     * @param patientId 患者id
     * @return
     */
    @ApiOperation("患者360视图-使用分组字段")
    @GetMapping(value = "/getPatientRecord")
    public CommonResult<List<PatientDataVo>> getPatientRecord(String patientId, String modelSourceCode, String groupBy) {
        return CommonResult.success(rdrPatientModelRecordService.getPatientRecord(patientId,modelSourceCode,groupBy));
    }

}
