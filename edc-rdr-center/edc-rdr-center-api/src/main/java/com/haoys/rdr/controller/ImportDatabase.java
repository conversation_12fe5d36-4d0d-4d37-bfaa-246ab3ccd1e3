package com.haoys.rdr.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class ImportDatabase {

//// xh	desc(名称)	name(列名)	type(数据类型)	require(允许空)	default(默认值)	desc2(说明)
//    public static void main(String[] args) {
//        ExcelReader reader = ExcelUtil.getReader(path);
//        List<Map<String, Object>> readAll = reader.readAll();
//        if (CollectionUtil.isNotEmpty(readAll)){
//            // 表名称
//            String tableName="inp_visit";
//            String sql =" CREATE TABLE "+tableName+" ( ";
//            for (Map<String, Object> map : readAll) {
//                // 字段名称
//                String fieldName= "`"+map.get("name")+"`";
//                // 字段类型
//                String type= (String) map.get("type");
//                if ("string".equals(type)){
//                    type=" varchar(200) ";
//                }
//                // 是否必填
//                String require= (String) map.get("require");
//                if ("否".equals(require)){
//                    require=" not null ";
//                }else {
//                    require=" null ";
//                }
//                 String def =  (String) map.get("default");
//                 def= " DEFAULT '"+def+"' ";
//                // 字段说明
//                String desc= (String) map.get("desc");
//                String desc2= (String) map.get("desc2");
//                if (ObjectUtil.isNotEmpty(desc2)){
//                    desc=desc+":"+desc2;
//                }
//                desc=" COMMENT '" +desc+"';";
//
//                 sql=sql+fieldName+type+require+def+desc;
//            }
//            sql=sql.substring(0,sql.length()-1);
//            sql=sql+" ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;";
//            System.out.println(sql);
//        }
//    }

//    COMMENT ON TABLE example_table IS '这是一个示例表';
//    COMMENT ON COLUMN example_table.id IS '主键';
// 地址
private static String path="/Users/<USER>/北京健康在线开发有限公司/cdm_副本.xlsx";
    // 表名称
    private static String tableName="nursing_record";
    private static String tableComment="护理记录";
    /*
    nursing_record	护理记录
    nursing_vital_signs	生命体征记录
    nursing_care_in_out	出入量记录
    inp_order_exe	住院医嘱执行
    oper_anesthesia	手术麻醉记录
    pathology_report	病理报告
    */
    public static void main(String[] args) {
        ExcelReader reader = ExcelUtil.getReader(path);
        List<List<Object>> readAll = reader.read();
        if (CollectionUtil.isNotEmpty(readAll)){
            String sql =" CREATE TABLE "+tableName+" ( ";
            String comment =" ";
            for (List<Object> map : readAll) {
                String fieldName= map.get(2).toString().trim();
                if ("patient_id".equals(fieldName)){
                    fieldName="patient_sn";
                }
                // 字段类型
                String type= (String) map.get(3);
                if ("pk_id".equals(fieldName)){
                    type=" varchar(100) COLLATE \"pg_catalog\".\"default\" NOT NULL ";
                } else if ("string".equals(type)){
                    type=" varchar(200) COLLATE \"pg_catalog\".\"default\" ";
                }else {
                    type=" "+type;
                }
                // 是否必填
                String require= (String) map.get(4);
                if ("否".equals(require)){
                    if ("string".equals(type)){
                        type +=" NOT NULL ";
                    }
                }else {
                    require="";
                }
                
                // 字段说明
                String desc= (String) map.get(1);
                sql=sql+fieldName+type+" ,";
                comment+="COMMENT ON COLUMN "+tableName+"."+fieldName+" IS '"+desc+"';";
            }
            sql+=" CONSTRAINT "+tableName+"_pkey PRIMARY KEY (\"pk_id\")";
            sql=sql+" ) ;";
            sql+="COMMENT ON TABLE "+tableName+" IS '"+tableComment+"';";
            sql+=comment;
            log.info("sql:{}",sql);
        }
    }
}

