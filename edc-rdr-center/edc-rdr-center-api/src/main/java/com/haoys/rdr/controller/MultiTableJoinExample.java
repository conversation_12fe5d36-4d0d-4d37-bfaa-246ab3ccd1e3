package com.haoys.rdr.controller;

import com.mongodb.client.AggregateIterable;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Accumulators;
import com.mongodb.client.model.Aggregates;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Projections;
import com.mongodb.client.model.Sorts;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.data.mongodb.MongoExpression;

import java.util.Arrays;

@Slf4j
public class MultiTableJoinExample {
    
    public static void main(String[] args) {
        
        /*MongoClientSettings settings = MongoClientSettings.builder()
                .codecRegistry(CodecRegistries.fromRegistries(
                        MongoClientSettings.getDefaultCodecRegistry(),
                        CodecRegistries.fromCodecs(new PersonCodec())))
                .build();
        
        MongoClient mongoClient = MongoClients.create(settings);
        MongoDatabase database = mongoClient.getDatabase("local");
        MongoCollection<Patients> collection = database.getCollection("patients", Patients.class);*/
        
        MongoClient mongoClient = MongoClients.create("mongodb://192.168.191.94:27017");
        MongoDatabase database = mongoClient.getDatabase("test");
        MongoCollection<Document> patients = database.getCollection("patients");
        MongoCollection<Document> outpVisit = database.getCollection("outp_visit");
        
        Bson condition1 = Filters.and(
                Filters.eq("visitGender", "女"),
                Filters.eq("test_group_items", "梅毒测定")
        );
        Bson condition2 = Filters.or(
                Filters.eq("visitGender", "女"),
                Filters.eq("item_name", "人类免疫缺陷病毒抗体检测")
        );
        //Aggregates.project(Projections.include("name", "gender", "age", "visitCount")),
        //Projections.computed("visitCount", Accumulators.sum("visitCount", "$visitArray.visit_sn")),
        //Aggregates.group("$visitArray.patient_sn", Accumulators.sum("visitCount", 1)),
        AggregateIterable<Document> pipeline = outpVisit.aggregate(
                Arrays.asList(
                        //Aggregates.project(Projections.include("name", "gender", "age", "visitCount")),
                        //Projections.computed("visitCount", Accumulators.sum("visitCount", "$visitArray.visit_sn")),
                        Aggregates.match(Filters.eq("gender", "女")),
                        Aggregates.lookup("patients","patient_sn","patient_sn","visitArray"),
                        Aggregates.unwind("$visitArray"),
                        Aggregates.lookup("lab_master","visit_sn","visit_sn","labMasterArray"),
                        Aggregates.unwind("$labMasterArray"),
                        Aggregates.lookup("lab_result","visit_sn","visit_sn","labResultArray"),
                        Aggregates.unwind("$labResultArray"),
                        
                        //Aggregates.group("$visitArray.patient_sn", Accumulators.sum("visitCount", 1)),
                        Aggregates.group("$visitArray.patient_sn",
                                Accumulators.sum("visitCount", 1),
                                Accumulators.first("name", "$name"),
                                Accumulators.first("patientSn", "$visitArray.patient_sn"),
                                Accumulators.first("visitSn", "$labMasterArray.visit_sn"),
                                Accumulators.first("visitGender", "$visitArray.gender"),
                                Accumulators.first("test_group_items", "$labMasterArray.test_group_items"),
                                Accumulators.first("item_name", "$labResultArray.item_name")
                        ),
                        Aggregates.match(Filters.or(condition1, condition2)),
                        Aggregates.project(
                            Projections.include("patientSn", "visitSn")
                        ),
                        Aggregates.sort(Sorts.ascending("patientSn")),
                        Projections.computed("visitGender", MongoExpression.create("visitArray.gender")),
                        Projections.computed("test_group_items", MongoExpression.create("labMasterArray.test_group_items"))
                )
        ).allowDiskUse(true);
        for (Document document : pipeline) {
            log.info("result document: {}", document.toJson());
        }
        mongoClient.close();
    }
}
