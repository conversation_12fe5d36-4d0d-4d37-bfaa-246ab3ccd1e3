<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.EmrAdmissionRecordMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.EmrAdmissionRecord">
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="tpatno" jdbcType="VARCHAR" property="tpatno" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="admission_date_time" jdbcType="TIMESTAMP" property="admissionDateTime" />
    <result column="pre_diag" jdbcType="VARCHAR" property="preDiag" />
    <result column="pre_diag_date" jdbcType="TIMESTAMP" property="preDiagDate" />
    <result column="pre_diag_doctor" jdbcType="VARCHAR" property="preDiagDoctor" />
    <result column="definite_diag" jdbcType="VARCHAR" property="definiteDiag" />
    <result column="definite_diag_date" jdbcType="TIMESTAMP" property="definiteDiagDate" />
    <result column="definite_diag_doctor" jdbcType="VARCHAR" property="definiteDiagDoctor" />
    <result column="chief_complaint" jdbcType="VARCHAR" property="chiefComplaint" />
    <result column="hy_present" jdbcType="VARCHAR" property="hyPresent" />
    <result column="hy_past" jdbcType="VARCHAR" property="hyPast" />
    <result column="hy_individual" jdbcType="VARCHAR" property="hyIndividual" />
    <result column="hy_menstrual_marriage" jdbcType="VARCHAR" property="hyMenstrualMarriage" />
    <result column="hy_family" jdbcType="VARCHAR" property="hyFamily" />
    <result column="physical_exam" jdbcType="VARCHAR" property="physicalExam" />
    <result column="speciality_exam" jdbcType="VARCHAR" property="specialityExam" />
    <result column="supplementary_exam" jdbcType="VARCHAR" property="supplementaryExam" />
    <result column="record_summary" jdbcType="VARCHAR" property="recordSummary" />
    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="recorder" jdbcType="VARCHAR" property="recorder" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
    <result column="full_text" jdbcType="VARCHAR" property="fullText" />
    <result column="patient_sn_org" jdbcType="VARCHAR" property="patientSnOrg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "hospital_code", "patient_sn", "visit_sn", "tpatno", "name", "admission_date_time",
    "pre_diag", "pre_diag_date", "pre_diag_doctor", "definite_diag", "definite_diag_date",
    "definite_diag_doctor", "chief_complaint", "hy_present", "hy_past", "hy_individual",
    "hy_menstrual_marriage", "hy_family", "physical_exam", "speciality_exam", "supplementary_exam",
    "record_summary", "record_time", "recorder", "source_path", "pk_id", "data_state",
    "full_text", "patient_sn_org"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.EmrAdmissionRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."emr_admission_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "public"."emr_admission_record"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."emr_admission_record"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.EmrAdmissionRecordExample">
    delete from "public"."emr_admission_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.EmrAdmissionRecord">
    insert into "public"."emr_admission_record" ("hospital_code", "patient_sn", "visit_sn",
      "tpatno", "name", "admission_date_time",
      "pre_diag", "pre_diag_date", "pre_diag_doctor",
      "definite_diag", "definite_diag_date", "definite_diag_doctor",
      "chief_complaint", "hy_present", "hy_past",
      "hy_individual", "hy_menstrual_marriage", "hy_family",
      "physical_exam", "speciality_exam", "supplementary_exam",
      "record_summary", "record_time", "recorder",
      "source_path", "pk_id", "data_state",
      "full_text", "patient_sn_org")
    values (#{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR},
      #{tpatno,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{admissionDateTime,jdbcType=TIMESTAMP},
      #{preDiag,jdbcType=VARCHAR}, #{preDiagDate,jdbcType=TIMESTAMP}, #{preDiagDoctor,jdbcType=VARCHAR},
      #{definiteDiag,jdbcType=VARCHAR}, #{definiteDiagDate,jdbcType=TIMESTAMP}, #{definiteDiagDoctor,jdbcType=VARCHAR},
      #{chiefComplaint,jdbcType=VARCHAR}, #{hyPresent,jdbcType=VARCHAR}, #{hyPast,jdbcType=VARCHAR},
      #{hyIndividual,jdbcType=VARCHAR}, #{hyMenstrualMarriage,jdbcType=VARCHAR}, #{hyFamily,jdbcType=VARCHAR},
      #{physicalExam,jdbcType=VARCHAR}, #{specialityExam,jdbcType=VARCHAR}, #{supplementaryExam,jdbcType=VARCHAR},
      #{recordSummary,jdbcType=VARCHAR}, #{recordTime,jdbcType=TIMESTAMP}, #{recorder,jdbcType=VARCHAR},
      #{sourcePath,jdbcType=VARCHAR}, #{pkId,jdbcType=VARCHAR}, #{dataState,jdbcType=VARCHAR},
      #{fullText,jdbcType=VARCHAR}, #{patientSnOrg,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.EmrAdmissionRecord">
    insert into "public"."emr_admission_record"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="tpatno != null">
        "tpatno",
      </if>
      <if test="name != null">
        "name",
      </if>
      <if test="admissionDateTime != null">
        "admission_date_time",
      </if>
      <if test="preDiag != null">
        "pre_diag",
      </if>
      <if test="preDiagDate != null">
        "pre_diag_date",
      </if>
      <if test="preDiagDoctor != null">
        "pre_diag_doctor",
      </if>
      <if test="definiteDiag != null">
        "definite_diag",
      </if>
      <if test="definiteDiagDate != null">
        "definite_diag_date",
      </if>
      <if test="definiteDiagDoctor != null">
        "definite_diag_doctor",
      </if>
      <if test="chiefComplaint != null">
        "chief_complaint",
      </if>
      <if test="hyPresent != null">
        "hy_present",
      </if>
      <if test="hyPast != null">
        "hy_past",
      </if>
      <if test="hyIndividual != null">
        "hy_individual",
      </if>
      <if test="hyMenstrualMarriage != null">
        "hy_menstrual_marriage",
      </if>
      <if test="hyFamily != null">
        "hy_family",
      </if>
      <if test="physicalExam != null">
        "physical_exam",
      </if>
      <if test="specialityExam != null">
        "speciality_exam",
      </if>
      <if test="supplementaryExam != null">
        "supplementary_exam",
      </if>
      <if test="recordSummary != null">
        "record_summary",
      </if>
      <if test="recordTime != null">
        "record_time",
      </if>
      <if test="recorder != null">
        "recorder",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
      <if test="fullText != null">
        "full_text",
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="admissionDateTime != null">
        #{admissionDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="preDiag != null">
        #{preDiag,jdbcType=VARCHAR},
      </if>
      <if test="preDiagDate != null">
        #{preDiagDate,jdbcType=TIMESTAMP},
      </if>
      <if test="preDiagDoctor != null">
        #{preDiagDoctor,jdbcType=VARCHAR},
      </if>
      <if test="definiteDiag != null">
        #{definiteDiag,jdbcType=VARCHAR},
      </if>
      <if test="definiteDiagDate != null">
        #{definiteDiagDate,jdbcType=TIMESTAMP},
      </if>
      <if test="definiteDiagDoctor != null">
        #{definiteDiagDoctor,jdbcType=VARCHAR},
      </if>
      <if test="chiefComplaint != null">
        #{chiefComplaint,jdbcType=VARCHAR},
      </if>
      <if test="hyPresent != null">
        #{hyPresent,jdbcType=VARCHAR},
      </if>
      <if test="hyPast != null">
        #{hyPast,jdbcType=VARCHAR},
      </if>
      <if test="hyIndividual != null">
        #{hyIndividual,jdbcType=VARCHAR},
      </if>
      <if test="hyMenstrualMarriage != null">
        #{hyMenstrualMarriage,jdbcType=VARCHAR},
      </if>
      <if test="hyFamily != null">
        #{hyFamily,jdbcType=VARCHAR},
      </if>
      <if test="physicalExam != null">
        #{physicalExam,jdbcType=VARCHAR},
      </if>
      <if test="specialityExam != null">
        #{specialityExam,jdbcType=VARCHAR},
      </if>
      <if test="supplementaryExam != null">
        #{supplementaryExam,jdbcType=VARCHAR},
      </if>
      <if test="recordSummary != null">
        #{recordSummary,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null">
        #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recorder != null">
        #{recorder,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="fullText != null">
        #{fullText,jdbcType=VARCHAR},
      </if>
      <if test="patientSnOrg != null">
        #{patientSnOrg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.EmrAdmissionRecordExample" resultType="java.lang.Long">
    select count(*) from "public"."emr_admission_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."emr_admission_record"
    <set>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.tpatno != null">
        "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        "name" = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.admissionDateTime != null">
        "admission_date_time" = #{record.admissionDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.preDiag != null">
        "pre_diag" = #{record.preDiag,jdbcType=VARCHAR},
      </if>
      <if test="record.preDiagDate != null">
        "pre_diag_date" = #{record.preDiagDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.preDiagDoctor != null">
        "pre_diag_doctor" = #{record.preDiagDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.definiteDiag != null">
        "definite_diag" = #{record.definiteDiag,jdbcType=VARCHAR},
      </if>
      <if test="record.definiteDiagDate != null">
        "definite_diag_date" = #{record.definiteDiagDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.definiteDiagDoctor != null">
        "definite_diag_doctor" = #{record.definiteDiagDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.chiefComplaint != null">
        "chief_complaint" = #{record.chiefComplaint,jdbcType=VARCHAR},
      </if>
      <if test="record.hyPresent != null">
        "hy_present" = #{record.hyPresent,jdbcType=VARCHAR},
      </if>
      <if test="record.hyPast != null">
        "hy_past" = #{record.hyPast,jdbcType=VARCHAR},
      </if>
      <if test="record.hyIndividual != null">
        "hy_individual" = #{record.hyIndividual,jdbcType=VARCHAR},
      </if>
      <if test="record.hyMenstrualMarriage != null">
        "hy_menstrual_marriage" = #{record.hyMenstrualMarriage,jdbcType=VARCHAR},
      </if>
      <if test="record.hyFamily != null">
        "hy_family" = #{record.hyFamily,jdbcType=VARCHAR},
      </if>
      <if test="record.physicalExam != null">
        "physical_exam" = #{record.physicalExam,jdbcType=VARCHAR},
      </if>
      <if test="record.specialityExam != null">
        "speciality_exam" = #{record.specialityExam,jdbcType=VARCHAR},
      </if>
      <if test="record.supplementaryExam != null">
        "supplementary_exam" = #{record.supplementaryExam,jdbcType=VARCHAR},
      </if>
      <if test="record.recordSummary != null">
        "record_summary" = #{record.recordSummary,jdbcType=VARCHAR},
      </if>
      <if test="record.recordTime != null">
        "record_time" = #{record.recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.recorder != null">
        "recorder" = #{record.recorder,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
      <if test="record.fullText != null">
        "full_text" = #{record.fullText,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSnOrg != null">
        "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."emr_admission_record"
    set "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      "name" = #{record.name,jdbcType=VARCHAR},
      "admission_date_time" = #{record.admissionDateTime,jdbcType=TIMESTAMP},
      "pre_diag" = #{record.preDiag,jdbcType=VARCHAR},
      "pre_diag_date" = #{record.preDiagDate,jdbcType=TIMESTAMP},
      "pre_diag_doctor" = #{record.preDiagDoctor,jdbcType=VARCHAR},
      "definite_diag" = #{record.definiteDiag,jdbcType=VARCHAR},
      "definite_diag_date" = #{record.definiteDiagDate,jdbcType=TIMESTAMP},
      "definite_diag_doctor" = #{record.definiteDiagDoctor,jdbcType=VARCHAR},
      "chief_complaint" = #{record.chiefComplaint,jdbcType=VARCHAR},
      "hy_present" = #{record.hyPresent,jdbcType=VARCHAR},
      "hy_past" = #{record.hyPast,jdbcType=VARCHAR},
      "hy_individual" = #{record.hyIndividual,jdbcType=VARCHAR},
      "hy_menstrual_marriage" = #{record.hyMenstrualMarriage,jdbcType=VARCHAR},
      "hy_family" = #{record.hyFamily,jdbcType=VARCHAR},
      "physical_exam" = #{record.physicalExam,jdbcType=VARCHAR},
      "speciality_exam" = #{record.specialityExam,jdbcType=VARCHAR},
      "supplementary_exam" = #{record.supplementaryExam,jdbcType=VARCHAR},
      "record_summary" = #{record.recordSummary,jdbcType=VARCHAR},
      "record_time" = #{record.recordTime,jdbcType=TIMESTAMP},
      "recorder" = #{record.recorder,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR},
      "full_text" = #{record.fullText,jdbcType=VARCHAR},
      "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.EmrAdmissionRecord">
    update "public"."emr_admission_record"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        "tpatno" = #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        "name" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="admissionDateTime != null">
        "admission_date_time" = #{admissionDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="preDiag != null">
        "pre_diag" = #{preDiag,jdbcType=VARCHAR},
      </if>
      <if test="preDiagDate != null">
        "pre_diag_date" = #{preDiagDate,jdbcType=TIMESTAMP},
      </if>
      <if test="preDiagDoctor != null">
        "pre_diag_doctor" = #{preDiagDoctor,jdbcType=VARCHAR},
      </if>
      <if test="definiteDiag != null">
        "definite_diag" = #{definiteDiag,jdbcType=VARCHAR},
      </if>
      <if test="definiteDiagDate != null">
        "definite_diag_date" = #{definiteDiagDate,jdbcType=TIMESTAMP},
      </if>
      <if test="definiteDiagDoctor != null">
        "definite_diag_doctor" = #{definiteDiagDoctor,jdbcType=VARCHAR},
      </if>
      <if test="hyPresent != null">
        "hy_present" = #{hyPresent,jdbcType=VARCHAR},
      </if>
      <if test="hyPast != null">
        "hy_past" = #{hyPast,jdbcType=VARCHAR},
      </if>
      <if test="hyIndividual != null">
        "hy_individual" = #{hyIndividual,jdbcType=VARCHAR},
      </if>
      <if test="hyMenstrualMarriage != null">
        "hy_menstrual_marriage" = #{hyMenstrualMarriage,jdbcType=VARCHAR},
      </if>
      <if test="hyFamily != null">
        "hy_family" = #{hyFamily,jdbcType=VARCHAR},
      </if>
      <if test="physicalExam != null">
        "physical_exam" = #{physicalExam,jdbcType=VARCHAR},
      </if>
      <if test="chiefComplaint != null">
        "chief_complaint" = #{chiefComplaint,jdbcType=VARCHAR},
      </if>
      <if test="specialityExam != null">
        "speciality_exam" = #{specialityExam,jdbcType=VARCHAR},
      </if>
      <if test="supplementaryExam != null">
        "supplementary_exam" = #{supplementaryExam,jdbcType=VARCHAR},
      </if>
      <if test="recordSummary != null">
        "record_summary" = #{recordSummary,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null">
        "record_time" = #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recorder != null">
        "recorder" = #{recorder,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="fullText != null">
        "full_text" = #{fullText,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.EmrAdmissionRecord">
    update "public"."emr_admission_record"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "tpatno" = #{tpatno,jdbcType=VARCHAR},
      "name" = #{name,jdbcType=VARCHAR},
      "admission_date_time" = #{admissionDateTime,jdbcType=TIMESTAMP},
      "pre_diag" = #{preDiag,jdbcType=VARCHAR},
      "pre_diag_date" = #{preDiagDate,jdbcType=TIMESTAMP},
      "pre_diag_doctor" = #{preDiagDoctor,jdbcType=VARCHAR},
      "definite_diag" = #{definiteDiag,jdbcType=VARCHAR},
      "definite_diag_date" = #{definiteDiagDate,jdbcType=TIMESTAMP},
      "definite_diag_doctor" = #{definiteDiagDoctor,jdbcType=VARCHAR},
      "hy_present" = #{hyPresent,jdbcType=VARCHAR},
      "hy_past" = #{hyPast,jdbcType=VARCHAR},
      "hy_individual" = #{hyIndividual,jdbcType=VARCHAR},
      "hy_menstrual_marriage" = #{hyMenstrualMarriage,jdbcType=VARCHAR},
      "hy_family" = #{hyFamily,jdbcType=VARCHAR},
      "physical_exam" = #{physicalExam,jdbcType=VARCHAR},
      "chief_complaint" = #{chiefComplaint,jdbcType=VARCHAR},
      "speciality_exam" = #{specialityExam,jdbcType=VARCHAR},
      "supplementary_exam" = #{supplementaryExam,jdbcType=VARCHAR},
      "record_summary" = #{recordSummary,jdbcType=VARCHAR},
      "record_time" = #{recordTime,jdbcType=TIMESTAMP},
      "recorder" = #{recorder,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR},
      "full_text" = #{fullText,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>

  <select id="getEmrAdmissionRecord" resultMap="BaseResultMap">
    select * from emr_admission_record where patient_sn = #{patientId} order by admission_date_time
  </select>

</mapper>
