<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.OperAnesthesiaMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.OperAnesthesia">
    <id column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="tpatno" jdbcType="VARCHAR" property="tpatno" />
    <result column="operation_no" jdbcType="VARCHAR" property="operationNo" />
    <result column="operation_type" jdbcType="VARCHAR" property="operationType" />
    <result column="operation_icd" jdbcType="VARCHAR" property="operationIcd" />
    <result column="operation_name" jdbcType="VARCHAR" property="operationName" />
    <result column="req_dept" jdbcType="VARCHAR" property="reqDept" />
    <result column="operation_room" jdbcType="VARCHAR" property="operationRoom" />
    <result column="in_room_time" jdbcType="TIMESTAMP" property="inRoomTime" />
    <result column="out_room_time" jdbcType="TIMESTAMP" property="outRoomTime" />
    <result column="operation_start_time" jdbcType="TIMESTAMP" property="operationStartTime" />
    <result column="operation_end_time" jdbcType="TIMESTAMP" property="operationEndTime" />
    <result column="anaesthesia_start_time" jdbcType="TIMESTAMP" property="anaesthesiaStartTime" />
    <result column="anaesthesia_end_time" jdbcType="TIMESTAMP" property="anaesthesiaEndTime" />
    <result column="anaesthesia_position" jdbcType="VARCHAR" property="anaesthesiaPosition" />
    <result column="surgical_incision_level" jdbcType="VARCHAR" property="surgicalIncisionLevel" />
    <result column="healing_level" jdbcType="VARCHAR" property="healingLevel" />
    <result column="anesthesia_method" jdbcType="VARCHAR" property="anesthesiaMethod" />
    <result column="anaesthesia_doctor_code" jdbcType="VARCHAR" property="anaesthesiaDoctorCode" />
    <result column="anaesthesia_doctor_name" jdbcType="VARCHAR" property="anaesthesiaDoctorName" />
    <result column="operation_level" jdbcType="VARCHAR" property="operationLevel" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "pk_id", "hospital_code", "patient_sn", "visit_sn", "tpatno", "operation_no", "operation_type", 
    "operation_icd", "operation_name", "req_dept", "operation_room", "in_room_time", 
    "out_room_time", "operation_start_time", "operation_end_time", "anaesthesia_start_time", 
    "anaesthesia_end_time", "anaesthesia_position", "surgical_incision_level", "healing_level", 
    "anesthesia_method", "anaesthesia_doctor_code", "anaesthesia_doctor_name", "operation_level", 
    "source_path", "data_state"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.OperAnesthesiaExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."oper_anesthesia"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."oper_anesthesia"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."oper_anesthesia"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.OperAnesthesiaExample">
    delete from "public"."oper_anesthesia"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.OperAnesthesia">
    insert into "public"."oper_anesthesia" ("pk_id", "hospital_code", "patient_sn", 
      "visit_sn", "tpatno", "operation_no", 
      "operation_type", "operation_icd", "operation_name", 
      "req_dept", "operation_room", "in_room_time", 
      "out_room_time", "operation_start_time", "operation_end_time", 
      "anaesthesia_start_time", "anaesthesia_end_time", 
      "anaesthesia_position", "surgical_incision_level", 
      "healing_level", "anesthesia_method", "anaesthesia_doctor_code", 
      "anaesthesia_doctor_name", "operation_level", 
      "source_path", "data_state")
    values (#{pkId,jdbcType=VARCHAR}, #{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, 
      #{visitSn,jdbcType=VARCHAR}, #{tpatno,jdbcType=VARCHAR}, #{operationNo,jdbcType=VARCHAR}, 
      #{operationType,jdbcType=VARCHAR}, #{operationIcd,jdbcType=VARCHAR}, #{operationName,jdbcType=VARCHAR}, 
      #{reqDept,jdbcType=VARCHAR}, #{operationRoom,jdbcType=VARCHAR}, #{inRoomTime,jdbcType=TIMESTAMP}, 
      #{outRoomTime,jdbcType=TIMESTAMP}, #{operationStartTime,jdbcType=TIMESTAMP}, #{operationEndTime,jdbcType=TIMESTAMP}, 
      #{anaesthesiaStartTime,jdbcType=TIMESTAMP}, #{anaesthesiaEndTime,jdbcType=TIMESTAMP}, 
      #{anaesthesiaPosition,jdbcType=VARCHAR}, #{surgicalIncisionLevel,jdbcType=VARCHAR}, 
      #{healingLevel,jdbcType=VARCHAR}, #{anesthesiaMethod,jdbcType=VARCHAR}, #{anaesthesiaDoctorCode,jdbcType=VARCHAR}, 
      #{anaesthesiaDoctorName,jdbcType=VARCHAR}, #{operationLevel,jdbcType=VARCHAR}, 
      #{sourcePath,jdbcType=VARCHAR}, #{dataState,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.OperAnesthesia">
    insert into "public"."oper_anesthesia"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="tpatno != null">
        "tpatno",
      </if>
      <if test="operationNo != null">
        "operation_no",
      </if>
      <if test="operationType != null">
        "operation_type",
      </if>
      <if test="operationIcd != null">
        "operation_icd",
      </if>
      <if test="operationName != null">
        "operation_name",
      </if>
      <if test="reqDept != null">
        "req_dept",
      </if>
      <if test="operationRoom != null">
        "operation_room",
      </if>
      <if test="inRoomTime != null">
        "in_room_time",
      </if>
      <if test="outRoomTime != null">
        "out_room_time",
      </if>
      <if test="operationStartTime != null">
        "operation_start_time",
      </if>
      <if test="operationEndTime != null">
        "operation_end_time",
      </if>
      <if test="anaesthesiaStartTime != null">
        "anaesthesia_start_time",
      </if>
      <if test="anaesthesiaEndTime != null">
        "anaesthesia_end_time",
      </if>
      <if test="anaesthesiaPosition != null">
        "anaesthesia_position",
      </if>
      <if test="surgicalIncisionLevel != null">
        "surgical_incision_level",
      </if>
      <if test="healingLevel != null">
        "healing_level",
      </if>
      <if test="anesthesiaMethod != null">
        "anesthesia_method",
      </if>
      <if test="anaesthesiaDoctorCode != null">
        "anaesthesia_doctor_code",
      </if>
      <if test="anaesthesiaDoctorName != null">
        "anaesthesia_doctor_name",
      </if>
      <if test="operationLevel != null">
        "operation_level",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="operationNo != null">
        #{operationNo,jdbcType=VARCHAR},
      </if>
      <if test="operationType != null">
        #{operationType,jdbcType=VARCHAR},
      </if>
      <if test="operationIcd != null">
        #{operationIcd,jdbcType=VARCHAR},
      </if>
      <if test="operationName != null">
        #{operationName,jdbcType=VARCHAR},
      </if>
      <if test="reqDept != null">
        #{reqDept,jdbcType=VARCHAR},
      </if>
      <if test="operationRoom != null">
        #{operationRoom,jdbcType=VARCHAR},
      </if>
      <if test="inRoomTime != null">
        #{inRoomTime,jdbcType=TIMESTAMP},
      </if>
      <if test="outRoomTime != null">
        #{outRoomTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationStartTime != null">
        #{operationStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationEndTime != null">
        #{operationEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="anaesthesiaStartTime != null">
        #{anaesthesiaStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="anaesthesiaEndTime != null">
        #{anaesthesiaEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="anaesthesiaPosition != null">
        #{anaesthesiaPosition,jdbcType=VARCHAR},
      </if>
      <if test="surgicalIncisionLevel != null">
        #{surgicalIncisionLevel,jdbcType=VARCHAR},
      </if>
      <if test="healingLevel != null">
        #{healingLevel,jdbcType=VARCHAR},
      </if>
      <if test="anesthesiaMethod != null">
        #{anesthesiaMethod,jdbcType=VARCHAR},
      </if>
      <if test="anaesthesiaDoctorCode != null">
        #{anaesthesiaDoctorCode,jdbcType=VARCHAR},
      </if>
      <if test="anaesthesiaDoctorName != null">
        #{anaesthesiaDoctorName,jdbcType=VARCHAR},
      </if>
      <if test="operationLevel != null">
        #{operationLevel,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.OperAnesthesiaExample" resultType="java.lang.Long">
    select count(*) from "public"."oper_anesthesia"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."oper_anesthesia"
    <set>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.tpatno != null">
        "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      </if>
      <if test="record.operationNo != null">
        "operation_no" = #{record.operationNo,jdbcType=VARCHAR},
      </if>
      <if test="record.operationType != null">
        "operation_type" = #{record.operationType,jdbcType=VARCHAR},
      </if>
      <if test="record.operationIcd != null">
        "operation_icd" = #{record.operationIcd,jdbcType=VARCHAR},
      </if>
      <if test="record.operationName != null">
        "operation_name" = #{record.operationName,jdbcType=VARCHAR},
      </if>
      <if test="record.reqDept != null">
        "req_dept" = #{record.reqDept,jdbcType=VARCHAR},
      </if>
      <if test="record.operationRoom != null">
        "operation_room" = #{record.operationRoom,jdbcType=VARCHAR},
      </if>
      <if test="record.inRoomTime != null">
        "in_room_time" = #{record.inRoomTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.outRoomTime != null">
        "out_room_time" = #{record.outRoomTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operationStartTime != null">
        "operation_start_time" = #{record.operationStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operationEndTime != null">
        "operation_end_time" = #{record.operationEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.anaesthesiaStartTime != null">
        "anaesthesia_start_time" = #{record.anaesthesiaStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.anaesthesiaEndTime != null">
        "anaesthesia_end_time" = #{record.anaesthesiaEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.anaesthesiaPosition != null">
        "anaesthesia_position" = #{record.anaesthesiaPosition,jdbcType=VARCHAR},
      </if>
      <if test="record.surgicalIncisionLevel != null">
        "surgical_incision_level" = #{record.surgicalIncisionLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.healingLevel != null">
        "healing_level" = #{record.healingLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.anesthesiaMethod != null">
        "anesthesia_method" = #{record.anesthesiaMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.anaesthesiaDoctorCode != null">
        "anaesthesia_doctor_code" = #{record.anaesthesiaDoctorCode,jdbcType=VARCHAR},
      </if>
      <if test="record.anaesthesiaDoctorName != null">
        "anaesthesia_doctor_name" = #{record.anaesthesiaDoctorName,jdbcType=VARCHAR},
      </if>
      <if test="record.operationLevel != null">
        "operation_level" = #{record.operationLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."oper_anesthesia"
    set "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      "operation_no" = #{record.operationNo,jdbcType=VARCHAR},
      "operation_type" = #{record.operationType,jdbcType=VARCHAR},
      "operation_icd" = #{record.operationIcd,jdbcType=VARCHAR},
      "operation_name" = #{record.operationName,jdbcType=VARCHAR},
      "req_dept" = #{record.reqDept,jdbcType=VARCHAR},
      "operation_room" = #{record.operationRoom,jdbcType=VARCHAR},
      "in_room_time" = #{record.inRoomTime,jdbcType=TIMESTAMP},
      "out_room_time" = #{record.outRoomTime,jdbcType=TIMESTAMP},
      "operation_start_time" = #{record.operationStartTime,jdbcType=TIMESTAMP},
      "operation_end_time" = #{record.operationEndTime,jdbcType=TIMESTAMP},
      "anaesthesia_start_time" = #{record.anaesthesiaStartTime,jdbcType=TIMESTAMP},
      "anaesthesia_end_time" = #{record.anaesthesiaEndTime,jdbcType=TIMESTAMP},
      "anaesthesia_position" = #{record.anaesthesiaPosition,jdbcType=VARCHAR},
      "surgical_incision_level" = #{record.surgicalIncisionLevel,jdbcType=VARCHAR},
      "healing_level" = #{record.healingLevel,jdbcType=VARCHAR},
      "anesthesia_method" = #{record.anesthesiaMethod,jdbcType=VARCHAR},
      "anaesthesia_doctor_code" = #{record.anaesthesiaDoctorCode,jdbcType=VARCHAR},
      "anaesthesia_doctor_name" = #{record.anaesthesiaDoctorName,jdbcType=VARCHAR},
      "operation_level" = #{record.operationLevel,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.OperAnesthesia">
    update "public"."oper_anesthesia"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        "tpatno" = #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="operationNo != null">
        "operation_no" = #{operationNo,jdbcType=VARCHAR},
      </if>
      <if test="operationType != null">
        "operation_type" = #{operationType,jdbcType=VARCHAR},
      </if>
      <if test="operationIcd != null">
        "operation_icd" = #{operationIcd,jdbcType=VARCHAR},
      </if>
      <if test="operationName != null">
        "operation_name" = #{operationName,jdbcType=VARCHAR},
      </if>
      <if test="reqDept != null">
        "req_dept" = #{reqDept,jdbcType=VARCHAR},
      </if>
      <if test="operationRoom != null">
        "operation_room" = #{operationRoom,jdbcType=VARCHAR},
      </if>
      <if test="inRoomTime != null">
        "in_room_time" = #{inRoomTime,jdbcType=TIMESTAMP},
      </if>
      <if test="outRoomTime != null">
        "out_room_time" = #{outRoomTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationStartTime != null">
        "operation_start_time" = #{operationStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationEndTime != null">
        "operation_end_time" = #{operationEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="anaesthesiaStartTime != null">
        "anaesthesia_start_time" = #{anaesthesiaStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="anaesthesiaEndTime != null">
        "anaesthesia_end_time" = #{anaesthesiaEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="anaesthesiaPosition != null">
        "anaesthesia_position" = #{anaesthesiaPosition,jdbcType=VARCHAR},
      </if>
      <if test="surgicalIncisionLevel != null">
        "surgical_incision_level" = #{surgicalIncisionLevel,jdbcType=VARCHAR},
      </if>
      <if test="healingLevel != null">
        "healing_level" = #{healingLevel,jdbcType=VARCHAR},
      </if>
      <if test="anesthesiaMethod != null">
        "anesthesia_method" = #{anesthesiaMethod,jdbcType=VARCHAR},
      </if>
      <if test="anaesthesiaDoctorCode != null">
        "anaesthesia_doctor_code" = #{anaesthesiaDoctorCode,jdbcType=VARCHAR},
      </if>
      <if test="anaesthesiaDoctorName != null">
        "anaesthesia_doctor_name" = #{anaesthesiaDoctorName,jdbcType=VARCHAR},
      </if>
      <if test="operationLevel != null">
        "operation_level" = #{operationLevel,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.OperAnesthesia">
    update "public"."oper_anesthesia"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "tpatno" = #{tpatno,jdbcType=VARCHAR},
      "operation_no" = #{operationNo,jdbcType=VARCHAR},
      "operation_type" = #{operationType,jdbcType=VARCHAR},
      "operation_icd" = #{operationIcd,jdbcType=VARCHAR},
      "operation_name" = #{operationName,jdbcType=VARCHAR},
      "req_dept" = #{reqDept,jdbcType=VARCHAR},
      "operation_room" = #{operationRoom,jdbcType=VARCHAR},
      "in_room_time" = #{inRoomTime,jdbcType=TIMESTAMP},
      "out_room_time" = #{outRoomTime,jdbcType=TIMESTAMP},
      "operation_start_time" = #{operationStartTime,jdbcType=TIMESTAMP},
      "operation_end_time" = #{operationEndTime,jdbcType=TIMESTAMP},
      "anaesthesia_start_time" = #{anaesthesiaStartTime,jdbcType=TIMESTAMP},
      "anaesthesia_end_time" = #{anaesthesiaEndTime,jdbcType=TIMESTAMP},
      "anaesthesia_position" = #{anaesthesiaPosition,jdbcType=VARCHAR},
      "surgical_incision_level" = #{surgicalIncisionLevel,jdbcType=VARCHAR},
      "healing_level" = #{healingLevel,jdbcType=VARCHAR},
      "anesthesia_method" = #{anesthesiaMethod,jdbcType=VARCHAR},
      "anaesthesia_doctor_code" = #{anaesthesiaDoctorCode,jdbcType=VARCHAR},
      "anaesthesia_doctor_name" = #{anaesthesiaDoctorName,jdbcType=VARCHAR},
      "operation_level" = #{operationLevel,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
</mapper>