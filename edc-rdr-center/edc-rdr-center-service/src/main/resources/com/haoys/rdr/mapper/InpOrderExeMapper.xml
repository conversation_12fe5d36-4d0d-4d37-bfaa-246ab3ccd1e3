<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.InpOrderExeMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.InpOrderExe">
    <id column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="tpatno" jdbcType="VARCHAR" property="tpatno" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_sub_no" jdbcType="VARCHAR" property="orderSubNo" />
    <result column="order_class" jdbcType="VARCHAR" property="orderClass" />
    <result column="order_text" jdbcType="VARCHAR" property="orderText" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="drug_spec" jdbcType="VARCHAR" property="drugSpec" />
    <result column="dosage" jdbcType="DOUBLE" property="dosage" />
    <result column="dosage_units" jdbcType="VARCHAR" property="dosageUnits" />
    <result column="administration_route" jdbcType="VARCHAR" property="administrationRoute" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="duration_units" jdbcType="VARCHAR" property="durationUnits" />
    <result column="start_date_time" jdbcType="TIMESTAMP" property="startDateTime" />
    <result column="stop_date_time" jdbcType="TIMESTAMP" property="stopDateTime" />
    <result column="frequency" jdbcType="VARCHAR" property="frequency" />
    <result column="freq_interval" jdbcType="VARCHAR" property="freqInterval" />
    <result column="freq_interval_unit" jdbcType="VARCHAR" property="freqIntervalUnit" />
    <result column="freq_detail" jdbcType="VARCHAR" property="freqDetail" />
    <result column="perform_schedule" jdbcType="TIMESTAMP" property="performSchedule" />
    <result column="order_dept" jdbcType="VARCHAR" property="orderDept" />
    <result column="order_doctor" jdbcType="VARCHAR" property="orderDoctor" />
    <result column="enter_date_time" jdbcType="TIMESTAMP" property="enterDateTime" />
    <result column="stop_order_date_time" jdbcType="TIMESTAMP" property="stopOrderDateTime" />
    <result column="order_status" jdbcType="VARCHAR" property="orderStatus" />
    <result column="cancel_date_time" jdbcType="TIMESTAMP" property="cancelDateTime" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "pk_id", "hospital_code", "patient_sn", "visit_sn", "tpatno", "order_no", "order_sub_no", 
    "order_class", "order_text", "order_type", "drug_spec", "dosage", "dosage_units", 
    "administration_route", "duration", "duration_units", "start_date_time", "stop_date_time", 
    "frequency", "freq_interval", "freq_interval_unit", "freq_detail", "perform_schedule", 
    "order_dept", "order_doctor", "enter_date_time", "stop_order_date_time", "order_status", 
    "cancel_date_time", "source_path", "data_state"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.InpOrderExeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."inp_order_exe"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."inp_order_exe"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."inp_order_exe"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.InpOrderExeExample">
    delete from "public"."inp_order_exe"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.InpOrderExe">
    insert into "public"."inp_order_exe" ("pk_id", "hospital_code", "patient_sn", 
      "visit_sn", "tpatno", "order_no", 
      "order_sub_no", "order_class", "order_text", 
      "order_type", "drug_spec", "dosage", 
      "dosage_units", "administration_route", "duration", 
      "duration_units", "start_date_time", "stop_date_time", 
      "frequency", "freq_interval", "freq_interval_unit", 
      "freq_detail", "perform_schedule", "order_dept", 
      "order_doctor", "enter_date_time", "stop_order_date_time", 
      "order_status", "cancel_date_time", "source_path", 
      "data_state")
    values (#{pkId,jdbcType=VARCHAR}, #{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, 
      #{visitSn,jdbcType=VARCHAR}, #{tpatno,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, 
      #{orderSubNo,jdbcType=VARCHAR}, #{orderClass,jdbcType=VARCHAR}, #{orderText,jdbcType=VARCHAR}, 
      #{orderType,jdbcType=VARCHAR}, #{drugSpec,jdbcType=VARCHAR}, #{dosage,jdbcType=DOUBLE}, 
      #{dosageUnits,jdbcType=VARCHAR}, #{administrationRoute,jdbcType=VARCHAR}, #{duration,jdbcType=INTEGER}, 
      #{durationUnits,jdbcType=VARCHAR}, #{startDateTime,jdbcType=TIMESTAMP}, #{stopDateTime,jdbcType=TIMESTAMP}, 
      #{frequency,jdbcType=VARCHAR}, #{freqInterval,jdbcType=VARCHAR}, #{freqIntervalUnit,jdbcType=VARCHAR}, 
      #{freqDetail,jdbcType=VARCHAR}, #{performSchedule,jdbcType=TIMESTAMP}, #{orderDept,jdbcType=VARCHAR}, 
      #{orderDoctor,jdbcType=VARCHAR}, #{enterDateTime,jdbcType=TIMESTAMP}, #{stopOrderDateTime,jdbcType=TIMESTAMP}, 
      #{orderStatus,jdbcType=VARCHAR}, #{cancelDateTime,jdbcType=TIMESTAMP}, #{sourcePath,jdbcType=VARCHAR}, 
      #{dataState,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.InpOrderExe">
    insert into "public"."inp_order_exe"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="tpatno != null">
        "tpatno",
      </if>
      <if test="orderNo != null">
        "order_no",
      </if>
      <if test="orderSubNo != null">
        "order_sub_no",
      </if>
      <if test="orderClass != null">
        "order_class",
      </if>
      <if test="orderText != null">
        "order_text",
      </if>
      <if test="orderType != null">
        "order_type",
      </if>
      <if test="drugSpec != null">
        "drug_spec",
      </if>
      <if test="dosage != null">
        "dosage",
      </if>
      <if test="dosageUnits != null">
        "dosage_units",
      </if>
      <if test="administrationRoute != null">
        "administration_route",
      </if>
      <if test="duration != null">
        "duration",
      </if>
      <if test="durationUnits != null">
        "duration_units",
      </if>
      <if test="startDateTime != null">
        "start_date_time",
      </if>
      <if test="stopDateTime != null">
        "stop_date_time",
      </if>
      <if test="frequency != null">
        "frequency",
      </if>
      <if test="freqInterval != null">
        "freq_interval",
      </if>
      <if test="freqIntervalUnit != null">
        "freq_interval_unit",
      </if>
      <if test="freqDetail != null">
        "freq_detail",
      </if>
      <if test="performSchedule != null">
        "perform_schedule",
      </if>
      <if test="orderDept != null">
        "order_dept",
      </if>
      <if test="orderDoctor != null">
        "order_doctor",
      </if>
      <if test="enterDateTime != null">
        "enter_date_time",
      </if>
      <if test="stopOrderDateTime != null">
        "stop_order_date_time",
      </if>
      <if test="orderStatus != null">
        "order_status",
      </if>
      <if test="cancelDateTime != null">
        "cancel_date_time",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderSubNo != null">
        #{orderSubNo,jdbcType=VARCHAR},
      </if>
      <if test="orderClass != null">
        #{orderClass,jdbcType=VARCHAR},
      </if>
      <if test="orderText != null">
        #{orderText,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="drugSpec != null">
        #{drugSpec,jdbcType=VARCHAR},
      </if>
      <if test="dosage != null">
        #{dosage,jdbcType=DOUBLE},
      </if>
      <if test="dosageUnits != null">
        #{dosageUnits,jdbcType=VARCHAR},
      </if>
      <if test="administrationRoute != null">
        #{administrationRoute,jdbcType=VARCHAR},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=INTEGER},
      </if>
      <if test="durationUnits != null">
        #{durationUnits,jdbcType=VARCHAR},
      </if>
      <if test="startDateTime != null">
        #{startDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="stopDateTime != null">
        #{stopDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="frequency != null">
        #{frequency,jdbcType=VARCHAR},
      </if>
      <if test="freqInterval != null">
        #{freqInterval,jdbcType=VARCHAR},
      </if>
      <if test="freqIntervalUnit != null">
        #{freqIntervalUnit,jdbcType=VARCHAR},
      </if>
      <if test="freqDetail != null">
        #{freqDetail,jdbcType=VARCHAR},
      </if>
      <if test="performSchedule != null">
        #{performSchedule,jdbcType=TIMESTAMP},
      </if>
      <if test="orderDept != null">
        #{orderDept,jdbcType=VARCHAR},
      </if>
      <if test="orderDoctor != null">
        #{orderDoctor,jdbcType=VARCHAR},
      </if>
      <if test="enterDateTime != null">
        #{enterDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="stopOrderDateTime != null">
        #{stopOrderDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=VARCHAR},
      </if>
      <if test="cancelDateTime != null">
        #{cancelDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.InpOrderExeExample" resultType="java.lang.Long">
    select count(*) from "public"."inp_order_exe"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."inp_order_exe"
    <set>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.tpatno != null">
        "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null">
        "order_no" = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderSubNo != null">
        "order_sub_no" = #{record.orderSubNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderClass != null">
        "order_class" = #{record.orderClass,jdbcType=VARCHAR},
      </if>
      <if test="record.orderText != null">
        "order_text" = #{record.orderText,jdbcType=VARCHAR},
      </if>
      <if test="record.orderType != null">
        "order_type" = #{record.orderType,jdbcType=VARCHAR},
      </if>
      <if test="record.drugSpec != null">
        "drug_spec" = #{record.drugSpec,jdbcType=VARCHAR},
      </if>
      <if test="record.dosage != null">
        "dosage" = #{record.dosage,jdbcType=DOUBLE},
      </if>
      <if test="record.dosageUnits != null">
        "dosage_units" = #{record.dosageUnits,jdbcType=VARCHAR},
      </if>
      <if test="record.administrationRoute != null">
        "administration_route" = #{record.administrationRoute,jdbcType=VARCHAR},
      </if>
      <if test="record.duration != null">
        "duration" = #{record.duration,jdbcType=INTEGER},
      </if>
      <if test="record.durationUnits != null">
        "duration_units" = #{record.durationUnits,jdbcType=VARCHAR},
      </if>
      <if test="record.startDateTime != null">
        "start_date_time" = #{record.startDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.stopDateTime != null">
        "stop_date_time" = #{record.stopDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.frequency != null">
        "frequency" = #{record.frequency,jdbcType=VARCHAR},
      </if>
      <if test="record.freqInterval != null">
        "freq_interval" = #{record.freqInterval,jdbcType=VARCHAR},
      </if>
      <if test="record.freqIntervalUnit != null">
        "freq_interval_unit" = #{record.freqIntervalUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.freqDetail != null">
        "freq_detail" = #{record.freqDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.performSchedule != null">
        "perform_schedule" = #{record.performSchedule,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderDept != null">
        "order_dept" = #{record.orderDept,jdbcType=VARCHAR},
      </if>
      <if test="record.orderDoctor != null">
        "order_doctor" = #{record.orderDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.enterDateTime != null">
        "enter_date_time" = #{record.enterDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.stopOrderDateTime != null">
        "stop_order_date_time" = #{record.stopOrderDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderStatus != null">
        "order_status" = #{record.orderStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.cancelDateTime != null">
        "cancel_date_time" = #{record.cancelDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."inp_order_exe"
    set "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      "order_no" = #{record.orderNo,jdbcType=VARCHAR},
      "order_sub_no" = #{record.orderSubNo,jdbcType=VARCHAR},
      "order_class" = #{record.orderClass,jdbcType=VARCHAR},
      "order_text" = #{record.orderText,jdbcType=VARCHAR},
      "order_type" = #{record.orderType,jdbcType=VARCHAR},
      "drug_spec" = #{record.drugSpec,jdbcType=VARCHAR},
      "dosage" = #{record.dosage,jdbcType=DOUBLE},
      "dosage_units" = #{record.dosageUnits,jdbcType=VARCHAR},
      "administration_route" = #{record.administrationRoute,jdbcType=VARCHAR},
      "duration" = #{record.duration,jdbcType=INTEGER},
      "duration_units" = #{record.durationUnits,jdbcType=VARCHAR},
      "start_date_time" = #{record.startDateTime,jdbcType=TIMESTAMP},
      "stop_date_time" = #{record.stopDateTime,jdbcType=TIMESTAMP},
      "frequency" = #{record.frequency,jdbcType=VARCHAR},
      "freq_interval" = #{record.freqInterval,jdbcType=VARCHAR},
      "freq_interval_unit" = #{record.freqIntervalUnit,jdbcType=VARCHAR},
      "freq_detail" = #{record.freqDetail,jdbcType=VARCHAR},
      "perform_schedule" = #{record.performSchedule,jdbcType=TIMESTAMP},
      "order_dept" = #{record.orderDept,jdbcType=VARCHAR},
      "order_doctor" = #{record.orderDoctor,jdbcType=VARCHAR},
      "enter_date_time" = #{record.enterDateTime,jdbcType=TIMESTAMP},
      "stop_order_date_time" = #{record.stopOrderDateTime,jdbcType=TIMESTAMP},
      "order_status" = #{record.orderStatus,jdbcType=VARCHAR},
      "cancel_date_time" = #{record.cancelDateTime,jdbcType=TIMESTAMP},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.InpOrderExe">
    update "public"."inp_order_exe"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        "tpatno" = #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        "order_no" = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderSubNo != null">
        "order_sub_no" = #{orderSubNo,jdbcType=VARCHAR},
      </if>
      <if test="orderClass != null">
        "order_class" = #{orderClass,jdbcType=VARCHAR},
      </if>
      <if test="orderText != null">
        "order_text" = #{orderText,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        "order_type" = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="drugSpec != null">
        "drug_spec" = #{drugSpec,jdbcType=VARCHAR},
      </if>
      <if test="dosage != null">
        "dosage" = #{dosage,jdbcType=DOUBLE},
      </if>
      <if test="dosageUnits != null">
        "dosage_units" = #{dosageUnits,jdbcType=VARCHAR},
      </if>
      <if test="administrationRoute != null">
        "administration_route" = #{administrationRoute,jdbcType=VARCHAR},
      </if>
      <if test="duration != null">
        "duration" = #{duration,jdbcType=INTEGER},
      </if>
      <if test="durationUnits != null">
        "duration_units" = #{durationUnits,jdbcType=VARCHAR},
      </if>
      <if test="startDateTime != null">
        "start_date_time" = #{startDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="stopDateTime != null">
        "stop_date_time" = #{stopDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="frequency != null">
        "frequency" = #{frequency,jdbcType=VARCHAR},
      </if>
      <if test="freqInterval != null">
        "freq_interval" = #{freqInterval,jdbcType=VARCHAR},
      </if>
      <if test="freqIntervalUnit != null">
        "freq_interval_unit" = #{freqIntervalUnit,jdbcType=VARCHAR},
      </if>
      <if test="freqDetail != null">
        "freq_detail" = #{freqDetail,jdbcType=VARCHAR},
      </if>
      <if test="performSchedule != null">
        "perform_schedule" = #{performSchedule,jdbcType=TIMESTAMP},
      </if>
      <if test="orderDept != null">
        "order_dept" = #{orderDept,jdbcType=VARCHAR},
      </if>
      <if test="orderDoctor != null">
        "order_doctor" = #{orderDoctor,jdbcType=VARCHAR},
      </if>
      <if test="enterDateTime != null">
        "enter_date_time" = #{enterDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="stopOrderDateTime != null">
        "stop_order_date_time" = #{stopOrderDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStatus != null">
        "order_status" = #{orderStatus,jdbcType=VARCHAR},
      </if>
      <if test="cancelDateTime != null">
        "cancel_date_time" = #{cancelDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.InpOrderExe">
    update "public"."inp_order_exe"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "tpatno" = #{tpatno,jdbcType=VARCHAR},
      "order_no" = #{orderNo,jdbcType=VARCHAR},
      "order_sub_no" = #{orderSubNo,jdbcType=VARCHAR},
      "order_class" = #{orderClass,jdbcType=VARCHAR},
      "order_text" = #{orderText,jdbcType=VARCHAR},
      "order_type" = #{orderType,jdbcType=VARCHAR},
      "drug_spec" = #{drugSpec,jdbcType=VARCHAR},
      "dosage" = #{dosage,jdbcType=DOUBLE},
      "dosage_units" = #{dosageUnits,jdbcType=VARCHAR},
      "administration_route" = #{administrationRoute,jdbcType=VARCHAR},
      "duration" = #{duration,jdbcType=INTEGER},
      "duration_units" = #{durationUnits,jdbcType=VARCHAR},
      "start_date_time" = #{startDateTime,jdbcType=TIMESTAMP},
      "stop_date_time" = #{stopDateTime,jdbcType=TIMESTAMP},
      "frequency" = #{frequency,jdbcType=VARCHAR},
      "freq_interval" = #{freqInterval,jdbcType=VARCHAR},
      "freq_interval_unit" = #{freqIntervalUnit,jdbcType=VARCHAR},
      "freq_detail" = #{freqDetail,jdbcType=VARCHAR},
      "perform_schedule" = #{performSchedule,jdbcType=TIMESTAMP},
      "order_dept" = #{orderDept,jdbcType=VARCHAR},
      "order_doctor" = #{orderDoctor,jdbcType=VARCHAR},
      "enter_date_time" = #{enterDateTime,jdbcType=TIMESTAMP},
      "stop_order_date_time" = #{stopOrderDateTime,jdbcType=TIMESTAMP},
      "order_status" = #{orderStatus,jdbcType=VARCHAR},
      "cancel_date_time" = #{cancelDateTime,jdbcType=TIMESTAMP},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
</mapper>