<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.NursingRecordMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.NursingRecord">
    <id column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="tpatno" jdbcType="VARCHAR" property="tpatno" />
    <result column="nursing_type" jdbcType="VARCHAR" property="nursingType" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
    <result column="result_value" jdbcType="VARCHAR" property="resultValue" />
    <result column="nursing_nurse" jdbcType="VARCHAR" property="nursingNurse" />
    <result column="nursing_dept" jdbcType="VARCHAR" property="nursingDept" />
    <result column="record_time" jdbcType="VARCHAR" property="recordTime" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "pk_id", "hospital_code", "patient_sn", "visit_sn", "tpatno", "nursing_type", "item_name", 
    "item_code", "result_value", "nursing_nurse", "nursing_dept", "record_time", "source_path", 
    "data_state"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.NursingRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."nursing_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."nursing_record"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."nursing_record"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.NursingRecordExample">
    delete from "public"."nursing_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.NursingRecord">
    insert into "public"."nursing_record" ("pk_id", "hospital_code", "patient_sn", 
      "visit_sn", "tpatno", "nursing_type", 
      "item_name", "item_code", "result_value", 
      "nursing_nurse", "nursing_dept", "record_time", 
      "source_path", "data_state")
    values (#{pkId,jdbcType=VARCHAR}, #{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, 
      #{visitSn,jdbcType=VARCHAR}, #{tpatno,jdbcType=VARCHAR}, #{nursingType,jdbcType=VARCHAR}, 
      #{itemName,jdbcType=VARCHAR}, #{itemCode,jdbcType=VARCHAR}, #{resultValue,jdbcType=VARCHAR}, 
      #{nursingNurse,jdbcType=VARCHAR}, #{nursingDept,jdbcType=VARCHAR}, #{recordTime,jdbcType=VARCHAR}, 
      #{sourcePath,jdbcType=VARCHAR}, #{dataState,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.NursingRecord">
    insert into "public"."nursing_record"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="tpatno != null">
        "tpatno",
      </if>
      <if test="nursingType != null">
        "nursing_type",
      </if>
      <if test="itemName != null">
        "item_name",
      </if>
      <if test="itemCode != null">
        "item_code",
      </if>
      <if test="resultValue != null">
        "result_value",
      </if>
      <if test="nursingNurse != null">
        "nursing_nurse",
      </if>
      <if test="nursingDept != null">
        "nursing_dept",
      </if>
      <if test="recordTime != null">
        "record_time",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="nursingType != null">
        #{nursingType,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemCode != null">
        #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="resultValue != null">
        #{resultValue,jdbcType=VARCHAR},
      </if>
      <if test="nursingNurse != null">
        #{nursingNurse,jdbcType=VARCHAR},
      </if>
      <if test="nursingDept != null">
        #{nursingDept,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null">
        #{recordTime,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.NursingRecordExample" resultType="java.lang.Long">
    select count(*) from "public"."nursing_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."nursing_record"
    <set>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.tpatno != null">
        "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      </if>
      <if test="record.nursingType != null">
        "nursing_type" = #{record.nursingType,jdbcType=VARCHAR},
      </if>
      <if test="record.itemName != null">
        "item_name" = #{record.itemName,jdbcType=VARCHAR},
      </if>
      <if test="record.itemCode != null">
        "item_code" = #{record.itemCode,jdbcType=VARCHAR},
      </if>
      <if test="record.resultValue != null">
        "result_value" = #{record.resultValue,jdbcType=VARCHAR},
      </if>
      <if test="record.nursingNurse != null">
        "nursing_nurse" = #{record.nursingNurse,jdbcType=VARCHAR},
      </if>
      <if test="record.nursingDept != null">
        "nursing_dept" = #{record.nursingDept,jdbcType=VARCHAR},
      </if>
      <if test="record.recordTime != null">
        "record_time" = #{record.recordTime,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."nursing_record"
    set "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      "nursing_type" = #{record.nursingType,jdbcType=VARCHAR},
      "item_name" = #{record.itemName,jdbcType=VARCHAR},
      "item_code" = #{record.itemCode,jdbcType=VARCHAR},
      "result_value" = #{record.resultValue,jdbcType=VARCHAR},
      "nursing_nurse" = #{record.nursingNurse,jdbcType=VARCHAR},
      "nursing_dept" = #{record.nursingDept,jdbcType=VARCHAR},
      "record_time" = #{record.recordTime,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.NursingRecord">
    update "public"."nursing_record"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        "tpatno" = #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="nursingType != null">
        "nursing_type" = #{nursingType,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        "item_name" = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemCode != null">
        "item_code" = #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="resultValue != null">
        "result_value" = #{resultValue,jdbcType=VARCHAR},
      </if>
      <if test="nursingNurse != null">
        "nursing_nurse" = #{nursingNurse,jdbcType=VARCHAR},
      </if>
      <if test="nursingDept != null">
        "nursing_dept" = #{nursingDept,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null">
        "record_time" = #{recordTime,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.NursingRecord">
    update "public"."nursing_record"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "tpatno" = #{tpatno,jdbcType=VARCHAR},
      "nursing_type" = #{nursingType,jdbcType=VARCHAR},
      "item_name" = #{itemName,jdbcType=VARCHAR},
      "item_code" = #{itemCode,jdbcType=VARCHAR},
      "result_value" = #{resultValue,jdbcType=VARCHAR},
      "nursing_nurse" = #{nursingNurse,jdbcType=VARCHAR},
      "nursing_dept" = #{nursingDept,jdbcType=VARCHAR},
      "record_time" = #{recordTime,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
</mapper>