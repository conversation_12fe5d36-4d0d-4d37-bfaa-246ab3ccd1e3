<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.RdrPatientAnalysisRecordMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.RdrPatientAnalysisRecord">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="data_base_id" jdbcType="VARCHAR" property="dataBaseId" />
    <result column="batch_code" jdbcType="VARCHAR" property="batchCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="dataset_id" jdbcType="VARCHAR" property="datasetId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "id", "data_base_id", "batch_code", "patient_sn", "description", "create_time", "dataset_id"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.RdrPatientAnalysisRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."rdr_patient_analysis_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "public"."rdr_patient_analysis_record"
    where "id" = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."rdr_patient_analysis_record"
    where "id" = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.RdrPatientAnalysisRecordExample">
    delete from "public"."rdr_patient_analysis_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.RdrPatientAnalysisRecord">
    insert into "public"."rdr_patient_analysis_record" ("id", "data_base_id", "batch_code",
      "patient_sn", "description", "create_time",
      "dataset_id")
    values (#{id,jdbcType=VARCHAR}, #{dataBaseId,jdbcType=VARCHAR}, #{batchCode,jdbcType=VARCHAR},
      #{patientSn,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{datasetId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.RdrPatientAnalysisRecord">
    insert into "public"."rdr_patient_analysis_record"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        "id",
      </if>
      <if test="dataBaseId != null">
        "data_base_id",
      </if>
      <if test="batchCode != null">
        "batch_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="description != null">
        "description",
      </if>
      <if test="createTime != null">
        "create_time",
      </if>
      <if test="datasetId != null">
        "dataset_id",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="dataBaseId != null">
        #{dataBaseId,jdbcType=VARCHAR},
      </if>
      <if test="batchCode != null">
        #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="datasetId != null">
        #{datasetId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.RdrPatientAnalysisRecordExample" resultType="java.lang.Long">
    select count(*) from "public"."rdr_patient_analysis_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."rdr_patient_analysis_record"
    <set>
      <if test="record.id != null">
        "id" = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.dataBaseId != null">
        "data_base_id" = #{record.dataBaseId,jdbcType=VARCHAR},
      </if>
      <if test="record.batchCode != null">
        "batch_code" = #{record.batchCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        "description" = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        "create_time" = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.datasetId != null">
        "dataset_id" = #{record.datasetId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."rdr_patient_analysis_record"
    set "id" = #{record.id,jdbcType=VARCHAR},
      "data_base_id" = #{record.dataBaseId,jdbcType=VARCHAR},
      "batch_code" = #{record.batchCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "description" = #{record.description,jdbcType=VARCHAR},
      "create_time" = #{record.createTime,jdbcType=TIMESTAMP},
      "dataset_id" = #{record.datasetId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.RdrPatientAnalysisRecord">
    update "public"."rdr_patient_analysis_record"
    <set>
      <if test="dataBaseId != null">
        "data_base_id" = #{dataBaseId,jdbcType=VARCHAR},
      </if>
      <if test="batchCode != null">
        "batch_code" = #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        "description" = #{description,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        "create_time" = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="datasetId != null">
        "dataset_id" = #{datasetId,jdbcType=VARCHAR},
      </if>
    </set>
    where "id" = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.RdrPatientAnalysisRecord">
    update "public"."rdr_patient_analysis_record"
    set "data_base_id" = #{dataBaseId,jdbcType=VARCHAR},
      "batch_code" = #{batchCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "description" = #{description,jdbcType=VARCHAR},
      "create_time" = #{createTime,jdbcType=TIMESTAMP},
      "dataset_id" = #{datasetId,jdbcType=VARCHAR}
    where "id" = #{id,jdbcType=VARCHAR}
  </update>

  <select id="getPatientAnalysisRecordByDataSetId" resultType="java.lang.String">
    select patient_sn from rdr_patient_analysis_record where dataset_id = #{dataSetId}
    <if test="patientIds != null">
      and patient_sn IN
      <foreach collection="patientIds" item="patientSn" open="(" close=")" separator=",">
        #{patientSn}
      </foreach>
    </if>
  </select>

</mapper>
