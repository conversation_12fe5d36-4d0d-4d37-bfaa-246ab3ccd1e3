<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.MrHomepageMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.MrHomepage">
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="hospital_name" jdbcType="VARCHAR" property="hospitalName" />
    <result column="insurance_card_no" jdbcType="VARCHAR" property="insuranceCardNo" />
    <result column="health_card_no" jdbcType="VARCHAR" property="healthCardNo" />
    <result column="pay_way" jdbcType="VARCHAR" property="payWay" />
    <result column="admission_number" jdbcType="INTEGER" property="admissionNumber" />
    <result column="tpatno" jdbcType="VARCHAR" property="tpatno" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="gender" jdbcType="VARCHAR" property="gender" />
    <result column="date_of_birth" jdbcType="TIMESTAMP" property="dateOfBirth" />
    <result column="age" jdbcType="VARCHAR" property="age" />
    <result column="marriage" jdbcType="VARCHAR" property="marriage" />
    <result column="occupation" jdbcType="VARCHAR" property="occupation" />
    <result column="birth_place" jdbcType="VARCHAR" property="birthPlace" />
    <result column="birth_place_province" jdbcType="VARCHAR" property="birthPlaceProvince" />
    <result column="birth_place_city" jdbcType="VARCHAR" property="birthPlaceCity" />
    <result column="birth_place_country" jdbcType="VARCHAR" property="birthPlaceCountry" />
    <result column="nation" jdbcType="VARCHAR" property="nation" />
    <result column="citizenship" jdbcType="VARCHAR" property="citizenship" />
    <result column="id_no" jdbcType="VARCHAR" property="idNo" />
    <result column="home_adress" jdbcType="VARCHAR" property="homeAdress" />
    <result column="home_phone" jdbcType="VARCHAR" property="homePhone" />
    <result column="home_adress_postcode" jdbcType="VARCHAR" property="homeAdressPostcode" />
    <result column="work_unit_and_adress" jdbcType="VARCHAR" property="workUnitAndAdress" />
    <result column="contact_telephone" jdbcType="VARCHAR" property="contactTelephone" />
    <result column="work_unit_postcode" jdbcType="VARCHAR" property="workUnitPostcode" />
    <result column="registered_residence" jdbcType="VARCHAR" property="registeredResidence" />
    <result column="registered_residence_postcode" jdbcType="VARCHAR" property="registeredResidencePostcode" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_relationship" jdbcType="VARCHAR" property="contactRelationship" />
    <result column="contact_adress" jdbcType="VARCHAR" property="contactAdress" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="admission_way" jdbcType="VARCHAR" property="admissionWay" />
    <result column="admission_date_time" jdbcType="TIMESTAMP" property="admissionDateTime" />
    <result column="dept_admission_to" jdbcType="VARCHAR" property="deptAdmissionTo" />
    <result column="ward_admission_to" jdbcType="VARCHAR" property="wardAdmissionTo" />
    <result column="dept_transfer_from" jdbcType="VARCHAR" property="deptTransferFrom" />
    <result column="discharge_date_time" jdbcType="TIMESTAMP" property="dischargeDateTime" />
    <result column="dept_discharge_from" jdbcType="VARCHAR" property="deptDischargeFrom" />
    <result column="ward_discharge_from" jdbcType="VARCHAR" property="wardDischargeFrom" />
    <result column="in_days" jdbcType="INTEGER" property="inDays" />
    <result column="infected_times" jdbcType="INTEGER" property="infectedTimes" />
    <result column="injury_poisoning_code" jdbcType="VARCHAR" property="injuryPoisoningCode" />
    <result column="injury_poisoning_causes" jdbcType="VARCHAR" property="injuryPoisoningCauses" />
    <result column="is_allergic" jdbcType="VARCHAR" property="isAllergic" />
    <result column="allergen_drug" jdbcType="VARCHAR" property="allergenDrug" />
    <result column="hbsag" jdbcType="VARCHAR" property="hbsag" />
    <result column="hcv_ab" jdbcType="VARCHAR" property="hcvAb" />
    <result column="hiv_ab" jdbcType="VARCHAR" property="hivAb" />
    <result column="outp_dis_diag_conformity" jdbcType="VARCHAR" property="outpDisDiagConformity" />
    <result column="admit_dis_diag_conformity" jdbcType="VARCHAR" property="admitDisDiagConformity" />
    <result column="preop_postop_diag_conformity" jdbcType="VARCHAR" property="preopPostopDiagConformity" />
    <result column="clinic_patho_diag_conformity" jdbcType="VARCHAR" property="clinicPathoDiagConformity" />
    <result column="radio_patho_diag_conformity" jdbcType="VARCHAR" property="radioPathoDiagConformity" />
    <result column="rescue_times" jdbcType="INTEGER" property="rescueTimes" />
    <result column="rescue_success_times" jdbcType="INTEGER" property="rescueSuccessTimes" />
    <result column="diag_basis" jdbcType="VARCHAR" property="diagBasis" />
    <result column="differentiation_degree" jdbcType="VARCHAR" property="differentiationDegree" />
    <result column="director" jdbcType="VARCHAR" property="director" />
    <result column="chief_doctor" jdbcType="VARCHAR" property="chiefDoctor" />
    <result column="attending_doctor" jdbcType="VARCHAR" property="attendingDoctor" />
    <result column="resident_doctor" jdbcType="VARCHAR" property="residentDoctor" />
    <result column="responsible_nurse" jdbcType="VARCHAR" property="responsibleNurse" />
    <result column="trainee_doctor" jdbcType="VARCHAR" property="traineeDoctor" />
    <result column="graduate_intern_doctor" jdbcType="VARCHAR" property="graduateInternDoctor" />
    <result column="intern_doctor" jdbcType="VARCHAR" property="internDoctor" />
    <result column="coder_name" jdbcType="VARCHAR" property="coderName" />
    <result column="mr_quality" jdbcType="VARCHAR" property="mrQuality" />
    <result column="quality_control_doctor" jdbcType="VARCHAR" property="qualityControlDoctor" />
    <result column="quality_control_nurse" jdbcType="VARCHAR" property="qualityControlNurse" />
    <result column="quality_confirm_datetime" jdbcType="TIMESTAMP" property="qualityConfirmDatetime" />
    <result column="special_nursing_days" jdbcType="INTEGER" property="specialNursingDays" />
    <result column="first_grade_nursing_days" jdbcType="INTEGER" property="firstGradeNursingDays" />
    <result column="second_grade_nursing_days" jdbcType="INTEGER" property="secondGradeNursingDays" />
    <result column="third_grade_nursing_days" jdbcType="INTEGER" property="thirdGradeNursingDays" />
    <result column="icu_name" jdbcType="VARCHAR" property="icuName" />
    <result column="icu_in_datetime" jdbcType="TIMESTAMP" property="icuInDatetime" />
    <result column="icu_out_datetime" jdbcType="TIMESTAMP" property="icuOutDatetime" />
    <result column="death_patient_autopsy" jdbcType="VARCHAR" property="deathPatientAutopsy" />
    <result column="first_example_in_hospital" jdbcType="VARCHAR" property="firstExampleInHospital" />
    <result column="operation_patient_type" jdbcType="VARCHAR" property="operationPatientType" />
    <result column="follow_up" jdbcType="VARCHAR" property="followUp" />
    <result column="follow_up_weeks" jdbcType="DOUBLE" property="followUpWeeks" />
    <result column="follow_up_months" jdbcType="DOUBLE" property="followUpMonths" />
    <result column="follow_up_years" jdbcType="DOUBLE" property="followUpYears" />
    <result column="demonstration_case" jdbcType="VARCHAR" property="demonstrationCase" />
    <result column="abo_blood_type" jdbcType="VARCHAR" property="aboBloodType" />
    <result column="rh_blood_type" jdbcType="VARCHAR" property="rhBloodType" />
    <result column="adverse_reaction" jdbcType="VARCHAR" property="adverseReaction" />
    <result column="erythrocyte" jdbcType="DOUBLE" property="erythrocyte" />
    <result column="platelet" jdbcType="DOUBLE" property="platelet" />
    <result column="plasma" jdbcType="DOUBLE" property="plasma" />
    <result column="whole_blood" jdbcType="DOUBLE" property="wholeBlood" />
    <result column="autologous_blood_callback" jdbcType="VARCHAR" property="autologousBloodCallback" />
    <result column="others_blood" jdbcType="DOUBLE" property="othersBlood" />
    <result column="age_under_one_year" jdbcType="VARCHAR" property="ageUnderOneYear" />
    <result column="newborn_weight" jdbcType="DOUBLE" property="newbornWeight" />
    <result column="newborn_admit_weight" jdbcType="DOUBLE" property="newbornAdmitWeight" />
    <result column="coma_hours_before_admit" jdbcType="DOUBLE" property="comaHoursBeforeAdmit" />
    <result column="coma_minutes_before_admit" jdbcType="DOUBLE" property="comaMinutesBeforeAdmit" />
    <result column="coma_hours_after_admit" jdbcType="DOUBLE" property="comaHoursAfterAdmit" />
    <result column="coma_minutes_after_admit" jdbcType="DOUBLE" property="comaMinutesAfterAdmit" />
    <result column="respirator_using_time" jdbcType="DOUBLE" property="respiratorUsingTime" />
    <result column="readmit_plan_in_thirty_days" jdbcType="VARCHAR" property="readmitPlanInThirtyDays" />
    <result column="readmit_reason_in_thirty_days" jdbcType="VARCHAR" property="readmitReasonInThirtyDays" />
    <result column="discharge_way" jdbcType="VARCHAR" property="dischargeWay" />
    <result column="thansfer_to_hospital" jdbcType="VARCHAR" property="thansferToHospital" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
    <result column="icu_days" jdbcType="INTEGER" property="icuDays" />
    <result column="ccu_days" jdbcType="INTEGER" property="ccuDays" />
    <result column="tb_patient_treatmrnt" jdbcType="VARCHAR" property="tbPatientTreatmrnt" />
    <result column="tb_resistance_type" jdbcType="VARCHAR" property="tbResistanceType" />
    <result column="tb_sputum_culture" jdbcType="VARCHAR" property="tbSputumCulture" />
    <result column="tb_sputum_smear" jdbcType="VARCHAR" property="tbSputumSmear" />
    <result column="patient_sn_org" jdbcType="VARCHAR" property="patientSnOrg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "hospital_code", "hospital_name", "insurance_card_no", "health_card_no", "pay_way",
    "admission_number", "tpatno", "patient_sn", "visit_sn", "name", "gender", "date_of_birth",
    "age", "marriage", "occupation", "birth_place", "birth_place_province", "birth_place_city",
    "birth_place_country", "nation", "citizenship", "id_no", "home_adress", "home_phone",
    "home_adress_postcode", "work_unit_and_adress", "contact_telephone", "work_unit_postcode",
    "registered_residence", "registered_residence_postcode", "contact_name", "contact_relationship",
    "contact_adress", "contact_phone", "admission_way", "admission_date_time", "dept_admission_to",
    "ward_admission_to", "dept_transfer_from", "discharge_date_time", "dept_discharge_from",
    "ward_discharge_from", "in_days", "infected_times", "injury_poisoning_code", "injury_poisoning_causes",
    "is_allergic", "allergen_drug", "hbsag", "hcv_ab", "hiv_ab", "outp_dis_diag_conformity",
    "admit_dis_diag_conformity", "preop_postop_diag_conformity", "clinic_patho_diag_conformity",
    "radio_patho_diag_conformity", "rescue_times", "rescue_success_times", "diag_basis",
    "differentiation_degree", "director", "chief_doctor", "attending_doctor", "resident_doctor",
    "responsible_nurse", "trainee_doctor", "graduate_intern_doctor", "intern_doctor",
    "coder_name", "mr_quality", "quality_control_doctor", "quality_control_nurse", "quality_confirm_datetime",
    "special_nursing_days", "first_grade_nursing_days", "second_grade_nursing_days",
    "third_grade_nursing_days", "icu_name", "icu_in_datetime", "icu_out_datetime", "death_patient_autopsy",
    "first_example_in_hospital", "operation_patient_type", "follow_up", "follow_up_weeks",
    "follow_up_months", "follow_up_years", "demonstration_case", "abo_blood_type", "rh_blood_type",
    "adverse_reaction", "erythrocyte", "platelet", "plasma", "whole_blood", "autologous_blood_callback",
    "others_blood", "age_under_one_year", "newborn_weight", "newborn_admit_weight", "coma_hours_before_admit",
    "coma_minutes_before_admit", "coma_hours_after_admit", "coma_minutes_after_admit",
    "respirator_using_time", "readmit_plan_in_thirty_days", "readmit_reason_in_thirty_days",
    "discharge_way", "thansfer_to_hospital", "source_path", "pk_id", "data_state", "icu_days",
    "ccu_days", "tb_patient_treatmrnt", "tb_resistance_type", "tb_sputum_culture", "tb_sputum_smear",
    "patient_sn_org"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.MrHomepageExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."mr_homepage"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.MrHomepageExample">
    delete from "public"."mr_homepage"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.MrHomepage">
    insert into "public"."mr_homepage" ("hospital_code", "hospital_name", "insurance_card_no",
      "health_card_no", "pay_way", "admission_number",
      "tpatno", "patient_sn", "visit_sn",
      "name", "gender", "date_of_birth",
      "age", "marriage", "occupation",
      "birth_place", "birth_place_province", "birth_place_city",
      "birth_place_country", "nation", "citizenship",
      "id_no", "home_adress", "home_phone",
      "home_adress_postcode", "work_unit_and_adress",
      "contact_telephone", "work_unit_postcode", "registered_residence",
      "registered_residence_postcode", "contact_name",
      "contact_relationship", "contact_adress", "contact_phone",
      "admission_way", "admission_date_time", "dept_admission_to",
      "ward_admission_to", "dept_transfer_from", "discharge_date_time",
      "dept_discharge_from", "ward_discharge_from", "in_days",
      "infected_times", "injury_poisoning_code", "injury_poisoning_causes",
      "is_allergic", "allergen_drug", "hbsag",
      "hcv_ab", "hiv_ab", "outp_dis_diag_conformity",
      "admit_dis_diag_conformity", "preop_postop_diag_conformity",
      "clinic_patho_diag_conformity", "radio_patho_diag_conformity",
      "rescue_times", "rescue_success_times", "diag_basis",
      "differentiation_degree", "director", "chief_doctor",
      "attending_doctor", "resident_doctor", "responsible_nurse",
      "trainee_doctor", "graduate_intern_doctor", "intern_doctor",
      "coder_name", "mr_quality", "quality_control_doctor",
      "quality_control_nurse", "quality_confirm_datetime",
      "special_nursing_days", "first_grade_nursing_days",
      "second_grade_nursing_days", "third_grade_nursing_days",
      "icu_name", "icu_in_datetime", "icu_out_datetime",
      "death_patient_autopsy", "first_example_in_hospital",
      "operation_patient_type", "follow_up", "follow_up_weeks",
      "follow_up_months", "follow_up_years", "demonstration_case",
      "abo_blood_type", "rh_blood_type", "adverse_reaction",
      "erythrocyte", "platelet", "plasma",
      "whole_blood", "autologous_blood_callback", "others_blood",
      "age_under_one_year", "newborn_weight", "newborn_admit_weight",
      "coma_hours_before_admit", "coma_minutes_before_admit",
      "coma_hours_after_admit", "coma_minutes_after_admit",
      "respirator_using_time", "readmit_plan_in_thirty_days",
      "readmit_reason_in_thirty_days", "discharge_way",
      "thansfer_to_hospital", "source_path", "pk_id",
      "data_state", "icu_days", "ccu_days",
      "tb_patient_treatmrnt", "tb_resistance_type", "tb_sputum_culture",
      "tb_sputum_smear", "patient_sn_org")
    values (#{hospitalCode,jdbcType=VARCHAR}, #{hospitalName,jdbcType=VARCHAR}, #{insuranceCardNo,jdbcType=VARCHAR},
      #{healthCardNo,jdbcType=VARCHAR}, #{payWay,jdbcType=VARCHAR}, #{admissionNumber,jdbcType=INTEGER},
      #{tpatno,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR},
      #{name,jdbcType=VARCHAR}, #{gender,jdbcType=VARCHAR}, #{dateOfBirth,jdbcType=TIMESTAMP},
      #{age,jdbcType=VARCHAR}, #{marriage,jdbcType=VARCHAR}, #{occupation,jdbcType=VARCHAR},
      #{birthPlace,jdbcType=VARCHAR}, #{birthPlaceProvince,jdbcType=VARCHAR}, #{birthPlaceCity,jdbcType=VARCHAR},
      #{birthPlaceCountry,jdbcType=VARCHAR}, #{nation,jdbcType=VARCHAR}, #{citizenship,jdbcType=VARCHAR},
      #{idNo,jdbcType=VARCHAR}, #{homeAdress,jdbcType=VARCHAR}, #{homePhone,jdbcType=VARCHAR},
      #{homeAdressPostcode,jdbcType=VARCHAR}, #{workUnitAndAdress,jdbcType=VARCHAR},
      #{contactTelephone,jdbcType=VARCHAR}, #{workUnitPostcode,jdbcType=VARCHAR}, #{registeredResidence,jdbcType=VARCHAR},
      #{registeredResidencePostcode,jdbcType=VARCHAR}, #{contactName,jdbcType=VARCHAR},
      #{contactRelationship,jdbcType=VARCHAR}, #{contactAdress,jdbcType=VARCHAR}, #{contactPhone,jdbcType=VARCHAR},
      #{admissionWay,jdbcType=VARCHAR}, #{admissionDateTime,jdbcType=TIMESTAMP}, #{deptAdmissionTo,jdbcType=VARCHAR},
      #{wardAdmissionTo,jdbcType=VARCHAR}, #{deptTransferFrom,jdbcType=VARCHAR}, #{dischargeDateTime,jdbcType=TIMESTAMP},
      #{deptDischargeFrom,jdbcType=VARCHAR}, #{wardDischargeFrom,jdbcType=VARCHAR}, #{inDays,jdbcType=INTEGER},
      #{infectedTimes,jdbcType=INTEGER}, #{injuryPoisoningCode,jdbcType=VARCHAR}, #{injuryPoisoningCauses,jdbcType=VARCHAR},
      #{isAllergic,jdbcType=VARCHAR}, #{allergenDrug,jdbcType=VARCHAR}, #{hbsag,jdbcType=VARCHAR},
      #{hcvAb,jdbcType=VARCHAR}, #{hivAb,jdbcType=VARCHAR}, #{outpDisDiagConformity,jdbcType=VARCHAR},
      #{admitDisDiagConformity,jdbcType=VARCHAR}, #{preopPostopDiagConformity,jdbcType=VARCHAR},
      #{clinicPathoDiagConformity,jdbcType=VARCHAR}, #{radioPathoDiagConformity,jdbcType=VARCHAR},
      #{rescueTimes,jdbcType=INTEGER}, #{rescueSuccessTimes,jdbcType=INTEGER}, #{diagBasis,jdbcType=VARCHAR},
      #{differentiationDegree,jdbcType=VARCHAR}, #{director,jdbcType=VARCHAR}, #{chiefDoctor,jdbcType=VARCHAR},
      #{attendingDoctor,jdbcType=VARCHAR}, #{residentDoctor,jdbcType=VARCHAR}, #{responsibleNurse,jdbcType=VARCHAR},
      #{traineeDoctor,jdbcType=VARCHAR}, #{graduateInternDoctor,jdbcType=VARCHAR}, #{internDoctor,jdbcType=VARCHAR},
      #{coderName,jdbcType=VARCHAR}, #{mrQuality,jdbcType=VARCHAR}, #{qualityControlDoctor,jdbcType=VARCHAR},
      #{qualityControlNurse,jdbcType=VARCHAR}, #{qualityConfirmDatetime,jdbcType=TIMESTAMP},
      #{specialNursingDays,jdbcType=INTEGER}, #{firstGradeNursingDays,jdbcType=INTEGER},
      #{secondGradeNursingDays,jdbcType=INTEGER}, #{thirdGradeNursingDays,jdbcType=INTEGER},
      #{icuName,jdbcType=VARCHAR}, #{icuInDatetime,jdbcType=TIMESTAMP}, #{icuOutDatetime,jdbcType=TIMESTAMP},
      #{deathPatientAutopsy,jdbcType=VARCHAR}, #{firstExampleInHospital,jdbcType=VARCHAR},
      #{operationPatientType,jdbcType=VARCHAR}, #{followUp,jdbcType=VARCHAR}, #{followUpWeeks,jdbcType=DOUBLE},
      #{followUpMonths,jdbcType=DOUBLE}, #{followUpYears,jdbcType=DOUBLE}, #{demonstrationCase,jdbcType=VARCHAR},
      #{aboBloodType,jdbcType=VARCHAR}, #{rhBloodType,jdbcType=VARCHAR}, #{adverseReaction,jdbcType=VARCHAR},
      #{erythrocyte,jdbcType=DOUBLE}, #{platelet,jdbcType=DOUBLE}, #{plasma,jdbcType=DOUBLE},
      #{wholeBlood,jdbcType=DOUBLE}, #{autologousBloodCallback,jdbcType=VARCHAR}, #{othersBlood,jdbcType=DOUBLE},
      #{ageUnderOneYear,jdbcType=VARCHAR}, #{newbornWeight,jdbcType=DOUBLE}, #{newbornAdmitWeight,jdbcType=DOUBLE},
      #{comaHoursBeforeAdmit,jdbcType=DOUBLE}, #{comaMinutesBeforeAdmit,jdbcType=DOUBLE},
      #{comaHoursAfterAdmit,jdbcType=DOUBLE}, #{comaMinutesAfterAdmit,jdbcType=DOUBLE},
      #{respiratorUsingTime,jdbcType=DOUBLE}, #{readmitPlanInThirtyDays,jdbcType=VARCHAR},
      #{readmitReasonInThirtyDays,jdbcType=VARCHAR}, #{dischargeWay,jdbcType=VARCHAR},
      #{thansferToHospital,jdbcType=VARCHAR}, #{sourcePath,jdbcType=VARCHAR}, #{pkId,jdbcType=VARCHAR},
      #{dataState,jdbcType=VARCHAR}, #{icuDays,jdbcType=INTEGER}, #{ccuDays,jdbcType=INTEGER},
      #{tbPatientTreatmrnt,jdbcType=VARCHAR}, #{tbResistanceType,jdbcType=VARCHAR}, #{tbSputumCulture,jdbcType=VARCHAR},
      #{tbSputumSmear,jdbcType=VARCHAR}, #{patientSnOrg,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.MrHomepage">
    insert into "public"."mr_homepage"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="hospitalName != null">
        "hospital_name",
      </if>
      <if test="insuranceCardNo != null">
        "insurance_card_no",
      </if>
      <if test="healthCardNo != null">
        "health_card_no",
      </if>
      <if test="payWay != null">
        "pay_way",
      </if>
      <if test="admissionNumber != null">
        "admission_number",
      </if>
      <if test="tpatno != null">
        "tpatno",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="name != null">
        "name",
      </if>
      <if test="gender != null">
        "gender",
      </if>
      <if test="dateOfBirth != null">
        "date_of_birth",
      </if>
      <if test="age != null">
        "age",
      </if>
      <if test="marriage != null">
        "marriage",
      </if>
      <if test="occupation != null">
        "occupation",
      </if>
      <if test="birthPlace != null">
        "birth_place",
      </if>
      <if test="birthPlaceProvince != null">
        "birth_place_province",
      </if>
      <if test="birthPlaceCity != null">
        "birth_place_city",
      </if>
      <if test="birthPlaceCountry != null">
        "birth_place_country",
      </if>
      <if test="nation != null">
        "nation",
      </if>
      <if test="citizenship != null">
        "citizenship",
      </if>
      <if test="idNo != null">
        "id_no",
      </if>
      <if test="homeAdress != null">
        "home_adress",
      </if>
      <if test="homePhone != null">
        "home_phone",
      </if>
      <if test="homeAdressPostcode != null">
        "home_adress_postcode",
      </if>
      <if test="workUnitAndAdress != null">
        "work_unit_and_adress",
      </if>
      <if test="contactTelephone != null">
        "contact_telephone",
      </if>
      <if test="workUnitPostcode != null">
        "work_unit_postcode",
      </if>
      <if test="registeredResidence != null">
        "registered_residence",
      </if>
      <if test="registeredResidencePostcode != null">
        "registered_residence_postcode",
      </if>
      <if test="contactName != null">
        "contact_name",
      </if>
      <if test="contactRelationship != null">
        "contact_relationship",
      </if>
      <if test="contactAdress != null">
        "contact_adress",
      </if>
      <if test="contactPhone != null">
        "contact_phone",
      </if>
      <if test="admissionWay != null">
        "admission_way",
      </if>
      <if test="admissionDateTime != null">
        "admission_date_time",
      </if>
      <if test="deptAdmissionTo != null">
        "dept_admission_to",
      </if>
      <if test="wardAdmissionTo != null">
        "ward_admission_to",
      </if>
      <if test="deptTransferFrom != null">
        "dept_transfer_from",
      </if>
      <if test="dischargeDateTime != null">
        "discharge_date_time",
      </if>
      <if test="deptDischargeFrom != null">
        "dept_discharge_from",
      </if>
      <if test="wardDischargeFrom != null">
        "ward_discharge_from",
      </if>
      <if test="inDays != null">
        "in_days",
      </if>
      <if test="infectedTimes != null">
        "infected_times",
      </if>
      <if test="injuryPoisoningCode != null">
        "injury_poisoning_code",
      </if>
      <if test="injuryPoisoningCauses != null">
        "injury_poisoning_causes",
      </if>
      <if test="isAllergic != null">
        "is_allergic",
      </if>
      <if test="allergenDrug != null">
        "allergen_drug",
      </if>
      <if test="hbsag != null">
        "hbsag",
      </if>
      <if test="hcvAb != null">
        "hcv_ab",
      </if>
      <if test="hivAb != null">
        "hiv_ab",
      </if>
      <if test="outpDisDiagConformity != null">
        "outp_dis_diag_conformity",
      </if>
      <if test="admitDisDiagConformity != null">
        "admit_dis_diag_conformity",
      </if>
      <if test="preopPostopDiagConformity != null">
        "preop_postop_diag_conformity",
      </if>
      <if test="clinicPathoDiagConformity != null">
        "clinic_patho_diag_conformity",
      </if>
      <if test="radioPathoDiagConformity != null">
        "radio_patho_diag_conformity",
      </if>
      <if test="rescueTimes != null">
        "rescue_times",
      </if>
      <if test="rescueSuccessTimes != null">
        "rescue_success_times",
      </if>
      <if test="diagBasis != null">
        "diag_basis",
      </if>
      <if test="differentiationDegree != null">
        "differentiation_degree",
      </if>
      <if test="director != null">
        "director",
      </if>
      <if test="chiefDoctor != null">
        "chief_doctor",
      </if>
      <if test="attendingDoctor != null">
        "attending_doctor",
      </if>
      <if test="residentDoctor != null">
        "resident_doctor",
      </if>
      <if test="responsibleNurse != null">
        "responsible_nurse",
      </if>
      <if test="traineeDoctor != null">
        "trainee_doctor",
      </if>
      <if test="graduateInternDoctor != null">
        "graduate_intern_doctor",
      </if>
      <if test="internDoctor != null">
        "intern_doctor",
      </if>
      <if test="coderName != null">
        "coder_name",
      </if>
      <if test="mrQuality != null">
        "mr_quality",
      </if>
      <if test="qualityControlDoctor != null">
        "quality_control_doctor",
      </if>
      <if test="qualityControlNurse != null">
        "quality_control_nurse",
      </if>
      <if test="qualityConfirmDatetime != null">
        "quality_confirm_datetime",
      </if>
      <if test="specialNursingDays != null">
        "special_nursing_days",
      </if>
      <if test="firstGradeNursingDays != null">
        "first_grade_nursing_days",
      </if>
      <if test="secondGradeNursingDays != null">
        "second_grade_nursing_days",
      </if>
      <if test="thirdGradeNursingDays != null">
        "third_grade_nursing_days",
      </if>
      <if test="icuName != null">
        "icu_name",
      </if>
      <if test="icuInDatetime != null">
        "icu_in_datetime",
      </if>
      <if test="icuOutDatetime != null">
        "icu_out_datetime",
      </if>
      <if test="deathPatientAutopsy != null">
        "death_patient_autopsy",
      </if>
      <if test="firstExampleInHospital != null">
        "first_example_in_hospital",
      </if>
      <if test="operationPatientType != null">
        "operation_patient_type",
      </if>
      <if test="followUp != null">
        "follow_up",
      </if>
      <if test="followUpWeeks != null">
        "follow_up_weeks",
      </if>
      <if test="followUpMonths != null">
        "follow_up_months",
      </if>
      <if test="followUpYears != null">
        "follow_up_years",
      </if>
      <if test="demonstrationCase != null">
        "demonstration_case",
      </if>
      <if test="aboBloodType != null">
        "abo_blood_type",
      </if>
      <if test="rhBloodType != null">
        "rh_blood_type",
      </if>
      <if test="adverseReaction != null">
        "adverse_reaction",
      </if>
      <if test="erythrocyte != null">
        "erythrocyte",
      </if>
      <if test="platelet != null">
        "platelet",
      </if>
      <if test="plasma != null">
        "plasma",
      </if>
      <if test="wholeBlood != null">
        "whole_blood",
      </if>
      <if test="autologousBloodCallback != null">
        "autologous_blood_callback",
      </if>
      <if test="othersBlood != null">
        "others_blood",
      </if>
      <if test="ageUnderOneYear != null">
        "age_under_one_year",
      </if>
      <if test="newbornWeight != null">
        "newborn_weight",
      </if>
      <if test="newbornAdmitWeight != null">
        "newborn_admit_weight",
      </if>
      <if test="comaHoursBeforeAdmit != null">
        "coma_hours_before_admit",
      </if>
      <if test="comaMinutesBeforeAdmit != null">
        "coma_minutes_before_admit",
      </if>
      <if test="comaHoursAfterAdmit != null">
        "coma_hours_after_admit",
      </if>
      <if test="comaMinutesAfterAdmit != null">
        "coma_minutes_after_admit",
      </if>
      <if test="respiratorUsingTime != null">
        "respirator_using_time",
      </if>
      <if test="readmitPlanInThirtyDays != null">
        "readmit_plan_in_thirty_days",
      </if>
      <if test="readmitReasonInThirtyDays != null">
        "readmit_reason_in_thirty_days",
      </if>
      <if test="dischargeWay != null">
        "discharge_way",
      </if>
      <if test="thansferToHospital != null">
        "thansfer_to_hospital",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
      <if test="icuDays != null">
        "icu_days",
      </if>
      <if test="ccuDays != null">
        "ccu_days",
      </if>
      <if test="tbPatientTreatmrnt != null">
        "tb_patient_treatmrnt",
      </if>
      <if test="tbResistanceType != null">
        "tb_resistance_type",
      </if>
      <if test="tbSputumCulture != null">
        "tb_sputum_culture",
      </if>
      <if test="tbSputumSmear != null">
        "tb_sputum_smear",
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="hospitalName != null">
        #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="insuranceCardNo != null">
        #{insuranceCardNo,jdbcType=VARCHAR},
      </if>
      <if test="healthCardNo != null">
        #{healthCardNo,jdbcType=VARCHAR},
      </if>
      <if test="payWay != null">
        #{payWay,jdbcType=VARCHAR},
      </if>
      <if test="admissionNumber != null">
        #{admissionNumber,jdbcType=INTEGER},
      </if>
      <if test="tpatno != null">
        #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=VARCHAR},
      </if>
      <if test="dateOfBirth != null">
        #{dateOfBirth,jdbcType=TIMESTAMP},
      </if>
      <if test="age != null">
        #{age,jdbcType=VARCHAR},
      </if>
      <if test="marriage != null">
        #{marriage,jdbcType=VARCHAR},
      </if>
      <if test="occupation != null">
        #{occupation,jdbcType=VARCHAR},
      </if>
      <if test="birthPlace != null">
        #{birthPlace,jdbcType=VARCHAR},
      </if>
      <if test="birthPlaceProvince != null">
        #{birthPlaceProvince,jdbcType=VARCHAR},
      </if>
      <if test="birthPlaceCity != null">
        #{birthPlaceCity,jdbcType=VARCHAR},
      </if>
      <if test="birthPlaceCountry != null">
        #{birthPlaceCountry,jdbcType=VARCHAR},
      </if>
      <if test="nation != null">
        #{nation,jdbcType=VARCHAR},
      </if>
      <if test="citizenship != null">
        #{citizenship,jdbcType=VARCHAR},
      </if>
      <if test="idNo != null">
        #{idNo,jdbcType=VARCHAR},
      </if>
      <if test="homeAdress != null">
        #{homeAdress,jdbcType=VARCHAR},
      </if>
      <if test="homePhone != null">
        #{homePhone,jdbcType=VARCHAR},
      </if>
      <if test="homeAdressPostcode != null">
        #{homeAdressPostcode,jdbcType=VARCHAR},
      </if>
      <if test="workUnitAndAdress != null">
        #{workUnitAndAdress,jdbcType=VARCHAR},
      </if>
      <if test="contactTelephone != null">
        #{contactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="workUnitPostcode != null">
        #{workUnitPostcode,jdbcType=VARCHAR},
      </if>
      <if test="registeredResidence != null">
        #{registeredResidence,jdbcType=VARCHAR},
      </if>
      <if test="registeredResidencePostcode != null">
        #{registeredResidencePostcode,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null">
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactRelationship != null">
        #{contactRelationship,jdbcType=VARCHAR},
      </if>
      <if test="contactAdress != null">
        #{contactAdress,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="admissionWay != null">
        #{admissionWay,jdbcType=VARCHAR},
      </if>
      <if test="admissionDateTime != null">
        #{admissionDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deptAdmissionTo != null">
        #{deptAdmissionTo,jdbcType=VARCHAR},
      </if>
      <if test="wardAdmissionTo != null">
        #{wardAdmissionTo,jdbcType=VARCHAR},
      </if>
      <if test="deptTransferFrom != null">
        #{deptTransferFrom,jdbcType=VARCHAR},
      </if>
      <if test="dischargeDateTime != null">
        #{dischargeDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deptDischargeFrom != null">
        #{deptDischargeFrom,jdbcType=VARCHAR},
      </if>
      <if test="wardDischargeFrom != null">
        #{wardDischargeFrom,jdbcType=VARCHAR},
      </if>
      <if test="inDays != null">
        #{inDays,jdbcType=INTEGER},
      </if>
      <if test="infectedTimes != null">
        #{infectedTimes,jdbcType=INTEGER},
      </if>
      <if test="injuryPoisoningCode != null">
        #{injuryPoisoningCode,jdbcType=VARCHAR},
      </if>
      <if test="injuryPoisoningCauses != null">
        #{injuryPoisoningCauses,jdbcType=VARCHAR},
      </if>
      <if test="isAllergic != null">
        #{isAllergic,jdbcType=VARCHAR},
      </if>
      <if test="allergenDrug != null">
        #{allergenDrug,jdbcType=VARCHAR},
      </if>
      <if test="hbsag != null">
        #{hbsag,jdbcType=VARCHAR},
      </if>
      <if test="hcvAb != null">
        #{hcvAb,jdbcType=VARCHAR},
      </if>
      <if test="hivAb != null">
        #{hivAb,jdbcType=VARCHAR},
      </if>
      <if test="outpDisDiagConformity != null">
        #{outpDisDiagConformity,jdbcType=VARCHAR},
      </if>
      <if test="admitDisDiagConformity != null">
        #{admitDisDiagConformity,jdbcType=VARCHAR},
      </if>
      <if test="preopPostopDiagConformity != null">
        #{preopPostopDiagConformity,jdbcType=VARCHAR},
      </if>
      <if test="clinicPathoDiagConformity != null">
        #{clinicPathoDiagConformity,jdbcType=VARCHAR},
      </if>
      <if test="radioPathoDiagConformity != null">
        #{radioPathoDiagConformity,jdbcType=VARCHAR},
      </if>
      <if test="rescueTimes != null">
        #{rescueTimes,jdbcType=INTEGER},
      </if>
      <if test="rescueSuccessTimes != null">
        #{rescueSuccessTimes,jdbcType=INTEGER},
      </if>
      <if test="diagBasis != null">
        #{diagBasis,jdbcType=VARCHAR},
      </if>
      <if test="differentiationDegree != null">
        #{differentiationDegree,jdbcType=VARCHAR},
      </if>
      <if test="director != null">
        #{director,jdbcType=VARCHAR},
      </if>
      <if test="chiefDoctor != null">
        #{chiefDoctor,jdbcType=VARCHAR},
      </if>
      <if test="attendingDoctor != null">
        #{attendingDoctor,jdbcType=VARCHAR},
      </if>
      <if test="residentDoctor != null">
        #{residentDoctor,jdbcType=VARCHAR},
      </if>
      <if test="responsibleNurse != null">
        #{responsibleNurse,jdbcType=VARCHAR},
      </if>
      <if test="traineeDoctor != null">
        #{traineeDoctor,jdbcType=VARCHAR},
      </if>
      <if test="graduateInternDoctor != null">
        #{graduateInternDoctor,jdbcType=VARCHAR},
      </if>
      <if test="internDoctor != null">
        #{internDoctor,jdbcType=VARCHAR},
      </if>
      <if test="coderName != null">
        #{coderName,jdbcType=VARCHAR},
      </if>
      <if test="mrQuality != null">
        #{mrQuality,jdbcType=VARCHAR},
      </if>
      <if test="qualityControlDoctor != null">
        #{qualityControlDoctor,jdbcType=VARCHAR},
      </if>
      <if test="qualityControlNurse != null">
        #{qualityControlNurse,jdbcType=VARCHAR},
      </if>
      <if test="qualityConfirmDatetime != null">
        #{qualityConfirmDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="specialNursingDays != null">
        #{specialNursingDays,jdbcType=INTEGER},
      </if>
      <if test="firstGradeNursingDays != null">
        #{firstGradeNursingDays,jdbcType=INTEGER},
      </if>
      <if test="secondGradeNursingDays != null">
        #{secondGradeNursingDays,jdbcType=INTEGER},
      </if>
      <if test="thirdGradeNursingDays != null">
        #{thirdGradeNursingDays,jdbcType=INTEGER},
      </if>
      <if test="icuName != null">
        #{icuName,jdbcType=VARCHAR},
      </if>
      <if test="icuInDatetime != null">
        #{icuInDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="icuOutDatetime != null">
        #{icuOutDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="deathPatientAutopsy != null">
        #{deathPatientAutopsy,jdbcType=VARCHAR},
      </if>
      <if test="firstExampleInHospital != null">
        #{firstExampleInHospital,jdbcType=VARCHAR},
      </if>
      <if test="operationPatientType != null">
        #{operationPatientType,jdbcType=VARCHAR},
      </if>
      <if test="followUp != null">
        #{followUp,jdbcType=VARCHAR},
      </if>
      <if test="followUpWeeks != null">
        #{followUpWeeks,jdbcType=DOUBLE},
      </if>
      <if test="followUpMonths != null">
        #{followUpMonths,jdbcType=DOUBLE},
      </if>
      <if test="followUpYears != null">
        #{followUpYears,jdbcType=DOUBLE},
      </if>
      <if test="demonstrationCase != null">
        #{demonstrationCase,jdbcType=VARCHAR},
      </if>
      <if test="aboBloodType != null">
        #{aboBloodType,jdbcType=VARCHAR},
      </if>
      <if test="rhBloodType != null">
        #{rhBloodType,jdbcType=VARCHAR},
      </if>
      <if test="adverseReaction != null">
        #{adverseReaction,jdbcType=VARCHAR},
      </if>
      <if test="erythrocyte != null">
        #{erythrocyte,jdbcType=DOUBLE},
      </if>
      <if test="platelet != null">
        #{platelet,jdbcType=DOUBLE},
      </if>
      <if test="plasma != null">
        #{plasma,jdbcType=DOUBLE},
      </if>
      <if test="wholeBlood != null">
        #{wholeBlood,jdbcType=DOUBLE},
      </if>
      <if test="autologousBloodCallback != null">
        #{autologousBloodCallback,jdbcType=VARCHAR},
      </if>
      <if test="othersBlood != null">
        #{othersBlood,jdbcType=DOUBLE},
      </if>
      <if test="ageUnderOneYear != null">
        #{ageUnderOneYear,jdbcType=VARCHAR},
      </if>
      <if test="newbornWeight != null">
        #{newbornWeight,jdbcType=DOUBLE},
      </if>
      <if test="newbornAdmitWeight != null">
        #{newbornAdmitWeight,jdbcType=DOUBLE},
      </if>
      <if test="comaHoursBeforeAdmit != null">
        #{comaHoursBeforeAdmit,jdbcType=DOUBLE},
      </if>
      <if test="comaMinutesBeforeAdmit != null">
        #{comaMinutesBeforeAdmit,jdbcType=DOUBLE},
      </if>
      <if test="comaHoursAfterAdmit != null">
        #{comaHoursAfterAdmit,jdbcType=DOUBLE},
      </if>
      <if test="comaMinutesAfterAdmit != null">
        #{comaMinutesAfterAdmit,jdbcType=DOUBLE},
      </if>
      <if test="respiratorUsingTime != null">
        #{respiratorUsingTime,jdbcType=DOUBLE},
      </if>
      <if test="readmitPlanInThirtyDays != null">
        #{readmitPlanInThirtyDays,jdbcType=VARCHAR},
      </if>
      <if test="readmitReasonInThirtyDays != null">
        #{readmitReasonInThirtyDays,jdbcType=VARCHAR},
      </if>
      <if test="dischargeWay != null">
        #{dischargeWay,jdbcType=VARCHAR},
      </if>
      <if test="thansferToHospital != null">
        #{thansferToHospital,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="icuDays != null">
        #{icuDays,jdbcType=INTEGER},
      </if>
      <if test="ccuDays != null">
        #{ccuDays,jdbcType=INTEGER},
      </if>
      <if test="tbPatientTreatmrnt != null">
        #{tbPatientTreatmrnt,jdbcType=VARCHAR},
      </if>
      <if test="tbResistanceType != null">
        #{tbResistanceType,jdbcType=VARCHAR},
      </if>
      <if test="tbSputumCulture != null">
        #{tbSputumCulture,jdbcType=VARCHAR},
      </if>
      <if test="tbSputumSmear != null">
        #{tbSputumSmear,jdbcType=VARCHAR},
      </if>
      <if test="patientSnOrg != null">
        #{patientSnOrg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.MrHomepageExample" resultType="java.lang.Long">
    select count(*) from "public"."mr_homepage"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."mr_homepage"
    <set>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalName != null">
        "hospital_name" = #{record.hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="record.insuranceCardNo != null">
        "insurance_card_no" = #{record.insuranceCardNo,jdbcType=VARCHAR},
      </if>
      <if test="record.healthCardNo != null">
        "health_card_no" = #{record.healthCardNo,jdbcType=VARCHAR},
      </if>
      <if test="record.payWay != null">
        "pay_way" = #{record.payWay,jdbcType=VARCHAR},
      </if>
      <if test="record.admissionNumber != null">
        "admission_number" = #{record.admissionNumber,jdbcType=INTEGER},
      </if>
      <if test="record.tpatno != null">
        "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        "name" = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.gender != null">
        "gender" = #{record.gender,jdbcType=VARCHAR},
      </if>
      <if test="record.dateOfBirth != null">
        "date_of_birth" = #{record.dateOfBirth,jdbcType=TIMESTAMP},
      </if>
      <if test="record.age != null">
        "age" = #{record.age,jdbcType=VARCHAR},
      </if>
      <if test="record.marriage != null">
        "marriage" = #{record.marriage,jdbcType=VARCHAR},
      </if>
      <if test="record.occupation != null">
        "occupation" = #{record.occupation,jdbcType=VARCHAR},
      </if>
      <if test="record.birthPlace != null">
        "birth_place" = #{record.birthPlace,jdbcType=VARCHAR},
      </if>
      <if test="record.birthPlaceProvince != null">
        "birth_place_province" = #{record.birthPlaceProvince,jdbcType=VARCHAR},
      </if>
      <if test="record.birthPlaceCity != null">
        "birth_place_city" = #{record.birthPlaceCity,jdbcType=VARCHAR},
      </if>
      <if test="record.birthPlaceCountry != null">
        "birth_place_country" = #{record.birthPlaceCountry,jdbcType=VARCHAR},
      </if>
      <if test="record.nation != null">
        "nation" = #{record.nation,jdbcType=VARCHAR},
      </if>
      <if test="record.citizenship != null">
        "citizenship" = #{record.citizenship,jdbcType=VARCHAR},
      </if>
      <if test="record.idNo != null">
        "id_no" = #{record.idNo,jdbcType=VARCHAR},
      </if>
      <if test="record.homeAdress != null">
        "home_adress" = #{record.homeAdress,jdbcType=VARCHAR},
      </if>
      <if test="record.homePhone != null">
        "home_phone" = #{record.homePhone,jdbcType=VARCHAR},
      </if>
      <if test="record.homeAdressPostcode != null">
        "home_adress_postcode" = #{record.homeAdressPostcode,jdbcType=VARCHAR},
      </if>
      <if test="record.workUnitAndAdress != null">
        "work_unit_and_adress" = #{record.workUnitAndAdress,jdbcType=VARCHAR},
      </if>
      <if test="record.contactTelephone != null">
        "contact_telephone" = #{record.contactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="record.workUnitPostcode != null">
        "work_unit_postcode" = #{record.workUnitPostcode,jdbcType=VARCHAR},
      </if>
      <if test="record.registeredResidence != null">
        "registered_residence" = #{record.registeredResidence,jdbcType=VARCHAR},
      </if>
      <if test="record.registeredResidencePostcode != null">
        "registered_residence_postcode" = #{record.registeredResidencePostcode,jdbcType=VARCHAR},
      </if>
      <if test="record.contactName != null">
        "contact_name" = #{record.contactName,jdbcType=VARCHAR},
      </if>
      <if test="record.contactRelationship != null">
        "contact_relationship" = #{record.contactRelationship,jdbcType=VARCHAR},
      </if>
      <if test="record.contactAdress != null">
        "contact_adress" = #{record.contactAdress,jdbcType=VARCHAR},
      </if>
      <if test="record.contactPhone != null">
        "contact_phone" = #{record.contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.admissionWay != null">
        "admission_way" = #{record.admissionWay,jdbcType=VARCHAR},
      </if>
      <if test="record.admissionDateTime != null">
        "admission_date_time" = #{record.admissionDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deptAdmissionTo != null">
        "dept_admission_to" = #{record.deptAdmissionTo,jdbcType=VARCHAR},
      </if>
      <if test="record.wardAdmissionTo != null">
        "ward_admission_to" = #{record.wardAdmissionTo,jdbcType=VARCHAR},
      </if>
      <if test="record.deptTransferFrom != null">
        "dept_transfer_from" = #{record.deptTransferFrom,jdbcType=VARCHAR},
      </if>
      <if test="record.dischargeDateTime != null">
        "discharge_date_time" = #{record.dischargeDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deptDischargeFrom != null">
        "dept_discharge_from" = #{record.deptDischargeFrom,jdbcType=VARCHAR},
      </if>
      <if test="record.wardDischargeFrom != null">
        "ward_discharge_from" = #{record.wardDischargeFrom,jdbcType=VARCHAR},
      </if>
      <if test="record.inDays != null">
        "in_days" = #{record.inDays,jdbcType=INTEGER},
      </if>
      <if test="record.infectedTimes != null">
        "infected_times" = #{record.infectedTimes,jdbcType=INTEGER},
      </if>
      <if test="record.injuryPoisoningCode != null">
        "injury_poisoning_code" = #{record.injuryPoisoningCode,jdbcType=VARCHAR},
      </if>
      <if test="record.injuryPoisoningCauses != null">
        "injury_poisoning_causes" = #{record.injuryPoisoningCauses,jdbcType=VARCHAR},
      </if>
      <if test="record.isAllergic != null">
        "is_allergic" = #{record.isAllergic,jdbcType=VARCHAR},
      </if>
      <if test="record.allergenDrug != null">
        "allergen_drug" = #{record.allergenDrug,jdbcType=VARCHAR},
      </if>
      <if test="record.hbsag != null">
        "hbsag" = #{record.hbsag,jdbcType=VARCHAR},
      </if>
      <if test="record.hcvAb != null">
        "hcv_ab" = #{record.hcvAb,jdbcType=VARCHAR},
      </if>
      <if test="record.hivAb != null">
        "hiv_ab" = #{record.hivAb,jdbcType=VARCHAR},
      </if>
      <if test="record.outpDisDiagConformity != null">
        "outp_dis_diag_conformity" = #{record.outpDisDiagConformity,jdbcType=VARCHAR},
      </if>
      <if test="record.admitDisDiagConformity != null">
        "admit_dis_diag_conformity" = #{record.admitDisDiagConformity,jdbcType=VARCHAR},
      </if>
      <if test="record.preopPostopDiagConformity != null">
        "preop_postop_diag_conformity" = #{record.preopPostopDiagConformity,jdbcType=VARCHAR},
      </if>
      <if test="record.clinicPathoDiagConformity != null">
        "clinic_patho_diag_conformity" = #{record.clinicPathoDiagConformity,jdbcType=VARCHAR},
      </if>
      <if test="record.radioPathoDiagConformity != null">
        "radio_patho_diag_conformity" = #{record.radioPathoDiagConformity,jdbcType=VARCHAR},
      </if>
      <if test="record.rescueTimes != null">
        "rescue_times" = #{record.rescueTimes,jdbcType=INTEGER},
      </if>
      <if test="record.rescueSuccessTimes != null">
        "rescue_success_times" = #{record.rescueSuccessTimes,jdbcType=INTEGER},
      </if>
      <if test="record.diagBasis != null">
        "diag_basis" = #{record.diagBasis,jdbcType=VARCHAR},
      </if>
      <if test="record.differentiationDegree != null">
        "differentiation_degree" = #{record.differentiationDegree,jdbcType=VARCHAR},
      </if>
      <if test="record.director != null">
        "director" = #{record.director,jdbcType=VARCHAR},
      </if>
      <if test="record.chiefDoctor != null">
        "chief_doctor" = #{record.chiefDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.attendingDoctor != null">
        "attending_doctor" = #{record.attendingDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.residentDoctor != null">
        "resident_doctor" = #{record.residentDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.responsibleNurse != null">
        "responsible_nurse" = #{record.responsibleNurse,jdbcType=VARCHAR},
      </if>
      <if test="record.traineeDoctor != null">
        "trainee_doctor" = #{record.traineeDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.graduateInternDoctor != null">
        "graduate_intern_doctor" = #{record.graduateInternDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.internDoctor != null">
        "intern_doctor" = #{record.internDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.coderName != null">
        "coder_name" = #{record.coderName,jdbcType=VARCHAR},
      </if>
      <if test="record.mrQuality != null">
        "mr_quality" = #{record.mrQuality,jdbcType=VARCHAR},
      </if>
      <if test="record.qualityControlDoctor != null">
        "quality_control_doctor" = #{record.qualityControlDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.qualityControlNurse != null">
        "quality_control_nurse" = #{record.qualityControlNurse,jdbcType=VARCHAR},
      </if>
      <if test="record.qualityConfirmDatetime != null">
        "quality_confirm_datetime" = #{record.qualityConfirmDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.specialNursingDays != null">
        "special_nursing_days" = #{record.specialNursingDays,jdbcType=INTEGER},
      </if>
      <if test="record.firstGradeNursingDays != null">
        "first_grade_nursing_days" = #{record.firstGradeNursingDays,jdbcType=INTEGER},
      </if>
      <if test="record.secondGradeNursingDays != null">
        "second_grade_nursing_days" = #{record.secondGradeNursingDays,jdbcType=INTEGER},
      </if>
      <if test="record.thirdGradeNursingDays != null">
        "third_grade_nursing_days" = #{record.thirdGradeNursingDays,jdbcType=INTEGER},
      </if>
      <if test="record.icuName != null">
        "icu_name" = #{record.icuName,jdbcType=VARCHAR},
      </if>
      <if test="record.icuInDatetime != null">
        "icu_in_datetime" = #{record.icuInDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.icuOutDatetime != null">
        "icu_out_datetime" = #{record.icuOutDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deathPatientAutopsy != null">
        "death_patient_autopsy" = #{record.deathPatientAutopsy,jdbcType=VARCHAR},
      </if>
      <if test="record.firstExampleInHospital != null">
        "first_example_in_hospital" = #{record.firstExampleInHospital,jdbcType=VARCHAR},
      </if>
      <if test="record.operationPatientType != null">
        "operation_patient_type" = #{record.operationPatientType,jdbcType=VARCHAR},
      </if>
      <if test="record.followUp != null">
        "follow_up" = #{record.followUp,jdbcType=VARCHAR},
      </if>
      <if test="record.followUpWeeks != null">
        "follow_up_weeks" = #{record.followUpWeeks,jdbcType=DOUBLE},
      </if>
      <if test="record.followUpMonths != null">
        "follow_up_months" = #{record.followUpMonths,jdbcType=DOUBLE},
      </if>
      <if test="record.followUpYears != null">
        "follow_up_years" = #{record.followUpYears,jdbcType=DOUBLE},
      </if>
      <if test="record.demonstrationCase != null">
        "demonstration_case" = #{record.demonstrationCase,jdbcType=VARCHAR},
      </if>
      <if test="record.aboBloodType != null">
        "abo_blood_type" = #{record.aboBloodType,jdbcType=VARCHAR},
      </if>
      <if test="record.rhBloodType != null">
        "rh_blood_type" = #{record.rhBloodType,jdbcType=VARCHAR},
      </if>
      <if test="record.adverseReaction != null">
        "adverse_reaction" = #{record.adverseReaction,jdbcType=VARCHAR},
      </if>
      <if test="record.erythrocyte != null">
        "erythrocyte" = #{record.erythrocyte,jdbcType=DOUBLE},
      </if>
      <if test="record.platelet != null">
        "platelet" = #{record.platelet,jdbcType=DOUBLE},
      </if>
      <if test="record.plasma != null">
        "plasma" = #{record.plasma,jdbcType=DOUBLE},
      </if>
      <if test="record.wholeBlood != null">
        "whole_blood" = #{record.wholeBlood,jdbcType=DOUBLE},
      </if>
      <if test="record.autologousBloodCallback != null">
        "autologous_blood_callback" = #{record.autologousBloodCallback,jdbcType=VARCHAR},
      </if>
      <if test="record.othersBlood != null">
        "others_blood" = #{record.othersBlood,jdbcType=DOUBLE},
      </if>
      <if test="record.ageUnderOneYear != null">
        "age_under_one_year" = #{record.ageUnderOneYear,jdbcType=VARCHAR},
      </if>
      <if test="record.newbornWeight != null">
        "newborn_weight" = #{record.newbornWeight,jdbcType=DOUBLE},
      </if>
      <if test="record.newbornAdmitWeight != null">
        "newborn_admit_weight" = #{record.newbornAdmitWeight,jdbcType=DOUBLE},
      </if>
      <if test="record.comaHoursBeforeAdmit != null">
        "coma_hours_before_admit" = #{record.comaHoursBeforeAdmit,jdbcType=DOUBLE},
      </if>
      <if test="record.comaMinutesBeforeAdmit != null">
        "coma_minutes_before_admit" = #{record.comaMinutesBeforeAdmit,jdbcType=DOUBLE},
      </if>
      <if test="record.comaHoursAfterAdmit != null">
        "coma_hours_after_admit" = #{record.comaHoursAfterAdmit,jdbcType=DOUBLE},
      </if>
      <if test="record.comaMinutesAfterAdmit != null">
        "coma_minutes_after_admit" = #{record.comaMinutesAfterAdmit,jdbcType=DOUBLE},
      </if>
      <if test="record.respiratorUsingTime != null">
        "respirator_using_time" = #{record.respiratorUsingTime,jdbcType=DOUBLE},
      </if>
      <if test="record.readmitPlanInThirtyDays != null">
        "readmit_plan_in_thirty_days" = #{record.readmitPlanInThirtyDays,jdbcType=VARCHAR},
      </if>
      <if test="record.readmitReasonInThirtyDays != null">
        "readmit_reason_in_thirty_days" = #{record.readmitReasonInThirtyDays,jdbcType=VARCHAR},
      </if>
      <if test="record.dischargeWay != null">
        "discharge_way" = #{record.dischargeWay,jdbcType=VARCHAR},
      </if>
      <if test="record.thansferToHospital != null">
        "thansfer_to_hospital" = #{record.thansferToHospital,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
      <if test="record.icuDays != null">
        "icu_days" = #{record.icuDays,jdbcType=INTEGER},
      </if>
      <if test="record.ccuDays != null">
        "ccu_days" = #{record.ccuDays,jdbcType=INTEGER},
      </if>
      <if test="record.tbPatientTreatmrnt != null">
        "tb_patient_treatmrnt" = #{record.tbPatientTreatmrnt,jdbcType=VARCHAR},
      </if>
      <if test="record.tbResistanceType != null">
        "tb_resistance_type" = #{record.tbResistanceType,jdbcType=VARCHAR},
      </if>
      <if test="record.tbSputumCulture != null">
        "tb_sputum_culture" = #{record.tbSputumCulture,jdbcType=VARCHAR},
      </if>
      <if test="record.tbSputumSmear != null">
        "tb_sputum_smear" = #{record.tbSputumSmear,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSnOrg != null">
        "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."mr_homepage"
    set "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "hospital_name" = #{record.hospitalName,jdbcType=VARCHAR},
      "insurance_card_no" = #{record.insuranceCardNo,jdbcType=VARCHAR},
      "health_card_no" = #{record.healthCardNo,jdbcType=VARCHAR},
      "pay_way" = #{record.payWay,jdbcType=VARCHAR},
      "admission_number" = #{record.admissionNumber,jdbcType=INTEGER},
      "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "name" = #{record.name,jdbcType=VARCHAR},
      "gender" = #{record.gender,jdbcType=VARCHAR},
      "date_of_birth" = #{record.dateOfBirth,jdbcType=TIMESTAMP},
      "age" = #{record.age,jdbcType=VARCHAR},
      "marriage" = #{record.marriage,jdbcType=VARCHAR},
      "occupation" = #{record.occupation,jdbcType=VARCHAR},
      "birth_place" = #{record.birthPlace,jdbcType=VARCHAR},
      "birth_place_province" = #{record.birthPlaceProvince,jdbcType=VARCHAR},
      "birth_place_city" = #{record.birthPlaceCity,jdbcType=VARCHAR},
      "birth_place_country" = #{record.birthPlaceCountry,jdbcType=VARCHAR},
      "nation" = #{record.nation,jdbcType=VARCHAR},
      "citizenship" = #{record.citizenship,jdbcType=VARCHAR},
      "id_no" = #{record.idNo,jdbcType=VARCHAR},
      "home_adress" = #{record.homeAdress,jdbcType=VARCHAR},
      "home_phone" = #{record.homePhone,jdbcType=VARCHAR},
      "home_adress_postcode" = #{record.homeAdressPostcode,jdbcType=VARCHAR},
      "work_unit_and_adress" = #{record.workUnitAndAdress,jdbcType=VARCHAR},
      "contact_telephone" = #{record.contactTelephone,jdbcType=VARCHAR},
      "work_unit_postcode" = #{record.workUnitPostcode,jdbcType=VARCHAR},
      "registered_residence" = #{record.registeredResidence,jdbcType=VARCHAR},
      "registered_residence_postcode" = #{record.registeredResidencePostcode,jdbcType=VARCHAR},
      "contact_name" = #{record.contactName,jdbcType=VARCHAR},
      "contact_relationship" = #{record.contactRelationship,jdbcType=VARCHAR},
      "contact_adress" = #{record.contactAdress,jdbcType=VARCHAR},
      "contact_phone" = #{record.contactPhone,jdbcType=VARCHAR},
      "admission_way" = #{record.admissionWay,jdbcType=VARCHAR},
      "admission_date_time" = #{record.admissionDateTime,jdbcType=TIMESTAMP},
      "dept_admission_to" = #{record.deptAdmissionTo,jdbcType=VARCHAR},
      "ward_admission_to" = #{record.wardAdmissionTo,jdbcType=VARCHAR},
      "dept_transfer_from" = #{record.deptTransferFrom,jdbcType=VARCHAR},
      "discharge_date_time" = #{record.dischargeDateTime,jdbcType=TIMESTAMP},
      "dept_discharge_from" = #{record.deptDischargeFrom,jdbcType=VARCHAR},
      "ward_discharge_from" = #{record.wardDischargeFrom,jdbcType=VARCHAR},
      "in_days" = #{record.inDays,jdbcType=INTEGER},
      "infected_times" = #{record.infectedTimes,jdbcType=INTEGER},
      "injury_poisoning_code" = #{record.injuryPoisoningCode,jdbcType=VARCHAR},
      "injury_poisoning_causes" = #{record.injuryPoisoningCauses,jdbcType=VARCHAR},
      "is_allergic" = #{record.isAllergic,jdbcType=VARCHAR},
      "allergen_drug" = #{record.allergenDrug,jdbcType=VARCHAR},
      "hbsag" = #{record.hbsag,jdbcType=VARCHAR},
      "hcv_ab" = #{record.hcvAb,jdbcType=VARCHAR},
      "hiv_ab" = #{record.hivAb,jdbcType=VARCHAR},
      "outp_dis_diag_conformity" = #{record.outpDisDiagConformity,jdbcType=VARCHAR},
      "admit_dis_diag_conformity" = #{record.admitDisDiagConformity,jdbcType=VARCHAR},
      "preop_postop_diag_conformity" = #{record.preopPostopDiagConformity,jdbcType=VARCHAR},
      "clinic_patho_diag_conformity" = #{record.clinicPathoDiagConformity,jdbcType=VARCHAR},
      "radio_patho_diag_conformity" = #{record.radioPathoDiagConformity,jdbcType=VARCHAR},
      "rescue_times" = #{record.rescueTimes,jdbcType=INTEGER},
      "rescue_success_times" = #{record.rescueSuccessTimes,jdbcType=INTEGER},
      "diag_basis" = #{record.diagBasis,jdbcType=VARCHAR},
      "differentiation_degree" = #{record.differentiationDegree,jdbcType=VARCHAR},
      "director" = #{record.director,jdbcType=VARCHAR},
      "chief_doctor" = #{record.chiefDoctor,jdbcType=VARCHAR},
      "attending_doctor" = #{record.attendingDoctor,jdbcType=VARCHAR},
      "resident_doctor" = #{record.residentDoctor,jdbcType=VARCHAR},
      "responsible_nurse" = #{record.responsibleNurse,jdbcType=VARCHAR},
      "trainee_doctor" = #{record.traineeDoctor,jdbcType=VARCHAR},
      "graduate_intern_doctor" = #{record.graduateInternDoctor,jdbcType=VARCHAR},
      "intern_doctor" = #{record.internDoctor,jdbcType=VARCHAR},
      "coder_name" = #{record.coderName,jdbcType=VARCHAR},
      "mr_quality" = #{record.mrQuality,jdbcType=VARCHAR},
      "quality_control_doctor" = #{record.qualityControlDoctor,jdbcType=VARCHAR},
      "quality_control_nurse" = #{record.qualityControlNurse,jdbcType=VARCHAR},
      "quality_confirm_datetime" = #{record.qualityConfirmDatetime,jdbcType=TIMESTAMP},
      "special_nursing_days" = #{record.specialNursingDays,jdbcType=INTEGER},
      "first_grade_nursing_days" = #{record.firstGradeNursingDays,jdbcType=INTEGER},
      "second_grade_nursing_days" = #{record.secondGradeNursingDays,jdbcType=INTEGER},
      "third_grade_nursing_days" = #{record.thirdGradeNursingDays,jdbcType=INTEGER},
      "icu_name" = #{record.icuName,jdbcType=VARCHAR},
      "icu_in_datetime" = #{record.icuInDatetime,jdbcType=TIMESTAMP},
      "icu_out_datetime" = #{record.icuOutDatetime,jdbcType=TIMESTAMP},
      "death_patient_autopsy" = #{record.deathPatientAutopsy,jdbcType=VARCHAR},
      "first_example_in_hospital" = #{record.firstExampleInHospital,jdbcType=VARCHAR},
      "operation_patient_type" = #{record.operationPatientType,jdbcType=VARCHAR},
      "follow_up" = #{record.followUp,jdbcType=VARCHAR},
      "follow_up_weeks" = #{record.followUpWeeks,jdbcType=DOUBLE},
      "follow_up_months" = #{record.followUpMonths,jdbcType=DOUBLE},
      "follow_up_years" = #{record.followUpYears,jdbcType=DOUBLE},
      "demonstration_case" = #{record.demonstrationCase,jdbcType=VARCHAR},
      "abo_blood_type" = #{record.aboBloodType,jdbcType=VARCHAR},
      "rh_blood_type" = #{record.rhBloodType,jdbcType=VARCHAR},
      "adverse_reaction" = #{record.adverseReaction,jdbcType=VARCHAR},
      "erythrocyte" = #{record.erythrocyte,jdbcType=DOUBLE},
      "platelet" = #{record.platelet,jdbcType=DOUBLE},
      "plasma" = #{record.plasma,jdbcType=DOUBLE},
      "whole_blood" = #{record.wholeBlood,jdbcType=DOUBLE},
      "autologous_blood_callback" = #{record.autologousBloodCallback,jdbcType=VARCHAR},
      "others_blood" = #{record.othersBlood,jdbcType=DOUBLE},
      "age_under_one_year" = #{record.ageUnderOneYear,jdbcType=VARCHAR},
      "newborn_weight" = #{record.newbornWeight,jdbcType=DOUBLE},
      "newborn_admit_weight" = #{record.newbornAdmitWeight,jdbcType=DOUBLE},
      "coma_hours_before_admit" = #{record.comaHoursBeforeAdmit,jdbcType=DOUBLE},
      "coma_minutes_before_admit" = #{record.comaMinutesBeforeAdmit,jdbcType=DOUBLE},
      "coma_hours_after_admit" = #{record.comaHoursAfterAdmit,jdbcType=DOUBLE},
      "coma_minutes_after_admit" = #{record.comaMinutesAfterAdmit,jdbcType=DOUBLE},
      "respirator_using_time" = #{record.respiratorUsingTime,jdbcType=DOUBLE},
      "readmit_plan_in_thirty_days" = #{record.readmitPlanInThirtyDays,jdbcType=VARCHAR},
      "readmit_reason_in_thirty_days" = #{record.readmitReasonInThirtyDays,jdbcType=VARCHAR},
      "discharge_way" = #{record.dischargeWay,jdbcType=VARCHAR},
      "thansfer_to_hospital" = #{record.thansferToHospital,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR},
      "icu_days" = #{record.icuDays,jdbcType=INTEGER},
      "ccu_days" = #{record.ccuDays,jdbcType=INTEGER},
      "tb_patient_treatmrnt" = #{record.tbPatientTreatmrnt,jdbcType=VARCHAR},
      "tb_resistance_type" = #{record.tbResistanceType,jdbcType=VARCHAR},
      "tb_sputum_culture" = #{record.tbSputumCulture,jdbcType=VARCHAR},
      "tb_sputum_smear" = #{record.tbSputumSmear,jdbcType=VARCHAR},
      "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.MrHomepage">
    update "public"."mr_homepage"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="hospitalName != null">
        "hospital_name" = #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="insuranceCardNo != null">
        "insurance_card_no" = #{insuranceCardNo,jdbcType=VARCHAR},
      </if>
      <if test="healthCardNo != null">
        "health_card_no" = #{healthCardNo,jdbcType=VARCHAR},
      </if>
      <if test="payWay != null">
        "pay_way" = #{payWay,jdbcType=VARCHAR},
      </if>
      <if test="admissionNumber != null">
        "admission_number" = #{admissionNumber,jdbcType=INTEGER},
      </if>
      <if test="tpatno != null">
        "tpatno" = #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        "name" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        "gender" = #{gender,jdbcType=VARCHAR},
      </if>
      <if test="dateOfBirth != null">
        "date_of_birth" = #{dateOfBirth,jdbcType=TIMESTAMP},
      </if>
      <if test="age != null">
        "age" = #{age,jdbcType=INTEGER},
      </if>
      <if test="marriage != null">
        "marriage" = #{marriage,jdbcType=VARCHAR},
      </if>
      <if test="occupation != null">
        "occupation" = #{occupation,jdbcType=VARCHAR},
      </if>
      <if test="birthPlaceProvince != null">
        "birth_place_province" = #{birthPlaceProvince,jdbcType=VARCHAR},
      </if>
      <if test="birthPlaceCity != null">
        "birth_place_city" = #{birthPlaceCity,jdbcType=VARCHAR},
      </if>
      <if test="birthPlaceCountry != null">
        "birth_place_country" = #{birthPlaceCountry,jdbcType=VARCHAR},
      </if>
      <if test="nation != null">
        "nation" = #{nation,jdbcType=VARCHAR},
      </if>
      <if test="citizenship != null">
        "citizenship" = #{citizenship,jdbcType=VARCHAR},
      </if>
      <if test="idNo != null">
        "id_no" = #{idNo,jdbcType=VARCHAR},
      </if>
      <if test="homeAdress != null">
        "home_adress" = #{homeAdress,jdbcType=VARCHAR},
      </if>
      <if test="homePhone != null">
        "home_phone" = #{homePhone,jdbcType=VARCHAR},
      </if>
      <if test="homeAdressPostcode != null">
        "home_adress_postcode" = #{homeAdressPostcode,jdbcType=VARCHAR},
      </if>
      <if test="workUnitAndAdress != null">
        "work_unit_and_adress" = #{workUnitAndAdress,jdbcType=VARCHAR},
      </if>
      <if test="contactTelephone != null">
        "contact_telephone" = #{contactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="workUnitPostcode != null">
        "work_unit_postcode" = #{workUnitPostcode,jdbcType=VARCHAR},
      </if>
      <if test="registeredResidence != null">
        "registered_residence" = #{registeredResidence,jdbcType=VARCHAR},
      </if>
      <if test="registeredResidencePostcode != null">
        "registered_residence_postcode" = #{registeredResidencePostcode,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null">
        "contact_name" = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactRelationship != null">
        "contact_relationship" = #{contactRelationship,jdbcType=VARCHAR},
      </if>
      <if test="contactAdress != null">
        "contact_adress" = #{contactAdress,jdbcType=VARCHAR},
      </if>
      <if test="admissionWay != null">
        "admission_way" = #{admissionWay,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        "contact_phone" = #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="admissionDateTime != null">
        "admission_date_time" = #{admissionDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deptAdmissionTo != null">
        "dept_admission_to" = #{deptAdmissionTo,jdbcType=VARCHAR},
      </if>
      <if test="wardAdmissionTo != null">
        "ward_admission_to" = #{wardAdmissionTo,jdbcType=VARCHAR},
      </if>
      <if test="deptTransferFrom != null">
        "dept_transfer_from" = #{deptTransferFrom,jdbcType=VARCHAR},
      </if>
      <if test="dischargeDateTime != null">
        "discharge_date_time" = #{dischargeDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deptDischargeFrom != null">
        "dept_discharge_from" = #{deptDischargeFrom,jdbcType=VARCHAR},
      </if>
      <if test="wardDischargeFrom != null">
        "ward_discharge_from" = #{wardDischargeFrom,jdbcType=VARCHAR},
      </if>
      <if test="inDays != null">
        "in_days" = #{inDays,jdbcType=INTEGER},
      </if>
      <if test="infectedTimes != null">
        "infected_times" = #{infectedTimes,jdbcType=INTEGER},
      </if>
      <if test="injuryPoisoningCode != null">
        "injury_poisoning_code" = #{injuryPoisoningCode,jdbcType=VARCHAR},
      </if>
      <if test="injuryPoisoningCauses != null">
        "injury_poisoning_causes" = #{injuryPoisoningCauses,jdbcType=VARCHAR},
      </if>
      <if test="isAllergic != null">
        "is_allergic" = #{isAllergic,jdbcType=VARCHAR},
      </if>
      <if test="allergenDrug != null">
        "allergen_drug" = #{allergenDrug,jdbcType=VARCHAR},
      </if>
      <if test="hbsag != null">
        "hbsag" = #{hbsag,jdbcType=VARCHAR},
      </if>
      <if test="hcvAb != null">
        "hcv_ab" = #{hcvAb,jdbcType=VARCHAR},
      </if>
      <if test="hivAb != null">
        "hiv_ab" = #{hivAb,jdbcType=VARCHAR},
      </if>
      <if test="outpDisDiagConformity != null">
        "outp_dis_diag_conformity" = #{outpDisDiagConformity,jdbcType=VARCHAR},
      </if>
      <if test="admitDisDiagConformity != null">
        "admit_dis_diag_conformity" = #{admitDisDiagConformity,jdbcType=VARCHAR},
      </if>
      <if test="preopPostopDiagConformity != null">
        "preop_postop_diag_conformity" = #{preopPostopDiagConformity,jdbcType=VARCHAR},
      </if>
      <if test="clinicPathoDiagConformity != null">
        "clinic_patho_diag_conformity" = #{clinicPathoDiagConformity,jdbcType=VARCHAR},
      </if>
      <if test="radioPathoDiagConformity != null">
        "radio_patho_diag_conformity" = #{radioPathoDiagConformity,jdbcType=VARCHAR},
      </if>
      <if test="rescueTimes != null">
        "rescue_times" = #{rescueTimes,jdbcType=INTEGER},
      </if>
      <if test="rescueSuccessTimes != null">
        "rescue_success_times" = #{rescueSuccessTimes,jdbcType=INTEGER},
      </if>
      <if test="diagBasis != null">
        "diag_basis" = #{diagBasis,jdbcType=VARCHAR},
      </if>
      <if test="differentiationDegree != null">
        "differentiation_degree" = #{differentiationDegree,jdbcType=VARCHAR},
      </if>
      <if test="director != null">
        "director" = #{director,jdbcType=VARCHAR},
      </if>
      <if test="chiefDoctor != null">
        "chief_doctor" = #{chiefDoctor,jdbcType=VARCHAR},
      </if>
      <if test="attendingDoctor != null">
        "attending_doctor" = #{attendingDoctor,jdbcType=VARCHAR},
      </if>
      <if test="residentDoctor != null">
        "resident_doctor" = #{residentDoctor,jdbcType=VARCHAR},
      </if>
      <if test="responsibleNurse != null">
        "responsible_nurse" = #{responsibleNurse,jdbcType=VARCHAR},
      </if>
      <if test="traineeDoctor != null">
        "trainee_doctor" = #{traineeDoctor,jdbcType=VARCHAR},
      </if>
      <if test="graduateInternDoctor != null">
        "graduate_intern_doctor" = #{graduateInternDoctor,jdbcType=VARCHAR},
      </if>
      <if test="internDoctor != null">
        "intern_doctor" = #{internDoctor,jdbcType=VARCHAR},
      </if>
      <if test="coderName != null">
        "coder_name" = #{coderName,jdbcType=VARCHAR},
      </if>
      <if test="mrQuality != null">
        "mr_quality" = #{mrQuality,jdbcType=VARCHAR},
      </if>
      <if test="qualityControlDoctor != null">
        "quality_control_doctor" = #{qualityControlDoctor,jdbcType=VARCHAR},
      </if>
      <if test="qualityControlNurse != null">
        "quality_control_nurse" = #{qualityControlNurse,jdbcType=VARCHAR},
      </if>
      <if test="qualityConfirmDatetime != null">
        "quality_confirm_datetime" = #{qualityConfirmDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="specialNursingDays != null">
        "special_nursing_days" = #{specialNursingDays,jdbcType=INTEGER},
      </if>
      <if test="firstGradeNursingDays != null">
        "first_grade_nursing_days" = #{firstGradeNursingDays,jdbcType=INTEGER},
      </if>
      <if test="secondGradeNursingDays != null">
        "second_grade_nursing_days" = #{secondGradeNursingDays,jdbcType=INTEGER},
      </if>
      <if test="thirdGradeNursingDays != null">
        "third_grade_nursing_days" = #{thirdGradeNursingDays,jdbcType=INTEGER},
      </if>
      <if test="icuName != null">
        "icu_name" = #{icuName,jdbcType=VARCHAR},
      </if>
      <if test="icuInDatetime != null">
        "icu_in_datetime" = #{icuInDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="icuOutDatetime != null">
        "icu_out_datetime" = #{icuOutDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="deathPatientAutopsy != null">
        "death_patient_autopsy" = #{deathPatientAutopsy,jdbcType=VARCHAR},
      </if>
      <if test="firstExampleInHospital != null">
        "first_example_in_hospital" = #{firstExampleInHospital,jdbcType=VARCHAR},
      </if>
      <if test="operationPatientType != null">
        "operation_patient_type" = #{operationPatientType,jdbcType=VARCHAR},
      </if>
      <if test="followUp != null">
        "follow_up" = #{followUp,jdbcType=VARCHAR},
      </if>
      <if test="followUpWeeks != null">
        "follow_up_weeks" = #{followUpWeeks,jdbcType=DOUBLE},
      </if>
      <if test="followUpMonths != null">
        "follow_up_months" = #{followUpMonths,jdbcType=DOUBLE},
      </if>
      <if test="followUpYears != null">
        "follow_up_years" = #{followUpYears,jdbcType=DOUBLE},
      </if>
      <if test="demonstrationCase != null">
        "demonstration_case" = #{demonstrationCase,jdbcType=VARCHAR},
      </if>
      <if test="aboBloodType != null">
        "abo_blood_type" = #{aboBloodType,jdbcType=VARCHAR},
      </if>
      <if test="rhBloodType != null">
        "rh_blood_type" = #{rhBloodType,jdbcType=VARCHAR},
      </if>
      <if test="adverseReaction != null">
        "adverse_reaction" = #{adverseReaction,jdbcType=VARCHAR},
      </if>
      <if test="erythrocyte != null">
        "erythrocyte" = #{erythrocyte,jdbcType=DOUBLE},
      </if>
      <if test="platelet != null">
        "platelet" = #{platelet,jdbcType=DOUBLE},
      </if>
      <if test="plasma != null">
        "plasma" = #{plasma,jdbcType=DOUBLE},
      </if>
      <if test="wholeBlood != null">
        "whole_blood" = #{wholeBlood,jdbcType=DOUBLE},
      </if>
      <if test="autologousBloodCallback != null">
        "autologous_blood_callback" = #{autologousBloodCallback,jdbcType=DOUBLE},
      </if>
      <if test="othersBlood != null">
        "others_blood" = #{othersBlood,jdbcType=DOUBLE},
      </if>
      <if test="ageUnderOneYear != null">
        "age_under_one_year" = #{ageUnderOneYear,jdbcType=DOUBLE},
      </if>
      <if test="newbornWeight != null">
        "newborn_weight" = #{newbornWeight,jdbcType=DOUBLE},
      </if>
      <if test="newbornAdmitWeight != null">
        "newborn_admit_weight" = #{newbornAdmitWeight,jdbcType=DOUBLE},
      </if>
      <if test="comaHoursBeforeAdmit != null">
        "coma_hours_before_admit" = #{comaHoursBeforeAdmit,jdbcType=DOUBLE},
      </if>
      <if test="comaMinutesBeforeAdmit != null">
        "coma_minutes_before_admit" = #{comaMinutesBeforeAdmit,jdbcType=DOUBLE},
      </if>
      <if test="comaHoursAfterAdmit != null">
        "coma_hours_after_admit" = #{comaHoursAfterAdmit,jdbcType=DOUBLE},
      </if>
      <if test="comaMinutesAfterAdmit != null">
        "coma_minutes_after_admit" = #{comaMinutesAfterAdmit,jdbcType=DOUBLE},
      </if>
      <if test="respiratorUsingTime != null">
        "respirator_using_time" = #{respiratorUsingTime,jdbcType=DOUBLE},
      </if>
      <if test="readmitPlanInThirtyDays != null">
        "readmit_plan_in_thirty_days" = #{readmitPlanInThirtyDays,jdbcType=VARCHAR},
      </if>
      <if test="readmitReasonInThirtyDays != null">
        "readmit_reason_in_thirty_days" = #{readmitReasonInThirtyDays,jdbcType=VARCHAR},
      </if>
      <if test="dischargeWay != null">
        "discharge_way" = #{dischargeWay,jdbcType=VARCHAR},
      </if>
      <if test="thansferToHospital != null">
        "thansfer_to_hospital" = #{thansferToHospital,jdbcType=VARCHAR},
      </if>
      <if test="thansferToHealthCenter != null">
        "thansfer_to_health_center" = #{thansferToHealthCenter,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="icuDays != null">
        "icu_days" = #{icuDays,jdbcType=INTEGER},
      </if>
      <if test="ccuDays != null">
        "ccu_days" = #{ccuDays,jdbcType=INTEGER},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.MrHomepage">
    update "public"."mr_homepage"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "hospital_name" = #{hospitalName,jdbcType=VARCHAR},
      "insurance_card_no" = #{insuranceCardNo,jdbcType=VARCHAR},
      "health_card_no" = #{healthCardNo,jdbcType=VARCHAR},
      "pay_way" = #{payWay,jdbcType=VARCHAR},
      "admission_number" = #{admissionNumber,jdbcType=INTEGER},
      "tpatno" = #{tpatno,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "name" = #{name,jdbcType=VARCHAR},
      "gender" = #{gender,jdbcType=VARCHAR},
      "date_of_birth" = #{dateOfBirth,jdbcType=TIMESTAMP},
      "age" = #{age,jdbcType=INTEGER},
      "marriage" = #{marriage,jdbcType=VARCHAR},
      "occupation" = #{occupation,jdbcType=VARCHAR},
      "birth_place_province" = #{birthPlaceProvince,jdbcType=VARCHAR},
      "birth_place_city" = #{birthPlaceCity,jdbcType=VARCHAR},
      "birth_place_country" = #{birthPlaceCountry,jdbcType=VARCHAR},
      "nation" = #{nation,jdbcType=VARCHAR},
      "citizenship" = #{citizenship,jdbcType=VARCHAR},
      "id_no" = #{idNo,jdbcType=VARCHAR},
      "home_adress" = #{homeAdress,jdbcType=VARCHAR},
      "home_phone" = #{homePhone,jdbcType=VARCHAR},
      "home_adress_postcode" = #{homeAdressPostcode,jdbcType=VARCHAR},
      "work_unit_and_adress" = #{workUnitAndAdress,jdbcType=VARCHAR},
      "contact_telephone" = #{contactTelephone,jdbcType=VARCHAR},
      "work_unit_postcode" = #{workUnitPostcode,jdbcType=VARCHAR},
      "registered_residence" = #{registeredResidence,jdbcType=VARCHAR},
      "registered_residence_postcode" = #{registeredResidencePostcode,jdbcType=VARCHAR},
      "contact_name" = #{contactName,jdbcType=VARCHAR},
      "contact_relationship" = #{contactRelationship,jdbcType=VARCHAR},
      "contact_adress" = #{contactAdress,jdbcType=VARCHAR},
      "admission_way" = #{admissionWay,jdbcType=VARCHAR},
      "contact_phone" = #{contactPhone,jdbcType=VARCHAR},
      "admission_date_time" = #{admissionDateTime,jdbcType=TIMESTAMP},
      "dept_admission_to" = #{deptAdmissionTo,jdbcType=VARCHAR},
      "ward_admission_to" = #{wardAdmissionTo,jdbcType=VARCHAR},
      "dept_transfer_from" = #{deptTransferFrom,jdbcType=VARCHAR},
      "discharge_date_time" = #{dischargeDateTime,jdbcType=TIMESTAMP},
      "dept_discharge_from" = #{deptDischargeFrom,jdbcType=VARCHAR},
      "ward_discharge_from" = #{wardDischargeFrom,jdbcType=VARCHAR},
      "in_days" = #{inDays,jdbcType=INTEGER},
      "infected_times" = #{infectedTimes,jdbcType=INTEGER},
      "injury_poisoning_code" = #{injuryPoisoningCode,jdbcType=VARCHAR},
      "injury_poisoning_causes" = #{injuryPoisoningCauses,jdbcType=VARCHAR},
      "is_allergic" = #{isAllergic,jdbcType=VARCHAR},
      "allergen_drug" = #{allergenDrug,jdbcType=VARCHAR},
      "hbsag" = #{hbsag,jdbcType=VARCHAR},
      "hcv_ab" = #{hcvAb,jdbcType=VARCHAR},
      "hiv_ab" = #{hivAb,jdbcType=VARCHAR},
      "outp_dis_diag_conformity" = #{outpDisDiagConformity,jdbcType=VARCHAR},
      "admit_dis_diag_conformity" = #{admitDisDiagConformity,jdbcType=VARCHAR},
      "preop_postop_diag_conformity" = #{preopPostopDiagConformity,jdbcType=VARCHAR},
      "clinic_patho_diag_conformity" = #{clinicPathoDiagConformity,jdbcType=VARCHAR},
      "radio_patho_diag_conformity" = #{radioPathoDiagConformity,jdbcType=VARCHAR},
      "rescue_times" = #{rescueTimes,jdbcType=INTEGER},
      "rescue_success_times" = #{rescueSuccessTimes,jdbcType=INTEGER},
      "diag_basis" = #{diagBasis,jdbcType=VARCHAR},
      "differentiation_degree" = #{differentiationDegree,jdbcType=VARCHAR},
      "director" = #{director,jdbcType=VARCHAR},
      "chief_doctor" = #{chiefDoctor,jdbcType=VARCHAR},
      "attending_doctor" = #{attendingDoctor,jdbcType=VARCHAR},
      "resident_doctor" = #{residentDoctor,jdbcType=VARCHAR},
      "responsible_nurse" = #{responsibleNurse,jdbcType=VARCHAR},
      "trainee_doctor" = #{traineeDoctor,jdbcType=VARCHAR},
      "graduate_intern_doctor" = #{graduateInternDoctor,jdbcType=VARCHAR},
      "intern_doctor" = #{internDoctor,jdbcType=VARCHAR},
      "coder_name" = #{coderName,jdbcType=VARCHAR},
      "mr_quality" = #{mrQuality,jdbcType=VARCHAR},
      "quality_control_doctor" = #{qualityControlDoctor,jdbcType=VARCHAR},
      "quality_control_nurse" = #{qualityControlNurse,jdbcType=VARCHAR},
      "quality_confirm_datetime" = #{qualityConfirmDatetime,jdbcType=TIMESTAMP},
      "special_nursing_days" = #{specialNursingDays,jdbcType=INTEGER},
      "first_grade_nursing_days" = #{firstGradeNursingDays,jdbcType=INTEGER},
      "second_grade_nursing_days" = #{secondGradeNursingDays,jdbcType=INTEGER},
      "third_grade_nursing_days" = #{thirdGradeNursingDays,jdbcType=INTEGER},
      "icu_name" = #{icuName,jdbcType=VARCHAR},
      "icu_in_datetime" = #{icuInDatetime,jdbcType=TIMESTAMP},
      "icu_out_datetime" = #{icuOutDatetime,jdbcType=TIMESTAMP},
      "death_patient_autopsy" = #{deathPatientAutopsy,jdbcType=VARCHAR},
      "first_example_in_hospital" = #{firstExampleInHospital,jdbcType=VARCHAR},
      "operation_patient_type" = #{operationPatientType,jdbcType=VARCHAR},
      "follow_up" = #{followUp,jdbcType=VARCHAR},
      "follow_up_weeks" = #{followUpWeeks,jdbcType=DOUBLE},
      "follow_up_months" = #{followUpMonths,jdbcType=DOUBLE},
      "follow_up_years" = #{followUpYears,jdbcType=DOUBLE},
      "demonstration_case" = #{demonstrationCase,jdbcType=VARCHAR},
      "abo_blood_type" = #{aboBloodType,jdbcType=VARCHAR},
      "rh_blood_type" = #{rhBloodType,jdbcType=VARCHAR},
      "adverse_reaction" = #{adverseReaction,jdbcType=VARCHAR},
      "erythrocyte" = #{erythrocyte,jdbcType=DOUBLE},
      "platelet" = #{platelet,jdbcType=DOUBLE},
      "plasma" = #{plasma,jdbcType=DOUBLE},
      "whole_blood" = #{wholeBlood,jdbcType=DOUBLE},
      "autologous_blood_callback" = #{autologousBloodCallback,jdbcType=DOUBLE},
      "others_blood" = #{othersBlood,jdbcType=DOUBLE},
      "age_under_one_year" = #{ageUnderOneYear,jdbcType=DOUBLE},
      "newborn_weight" = #{newbornWeight,jdbcType=DOUBLE},
      "newborn_admit_weight" = #{newbornAdmitWeight,jdbcType=DOUBLE},
      "coma_hours_before_admit" = #{comaHoursBeforeAdmit,jdbcType=DOUBLE},
      "coma_minutes_before_admit" = #{comaMinutesBeforeAdmit,jdbcType=DOUBLE},
      "coma_hours_after_admit" = #{comaHoursAfterAdmit,jdbcType=DOUBLE},
      "coma_minutes_after_admit" = #{comaMinutesAfterAdmit,jdbcType=DOUBLE},
      "respirator_using_time" = #{respiratorUsingTime,jdbcType=DOUBLE},
      "readmit_plan_in_thirty_days" = #{readmitPlanInThirtyDays,jdbcType=VARCHAR},
      "readmit_reason_in_thirty_days" = #{readmitReasonInThirtyDays,jdbcType=VARCHAR},
      "discharge_way" = #{dischargeWay,jdbcType=VARCHAR},
      "thansfer_to_hospital" = #{thansferToHospital,jdbcType=VARCHAR},
      "thansfer_to_health_center" = #{thansferToHealthCenter,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR},
      "icu_days" = #{icuDays,jdbcType=INTEGER},
      "ccu_days" = #{ccuDays,jdbcType=INTEGER}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>

  <select id="getPatientMrHomepage" resultMap="BaseResultMap">
    select * from mr_homepage where patient_sn = #{patientId}
  </select>

  <select id="getMrHomepageByPatientId" resultMap="BaseResultMap">
    select * from mr_homepage where patient_sn = #{patientId} limit 1
  </select>
</mapper>
