<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.InpVisitMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.InpVisit">
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="admission_number" jdbcType="INTEGER" property="admissionNumber" />
    <result column="tpatno" jdbcType="VARCHAR" property="tpatno" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="gender" jdbcType="VARCHAR" property="gender" />
    <result column="age" jdbcType="INTEGER" property="age" />
    <result column="charge_type" jdbcType="VARCHAR" property="chargeType" />
    <result column="insurance_type" jdbcType="VARCHAR" property="insuranceType" />
    <result column="admission_way" jdbcType="VARCHAR" property="admissionWay" />
    <result column="admission_date_time" jdbcType="TIMESTAMP" property="admissionDateTime" />
    <result column="dept_admission_to" jdbcType="VARCHAR" property="deptAdmissionTo" />
    <result column="dept_transfer_from" jdbcType="VARCHAR" property="deptTransferFrom" />
    <result column="discharge_date_time" jdbcType="TIMESTAMP" property="dischargeDateTime" />
    <result column="dept_discharge_from" jdbcType="VARCHAR" property="deptDischargeFrom" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
    <result column="patient_sn_org" jdbcType="VARCHAR" property="patientSnOrg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "hospital_code", "patient_sn", "visit_sn", "admission_number", "tpatno", "name",
    "gender", "age", "charge_type", "insurance_type", "admission_way", "admission_date_time",
    "dept_admission_to", "dept_transfer_from", "discharge_date_time", "dept_discharge_from",
    "source_path", "pk_id", "data_state", "patient_sn_org"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.InpVisitExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."inp_visit"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "public"."inp_visit"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."inp_visit"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.InpVisitExample">
    delete from "public"."inp_visit"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.InpVisit">
    insert into "public"."inp_visit" ("hospital_code", "patient_sn", "visit_sn",
      "admission_number", "tpatno", "name",
      "gender", "age", "charge_type",
      "insurance_type", "admission_way", "admission_date_time",
      "dept_admission_to", "dept_transfer_from", "discharge_date_time",
      "dept_discharge_from", "source_path", "pk_id",
      "data_state", "patient_sn_org")
    values (#{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR},
      #{admissionNumber,jdbcType=INTEGER}, #{tpatno,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
      #{gender,jdbcType=VARCHAR}, #{age,jdbcType=INTEGER}, #{chargeType,jdbcType=VARCHAR},
      #{insuranceType,jdbcType=VARCHAR}, #{admissionWay,jdbcType=VARCHAR}, #{admissionDateTime,jdbcType=TIMESTAMP},
      #{deptAdmissionTo,jdbcType=VARCHAR}, #{deptTransferFrom,jdbcType=VARCHAR}, #{dischargeDateTime,jdbcType=TIMESTAMP},
      #{deptDischargeFrom,jdbcType=VARCHAR}, #{sourcePath,jdbcType=VARCHAR}, #{pkId,jdbcType=VARCHAR},
      #{dataState,jdbcType=VARCHAR}, #{patientSnOrg,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.InpVisit">
    insert into "public"."inp_visit"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="admissionNumber != null">
        "admission_number",
      </if>
      <if test="tpatno != null">
        "tpatno",
      </if>
      <if test="name != null">
        "name",
      </if>
      <if test="gender != null">
        "gender",
      </if>
      <if test="age != null">
        "age",
      </if>
      <if test="chargeType != null">
        "charge_type",
      </if>
      <if test="insuranceType != null">
        "insurance_type",
      </if>
      <if test="admissionWay != null">
        "admission_way",
      </if>
      <if test="admissionDateTime != null">
        "admission_date_time",
      </if>
      <if test="deptAdmissionTo != null">
        "dept_admission_to",
      </if>
      <if test="deptTransferFrom != null">
        "dept_transfer_from",
      </if>
      <if test="dischargeDateTime != null">
        "discharge_date_time",
      </if>
      <if test="deptDischargeFrom != null">
        "dept_discharge_from",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="admissionNumber != null">
        #{admissionNumber,jdbcType=INTEGER},
      </if>
      <if test="tpatno != null">
        #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=VARCHAR},
      </if>
      <if test="age != null">
        #{age,jdbcType=INTEGER},
      </if>
      <if test="chargeType != null">
        #{chargeType,jdbcType=VARCHAR},
      </if>
      <if test="insuranceType != null">
        #{insuranceType,jdbcType=VARCHAR},
      </if>
      <if test="admissionWay != null">
        #{admissionWay,jdbcType=VARCHAR},
      </if>
      <if test="admissionDateTime != null">
        #{admissionDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deptAdmissionTo != null">
        #{deptAdmissionTo,jdbcType=VARCHAR},
      </if>
      <if test="deptTransferFrom != null">
        #{deptTransferFrom,jdbcType=VARCHAR},
      </if>
      <if test="dischargeDateTime != null">
        #{dischargeDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deptDischargeFrom != null">
        #{deptDischargeFrom,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="patientSnOrg != null">
        #{patientSnOrg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.InpVisitExample" resultType="java.lang.Long">
    select count(*) from "public"."inp_visit"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."inp_visit"
    <set>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.admissionNumber != null">
        "admission_number" = #{record.admissionNumber,jdbcType=INTEGER},
      </if>
      <if test="record.tpatno != null">
        "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        "name" = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.gender != null">
        "gender" = #{record.gender,jdbcType=VARCHAR},
      </if>
      <if test="record.age != null">
        "age" = #{record.age,jdbcType=INTEGER},
      </if>
      <if test="record.chargeType != null">
        "charge_type" = #{record.chargeType,jdbcType=VARCHAR},
      </if>
      <if test="record.insuranceType != null">
        "insurance_type" = #{record.insuranceType,jdbcType=VARCHAR},
      </if>
      <if test="record.admissionWay != null">
        "admission_way" = #{record.admissionWay,jdbcType=VARCHAR},
      </if>
      <if test="record.admissionDateTime != null">
        "admission_date_time" = #{record.admissionDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deptAdmissionTo != null">
        "dept_admission_to" = #{record.deptAdmissionTo,jdbcType=VARCHAR},
      </if>
      <if test="record.deptTransferFrom != null">
        "dept_transfer_from" = #{record.deptTransferFrom,jdbcType=VARCHAR},
      </if>
      <if test="record.dischargeDateTime != null">
        "discharge_date_time" = #{record.dischargeDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deptDischargeFrom != null">
        "dept_discharge_from" = #{record.deptDischargeFrom,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSnOrg != null">
        "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."inp_visit"
    set "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "admission_number" = #{record.admissionNumber,jdbcType=INTEGER},
      "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      "name" = #{record.name,jdbcType=VARCHAR},
      "gender" = #{record.gender,jdbcType=VARCHAR},
      "age" = #{record.age,jdbcType=INTEGER},
      "charge_type" = #{record.chargeType,jdbcType=VARCHAR},
      "insurance_type" = #{record.insuranceType,jdbcType=VARCHAR},
      "admission_way" = #{record.admissionWay,jdbcType=VARCHAR},
      "admission_date_time" = #{record.admissionDateTime,jdbcType=TIMESTAMP},
      "dept_admission_to" = #{record.deptAdmissionTo,jdbcType=VARCHAR},
      "dept_transfer_from" = #{record.deptTransferFrom,jdbcType=VARCHAR},
      "discharge_date_time" = #{record.dischargeDateTime,jdbcType=TIMESTAMP},
      "dept_discharge_from" = #{record.deptDischargeFrom,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR},
      "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.InpVisit">
    update "public"."inp_visit"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="admissionNumber != null">
        "admission_number" = #{admissionNumber,jdbcType=INTEGER},
      </if>
      <if test="tpatno != null">
        "tpatno" = #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        "name" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="chargeType != null">
        "charge_type" = #{chargeType,jdbcType=VARCHAR},
      </if>
      <if test="insuranceType != null">
        "insurance_type" = #{insuranceType,jdbcType=VARCHAR},
      </if>
      <if test="admissionWay != null">
        "admission_way" = #{admissionWay,jdbcType=VARCHAR},
      </if>
      <if test="admissionDateTime != null">
        "admission_date_time" = #{admissionDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deptAdmissionTo != null">
        "dept_admission_to" = #{deptAdmissionTo,jdbcType=VARCHAR},
      </if>
      <if test="deptTransferFrom != null">
        "dept_transfer_from" = #{deptTransferFrom,jdbcType=VARCHAR},
      </if>
      <if test="dischargeDateTime != null">
        "discharge_date_time" = #{dischargeDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deptDischargeFrom != null">
        "dept_discharge_from" = #{deptDischargeFrom,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.InpVisit">
    update "public"."inp_visit"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "admission_number" = #{admissionNumber,jdbcType=INTEGER},
      "tpatno" = #{tpatno,jdbcType=VARCHAR},
      "name" = #{name,jdbcType=VARCHAR},
      "charge_type" = #{chargeType,jdbcType=VARCHAR},
      "insurance_type" = #{insuranceType,jdbcType=VARCHAR},
      "admission_way" = #{admissionWay,jdbcType=VARCHAR},
      "admission_date_time" = #{admissionDateTime,jdbcType=TIMESTAMP},
      "dept_admission_to" = #{deptAdmissionTo,jdbcType=VARCHAR},
      "dept_transfer_from" = #{deptTransferFrom,jdbcType=VARCHAR},
      "discharge_date_time" = #{dischargeDateTime,jdbcType=TIMESTAMP},
      "dept_discharge_from" = #{deptDischargeFrom,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>

  <select id="getInpVisit" resultMap="BaseResultMap">
    select * from inp_visit where patient_sn = #{patientId} order by admission_date_time
  </select>
  <select id="countPgSqlQuarter" resultType="java.lang.Long">
    select count(0) from inp_visit
    where 1=1
    and  date_part( 'months', admission_date_time )  in
    <foreach collection="months" item="month" separator="," open="(" close=")">
      #{month}
    </foreach>
  </select>
  <select id="count" resultType="java.lang.Long">
    select count(inp_visit.patient_sn) from inp_visit
    <if test="dataBaseId != null and dataBaseId != ''">
      join rdr_patient_data_base_record  on rdr_patient_data_base_record.patient_sn=inp_visit.patient_sn and rdr_patient_data_base_record.data_base_id=cast(#{dataBaseId} as text )
    </if>
  </select>
</mapper>
