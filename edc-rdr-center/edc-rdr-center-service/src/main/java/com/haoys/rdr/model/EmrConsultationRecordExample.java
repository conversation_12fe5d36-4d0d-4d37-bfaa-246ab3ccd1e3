package com.haoys.rdr.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class EmrConsultationRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public EmrConsultationRecordExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPkIdIsNull() {
            addCriterion("\"pk_id\" is null");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNotNull() {
            addCriterion("\"pk_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkIdEqualTo(String value) {
            addCriterion("\"pk_id\" =", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotEqualTo(String value) {
            addCriterion("\"pk_id\" <>", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThan(String value) {
            addCriterion("\"pk_id\" >", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" >=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThan(String value) {
            addCriterion("\"pk_id\" <", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" <=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLike(String value) {
            addCriterion("\"pk_id\" like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotLike(String value) {
            addCriterion("\"pk_id\" not like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdIn(List<String> values) {
            addCriterion("\"pk_id\" in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotIn(List<String> values) {
            addCriterion("\"pk_id\" not in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdBetween(String value1, String value2) {
            addCriterion("\"pk_id\" between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotBetween(String value1, String value2) {
            addCriterion("\"pk_id\" not between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIsNull() {
            addCriterion("\"hospital_code\" is null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIsNotNull() {
            addCriterion("\"hospital_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeEqualTo(String value) {
            addCriterion("\"hospital_code\" =", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotEqualTo(String value) {
            addCriterion("\"hospital_code\" <>", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThan(String value) {
            addCriterion("\"hospital_code\" >", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" >=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThan(String value) {
            addCriterion("\"hospital_code\" <", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" <=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLike(String value) {
            addCriterion("\"hospital_code\" like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotLike(String value) {
            addCriterion("\"hospital_code\" not like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIn(List<String> values) {
            addCriterion("\"hospital_code\" in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotIn(List<String> values) {
            addCriterion("\"hospital_code\" not in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" not between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andTpatnoIsNull() {
            addCriterion("\"tpatno\" is null");
            return (Criteria) this;
        }

        public Criteria andTpatnoIsNotNull() {
            addCriterion("\"tpatno\" is not null");
            return (Criteria) this;
        }

        public Criteria andTpatnoEqualTo(String value) {
            addCriterion("\"tpatno\" =", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotEqualTo(String value) {
            addCriterion("\"tpatno\" <>", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoGreaterThan(String value) {
            addCriterion("\"tpatno\" >", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoGreaterThanOrEqualTo(String value) {
            addCriterion("\"tpatno\" >=", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLessThan(String value) {
            addCriterion("\"tpatno\" <", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLessThanOrEqualTo(String value) {
            addCriterion("\"tpatno\" <=", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLike(String value) {
            addCriterion("\"tpatno\" like", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotLike(String value) {
            addCriterion("\"tpatno\" not like", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoIn(List<String> values) {
            addCriterion("\"tpatno\" in", values, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotIn(List<String> values) {
            addCriterion("\"tpatno\" not in", values, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoBetween(String value1, String value2) {
            addCriterion("\"tpatno\" between", value1, value2, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotBetween(String value1, String value2) {
            addCriterion("\"tpatno\" not between", value1, value2, "tpatno");
            return (Criteria) this;
        }

        public Criteria andConsultationNoIsNull() {
            addCriterion("\"consultation_no\" is null");
            return (Criteria) this;
        }

        public Criteria andConsultationNoIsNotNull() {
            addCriterion("\"consultation_no\" is not null");
            return (Criteria) this;
        }

        public Criteria andConsultationNoEqualTo(String value) {
            addCriterion("\"consultation_no\" =", value, "consultationNo");
            return (Criteria) this;
        }

        public Criteria andConsultationNoNotEqualTo(String value) {
            addCriterion("\"consultation_no\" <>", value, "consultationNo");
            return (Criteria) this;
        }

        public Criteria andConsultationNoGreaterThan(String value) {
            addCriterion("\"consultation_no\" >", value, "consultationNo");
            return (Criteria) this;
        }

        public Criteria andConsultationNoGreaterThanOrEqualTo(String value) {
            addCriterion("\"consultation_no\" >=", value, "consultationNo");
            return (Criteria) this;
        }

        public Criteria andConsultationNoLessThan(String value) {
            addCriterion("\"consultation_no\" <", value, "consultationNo");
            return (Criteria) this;
        }

        public Criteria andConsultationNoLessThanOrEqualTo(String value) {
            addCriterion("\"consultation_no\" <=", value, "consultationNo");
            return (Criteria) this;
        }

        public Criteria andConsultationNoLike(String value) {
            addCriterion("\"consultation_no\" like", value, "consultationNo");
            return (Criteria) this;
        }

        public Criteria andConsultationNoNotLike(String value) {
            addCriterion("\"consultation_no\" not like", value, "consultationNo");
            return (Criteria) this;
        }

        public Criteria andConsultationNoIn(List<String> values) {
            addCriterion("\"consultation_no\" in", values, "consultationNo");
            return (Criteria) this;
        }

        public Criteria andConsultationNoNotIn(List<String> values) {
            addCriterion("\"consultation_no\" not in", values, "consultationNo");
            return (Criteria) this;
        }

        public Criteria andConsultationNoBetween(String value1, String value2) {
            addCriterion("\"consultation_no\" between", value1, value2, "consultationNo");
            return (Criteria) this;
        }

        public Criteria andConsultationNoNotBetween(String value1, String value2) {
            addCriterion("\"consultation_no\" not between", value1, value2, "consultationNo");
            return (Criteria) this;
        }

        public Criteria andConsultationTypeIsNull() {
            addCriterion("\"consultation_type\" is null");
            return (Criteria) this;
        }

        public Criteria andConsultationTypeIsNotNull() {
            addCriterion("\"consultation_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andConsultationTypeEqualTo(String value) {
            addCriterion("\"consultation_type\" =", value, "consultationType");
            return (Criteria) this;
        }

        public Criteria andConsultationTypeNotEqualTo(String value) {
            addCriterion("\"consultation_type\" <>", value, "consultationType");
            return (Criteria) this;
        }

        public Criteria andConsultationTypeGreaterThan(String value) {
            addCriterion("\"consultation_type\" >", value, "consultationType");
            return (Criteria) this;
        }

        public Criteria andConsultationTypeGreaterThanOrEqualTo(String value) {
            addCriterion("\"consultation_type\" >=", value, "consultationType");
            return (Criteria) this;
        }

        public Criteria andConsultationTypeLessThan(String value) {
            addCriterion("\"consultation_type\" <", value, "consultationType");
            return (Criteria) this;
        }

        public Criteria andConsultationTypeLessThanOrEqualTo(String value) {
            addCriterion("\"consultation_type\" <=", value, "consultationType");
            return (Criteria) this;
        }

        public Criteria andConsultationTypeLike(String value) {
            addCriterion("\"consultation_type\" like", value, "consultationType");
            return (Criteria) this;
        }

        public Criteria andConsultationTypeNotLike(String value) {
            addCriterion("\"consultation_type\" not like", value, "consultationType");
            return (Criteria) this;
        }

        public Criteria andConsultationTypeIn(List<String> values) {
            addCriterion("\"consultation_type\" in", values, "consultationType");
            return (Criteria) this;
        }

        public Criteria andConsultationTypeNotIn(List<String> values) {
            addCriterion("\"consultation_type\" not in", values, "consultationType");
            return (Criteria) this;
        }

        public Criteria andConsultationTypeBetween(String value1, String value2) {
            addCriterion("\"consultation_type\" between", value1, value2, "consultationType");
            return (Criteria) this;
        }

        public Criteria andConsultationTypeNotBetween(String value1, String value2) {
            addCriterion("\"consultation_type\" not between", value1, value2, "consultationType");
            return (Criteria) this;
        }

        public Criteria andConsultationExplainIsNull() {
            addCriterion("\"consultation_explain\" is null");
            return (Criteria) this;
        }

        public Criteria andConsultationExplainIsNotNull() {
            addCriterion("\"consultation_explain\" is not null");
            return (Criteria) this;
        }

        public Criteria andConsultationExplainEqualTo(String value) {
            addCriterion("\"consultation_explain\" =", value, "consultationExplain");
            return (Criteria) this;
        }

        public Criteria andConsultationExplainNotEqualTo(String value) {
            addCriterion("\"consultation_explain\" <>", value, "consultationExplain");
            return (Criteria) this;
        }

        public Criteria andConsultationExplainGreaterThan(String value) {
            addCriterion("\"consultation_explain\" >", value, "consultationExplain");
            return (Criteria) this;
        }

        public Criteria andConsultationExplainGreaterThanOrEqualTo(String value) {
            addCriterion("\"consultation_explain\" >=", value, "consultationExplain");
            return (Criteria) this;
        }

        public Criteria andConsultationExplainLessThan(String value) {
            addCriterion("\"consultation_explain\" <", value, "consultationExplain");
            return (Criteria) this;
        }

        public Criteria andConsultationExplainLessThanOrEqualTo(String value) {
            addCriterion("\"consultation_explain\" <=", value, "consultationExplain");
            return (Criteria) this;
        }

        public Criteria andConsultationExplainLike(String value) {
            addCriterion("\"consultation_explain\" like", value, "consultationExplain");
            return (Criteria) this;
        }

        public Criteria andConsultationExplainNotLike(String value) {
            addCriterion("\"consultation_explain\" not like", value, "consultationExplain");
            return (Criteria) this;
        }

        public Criteria andConsultationExplainIn(List<String> values) {
            addCriterion("\"consultation_explain\" in", values, "consultationExplain");
            return (Criteria) this;
        }

        public Criteria andConsultationExplainNotIn(List<String> values) {
            addCriterion("\"consultation_explain\" not in", values, "consultationExplain");
            return (Criteria) this;
        }

        public Criteria andConsultationExplainBetween(String value1, String value2) {
            addCriterion("\"consultation_explain\" between", value1, value2, "consultationExplain");
            return (Criteria) this;
        }

        public Criteria andConsultationExplainNotBetween(String value1, String value2) {
            addCriterion("\"consultation_explain\" not between", value1, value2, "consultationExplain");
            return (Criteria) this;
        }

        public Criteria andApplyDateTimeIsNull() {
            addCriterion("\"apply_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andApplyDateTimeIsNotNull() {
            addCriterion("\"apply_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andApplyDateTimeEqualTo(Date value) {
            addCriterion("\"apply_date_time\" =", value, "applyDateTime");
            return (Criteria) this;
        }

        public Criteria andApplyDateTimeNotEqualTo(Date value) {
            addCriterion("\"apply_date_time\" <>", value, "applyDateTime");
            return (Criteria) this;
        }

        public Criteria andApplyDateTimeGreaterThan(Date value) {
            addCriterion("\"apply_date_time\" >", value, "applyDateTime");
            return (Criteria) this;
        }

        public Criteria andApplyDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"apply_date_time\" >=", value, "applyDateTime");
            return (Criteria) this;
        }

        public Criteria andApplyDateTimeLessThan(Date value) {
            addCriterion("\"apply_date_time\" <", value, "applyDateTime");
            return (Criteria) this;
        }

        public Criteria andApplyDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"apply_date_time\" <=", value, "applyDateTime");
            return (Criteria) this;
        }

        public Criteria andApplyDateTimeIn(List<Date> values) {
            addCriterion("\"apply_date_time\" in", values, "applyDateTime");
            return (Criteria) this;
        }

        public Criteria andApplyDateTimeNotIn(List<Date> values) {
            addCriterion("\"apply_date_time\" not in", values, "applyDateTime");
            return (Criteria) this;
        }

        public Criteria andApplyDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"apply_date_time\" between", value1, value2, "applyDateTime");
            return (Criteria) this;
        }

        public Criteria andApplyDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"apply_date_time\" not between", value1, value2, "applyDateTime");
            return (Criteria) this;
        }

        public Criteria andConsultationDeptIsNull() {
            addCriterion("\"consultation_dept\" is null");
            return (Criteria) this;
        }

        public Criteria andConsultationDeptIsNotNull() {
            addCriterion("\"consultation_dept\" is not null");
            return (Criteria) this;
        }

        public Criteria andConsultationDeptEqualTo(String value) {
            addCriterion("\"consultation_dept\" =", value, "consultationDept");
            return (Criteria) this;
        }

        public Criteria andConsultationDeptNotEqualTo(String value) {
            addCriterion("\"consultation_dept\" <>", value, "consultationDept");
            return (Criteria) this;
        }

        public Criteria andConsultationDeptGreaterThan(String value) {
            addCriterion("\"consultation_dept\" >", value, "consultationDept");
            return (Criteria) this;
        }

        public Criteria andConsultationDeptGreaterThanOrEqualTo(String value) {
            addCriterion("\"consultation_dept\" >=", value, "consultationDept");
            return (Criteria) this;
        }

        public Criteria andConsultationDeptLessThan(String value) {
            addCriterion("\"consultation_dept\" <", value, "consultationDept");
            return (Criteria) this;
        }

        public Criteria andConsultationDeptLessThanOrEqualTo(String value) {
            addCriterion("\"consultation_dept\" <=", value, "consultationDept");
            return (Criteria) this;
        }

        public Criteria andConsultationDeptLike(String value) {
            addCriterion("\"consultation_dept\" like", value, "consultationDept");
            return (Criteria) this;
        }

        public Criteria andConsultationDeptNotLike(String value) {
            addCriterion("\"consultation_dept\" not like", value, "consultationDept");
            return (Criteria) this;
        }

        public Criteria andConsultationDeptIn(List<String> values) {
            addCriterion("\"consultation_dept\" in", values, "consultationDept");
            return (Criteria) this;
        }

        public Criteria andConsultationDeptNotIn(List<String> values) {
            addCriterion("\"consultation_dept\" not in", values, "consultationDept");
            return (Criteria) this;
        }

        public Criteria andConsultationDeptBetween(String value1, String value2) {
            addCriterion("\"consultation_dept\" between", value1, value2, "consultationDept");
            return (Criteria) this;
        }

        public Criteria andConsultationDeptNotBetween(String value1, String value2) {
            addCriterion("\"consultation_dept\" not between", value1, value2, "consultationDept");
            return (Criteria) this;
        }

        public Criteria andConsultationDoctorIsNull() {
            addCriterion("\"consultation_doctor\" is null");
            return (Criteria) this;
        }

        public Criteria andConsultationDoctorIsNotNull() {
            addCriterion("\"consultation_doctor\" is not null");
            return (Criteria) this;
        }

        public Criteria andConsultationDoctorEqualTo(String value) {
            addCriterion("\"consultation_doctor\" =", value, "consultationDoctor");
            return (Criteria) this;
        }

        public Criteria andConsultationDoctorNotEqualTo(String value) {
            addCriterion("\"consultation_doctor\" <>", value, "consultationDoctor");
            return (Criteria) this;
        }

        public Criteria andConsultationDoctorGreaterThan(String value) {
            addCriterion("\"consultation_doctor\" >", value, "consultationDoctor");
            return (Criteria) this;
        }

        public Criteria andConsultationDoctorGreaterThanOrEqualTo(String value) {
            addCriterion("\"consultation_doctor\" >=", value, "consultationDoctor");
            return (Criteria) this;
        }

        public Criteria andConsultationDoctorLessThan(String value) {
            addCriterion("\"consultation_doctor\" <", value, "consultationDoctor");
            return (Criteria) this;
        }

        public Criteria andConsultationDoctorLessThanOrEqualTo(String value) {
            addCriterion("\"consultation_doctor\" <=", value, "consultationDoctor");
            return (Criteria) this;
        }

        public Criteria andConsultationDoctorLike(String value) {
            addCriterion("\"consultation_doctor\" like", value, "consultationDoctor");
            return (Criteria) this;
        }

        public Criteria andConsultationDoctorNotLike(String value) {
            addCriterion("\"consultation_doctor\" not like", value, "consultationDoctor");
            return (Criteria) this;
        }

        public Criteria andConsultationDoctorIn(List<String> values) {
            addCriterion("\"consultation_doctor\" in", values, "consultationDoctor");
            return (Criteria) this;
        }

        public Criteria andConsultationDoctorNotIn(List<String> values) {
            addCriterion("\"consultation_doctor\" not in", values, "consultationDoctor");
            return (Criteria) this;
        }

        public Criteria andConsultationDoctorBetween(String value1, String value2) {
            addCriterion("\"consultation_doctor\" between", value1, value2, "consultationDoctor");
            return (Criteria) this;
        }

        public Criteria andConsultationDoctorNotBetween(String value1, String value2) {
            addCriterion("\"consultation_doctor\" not between", value1, value2, "consultationDoctor");
            return (Criteria) this;
        }

        public Criteria andAffirmDateTimeIsNull() {
            addCriterion("\"affirm_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andAffirmDateTimeIsNotNull() {
            addCriterion("\"affirm_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andAffirmDateTimeEqualTo(Date value) {
            addCriterion("\"affirm_date_time\" =", value, "affirmDateTime");
            return (Criteria) this;
        }

        public Criteria andAffirmDateTimeNotEqualTo(Date value) {
            addCriterion("\"affirm_date_time\" <>", value, "affirmDateTime");
            return (Criteria) this;
        }

        public Criteria andAffirmDateTimeGreaterThan(Date value) {
            addCriterion("\"affirm_date_time\" >", value, "affirmDateTime");
            return (Criteria) this;
        }

        public Criteria andAffirmDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"affirm_date_time\" >=", value, "affirmDateTime");
            return (Criteria) this;
        }

        public Criteria andAffirmDateTimeLessThan(Date value) {
            addCriterion("\"affirm_date_time\" <", value, "affirmDateTime");
            return (Criteria) this;
        }

        public Criteria andAffirmDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"affirm_date_time\" <=", value, "affirmDateTime");
            return (Criteria) this;
        }

        public Criteria andAffirmDateTimeIn(List<Date> values) {
            addCriterion("\"affirm_date_time\" in", values, "affirmDateTime");
            return (Criteria) this;
        }

        public Criteria andAffirmDateTimeNotIn(List<Date> values) {
            addCriterion("\"affirm_date_time\" not in", values, "affirmDateTime");
            return (Criteria) this;
        }

        public Criteria andAffirmDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"affirm_date_time\" between", value1, value2, "affirmDateTime");
            return (Criteria) this;
        }

        public Criteria andAffirmDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"affirm_date_time\" not between", value1, value2, "affirmDateTime");
            return (Criteria) this;
        }

        public Criteria andCommitDateTimeIsNull() {
            addCriterion("\"commit_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andCommitDateTimeIsNotNull() {
            addCriterion("\"commit_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andCommitDateTimeEqualTo(Date value) {
            addCriterion("\"commit_date_time\" =", value, "commitDateTime");
            return (Criteria) this;
        }

        public Criteria andCommitDateTimeNotEqualTo(Date value) {
            addCriterion("\"commit_date_time\" <>", value, "commitDateTime");
            return (Criteria) this;
        }

        public Criteria andCommitDateTimeGreaterThan(Date value) {
            addCriterion("\"commit_date_time\" >", value, "commitDateTime");
            return (Criteria) this;
        }

        public Criteria andCommitDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"commit_date_time\" >=", value, "commitDateTime");
            return (Criteria) this;
        }

        public Criteria andCommitDateTimeLessThan(Date value) {
            addCriterion("\"commit_date_time\" <", value, "commitDateTime");
            return (Criteria) this;
        }

        public Criteria andCommitDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"commit_date_time\" <=", value, "commitDateTime");
            return (Criteria) this;
        }

        public Criteria andCommitDateTimeIn(List<Date> values) {
            addCriterion("\"commit_date_time\" in", values, "commitDateTime");
            return (Criteria) this;
        }

        public Criteria andCommitDateTimeNotIn(List<Date> values) {
            addCriterion("\"commit_date_time\" not in", values, "commitDateTime");
            return (Criteria) this;
        }

        public Criteria andCommitDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"commit_date_time\" between", value1, value2, "commitDateTime");
            return (Criteria) this;
        }

        public Criteria andCommitDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"commit_date_time\" not between", value1, value2, "commitDateTime");
            return (Criteria) this;
        }

        public Criteria andConsultationIdeaIsNull() {
            addCriterion("\"consultation_idea\" is null");
            return (Criteria) this;
        }

        public Criteria andConsultationIdeaIsNotNull() {
            addCriterion("\"consultation_idea\" is not null");
            return (Criteria) this;
        }

        public Criteria andConsultationIdeaEqualTo(String value) {
            addCriterion("\"consultation_idea\" =", value, "consultationIdea");
            return (Criteria) this;
        }

        public Criteria andConsultationIdeaNotEqualTo(String value) {
            addCriterion("\"consultation_idea\" <>", value, "consultationIdea");
            return (Criteria) this;
        }

        public Criteria andConsultationIdeaGreaterThan(String value) {
            addCriterion("\"consultation_idea\" >", value, "consultationIdea");
            return (Criteria) this;
        }

        public Criteria andConsultationIdeaGreaterThanOrEqualTo(String value) {
            addCriterion("\"consultation_idea\" >=", value, "consultationIdea");
            return (Criteria) this;
        }

        public Criteria andConsultationIdeaLessThan(String value) {
            addCriterion("\"consultation_idea\" <", value, "consultationIdea");
            return (Criteria) this;
        }

        public Criteria andConsultationIdeaLessThanOrEqualTo(String value) {
            addCriterion("\"consultation_idea\" <=", value, "consultationIdea");
            return (Criteria) this;
        }

        public Criteria andConsultationIdeaLike(String value) {
            addCriterion("\"consultation_idea\" like", value, "consultationIdea");
            return (Criteria) this;
        }

        public Criteria andConsultationIdeaNotLike(String value) {
            addCriterion("\"consultation_idea\" not like", value, "consultationIdea");
            return (Criteria) this;
        }

        public Criteria andConsultationIdeaIn(List<String> values) {
            addCriterion("\"consultation_idea\" in", values, "consultationIdea");
            return (Criteria) this;
        }

        public Criteria andConsultationIdeaNotIn(List<String> values) {
            addCriterion("\"consultation_idea\" not in", values, "consultationIdea");
            return (Criteria) this;
        }

        public Criteria andConsultationIdeaBetween(String value1, String value2) {
            addCriterion("\"consultation_idea\" between", value1, value2, "consultationIdea");
            return (Criteria) this;
        }

        public Criteria andConsultationIdeaNotBetween(String value1, String value2) {
            addCriterion("\"consultation_idea\" not between", value1, value2, "consultationIdea");
            return (Criteria) this;
        }

        public Criteria andDeptAssignIsNull() {
            addCriterion("\"dept_assign\" is null");
            return (Criteria) this;
        }

        public Criteria andDeptAssignIsNotNull() {
            addCriterion("\"dept_assign\" is not null");
            return (Criteria) this;
        }

        public Criteria andDeptAssignEqualTo(String value) {
            addCriterion("\"dept_assign\" =", value, "deptAssign");
            return (Criteria) this;
        }

        public Criteria andDeptAssignNotEqualTo(String value) {
            addCriterion("\"dept_assign\" <>", value, "deptAssign");
            return (Criteria) this;
        }

        public Criteria andDeptAssignGreaterThan(String value) {
            addCriterion("\"dept_assign\" >", value, "deptAssign");
            return (Criteria) this;
        }

        public Criteria andDeptAssignGreaterThanOrEqualTo(String value) {
            addCriterion("\"dept_assign\" >=", value, "deptAssign");
            return (Criteria) this;
        }

        public Criteria andDeptAssignLessThan(String value) {
            addCriterion("\"dept_assign\" <", value, "deptAssign");
            return (Criteria) this;
        }

        public Criteria andDeptAssignLessThanOrEqualTo(String value) {
            addCriterion("\"dept_assign\" <=", value, "deptAssign");
            return (Criteria) this;
        }

        public Criteria andDeptAssignLike(String value) {
            addCriterion("\"dept_assign\" like", value, "deptAssign");
            return (Criteria) this;
        }

        public Criteria andDeptAssignNotLike(String value) {
            addCriterion("\"dept_assign\" not like", value, "deptAssign");
            return (Criteria) this;
        }

        public Criteria andDeptAssignIn(List<String> values) {
            addCriterion("\"dept_assign\" in", values, "deptAssign");
            return (Criteria) this;
        }

        public Criteria andDeptAssignNotIn(List<String> values) {
            addCriterion("\"dept_assign\" not in", values, "deptAssign");
            return (Criteria) this;
        }

        public Criteria andDeptAssignBetween(String value1, String value2) {
            addCriterion("\"dept_assign\" between", value1, value2, "deptAssign");
            return (Criteria) this;
        }

        public Criteria andDeptAssignNotBetween(String value1, String value2) {
            addCriterion("\"dept_assign\" not between", value1, value2, "deptAssign");
            return (Criteria) this;
        }

        public Criteria andConsultationCommitIsNull() {
            addCriterion("\"consultation_commit\" is null");
            return (Criteria) this;
        }

        public Criteria andConsultationCommitIsNotNull() {
            addCriterion("\"consultation_commit\" is not null");
            return (Criteria) this;
        }

        public Criteria andConsultationCommitEqualTo(String value) {
            addCriterion("\"consultation_commit\" =", value, "consultationCommit");
            return (Criteria) this;
        }

        public Criteria andConsultationCommitNotEqualTo(String value) {
            addCriterion("\"consultation_commit\" <>", value, "consultationCommit");
            return (Criteria) this;
        }

        public Criteria andConsultationCommitGreaterThan(String value) {
            addCriterion("\"consultation_commit\" >", value, "consultationCommit");
            return (Criteria) this;
        }

        public Criteria andConsultationCommitGreaterThanOrEqualTo(String value) {
            addCriterion("\"consultation_commit\" >=", value, "consultationCommit");
            return (Criteria) this;
        }

        public Criteria andConsultationCommitLessThan(String value) {
            addCriterion("\"consultation_commit\" <", value, "consultationCommit");
            return (Criteria) this;
        }

        public Criteria andConsultationCommitLessThanOrEqualTo(String value) {
            addCriterion("\"consultation_commit\" <=", value, "consultationCommit");
            return (Criteria) this;
        }

        public Criteria andConsultationCommitLike(String value) {
            addCriterion("\"consultation_commit\" like", value, "consultationCommit");
            return (Criteria) this;
        }

        public Criteria andConsultationCommitNotLike(String value) {
            addCriterion("\"consultation_commit\" not like", value, "consultationCommit");
            return (Criteria) this;
        }

        public Criteria andConsultationCommitIn(List<String> values) {
            addCriterion("\"consultation_commit\" in", values, "consultationCommit");
            return (Criteria) this;
        }

        public Criteria andConsultationCommitNotIn(List<String> values) {
            addCriterion("\"consultation_commit\" not in", values, "consultationCommit");
            return (Criteria) this;
        }

        public Criteria andConsultationCommitBetween(String value1, String value2) {
            addCriterion("\"consultation_commit\" between", value1, value2, "consultationCommit");
            return (Criteria) this;
        }

        public Criteria andConsultationCommitNotBetween(String value1, String value2) {
            addCriterion("\"consultation_commit\" not between", value1, value2, "consultationCommit");
            return (Criteria) this;
        }

        public Criteria andEndDateTimeIsNull() {
            addCriterion("\"end_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andEndDateTimeIsNotNull() {
            addCriterion("\"end_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andEndDateTimeEqualTo(Date value) {
            addCriterion("\"end_date_time\" =", value, "endDateTime");
            return (Criteria) this;
        }

        public Criteria andEndDateTimeNotEqualTo(Date value) {
            addCriterion("\"end_date_time\" <>", value, "endDateTime");
            return (Criteria) this;
        }

        public Criteria andEndDateTimeGreaterThan(Date value) {
            addCriterion("\"end_date_time\" >", value, "endDateTime");
            return (Criteria) this;
        }

        public Criteria andEndDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"end_date_time\" >=", value, "endDateTime");
            return (Criteria) this;
        }

        public Criteria andEndDateTimeLessThan(Date value) {
            addCriterion("\"end_date_time\" <", value, "endDateTime");
            return (Criteria) this;
        }

        public Criteria andEndDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"end_date_time\" <=", value, "endDateTime");
            return (Criteria) this;
        }

        public Criteria andEndDateTimeIn(List<Date> values) {
            addCriterion("\"end_date_time\" in", values, "endDateTime");
            return (Criteria) this;
        }

        public Criteria andEndDateTimeNotIn(List<Date> values) {
            addCriterion("\"end_date_time\" not in", values, "endDateTime");
            return (Criteria) this;
        }

        public Criteria andEndDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"end_date_time\" between", value1, value2, "endDateTime");
            return (Criteria) this;
        }

        public Criteria andEndDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"end_date_time\" not between", value1, value2, "endDateTime");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNull() {
            addCriterion("\"source_path\" is null");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNotNull() {
            addCriterion("\"source_path\" is not null");
            return (Criteria) this;
        }

        public Criteria andSourcePathEqualTo(String value) {
            addCriterion("\"source_path\" =", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotEqualTo(String value) {
            addCriterion("\"source_path\" <>", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThan(String value) {
            addCriterion("\"source_path\" >", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThanOrEqualTo(String value) {
            addCriterion("\"source_path\" >=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThan(String value) {
            addCriterion("\"source_path\" <", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThanOrEqualTo(String value) {
            addCriterion("\"source_path\" <=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLike(String value) {
            addCriterion("\"source_path\" like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotLike(String value) {
            addCriterion("\"source_path\" not like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathIn(List<String> values) {
            addCriterion("\"source_path\" in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotIn(List<String> values) {
            addCriterion("\"source_path\" not in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathBetween(String value1, String value2) {
            addCriterion("\"source_path\" between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotBetween(String value1, String value2) {
            addCriterion("\"source_path\" not between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNull() {
            addCriterion("\"data_state\" is null");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNotNull() {
            addCriterion("\"data_state\" is not null");
            return (Criteria) this;
        }

        public Criteria andDataStateEqualTo(String value) {
            addCriterion("\"data_state\" =", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotEqualTo(String value) {
            addCriterion("\"data_state\" <>", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThan(String value) {
            addCriterion("\"data_state\" >", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThanOrEqualTo(String value) {
            addCriterion("\"data_state\" >=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThan(String value) {
            addCriterion("\"data_state\" <", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThanOrEqualTo(String value) {
            addCriterion("\"data_state\" <=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLike(String value) {
            addCriterion("\"data_state\" like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotLike(String value) {
            addCriterion("\"data_state\" not like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateIn(List<String> values) {
            addCriterion("\"data_state\" in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotIn(List<String> values) {
            addCriterion("\"data_state\" not in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateBetween(String value1, String value2) {
            addCriterion("\"data_state\" between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotBetween(String value1, String value2) {
            addCriterion("\"data_state\" not between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andFullTextIsNull() {
            addCriterion("\"full_text\" is null");
            return (Criteria) this;
        }

        public Criteria andFullTextIsNotNull() {
            addCriterion("\"full_text\" is not null");
            return (Criteria) this;
        }

        public Criteria andFullTextEqualTo(String value) {
            addCriterion("\"full_text\" =", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextNotEqualTo(String value) {
            addCriterion("\"full_text\" <>", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextGreaterThan(String value) {
            addCriterion("\"full_text\" >", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextGreaterThanOrEqualTo(String value) {
            addCriterion("\"full_text\" >=", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextLessThan(String value) {
            addCriterion("\"full_text\" <", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextLessThanOrEqualTo(String value) {
            addCriterion("\"full_text\" <=", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextLike(String value) {
            addCriterion("\"full_text\" like", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextNotLike(String value) {
            addCriterion("\"full_text\" not like", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextIn(List<String> values) {
            addCriterion("\"full_text\" in", values, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextNotIn(List<String> values) {
            addCriterion("\"full_text\" not in", values, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextBetween(String value1, String value2) {
            addCriterion("\"full_text\" between", value1, value2, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextNotBetween(String value1, String value2) {
            addCriterion("\"full_text\" not between", value1, value2, "fullText");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}