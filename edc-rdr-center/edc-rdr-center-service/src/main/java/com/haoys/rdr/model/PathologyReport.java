package com.haoys.rdr.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class PathologyReport implements Serializable {
    @ApiModelProperty(value = "医疗机构代码")
    private String hospitalCode;

    @ApiModelProperty(value = "患者ID")
    private String patientSn;

    @ApiModelProperty(value = "住院号")
    private String visitSn;

    @ApiModelProperty(value = "门（急）诊号（原始）")
    private String visitSnOrg;

    @ApiModelProperty(value = "病案号")
    private String tpatno;

    @ApiModelProperty(value = "病理系统编号")
    private String pathSysCode;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "病人来源")
    private String patientSource;

    @ApiModelProperty(value = "送检医院")
    private String subHospital;

    @ApiModelProperty(value = "送检科室")
    private String subDept;

    @ApiModelProperty(value = "送检医师")
    private String subDoctor;

    @ApiModelProperty(value = "送检时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date subDate;

    @ApiModelProperty(value = "送检部位")
    private String subPart;

    @ApiModelProperty(value = "临床诊断")
    private String clinDiag;

    @ApiModelProperty(value = "标本接收时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receiptTime;

    @ApiModelProperty(value = "检查时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date examTime;

    @ApiModelProperty(value = "检查科室")
    private String examDept;

    @ApiModelProperty(value = "检查医生")
    private String examDoctor;

    @ApiModelProperty(value = "报告时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;

    @ApiModelProperty(value = "审核时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reviewTime;

    @ApiModelProperty(value = "病理所见-肉眼所见")
    private String pathEyeSee;

    @ApiModelProperty(value = "病理所见-镜下所见")
    private String pathMicroscope;

    @ApiModelProperty(value = "病理诊断")
    private String pathDiag;

    @ApiModelProperty(value = "病理诊断编码")
    private String pathDiagCode;

    @ApiModelProperty(value = "报告医师")
    private String reportDoctor;

    @ApiModelProperty(value = "审核医师")
    private String reviewDoctor;

    @ApiModelProperty(value = "报告状态")
    private String reportStatus;

    @ApiModelProperty(value = "溯源路径")
    private String sourcePath;

    @ApiModelProperty(value = "院内唯一id")
    private String pkId;

    @ApiModelProperty(value = "数据状态")
    private String dataState;

    @ApiModelProperty(value = "检查项目名称")
    private String examName;

    @ApiModelProperty(value = "原始患者ID")
    private String patientSnOrg;

    private static final long serialVersionUID = 1L;

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }

    public String getVisitSn() {
        return visitSn;
    }

    public void setVisitSn(String visitSn) {
        this.visitSn = visitSn;
    }

    public String getVisitSnOrg() {
        return visitSnOrg;
    }

    public void setVisitSnOrg(String visitSnOrg) {
        this.visitSnOrg = visitSnOrg;
    }

    public String getTpatno() {
        return tpatno;
    }

    public void setTpatno(String tpatno) {
        this.tpatno = tpatno;
    }

    public String getPathSysCode() {
        return pathSysCode;
    }

    public void setPathSysCode(String pathSysCode) {
        this.pathSysCode = pathSysCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPatientSource() {
        return patientSource;
    }

    public void setPatientSource(String patientSource) {
        this.patientSource = patientSource;
    }

    public String getSubHospital() {
        return subHospital;
    }

    public void setSubHospital(String subHospital) {
        this.subHospital = subHospital;
    }

    public String getSubDept() {
        return subDept;
    }

    public void setSubDept(String subDept) {
        this.subDept = subDept;
    }

    public String getSubDoctor() {
        return subDoctor;
    }

    public void setSubDoctor(String subDoctor) {
        this.subDoctor = subDoctor;
    }

    public Date getSubDate() {
        return subDate;
    }

    public void setSubDate(Date subDate) {
        this.subDate = subDate;
    }

    public String getSubPart() {
        return subPart;
    }

    public void setSubPart(String subPart) {
        this.subPart = subPart;
    }

    public String getClinDiag() {
        return clinDiag;
    }

    public void setClinDiag(String clinDiag) {
        this.clinDiag = clinDiag;
    }

    public Date getReceiptTime() {
        return receiptTime;
    }

    public void setReceiptTime(Date receiptTime) {
        this.receiptTime = receiptTime;
    }

    public Date getExamTime() {
        return examTime;
    }

    public void setExamTime(Date examTime) {
        this.examTime = examTime;
    }

    public String getExamDept() {
        return examDept;
    }

    public void setExamDept(String examDept) {
        this.examDept = examDept;
    }

    public String getExamDoctor() {
        return examDoctor;
    }

    public void setExamDoctor(String examDoctor) {
        this.examDoctor = examDoctor;
    }

    public Date getReportTime() {
        return reportTime;
    }

    public void setReportTime(Date reportTime) {
        this.reportTime = reportTime;
    }

    public Date getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(Date reviewTime) {
        this.reviewTime = reviewTime;
    }

    public String getPathEyeSee() {
        return pathEyeSee;
    }

    public void setPathEyeSee(String pathEyeSee) {
        this.pathEyeSee = pathEyeSee;
    }

    public String getPathMicroscope() {
        return pathMicroscope;
    }

    public void setPathMicroscope(String pathMicroscope) {
        this.pathMicroscope = pathMicroscope;
    }

    public String getPathDiag() {
        return pathDiag;
    }

    public void setPathDiag(String pathDiag) {
        this.pathDiag = pathDiag;
    }

    public String getPathDiagCode() {
        return pathDiagCode;
    }

    public void setPathDiagCode(String pathDiagCode) {
        this.pathDiagCode = pathDiagCode;
    }

    public String getReportDoctor() {
        return reportDoctor;
    }

    public void setReportDoctor(String reportDoctor) {
        this.reportDoctor = reportDoctor;
    }

    public String getReviewDoctor() {
        return reviewDoctor;
    }

    public void setReviewDoctor(String reviewDoctor) {
        this.reviewDoctor = reviewDoctor;
    }

    public String getReportStatus() {
        return reportStatus;
    }

    public void setReportStatus(String reportStatus) {
        this.reportStatus = reportStatus;
    }

    public String getSourcePath() {
        return sourcePath;
    }

    public void setSourcePath(String sourcePath) {
        this.sourcePath = sourcePath;
    }

    public String getPkId() {
        return pkId;
    }

    public void setPkId(String pkId) {
        this.pkId = pkId;
    }

    public String getDataState() {
        return dataState;
    }

    public void setDataState(String dataState) {
        this.dataState = dataState;
    }

    public String getExamName() {
        return examName;
    }

    public void setExamName(String examName) {
        this.examName = examName;
    }

    public String getPatientSnOrg() {
        return patientSnOrg;
    }

    public void setPatientSnOrg(String patientSnOrg) {
        this.patientSnOrg = patientSnOrg;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", hospitalCode=").append(hospitalCode);
        sb.append(", patientSn=").append(patientSn);
        sb.append(", visitSn=").append(visitSn);
        sb.append(", visitSnOrg=").append(visitSnOrg);
        sb.append(", tpatno=").append(tpatno);
        sb.append(", pathSysCode=").append(pathSysCode);
        sb.append(", name=").append(name);
        sb.append(", patientSource=").append(patientSource);
        sb.append(", subHospital=").append(subHospital);
        sb.append(", subDept=").append(subDept);
        sb.append(", subDoctor=").append(subDoctor);
        sb.append(", subDate=").append(subDate);
        sb.append(", subPart=").append(subPart);
        sb.append(", clinDiag=").append(clinDiag);
        sb.append(", receiptTime=").append(receiptTime);
        sb.append(", examTime=").append(examTime);
        sb.append(", examDept=").append(examDept);
        sb.append(", examDoctor=").append(examDoctor);
        sb.append(", reportTime=").append(reportTime);
        sb.append(", reviewTime=").append(reviewTime);
        sb.append(", pathEyeSee=").append(pathEyeSee);
        sb.append(", pathMicroscope=").append(pathMicroscope);
        sb.append(", pathDiag=").append(pathDiag);
        sb.append(", pathDiagCode=").append(pathDiagCode);
        sb.append(", reportDoctor=").append(reportDoctor);
        sb.append(", reviewDoctor=").append(reviewDoctor);
        sb.append(", reportStatus=").append(reportStatus);
        sb.append(", sourcePath=").append(sourcePath);
        sb.append(", pkId=").append(pkId);
        sb.append(", dataState=").append(dataState);
        sb.append(", examName=").append(examName);
        sb.append(", patientSnOrg=").append(patientSnOrg);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}