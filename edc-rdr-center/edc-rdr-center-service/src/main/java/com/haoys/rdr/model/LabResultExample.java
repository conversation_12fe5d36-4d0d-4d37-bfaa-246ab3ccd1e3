package com.haoys.rdr.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class LabResultExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public LabResultExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andHospitalCodeIsNull() {
            addCriterion("\"hospital_code\" is null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIsNotNull() {
            addCriterion("\"hospital_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeEqualTo(String value) {
            addCriterion("\"hospital_code\" =", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotEqualTo(String value) {
            addCriterion("\"hospital_code\" <>", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThan(String value) {
            addCriterion("\"hospital_code\" >", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" >=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThan(String value) {
            addCriterion("\"hospital_code\" <", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" <=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLike(String value) {
            addCriterion("\"hospital_code\" like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotLike(String value) {
            addCriterion("\"hospital_code\" not like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIn(List<String> values) {
            addCriterion("\"hospital_code\" in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotIn(List<String> values) {
            addCriterion("\"hospital_code\" not in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" not between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andTestNoIsNull() {
            addCriterion("\"test_no\" is null");
            return (Criteria) this;
        }

        public Criteria andTestNoIsNotNull() {
            addCriterion("\"test_no\" is not null");
            return (Criteria) this;
        }

        public Criteria andTestNoEqualTo(String value) {
            addCriterion("\"test_no\" =", value, "testNo");
            return (Criteria) this;
        }

        public Criteria andTestNoNotEqualTo(String value) {
            addCriterion("\"test_no\" <>", value, "testNo");
            return (Criteria) this;
        }

        public Criteria andTestNoGreaterThan(String value) {
            addCriterion("\"test_no\" >", value, "testNo");
            return (Criteria) this;
        }

        public Criteria andTestNoGreaterThanOrEqualTo(String value) {
            addCriterion("\"test_no\" >=", value, "testNo");
            return (Criteria) this;
        }

        public Criteria andTestNoLessThan(String value) {
            addCriterion("\"test_no\" <", value, "testNo");
            return (Criteria) this;
        }

        public Criteria andTestNoLessThanOrEqualTo(String value) {
            addCriterion("\"test_no\" <=", value, "testNo");
            return (Criteria) this;
        }

        public Criteria andTestNoLike(String value) {
            addCriterion("\"test_no\" like", value, "testNo");
            return (Criteria) this;
        }

        public Criteria andTestNoNotLike(String value) {
            addCriterion("\"test_no\" not like", value, "testNo");
            return (Criteria) this;
        }

        public Criteria andTestNoIn(List<String> values) {
            addCriterion("\"test_no\" in", values, "testNo");
            return (Criteria) this;
        }

        public Criteria andTestNoNotIn(List<String> values) {
            addCriterion("\"test_no\" not in", values, "testNo");
            return (Criteria) this;
        }

        public Criteria andTestNoBetween(String value1, String value2) {
            addCriterion("\"test_no\" between", value1, value2, "testNo");
            return (Criteria) this;
        }

        public Criteria andTestNoNotBetween(String value1, String value2) {
            addCriterion("\"test_no\" not between", value1, value2, "testNo");
            return (Criteria) this;
        }

        public Criteria andItemNoIsNull() {
            addCriterion("\"item_no\" is null");
            return (Criteria) this;
        }

        public Criteria andItemNoIsNotNull() {
            addCriterion("\"item_no\" is not null");
            return (Criteria) this;
        }

        public Criteria andItemNoEqualTo(String value) {
            addCriterion("\"item_no\" =", value, "itemNo");
            return (Criteria) this;
        }

        public Criteria andItemNoNotEqualTo(String value) {
            addCriterion("\"item_no\" <>", value, "itemNo");
            return (Criteria) this;
        }

        public Criteria andItemNoGreaterThan(String value) {
            addCriterion("\"item_no\" >", value, "itemNo");
            return (Criteria) this;
        }

        public Criteria andItemNoGreaterThanOrEqualTo(String value) {
            addCriterion("\"item_no\" >=", value, "itemNo");
            return (Criteria) this;
        }

        public Criteria andItemNoLessThan(String value) {
            addCriterion("\"item_no\" <", value, "itemNo");
            return (Criteria) this;
        }

        public Criteria andItemNoLessThanOrEqualTo(String value) {
            addCriterion("\"item_no\" <=", value, "itemNo");
            return (Criteria) this;
        }

        public Criteria andItemNoLike(String value) {
            addCriterion("\"item_no\" like", value, "itemNo");
            return (Criteria) this;
        }

        public Criteria andItemNoNotLike(String value) {
            addCriterion("\"item_no\" not like", value, "itemNo");
            return (Criteria) this;
        }

        public Criteria andItemNoIn(List<String> values) {
            addCriterion("\"item_no\" in", values, "itemNo");
            return (Criteria) this;
        }

        public Criteria andItemNoNotIn(List<String> values) {
            addCriterion("\"item_no\" not in", values, "itemNo");
            return (Criteria) this;
        }

        public Criteria andItemNoBetween(String value1, String value2) {
            addCriterion("\"item_no\" between", value1, value2, "itemNo");
            return (Criteria) this;
        }

        public Criteria andItemNoNotBetween(String value1, String value2) {
            addCriterion("\"item_no\" not between", value1, value2, "itemNo");
            return (Criteria) this;
        }

        public Criteria andPrintOrderIsNull() {
            addCriterion("\"print_order\" is null");
            return (Criteria) this;
        }

        public Criteria andPrintOrderIsNotNull() {
            addCriterion("\"print_order\" is not null");
            return (Criteria) this;
        }

        public Criteria andPrintOrderEqualTo(String value) {
            addCriterion("\"print_order\" =", value, "printOrder");
            return (Criteria) this;
        }

        public Criteria andPrintOrderNotEqualTo(String value) {
            addCriterion("\"print_order\" <>", value, "printOrder");
            return (Criteria) this;
        }

        public Criteria andPrintOrderGreaterThan(String value) {
            addCriterion("\"print_order\" >", value, "printOrder");
            return (Criteria) this;
        }

        public Criteria andPrintOrderGreaterThanOrEqualTo(String value) {
            addCriterion("\"print_order\" >=", value, "printOrder");
            return (Criteria) this;
        }

        public Criteria andPrintOrderLessThan(String value) {
            addCriterion("\"print_order\" <", value, "printOrder");
            return (Criteria) this;
        }

        public Criteria andPrintOrderLessThanOrEqualTo(String value) {
            addCriterion("\"print_order\" <=", value, "printOrder");
            return (Criteria) this;
        }

        public Criteria andPrintOrderLike(String value) {
            addCriterion("\"print_order\" like", value, "printOrder");
            return (Criteria) this;
        }

        public Criteria andPrintOrderNotLike(String value) {
            addCriterion("\"print_order\" not like", value, "printOrder");
            return (Criteria) this;
        }

        public Criteria andPrintOrderIn(List<String> values) {
            addCriterion("\"print_order\" in", values, "printOrder");
            return (Criteria) this;
        }

        public Criteria andPrintOrderNotIn(List<String> values) {
            addCriterion("\"print_order\" not in", values, "printOrder");
            return (Criteria) this;
        }

        public Criteria andPrintOrderBetween(String value1, String value2) {
            addCriterion("\"print_order\" between", value1, value2, "printOrder");
            return (Criteria) this;
        }

        public Criteria andPrintOrderNotBetween(String value1, String value2) {
            addCriterion("\"print_order\" not between", value1, value2, "printOrder");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeIsNull() {
            addCriterion("\"spcm_type\" is null");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeIsNotNull() {
            addCriterion("\"spcm_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeEqualTo(String value) {
            addCriterion("\"spcm_type\" =", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeNotEqualTo(String value) {
            addCriterion("\"spcm_type\" <>", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeGreaterThan(String value) {
            addCriterion("\"spcm_type\" >", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeGreaterThanOrEqualTo(String value) {
            addCriterion("\"spcm_type\" >=", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeLessThan(String value) {
            addCriterion("\"spcm_type\" <", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeLessThanOrEqualTo(String value) {
            addCriterion("\"spcm_type\" <=", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeLike(String value) {
            addCriterion("\"spcm_type\" like", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeNotLike(String value) {
            addCriterion("\"spcm_type\" not like", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeIn(List<String> values) {
            addCriterion("\"spcm_type\" in", values, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeNotIn(List<String> values) {
            addCriterion("\"spcm_type\" not in", values, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeBetween(String value1, String value2) {
            addCriterion("\"spcm_type\" between", value1, value2, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeNotBetween(String value1, String value2) {
            addCriterion("\"spcm_type\" not between", value1, value2, "spcmType");
            return (Criteria) this;
        }

        public Criteria andItemNameIsNull() {
            addCriterion("\"item_name\" is null");
            return (Criteria) this;
        }

        public Criteria andItemNameIsNotNull() {
            addCriterion("\"item_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andItemNameEqualTo(String value) {
            addCriterion("\"item_name\" =", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotEqualTo(String value) {
            addCriterion("\"item_name\" <>", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameGreaterThan(String value) {
            addCriterion("\"item_name\" >", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"item_name\" >=", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLessThan(String value) {
            addCriterion("\"item_name\" <", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLessThanOrEqualTo(String value) {
            addCriterion("\"item_name\" <=", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLike(String value) {
            addCriterion("\"item_name\" like", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotLike(String value) {
            addCriterion("\"item_name\" not like", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameIn(List<String> values) {
            addCriterion("\"item_name\" in", values, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotIn(List<String> values) {
            addCriterion("\"item_name\" not in", values, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameBetween(String value1, String value2) {
            addCriterion("\"item_name\" between", value1, value2, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotBetween(String value1, String value2) {
            addCriterion("\"item_name\" not between", value1, value2, "itemName");
            return (Criteria) this;
        }

        public Criteria andResultIsNull() {
            addCriterion("\"result\" is null");
            return (Criteria) this;
        }

        public Criteria andResultIsNotNull() {
            addCriterion("\"result\" is not null");
            return (Criteria) this;
        }

        public Criteria andResultEqualTo(String value) {
            addCriterion("\"result\" =", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultNotEqualTo(String value) {
            addCriterion("\"result\" <>", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultGreaterThan(String value) {
            addCriterion("\"result\" >", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultGreaterThanOrEqualTo(String value) {
            addCriterion("\"result\" >=", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultLessThan(String value) {
            addCriterion("\"result\" <", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultLessThanOrEqualTo(String value) {
            addCriterion("\"result\" <=", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultLike(String value) {
            addCriterion("\"result\" like", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultNotLike(String value) {
            addCriterion("\"result\" not like", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultIn(List<String> values) {
            addCriterion("\"result\" in", values, "result");
            return (Criteria) this;
        }

        public Criteria andResultNotIn(List<String> values) {
            addCriterion("\"result\" not in", values, "result");
            return (Criteria) this;
        }

        public Criteria andResultBetween(String value1, String value2) {
            addCriterion("\"result\" between", value1, value2, "result");
            return (Criteria) this;
        }

        public Criteria andResultNotBetween(String value1, String value2) {
            addCriterion("\"result\" not between", value1, value2, "result");
            return (Criteria) this;
        }

        public Criteria andResultNumIsNull() {
            addCriterion("\"result_num\" is null");
            return (Criteria) this;
        }

        public Criteria andResultNumIsNotNull() {
            addCriterion("\"result_num\" is not null");
            return (Criteria) this;
        }

        public Criteria andResultNumEqualTo(Double value) {
            addCriterion("\"result_num\" =", value, "resultNum");
            return (Criteria) this;
        }

        public Criteria andResultNumNotEqualTo(Double value) {
            addCriterion("\"result_num\" <>", value, "resultNum");
            return (Criteria) this;
        }

        public Criteria andResultNumGreaterThan(Double value) {
            addCriterion("\"result_num\" >", value, "resultNum");
            return (Criteria) this;
        }

        public Criteria andResultNumGreaterThanOrEqualTo(Double value) {
            addCriterion("\"result_num\" >=", value, "resultNum");
            return (Criteria) this;
        }

        public Criteria andResultNumLessThan(Double value) {
            addCriterion("\"result_num\" <", value, "resultNum");
            return (Criteria) this;
        }

        public Criteria andResultNumLessThanOrEqualTo(Double value) {
            addCriterion("\"result_num\" <=", value, "resultNum");
            return (Criteria) this;
        }

        public Criteria andResultNumIn(List<Double> values) {
            addCriterion("\"result_num\" in", values, "resultNum");
            return (Criteria) this;
        }

        public Criteria andResultNumNotIn(List<Double> values) {
            addCriterion("\"result_num\" not in", values, "resultNum");
            return (Criteria) this;
        }

        public Criteria andResultNumBetween(Double value1, Double value2) {
            addCriterion("\"result_num\" between", value1, value2, "resultNum");
            return (Criteria) this;
        }

        public Criteria andResultNumNotBetween(Double value1, Double value2) {
            addCriterion("\"result_num\" not between", value1, value2, "resultNum");
            return (Criteria) this;
        }

        public Criteria andResultTextIsNull() {
            addCriterion("\"result_text\" is null");
            return (Criteria) this;
        }

        public Criteria andResultTextIsNotNull() {
            addCriterion("\"result_text\" is not null");
            return (Criteria) this;
        }

        public Criteria andResultTextEqualTo(String value) {
            addCriterion("\"result_text\" =", value, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultTextNotEqualTo(String value) {
            addCriterion("\"result_text\" <>", value, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultTextGreaterThan(String value) {
            addCriterion("\"result_text\" >", value, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultTextGreaterThanOrEqualTo(String value) {
            addCriterion("\"result_text\" >=", value, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultTextLessThan(String value) {
            addCriterion("\"result_text\" <", value, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultTextLessThanOrEqualTo(String value) {
            addCriterion("\"result_text\" <=", value, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultTextLike(String value) {
            addCriterion("\"result_text\" like", value, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultTextNotLike(String value) {
            addCriterion("\"result_text\" not like", value, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultTextIn(List<String> values) {
            addCriterion("\"result_text\" in", values, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultTextNotIn(List<String> values) {
            addCriterion("\"result_text\" not in", values, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultTextBetween(String value1, String value2) {
            addCriterion("\"result_text\" between", value1, value2, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultTextNotBetween(String value1, String value2) {
            addCriterion("\"result_text\" not between", value1, value2, "resultText");
            return (Criteria) this;
        }

        public Criteria andUnitsIsNull() {
            addCriterion("\"units\" is null");
            return (Criteria) this;
        }

        public Criteria andUnitsIsNotNull() {
            addCriterion("\"units\" is not null");
            return (Criteria) this;
        }

        public Criteria andUnitsEqualTo(String value) {
            addCriterion("\"units\" =", value, "units");
            return (Criteria) this;
        }

        public Criteria andUnitsNotEqualTo(String value) {
            addCriterion("\"units\" <>", value, "units");
            return (Criteria) this;
        }

        public Criteria andUnitsGreaterThan(String value) {
            addCriterion("\"units\" >", value, "units");
            return (Criteria) this;
        }

        public Criteria andUnitsGreaterThanOrEqualTo(String value) {
            addCriterion("\"units\" >=", value, "units");
            return (Criteria) this;
        }

        public Criteria andUnitsLessThan(String value) {
            addCriterion("\"units\" <", value, "units");
            return (Criteria) this;
        }

        public Criteria andUnitsLessThanOrEqualTo(String value) {
            addCriterion("\"units\" <=", value, "units");
            return (Criteria) this;
        }

        public Criteria andUnitsLike(String value) {
            addCriterion("\"units\" like", value, "units");
            return (Criteria) this;
        }

        public Criteria andUnitsNotLike(String value) {
            addCriterion("\"units\" not like", value, "units");
            return (Criteria) this;
        }

        public Criteria andUnitsIn(List<String> values) {
            addCriterion("\"units\" in", values, "units");
            return (Criteria) this;
        }

        public Criteria andUnitsNotIn(List<String> values) {
            addCriterion("\"units\" not in", values, "units");
            return (Criteria) this;
        }

        public Criteria andUnitsBetween(String value1, String value2) {
            addCriterion("\"units\" between", value1, value2, "units");
            return (Criteria) this;
        }

        public Criteria andUnitsNotBetween(String value1, String value2) {
            addCriterion("\"units\" not between", value1, value2, "units");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeIsNull() {
            addCriterion("\"reference_range\" is null");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeIsNotNull() {
            addCriterion("\"reference_range\" is not null");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeEqualTo(String value) {
            addCriterion("\"reference_range\" =", value, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeNotEqualTo(String value) {
            addCriterion("\"reference_range\" <>", value, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeGreaterThan(String value) {
            addCriterion("\"reference_range\" >", value, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeGreaterThanOrEqualTo(String value) {
            addCriterion("\"reference_range\" >=", value, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeLessThan(String value) {
            addCriterion("\"reference_range\" <", value, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeLessThanOrEqualTo(String value) {
            addCriterion("\"reference_range\" <=", value, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeLike(String value) {
            addCriterion("\"reference_range\" like", value, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeNotLike(String value) {
            addCriterion("\"reference_range\" not like", value, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeIn(List<String> values) {
            addCriterion("\"reference_range\" in", values, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeNotIn(List<String> values) {
            addCriterion("\"reference_range\" not in", values, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeBetween(String value1, String value2) {
            addCriterion("\"reference_range\" between", value1, value2, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeNotBetween(String value1, String value2) {
            addCriterion("\"reference_range\" not between", value1, value2, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMinIsNull() {
            addCriterion("\"reference_range_min\" is null");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMinIsNotNull() {
            addCriterion("\"reference_range_min\" is not null");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMinEqualTo(String value) {
            addCriterion("\"reference_range_min\" =", value, "referenceRangeMin");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMinNotEqualTo(String value) {
            addCriterion("\"reference_range_min\" <>", value, "referenceRangeMin");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMinGreaterThan(String value) {
            addCriterion("\"reference_range_min\" >", value, "referenceRangeMin");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMinGreaterThanOrEqualTo(String value) {
            addCriterion("\"reference_range_min\" >=", value, "referenceRangeMin");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMinLessThan(String value) {
            addCriterion("\"reference_range_min\" <", value, "referenceRangeMin");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMinLessThanOrEqualTo(String value) {
            addCriterion("\"reference_range_min\" <=", value, "referenceRangeMin");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMinLike(String value) {
            addCriterion("\"reference_range_min\" like", value, "referenceRangeMin");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMinNotLike(String value) {
            addCriterion("\"reference_range_min\" not like", value, "referenceRangeMin");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMinIn(List<String> values) {
            addCriterion("\"reference_range_min\" in", values, "referenceRangeMin");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMinNotIn(List<String> values) {
            addCriterion("\"reference_range_min\" not in", values, "referenceRangeMin");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMinBetween(String value1, String value2) {
            addCriterion("\"reference_range_min\" between", value1, value2, "referenceRangeMin");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMinNotBetween(String value1, String value2) {
            addCriterion("\"reference_range_min\" not between", value1, value2, "referenceRangeMin");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMaxIsNull() {
            addCriterion("\"reference_range_max\" is null");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMaxIsNotNull() {
            addCriterion("\"reference_range_max\" is not null");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMaxEqualTo(String value) {
            addCriterion("\"reference_range_max\" =", value, "referenceRangeMax");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMaxNotEqualTo(String value) {
            addCriterion("\"reference_range_max\" <>", value, "referenceRangeMax");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMaxGreaterThan(String value) {
            addCriterion("\"reference_range_max\" >", value, "referenceRangeMax");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMaxGreaterThanOrEqualTo(String value) {
            addCriterion("\"reference_range_max\" >=", value, "referenceRangeMax");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMaxLessThan(String value) {
            addCriterion("\"reference_range_max\" <", value, "referenceRangeMax");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMaxLessThanOrEqualTo(String value) {
            addCriterion("\"reference_range_max\" <=", value, "referenceRangeMax");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMaxLike(String value) {
            addCriterion("\"reference_range_max\" like", value, "referenceRangeMax");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMaxNotLike(String value) {
            addCriterion("\"reference_range_max\" not like", value, "referenceRangeMax");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMaxIn(List<String> values) {
            addCriterion("\"reference_range_max\" in", values, "referenceRangeMax");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMaxNotIn(List<String> values) {
            addCriterion("\"reference_range_max\" not in", values, "referenceRangeMax");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMaxBetween(String value1, String value2) {
            addCriterion("\"reference_range_max\" between", value1, value2, "referenceRangeMax");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeMaxNotBetween(String value1, String value2) {
            addCriterion("\"reference_range_max\" not between", value1, value2, "referenceRangeMax");
            return (Criteria) this;
        }

        public Criteria andReferenceNoteIsNull() {
            addCriterion("\"reference_note\" is null");
            return (Criteria) this;
        }

        public Criteria andReferenceNoteIsNotNull() {
            addCriterion("\"reference_note\" is not null");
            return (Criteria) this;
        }

        public Criteria andReferenceNoteEqualTo(String value) {
            addCriterion("\"reference_note\" =", value, "referenceNote");
            return (Criteria) this;
        }

        public Criteria andReferenceNoteNotEqualTo(String value) {
            addCriterion("\"reference_note\" <>", value, "referenceNote");
            return (Criteria) this;
        }

        public Criteria andReferenceNoteGreaterThan(String value) {
            addCriterion("\"reference_note\" >", value, "referenceNote");
            return (Criteria) this;
        }

        public Criteria andReferenceNoteGreaterThanOrEqualTo(String value) {
            addCriterion("\"reference_note\" >=", value, "referenceNote");
            return (Criteria) this;
        }

        public Criteria andReferenceNoteLessThan(String value) {
            addCriterion("\"reference_note\" <", value, "referenceNote");
            return (Criteria) this;
        }

        public Criteria andReferenceNoteLessThanOrEqualTo(String value) {
            addCriterion("\"reference_note\" <=", value, "referenceNote");
            return (Criteria) this;
        }

        public Criteria andReferenceNoteLike(String value) {
            addCriterion("\"reference_note\" like", value, "referenceNote");
            return (Criteria) this;
        }

        public Criteria andReferenceNoteNotLike(String value) {
            addCriterion("\"reference_note\" not like", value, "referenceNote");
            return (Criteria) this;
        }

        public Criteria andReferenceNoteIn(List<String> values) {
            addCriterion("\"reference_note\" in", values, "referenceNote");
            return (Criteria) this;
        }

        public Criteria andReferenceNoteNotIn(List<String> values) {
            addCriterion("\"reference_note\" not in", values, "referenceNote");
            return (Criteria) this;
        }

        public Criteria andReferenceNoteBetween(String value1, String value2) {
            addCriterion("\"reference_note\" between", value1, value2, "referenceNote");
            return (Criteria) this;
        }

        public Criteria andReferenceNoteNotBetween(String value1, String value2) {
            addCriterion("\"reference_note\" not between", value1, value2, "referenceNote");
            return (Criteria) this;
        }

        public Criteria andAbnormalIndicatorIsNull() {
            addCriterion("\"abnormal_indicator\" is null");
            return (Criteria) this;
        }

        public Criteria andAbnormalIndicatorIsNotNull() {
            addCriterion("\"abnormal_indicator\" is not null");
            return (Criteria) this;
        }

        public Criteria andAbnormalIndicatorEqualTo(String value) {
            addCriterion("\"abnormal_indicator\" =", value, "abnormalIndicator");
            return (Criteria) this;
        }

        public Criteria andAbnormalIndicatorNotEqualTo(String value) {
            addCriterion("\"abnormal_indicator\" <>", value, "abnormalIndicator");
            return (Criteria) this;
        }

        public Criteria andAbnormalIndicatorGreaterThan(String value) {
            addCriterion("\"abnormal_indicator\" >", value, "abnormalIndicator");
            return (Criteria) this;
        }

        public Criteria andAbnormalIndicatorGreaterThanOrEqualTo(String value) {
            addCriterion("\"abnormal_indicator\" >=", value, "abnormalIndicator");
            return (Criteria) this;
        }

        public Criteria andAbnormalIndicatorLessThan(String value) {
            addCriterion("\"abnormal_indicator\" <", value, "abnormalIndicator");
            return (Criteria) this;
        }

        public Criteria andAbnormalIndicatorLessThanOrEqualTo(String value) {
            addCriterion("\"abnormal_indicator\" <=", value, "abnormalIndicator");
            return (Criteria) this;
        }

        public Criteria andAbnormalIndicatorLike(String value) {
            addCriterion("\"abnormal_indicator\" like", value, "abnormalIndicator");
            return (Criteria) this;
        }

        public Criteria andAbnormalIndicatorNotLike(String value) {
            addCriterion("\"abnormal_indicator\" not like", value, "abnormalIndicator");
            return (Criteria) this;
        }

        public Criteria andAbnormalIndicatorIn(List<String> values) {
            addCriterion("\"abnormal_indicator\" in", values, "abnormalIndicator");
            return (Criteria) this;
        }

        public Criteria andAbnormalIndicatorNotIn(List<String> values) {
            addCriterion("\"abnormal_indicator\" not in", values, "abnormalIndicator");
            return (Criteria) this;
        }

        public Criteria andAbnormalIndicatorBetween(String value1, String value2) {
            addCriterion("\"abnormal_indicator\" between", value1, value2, "abnormalIndicator");
            return (Criteria) this;
        }

        public Criteria andAbnormalIndicatorNotBetween(String value1, String value2) {
            addCriterion("\"abnormal_indicator\" not between", value1, value2, "abnormalIndicator");
            return (Criteria) this;
        }

        public Criteria andResultDateTimeIsNull() {
            addCriterion("\"result_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andResultDateTimeIsNotNull() {
            addCriterion("\"result_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andResultDateTimeEqualTo(Date value) {
            addCriterion("\"result_date_time\" =", value, "resultDateTime");
            return (Criteria) this;
        }

        public Criteria andResultDateTimeNotEqualTo(Date value) {
            addCriterion("\"result_date_time\" <>", value, "resultDateTime");
            return (Criteria) this;
        }

        public Criteria andResultDateTimeGreaterThan(Date value) {
            addCriterion("\"result_date_time\" >", value, "resultDateTime");
            return (Criteria) this;
        }

        public Criteria andResultDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"result_date_time\" >=", value, "resultDateTime");
            return (Criteria) this;
        }

        public Criteria andResultDateTimeLessThan(Date value) {
            addCriterion("\"result_date_time\" <", value, "resultDateTime");
            return (Criteria) this;
        }

        public Criteria andResultDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"result_date_time\" <=", value, "resultDateTime");
            return (Criteria) this;
        }

        public Criteria andResultDateTimeIn(List<Date> values) {
            addCriterion("\"result_date_time\" in", values, "resultDateTime");
            return (Criteria) this;
        }

        public Criteria andResultDateTimeNotIn(List<Date> values) {
            addCriterion("\"result_date_time\" not in", values, "resultDateTime");
            return (Criteria) this;
        }

        public Criteria andResultDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"result_date_time\" between", value1, value2, "resultDateTime");
            return (Criteria) this;
        }

        public Criteria andResultDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"result_date_time\" not between", value1, value2, "resultDateTime");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNull() {
            addCriterion("\"source_path\" is null");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNotNull() {
            addCriterion("\"source_path\" is not null");
            return (Criteria) this;
        }

        public Criteria andSourcePathEqualTo(String value) {
            addCriterion("\"source_path\" =", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotEqualTo(String value) {
            addCriterion("\"source_path\" <>", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThan(String value) {
            addCriterion("\"source_path\" >", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThanOrEqualTo(String value) {
            addCriterion("\"source_path\" >=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThan(String value) {
            addCriterion("\"source_path\" <", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThanOrEqualTo(String value) {
            addCriterion("\"source_path\" <=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLike(String value) {
            addCriterion("\"source_path\" like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotLike(String value) {
            addCriterion("\"source_path\" not like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathIn(List<String> values) {
            addCriterion("\"source_path\" in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotIn(List<String> values) {
            addCriterion("\"source_path\" not in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathBetween(String value1, String value2) {
            addCriterion("\"source_path\" between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotBetween(String value1, String value2) {
            addCriterion("\"source_path\" not between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNull() {
            addCriterion("\"pk_id\" is null");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNotNull() {
            addCriterion("\"pk_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkIdEqualTo(String value) {
            addCriterion("\"pk_id\" =", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotEqualTo(String value) {
            addCriterion("\"pk_id\" <>", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThan(String value) {
            addCriterion("\"pk_id\" >", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" >=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThan(String value) {
            addCriterion("\"pk_id\" <", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" <=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLike(String value) {
            addCriterion("\"pk_id\" like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotLike(String value) {
            addCriterion("\"pk_id\" not like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdIn(List<String> values) {
            addCriterion("\"pk_id\" in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotIn(List<String> values) {
            addCriterion("\"pk_id\" not in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdBetween(String value1, String value2) {
            addCriterion("\"pk_id\" between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotBetween(String value1, String value2) {
            addCriterion("\"pk_id\" not between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNull() {
            addCriterion("\"data_state\" is null");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNotNull() {
            addCriterion("\"data_state\" is not null");
            return (Criteria) this;
        }

        public Criteria andDataStateEqualTo(String value) {
            addCriterion("\"data_state\" =", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotEqualTo(String value) {
            addCriterion("\"data_state\" <>", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThan(String value) {
            addCriterion("\"data_state\" >", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThanOrEqualTo(String value) {
            addCriterion("\"data_state\" >=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThan(String value) {
            addCriterion("\"data_state\" <", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThanOrEqualTo(String value) {
            addCriterion("\"data_state\" <=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLike(String value) {
            addCriterion("\"data_state\" like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotLike(String value) {
            addCriterion("\"data_state\" not like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateIn(List<String> values) {
            addCriterion("\"data_state\" in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotIn(List<String> values) {
            addCriterion("\"data_state\" not in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateBetween(String value1, String value2) {
            addCriterion("\"data_state\" between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotBetween(String value1, String value2) {
            addCriterion("\"data_state\" not between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsIsNull() {
            addCriterion("\"test_group_items\" is null");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsIsNotNull() {
            addCriterion("\"test_group_items\" is not null");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsEqualTo(String value) {
            addCriterion("\"test_group_items\" =", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsNotEqualTo(String value) {
            addCriterion("\"test_group_items\" <>", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsGreaterThan(String value) {
            addCriterion("\"test_group_items\" >", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsGreaterThanOrEqualTo(String value) {
            addCriterion("\"test_group_items\" >=", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsLessThan(String value) {
            addCriterion("\"test_group_items\" <", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsLessThanOrEqualTo(String value) {
            addCriterion("\"test_group_items\" <=", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsLike(String value) {
            addCriterion("\"test_group_items\" like", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsNotLike(String value) {
            addCriterion("\"test_group_items\" not like", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsIn(List<String> values) {
            addCriterion("\"test_group_items\" in", values, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsNotIn(List<String> values) {
            addCriterion("\"test_group_items\" not in", values, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsBetween(String value1, String value2) {
            addCriterion("\"test_group_items\" between", value1, value2, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsNotBetween(String value1, String value2) {
            addCriterion("\"test_group_items\" not between", value1, value2, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestMethodIsNull() {
            addCriterion("\"test_method\" is null");
            return (Criteria) this;
        }

        public Criteria andTestMethodIsNotNull() {
            addCriterion("\"test_method\" is not null");
            return (Criteria) this;
        }

        public Criteria andTestMethodEqualTo(String value) {
            addCriterion("\"test_method\" =", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodNotEqualTo(String value) {
            addCriterion("\"test_method\" <>", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodGreaterThan(String value) {
            addCriterion("\"test_method\" >", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodGreaterThanOrEqualTo(String value) {
            addCriterion("\"test_method\" >=", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodLessThan(String value) {
            addCriterion("\"test_method\" <", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodLessThanOrEqualTo(String value) {
            addCriterion("\"test_method\" <=", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodLike(String value) {
            addCriterion("\"test_method\" like", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodNotLike(String value) {
            addCriterion("\"test_method\" not like", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodIn(List<String> values) {
            addCriterion("\"test_method\" in", values, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodNotIn(List<String> values) {
            addCriterion("\"test_method\" not in", values, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodBetween(String value1, String value2) {
            addCriterion("\"test_method\" between", value1, value2, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodNotBetween(String value1, String value2) {
            addCriterion("\"test_method\" not between", value1, value2, "testMethod");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeIsNull() {
            addCriterion("\"spcm_sample_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeIsNotNull() {
            addCriterion("\"spcm_sample_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeEqualTo(Date value) {
            addCriterion("\"spcm_sample_date_time\" =", value, "spcmSampleDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeNotEqualTo(Date value) {
            addCriterion("\"spcm_sample_date_time\" <>", value, "spcmSampleDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeGreaterThan(Date value) {
            addCriterion("\"spcm_sample_date_time\" >", value, "spcmSampleDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"spcm_sample_date_time\" >=", value, "spcmSampleDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeLessThan(Date value) {
            addCriterion("\"spcm_sample_date_time\" <", value, "spcmSampleDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"spcm_sample_date_time\" <=", value, "spcmSampleDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeIn(List<Date> values) {
            addCriterion("\"spcm_sample_date_time\" in", values, "spcmSampleDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeNotIn(List<Date> values) {
            addCriterion("\"spcm_sample_date_time\" not in", values, "spcmSampleDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"spcm_sample_date_time\" between", value1, value2, "spcmSampleDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"spcm_sample_date_time\" not between", value1, value2, "spcmSampleDateTime");
            return (Criteria) this;
        }

        public Criteria andReqDeptIsNull() {
            addCriterion("\"req_dept\" is null");
            return (Criteria) this;
        }

        public Criteria andReqDeptIsNotNull() {
            addCriterion("\"req_dept\" is not null");
            return (Criteria) this;
        }

        public Criteria andReqDeptEqualTo(String value) {
            addCriterion("\"req_dept\" =", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptNotEqualTo(String value) {
            addCriterion("\"req_dept\" <>", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptGreaterThan(String value) {
            addCriterion("\"req_dept\" >", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptGreaterThanOrEqualTo(String value) {
            addCriterion("\"req_dept\" >=", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptLessThan(String value) {
            addCriterion("\"req_dept\" <", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptLessThanOrEqualTo(String value) {
            addCriterion("\"req_dept\" <=", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptLike(String value) {
            addCriterion("\"req_dept\" like", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptNotLike(String value) {
            addCriterion("\"req_dept\" not like", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptIn(List<String> values) {
            addCriterion("\"req_dept\" in", values, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptNotIn(List<String> values) {
            addCriterion("\"req_dept\" not in", values, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptBetween(String value1, String value2) {
            addCriterion("\"req_dept\" between", value1, value2, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptNotBetween(String value1, String value2) {
            addCriterion("\"req_dept\" not between", value1, value2, "reqDept");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIsNull() {
            addCriterion("\"patient_sn_org\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIsNotNull() {
            addCriterion("\"patient_sn_org\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgEqualTo(String value) {
            addCriterion("\"patient_sn_org\" =", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotEqualTo(String value) {
            addCriterion("\"patient_sn_org\" <>", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgGreaterThan(String value) {
            addCriterion("\"patient_sn_org\" >", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn_org\" >=", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLessThan(String value) {
            addCriterion("\"patient_sn_org\" <", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn_org\" <=", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLike(String value) {
            addCriterion("\"patient_sn_org\" like", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotLike(String value) {
            addCriterion("\"patient_sn_org\" not like", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIn(List<String> values) {
            addCriterion("\"patient_sn_org\" in", values, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotIn(List<String> values) {
            addCriterion("\"patient_sn_org\" not in", values, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgBetween(String value1, String value2) {
            addCriterion("\"patient_sn_org\" between", value1, value2, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn_org\" not between", value1, value2, "patientSnOrg");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}