package com.haoys.rdr.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.haoys.rdr.model.RdrAnalysisDataExport;
import com.haoys.rdr.model.ProjectTesteeExportExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS("master")
public interface RdrAnalysisDataExportMapper {
    long countByExample(ProjectTesteeExportExample example);

    int deleteByExample(ProjectTesteeExportExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RdrAnalysisDataExport record);

    int insertSelective(RdrAnalysisDataExport record);

    List<RdrAnalysisDataExport> selectByExample(ProjectTesteeExportExample example);

    RdrAnalysisDataExport selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RdrAnalysisDataExport record, @Param("example") ProjectTesteeExportExample example);

    int updateByExample(@Param("record") RdrAnalysisDataExport record, @Param("example") ProjectTesteeExportExample example);

    int updateByPrimaryKeySelective(RdrAnalysisDataExport record);

    int updateByPrimaryKey(RdrAnalysisDataExport record);

    Long getPorjectTesteeCount(String projectId, String orgId);

    int rmById(String id);
    int deleteById(String id);
}
