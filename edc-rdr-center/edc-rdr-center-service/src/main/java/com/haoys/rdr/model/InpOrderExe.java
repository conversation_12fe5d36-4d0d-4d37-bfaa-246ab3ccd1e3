package com.haoys.rdr.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class InpOrderExe implements Serializable {
    @ApiModelProperty(value = "院内唯一id")
    private String pkId;

    @ApiModelProperty(value = "医疗机构代码")
    private String hospitalCode;

    @ApiModelProperty(value = "患者ID")
    private String patientSn;

    @ApiModelProperty(value = "住院号")
    private String visitSn;

    @ApiModelProperty(value = "病案号")
    private String tpatno;

    @ApiModelProperty(value = "医嘱号")
    private String orderNo;

    @ApiModelProperty(value = "医嘱子序号")
    private String orderSubNo;

    @ApiModelProperty(value = "医嘱类别")
    private String orderClass;

    @ApiModelProperty(value = "医嘱正文")
    private String orderText;

    @ApiModelProperty(value = "医嘱项目类型")
    private String orderType;

    @ApiModelProperty(value = "药品规格")
    private String drugSpec;

    @ApiModelProperty(value = "药品单次用量")
    private Double dosage;

    @ApiModelProperty(value = "剂量单位")
    private String dosageUnits;

    @ApiModelProperty(value = "用药途径")
    private String administrationRoute;

    @ApiModelProperty(value = "持续时间")
    private Integer duration;

    @ApiModelProperty(value = "持续时间单位")
    private String durationUnits;

    @ApiModelProperty(value = "开始时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDateTime;

    @ApiModelProperty(value = "停止时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stopDateTime;

    @ApiModelProperty(value = "执行频率描述")
    private String frequency;

    @ApiModelProperty(value = "频率间隔")
    private String freqInterval;

    @ApiModelProperty(value = "频率间隔单位")
    private String freqIntervalUnit;

    @ApiModelProperty(value = "执行详细时间")
    private String freqDetail;

    @ApiModelProperty(value = "护士执行时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date performSchedule;

    @ApiModelProperty(value = "开医嘱科室")
    private String orderDept;

    @ApiModelProperty(value = "开医嘱医生")
    private String orderDoctor;

    @ApiModelProperty(value = "开医嘱录入时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date enterDateTime;

    @ApiModelProperty(value = "停医嘱录入时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stopOrderDateTime;

    @ApiModelProperty(value = "医嘱状态")
    private String orderStatus;

    @ApiModelProperty(value = "作废时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date cancelDateTime;

    @ApiModelProperty(value = "溯源路径")
    private String sourcePath;

    @ApiModelProperty(value = "数据状态")
    private String dataState;

    private static final long serialVersionUID = 1L;

    public String getPkId() {
        return pkId;
    }

    public void setPkId(String pkId) {
        this.pkId = pkId;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }

    public String getVisitSn() {
        return visitSn;
    }

    public void setVisitSn(String visitSn) {
        this.visitSn = visitSn;
    }

    public String getTpatno() {
        return tpatno;
    }

    public void setTpatno(String tpatno) {
        this.tpatno = tpatno;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderSubNo() {
        return orderSubNo;
    }

    public void setOrderSubNo(String orderSubNo) {
        this.orderSubNo = orderSubNo;
    }

    public String getOrderClass() {
        return orderClass;
    }

    public void setOrderClass(String orderClass) {
        this.orderClass = orderClass;
    }

    public String getOrderText() {
        return orderText;
    }

    public void setOrderText(String orderText) {
        this.orderText = orderText;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getDrugSpec() {
        return drugSpec;
    }

    public void setDrugSpec(String drugSpec) {
        this.drugSpec = drugSpec;
    }

    public Double getDosage() {
        return dosage;
    }

    public void setDosage(Double dosage) {
        this.dosage = dosage;
    }

    public String getDosageUnits() {
        return dosageUnits;
    }

    public void setDosageUnits(String dosageUnits) {
        this.dosageUnits = dosageUnits;
    }

    public String getAdministrationRoute() {
        return administrationRoute;
    }

    public void setAdministrationRoute(String administrationRoute) {
        this.administrationRoute = administrationRoute;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getDurationUnits() {
        return durationUnits;
    }

    public void setDurationUnits(String durationUnits) {
        this.durationUnits = durationUnits;
    }

    public Date getStartDateTime() {
        return startDateTime;
    }

    public void setStartDateTime(Date startDateTime) {
        this.startDateTime = startDateTime;
    }

    public Date getStopDateTime() {
        return stopDateTime;
    }

    public void setStopDateTime(Date stopDateTime) {
        this.stopDateTime = stopDateTime;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public String getFreqInterval() {
        return freqInterval;
    }

    public void setFreqInterval(String freqInterval) {
        this.freqInterval = freqInterval;
    }

    public String getFreqIntervalUnit() {
        return freqIntervalUnit;
    }

    public void setFreqIntervalUnit(String freqIntervalUnit) {
        this.freqIntervalUnit = freqIntervalUnit;
    }

    public String getFreqDetail() {
        return freqDetail;
    }

    public void setFreqDetail(String freqDetail) {
        this.freqDetail = freqDetail;
    }

    public Date getPerformSchedule() {
        return performSchedule;
    }

    public void setPerformSchedule(Date performSchedule) {
        this.performSchedule = performSchedule;
    }

    public String getOrderDept() {
        return orderDept;
    }

    public void setOrderDept(String orderDept) {
        this.orderDept = orderDept;
    }

    public String getOrderDoctor() {
        return orderDoctor;
    }

    public void setOrderDoctor(String orderDoctor) {
        this.orderDoctor = orderDoctor;
    }

    public Date getEnterDateTime() {
        return enterDateTime;
    }

    public void setEnterDateTime(Date enterDateTime) {
        this.enterDateTime = enterDateTime;
    }

    public Date getStopOrderDateTime() {
        return stopOrderDateTime;
    }

    public void setStopOrderDateTime(Date stopOrderDateTime) {
        this.stopOrderDateTime = stopOrderDateTime;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Date getCancelDateTime() {
        return cancelDateTime;
    }

    public void setCancelDateTime(Date cancelDateTime) {
        this.cancelDateTime = cancelDateTime;
    }

    public String getSourcePath() {
        return sourcePath;
    }

    public void setSourcePath(String sourcePath) {
        this.sourcePath = sourcePath;
    }

    public String getDataState() {
        return dataState;
    }

    public void setDataState(String dataState) {
        this.dataState = dataState;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", pkId=").append(pkId);
        sb.append(", hospitalCode=").append(hospitalCode);
        sb.append(", patientSn=").append(patientSn);
        sb.append(", visitSn=").append(visitSn);
        sb.append(", tpatno=").append(tpatno);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", orderSubNo=").append(orderSubNo);
        sb.append(", orderClass=").append(orderClass);
        sb.append(", orderText=").append(orderText);
        sb.append(", orderType=").append(orderType);
        sb.append(", drugSpec=").append(drugSpec);
        sb.append(", dosage=").append(dosage);
        sb.append(", dosageUnits=").append(dosageUnits);
        sb.append(", administrationRoute=").append(administrationRoute);
        sb.append(", duration=").append(duration);
        sb.append(", durationUnits=").append(durationUnits);
        sb.append(", startDateTime=").append(startDateTime);
        sb.append(", stopDateTime=").append(stopDateTime);
        sb.append(", frequency=").append(frequency);
        sb.append(", freqInterval=").append(freqInterval);
        sb.append(", freqIntervalUnit=").append(freqIntervalUnit);
        sb.append(", freqDetail=").append(freqDetail);
        sb.append(", performSchedule=").append(performSchedule);
        sb.append(", orderDept=").append(orderDept);
        sb.append(", orderDoctor=").append(orderDoctor);
        sb.append(", enterDateTime=").append(enterDateTime);
        sb.append(", stopOrderDateTime=").append(stopOrderDateTime);
        sb.append(", orderStatus=").append(orderStatus);
        sb.append(", cancelDateTime=").append(cancelDateTime);
        sb.append(", sourcePath=").append(sourcePath);
        sb.append(", dataState=").append(dataState);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}