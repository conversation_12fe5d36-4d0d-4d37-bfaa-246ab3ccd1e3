package com.haoys.rdr.domain.vo;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.haoys.rdr.model.RdrPatientModelVariable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class PatientCommonDataVo {

    @ApiModelProperty(value = "表自定义")
    private List<RdrPatientModelVariable> model;

    @ApiModelProperty(value = "数据")
    private List<PatientDataVo<JSONObject>> data;

}
