package com.haoys.rdr.mapper;

import com.haoys.rdr.model.EmrFirstCourseAfterOper;
import com.haoys.rdr.model.EmrFirstCourseAfterOperExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface EmrFirstCourseAfterOperMapper {
    long countByExample(EmrFirstCourseAfterOperExample example);

    int deleteByExample(EmrFirstCourseAfterOperExample example);

    int deleteByPrimaryKey(String pkId);

    int insert(EmrFirstCourseAfterOper record);

    int insertSelective(EmrFirstCourseAfterOper record);

    List<EmrFirstCourseAfterOper> selectByExample(EmrFirstCourseAfterOperExample example);

    EmrFirstCourseAfterOper selectByPrimaryKey(String pkId);

    int updateByExampleSelective(@Param("record") EmrFirstCourseAfterOper record, @Param("example") EmrFirstCourseAfterOperExample example);

    int updateByExample(@Param("record") EmrFirstCourseAfterOper record, @Param("example") EmrFirstCourseAfterOperExample example);

    int updateByPrimaryKeySelective(EmrFirstCourseAfterOper record);

    int updateByPrimaryKey(EmrFirstCourseAfterOper record);
}
