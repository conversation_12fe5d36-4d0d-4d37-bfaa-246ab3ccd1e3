package com.haoys.rdr.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class InpDrugDispence implements Serializable {
    @ApiModelProperty(value = "院内唯一id")
    private String pkId;

    @ApiModelProperty(value = "医疗机构代码")
    private String hospitalCode;

    @ApiModelProperty(value = "患者ID")
    private String patientSn;

    @ApiModelProperty(value = "住院号")
    private String visitSn;

    @ApiModelProperty(value = "病案号")
    private String tpatno;

    @ApiModelProperty(value = "摆药（退药）记录标识")
    private String recordId;

    @ApiModelProperty(value = "住院药品医嘱标识")
    private String orderId;

    @ApiModelProperty(value = "组医嘱标识")
    private String orderGroupId;

    @ApiModelProperty(value = "药品名称")
    private String drugName;

    @ApiModelProperty(value = "药品编码")
    private String drugCode;

    @ApiModelProperty(value = "商品名")
    private String brandName;

    @ApiModelProperty(value = "通用名")
    private String genericName;

    @ApiModelProperty(value = "药品类型，西药、中成药、中草药等")
    private String drugType;

    @ApiModelProperty(value = "药品剂型")
    private String drugForm;

    @ApiModelProperty(value = "药品规格")
    private String drugSpec;

    @ApiModelProperty(value = "生产厂商")
    private String firm;

    @ApiModelProperty(value = "用药途径")
    private String administrationRoute;

    @ApiModelProperty(value = "给药途径编码")
    private String administrationRouteCode;

    @ApiModelProperty(value = "摆药（退药）数量，为正数时，表示摆药数量；为负数时，表示退药数量")
    private String quantity;

    @ApiModelProperty(value = "摆药（退药）数量单位")
    private String unit;

    @ApiModelProperty(value = "剂量单位编码")
    private String unitCode;

    @ApiModelProperty(value = "摆药天数")
    private String days;

    @ApiModelProperty(value = "摆药（用药）次数")
    private String times;

    @ApiModelProperty(value = "摆药（退药）药品金额")
    private String costs;

    @ApiModelProperty(value = "药品批准文号")
    private String drugApprovalNumber;

    @ApiModelProperty(value = "药品生产批号")
    private String batchNo;

    @ApiModelProperty(value = "失效日期")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expirationDate;

    @ApiModelProperty(value = "最小包装单位")
    private String minPackUnit;

    @ApiModelProperty(value = "最小包装单位编码")
    private String minPackUnitCode;

    @ApiModelProperty(value = "最小包装所含剂量")
    private String minPackDose;

    @ApiModelProperty(value = "最小包装所含剂量的剂量单位")
    private String minPackDoseUnit;

    @ApiModelProperty(value = "最小包装所含剂量单位编码")
    private String minPackDoseUnitCode;

    @ApiModelProperty(value = "包装单位")
    private String packUnit;

    @ApiModelProperty(value = "包装单位编码")
    private String packUnitCode;

    @ApiModelProperty(value = "每包装所含最小包装数量")
    private String quantityPerPack;

    @ApiModelProperty(value = "申请科室")
    private String orderDept;

    @ApiModelProperty(value = "申请科室代码")
    private String orderDeptCode;

    @ApiModelProperty(value = "发药状态，已发药，未发药")
    private String status;

    @ApiModelProperty(value = "摆药（退药）时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operationTime;

    @ApiModelProperty(value = "摆药（退药）者")
    private String operator;

    @ApiModelProperty(value = "摆药（退药）者标识")
    private String operatorId;

    @ApiModelProperty(value = "溯源路径")
    private String sourcePath;

    @ApiModelProperty(value = "数据状态")
    private String dataState;

    private static final long serialVersionUID = 1L;

    public String getPkId() {
        return pkId;
    }

    public void setPkId(String pkId) {
        this.pkId = pkId;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }

    public String getVisitSn() {
        return visitSn;
    }

    public void setVisitSn(String visitSn) {
        this.visitSn = visitSn;
    }

    public String getTpatno() {
        return tpatno;
    }

    public void setTpatno(String tpatno) {
        this.tpatno = tpatno;
    }

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderGroupId() {
        return orderGroupId;
    }

    public void setOrderGroupId(String orderGroupId) {
        this.orderGroupId = orderGroupId;
    }

    public String getDrugName() {
        return drugName;
    }

    public void setDrugName(String drugName) {
        this.drugName = drugName;
    }

    public String getDrugCode() {
        return drugCode;
    }

    public void setDrugCode(String drugCode) {
        this.drugCode = drugCode;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getGenericName() {
        return genericName;
    }

    public void setGenericName(String genericName) {
        this.genericName = genericName;
    }

    public String getDrugType() {
        return drugType;
    }

    public void setDrugType(String drugType) {
        this.drugType = drugType;
    }

    public String getDrugForm() {
        return drugForm;
    }

    public void setDrugForm(String drugForm) {
        this.drugForm = drugForm;
    }

    public String getDrugSpec() {
        return drugSpec;
    }

    public void setDrugSpec(String drugSpec) {
        this.drugSpec = drugSpec;
    }

    public String getFirm() {
        return firm;
    }

    public void setFirm(String firm) {
        this.firm = firm;
    }

    public String getAdministrationRoute() {
        return administrationRoute;
    }

    public void setAdministrationRoute(String administrationRoute) {
        this.administrationRoute = administrationRoute;
    }

    public String getAdministrationRouteCode() {
        return administrationRouteCode;
    }

    public void setAdministrationRouteCode(String administrationRouteCode) {
        this.administrationRouteCode = administrationRouteCode;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getDays() {
        return days;
    }

    public void setDays(String days) {
        this.days = days;
    }

    public String getTimes() {
        return times;
    }

    public void setTimes(String times) {
        this.times = times;
    }

    public String getCosts() {
        return costs;
    }

    public void setCosts(String costs) {
        this.costs = costs;
    }

    public String getDrugApprovalNumber() {
        return drugApprovalNumber;
    }

    public void setDrugApprovalNumber(String drugApprovalNumber) {
        this.drugApprovalNumber = drugApprovalNumber;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getMinPackUnit() {
        return minPackUnit;
    }

    public void setMinPackUnit(String minPackUnit) {
        this.minPackUnit = minPackUnit;
    }

    public String getMinPackUnitCode() {
        return minPackUnitCode;
    }

    public void setMinPackUnitCode(String minPackUnitCode) {
        this.minPackUnitCode = minPackUnitCode;
    }

    public String getMinPackDose() {
        return minPackDose;
    }

    public void setMinPackDose(String minPackDose) {
        this.minPackDose = minPackDose;
    }

    public String getMinPackDoseUnit() {
        return minPackDoseUnit;
    }

    public void setMinPackDoseUnit(String minPackDoseUnit) {
        this.minPackDoseUnit = minPackDoseUnit;
    }

    public String getMinPackDoseUnitCode() {
        return minPackDoseUnitCode;
    }

    public void setMinPackDoseUnitCode(String minPackDoseUnitCode) {
        this.minPackDoseUnitCode = minPackDoseUnitCode;
    }

    public String getPackUnit() {
        return packUnit;
    }

    public void setPackUnit(String packUnit) {
        this.packUnit = packUnit;
    }

    public String getPackUnitCode() {
        return packUnitCode;
    }

    public void setPackUnitCode(String packUnitCode) {
        this.packUnitCode = packUnitCode;
    }

    public String getQuantityPerPack() {
        return quantityPerPack;
    }

    public void setQuantityPerPack(String quantityPerPack) {
        this.quantityPerPack = quantityPerPack;
    }

    public String getOrderDept() {
        return orderDept;
    }

    public void setOrderDept(String orderDept) {
        this.orderDept = orderDept;
    }

    public String getOrderDeptCode() {
        return orderDeptCode;
    }

    public void setOrderDeptCode(String orderDeptCode) {
        this.orderDeptCode = orderDeptCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(Date operationTime) {
        this.operationTime = operationTime;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getSourcePath() {
        return sourcePath;
    }

    public void setSourcePath(String sourcePath) {
        this.sourcePath = sourcePath;
    }

    public String getDataState() {
        return dataState;
    }

    public void setDataState(String dataState) {
        this.dataState = dataState;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", pkId=").append(pkId);
        sb.append(", hospitalCode=").append(hospitalCode);
        sb.append(", patientSn=").append(patientSn);
        sb.append(", visitSn=").append(visitSn);
        sb.append(", tpatno=").append(tpatno);
        sb.append(", recordId=").append(recordId);
        sb.append(", orderId=").append(orderId);
        sb.append(", orderGroupId=").append(orderGroupId);
        sb.append(", drugName=").append(drugName);
        sb.append(", drugCode=").append(drugCode);
        sb.append(", brandName=").append(brandName);
        sb.append(", genericName=").append(genericName);
        sb.append(", drugType=").append(drugType);
        sb.append(", drugForm=").append(drugForm);
        sb.append(", drugSpec=").append(drugSpec);
        sb.append(", firm=").append(firm);
        sb.append(", administrationRoute=").append(administrationRoute);
        sb.append(", administrationRouteCode=").append(administrationRouteCode);
        sb.append(", quantity=").append(quantity);
        sb.append(", unit=").append(unit);
        sb.append(", unitCode=").append(unitCode);
        sb.append(", days=").append(days);
        sb.append(", times=").append(times);
        sb.append(", costs=").append(costs);
        sb.append(", drugApprovalNumber=").append(drugApprovalNumber);
        sb.append(", batchNo=").append(batchNo);
        sb.append(", expirationDate=").append(expirationDate);
        sb.append(", minPackUnit=").append(minPackUnit);
        sb.append(", minPackUnitCode=").append(minPackUnitCode);
        sb.append(", minPackDose=").append(minPackDose);
        sb.append(", minPackDoseUnit=").append(minPackDoseUnit);
        sb.append(", minPackDoseUnitCode=").append(minPackDoseUnitCode);
        sb.append(", packUnit=").append(packUnit);
        sb.append(", packUnitCode=").append(packUnitCode);
        sb.append(", quantityPerPack=").append(quantityPerPack);
        sb.append(", orderDept=").append(orderDept);
        sb.append(", orderDeptCode=").append(orderDeptCode);
        sb.append(", status=").append(status);
        sb.append(", operationTime=").append(operationTime);
        sb.append(", operator=").append(operator);
        sb.append(", operatorId=").append(operatorId);
        sb.append(", sourcePath=").append(sourcePath);
        sb.append(", dataState=").append(dataState);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}