package com.haoys.rdr.domain.dto;

import com.haoys.user.elasticsearch.SearchDto;
import com.haoys.user.elasticsearch.SearchTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public
class SearchGroupDto {

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "组与组的关联关系,默认与关系")
    private String groupSearchType = SearchTypeEnum.AND.getCode();

    @ApiModelProperty(value = "组内条件")
    List<SearchDto> list;


}

