package com.haoys.rdr.mapper;

import com.haoys.rdr.model.InpVisit;
import com.haoys.rdr.model.InpVisitExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface InpVisitMapper {
    long countByExample(InpVisitExample example);

    int deleteByExample(InpVisitExample example);

    int deleteByPrimaryKey(String pkId);

    int insert(InpVisit record);

    int insertSelective(InpVisit record);

    List<InpVisit> selectByExample(InpVisitExample example);

    InpVisit selectByPrimaryKey(String pkId);

    int updateByExampleSelective(@Param("record") InpVisit record, @Param("example") InpVisitExample example);

    int updateByExample(@Param("record") InpVisit record, @Param("example") InpVisitExample example);

    int updateByPrimaryKeySelective(InpVisit record);

    int updateByPrimaryKey(InpVisit record);

    List<InpVisit> getInpVisit(String patientId);

    /**
     * 统计就诊人数(月份统计)(PostGreSQL数据库)
     * @param months 月份
     * @return
     */
    Long countPgSqlQuarter(List<Integer> months);

    long count(String databaseId);

}
