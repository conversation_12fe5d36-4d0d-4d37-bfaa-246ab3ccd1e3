package com.haoys.rdr.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class EmrEmergencyRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public EmrEmergencyRecordExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andHospitalCodeIsNull() {
            addCriterion("\"hospital_code\" is null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIsNotNull() {
            addCriterion("\"hospital_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeEqualTo(String value) {
            addCriterion("\"hospital_code\" =", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotEqualTo(String value) {
            addCriterion("\"hospital_code\" <>", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThan(String value) {
            addCriterion("\"hospital_code\" >", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" >=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThan(String value) {
            addCriterion("\"hospital_code\" <", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" <=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLike(String value) {
            addCriterion("\"hospital_code\" like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotLike(String value) {
            addCriterion("\"hospital_code\" not like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIn(List<String> values) {
            addCriterion("\"hospital_code\" in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotIn(List<String> values) {
            addCriterion("\"hospital_code\" not in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" not between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andTpatnoIsNull() {
            addCriterion("\"tpatno\" is null");
            return (Criteria) this;
        }

        public Criteria andTpatnoIsNotNull() {
            addCriterion("\"tpatno\" is not null");
            return (Criteria) this;
        }

        public Criteria andTpatnoEqualTo(String value) {
            addCriterion("\"tpatno\" =", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotEqualTo(String value) {
            addCriterion("\"tpatno\" <>", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoGreaterThan(String value) {
            addCriterion("\"tpatno\" >", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoGreaterThanOrEqualTo(String value) {
            addCriterion("\"tpatno\" >=", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLessThan(String value) {
            addCriterion("\"tpatno\" <", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLessThanOrEqualTo(String value) {
            addCriterion("\"tpatno\" <=", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLike(String value) {
            addCriterion("\"tpatno\" like", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotLike(String value) {
            addCriterion("\"tpatno\" not like", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoIn(List<String> values) {
            addCriterion("\"tpatno\" in", values, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotIn(List<String> values) {
            addCriterion("\"tpatno\" not in", values, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoBetween(String value1, String value2) {
            addCriterion("\"tpatno\" between", value1, value2, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotBetween(String value1, String value2) {
            addCriterion("\"tpatno\" not between", value1, value2, "tpatno");
            return (Criteria) this;
        }

        public Criteria andEmergencyNoteIsNull() {
            addCriterion("\"emergency_note\" is null");
            return (Criteria) this;
        }

        public Criteria andEmergencyNoteIsNotNull() {
            addCriterion("\"emergency_note\" is not null");
            return (Criteria) this;
        }

        public Criteria andEmergencyNoteEqualTo(String value) {
            addCriterion("\"emergency_note\" =", value, "emergencyNote");
            return (Criteria) this;
        }

        public Criteria andEmergencyNoteNotEqualTo(String value) {
            addCriterion("\"emergency_note\" <>", value, "emergencyNote");
            return (Criteria) this;
        }

        public Criteria andEmergencyNoteGreaterThan(String value) {
            addCriterion("\"emergency_note\" >", value, "emergencyNote");
            return (Criteria) this;
        }

        public Criteria andEmergencyNoteGreaterThanOrEqualTo(String value) {
            addCriterion("\"emergency_note\" >=", value, "emergencyNote");
            return (Criteria) this;
        }

        public Criteria andEmergencyNoteLessThan(String value) {
            addCriterion("\"emergency_note\" <", value, "emergencyNote");
            return (Criteria) this;
        }

        public Criteria andEmergencyNoteLessThanOrEqualTo(String value) {
            addCriterion("\"emergency_note\" <=", value, "emergencyNote");
            return (Criteria) this;
        }

        public Criteria andEmergencyNoteLike(String value) {
            addCriterion("\"emergency_note\" like", value, "emergencyNote");
            return (Criteria) this;
        }

        public Criteria andEmergencyNoteNotLike(String value) {
            addCriterion("\"emergency_note\" not like", value, "emergencyNote");
            return (Criteria) this;
        }

        public Criteria andEmergencyNoteIn(List<String> values) {
            addCriterion("\"emergency_note\" in", values, "emergencyNote");
            return (Criteria) this;
        }

        public Criteria andEmergencyNoteNotIn(List<String> values) {
            addCriterion("\"emergency_note\" not in", values, "emergencyNote");
            return (Criteria) this;
        }

        public Criteria andEmergencyNoteBetween(String value1, String value2) {
            addCriterion("\"emergency_note\" between", value1, value2, "emergencyNote");
            return (Criteria) this;
        }

        public Criteria andEmergencyNoteNotBetween(String value1, String value2) {
            addCriterion("\"emergency_note\" not between", value1, value2, "emergencyNote");
            return (Criteria) this;
        }

        public Criteria andEmergencyPersonnelIsNull() {
            addCriterion("\"emergency_personnel\" is null");
            return (Criteria) this;
        }

        public Criteria andEmergencyPersonnelIsNotNull() {
            addCriterion("\"emergency_personnel\" is not null");
            return (Criteria) this;
        }

        public Criteria andEmergencyPersonnelEqualTo(String value) {
            addCriterion("\"emergency_personnel\" =", value, "emergencyPersonnel");
            return (Criteria) this;
        }

        public Criteria andEmergencyPersonnelNotEqualTo(String value) {
            addCriterion("\"emergency_personnel\" <>", value, "emergencyPersonnel");
            return (Criteria) this;
        }

        public Criteria andEmergencyPersonnelGreaterThan(String value) {
            addCriterion("\"emergency_personnel\" >", value, "emergencyPersonnel");
            return (Criteria) this;
        }

        public Criteria andEmergencyPersonnelGreaterThanOrEqualTo(String value) {
            addCriterion("\"emergency_personnel\" >=", value, "emergencyPersonnel");
            return (Criteria) this;
        }

        public Criteria andEmergencyPersonnelLessThan(String value) {
            addCriterion("\"emergency_personnel\" <", value, "emergencyPersonnel");
            return (Criteria) this;
        }

        public Criteria andEmergencyPersonnelLessThanOrEqualTo(String value) {
            addCriterion("\"emergency_personnel\" <=", value, "emergencyPersonnel");
            return (Criteria) this;
        }

        public Criteria andEmergencyPersonnelLike(String value) {
            addCriterion("\"emergency_personnel\" like", value, "emergencyPersonnel");
            return (Criteria) this;
        }

        public Criteria andEmergencyPersonnelNotLike(String value) {
            addCriterion("\"emergency_personnel\" not like", value, "emergencyPersonnel");
            return (Criteria) this;
        }

        public Criteria andEmergencyPersonnelIn(List<String> values) {
            addCriterion("\"emergency_personnel\" in", values, "emergencyPersonnel");
            return (Criteria) this;
        }

        public Criteria andEmergencyPersonnelNotIn(List<String> values) {
            addCriterion("\"emergency_personnel\" not in", values, "emergencyPersonnel");
            return (Criteria) this;
        }

        public Criteria andEmergencyPersonnelBetween(String value1, String value2) {
            addCriterion("\"emergency_personnel\" between", value1, value2, "emergencyPersonnel");
            return (Criteria) this;
        }

        public Criteria andEmergencyPersonnelNotBetween(String value1, String value2) {
            addCriterion("\"emergency_personnel\" not between", value1, value2, "emergencyPersonnel");
            return (Criteria) this;
        }

        public Criteria andRecordTimeIsNull() {
            addCriterion("\"record_time\" is null");
            return (Criteria) this;
        }

        public Criteria andRecordTimeIsNotNull() {
            addCriterion("\"record_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andRecordTimeEqualTo(Date value) {
            addCriterion("\"record_time\" =", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeNotEqualTo(Date value) {
            addCriterion("\"record_time\" <>", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeGreaterThan(Date value) {
            addCriterion("\"record_time\" >", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"record_time\" >=", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeLessThan(Date value) {
            addCriterion("\"record_time\" <", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"record_time\" <=", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeIn(List<Date> values) {
            addCriterion("\"record_time\" in", values, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeNotIn(List<Date> values) {
            addCriterion("\"record_time\" not in", values, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeBetween(Date value1, Date value2) {
            addCriterion("\"record_time\" between", value1, value2, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"record_time\" not between", value1, value2, "recordTime");
            return (Criteria) this;
        }

        public Criteria andDoctorSignIsNull() {
            addCriterion("\"doctor_sign\" is null");
            return (Criteria) this;
        }

        public Criteria andDoctorSignIsNotNull() {
            addCriterion("\"doctor_sign\" is not null");
            return (Criteria) this;
        }

        public Criteria andDoctorSignEqualTo(String value) {
            addCriterion("\"doctor_sign\" =", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignNotEqualTo(String value) {
            addCriterion("\"doctor_sign\" <>", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignGreaterThan(String value) {
            addCriterion("\"doctor_sign\" >", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignGreaterThanOrEqualTo(String value) {
            addCriterion("\"doctor_sign\" >=", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignLessThan(String value) {
            addCriterion("\"doctor_sign\" <", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignLessThanOrEqualTo(String value) {
            addCriterion("\"doctor_sign\" <=", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignLike(String value) {
            addCriterion("\"doctor_sign\" like", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignNotLike(String value) {
            addCriterion("\"doctor_sign\" not like", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignIn(List<String> values) {
            addCriterion("\"doctor_sign\" in", values, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignNotIn(List<String> values) {
            addCriterion("\"doctor_sign\" not in", values, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignBetween(String value1, String value2) {
            addCriterion("\"doctor_sign\" between", value1, value2, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignNotBetween(String value1, String value2) {
            addCriterion("\"doctor_sign\" not between", value1, value2, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNull() {
            addCriterion("\"source_path\" is null");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNotNull() {
            addCriterion("\"source_path\" is not null");
            return (Criteria) this;
        }

        public Criteria andSourcePathEqualTo(String value) {
            addCriterion("\"source_path\" =", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotEqualTo(String value) {
            addCriterion("\"source_path\" <>", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThan(String value) {
            addCriterion("\"source_path\" >", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThanOrEqualTo(String value) {
            addCriterion("\"source_path\" >=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThan(String value) {
            addCriterion("\"source_path\" <", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThanOrEqualTo(String value) {
            addCriterion("\"source_path\" <=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLike(String value) {
            addCriterion("\"source_path\" like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotLike(String value) {
            addCriterion("\"source_path\" not like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathIn(List<String> values) {
            addCriterion("\"source_path\" in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotIn(List<String> values) {
            addCriterion("\"source_path\" not in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathBetween(String value1, String value2) {
            addCriterion("\"source_path\" between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotBetween(String value1, String value2) {
            addCriterion("\"source_path\" not between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNull() {
            addCriterion("\"pk_id\" is null");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNotNull() {
            addCriterion("\"pk_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkIdEqualTo(String value) {
            addCriterion("\"pk_id\" =", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotEqualTo(String value) {
            addCriterion("\"pk_id\" <>", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThan(String value) {
            addCriterion("\"pk_id\" >", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" >=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThan(String value) {
            addCriterion("\"pk_id\" <", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" <=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLike(String value) {
            addCriterion("\"pk_id\" like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotLike(String value) {
            addCriterion("\"pk_id\" not like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdIn(List<String> values) {
            addCriterion("\"pk_id\" in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotIn(List<String> values) {
            addCriterion("\"pk_id\" not in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdBetween(String value1, String value2) {
            addCriterion("\"pk_id\" between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotBetween(String value1, String value2) {
            addCriterion("\"pk_id\" not between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNull() {
            addCriterion("\"data_state\" is null");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNotNull() {
            addCriterion("\"data_state\" is not null");
            return (Criteria) this;
        }

        public Criteria andDataStateEqualTo(String value) {
            addCriterion("\"data_state\" =", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotEqualTo(String value) {
            addCriterion("\"data_state\" <>", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThan(String value) {
            addCriterion("\"data_state\" >", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThanOrEqualTo(String value) {
            addCriterion("\"data_state\" >=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThan(String value) {
            addCriterion("\"data_state\" <", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThanOrEqualTo(String value) {
            addCriterion("\"data_state\" <=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLike(String value) {
            addCriterion("\"data_state\" like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotLike(String value) {
            addCriterion("\"data_state\" not like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateIn(List<String> values) {
            addCriterion("\"data_state\" in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotIn(List<String> values) {
            addCriterion("\"data_state\" not in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateBetween(String value1, String value2) {
            addCriterion("\"data_state\" between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotBetween(String value1, String value2) {
            addCriterion("\"data_state\" not between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andFullTextIsNull() {
            addCriterion("\"full_text\" is null");
            return (Criteria) this;
        }

        public Criteria andFullTextIsNotNull() {
            addCriterion("\"full_text\" is not null");
            return (Criteria) this;
        }

        public Criteria andFullTextEqualTo(String value) {
            addCriterion("\"full_text\" =", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextNotEqualTo(String value) {
            addCriterion("\"full_text\" <>", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextGreaterThan(String value) {
            addCriterion("\"full_text\" >", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextGreaterThanOrEqualTo(String value) {
            addCriterion("\"full_text\" >=", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextLessThan(String value) {
            addCriterion("\"full_text\" <", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextLessThanOrEqualTo(String value) {
            addCriterion("\"full_text\" <=", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextLike(String value) {
            addCriterion("\"full_text\" like", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextNotLike(String value) {
            addCriterion("\"full_text\" not like", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextIn(List<String> values) {
            addCriterion("\"full_text\" in", values, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextNotIn(List<String> values) {
            addCriterion("\"full_text\" not in", values, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextBetween(String value1, String value2) {
            addCriterion("\"full_text\" between", value1, value2, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextNotBetween(String value1, String value2) {
            addCriterion("\"full_text\" not between", value1, value2, "fullText");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIsNull() {
            addCriterion("\"patient_sn_org\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIsNotNull() {
            addCriterion("\"patient_sn_org\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgEqualTo(String value) {
            addCriterion("\"patient_sn_org\" =", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotEqualTo(String value) {
            addCriterion("\"patient_sn_org\" <>", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgGreaterThan(String value) {
            addCriterion("\"patient_sn_org\" >", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn_org\" >=", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLessThan(String value) {
            addCriterion("\"patient_sn_org\" <", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn_org\" <=", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLike(String value) {
            addCriterion("\"patient_sn_org\" like", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotLike(String value) {
            addCriterion("\"patient_sn_org\" not like", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIn(List<String> values) {
            addCriterion("\"patient_sn_org\" in", values, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotIn(List<String> values) {
            addCriterion("\"patient_sn_org\" not in", values, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgBetween(String value1, String value2) {
            addCriterion("\"patient_sn_org\" between", value1, value2, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn_org\" not between", value1, value2, "patientSnOrg");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}