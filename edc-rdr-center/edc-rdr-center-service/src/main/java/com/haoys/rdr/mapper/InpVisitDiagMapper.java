package com.haoys.rdr.mapper;

import com.haoys.rdr.model.InpVisitDiag;
import com.haoys.rdr.model.InpVisitDiagExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface InpVisitDiagMapper {
    long countByExample(InpVisitDiagExample example);

    int deleteByExample(InpVisitDiagExample example);

    int deleteByPrimaryKey(String pkId);

    int insert(InpVisitDiag record);

    int insertSelective(InpVisitDiag record);

    List<InpVisitDiag> selectByExample(InpVisitDiagExample example);

    InpVisitDiag selectByPrimaryKey(String pkId);

    int updateByExampleSelective(@Param("record") InpVisitDiag record, @Param("example") InpVisitDiagExample example);

    int updateByExample(@Param("record") InpVisitDiag record, @Param("example") InpVisitDiagExample example);

    int updateByPrimaryKeySelective(InpVisitDiag record);

    int updateByPrimaryKey(InpVisitDiag record);
}