package com.haoys.rdr.service;


import com.haoys.user.common.api.CommonResult;
import com.haoys.rdr.domain.param.SearchParam;

import java.io.IOException;
import java.util.Map;

/**
 * 纳排搜索(实体表搜索的方式)
 */

public interface RdrPatientNaPiSearchRdrEsService {

    /**
     * 根据搜索条件进行查询
     * @param param 搜索和显示信息
     * @return
     */
    CommonResult<Map<String, Object>> list(SearchParam param) throws IOException;

    /**
     * 统计命中的病历
     * @param param
     * @return
     */
    CommonResult<Object> countVisit(SearchParam param);

    /**
     * 就诊视图
     * @param param
     * @return
     */
    CommonResult<Object> visitList(SearchParam param) throws IOException ;

    /**
     * 单次就诊信息
     * @param visitSn 就诊流水号
     * @return
     */
    CommonResult<Object> visitInfo(String visitSn);
}
