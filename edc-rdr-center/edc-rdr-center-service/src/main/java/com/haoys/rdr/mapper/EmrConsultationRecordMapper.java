package com.haoys.rdr.mapper;

import com.haoys.rdr.model.EmrConsultationRecord;
import com.haoys.rdr.model.EmrConsultationRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface EmrConsultationRecordMapper {
    long countByExample(EmrConsultationRecordExample example);

    int deleteByExample(EmrConsultationRecordExample example);

    int deleteByPrimaryKey(String pkId);

    int insert(EmrConsultationRecord record);

    int insertSelective(EmrConsultationRecord record);

    List<EmrConsultationRecord> selectByExample(EmrConsultationRecordExample example);

    EmrConsultationRecord selectByPrimaryKey(String pkId);

    int updateByExampleSelective(@Param("record") EmrConsultationRecord record, @Param("example") EmrConsultationRecordExample example);

    int updateByExample(@Param("record") EmrConsultationRecord record, @Param("example") EmrConsultationRecordExample example);

    int updateByPrimaryKeySelective(EmrConsultationRecord record);

    int updateByPrimaryKey(EmrConsultationRecord record);
}