package com.haoys.rdr.domain.geo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class GeoJsonVo {
    
    
    private String type;
    private List<Feature> features = new ArrayList<>();
    
    
    @Data
    public static class Feature{
        private String type;
        private Geometry geometry;
        private Properties properties;
    }
    
    @Data
    public static class Geometry{
        private String type;
        //private List<Double> coordinates;
    }
    
    @Data
    public static class Properties{
        private String name;
        private String adcode;
        private String level;
        private Parent parent;
        private String center;
        private String centroid;
        private int childrenNum;
    }
    
    @Data
    public static class Parent{
        private String adcode;
        private String name;
    }
    
}
