package com.haoys.rdr.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class RdrPatientDataCenterExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public RdrPatientDataCenterExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andDatasetCodeIsNull() {
            addCriterion("\"dataset_code\" is null");
            return (Criteria) this;
        }

        public Criteria andDatasetCodeIsNotNull() {
            addCriterion("\"dataset_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andDatasetCodeEqualTo(String value) {
            addCriterion("\"dataset_code\" =", value, "datasetCode");
            return (Criteria) this;
        }

        public Criteria andDatasetCodeNotEqualTo(String value) {
            addCriterion("\"dataset_code\" <>", value, "datasetCode");
            return (Criteria) this;
        }

        public Criteria andDatasetCodeGreaterThan(String value) {
            addCriterion("\"dataset_code\" >", value, "datasetCode");
            return (Criteria) this;
        }

        public Criteria andDatasetCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"dataset_code\" >=", value, "datasetCode");
            return (Criteria) this;
        }

        public Criteria andDatasetCodeLessThan(String value) {
            addCriterion("\"dataset_code\" <", value, "datasetCode");
            return (Criteria) this;
        }

        public Criteria andDatasetCodeLessThanOrEqualTo(String value) {
            addCriterion("\"dataset_code\" <=", value, "datasetCode");
            return (Criteria) this;
        }

        public Criteria andDatasetCodeLike(String value) {
            addCriterion("\"dataset_code\" like", value, "datasetCode");
            return (Criteria) this;
        }

        public Criteria andDatasetCodeNotLike(String value) {
            addCriterion("\"dataset_code\" not like", value, "datasetCode");
            return (Criteria) this;
        }

        public Criteria andDatasetCodeIn(List<String> values) {
            addCriterion("\"dataset_code\" in", values, "datasetCode");
            return (Criteria) this;
        }

        public Criteria andDatasetCodeNotIn(List<String> values) {
            addCriterion("\"dataset_code\" not in", values, "datasetCode");
            return (Criteria) this;
        }

        public Criteria andDatasetCodeBetween(String value1, String value2) {
            addCriterion("\"dataset_code\" between", value1, value2, "datasetCode");
            return (Criteria) this;
        }

        public Criteria andDatasetCodeNotBetween(String value1, String value2) {
            addCriterion("\"dataset_code\" not between", value1, value2, "datasetCode");
            return (Criteria) this;
        }

        public Criteria andDatasetNameIsNull() {
            addCriterion("\"dataset_name\" is null");
            return (Criteria) this;
        }

        public Criteria andDatasetNameIsNotNull() {
            addCriterion("\"dataset_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andDatasetNameEqualTo(String value) {
            addCriterion("\"dataset_name\" =", value, "datasetName");
            return (Criteria) this;
        }

        public Criteria andDatasetNameNotEqualTo(String value) {
            addCriterion("\"dataset_name\" <>", value, "datasetName");
            return (Criteria) this;
        }

        public Criteria andDatasetNameGreaterThan(String value) {
            addCriterion("\"dataset_name\" >", value, "datasetName");
            return (Criteria) this;
        }

        public Criteria andDatasetNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"dataset_name\" >=", value, "datasetName");
            return (Criteria) this;
        }

        public Criteria andDatasetNameLessThan(String value) {
            addCriterion("\"dataset_name\" <", value, "datasetName");
            return (Criteria) this;
        }

        public Criteria andDatasetNameLessThanOrEqualTo(String value) {
            addCriterion("\"dataset_name\" <=", value, "datasetName");
            return (Criteria) this;
        }

        public Criteria andDatasetNameLike(String value) {
            addCriterion("\"dataset_name\" like", value, "datasetName");
            return (Criteria) this;
        }

        public Criteria andDatasetNameNotLike(String value) {
            addCriterion("\"dataset_name\" not like", value, "datasetName");
            return (Criteria) this;
        }

        public Criteria andDatasetNameIn(List<String> values) {
            addCriterion("\"dataset_name\" in", values, "datasetName");
            return (Criteria) this;
        }

        public Criteria andDatasetNameNotIn(List<String> values) {
            addCriterion("\"dataset_name\" not in", values, "datasetName");
            return (Criteria) this;
        }

        public Criteria andDatasetNameBetween(String value1, String value2) {
            addCriterion("\"dataset_name\" between", value1, value2, "datasetName");
            return (Criteria) this;
        }

        public Criteria andDatasetNameNotBetween(String value1, String value2) {
            addCriterion("\"dataset_name\" not between", value1, value2, "datasetName");
            return (Criteria) this;
        }

        public Criteria andYearIsNull() {
            addCriterion("\"year\" is null");
            return (Criteria) this;
        }

        public Criteria andYearIsNotNull() {
            addCriterion("\"year\" is not null");
            return (Criteria) this;
        }

        public Criteria andYearEqualTo(String value) {
            addCriterion("\"year\" =", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotEqualTo(String value) {
            addCriterion("\"year\" <>", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearGreaterThan(String value) {
            addCriterion("\"year\" >", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearGreaterThanOrEqualTo(String value) {
            addCriterion("\"year\" >=", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearLessThan(String value) {
            addCriterion("\"year\" <", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearLessThanOrEqualTo(String value) {
            addCriterion("\"year\" <=", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearLike(String value) {
            addCriterion("\"year\" like", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotLike(String value) {
            addCriterion("\"year\" not like", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearIn(List<String> values) {
            addCriterion("\"year\" in", values, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotIn(List<String> values) {
            addCriterion("\"year\" not in", values, "year");
            return (Criteria) this;
        }

        public Criteria andYearBetween(String value1, String value2) {
            addCriterion("\"year\" between", value1, value2, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotBetween(String value1, String value2) {
            addCriterion("\"year\" not between", value1, value2, "year");
            return (Criteria) this;
        }

        public Criteria andDepartmentIsNull() {
            addCriterion("\"department\" is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIsNotNull() {
            addCriterion("\"department\" is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentEqualTo(String value) {
            addCriterion("\"department\" =", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotEqualTo(String value) {
            addCriterion("\"department\" <>", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentGreaterThan(String value) {
            addCriterion("\"department\" >", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentGreaterThanOrEqualTo(String value) {
            addCriterion("\"department\" >=", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLessThan(String value) {
            addCriterion("\"department\" <", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLessThanOrEqualTo(String value) {
            addCriterion("\"department\" <=", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLike(String value) {
            addCriterion("\"department\" like", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotLike(String value) {
            addCriterion("\"department\" not like", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentIn(List<String> values) {
            addCriterion("\"department\" in", values, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotIn(List<String> values) {
            addCriterion("\"department\" not in", values, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentBetween(String value1, String value2) {
            addCriterion("\"department\" between", value1, value2, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotBetween(String value1, String value2) {
            addCriterion("\"department\" not between", value1, value2, "department");
            return (Criteria) this;
        }

        public Criteria andVisitTypeIsNull() {
            addCriterion("\"visit_type\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitTypeIsNotNull() {
            addCriterion("\"visit_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitTypeEqualTo(String value) {
            addCriterion("\"visit_type\" =", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeNotEqualTo(String value) {
            addCriterion("\"visit_type\" <>", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeGreaterThan(String value) {
            addCriterion("\"visit_type\" >", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_type\" >=", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeLessThan(String value) {
            addCriterion("\"visit_type\" <", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeLessThanOrEqualTo(String value) {
            addCriterion("\"visit_type\" <=", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeLike(String value) {
            addCriterion("\"visit_type\" like", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeNotLike(String value) {
            addCriterion("\"visit_type\" not like", value, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeIn(List<String> values) {
            addCriterion("\"visit_type\" in", values, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeNotIn(List<String> values) {
            addCriterion("\"visit_type\" not in", values, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeBetween(String value1, String value2) {
            addCriterion("\"visit_type\" between", value1, value2, "visitType");
            return (Criteria) this;
        }

        public Criteria andVisitTypeNotBetween(String value1, String value2) {
            addCriterion("\"visit_type\" not between", value1, value2, "visitType");
            return (Criteria) this;
        }

        public Criteria andDatasetResultIsNull() {
            addCriterion("\"dataset_result\" is null");
            return (Criteria) this;
        }

        public Criteria andDatasetResultIsNotNull() {
            addCriterion("\"dataset_result\" is not null");
            return (Criteria) this;
        }

        public Criteria andDatasetResultEqualTo(String value) {
            addCriterion("\"dataset_result\" =", value, "datasetResult");
            return (Criteria) this;
        }

        public Criteria andDatasetResultNotEqualTo(String value) {
            addCriterion("\"dataset_result\" <>", value, "datasetResult");
            return (Criteria) this;
        }

        public Criteria andDatasetResultGreaterThan(String value) {
            addCriterion("\"dataset_result\" >", value, "datasetResult");
            return (Criteria) this;
        }

        public Criteria andDatasetResultGreaterThanOrEqualTo(String value) {
            addCriterion("\"dataset_result\" >=", value, "datasetResult");
            return (Criteria) this;
        }

        public Criteria andDatasetResultLessThan(String value) {
            addCriterion("\"dataset_result\" <", value, "datasetResult");
            return (Criteria) this;
        }

        public Criteria andDatasetResultLessThanOrEqualTo(String value) {
            addCriterion("\"dataset_result\" <=", value, "datasetResult");
            return (Criteria) this;
        }

        public Criteria andDatasetResultLike(String value) {
            addCriterion("\"dataset_result\" like", value, "datasetResult");
            return (Criteria) this;
        }

        public Criteria andDatasetResultNotLike(String value) {
            addCriterion("\"dataset_result\" not like", value, "datasetResult");
            return (Criteria) this;
        }

        public Criteria andDatasetResultIn(List<String> values) {
            addCriterion("\"dataset_result\" in", values, "datasetResult");
            return (Criteria) this;
        }

        public Criteria andDatasetResultNotIn(List<String> values) {
            addCriterion("\"dataset_result\" not in", values, "datasetResult");
            return (Criteria) this;
        }

        public Criteria andDatasetResultBetween(String value1, String value2) {
            addCriterion("\"dataset_result\" between", value1, value2, "datasetResult");
            return (Criteria) this;
        }

        public Criteria andDatasetResultNotBetween(String value1, String value2) {
            addCriterion("\"dataset_result\" not between", value1, value2, "datasetResult");
            return (Criteria) this;
        }

        public Criteria andSortIsNull() {
            addCriterion("\"sort\" is null");
            return (Criteria) this;
        }

        public Criteria andSortIsNotNull() {
            addCriterion("\"sort\" is not null");
            return (Criteria) this;
        }

        public Criteria andSortEqualTo(Integer value) {
            addCriterion("\"sort\" =", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotEqualTo(Integer value) {
            addCriterion("\"sort\" <>", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThan(Integer value) {
            addCriterion("\"sort\" >", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"sort\" >=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThan(Integer value) {
            addCriterion("\"sort\" <", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThanOrEqualTo(Integer value) {
            addCriterion("\"sort\" <=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortIn(List<Integer> values) {
            addCriterion("\"sort\" in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotIn(List<Integer> values) {
            addCriterion("\"sort\" not in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortBetween(Integer value1, Integer value2) {
            addCriterion("\"sort\" between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotBetween(Integer value1, Integer value2) {
            addCriterion("\"sort\" not between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andExpandIsNull() {
            addCriterion("\"expand\" is null");
            return (Criteria) this;
        }

        public Criteria andExpandIsNotNull() {
            addCriterion("\"expand\" is not null");
            return (Criteria) this;
        }

        public Criteria andExpandEqualTo(String value) {
            addCriterion("\"expand\" =", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotEqualTo(String value) {
            addCriterion("\"expand\" <>", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandGreaterThan(String value) {
            addCriterion("\"expand\" >", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandGreaterThanOrEqualTo(String value) {
            addCriterion("\"expand\" >=", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLessThan(String value) {
            addCriterion("\"expand\" <", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLessThanOrEqualTo(String value) {
            addCriterion("\"expand\" <=", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLike(String value) {
            addCriterion("\"expand\" like", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotLike(String value) {
            addCriterion("\"expand\" not like", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandIn(List<String> values) {
            addCriterion("\"expand\" in", values, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotIn(List<String> values) {
            addCriterion("\"expand\" not in", values, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandBetween(String value1, String value2) {
            addCriterion("\"expand\" between", value1, value2, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotBetween(String value1, String value2) {
            addCriterion("\"expand\" not between", value1, value2, "expand");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("\"create_time\" is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("\"create_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("\"create_time\" =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("\"create_time\" <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("\"create_time\" >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"create_time\" >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("\"create_time\" <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"create_time\" <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("\"create_time\" in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("\"create_time\" not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("\"create_time\" between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"create_time\" not between", value1, value2, "createTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}