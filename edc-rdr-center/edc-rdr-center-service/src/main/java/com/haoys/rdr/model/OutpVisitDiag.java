package com.haoys.rdr.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class OutpVisitDiag implements Serializable {
    @ApiModelProperty(value = "院内唯一id")
    private String pkId;

    @ApiModelProperty(value = "医疗机构代码")
    private String hospitalCode;

    @ApiModelProperty(value = "患者ID")
    private String patientSn;

    @ApiModelProperty(value = "门（急）诊号")
    private String visitSn;

    @ApiModelProperty(value = "就诊时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date visitDate;

    @ApiModelProperty(value = "诊断分类")
    private String diagnosisClass;

    @ApiModelProperty(value = "诊断顺序号")
    private String diagnosisOrderNo;

    @ApiModelProperty(value = "诊断名称")
    private String diagnosisName;

    @ApiModelProperty(value = "诊断编码")
    private String diagnosisCode;

    @ApiModelProperty(value = "诊断辅助编码")
    private String diagnosisCode2;

    @ApiModelProperty(value = "是否主诊断")
    private String isMain;

    @ApiModelProperty(value = "诊断日期")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date diagnosisDate;

    @ApiModelProperty(value = "诊断医生")
    private String doctor;

    @ApiModelProperty(value = "科室名称")
    private String deptName;

    @ApiModelProperty(value = "溯源路径")
    private String sourcePath;

    @ApiModelProperty(value = "数据状态")
    private String dataState;

    private static final long serialVersionUID = 1L;

    public String getPkId() {
        return pkId;
    }

    public void setPkId(String pkId) {
        this.pkId = pkId;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }

    public String getVisitSn() {
        return visitSn;
    }

    public void setVisitSn(String visitSn) {
        this.visitSn = visitSn;
    }

    public Date getVisitDate() {
        return visitDate;
    }

    public void setVisitDate(Date visitDate) {
        this.visitDate = visitDate;
    }

    public String getDiagnosisClass() {
        return diagnosisClass;
    }

    public void setDiagnosisClass(String diagnosisClass) {
        this.diagnosisClass = diagnosisClass;
    }

    public String getDiagnosisOrderNo() {
        return diagnosisOrderNo;
    }

    public void setDiagnosisOrderNo(String diagnosisOrderNo) {
        this.diagnosisOrderNo = diagnosisOrderNo;
    }

    public String getDiagnosisName() {
        return diagnosisName;
    }

    public void setDiagnosisName(String diagnosisName) {
        this.diagnosisName = diagnosisName;
    }

    public String getDiagnosisCode() {
        return diagnosisCode;
    }

    public void setDiagnosisCode(String diagnosisCode) {
        this.diagnosisCode = diagnosisCode;
    }

    public String getDiagnosisCode2() {
        return diagnosisCode2;
    }

    public void setDiagnosisCode2(String diagnosisCode2) {
        this.diagnosisCode2 = diagnosisCode2;
    }

    public String getIsMain() {
        return isMain;
    }

    public void setIsMain(String isMain) {
        this.isMain = isMain;
    }

    public Date getDiagnosisDate() {
        return diagnosisDate;
    }

    public void setDiagnosisDate(Date diagnosisDate) {
        this.diagnosisDate = diagnosisDate;
    }

    public String getDoctor() {
        return doctor;
    }

    public void setDoctor(String doctor) {
        this.doctor = doctor;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getSourcePath() {
        return sourcePath;
    }

    public void setSourcePath(String sourcePath) {
        this.sourcePath = sourcePath;
    }

    public String getDataState() {
        return dataState;
    }

    public void setDataState(String dataState) {
        this.dataState = dataState;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", pkId=").append(pkId);
        sb.append(", hospitalCode=").append(hospitalCode);
        sb.append(", patientSn=").append(patientSn);
        sb.append(", visitSn=").append(visitSn);
        sb.append(", visitDate=").append(visitDate);
        sb.append(", diagnosisClass=").append(diagnosisClass);
        sb.append(", diagnosisOrderNo=").append(diagnosisOrderNo);
        sb.append(", diagnosisName=").append(diagnosisName);
        sb.append(", diagnosisCode=").append(diagnosisCode);
        sb.append(", diagnosisCode2=").append(diagnosisCode2);
        sb.append(", isMain=").append(isMain);
        sb.append(", diagnosisDate=").append(diagnosisDate);
        sb.append(", doctor=").append(doctor);
        sb.append(", deptName=").append(deptName);
        sb.append(", sourcePath=").append(sourcePath);
        sb.append(", dataState=").append(dataState);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}