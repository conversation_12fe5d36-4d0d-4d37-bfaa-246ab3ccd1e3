package com.haoys.rdr.mapper;

import com.haoys.rdr.model.RdrPatientModelVariable;
import com.haoys.rdr.model.RdrPatientModelVariableExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RdrPatientModelVariableMapper {
    long countByExample(RdrPatientModelVariableExample example);

    int deleteByExample(RdrPatientModelVariableExample example);

    int deleteByPrimaryKey(String id);

    int insert(RdrPatientModelVariable record);

    int insertSelective(RdrPatientModelVariable record);

    List<RdrPatientModelVariable> selectByExample(RdrPatientModelVariableExample example);

    RdrPatientModelVariable selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") RdrPatientModelVariable record, @Param("example") RdrPatientModelVariableExample example);

    int updateByExample(@Param("record") RdrPatientModelVariable record, @Param("example") RdrPatientModelVariableExample example);

    int updateByPrimaryKeySelective(RdrPatientModelVariable record);

    int updateByPrimaryKey(RdrPatientModelVariable record);
    
    void cleanPatientModelVariableConfig(String patientModelVariableValue);
}