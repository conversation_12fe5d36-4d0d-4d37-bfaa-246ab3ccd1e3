package com.haoys.rdr.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class LabMasterExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public LabMasterExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andHospitalCodeIsNull() {
            addCriterion("\"hospital_code\" is null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIsNotNull() {
            addCriterion("\"hospital_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeEqualTo(String value) {
            addCriterion("\"hospital_code\" =", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotEqualTo(String value) {
            addCriterion("\"hospital_code\" <>", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThan(String value) {
            addCriterion("\"hospital_code\" >", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" >=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThan(String value) {
            addCriterion("\"hospital_code\" <", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" <=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLike(String value) {
            addCriterion("\"hospital_code\" like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotLike(String value) {
            addCriterion("\"hospital_code\" not like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIn(List<String> values) {
            addCriterion("\"hospital_code\" in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotIn(List<String> values) {
            addCriterion("\"hospital_code\" not in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" not between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgIsNull() {
            addCriterion("\"visit_sn_org\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgIsNotNull() {
            addCriterion("\"visit_sn_org\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgEqualTo(String value) {
            addCriterion("\"visit_sn_org\" =", value, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgNotEqualTo(String value) {
            addCriterion("\"visit_sn_org\" <>", value, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgGreaterThan(String value) {
            addCriterion("\"visit_sn_org\" >", value, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn_org\" >=", value, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgLessThan(String value) {
            addCriterion("\"visit_sn_org\" <", value, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn_org\" <=", value, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgLike(String value) {
            addCriterion("\"visit_sn_org\" like", value, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgNotLike(String value) {
            addCriterion("\"visit_sn_org\" not like", value, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgIn(List<String> values) {
            addCriterion("\"visit_sn_org\" in", values, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgNotIn(List<String> values) {
            addCriterion("\"visit_sn_org\" not in", values, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgBetween(String value1, String value2) {
            addCriterion("\"visit_sn_org\" between", value1, value2, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn_org\" not between", value1, value2, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andTestNoIsNull() {
            addCriterion("\"test_no\" is null");
            return (Criteria) this;
        }

        public Criteria andTestNoIsNotNull() {
            addCriterion("\"test_no\" is not null");
            return (Criteria) this;
        }

        public Criteria andTestNoEqualTo(String value) {
            addCriterion("\"test_no\" =", value, "testNo");
            return (Criteria) this;
        }

        public Criteria andTestNoNotEqualTo(String value) {
            addCriterion("\"test_no\" <>", value, "testNo");
            return (Criteria) this;
        }

        public Criteria andTestNoGreaterThan(String value) {
            addCriterion("\"test_no\" >", value, "testNo");
            return (Criteria) this;
        }

        public Criteria andTestNoGreaterThanOrEqualTo(String value) {
            addCriterion("\"test_no\" >=", value, "testNo");
            return (Criteria) this;
        }

        public Criteria andTestNoLessThan(String value) {
            addCriterion("\"test_no\" <", value, "testNo");
            return (Criteria) this;
        }

        public Criteria andTestNoLessThanOrEqualTo(String value) {
            addCriterion("\"test_no\" <=", value, "testNo");
            return (Criteria) this;
        }

        public Criteria andTestNoLike(String value) {
            addCriterion("\"test_no\" like", value, "testNo");
            return (Criteria) this;
        }

        public Criteria andTestNoNotLike(String value) {
            addCriterion("\"test_no\" not like", value, "testNo");
            return (Criteria) this;
        }

        public Criteria andTestNoIn(List<String> values) {
            addCriterion("\"test_no\" in", values, "testNo");
            return (Criteria) this;
        }

        public Criteria andTestNoNotIn(List<String> values) {
            addCriterion("\"test_no\" not in", values, "testNo");
            return (Criteria) this;
        }

        public Criteria andTestNoBetween(String value1, String value2) {
            addCriterion("\"test_no\" between", value1, value2, "testNo");
            return (Criteria) this;
        }

        public Criteria andTestNoNotBetween(String value1, String value2) {
            addCriterion("\"test_no\" not between", value1, value2, "testNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("\"order_no\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("\"order_no\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("\"order_no\" =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("\"order_no\" <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("\"order_no\" >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("\"order_no\" >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("\"order_no\" <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("\"order_no\" <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("\"order_no\" like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("\"order_no\" not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("\"order_no\" in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("\"order_no\" not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("\"order_no\" between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("\"order_no\" not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andPerformDateIsNull() {
            addCriterion("\"perform_date\" is null");
            return (Criteria) this;
        }

        public Criteria andPerformDateIsNotNull() {
            addCriterion("\"perform_date\" is not null");
            return (Criteria) this;
        }

        public Criteria andPerformDateEqualTo(Date value) {
            addCriterion("\"perform_date\" =", value, "performDate");
            return (Criteria) this;
        }

        public Criteria andPerformDateNotEqualTo(Date value) {
            addCriterion("\"perform_date\" <>", value, "performDate");
            return (Criteria) this;
        }

        public Criteria andPerformDateGreaterThan(Date value) {
            addCriterion("\"perform_date\" >", value, "performDate");
            return (Criteria) this;
        }

        public Criteria andPerformDateGreaterThanOrEqualTo(Date value) {
            addCriterion("\"perform_date\" >=", value, "performDate");
            return (Criteria) this;
        }

        public Criteria andPerformDateLessThan(Date value) {
            addCriterion("\"perform_date\" <", value, "performDate");
            return (Criteria) this;
        }

        public Criteria andPerformDateLessThanOrEqualTo(Date value) {
            addCriterion("\"perform_date\" <=", value, "performDate");
            return (Criteria) this;
        }

        public Criteria andPerformDateIn(List<Date> values) {
            addCriterion("\"perform_date\" in", values, "performDate");
            return (Criteria) this;
        }

        public Criteria andPerformDateNotIn(List<Date> values) {
            addCriterion("\"perform_date\" not in", values, "performDate");
            return (Criteria) this;
        }

        public Criteria andPerformDateBetween(Date value1, Date value2) {
            addCriterion("\"perform_date\" between", value1, value2, "performDate");
            return (Criteria) this;
        }

        public Criteria andPerformDateNotBetween(Date value1, Date value2) {
            addCriterion("\"perform_date\" not between", value1, value2, "performDate");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("\"name\" is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("\"name\" is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("\"name\" =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("\"name\" <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("\"name\" >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"name\" >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("\"name\" <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("\"name\" <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("\"name\" like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("\"name\" not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("\"name\" in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("\"name\" not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("\"name\" between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("\"name\" not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andGenderIsNull() {
            addCriterion("\"gender\" is null");
            return (Criteria) this;
        }

        public Criteria andGenderIsNotNull() {
            addCriterion("\"gender\" is not null");
            return (Criteria) this;
        }

        public Criteria andGenderEqualTo(String value) {
            addCriterion("\"gender\" =", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotEqualTo(String value) {
            addCriterion("\"gender\" <>", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderGreaterThan(String value) {
            addCriterion("\"gender\" >", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderGreaterThanOrEqualTo(String value) {
            addCriterion("\"gender\" >=", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderLessThan(String value) {
            addCriterion("\"gender\" <", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderLessThanOrEqualTo(String value) {
            addCriterion("\"gender\" <=", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderLike(String value) {
            addCriterion("\"gender\" like", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotLike(String value) {
            addCriterion("\"gender\" not like", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderIn(List<String> values) {
            addCriterion("\"gender\" in", values, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotIn(List<String> values) {
            addCriterion("\"gender\" not in", values, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderBetween(String value1, String value2) {
            addCriterion("\"gender\" between", value1, value2, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotBetween(String value1, String value2) {
            addCriterion("\"gender\" not between", value1, value2, "gender");
            return (Criteria) this;
        }

        public Criteria andAgeIsNull() {
            addCriterion("\"age\" is null");
            return (Criteria) this;
        }

        public Criteria andAgeIsNotNull() {
            addCriterion("\"age\" is not null");
            return (Criteria) this;
        }

        public Criteria andAgeEqualTo(Integer value) {
            addCriterion("\"age\" =", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotEqualTo(Integer value) {
            addCriterion("\"age\" <>", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeGreaterThan(Integer value) {
            addCriterion("\"age\" >", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"age\" >=", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeLessThan(Integer value) {
            addCriterion("\"age\" <", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeLessThanOrEqualTo(Integer value) {
            addCriterion("\"age\" <=", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeIn(List<Integer> values) {
            addCriterion("\"age\" in", values, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotIn(List<Integer> values) {
            addCriterion("\"age\" not in", values, "age");
            return (Criteria) this;
        }

        public Criteria andAgeBetween(Integer value1, Integer value2) {
            addCriterion("\"age\" between", value1, value2, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotBetween(Integer value1, Integer value2) {
            addCriterion("\"age\" not between", value1, value2, "age");
            return (Criteria) this;
        }

        public Criteria andPatientSourceIsNull() {
            addCriterion("\"patient_source\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSourceIsNotNull() {
            addCriterion("\"patient_source\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSourceEqualTo(String value) {
            addCriterion("\"patient_source\" =", value, "patientSource");
            return (Criteria) this;
        }

        public Criteria andPatientSourceNotEqualTo(String value) {
            addCriterion("\"patient_source\" <>", value, "patientSource");
            return (Criteria) this;
        }

        public Criteria andPatientSourceGreaterThan(String value) {
            addCriterion("\"patient_source\" >", value, "patientSource");
            return (Criteria) this;
        }

        public Criteria andPatientSourceGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_source\" >=", value, "patientSource");
            return (Criteria) this;
        }

        public Criteria andPatientSourceLessThan(String value) {
            addCriterion("\"patient_source\" <", value, "patientSource");
            return (Criteria) this;
        }

        public Criteria andPatientSourceLessThanOrEqualTo(String value) {
            addCriterion("\"patient_source\" <=", value, "patientSource");
            return (Criteria) this;
        }

        public Criteria andPatientSourceLike(String value) {
            addCriterion("\"patient_source\" like", value, "patientSource");
            return (Criteria) this;
        }

        public Criteria andPatientSourceNotLike(String value) {
            addCriterion("\"patient_source\" not like", value, "patientSource");
            return (Criteria) this;
        }

        public Criteria andPatientSourceIn(List<String> values) {
            addCriterion("\"patient_source\" in", values, "patientSource");
            return (Criteria) this;
        }

        public Criteria andPatientSourceNotIn(List<String> values) {
            addCriterion("\"patient_source\" not in", values, "patientSource");
            return (Criteria) this;
        }

        public Criteria andPatientSourceBetween(String value1, String value2) {
            addCriterion("\"patient_source\" between", value1, value2, "patientSource");
            return (Criteria) this;
        }

        public Criteria andPatientSourceNotBetween(String value1, String value2) {
            addCriterion("\"patient_source\" not between", value1, value2, "patientSource");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsIsNull() {
            addCriterion("\"test_group_items\" is null");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsIsNotNull() {
            addCriterion("\"test_group_items\" is not null");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsEqualTo(String value) {
            addCriterion("\"test_group_items\" =", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsNotEqualTo(String value) {
            addCriterion("\"test_group_items\" <>", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsGreaterThan(String value) {
            addCriterion("\"test_group_items\" >", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsGreaterThanOrEqualTo(String value) {
            addCriterion("\"test_group_items\" >=", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsLessThan(String value) {
            addCriterion("\"test_group_items\" <", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsLessThanOrEqualTo(String value) {
            addCriterion("\"test_group_items\" <=", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsLike(String value) {
            addCriterion("\"test_group_items\" like", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsNotLike(String value) {
            addCriterion("\"test_group_items\" not like", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsIn(List<String> values) {
            addCriterion("\"test_group_items\" in", values, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsNotIn(List<String> values) {
            addCriterion("\"test_group_items\" not in", values, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsBetween(String value1, String value2) {
            addCriterion("\"test_group_items\" between", value1, value2, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsNotBetween(String value1, String value2) {
            addCriterion("\"test_group_items\" not between", value1, value2, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestCauseIsNull() {
            addCriterion("\"test_cause\" is null");
            return (Criteria) this;
        }

        public Criteria andTestCauseIsNotNull() {
            addCriterion("\"test_cause\" is not null");
            return (Criteria) this;
        }

        public Criteria andTestCauseEqualTo(String value) {
            addCriterion("\"test_cause\" =", value, "testCause");
            return (Criteria) this;
        }

        public Criteria andTestCauseNotEqualTo(String value) {
            addCriterion("\"test_cause\" <>", value, "testCause");
            return (Criteria) this;
        }

        public Criteria andTestCauseGreaterThan(String value) {
            addCriterion("\"test_cause\" >", value, "testCause");
            return (Criteria) this;
        }

        public Criteria andTestCauseGreaterThanOrEqualTo(String value) {
            addCriterion("\"test_cause\" >=", value, "testCause");
            return (Criteria) this;
        }

        public Criteria andTestCauseLessThan(String value) {
            addCriterion("\"test_cause\" <", value, "testCause");
            return (Criteria) this;
        }

        public Criteria andTestCauseLessThanOrEqualTo(String value) {
            addCriterion("\"test_cause\" <=", value, "testCause");
            return (Criteria) this;
        }

        public Criteria andTestCauseLike(String value) {
            addCriterion("\"test_cause\" like", value, "testCause");
            return (Criteria) this;
        }

        public Criteria andTestCauseNotLike(String value) {
            addCriterion("\"test_cause\" not like", value, "testCause");
            return (Criteria) this;
        }

        public Criteria andTestCauseIn(List<String> values) {
            addCriterion("\"test_cause\" in", values, "testCause");
            return (Criteria) this;
        }

        public Criteria andTestCauseNotIn(List<String> values) {
            addCriterion("\"test_cause\" not in", values, "testCause");
            return (Criteria) this;
        }

        public Criteria andTestCauseBetween(String value1, String value2) {
            addCriterion("\"test_cause\" between", value1, value2, "testCause");
            return (Criteria) this;
        }

        public Criteria andTestCauseNotBetween(String value1, String value2) {
            addCriterion("\"test_cause\" not between", value1, value2, "testCause");
            return (Criteria) this;
        }

        public Criteria andTestMethodIsNull() {
            addCriterion("\"test_method\" is null");
            return (Criteria) this;
        }

        public Criteria andTestMethodIsNotNull() {
            addCriterion("\"test_method\" is not null");
            return (Criteria) this;
        }

        public Criteria andTestMethodEqualTo(String value) {
            addCriterion("\"test_method\" =", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodNotEqualTo(String value) {
            addCriterion("\"test_method\" <>", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodGreaterThan(String value) {
            addCriterion("\"test_method\" >", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodGreaterThanOrEqualTo(String value) {
            addCriterion("\"test_method\" >=", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodLessThan(String value) {
            addCriterion("\"test_method\" <", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodLessThanOrEqualTo(String value) {
            addCriterion("\"test_method\" <=", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodLike(String value) {
            addCriterion("\"test_method\" like", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodNotLike(String value) {
            addCriterion("\"test_method\" not like", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodIn(List<String> values) {
            addCriterion("\"test_method\" in", values, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodNotIn(List<String> values) {
            addCriterion("\"test_method\" not in", values, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodBetween(String value1, String value2) {
            addCriterion("\"test_method\" between", value1, value2, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodNotBetween(String value1, String value2) {
            addCriterion("\"test_method\" not between", value1, value2, "testMethod");
            return (Criteria) this;
        }

        public Criteria andClinDiagIsNull() {
            addCriterion("\"clin_diag\" is null");
            return (Criteria) this;
        }

        public Criteria andClinDiagIsNotNull() {
            addCriterion("\"clin_diag\" is not null");
            return (Criteria) this;
        }

        public Criteria andClinDiagEqualTo(String value) {
            addCriterion("\"clin_diag\" =", value, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andClinDiagNotEqualTo(String value) {
            addCriterion("\"clin_diag\" <>", value, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andClinDiagGreaterThan(String value) {
            addCriterion("\"clin_diag\" >", value, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andClinDiagGreaterThanOrEqualTo(String value) {
            addCriterion("\"clin_diag\" >=", value, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andClinDiagLessThan(String value) {
            addCriterion("\"clin_diag\" <", value, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andClinDiagLessThanOrEqualTo(String value) {
            addCriterion("\"clin_diag\" <=", value, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andClinDiagLike(String value) {
            addCriterion("\"clin_diag\" like", value, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andClinDiagNotLike(String value) {
            addCriterion("\"clin_diag\" not like", value, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andClinDiagIn(List<String> values) {
            addCriterion("\"clin_diag\" in", values, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andClinDiagNotIn(List<String> values) {
            addCriterion("\"clin_diag\" not in", values, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andClinDiagBetween(String value1, String value2) {
            addCriterion("\"clin_diag\" between", value1, value2, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andClinDiagNotBetween(String value1, String value2) {
            addCriterion("\"clin_diag\" not between", value1, value2, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeIsNull() {
            addCriterion("\"spcm_type\" is null");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeIsNotNull() {
            addCriterion("\"spcm_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeEqualTo(String value) {
            addCriterion("\"spcm_type\" =", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeNotEqualTo(String value) {
            addCriterion("\"spcm_type\" <>", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeGreaterThan(String value) {
            addCriterion("\"spcm_type\" >", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeGreaterThanOrEqualTo(String value) {
            addCriterion("\"spcm_type\" >=", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeLessThan(String value) {
            addCriterion("\"spcm_type\" <", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeLessThanOrEqualTo(String value) {
            addCriterion("\"spcm_type\" <=", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeLike(String value) {
            addCriterion("\"spcm_type\" like", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeNotLike(String value) {
            addCriterion("\"spcm_type\" not like", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeIn(List<String> values) {
            addCriterion("\"spcm_type\" in", values, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeNotIn(List<String> values) {
            addCriterion("\"spcm_type\" not in", values, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeBetween(String value1, String value2) {
            addCriterion("\"spcm_type\" between", value1, value2, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeNotBetween(String value1, String value2) {
            addCriterion("\"spcm_type\" not between", value1, value2, "spcmType");
            return (Criteria) this;
        }

        public Criteria andNotesForSpcmIsNull() {
            addCriterion("\"notes_for_spcm\" is null");
            return (Criteria) this;
        }

        public Criteria andNotesForSpcmIsNotNull() {
            addCriterion("\"notes_for_spcm\" is not null");
            return (Criteria) this;
        }

        public Criteria andNotesForSpcmEqualTo(String value) {
            addCriterion("\"notes_for_spcm\" =", value, "notesForSpcm");
            return (Criteria) this;
        }

        public Criteria andNotesForSpcmNotEqualTo(String value) {
            addCriterion("\"notes_for_spcm\" <>", value, "notesForSpcm");
            return (Criteria) this;
        }

        public Criteria andNotesForSpcmGreaterThan(String value) {
            addCriterion("\"notes_for_spcm\" >", value, "notesForSpcm");
            return (Criteria) this;
        }

        public Criteria andNotesForSpcmGreaterThanOrEqualTo(String value) {
            addCriterion("\"notes_for_spcm\" >=", value, "notesForSpcm");
            return (Criteria) this;
        }

        public Criteria andNotesForSpcmLessThan(String value) {
            addCriterion("\"notes_for_spcm\" <", value, "notesForSpcm");
            return (Criteria) this;
        }

        public Criteria andNotesForSpcmLessThanOrEqualTo(String value) {
            addCriterion("\"notes_for_spcm\" <=", value, "notesForSpcm");
            return (Criteria) this;
        }

        public Criteria andNotesForSpcmLike(String value) {
            addCriterion("\"notes_for_spcm\" like", value, "notesForSpcm");
            return (Criteria) this;
        }

        public Criteria andNotesForSpcmNotLike(String value) {
            addCriterion("\"notes_for_spcm\" not like", value, "notesForSpcm");
            return (Criteria) this;
        }

        public Criteria andNotesForSpcmIn(List<String> values) {
            addCriterion("\"notes_for_spcm\" in", values, "notesForSpcm");
            return (Criteria) this;
        }

        public Criteria andNotesForSpcmNotIn(List<String> values) {
            addCriterion("\"notes_for_spcm\" not in", values, "notesForSpcm");
            return (Criteria) this;
        }

        public Criteria andNotesForSpcmBetween(String value1, String value2) {
            addCriterion("\"notes_for_spcm\" between", value1, value2, "notesForSpcm");
            return (Criteria) this;
        }

        public Criteria andNotesForSpcmNotBetween(String value1, String value2) {
            addCriterion("\"notes_for_spcm\" not between", value1, value2, "notesForSpcm");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeIsNull() {
            addCriterion("\"spcm_sample_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeIsNotNull() {
            addCriterion("\"spcm_sample_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeEqualTo(Date value) {
            addCriterion("\"spcm_sample_date_time\" =", value, "spcmSampleDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeNotEqualTo(Date value) {
            addCriterion("\"spcm_sample_date_time\" <>", value, "spcmSampleDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeGreaterThan(Date value) {
            addCriterion("\"spcm_sample_date_time\" >", value, "spcmSampleDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"spcm_sample_date_time\" >=", value, "spcmSampleDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeLessThan(Date value) {
            addCriterion("\"spcm_sample_date_time\" <", value, "spcmSampleDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"spcm_sample_date_time\" <=", value, "spcmSampleDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeIn(List<Date> values) {
            addCriterion("\"spcm_sample_date_time\" in", values, "spcmSampleDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeNotIn(List<Date> values) {
            addCriterion("\"spcm_sample_date_time\" not in", values, "spcmSampleDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"spcm_sample_date_time\" between", value1, value2, "spcmSampleDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmSampleDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"spcm_sample_date_time\" not between", value1, value2, "spcmSampleDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmReceivedDateTimeIsNull() {
            addCriterion("\"spcm_received_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andSpcmReceivedDateTimeIsNotNull() {
            addCriterion("\"spcm_received_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andSpcmReceivedDateTimeEqualTo(Date value) {
            addCriterion("\"spcm_received_date_time\" =", value, "spcmReceivedDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmReceivedDateTimeNotEqualTo(Date value) {
            addCriterion("\"spcm_received_date_time\" <>", value, "spcmReceivedDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmReceivedDateTimeGreaterThan(Date value) {
            addCriterion("\"spcm_received_date_time\" >", value, "spcmReceivedDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmReceivedDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"spcm_received_date_time\" >=", value, "spcmReceivedDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmReceivedDateTimeLessThan(Date value) {
            addCriterion("\"spcm_received_date_time\" <", value, "spcmReceivedDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmReceivedDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"spcm_received_date_time\" <=", value, "spcmReceivedDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmReceivedDateTimeIn(List<Date> values) {
            addCriterion("\"spcm_received_date_time\" in", values, "spcmReceivedDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmReceivedDateTimeNotIn(List<Date> values) {
            addCriterion("\"spcm_received_date_time\" not in", values, "spcmReceivedDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmReceivedDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"spcm_received_date_time\" between", value1, value2, "spcmReceivedDateTime");
            return (Criteria) this;
        }

        public Criteria andSpcmReceivedDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"spcm_received_date_time\" not between", value1, value2, "spcmReceivedDateTime");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeIsNull() {
            addCriterion("\"req_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeIsNotNull() {
            addCriterion("\"req_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeEqualTo(Date value) {
            addCriterion("\"req_date_time\" =", value, "reqDateTime");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeNotEqualTo(Date value) {
            addCriterion("\"req_date_time\" <>", value, "reqDateTime");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeGreaterThan(Date value) {
            addCriterion("\"req_date_time\" >", value, "reqDateTime");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"req_date_time\" >=", value, "reqDateTime");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeLessThan(Date value) {
            addCriterion("\"req_date_time\" <", value, "reqDateTime");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"req_date_time\" <=", value, "reqDateTime");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeIn(List<Date> values) {
            addCriterion("\"req_date_time\" in", values, "reqDateTime");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeNotIn(List<Date> values) {
            addCriterion("\"req_date_time\" not in", values, "reqDateTime");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"req_date_time\" between", value1, value2, "reqDateTime");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"req_date_time\" not between", value1, value2, "reqDateTime");
            return (Criteria) this;
        }

        public Criteria andReqDeptIsNull() {
            addCriterion("\"req_dept\" is null");
            return (Criteria) this;
        }

        public Criteria andReqDeptIsNotNull() {
            addCriterion("\"req_dept\" is not null");
            return (Criteria) this;
        }

        public Criteria andReqDeptEqualTo(String value) {
            addCriterion("\"req_dept\" =", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptNotEqualTo(String value) {
            addCriterion("\"req_dept\" <>", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptGreaterThan(String value) {
            addCriterion("\"req_dept\" >", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptGreaterThanOrEqualTo(String value) {
            addCriterion("\"req_dept\" >=", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptLessThan(String value) {
            addCriterion("\"req_dept\" <", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptLessThanOrEqualTo(String value) {
            addCriterion("\"req_dept\" <=", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptLike(String value) {
            addCriterion("\"req_dept\" like", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptNotLike(String value) {
            addCriterion("\"req_dept\" not like", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptIn(List<String> values) {
            addCriterion("\"req_dept\" in", values, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptNotIn(List<String> values) {
            addCriterion("\"req_dept\" not in", values, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptBetween(String value1, String value2) {
            addCriterion("\"req_dept\" between", value1, value2, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptNotBetween(String value1, String value2) {
            addCriterion("\"req_dept\" not between", value1, value2, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDoctorIsNull() {
            addCriterion("\"req_doctor\" is null");
            return (Criteria) this;
        }

        public Criteria andReqDoctorIsNotNull() {
            addCriterion("\"req_doctor\" is not null");
            return (Criteria) this;
        }

        public Criteria andReqDoctorEqualTo(String value) {
            addCriterion("\"req_doctor\" =", value, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andReqDoctorNotEqualTo(String value) {
            addCriterion("\"req_doctor\" <>", value, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andReqDoctorGreaterThan(String value) {
            addCriterion("\"req_doctor\" >", value, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andReqDoctorGreaterThanOrEqualTo(String value) {
            addCriterion("\"req_doctor\" >=", value, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andReqDoctorLessThan(String value) {
            addCriterion("\"req_doctor\" <", value, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andReqDoctorLessThanOrEqualTo(String value) {
            addCriterion("\"req_doctor\" <=", value, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andReqDoctorLike(String value) {
            addCriterion("\"req_doctor\" like", value, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andReqDoctorNotLike(String value) {
            addCriterion("\"req_doctor\" not like", value, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andReqDoctorIn(List<String> values) {
            addCriterion("\"req_doctor\" in", values, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andReqDoctorNotIn(List<String> values) {
            addCriterion("\"req_doctor\" not in", values, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andReqDoctorBetween(String value1, String value2) {
            addCriterion("\"req_doctor\" between", value1, value2, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andReqDoctorNotBetween(String value1, String value2) {
            addCriterion("\"req_doctor\" not between", value1, value2, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andPerformDeptIsNull() {
            addCriterion("\"perform_dept\" is null");
            return (Criteria) this;
        }

        public Criteria andPerformDeptIsNotNull() {
            addCriterion("\"perform_dept\" is not null");
            return (Criteria) this;
        }

        public Criteria andPerformDeptEqualTo(String value) {
            addCriterion("\"perform_dept\" =", value, "performDept");
            return (Criteria) this;
        }

        public Criteria andPerformDeptNotEqualTo(String value) {
            addCriterion("\"perform_dept\" <>", value, "performDept");
            return (Criteria) this;
        }

        public Criteria andPerformDeptGreaterThan(String value) {
            addCriterion("\"perform_dept\" >", value, "performDept");
            return (Criteria) this;
        }

        public Criteria andPerformDeptGreaterThanOrEqualTo(String value) {
            addCriterion("\"perform_dept\" >=", value, "performDept");
            return (Criteria) this;
        }

        public Criteria andPerformDeptLessThan(String value) {
            addCriterion("\"perform_dept\" <", value, "performDept");
            return (Criteria) this;
        }

        public Criteria andPerformDeptLessThanOrEqualTo(String value) {
            addCriterion("\"perform_dept\" <=", value, "performDept");
            return (Criteria) this;
        }

        public Criteria andPerformDeptLike(String value) {
            addCriterion("\"perform_dept\" like", value, "performDept");
            return (Criteria) this;
        }

        public Criteria andPerformDeptNotLike(String value) {
            addCriterion("\"perform_dept\" not like", value, "performDept");
            return (Criteria) this;
        }

        public Criteria andPerformDeptIn(List<String> values) {
            addCriterion("\"perform_dept\" in", values, "performDept");
            return (Criteria) this;
        }

        public Criteria andPerformDeptNotIn(List<String> values) {
            addCriterion("\"perform_dept\" not in", values, "performDept");
            return (Criteria) this;
        }

        public Criteria andPerformDeptBetween(String value1, String value2) {
            addCriterion("\"perform_dept\" between", value1, value2, "performDept");
            return (Criteria) this;
        }

        public Criteria andPerformDeptNotBetween(String value1, String value2) {
            addCriterion("\"perform_dept\" not between", value1, value2, "performDept");
            return (Criteria) this;
        }

        public Criteria andResultStatusIsNull() {
            addCriterion("\"result_status\" is null");
            return (Criteria) this;
        }

        public Criteria andResultStatusIsNotNull() {
            addCriterion("\"result_status\" is not null");
            return (Criteria) this;
        }

        public Criteria andResultStatusEqualTo(String value) {
            addCriterion("\"result_status\" =", value, "resultStatus");
            return (Criteria) this;
        }

        public Criteria andResultStatusNotEqualTo(String value) {
            addCriterion("\"result_status\" <>", value, "resultStatus");
            return (Criteria) this;
        }

        public Criteria andResultStatusGreaterThan(String value) {
            addCriterion("\"result_status\" >", value, "resultStatus");
            return (Criteria) this;
        }

        public Criteria andResultStatusGreaterThanOrEqualTo(String value) {
            addCriterion("\"result_status\" >=", value, "resultStatus");
            return (Criteria) this;
        }

        public Criteria andResultStatusLessThan(String value) {
            addCriterion("\"result_status\" <", value, "resultStatus");
            return (Criteria) this;
        }

        public Criteria andResultStatusLessThanOrEqualTo(String value) {
            addCriterion("\"result_status\" <=", value, "resultStatus");
            return (Criteria) this;
        }

        public Criteria andResultStatusLike(String value) {
            addCriterion("\"result_status\" like", value, "resultStatus");
            return (Criteria) this;
        }

        public Criteria andResultStatusNotLike(String value) {
            addCriterion("\"result_status\" not like", value, "resultStatus");
            return (Criteria) this;
        }

        public Criteria andResultStatusIn(List<String> values) {
            addCriterion("\"result_status\" in", values, "resultStatus");
            return (Criteria) this;
        }

        public Criteria andResultStatusNotIn(List<String> values) {
            addCriterion("\"result_status\" not in", values, "resultStatus");
            return (Criteria) this;
        }

        public Criteria andResultStatusBetween(String value1, String value2) {
            addCriterion("\"result_status\" between", value1, value2, "resultStatus");
            return (Criteria) this;
        }

        public Criteria andResultStatusNotBetween(String value1, String value2) {
            addCriterion("\"result_status\" not between", value1, value2, "resultStatus");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeIsNull() {
            addCriterion("\"exam_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeIsNotNull() {
            addCriterion("\"exam_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeEqualTo(Date value) {
            addCriterion("\"exam_date_time\" =", value, "examDateTime");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeNotEqualTo(Date value) {
            addCriterion("\"exam_date_time\" <>", value, "examDateTime");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeGreaterThan(Date value) {
            addCriterion("\"exam_date_time\" >", value, "examDateTime");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"exam_date_time\" >=", value, "examDateTime");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeLessThan(Date value) {
            addCriterion("\"exam_date_time\" <", value, "examDateTime");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"exam_date_time\" <=", value, "examDateTime");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeIn(List<Date> values) {
            addCriterion("\"exam_date_time\" in", values, "examDateTime");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeNotIn(List<Date> values) {
            addCriterion("\"exam_date_time\" not in", values, "examDateTime");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"exam_date_time\" between", value1, value2, "examDateTime");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"exam_date_time\" not between", value1, value2, "examDateTime");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeIsNull() {
            addCriterion("\"report_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeIsNotNull() {
            addCriterion("\"report_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeEqualTo(Date value) {
            addCriterion("\"report_date_time\" =", value, "reportDateTime");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeNotEqualTo(Date value) {
            addCriterion("\"report_date_time\" <>", value, "reportDateTime");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeGreaterThan(Date value) {
            addCriterion("\"report_date_time\" >", value, "reportDateTime");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"report_date_time\" >=", value, "reportDateTime");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeLessThan(Date value) {
            addCriterion("\"report_date_time\" <", value, "reportDateTime");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"report_date_time\" <=", value, "reportDateTime");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeIn(List<Date> values) {
            addCriterion("\"report_date_time\" in", values, "reportDateTime");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeNotIn(List<Date> values) {
            addCriterion("\"report_date_time\" not in", values, "reportDateTime");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"report_date_time\" between", value1, value2, "reportDateTime");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"report_date_time\" not between", value1, value2, "reportDateTime");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeIsNull() {
            addCriterion("\"review_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeIsNotNull() {
            addCriterion("\"review_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeEqualTo(Date value) {
            addCriterion("\"review_date_time\" =", value, "reviewDateTime");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeNotEqualTo(Date value) {
            addCriterion("\"review_date_time\" <>", value, "reviewDateTime");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeGreaterThan(Date value) {
            addCriterion("\"review_date_time\" >", value, "reviewDateTime");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"review_date_time\" >=", value, "reviewDateTime");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeLessThan(Date value) {
            addCriterion("\"review_date_time\" <", value, "reviewDateTime");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"review_date_time\" <=", value, "reviewDateTime");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeIn(List<Date> values) {
            addCriterion("\"review_date_time\" in", values, "reviewDateTime");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeNotIn(List<Date> values) {
            addCriterion("\"review_date_time\" not in", values, "reviewDateTime");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"review_date_time\" between", value1, value2, "reviewDateTime");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"review_date_time\" not between", value1, value2, "reviewDateTime");
            return (Criteria) this;
        }

        public Criteria andTestDoctorIsNull() {
            addCriterion("\"test_doctor\" is null");
            return (Criteria) this;
        }

        public Criteria andTestDoctorIsNotNull() {
            addCriterion("\"test_doctor\" is not null");
            return (Criteria) this;
        }

        public Criteria andTestDoctorEqualTo(String value) {
            addCriterion("\"test_doctor\" =", value, "testDoctor");
            return (Criteria) this;
        }

        public Criteria andTestDoctorNotEqualTo(String value) {
            addCriterion("\"test_doctor\" <>", value, "testDoctor");
            return (Criteria) this;
        }

        public Criteria andTestDoctorGreaterThan(String value) {
            addCriterion("\"test_doctor\" >", value, "testDoctor");
            return (Criteria) this;
        }

        public Criteria andTestDoctorGreaterThanOrEqualTo(String value) {
            addCriterion("\"test_doctor\" >=", value, "testDoctor");
            return (Criteria) this;
        }

        public Criteria andTestDoctorLessThan(String value) {
            addCriterion("\"test_doctor\" <", value, "testDoctor");
            return (Criteria) this;
        }

        public Criteria andTestDoctorLessThanOrEqualTo(String value) {
            addCriterion("\"test_doctor\" <=", value, "testDoctor");
            return (Criteria) this;
        }

        public Criteria andTestDoctorLike(String value) {
            addCriterion("\"test_doctor\" like", value, "testDoctor");
            return (Criteria) this;
        }

        public Criteria andTestDoctorNotLike(String value) {
            addCriterion("\"test_doctor\" not like", value, "testDoctor");
            return (Criteria) this;
        }

        public Criteria andTestDoctorIn(List<String> values) {
            addCriterion("\"test_doctor\" in", values, "testDoctor");
            return (Criteria) this;
        }

        public Criteria andTestDoctorNotIn(List<String> values) {
            addCriterion("\"test_doctor\" not in", values, "testDoctor");
            return (Criteria) this;
        }

        public Criteria andTestDoctorBetween(String value1, String value2) {
            addCriterion("\"test_doctor\" between", value1, value2, "testDoctor");
            return (Criteria) this;
        }

        public Criteria andTestDoctorNotBetween(String value1, String value2) {
            addCriterion("\"test_doctor\" not between", value1, value2, "testDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorIsNull() {
            addCriterion("\"review_doctor\" is null");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorIsNotNull() {
            addCriterion("\"review_doctor\" is not null");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorEqualTo(String value) {
            addCriterion("\"review_doctor\" =", value, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorNotEqualTo(String value) {
            addCriterion("\"review_doctor\" <>", value, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorGreaterThan(String value) {
            addCriterion("\"review_doctor\" >", value, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorGreaterThanOrEqualTo(String value) {
            addCriterion("\"review_doctor\" >=", value, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorLessThan(String value) {
            addCriterion("\"review_doctor\" <", value, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorLessThanOrEqualTo(String value) {
            addCriterion("\"review_doctor\" <=", value, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorLike(String value) {
            addCriterion("\"review_doctor\" like", value, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorNotLike(String value) {
            addCriterion("\"review_doctor\" not like", value, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorIn(List<String> values) {
            addCriterion("\"review_doctor\" in", values, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorNotIn(List<String> values) {
            addCriterion("\"review_doctor\" not in", values, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorBetween(String value1, String value2) {
            addCriterion("\"review_doctor\" between", value1, value2, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorNotBetween(String value1, String value2) {
            addCriterion("\"review_doctor\" not between", value1, value2, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNull() {
            addCriterion("\"source_path\" is null");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNotNull() {
            addCriterion("\"source_path\" is not null");
            return (Criteria) this;
        }

        public Criteria andSourcePathEqualTo(String value) {
            addCriterion("\"source_path\" =", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotEqualTo(String value) {
            addCriterion("\"source_path\" <>", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThan(String value) {
            addCriterion("\"source_path\" >", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThanOrEqualTo(String value) {
            addCriterion("\"source_path\" >=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThan(String value) {
            addCriterion("\"source_path\" <", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThanOrEqualTo(String value) {
            addCriterion("\"source_path\" <=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLike(String value) {
            addCriterion("\"source_path\" like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotLike(String value) {
            addCriterion("\"source_path\" not like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathIn(List<String> values) {
            addCriterion("\"source_path\" in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotIn(List<String> values) {
            addCriterion("\"source_path\" not in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathBetween(String value1, String value2) {
            addCriterion("\"source_path\" between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotBetween(String value1, String value2) {
            addCriterion("\"source_path\" not between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNull() {
            addCriterion("\"pk_id\" is null");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNotNull() {
            addCriterion("\"pk_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkIdEqualTo(String value) {
            addCriterion("\"pk_id\" =", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotEqualTo(String value) {
            addCriterion("\"pk_id\" <>", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThan(String value) {
            addCriterion("\"pk_id\" >", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" >=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThan(String value) {
            addCriterion("\"pk_id\" <", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" <=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLike(String value) {
            addCriterion("\"pk_id\" like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotLike(String value) {
            addCriterion("\"pk_id\" not like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdIn(List<String> values) {
            addCriterion("\"pk_id\" in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotIn(List<String> values) {
            addCriterion("\"pk_id\" not in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdBetween(String value1, String value2) {
            addCriterion("\"pk_id\" between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotBetween(String value1, String value2) {
            addCriterion("\"pk_id\" not between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNull() {
            addCriterion("\"data_state\" is null");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNotNull() {
            addCriterion("\"data_state\" is not null");
            return (Criteria) this;
        }

        public Criteria andDataStateEqualTo(String value) {
            addCriterion("\"data_state\" =", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotEqualTo(String value) {
            addCriterion("\"data_state\" <>", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThan(String value) {
            addCriterion("\"data_state\" >", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThanOrEqualTo(String value) {
            addCriterion("\"data_state\" >=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThan(String value) {
            addCriterion("\"data_state\" <", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThanOrEqualTo(String value) {
            addCriterion("\"data_state\" <=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLike(String value) {
            addCriterion("\"data_state\" like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotLike(String value) {
            addCriterion("\"data_state\" not like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateIn(List<String> values) {
            addCriterion("\"data_state\" in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotIn(List<String> values) {
            addCriterion("\"data_state\" not in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateBetween(String value1, String value2) {
            addCriterion("\"data_state\" between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotBetween(String value1, String value2) {
            addCriterion("\"data_state\" not between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIsNull() {
            addCriterion("\"patient_sn_org\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIsNotNull() {
            addCriterion("\"patient_sn_org\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgEqualTo(String value) {
            addCriterion("\"patient_sn_org\" =", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotEqualTo(String value) {
            addCriterion("\"patient_sn_org\" <>", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgGreaterThan(String value) {
            addCriterion("\"patient_sn_org\" >", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn_org\" >=", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLessThan(String value) {
            addCriterion("\"patient_sn_org\" <", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn_org\" <=", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLike(String value) {
            addCriterion("\"patient_sn_org\" like", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotLike(String value) {
            addCriterion("\"patient_sn_org\" not like", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIn(List<String> values) {
            addCriterion("\"patient_sn_org\" in", values, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotIn(List<String> values) {
            addCriterion("\"patient_sn_org\" not in", values, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgBetween(String value1, String value2) {
            addCriterion("\"patient_sn_org\" between", value1, value2, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn_org\" not between", value1, value2, "patientSnOrg");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}