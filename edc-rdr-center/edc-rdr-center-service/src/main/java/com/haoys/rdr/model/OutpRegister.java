package com.haoys.rdr.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class OutpRegister implements Serializable {
    @ApiModelProperty(value = "医疗机构代码")
    private String hospitalCode;

    @ApiModelProperty(value = "患者ID")
    private String patientSn;

    @ApiModelProperty(value = "门（急）诊号")
    private String visitSn;

    @ApiModelProperty(value = "门（急）诊号（原始）")
    private Long visitSnOrg;

    @ApiModelProperty(value = "患者姓名")
    private String name;

    @ApiModelProperty(value = "挂号时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date registerTime;

    @ApiModelProperty(value = "挂号类别")
    private String registerType;

    @ApiModelProperty(value = "挂号途径")
    private String registerWay;

    @ApiModelProperty(value = "挂号科室")
    private String registerDept;

    @ApiModelProperty(value = "挂号医师")
    private String registerDoctor;

    @ApiModelProperty(value = "挂号医师级别")
    private String registerDoctorLevel;

    @ApiModelProperty(value = "挂号费")
    private Double registerFee;

    @ApiModelProperty(value = "是否医保")
    private String insuranceFlag;

    @ApiModelProperty(value = "医保类型")
    private String insuranceType;

    @ApiModelProperty(value = "发票号")
    private String receiptId;

    @ApiModelProperty(value = "门急诊标识")
    private String emergencyFlag;

    @ApiModelProperty(value = "退号标识")
    private String refoundFlag;

    @ApiModelProperty(value = "就诊标识")
    private String visitFlag;

    @ApiModelProperty(value = "挂号状态")
    private String registerState;

    @ApiModelProperty(value = "溯源路径")
    private String sourcePath;

    @ApiModelProperty(value = "院内唯一id")
    private String pkId;

    @ApiModelProperty(value = "数据状态")
    private String dataState;

    @ApiModelProperty(value = "原始患者ID")
    private String patientSnOrg;

    private static final long serialVersionUID = 1L;

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }

    public String getVisitSn() {
        return visitSn;
    }

    public void setVisitSn(String visitSn) {
        this.visitSn = visitSn;
    }

    public Long getVisitSnOrg() {
        return visitSnOrg;
    }

    public void setVisitSnOrg(Long visitSnOrg) {
        this.visitSnOrg = visitSnOrg;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(Date registerTime) {
        this.registerTime = registerTime;
    }

    public String getRegisterType() {
        return registerType;
    }

    public void setRegisterType(String registerType) {
        this.registerType = registerType;
    }

    public String getRegisterWay() {
        return registerWay;
    }

    public void setRegisterWay(String registerWay) {
        this.registerWay = registerWay;
    }

    public String getRegisterDept() {
        return registerDept;
    }

    public void setRegisterDept(String registerDept) {
        this.registerDept = registerDept;
    }

    public String getRegisterDoctor() {
        return registerDoctor;
    }

    public void setRegisterDoctor(String registerDoctor) {
        this.registerDoctor = registerDoctor;
    }

    public String getRegisterDoctorLevel() {
        return registerDoctorLevel;
    }

    public void setRegisterDoctorLevel(String registerDoctorLevel) {
        this.registerDoctorLevel = registerDoctorLevel;
    }

    public Double getRegisterFee() {
        return registerFee;
    }

    public void setRegisterFee(Double registerFee) {
        this.registerFee = registerFee;
    }

    public String getInsuranceFlag() {
        return insuranceFlag;
    }

    public void setInsuranceFlag(String insuranceFlag) {
        this.insuranceFlag = insuranceFlag;
    }

    public String getInsuranceType() {
        return insuranceType;
    }

    public void setInsuranceType(String insuranceType) {
        this.insuranceType = insuranceType;
    }

    public String getReceiptId() {
        return receiptId;
    }

    public void setReceiptId(String receiptId) {
        this.receiptId = receiptId;
    }

    public String getEmergencyFlag() {
        return emergencyFlag;
    }

    public void setEmergencyFlag(String emergencyFlag) {
        this.emergencyFlag = emergencyFlag;
    }

    public String getRefoundFlag() {
        return refoundFlag;
    }

    public void setRefoundFlag(String refoundFlag) {
        this.refoundFlag = refoundFlag;
    }

    public String getVisitFlag() {
        return visitFlag;
    }

    public void setVisitFlag(String visitFlag) {
        this.visitFlag = visitFlag;
    }

    public String getRegisterState() {
        return registerState;
    }

    public void setRegisterState(String registerState) {
        this.registerState = registerState;
    }

    public String getSourcePath() {
        return sourcePath;
    }

    public void setSourcePath(String sourcePath) {
        this.sourcePath = sourcePath;
    }

    public String getPkId() {
        return pkId;
    }

    public void setPkId(String pkId) {
        this.pkId = pkId;
    }

    public String getDataState() {
        return dataState;
    }

    public void setDataState(String dataState) {
        this.dataState = dataState;
    }

    public String getPatientSnOrg() {
        return patientSnOrg;
    }

    public void setPatientSnOrg(String patientSnOrg) {
        this.patientSnOrg = patientSnOrg;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", hospitalCode=").append(hospitalCode);
        sb.append(", patientSn=").append(patientSn);
        sb.append(", visitSn=").append(visitSn);
        sb.append(", visitSnOrg=").append(visitSnOrg);
        sb.append(", name=").append(name);
        sb.append(", registerTime=").append(registerTime);
        sb.append(", registerType=").append(registerType);
        sb.append(", registerWay=").append(registerWay);
        sb.append(", registerDept=").append(registerDept);
        sb.append(", registerDoctor=").append(registerDoctor);
        sb.append(", registerDoctorLevel=").append(registerDoctorLevel);
        sb.append(", registerFee=").append(registerFee);
        sb.append(", insuranceFlag=").append(insuranceFlag);
        sb.append(", insuranceType=").append(insuranceType);
        sb.append(", receiptId=").append(receiptId);
        sb.append(", emergencyFlag=").append(emergencyFlag);
        sb.append(", refoundFlag=").append(refoundFlag);
        sb.append(", visitFlag=").append(visitFlag);
        sb.append(", registerState=").append(registerState);
        sb.append(", sourcePath=").append(sourcePath);
        sb.append(", pkId=").append(pkId);
        sb.append(", dataState=").append(dataState);
        sb.append(", patientSnOrg=").append(patientSnOrg);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}