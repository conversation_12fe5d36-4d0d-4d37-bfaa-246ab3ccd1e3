package com.haoys.rdr.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class VisitInformation implements Serializable {
    @ApiModelProperty(value = "平台业务主键ID")
    private String pkId;

    @ApiModelProperty(value = "患者EMPI")
    private String patientSn;

    @ApiModelProperty(value = "原始患者ID")
    private String patientSnOrg;

    @ApiModelProperty(value = "住院号/门诊号,就诊标识")
    private String visitSn;

    @ApiModelProperty(value = "就诊类型")
    private String visitType;

    @ApiModelProperty(value = "就诊/入院日期")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date visitOrAdmissionDatetime;

    @ApiModelProperty(value = "就诊/入院科室")
    private String visitOrAdmissionDept;

    @ApiModelProperty(value = "就诊/主治医师")
    private String visitOrAttendingDoctor;

    @ApiModelProperty(value = "就诊年龄(岁)")
    private Integer visitAge;

    @ApiModelProperty(value = "诊断名称")
    private Object diagnosisName;

    @ApiModelProperty(value = "诊断ICD10编码")
    private Object diagnosisCode;

    @ApiModelProperty(value = "入院途径")
    private String admissionWay;

    @ApiModelProperty(value = "出院日期")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dischargeDatetime;

    @ApiModelProperty(value = "出院科室")
    private String dischargeDept;

    @ApiModelProperty(value = "离院方式")
    private String dischargeWay;

    @ApiModelProperty(value = "住院天数")
    private Integer inDays;

    @ApiModelProperty(value = "住院次数")
    private Integer admissionNumber;

    @ApiModelProperty(value = "原始业务系统ID")
    private String sourceId;

    @ApiModelProperty(value = "溯源路径")
    private String sourcePath;

    @ApiModelProperty(value = "数据状态")
    private String dataState;

    private static final long serialVersionUID = 1L;

    public String getPkId() {
        return pkId;
    }

    public void setPkId(String pkId) {
        this.pkId = pkId;
    }

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }

    public String getPatientSnOrg() {
        return patientSnOrg;
    }

    public void setPatientSnOrg(String patientSnOrg) {
        this.patientSnOrg = patientSnOrg;
    }

    public String getVisitSn() {
        return visitSn;
    }

    public void setVisitSn(String visitSn) {
        this.visitSn = visitSn;
    }

    public String getVisitType() {
        return visitType;
    }

    public void setVisitType(String visitType) {
        this.visitType = visitType;
    }

    public Date getVisitOrAdmissionDatetime() {
        return visitOrAdmissionDatetime;
    }

    public void setVisitOrAdmissionDatetime(Date visitOrAdmissionDatetime) {
        this.visitOrAdmissionDatetime = visitOrAdmissionDatetime;
    }

    public String getVisitOrAdmissionDept() {
        return visitOrAdmissionDept;
    }

    public void setVisitOrAdmissionDept(String visitOrAdmissionDept) {
        this.visitOrAdmissionDept = visitOrAdmissionDept;
    }

    public String getVisitOrAttendingDoctor() {
        return visitOrAttendingDoctor;
    }

    public void setVisitOrAttendingDoctor(String visitOrAttendingDoctor) {
        this.visitOrAttendingDoctor = visitOrAttendingDoctor;
    }

    public Integer getVisitAge() {
        return visitAge;
    }

    public void setVisitAge(Integer visitAge) {
        this.visitAge = visitAge;
    }

    public Object getDiagnosisName() {
        return diagnosisName;
    }

    public void setDiagnosisName(Object diagnosisName) {
        this.diagnosisName = diagnosisName;
    }

    public Object getDiagnosisCode() {
        return diagnosisCode;
    }

    public void setDiagnosisCode(Object diagnosisCode) {
        this.diagnosisCode = diagnosisCode;
    }

    public String getAdmissionWay() {
        return admissionWay;
    }

    public void setAdmissionWay(String admissionWay) {
        this.admissionWay = admissionWay;
    }

    public Date getDischargeDatetime() {
        return dischargeDatetime;
    }

    public void setDischargeDatetime(Date dischargeDatetime) {
        this.dischargeDatetime = dischargeDatetime;
    }

    public String getDischargeDept() {
        return dischargeDept;
    }

    public void setDischargeDept(String dischargeDept) {
        this.dischargeDept = dischargeDept;
    }

    public String getDischargeWay() {
        return dischargeWay;
    }

    public void setDischargeWay(String dischargeWay) {
        this.dischargeWay = dischargeWay;
    }

    public Integer getInDays() {
        return inDays;
    }

    public void setInDays(Integer inDays) {
        this.inDays = inDays;
    }

    public Integer getAdmissionNumber() {
        return admissionNumber;
    }

    public void setAdmissionNumber(Integer admissionNumber) {
        this.admissionNumber = admissionNumber;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getSourcePath() {
        return sourcePath;
    }

    public void setSourcePath(String sourcePath) {
        this.sourcePath = sourcePath;
    }

    public String getDataState() {
        return dataState;
    }

    public void setDataState(String dataState) {
        this.dataState = dataState;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", pkId=").append(pkId);
        sb.append(", patientSn=").append(patientSn);
        sb.append(", patientSnOrg=").append(patientSnOrg);
        sb.append(", visitSn=").append(visitSn);
        sb.append(", visitType=").append(visitType);
        sb.append(", visitOrAdmissionDatetime=").append(visitOrAdmissionDatetime);
        sb.append(", visitOrAdmissionDept=").append(visitOrAdmissionDept);
        sb.append(", visitOrAttendingDoctor=").append(visitOrAttendingDoctor);
        sb.append(", visitAge=").append(visitAge);
        sb.append(", diagnosisName=").append(diagnosisName);
        sb.append(", diagnosisCode=").append(diagnosisCode);
        sb.append(", admissionWay=").append(admissionWay);
        sb.append(", dischargeDatetime=").append(dischargeDatetime);
        sb.append(", dischargeDept=").append(dischargeDept);
        sb.append(", dischargeWay=").append(dischargeWay);
        sb.append(", inDays=").append(inDays);
        sb.append(", admissionNumber=").append(admissionNumber);
        sb.append(", sourceId=").append(sourceId);
        sb.append(", sourcePath=").append(sourcePath);
        sb.append(", dataState=").append(dataState);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}