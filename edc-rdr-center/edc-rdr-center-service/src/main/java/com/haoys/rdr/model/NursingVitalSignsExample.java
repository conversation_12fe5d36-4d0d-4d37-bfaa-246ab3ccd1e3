package com.haoys.rdr.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class NursingVitalSignsExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public NursingVitalSignsExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andHospitalCodeIsNull() {
            addCriterion("\"hospital_code\" is null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIsNotNull() {
            addCriterion("\"hospital_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeEqualTo(String value) {
            addCriterion("\"hospital_code\" =", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotEqualTo(String value) {
            addCriterion("\"hospital_code\" <>", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThan(String value) {
            addCriterion("\"hospital_code\" >", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" >=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThan(String value) {
            addCriterion("\"hospital_code\" <", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" <=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLike(String value) {
            addCriterion("\"hospital_code\" like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotLike(String value) {
            addCriterion("\"hospital_code\" not like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIn(List<String> values) {
            addCriterion("\"hospital_code\" in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotIn(List<String> values) {
            addCriterion("\"hospital_code\" not in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" not between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andTpatnoIsNull() {
            addCriterion("\"tpatno\" is null");
            return (Criteria) this;
        }

        public Criteria andTpatnoIsNotNull() {
            addCriterion("\"tpatno\" is not null");
            return (Criteria) this;
        }

        public Criteria andTpatnoEqualTo(String value) {
            addCriterion("\"tpatno\" =", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotEqualTo(String value) {
            addCriterion("\"tpatno\" <>", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoGreaterThan(String value) {
            addCriterion("\"tpatno\" >", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoGreaterThanOrEqualTo(String value) {
            addCriterion("\"tpatno\" >=", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLessThan(String value) {
            addCriterion("\"tpatno\" <", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLessThanOrEqualTo(String value) {
            addCriterion("\"tpatno\" <=", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLike(String value) {
            addCriterion("\"tpatno\" like", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotLike(String value) {
            addCriterion("\"tpatno\" not like", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoIn(List<String> values) {
            addCriterion("\"tpatno\" in", values, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotIn(List<String> values) {
            addCriterion("\"tpatno\" not in", values, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoBetween(String value1, String value2) {
            addCriterion("\"tpatno\" between", value1, value2, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotBetween(String value1, String value2) {
            addCriterion("\"tpatno\" not between", value1, value2, "tpatno");
            return (Criteria) this;
        }

        public Criteria andItemIdIsNull() {
            addCriterion("\"item_id\" is null");
            return (Criteria) this;
        }

        public Criteria andItemIdIsNotNull() {
            addCriterion("\"item_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andItemIdEqualTo(String value) {
            addCriterion("\"item_id\" =", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotEqualTo(String value) {
            addCriterion("\"item_id\" <>", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdGreaterThan(String value) {
            addCriterion("\"item_id\" >", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"item_id\" >=", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdLessThan(String value) {
            addCriterion("\"item_id\" <", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdLessThanOrEqualTo(String value) {
            addCriterion("\"item_id\" <=", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdLike(String value) {
            addCriterion("\"item_id\" like", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotLike(String value) {
            addCriterion("\"item_id\" not like", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdIn(List<String> values) {
            addCriterion("\"item_id\" in", values, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotIn(List<String> values) {
            addCriterion("\"item_id\" not in", values, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdBetween(String value1, String value2) {
            addCriterion("\"item_id\" between", value1, value2, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotBetween(String value1, String value2) {
            addCriterion("\"item_id\" not between", value1, value2, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemNameIsNull() {
            addCriterion("\"item_name\" is null");
            return (Criteria) this;
        }

        public Criteria andItemNameIsNotNull() {
            addCriterion("\"item_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andItemNameEqualTo(String value) {
            addCriterion("\"item_name\" =", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotEqualTo(String value) {
            addCriterion("\"item_name\" <>", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameGreaterThan(String value) {
            addCriterion("\"item_name\" >", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"item_name\" >=", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLessThan(String value) {
            addCriterion("\"item_name\" <", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLessThanOrEqualTo(String value) {
            addCriterion("\"item_name\" <=", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLike(String value) {
            addCriterion("\"item_name\" like", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotLike(String value) {
            addCriterion("\"item_name\" not like", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameIn(List<String> values) {
            addCriterion("\"item_name\" in", values, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotIn(List<String> values) {
            addCriterion("\"item_name\" not in", values, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameBetween(String value1, String value2) {
            addCriterion("\"item_name\" between", value1, value2, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotBetween(String value1, String value2) {
            addCriterion("\"item_name\" not between", value1, value2, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemCodeIsNull() {
            addCriterion("\"item_code\" is null");
            return (Criteria) this;
        }

        public Criteria andItemCodeIsNotNull() {
            addCriterion("\"item_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andItemCodeEqualTo(String value) {
            addCriterion("\"item_code\" =", value, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeNotEqualTo(String value) {
            addCriterion("\"item_code\" <>", value, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeGreaterThan(String value) {
            addCriterion("\"item_code\" >", value, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"item_code\" >=", value, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeLessThan(String value) {
            addCriterion("\"item_code\" <", value, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeLessThanOrEqualTo(String value) {
            addCriterion("\"item_code\" <=", value, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeLike(String value) {
            addCriterion("\"item_code\" like", value, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeNotLike(String value) {
            addCriterion("\"item_code\" not like", value, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeIn(List<String> values) {
            addCriterion("\"item_code\" in", values, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeNotIn(List<String> values) {
            addCriterion("\"item_code\" not in", values, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeBetween(String value1, String value2) {
            addCriterion("\"item_code\" between", value1, value2, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemCodeNotBetween(String value1, String value2) {
            addCriterion("\"item_code\" not between", value1, value2, "itemCode");
            return (Criteria) this;
        }

        public Criteria andItemValueIsNull() {
            addCriterion("\"item_value\" is null");
            return (Criteria) this;
        }

        public Criteria andItemValueIsNotNull() {
            addCriterion("\"item_value\" is not null");
            return (Criteria) this;
        }

        public Criteria andItemValueEqualTo(String value) {
            addCriterion("\"item_value\" =", value, "itemValue");
            return (Criteria) this;
        }

        public Criteria andItemValueNotEqualTo(String value) {
            addCriterion("\"item_value\" <>", value, "itemValue");
            return (Criteria) this;
        }

        public Criteria andItemValueGreaterThan(String value) {
            addCriterion("\"item_value\" >", value, "itemValue");
            return (Criteria) this;
        }

        public Criteria andItemValueGreaterThanOrEqualTo(String value) {
            addCriterion("\"item_value\" >=", value, "itemValue");
            return (Criteria) this;
        }

        public Criteria andItemValueLessThan(String value) {
            addCriterion("\"item_value\" <", value, "itemValue");
            return (Criteria) this;
        }

        public Criteria andItemValueLessThanOrEqualTo(String value) {
            addCriterion("\"item_value\" <=", value, "itemValue");
            return (Criteria) this;
        }

        public Criteria andItemValueLike(String value) {
            addCriterion("\"item_value\" like", value, "itemValue");
            return (Criteria) this;
        }

        public Criteria andItemValueNotLike(String value) {
            addCriterion("\"item_value\" not like", value, "itemValue");
            return (Criteria) this;
        }

        public Criteria andItemValueIn(List<String> values) {
            addCriterion("\"item_value\" in", values, "itemValue");
            return (Criteria) this;
        }

        public Criteria andItemValueNotIn(List<String> values) {
            addCriterion("\"item_value\" not in", values, "itemValue");
            return (Criteria) this;
        }

        public Criteria andItemValueBetween(String value1, String value2) {
            addCriterion("\"item_value\" between", value1, value2, "itemValue");
            return (Criteria) this;
        }

        public Criteria andItemValueNotBetween(String value1, String value2) {
            addCriterion("\"item_value\" not between", value1, value2, "itemValue");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNull() {
            addCriterion("\"unit_name\" is null");
            return (Criteria) this;
        }

        public Criteria andUnitNameIsNotNull() {
            addCriterion("\"unit_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andUnitNameEqualTo(String value) {
            addCriterion("\"unit_name\" =", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotEqualTo(String value) {
            addCriterion("\"unit_name\" <>", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThan(String value) {
            addCriterion("\"unit_name\" >", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"unit_name\" >=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThan(String value) {
            addCriterion("\"unit_name\" <", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLessThanOrEqualTo(String value) {
            addCriterion("\"unit_name\" <=", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameLike(String value) {
            addCriterion("\"unit_name\" like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotLike(String value) {
            addCriterion("\"unit_name\" not like", value, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameIn(List<String> values) {
            addCriterion("\"unit_name\" in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotIn(List<String> values) {
            addCriterion("\"unit_name\" not in", values, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameBetween(String value1, String value2) {
            addCriterion("\"unit_name\" between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andUnitNameNotBetween(String value1, String value2) {
            addCriterion("\"unit_name\" not between", value1, value2, "unitName");
            return (Criteria) this;
        }

        public Criteria andItemValueNumIsNull() {
            addCriterion("\"item_value_num\" is null");
            return (Criteria) this;
        }

        public Criteria andItemValueNumIsNotNull() {
            addCriterion("\"item_value_num\" is not null");
            return (Criteria) this;
        }

        public Criteria andItemValueNumEqualTo(Double value) {
            addCriterion("\"item_value_num\" =", value, "itemValueNum");
            return (Criteria) this;
        }

        public Criteria andItemValueNumNotEqualTo(Double value) {
            addCriterion("\"item_value_num\" <>", value, "itemValueNum");
            return (Criteria) this;
        }

        public Criteria andItemValueNumGreaterThan(Double value) {
            addCriterion("\"item_value_num\" >", value, "itemValueNum");
            return (Criteria) this;
        }

        public Criteria andItemValueNumGreaterThanOrEqualTo(Double value) {
            addCriterion("\"item_value_num\" >=", value, "itemValueNum");
            return (Criteria) this;
        }

        public Criteria andItemValueNumLessThan(Double value) {
            addCriterion("\"item_value_num\" <", value, "itemValueNum");
            return (Criteria) this;
        }

        public Criteria andItemValueNumLessThanOrEqualTo(Double value) {
            addCriterion("\"item_value_num\" <=", value, "itemValueNum");
            return (Criteria) this;
        }

        public Criteria andItemValueNumIn(List<Double> values) {
            addCriterion("\"item_value_num\" in", values, "itemValueNum");
            return (Criteria) this;
        }

        public Criteria andItemValueNumNotIn(List<Double> values) {
            addCriterion("\"item_value_num\" not in", values, "itemValueNum");
            return (Criteria) this;
        }

        public Criteria andItemValueNumBetween(Double value1, Double value2) {
            addCriterion("\"item_value_num\" between", value1, value2, "itemValueNum");
            return (Criteria) this;
        }

        public Criteria andItemValueNumNotBetween(Double value1, Double value2) {
            addCriterion("\"item_value_num\" not between", value1, value2, "itemValueNum");
            return (Criteria) this;
        }

        public Criteria andItemValueTextIsNull() {
            addCriterion("\"item_value_text\" is null");
            return (Criteria) this;
        }

        public Criteria andItemValueTextIsNotNull() {
            addCriterion("\"item_value_text\" is not null");
            return (Criteria) this;
        }

        public Criteria andItemValueTextEqualTo(String value) {
            addCriterion("\"item_value_text\" =", value, "itemValueText");
            return (Criteria) this;
        }

        public Criteria andItemValueTextNotEqualTo(String value) {
            addCriterion("\"item_value_text\" <>", value, "itemValueText");
            return (Criteria) this;
        }

        public Criteria andItemValueTextGreaterThan(String value) {
            addCriterion("\"item_value_text\" >", value, "itemValueText");
            return (Criteria) this;
        }

        public Criteria andItemValueTextGreaterThanOrEqualTo(String value) {
            addCriterion("\"item_value_text\" >=", value, "itemValueText");
            return (Criteria) this;
        }

        public Criteria andItemValueTextLessThan(String value) {
            addCriterion("\"item_value_text\" <", value, "itemValueText");
            return (Criteria) this;
        }

        public Criteria andItemValueTextLessThanOrEqualTo(String value) {
            addCriterion("\"item_value_text\" <=", value, "itemValueText");
            return (Criteria) this;
        }

        public Criteria andItemValueTextLike(String value) {
            addCriterion("\"item_value_text\" like", value, "itemValueText");
            return (Criteria) this;
        }

        public Criteria andItemValueTextNotLike(String value) {
            addCriterion("\"item_value_text\" not like", value, "itemValueText");
            return (Criteria) this;
        }

        public Criteria andItemValueTextIn(List<String> values) {
            addCriterion("\"item_value_text\" in", values, "itemValueText");
            return (Criteria) this;
        }

        public Criteria andItemValueTextNotIn(List<String> values) {
            addCriterion("\"item_value_text\" not in", values, "itemValueText");
            return (Criteria) this;
        }

        public Criteria andItemValueTextBetween(String value1, String value2) {
            addCriterion("\"item_value_text\" between", value1, value2, "itemValueText");
            return (Criteria) this;
        }

        public Criteria andItemValueTextNotBetween(String value1, String value2) {
            addCriterion("\"item_value_text\" not between", value1, value2, "itemValueText");
            return (Criteria) this;
        }

        public Criteria andRecordTimeIsNull() {
            addCriterion("\"record_time\" is null");
            return (Criteria) this;
        }

        public Criteria andRecordTimeIsNotNull() {
            addCriterion("\"record_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andRecordTimeEqualTo(Date value) {
            addCriterion("\"record_time\" =", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeNotEqualTo(Date value) {
            addCriterion("\"record_time\" <>", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeGreaterThan(Date value) {
            addCriterion("\"record_time\" >", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"record_time\" >=", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeLessThan(Date value) {
            addCriterion("\"record_time\" <", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"record_time\" <=", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeIn(List<Date> values) {
            addCriterion("\"record_time\" in", values, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeNotIn(List<Date> values) {
            addCriterion("\"record_time\" not in", values, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeBetween(Date value1, Date value2) {
            addCriterion("\"record_time\" between", value1, value2, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"record_time\" not between", value1, value2, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecorderIsNull() {
            addCriterion("\"recorder\" is null");
            return (Criteria) this;
        }

        public Criteria andRecorderIsNotNull() {
            addCriterion("\"recorder\" is not null");
            return (Criteria) this;
        }

        public Criteria andRecorderEqualTo(String value) {
            addCriterion("\"recorder\" =", value, "recorder");
            return (Criteria) this;
        }

        public Criteria andRecorderNotEqualTo(String value) {
            addCriterion("\"recorder\" <>", value, "recorder");
            return (Criteria) this;
        }

        public Criteria andRecorderGreaterThan(String value) {
            addCriterion("\"recorder\" >", value, "recorder");
            return (Criteria) this;
        }

        public Criteria andRecorderGreaterThanOrEqualTo(String value) {
            addCriterion("\"recorder\" >=", value, "recorder");
            return (Criteria) this;
        }

        public Criteria andRecorderLessThan(String value) {
            addCriterion("\"recorder\" <", value, "recorder");
            return (Criteria) this;
        }

        public Criteria andRecorderLessThanOrEqualTo(String value) {
            addCriterion("\"recorder\" <=", value, "recorder");
            return (Criteria) this;
        }

        public Criteria andRecorderLike(String value) {
            addCriterion("\"recorder\" like", value, "recorder");
            return (Criteria) this;
        }

        public Criteria andRecorderNotLike(String value) {
            addCriterion("\"recorder\" not like", value, "recorder");
            return (Criteria) this;
        }

        public Criteria andRecorderIn(List<String> values) {
            addCriterion("\"recorder\" in", values, "recorder");
            return (Criteria) this;
        }

        public Criteria andRecorderNotIn(List<String> values) {
            addCriterion("\"recorder\" not in", values, "recorder");
            return (Criteria) this;
        }

        public Criteria andRecorderBetween(String value1, String value2) {
            addCriterion("\"recorder\" between", value1, value2, "recorder");
            return (Criteria) this;
        }

        public Criteria andRecorderNotBetween(String value1, String value2) {
            addCriterion("\"recorder\" not between", value1, value2, "recorder");
            return (Criteria) this;
        }

        public Criteria andCommentIsNull() {
            addCriterion("\"comment\" is null");
            return (Criteria) this;
        }

        public Criteria andCommentIsNotNull() {
            addCriterion("\"comment\" is not null");
            return (Criteria) this;
        }

        public Criteria andCommentEqualTo(String value) {
            addCriterion("\"comment\" =", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotEqualTo(String value) {
            addCriterion("\"comment\" <>", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentGreaterThan(String value) {
            addCriterion("\"comment\" >", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentGreaterThanOrEqualTo(String value) {
            addCriterion("\"comment\" >=", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentLessThan(String value) {
            addCriterion("\"comment\" <", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentLessThanOrEqualTo(String value) {
            addCriterion("\"comment\" <=", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentLike(String value) {
            addCriterion("\"comment\" like", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotLike(String value) {
            addCriterion("\"comment\" not like", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentIn(List<String> values) {
            addCriterion("\"comment\" in", values, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotIn(List<String> values) {
            addCriterion("\"comment\" not in", values, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentBetween(String value1, String value2) {
            addCriterion("\"comment\" between", value1, value2, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotBetween(String value1, String value2) {
            addCriterion("\"comment\" not between", value1, value2, "comment");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeIsNull() {
            addCriterion("\"reference_range\" is null");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeIsNotNull() {
            addCriterion("\"reference_range\" is not null");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeEqualTo(String value) {
            addCriterion("\"reference_range\" =", value, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeNotEqualTo(String value) {
            addCriterion("\"reference_range\" <>", value, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeGreaterThan(String value) {
            addCriterion("\"reference_range\" >", value, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeGreaterThanOrEqualTo(String value) {
            addCriterion("\"reference_range\" >=", value, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeLessThan(String value) {
            addCriterion("\"reference_range\" <", value, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeLessThanOrEqualTo(String value) {
            addCriterion("\"reference_range\" <=", value, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeLike(String value) {
            addCriterion("\"reference_range\" like", value, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeNotLike(String value) {
            addCriterion("\"reference_range\" not like", value, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeIn(List<String> values) {
            addCriterion("\"reference_range\" in", values, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeNotIn(List<String> values) {
            addCriterion("\"reference_range\" not in", values, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeBetween(String value1, String value2) {
            addCriterion("\"reference_range\" between", value1, value2, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andReferenceRangeNotBetween(String value1, String value2) {
            addCriterion("\"reference_range\" not between", value1, value2, "referenceRange");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNull() {
            addCriterion("\"source_path\" is null");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNotNull() {
            addCriterion("\"source_path\" is not null");
            return (Criteria) this;
        }

        public Criteria andSourcePathEqualTo(String value) {
            addCriterion("\"source_path\" =", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotEqualTo(String value) {
            addCriterion("\"source_path\" <>", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThan(String value) {
            addCriterion("\"source_path\" >", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThanOrEqualTo(String value) {
            addCriterion("\"source_path\" >=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThan(String value) {
            addCriterion("\"source_path\" <", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThanOrEqualTo(String value) {
            addCriterion("\"source_path\" <=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLike(String value) {
            addCriterion("\"source_path\" like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotLike(String value) {
            addCriterion("\"source_path\" not like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathIn(List<String> values) {
            addCriterion("\"source_path\" in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotIn(List<String> values) {
            addCriterion("\"source_path\" not in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathBetween(String value1, String value2) {
            addCriterion("\"source_path\" between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotBetween(String value1, String value2) {
            addCriterion("\"source_path\" not between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNull() {
            addCriterion("\"pk_id\" is null");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNotNull() {
            addCriterion("\"pk_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkIdEqualTo(String value) {
            addCriterion("\"pk_id\" =", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotEqualTo(String value) {
            addCriterion("\"pk_id\" <>", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThan(String value) {
            addCriterion("\"pk_id\" >", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" >=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThan(String value) {
            addCriterion("\"pk_id\" <", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" <=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLike(String value) {
            addCriterion("\"pk_id\" like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotLike(String value) {
            addCriterion("\"pk_id\" not like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdIn(List<String> values) {
            addCriterion("\"pk_id\" in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotIn(List<String> values) {
            addCriterion("\"pk_id\" not in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdBetween(String value1, String value2) {
            addCriterion("\"pk_id\" between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotBetween(String value1, String value2) {
            addCriterion("\"pk_id\" not between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNull() {
            addCriterion("\"data_state\" is null");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNotNull() {
            addCriterion("\"data_state\" is not null");
            return (Criteria) this;
        }

        public Criteria andDataStateEqualTo(String value) {
            addCriterion("\"data_state\" =", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotEqualTo(String value) {
            addCriterion("\"data_state\" <>", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThan(String value) {
            addCriterion("\"data_state\" >", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThanOrEqualTo(String value) {
            addCriterion("\"data_state\" >=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThan(String value) {
            addCriterion("\"data_state\" <", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThanOrEqualTo(String value) {
            addCriterion("\"data_state\" <=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLike(String value) {
            addCriterion("\"data_state\" like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotLike(String value) {
            addCriterion("\"data_state\" not like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateIn(List<String> values) {
            addCriterion("\"data_state\" in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotIn(List<String> values) {
            addCriterion("\"data_state\" not in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateBetween(String value1, String value2) {
            addCriterion("\"data_state\" between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotBetween(String value1, String value2) {
            addCriterion("\"data_state\" not between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIsNull() {
            addCriterion("\"patient_sn_org\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIsNotNull() {
            addCriterion("\"patient_sn_org\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgEqualTo(String value) {
            addCriterion("\"patient_sn_org\" =", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotEqualTo(String value) {
            addCriterion("\"patient_sn_org\" <>", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgGreaterThan(String value) {
            addCriterion("\"patient_sn_org\" >", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn_org\" >=", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLessThan(String value) {
            addCriterion("\"patient_sn_org\" <", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn_org\" <=", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLike(String value) {
            addCriterion("\"patient_sn_org\" like", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotLike(String value) {
            addCriterion("\"patient_sn_org\" not like", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIn(List<String> values) {
            addCriterion("\"patient_sn_org\" in", values, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotIn(List<String> values) {
            addCriterion("\"patient_sn_org\" not in", values, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgBetween(String value1, String value2) {
            addCriterion("\"patient_sn_org\" between", value1, value2, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn_org\" not between", value1, value2, "patientSnOrg");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}