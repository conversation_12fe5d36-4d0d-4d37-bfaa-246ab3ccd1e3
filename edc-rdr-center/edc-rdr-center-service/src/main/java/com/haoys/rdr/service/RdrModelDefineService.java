package com.haoys.rdr.service;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.rdr.domain.vo.RdrPatientModelDefineVo;
import com.haoys.rdr.model.RdrPatientModelDefine;
import com.haoys.rdr.model.RdrPatientModelVariable;

import java.util.List;

public interface RdrModelDefineService {

    CustomResult savePatientModelDefineConfig();

    List<RdrPatientModelVariable> getPatientDefaultModelVariableConfig(String modelSourceCode);

    RdrPatientModelDefine getPatientModelDefineByModelSourceCode(String modelSourceCode);

    List<RdrPatientModelDefine> getPatientModelSourceConfigList();

    CommonResult<List<RdrPatientModelDefineVo>> getModelVariableTreeConfig(String showPatients);
    
    CommonResult<List<RdrPatientModelDefineVo>> getModelVariableGroupTreeConfig();

    CustomResult updatePatientModelQueryConfig(Boolean batchUpdate, String modelVariableIds, String queryConfig);

    CustomResult deletePatientModelDefineConfig();

    List<RdrPatientModelDefineVo> getModelVariableTreeConfigList(String showPatients);
    
    /**
     * 查询表单模型列表-住院表单/门诊表单
     * @param type
     * @return
     */
    CommonResult<List<RdrPatientModelDefineVo>> getModelSourceByType(String type);
    
    void saveRdrPatientsAreaInfo(String name, String adcode, String parentCode, String level, int childrenNum, String center, String centroid);
}
