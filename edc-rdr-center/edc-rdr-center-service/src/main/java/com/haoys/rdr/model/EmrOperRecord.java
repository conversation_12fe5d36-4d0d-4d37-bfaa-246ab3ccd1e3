package com.haoys.rdr.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class EmrOperRecord implements Serializable {
    @ApiModelProperty(value = "医疗机构代码")
    private String hospitalCode;

    @ApiModelProperty(value = "患者ID")
    private String patientSn;

    @ApiModelProperty(value = "住院号")
    private String visitSn;

    @ApiModelProperty(value = "病案号")
    private String tpatno;

    @ApiModelProperty(value = "手术登记号")
    private String operationNo;

    @ApiModelProperty(value = "病人姓名")
    private String name;

    @ApiModelProperty(value = "手术名称")
    private String operationName;

    @ApiModelProperty(value = "手术及操作编码")
    private String operationCode;

    @ApiModelProperty(value = "手术日期")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operationDatetime;

    @ApiModelProperty(value = "手术持续时间")
    private String operationDuration;

    @ApiModelProperty(value = "手术开始日期时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operationBeginTime;

    @ApiModelProperty(value = "手术结束日期时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operationEndTime;

    @ApiModelProperty(value = "手术级别代码")
    private String operationLevelCode;

    @ApiModelProperty(value = "手术目标部位名称")
    private String operationSiteName;

    @ApiModelProperty(value = "手术体位名称")
    private String operationPosition;

    @ApiModelProperty(value = "手术史标志")
    private String operationHistory;

    @ApiModelProperty(value = "手术切口描述")
    private String operationIncisionDescription;

    @ApiModelProperty(value = "引流标志")
    private String drainageSign;

    @ApiModelProperty(value = "出血量")
    private String bleedingVolume;

    @ApiModelProperty(value = "输液量")
    private String transfusionVolume;

    @ApiModelProperty(value = "输血量")
    private String bloodTransfusionVolume;

    @ApiModelProperty(value = "术前用药")
    private String drugBeforeOper;

    @ApiModelProperty(value = "术中用药")
    private String drugDuringOper;

    @ApiModelProperty(value = "输血反应标志")
    private String transfusionReactionSigns;

    @ApiModelProperty(value = "手术医生")
    private String surgeonDoctor;

    @ApiModelProperty(value = "I助姓名")
    private String firstAssistant;

    @ApiModelProperty(value = "II助姓名")
    private String secondAssistant;

    @ApiModelProperty(value = "麻醉方式代码")
    private String anesthesiaCode;

    @ApiModelProperty(value = "麻醉方式")
    private String anesthesiaMethod;

    @ApiModelProperty(value = "麻醉医师")
    private String anaesthesiaDoctor;

    @ApiModelProperty(value = "术前诊断")
    private String diagPreoperation;

    @ApiModelProperty(value = "术中诊断")
    private String diagPerioperation;

    @ApiModelProperty(value = "术后诊断")
    private String diagPostoperation;

    @ApiModelProperty(value = "手术简要经过")
    private String surgicalProcess;

    @ApiModelProperty(value = "术后并发症")
    private String postoperationComplite;

    @ApiModelProperty(value = "病灶描述")
    private String focusDescription;

    @ApiModelProperty(value = "记录时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date recordTime;

    @ApiModelProperty(value = "医师签名")
    private String doctorSign;

    @ApiModelProperty(value = "溯源路径")
    private String sourcePath;

    @ApiModelProperty(value = "院内唯一id")
    private String pkId;

    @ApiModelProperty(value = "数据状态")
    private String dataState;

    @ApiModelProperty(value = "病例原文")
    private String fullText;

    @ApiModelProperty(value = "原始患者ID")
    private String patientSnOrg;

    private static final long serialVersionUID = 1L;

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }

    public String getVisitSn() {
        return visitSn;
    }

    public void setVisitSn(String visitSn) {
        this.visitSn = visitSn;
    }

    public String getTpatno() {
        return tpatno;
    }

    public void setTpatno(String tpatno) {
        this.tpatno = tpatno;
    }

    public String getOperationNo() {
        return operationNo;
    }

    public void setOperationNo(String operationNo) {
        this.operationNo = operationNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public String getOperationCode() {
        return operationCode;
    }

    public void setOperationCode(String operationCode) {
        this.operationCode = operationCode;
    }

    public Date getOperationDatetime() {
        return operationDatetime;
    }

    public void setOperationDatetime(Date operationDatetime) {
        this.operationDatetime = operationDatetime;
    }

    public String getOperationDuration() {
        return operationDuration;
    }

    public void setOperationDuration(String operationDuration) {
        this.operationDuration = operationDuration;
    }

    public Date getOperationBeginTime() {
        return operationBeginTime;
    }

    public void setOperationBeginTime(Date operationBeginTime) {
        this.operationBeginTime = operationBeginTime;
    }

    public Date getOperationEndTime() {
        return operationEndTime;
    }

    public void setOperationEndTime(Date operationEndTime) {
        this.operationEndTime = operationEndTime;
    }

    public String getOperationLevelCode() {
        return operationLevelCode;
    }

    public void setOperationLevelCode(String operationLevelCode) {
        this.operationLevelCode = operationLevelCode;
    }

    public String getOperationSiteName() {
        return operationSiteName;
    }

    public void setOperationSiteName(String operationSiteName) {
        this.operationSiteName = operationSiteName;
    }

    public String getOperationPosition() {
        return operationPosition;
    }

    public void setOperationPosition(String operationPosition) {
        this.operationPosition = operationPosition;
    }

    public String getOperationHistory() {
        return operationHistory;
    }

    public void setOperationHistory(String operationHistory) {
        this.operationHistory = operationHistory;
    }

    public String getOperationIncisionDescription() {
        return operationIncisionDescription;
    }

    public void setOperationIncisionDescription(String operationIncisionDescription) {
        this.operationIncisionDescription = operationIncisionDescription;
    }

    public String getDrainageSign() {
        return drainageSign;
    }

    public void setDrainageSign(String drainageSign) {
        this.drainageSign = drainageSign;
    }

    public String getBleedingVolume() {
        return bleedingVolume;
    }

    public void setBleedingVolume(String bleedingVolume) {
        this.bleedingVolume = bleedingVolume;
    }

    public String getTransfusionVolume() {
        return transfusionVolume;
    }

    public void setTransfusionVolume(String transfusionVolume) {
        this.transfusionVolume = transfusionVolume;
    }

    public String getBloodTransfusionVolume() {
        return bloodTransfusionVolume;
    }

    public void setBloodTransfusionVolume(String bloodTransfusionVolume) {
        this.bloodTransfusionVolume = bloodTransfusionVolume;
    }

    public String getDrugBeforeOper() {
        return drugBeforeOper;
    }

    public void setDrugBeforeOper(String drugBeforeOper) {
        this.drugBeforeOper = drugBeforeOper;
    }

    public String getDrugDuringOper() {
        return drugDuringOper;
    }

    public void setDrugDuringOper(String drugDuringOper) {
        this.drugDuringOper = drugDuringOper;
    }

    public String getTransfusionReactionSigns() {
        return transfusionReactionSigns;
    }

    public void setTransfusionReactionSigns(String transfusionReactionSigns) {
        this.transfusionReactionSigns = transfusionReactionSigns;
    }

    public String getSurgeonDoctor() {
        return surgeonDoctor;
    }

    public void setSurgeonDoctor(String surgeonDoctor) {
        this.surgeonDoctor = surgeonDoctor;
    }

    public String getFirstAssistant() {
        return firstAssistant;
    }

    public void setFirstAssistant(String firstAssistant) {
        this.firstAssistant = firstAssistant;
    }

    public String getSecondAssistant() {
        return secondAssistant;
    }

    public void setSecondAssistant(String secondAssistant) {
        this.secondAssistant = secondAssistant;
    }

    public String getAnesthesiaCode() {
        return anesthesiaCode;
    }

    public void setAnesthesiaCode(String anesthesiaCode) {
        this.anesthesiaCode = anesthesiaCode;
    }

    public String getAnesthesiaMethod() {
        return anesthesiaMethod;
    }

    public void setAnesthesiaMethod(String anesthesiaMethod) {
        this.anesthesiaMethod = anesthesiaMethod;
    }

    public String getAnaesthesiaDoctor() {
        return anaesthesiaDoctor;
    }

    public void setAnaesthesiaDoctor(String anaesthesiaDoctor) {
        this.anaesthesiaDoctor = anaesthesiaDoctor;
    }

    public String getDiagPreoperation() {
        return diagPreoperation;
    }

    public void setDiagPreoperation(String diagPreoperation) {
        this.diagPreoperation = diagPreoperation;
    }

    public String getDiagPerioperation() {
        return diagPerioperation;
    }

    public void setDiagPerioperation(String diagPerioperation) {
        this.diagPerioperation = diagPerioperation;
    }

    public String getDiagPostoperation() {
        return diagPostoperation;
    }

    public void setDiagPostoperation(String diagPostoperation) {
        this.diagPostoperation = diagPostoperation;
    }

    public String getSurgicalProcess() {
        return surgicalProcess;
    }

    public void setSurgicalProcess(String surgicalProcess) {
        this.surgicalProcess = surgicalProcess;
    }

    public String getPostoperationComplite() {
        return postoperationComplite;
    }

    public void setPostoperationComplite(String postoperationComplite) {
        this.postoperationComplite = postoperationComplite;
    }

    public String getFocusDescription() {
        return focusDescription;
    }

    public void setFocusDescription(String focusDescription) {
        this.focusDescription = focusDescription;
    }

    public Date getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(Date recordTime) {
        this.recordTime = recordTime;
    }

    public String getDoctorSign() {
        return doctorSign;
    }

    public void setDoctorSign(String doctorSign) {
        this.doctorSign = doctorSign;
    }

    public String getSourcePath() {
        return sourcePath;
    }

    public void setSourcePath(String sourcePath) {
        this.sourcePath = sourcePath;
    }

    public String getPkId() {
        return pkId;
    }

    public void setPkId(String pkId) {
        this.pkId = pkId;
    }

    public String getDataState() {
        return dataState;
    }

    public void setDataState(String dataState) {
        this.dataState = dataState;
    }

    public String getFullText() {
        return fullText;
    }

    public void setFullText(String fullText) {
        this.fullText = fullText;
    }

    public String getPatientSnOrg() {
        return patientSnOrg;
    }

    public void setPatientSnOrg(String patientSnOrg) {
        this.patientSnOrg = patientSnOrg;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", hospitalCode=").append(hospitalCode);
        sb.append(", patientSn=").append(patientSn);
        sb.append(", visitSn=").append(visitSn);
        sb.append(", tpatno=").append(tpatno);
        sb.append(", operationNo=").append(operationNo);
        sb.append(", name=").append(name);
        sb.append(", operationName=").append(operationName);
        sb.append(", operationCode=").append(operationCode);
        sb.append(", operationDatetime=").append(operationDatetime);
        sb.append(", operationDuration=").append(operationDuration);
        sb.append(", operationBeginTime=").append(operationBeginTime);
        sb.append(", operationEndTime=").append(operationEndTime);
        sb.append(", operationLevelCode=").append(operationLevelCode);
        sb.append(", operationSiteName=").append(operationSiteName);
        sb.append(", operationPosition=").append(operationPosition);
        sb.append(", operationHistory=").append(operationHistory);
        sb.append(", operationIncisionDescription=").append(operationIncisionDescription);
        sb.append(", drainageSign=").append(drainageSign);
        sb.append(", bleedingVolume=").append(bleedingVolume);
        sb.append(", transfusionVolume=").append(transfusionVolume);
        sb.append(", bloodTransfusionVolume=").append(bloodTransfusionVolume);
        sb.append(", drugBeforeOper=").append(drugBeforeOper);
        sb.append(", drugDuringOper=").append(drugDuringOper);
        sb.append(", transfusionReactionSigns=").append(transfusionReactionSigns);
        sb.append(", surgeonDoctor=").append(surgeonDoctor);
        sb.append(", firstAssistant=").append(firstAssistant);
        sb.append(", secondAssistant=").append(secondAssistant);
        sb.append(", anesthesiaCode=").append(anesthesiaCode);
        sb.append(", anesthesiaMethod=").append(anesthesiaMethod);
        sb.append(", anaesthesiaDoctor=").append(anaesthesiaDoctor);
        sb.append(", diagPreoperation=").append(diagPreoperation);
        sb.append(", diagPerioperation=").append(diagPerioperation);
        sb.append(", diagPostoperation=").append(diagPostoperation);
        sb.append(", surgicalProcess=").append(surgicalProcess);
        sb.append(", postoperationComplite=").append(postoperationComplite);
        sb.append(", focusDescription=").append(focusDescription);
        sb.append(", recordTime=").append(recordTime);
        sb.append(", doctorSign=").append(doctorSign);
        sb.append(", sourcePath=").append(sourcePath);
        sb.append(", pkId=").append(pkId);
        sb.append(", dataState=").append(dataState);
        sb.append(", fullText=").append(fullText);
        sb.append(", patientSnOrg=").append(patientSnOrg);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}