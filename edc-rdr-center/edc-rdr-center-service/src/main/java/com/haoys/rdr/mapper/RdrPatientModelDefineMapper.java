package com.haoys.rdr.mapper;

import com.haoys.rdr.domain.scheme.RdrTableModelVo;
import com.haoys.rdr.domain.vo.RdrPatientModelDefineGroup;
import com.haoys.rdr.domain.vo.RdrPatientModelDefineVo;
import com.haoys.rdr.model.RdrPatientModelDefine;
import com.haoys.rdr.model.RdrPatientModelDefineExample;
import com.haoys.rdr.model.RdrPatientModelVariable;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RdrPatientModelDefineMapper {
    long countByExample(RdrPatientModelDefineExample example);

    int deleteByExample(RdrPatientModelDefineExample example);

    int deleteByPrimaryKey(String modelSourceId);

    int insert(RdrPatientModelDefine record);

    int insertSelective(RdrPatientModelDefine record);

    List<RdrPatientModelDefine> selectByExample(RdrPatientModelDefineExample example);

    RdrPatientModelDefine selectByPrimaryKey(String modelSourceId);

    int updateByExampleSelective(@Param("record") RdrPatientModelDefine record, @Param("example") RdrPatientModelDefineExample example);

    int updateByExample(@Param("record") RdrPatientModelDefine record, @Param("example") RdrPatientModelDefineExample example);

    int updateByPrimaryKeySelective(RdrPatientModelDefine record);

    int updateByPrimaryKey(RdrPatientModelDefine record);

    /**
     * 查询指定数据库的表信息
     * @return
     */
    List<RdrTableModelVo> getRdrTableModelConfigList();

    /**
     * 查询表字段信息
     * @param tableName
     * @return
     */
    List<RdrTableModelVo> getTableModelColumnBySourceModelCode(String tableName);

    /**
     * 查询指定表单模型字段列表
     * @param modelSourceCode
     * @return
     */
    List<RdrPatientModelVariable> getPatientModelConfigByModelCode(String modelSourceCode);

    /**
     * 根据模型来源代码查询模型定义
     * @param modelSourceCode
     * @return
     */
    RdrPatientModelDefine getPatientModelDefineByModelSourceCode(String modelSourceCode);

    /**
     * 查询指定表单模型字段映射规则列表
     * @param tableName
     * @return
     */
    List<RdrTableModelVo> getTableModelColumnOrdinalPositionBySourceModelCode(String tableName);

    List<RdrPatientModelVariable> getPatientDefaultModelConfigList();

    List<RdrPatientModelDefine> getPatientModelSourceConfigList();

    List<RdrPatientModelDefineVo> getSourceModelVariableTreeConfigListForRdr(String showPatients);

    void updatePatientModelVariable(List<RdrPatientModelVariable> patientModelVariableList);
    
    void cleanPatientModelDefineConfig(String patientModelDefineValue);
    
    String getRdrTableSourceModel(String tableName);
    
    List<RdrPatientModelDefineVo> getModelSourceCodeListByModelSourceCode(String modelSourceCode);
    
    List<RdrPatientModelDefineGroup> getModelSourceCodeListByOwnerGroup(String groupCode);
}