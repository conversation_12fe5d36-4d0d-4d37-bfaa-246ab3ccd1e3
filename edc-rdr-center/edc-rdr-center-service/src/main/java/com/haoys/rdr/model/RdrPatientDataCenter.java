package com.haoys.rdr.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class RdrPatientDataCenter implements Serializable {
    private String datasetCode;

    private String datasetName;

    private String year;

    private String department;

    private String visitType;

    private String datasetResult;

    private Integer sort;

    private String expand;

    private Date createTime;

    private static final long serialVersionUID = 1L;

    public String getDatasetCode() {
        return datasetCode;
    }

    public void setDatasetCode(String datasetCode) {
        this.datasetCode = datasetCode;
    }

    public String getDatasetName() {
        return datasetName;
    }

    public void setDatasetName(String datasetName) {
        this.datasetName = datasetName;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getVisitType() {
        return visitType;
    }

    public void setVisitType(String visitType) {
        this.visitType = visitType;
    }

    public String getDatasetResult() {
        return datasetResult;
    }

    public void setDatasetResult(String datasetResult) {
        this.datasetResult = datasetResult;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getExpand() {
        return expand;
    }

    public void setExpand(String expand) {
        this.expand = expand;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", datasetCode=").append(datasetCode);
        sb.append(", datasetName=").append(datasetName);
        sb.append(", year=").append(year);
        sb.append(", department=").append(department);
        sb.append(", visitType=").append(visitType);
        sb.append(", datasetResult=").append(datasetResult);
        sb.append(", sort=").append(sort);
        sb.append(", expand=").append(expand);
        sb.append(", createTime=").append(createTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}