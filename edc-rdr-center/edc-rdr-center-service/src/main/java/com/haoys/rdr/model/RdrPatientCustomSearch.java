package com.haoys.rdr.model;

import com.haoys.rdr.domain.dto.CustomSearchDto;
import com.haoys.rdr.domain.dto.CustomSearchHeadDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class RdrPatientCustomSearch implements Serializable {
    @ApiModelProperty(value = "主键id")
    private String id;

    @ApiModelProperty(value = "规则名称")
    private String ruleName;

    @ApiModelProperty(value = "规则描述")
    private String ruleDesc;

    @ApiModelProperty(value = "搜搜条件")
    private String search;

    @ApiModelProperty(value = "表格表头")
    private String title;

    @ApiModelProperty(value = "创建人")
    private String createUserId;

    @ApiModelProperty(value = "创建时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "病种")
    private String diseaseType;

    @ApiModelProperty(value = "数据库id")
    private String dataBaseId;

    @ApiModelProperty(value = "排序信息")
    private String sortBy;

    @ApiModelProperty(value = "搜索条件")
    List<CustomSearchDto> searchList;

    @ApiModelProperty(value = "表头")
    List<CustomSearchHeadDto> headList;

}
