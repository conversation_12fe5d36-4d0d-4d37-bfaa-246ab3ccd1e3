package com.haoys.rdr.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class EmrOperRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public EmrOperRecordExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andHospitalCodeIsNull() {
            addCriterion("\"hospital_code\" is null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIsNotNull() {
            addCriterion("\"hospital_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeEqualTo(String value) {
            addCriterion("\"hospital_code\" =", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotEqualTo(String value) {
            addCriterion("\"hospital_code\" <>", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThan(String value) {
            addCriterion("\"hospital_code\" >", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" >=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThan(String value) {
            addCriterion("\"hospital_code\" <", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" <=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLike(String value) {
            addCriterion("\"hospital_code\" like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotLike(String value) {
            addCriterion("\"hospital_code\" not like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIn(List<String> values) {
            addCriterion("\"hospital_code\" in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotIn(List<String> values) {
            addCriterion("\"hospital_code\" not in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" not between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andTpatnoIsNull() {
            addCriterion("\"tpatno\" is null");
            return (Criteria) this;
        }

        public Criteria andTpatnoIsNotNull() {
            addCriterion("\"tpatno\" is not null");
            return (Criteria) this;
        }

        public Criteria andTpatnoEqualTo(String value) {
            addCriterion("\"tpatno\" =", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotEqualTo(String value) {
            addCriterion("\"tpatno\" <>", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoGreaterThan(String value) {
            addCriterion("\"tpatno\" >", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoGreaterThanOrEqualTo(String value) {
            addCriterion("\"tpatno\" >=", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLessThan(String value) {
            addCriterion("\"tpatno\" <", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLessThanOrEqualTo(String value) {
            addCriterion("\"tpatno\" <=", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLike(String value) {
            addCriterion("\"tpatno\" like", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotLike(String value) {
            addCriterion("\"tpatno\" not like", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoIn(List<String> values) {
            addCriterion("\"tpatno\" in", values, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotIn(List<String> values) {
            addCriterion("\"tpatno\" not in", values, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoBetween(String value1, String value2) {
            addCriterion("\"tpatno\" between", value1, value2, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotBetween(String value1, String value2) {
            addCriterion("\"tpatno\" not between", value1, value2, "tpatno");
            return (Criteria) this;
        }

        public Criteria andOperationNoIsNull() {
            addCriterion("\"operation_no\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationNoIsNotNull() {
            addCriterion("\"operation_no\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationNoEqualTo(String value) {
            addCriterion("\"operation_no\" =", value, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationNoNotEqualTo(String value) {
            addCriterion("\"operation_no\" <>", value, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationNoGreaterThan(String value) {
            addCriterion("\"operation_no\" >", value, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationNoGreaterThanOrEqualTo(String value) {
            addCriterion("\"operation_no\" >=", value, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationNoLessThan(String value) {
            addCriterion("\"operation_no\" <", value, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationNoLessThanOrEqualTo(String value) {
            addCriterion("\"operation_no\" <=", value, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationNoLike(String value) {
            addCriterion("\"operation_no\" like", value, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationNoNotLike(String value) {
            addCriterion("\"operation_no\" not like", value, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationNoIn(List<String> values) {
            addCriterion("\"operation_no\" in", values, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationNoNotIn(List<String> values) {
            addCriterion("\"operation_no\" not in", values, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationNoBetween(String value1, String value2) {
            addCriterion("\"operation_no\" between", value1, value2, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationNoNotBetween(String value1, String value2) {
            addCriterion("\"operation_no\" not between", value1, value2, "operationNo");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("\"name\" is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("\"name\" is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("\"name\" =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("\"name\" <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("\"name\" >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"name\" >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("\"name\" <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("\"name\" <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("\"name\" like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("\"name\" not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("\"name\" in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("\"name\" not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("\"name\" between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("\"name\" not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andOperationNameIsNull() {
            addCriterion("\"operation_name\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationNameIsNotNull() {
            addCriterion("\"operation_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationNameEqualTo(String value) {
            addCriterion("\"operation_name\" =", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameNotEqualTo(String value) {
            addCriterion("\"operation_name\" <>", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameGreaterThan(String value) {
            addCriterion("\"operation_name\" >", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"operation_name\" >=", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameLessThan(String value) {
            addCriterion("\"operation_name\" <", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameLessThanOrEqualTo(String value) {
            addCriterion("\"operation_name\" <=", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameLike(String value) {
            addCriterion("\"operation_name\" like", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameNotLike(String value) {
            addCriterion("\"operation_name\" not like", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameIn(List<String> values) {
            addCriterion("\"operation_name\" in", values, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameNotIn(List<String> values) {
            addCriterion("\"operation_name\" not in", values, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameBetween(String value1, String value2) {
            addCriterion("\"operation_name\" between", value1, value2, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameNotBetween(String value1, String value2) {
            addCriterion("\"operation_name\" not between", value1, value2, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationCodeIsNull() {
            addCriterion("\"operation_code\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationCodeIsNotNull() {
            addCriterion("\"operation_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationCodeEqualTo(String value) {
            addCriterion("\"operation_code\" =", value, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationCodeNotEqualTo(String value) {
            addCriterion("\"operation_code\" <>", value, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationCodeGreaterThan(String value) {
            addCriterion("\"operation_code\" >", value, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"operation_code\" >=", value, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationCodeLessThan(String value) {
            addCriterion("\"operation_code\" <", value, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationCodeLessThanOrEqualTo(String value) {
            addCriterion("\"operation_code\" <=", value, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationCodeLike(String value) {
            addCriterion("\"operation_code\" like", value, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationCodeNotLike(String value) {
            addCriterion("\"operation_code\" not like", value, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationCodeIn(List<String> values) {
            addCriterion("\"operation_code\" in", values, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationCodeNotIn(List<String> values) {
            addCriterion("\"operation_code\" not in", values, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationCodeBetween(String value1, String value2) {
            addCriterion("\"operation_code\" between", value1, value2, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationCodeNotBetween(String value1, String value2) {
            addCriterion("\"operation_code\" not between", value1, value2, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeIsNull() {
            addCriterion("\"operation_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeIsNotNull() {
            addCriterion("\"operation_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeEqualTo(Date value) {
            addCriterion("\"operation_datetime\" =", value, "operationDatetime");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeNotEqualTo(Date value) {
            addCriterion("\"operation_datetime\" <>", value, "operationDatetime");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeGreaterThan(Date value) {
            addCriterion("\"operation_datetime\" >", value, "operationDatetime");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"operation_datetime\" >=", value, "operationDatetime");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeLessThan(Date value) {
            addCriterion("\"operation_datetime\" <", value, "operationDatetime");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"operation_datetime\" <=", value, "operationDatetime");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeIn(List<Date> values) {
            addCriterion("\"operation_datetime\" in", values, "operationDatetime");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeNotIn(List<Date> values) {
            addCriterion("\"operation_datetime\" not in", values, "operationDatetime");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"operation_datetime\" between", value1, value2, "operationDatetime");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"operation_datetime\" not between", value1, value2, "operationDatetime");
            return (Criteria) this;
        }

        public Criteria andOperationDurationIsNull() {
            addCriterion("\"operation_duration\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationDurationIsNotNull() {
            addCriterion("\"operation_duration\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationDurationEqualTo(String value) {
            addCriterion("\"operation_duration\" =", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationNotEqualTo(String value) {
            addCriterion("\"operation_duration\" <>", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationGreaterThan(String value) {
            addCriterion("\"operation_duration\" >", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationGreaterThanOrEqualTo(String value) {
            addCriterion("\"operation_duration\" >=", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationLessThan(String value) {
            addCriterion("\"operation_duration\" <", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationLessThanOrEqualTo(String value) {
            addCriterion("\"operation_duration\" <=", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationLike(String value) {
            addCriterion("\"operation_duration\" like", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationNotLike(String value) {
            addCriterion("\"operation_duration\" not like", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationIn(List<String> values) {
            addCriterion("\"operation_duration\" in", values, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationNotIn(List<String> values) {
            addCriterion("\"operation_duration\" not in", values, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationBetween(String value1, String value2) {
            addCriterion("\"operation_duration\" between", value1, value2, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationNotBetween(String value1, String value2) {
            addCriterion("\"operation_duration\" not between", value1, value2, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeIsNull() {
            addCriterion("\"operation_begin_time\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeIsNotNull() {
            addCriterion("\"operation_begin_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeEqualTo(Date value) {
            addCriterion("\"operation_begin_time\" =", value, "operationBeginTime");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeNotEqualTo(Date value) {
            addCriterion("\"operation_begin_time\" <>", value, "operationBeginTime");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeGreaterThan(Date value) {
            addCriterion("\"operation_begin_time\" >", value, "operationBeginTime");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"operation_begin_time\" >=", value, "operationBeginTime");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeLessThan(Date value) {
            addCriterion("\"operation_begin_time\" <", value, "operationBeginTime");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"operation_begin_time\" <=", value, "operationBeginTime");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeIn(List<Date> values) {
            addCriterion("\"operation_begin_time\" in", values, "operationBeginTime");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeNotIn(List<Date> values) {
            addCriterion("\"operation_begin_time\" not in", values, "operationBeginTime");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeBetween(Date value1, Date value2) {
            addCriterion("\"operation_begin_time\" between", value1, value2, "operationBeginTime");
            return (Criteria) this;
        }

        public Criteria andOperationBeginTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"operation_begin_time\" not between", value1, value2, "operationBeginTime");
            return (Criteria) this;
        }

        public Criteria andOperationEndTimeIsNull() {
            addCriterion("\"operation_end_time\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationEndTimeIsNotNull() {
            addCriterion("\"operation_end_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationEndTimeEqualTo(Date value) {
            addCriterion("\"operation_end_time\" =", value, "operationEndTime");
            return (Criteria) this;
        }

        public Criteria andOperationEndTimeNotEqualTo(Date value) {
            addCriterion("\"operation_end_time\" <>", value, "operationEndTime");
            return (Criteria) this;
        }

        public Criteria andOperationEndTimeGreaterThan(Date value) {
            addCriterion("\"operation_end_time\" >", value, "operationEndTime");
            return (Criteria) this;
        }

        public Criteria andOperationEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"operation_end_time\" >=", value, "operationEndTime");
            return (Criteria) this;
        }

        public Criteria andOperationEndTimeLessThan(Date value) {
            addCriterion("\"operation_end_time\" <", value, "operationEndTime");
            return (Criteria) this;
        }

        public Criteria andOperationEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"operation_end_time\" <=", value, "operationEndTime");
            return (Criteria) this;
        }

        public Criteria andOperationEndTimeIn(List<Date> values) {
            addCriterion("\"operation_end_time\" in", values, "operationEndTime");
            return (Criteria) this;
        }

        public Criteria andOperationEndTimeNotIn(List<Date> values) {
            addCriterion("\"operation_end_time\" not in", values, "operationEndTime");
            return (Criteria) this;
        }

        public Criteria andOperationEndTimeBetween(Date value1, Date value2) {
            addCriterion("\"operation_end_time\" between", value1, value2, "operationEndTime");
            return (Criteria) this;
        }

        public Criteria andOperationEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"operation_end_time\" not between", value1, value2, "operationEndTime");
            return (Criteria) this;
        }

        public Criteria andOperationLevelCodeIsNull() {
            addCriterion("\"operation_level_code\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationLevelCodeIsNotNull() {
            addCriterion("\"operation_level_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationLevelCodeEqualTo(String value) {
            addCriterion("\"operation_level_code\" =", value, "operationLevelCode");
            return (Criteria) this;
        }

        public Criteria andOperationLevelCodeNotEqualTo(String value) {
            addCriterion("\"operation_level_code\" <>", value, "operationLevelCode");
            return (Criteria) this;
        }

        public Criteria andOperationLevelCodeGreaterThan(String value) {
            addCriterion("\"operation_level_code\" >", value, "operationLevelCode");
            return (Criteria) this;
        }

        public Criteria andOperationLevelCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"operation_level_code\" >=", value, "operationLevelCode");
            return (Criteria) this;
        }

        public Criteria andOperationLevelCodeLessThan(String value) {
            addCriterion("\"operation_level_code\" <", value, "operationLevelCode");
            return (Criteria) this;
        }

        public Criteria andOperationLevelCodeLessThanOrEqualTo(String value) {
            addCriterion("\"operation_level_code\" <=", value, "operationLevelCode");
            return (Criteria) this;
        }

        public Criteria andOperationLevelCodeLike(String value) {
            addCriterion("\"operation_level_code\" like", value, "operationLevelCode");
            return (Criteria) this;
        }

        public Criteria andOperationLevelCodeNotLike(String value) {
            addCriterion("\"operation_level_code\" not like", value, "operationLevelCode");
            return (Criteria) this;
        }

        public Criteria andOperationLevelCodeIn(List<String> values) {
            addCriterion("\"operation_level_code\" in", values, "operationLevelCode");
            return (Criteria) this;
        }

        public Criteria andOperationLevelCodeNotIn(List<String> values) {
            addCriterion("\"operation_level_code\" not in", values, "operationLevelCode");
            return (Criteria) this;
        }

        public Criteria andOperationLevelCodeBetween(String value1, String value2) {
            addCriterion("\"operation_level_code\" between", value1, value2, "operationLevelCode");
            return (Criteria) this;
        }

        public Criteria andOperationLevelCodeNotBetween(String value1, String value2) {
            addCriterion("\"operation_level_code\" not between", value1, value2, "operationLevelCode");
            return (Criteria) this;
        }

        public Criteria andOperationSiteNameIsNull() {
            addCriterion("\"operation_site_name\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationSiteNameIsNotNull() {
            addCriterion("\"operation_site_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationSiteNameEqualTo(String value) {
            addCriterion("\"operation_site_name\" =", value, "operationSiteName");
            return (Criteria) this;
        }

        public Criteria andOperationSiteNameNotEqualTo(String value) {
            addCriterion("\"operation_site_name\" <>", value, "operationSiteName");
            return (Criteria) this;
        }

        public Criteria andOperationSiteNameGreaterThan(String value) {
            addCriterion("\"operation_site_name\" >", value, "operationSiteName");
            return (Criteria) this;
        }

        public Criteria andOperationSiteNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"operation_site_name\" >=", value, "operationSiteName");
            return (Criteria) this;
        }

        public Criteria andOperationSiteNameLessThan(String value) {
            addCriterion("\"operation_site_name\" <", value, "operationSiteName");
            return (Criteria) this;
        }

        public Criteria andOperationSiteNameLessThanOrEqualTo(String value) {
            addCriterion("\"operation_site_name\" <=", value, "operationSiteName");
            return (Criteria) this;
        }

        public Criteria andOperationSiteNameLike(String value) {
            addCriterion("\"operation_site_name\" like", value, "operationSiteName");
            return (Criteria) this;
        }

        public Criteria andOperationSiteNameNotLike(String value) {
            addCriterion("\"operation_site_name\" not like", value, "operationSiteName");
            return (Criteria) this;
        }

        public Criteria andOperationSiteNameIn(List<String> values) {
            addCriterion("\"operation_site_name\" in", values, "operationSiteName");
            return (Criteria) this;
        }

        public Criteria andOperationSiteNameNotIn(List<String> values) {
            addCriterion("\"operation_site_name\" not in", values, "operationSiteName");
            return (Criteria) this;
        }

        public Criteria andOperationSiteNameBetween(String value1, String value2) {
            addCriterion("\"operation_site_name\" between", value1, value2, "operationSiteName");
            return (Criteria) this;
        }

        public Criteria andOperationSiteNameNotBetween(String value1, String value2) {
            addCriterion("\"operation_site_name\" not between", value1, value2, "operationSiteName");
            return (Criteria) this;
        }

        public Criteria andOperationPositionIsNull() {
            addCriterion("\"operation_position\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationPositionIsNotNull() {
            addCriterion("\"operation_position\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationPositionEqualTo(String value) {
            addCriterion("\"operation_position\" =", value, "operationPosition");
            return (Criteria) this;
        }

        public Criteria andOperationPositionNotEqualTo(String value) {
            addCriterion("\"operation_position\" <>", value, "operationPosition");
            return (Criteria) this;
        }

        public Criteria andOperationPositionGreaterThan(String value) {
            addCriterion("\"operation_position\" >", value, "operationPosition");
            return (Criteria) this;
        }

        public Criteria andOperationPositionGreaterThanOrEqualTo(String value) {
            addCriterion("\"operation_position\" >=", value, "operationPosition");
            return (Criteria) this;
        }

        public Criteria andOperationPositionLessThan(String value) {
            addCriterion("\"operation_position\" <", value, "operationPosition");
            return (Criteria) this;
        }

        public Criteria andOperationPositionLessThanOrEqualTo(String value) {
            addCriterion("\"operation_position\" <=", value, "operationPosition");
            return (Criteria) this;
        }

        public Criteria andOperationPositionLike(String value) {
            addCriterion("\"operation_position\" like", value, "operationPosition");
            return (Criteria) this;
        }

        public Criteria andOperationPositionNotLike(String value) {
            addCriterion("\"operation_position\" not like", value, "operationPosition");
            return (Criteria) this;
        }

        public Criteria andOperationPositionIn(List<String> values) {
            addCriterion("\"operation_position\" in", values, "operationPosition");
            return (Criteria) this;
        }

        public Criteria andOperationPositionNotIn(List<String> values) {
            addCriterion("\"operation_position\" not in", values, "operationPosition");
            return (Criteria) this;
        }

        public Criteria andOperationPositionBetween(String value1, String value2) {
            addCriterion("\"operation_position\" between", value1, value2, "operationPosition");
            return (Criteria) this;
        }

        public Criteria andOperationPositionNotBetween(String value1, String value2) {
            addCriterion("\"operation_position\" not between", value1, value2, "operationPosition");
            return (Criteria) this;
        }

        public Criteria andOperationHistoryIsNull() {
            addCriterion("\"operation_history\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationHistoryIsNotNull() {
            addCriterion("\"operation_history\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationHistoryEqualTo(String value) {
            addCriterion("\"operation_history\" =", value, "operationHistory");
            return (Criteria) this;
        }

        public Criteria andOperationHistoryNotEqualTo(String value) {
            addCriterion("\"operation_history\" <>", value, "operationHistory");
            return (Criteria) this;
        }

        public Criteria andOperationHistoryGreaterThan(String value) {
            addCriterion("\"operation_history\" >", value, "operationHistory");
            return (Criteria) this;
        }

        public Criteria andOperationHistoryGreaterThanOrEqualTo(String value) {
            addCriterion("\"operation_history\" >=", value, "operationHistory");
            return (Criteria) this;
        }

        public Criteria andOperationHistoryLessThan(String value) {
            addCriterion("\"operation_history\" <", value, "operationHistory");
            return (Criteria) this;
        }

        public Criteria andOperationHistoryLessThanOrEqualTo(String value) {
            addCriterion("\"operation_history\" <=", value, "operationHistory");
            return (Criteria) this;
        }

        public Criteria andOperationHistoryLike(String value) {
            addCriterion("\"operation_history\" like", value, "operationHistory");
            return (Criteria) this;
        }

        public Criteria andOperationHistoryNotLike(String value) {
            addCriterion("\"operation_history\" not like", value, "operationHistory");
            return (Criteria) this;
        }

        public Criteria andOperationHistoryIn(List<String> values) {
            addCriterion("\"operation_history\" in", values, "operationHistory");
            return (Criteria) this;
        }

        public Criteria andOperationHistoryNotIn(List<String> values) {
            addCriterion("\"operation_history\" not in", values, "operationHistory");
            return (Criteria) this;
        }

        public Criteria andOperationHistoryBetween(String value1, String value2) {
            addCriterion("\"operation_history\" between", value1, value2, "operationHistory");
            return (Criteria) this;
        }

        public Criteria andOperationHistoryNotBetween(String value1, String value2) {
            addCriterion("\"operation_history\" not between", value1, value2, "operationHistory");
            return (Criteria) this;
        }

        public Criteria andOperationIncisionDescriptionIsNull() {
            addCriterion("\"operation_incision_description\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationIncisionDescriptionIsNotNull() {
            addCriterion("\"operation_incision_description\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationIncisionDescriptionEqualTo(String value) {
            addCriterion("\"operation_incision_description\" =", value, "operationIncisionDescription");
            return (Criteria) this;
        }

        public Criteria andOperationIncisionDescriptionNotEqualTo(String value) {
            addCriterion("\"operation_incision_description\" <>", value, "operationIncisionDescription");
            return (Criteria) this;
        }

        public Criteria andOperationIncisionDescriptionGreaterThan(String value) {
            addCriterion("\"operation_incision_description\" >", value, "operationIncisionDescription");
            return (Criteria) this;
        }

        public Criteria andOperationIncisionDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("\"operation_incision_description\" >=", value, "operationIncisionDescription");
            return (Criteria) this;
        }

        public Criteria andOperationIncisionDescriptionLessThan(String value) {
            addCriterion("\"operation_incision_description\" <", value, "operationIncisionDescription");
            return (Criteria) this;
        }

        public Criteria andOperationIncisionDescriptionLessThanOrEqualTo(String value) {
            addCriterion("\"operation_incision_description\" <=", value, "operationIncisionDescription");
            return (Criteria) this;
        }

        public Criteria andOperationIncisionDescriptionLike(String value) {
            addCriterion("\"operation_incision_description\" like", value, "operationIncisionDescription");
            return (Criteria) this;
        }

        public Criteria andOperationIncisionDescriptionNotLike(String value) {
            addCriterion("\"operation_incision_description\" not like", value, "operationIncisionDescription");
            return (Criteria) this;
        }

        public Criteria andOperationIncisionDescriptionIn(List<String> values) {
            addCriterion("\"operation_incision_description\" in", values, "operationIncisionDescription");
            return (Criteria) this;
        }

        public Criteria andOperationIncisionDescriptionNotIn(List<String> values) {
            addCriterion("\"operation_incision_description\" not in", values, "operationIncisionDescription");
            return (Criteria) this;
        }

        public Criteria andOperationIncisionDescriptionBetween(String value1, String value2) {
            addCriterion("\"operation_incision_description\" between", value1, value2, "operationIncisionDescription");
            return (Criteria) this;
        }

        public Criteria andOperationIncisionDescriptionNotBetween(String value1, String value2) {
            addCriterion("\"operation_incision_description\" not between", value1, value2, "operationIncisionDescription");
            return (Criteria) this;
        }

        public Criteria andDrainageSignIsNull() {
            addCriterion("\"drainage_sign\" is null");
            return (Criteria) this;
        }

        public Criteria andDrainageSignIsNotNull() {
            addCriterion("\"drainage_sign\" is not null");
            return (Criteria) this;
        }

        public Criteria andDrainageSignEqualTo(String value) {
            addCriterion("\"drainage_sign\" =", value, "drainageSign");
            return (Criteria) this;
        }

        public Criteria andDrainageSignNotEqualTo(String value) {
            addCriterion("\"drainage_sign\" <>", value, "drainageSign");
            return (Criteria) this;
        }

        public Criteria andDrainageSignGreaterThan(String value) {
            addCriterion("\"drainage_sign\" >", value, "drainageSign");
            return (Criteria) this;
        }

        public Criteria andDrainageSignGreaterThanOrEqualTo(String value) {
            addCriterion("\"drainage_sign\" >=", value, "drainageSign");
            return (Criteria) this;
        }

        public Criteria andDrainageSignLessThan(String value) {
            addCriterion("\"drainage_sign\" <", value, "drainageSign");
            return (Criteria) this;
        }

        public Criteria andDrainageSignLessThanOrEqualTo(String value) {
            addCriterion("\"drainage_sign\" <=", value, "drainageSign");
            return (Criteria) this;
        }

        public Criteria andDrainageSignLike(String value) {
            addCriterion("\"drainage_sign\" like", value, "drainageSign");
            return (Criteria) this;
        }

        public Criteria andDrainageSignNotLike(String value) {
            addCriterion("\"drainage_sign\" not like", value, "drainageSign");
            return (Criteria) this;
        }

        public Criteria andDrainageSignIn(List<String> values) {
            addCriterion("\"drainage_sign\" in", values, "drainageSign");
            return (Criteria) this;
        }

        public Criteria andDrainageSignNotIn(List<String> values) {
            addCriterion("\"drainage_sign\" not in", values, "drainageSign");
            return (Criteria) this;
        }

        public Criteria andDrainageSignBetween(String value1, String value2) {
            addCriterion("\"drainage_sign\" between", value1, value2, "drainageSign");
            return (Criteria) this;
        }

        public Criteria andDrainageSignNotBetween(String value1, String value2) {
            addCriterion("\"drainage_sign\" not between", value1, value2, "drainageSign");
            return (Criteria) this;
        }

        public Criteria andBleedingVolumeIsNull() {
            addCriterion("\"bleeding_volume\" is null");
            return (Criteria) this;
        }

        public Criteria andBleedingVolumeIsNotNull() {
            addCriterion("\"bleeding_volume\" is not null");
            return (Criteria) this;
        }

        public Criteria andBleedingVolumeEqualTo(String value) {
            addCriterion("\"bleeding_volume\" =", value, "bleedingVolume");
            return (Criteria) this;
        }

        public Criteria andBleedingVolumeNotEqualTo(String value) {
            addCriterion("\"bleeding_volume\" <>", value, "bleedingVolume");
            return (Criteria) this;
        }

        public Criteria andBleedingVolumeGreaterThan(String value) {
            addCriterion("\"bleeding_volume\" >", value, "bleedingVolume");
            return (Criteria) this;
        }

        public Criteria andBleedingVolumeGreaterThanOrEqualTo(String value) {
            addCriterion("\"bleeding_volume\" >=", value, "bleedingVolume");
            return (Criteria) this;
        }

        public Criteria andBleedingVolumeLessThan(String value) {
            addCriterion("\"bleeding_volume\" <", value, "bleedingVolume");
            return (Criteria) this;
        }

        public Criteria andBleedingVolumeLessThanOrEqualTo(String value) {
            addCriterion("\"bleeding_volume\" <=", value, "bleedingVolume");
            return (Criteria) this;
        }

        public Criteria andBleedingVolumeLike(String value) {
            addCriterion("\"bleeding_volume\" like", value, "bleedingVolume");
            return (Criteria) this;
        }

        public Criteria andBleedingVolumeNotLike(String value) {
            addCriterion("\"bleeding_volume\" not like", value, "bleedingVolume");
            return (Criteria) this;
        }

        public Criteria andBleedingVolumeIn(List<String> values) {
            addCriterion("\"bleeding_volume\" in", values, "bleedingVolume");
            return (Criteria) this;
        }

        public Criteria andBleedingVolumeNotIn(List<String> values) {
            addCriterion("\"bleeding_volume\" not in", values, "bleedingVolume");
            return (Criteria) this;
        }

        public Criteria andBleedingVolumeBetween(String value1, String value2) {
            addCriterion("\"bleeding_volume\" between", value1, value2, "bleedingVolume");
            return (Criteria) this;
        }

        public Criteria andBleedingVolumeNotBetween(String value1, String value2) {
            addCriterion("\"bleeding_volume\" not between", value1, value2, "bleedingVolume");
            return (Criteria) this;
        }

        public Criteria andTransfusionVolumeIsNull() {
            addCriterion("\"transfusion_volume\" is null");
            return (Criteria) this;
        }

        public Criteria andTransfusionVolumeIsNotNull() {
            addCriterion("\"transfusion_volume\" is not null");
            return (Criteria) this;
        }

        public Criteria andTransfusionVolumeEqualTo(String value) {
            addCriterion("\"transfusion_volume\" =", value, "transfusionVolume");
            return (Criteria) this;
        }

        public Criteria andTransfusionVolumeNotEqualTo(String value) {
            addCriterion("\"transfusion_volume\" <>", value, "transfusionVolume");
            return (Criteria) this;
        }

        public Criteria andTransfusionVolumeGreaterThan(String value) {
            addCriterion("\"transfusion_volume\" >", value, "transfusionVolume");
            return (Criteria) this;
        }

        public Criteria andTransfusionVolumeGreaterThanOrEqualTo(String value) {
            addCriterion("\"transfusion_volume\" >=", value, "transfusionVolume");
            return (Criteria) this;
        }

        public Criteria andTransfusionVolumeLessThan(String value) {
            addCriterion("\"transfusion_volume\" <", value, "transfusionVolume");
            return (Criteria) this;
        }

        public Criteria andTransfusionVolumeLessThanOrEqualTo(String value) {
            addCriterion("\"transfusion_volume\" <=", value, "transfusionVolume");
            return (Criteria) this;
        }

        public Criteria andTransfusionVolumeLike(String value) {
            addCriterion("\"transfusion_volume\" like", value, "transfusionVolume");
            return (Criteria) this;
        }

        public Criteria andTransfusionVolumeNotLike(String value) {
            addCriterion("\"transfusion_volume\" not like", value, "transfusionVolume");
            return (Criteria) this;
        }

        public Criteria andTransfusionVolumeIn(List<String> values) {
            addCriterion("\"transfusion_volume\" in", values, "transfusionVolume");
            return (Criteria) this;
        }

        public Criteria andTransfusionVolumeNotIn(List<String> values) {
            addCriterion("\"transfusion_volume\" not in", values, "transfusionVolume");
            return (Criteria) this;
        }

        public Criteria andTransfusionVolumeBetween(String value1, String value2) {
            addCriterion("\"transfusion_volume\" between", value1, value2, "transfusionVolume");
            return (Criteria) this;
        }

        public Criteria andTransfusionVolumeNotBetween(String value1, String value2) {
            addCriterion("\"transfusion_volume\" not between", value1, value2, "transfusionVolume");
            return (Criteria) this;
        }

        public Criteria andBloodTransfusionVolumeIsNull() {
            addCriterion("\"blood_transfusion_volume\" is null");
            return (Criteria) this;
        }

        public Criteria andBloodTransfusionVolumeIsNotNull() {
            addCriterion("\"blood_transfusion_volume\" is not null");
            return (Criteria) this;
        }

        public Criteria andBloodTransfusionVolumeEqualTo(String value) {
            addCriterion("\"blood_transfusion_volume\" =", value, "bloodTransfusionVolume");
            return (Criteria) this;
        }

        public Criteria andBloodTransfusionVolumeNotEqualTo(String value) {
            addCriterion("\"blood_transfusion_volume\" <>", value, "bloodTransfusionVolume");
            return (Criteria) this;
        }

        public Criteria andBloodTransfusionVolumeGreaterThan(String value) {
            addCriterion("\"blood_transfusion_volume\" >", value, "bloodTransfusionVolume");
            return (Criteria) this;
        }

        public Criteria andBloodTransfusionVolumeGreaterThanOrEqualTo(String value) {
            addCriterion("\"blood_transfusion_volume\" >=", value, "bloodTransfusionVolume");
            return (Criteria) this;
        }

        public Criteria andBloodTransfusionVolumeLessThan(String value) {
            addCriterion("\"blood_transfusion_volume\" <", value, "bloodTransfusionVolume");
            return (Criteria) this;
        }

        public Criteria andBloodTransfusionVolumeLessThanOrEqualTo(String value) {
            addCriterion("\"blood_transfusion_volume\" <=", value, "bloodTransfusionVolume");
            return (Criteria) this;
        }

        public Criteria andBloodTransfusionVolumeLike(String value) {
            addCriterion("\"blood_transfusion_volume\" like", value, "bloodTransfusionVolume");
            return (Criteria) this;
        }

        public Criteria andBloodTransfusionVolumeNotLike(String value) {
            addCriterion("\"blood_transfusion_volume\" not like", value, "bloodTransfusionVolume");
            return (Criteria) this;
        }

        public Criteria andBloodTransfusionVolumeIn(List<String> values) {
            addCriterion("\"blood_transfusion_volume\" in", values, "bloodTransfusionVolume");
            return (Criteria) this;
        }

        public Criteria andBloodTransfusionVolumeNotIn(List<String> values) {
            addCriterion("\"blood_transfusion_volume\" not in", values, "bloodTransfusionVolume");
            return (Criteria) this;
        }

        public Criteria andBloodTransfusionVolumeBetween(String value1, String value2) {
            addCriterion("\"blood_transfusion_volume\" between", value1, value2, "bloodTransfusionVolume");
            return (Criteria) this;
        }

        public Criteria andBloodTransfusionVolumeNotBetween(String value1, String value2) {
            addCriterion("\"blood_transfusion_volume\" not between", value1, value2, "bloodTransfusionVolume");
            return (Criteria) this;
        }

        public Criteria andDrugBeforeOperIsNull() {
            addCriterion("\"drug_before_oper\" is null");
            return (Criteria) this;
        }

        public Criteria andDrugBeforeOperIsNotNull() {
            addCriterion("\"drug_before_oper\" is not null");
            return (Criteria) this;
        }

        public Criteria andDrugBeforeOperEqualTo(String value) {
            addCriterion("\"drug_before_oper\" =", value, "drugBeforeOper");
            return (Criteria) this;
        }

        public Criteria andDrugBeforeOperNotEqualTo(String value) {
            addCriterion("\"drug_before_oper\" <>", value, "drugBeforeOper");
            return (Criteria) this;
        }

        public Criteria andDrugBeforeOperGreaterThan(String value) {
            addCriterion("\"drug_before_oper\" >", value, "drugBeforeOper");
            return (Criteria) this;
        }

        public Criteria andDrugBeforeOperGreaterThanOrEqualTo(String value) {
            addCriterion("\"drug_before_oper\" >=", value, "drugBeforeOper");
            return (Criteria) this;
        }

        public Criteria andDrugBeforeOperLessThan(String value) {
            addCriterion("\"drug_before_oper\" <", value, "drugBeforeOper");
            return (Criteria) this;
        }

        public Criteria andDrugBeforeOperLessThanOrEqualTo(String value) {
            addCriterion("\"drug_before_oper\" <=", value, "drugBeforeOper");
            return (Criteria) this;
        }

        public Criteria andDrugBeforeOperLike(String value) {
            addCriterion("\"drug_before_oper\" like", value, "drugBeforeOper");
            return (Criteria) this;
        }

        public Criteria andDrugBeforeOperNotLike(String value) {
            addCriterion("\"drug_before_oper\" not like", value, "drugBeforeOper");
            return (Criteria) this;
        }

        public Criteria andDrugBeforeOperIn(List<String> values) {
            addCriterion("\"drug_before_oper\" in", values, "drugBeforeOper");
            return (Criteria) this;
        }

        public Criteria andDrugBeforeOperNotIn(List<String> values) {
            addCriterion("\"drug_before_oper\" not in", values, "drugBeforeOper");
            return (Criteria) this;
        }

        public Criteria andDrugBeforeOperBetween(String value1, String value2) {
            addCriterion("\"drug_before_oper\" between", value1, value2, "drugBeforeOper");
            return (Criteria) this;
        }

        public Criteria andDrugBeforeOperNotBetween(String value1, String value2) {
            addCriterion("\"drug_before_oper\" not between", value1, value2, "drugBeforeOper");
            return (Criteria) this;
        }

        public Criteria andDrugDuringOperIsNull() {
            addCriterion("\"drug_during_oper\" is null");
            return (Criteria) this;
        }

        public Criteria andDrugDuringOperIsNotNull() {
            addCriterion("\"drug_during_oper\" is not null");
            return (Criteria) this;
        }

        public Criteria andDrugDuringOperEqualTo(String value) {
            addCriterion("\"drug_during_oper\" =", value, "drugDuringOper");
            return (Criteria) this;
        }

        public Criteria andDrugDuringOperNotEqualTo(String value) {
            addCriterion("\"drug_during_oper\" <>", value, "drugDuringOper");
            return (Criteria) this;
        }

        public Criteria andDrugDuringOperGreaterThan(String value) {
            addCriterion("\"drug_during_oper\" >", value, "drugDuringOper");
            return (Criteria) this;
        }

        public Criteria andDrugDuringOperGreaterThanOrEqualTo(String value) {
            addCriterion("\"drug_during_oper\" >=", value, "drugDuringOper");
            return (Criteria) this;
        }

        public Criteria andDrugDuringOperLessThan(String value) {
            addCriterion("\"drug_during_oper\" <", value, "drugDuringOper");
            return (Criteria) this;
        }

        public Criteria andDrugDuringOperLessThanOrEqualTo(String value) {
            addCriterion("\"drug_during_oper\" <=", value, "drugDuringOper");
            return (Criteria) this;
        }

        public Criteria andDrugDuringOperLike(String value) {
            addCriterion("\"drug_during_oper\" like", value, "drugDuringOper");
            return (Criteria) this;
        }

        public Criteria andDrugDuringOperNotLike(String value) {
            addCriterion("\"drug_during_oper\" not like", value, "drugDuringOper");
            return (Criteria) this;
        }

        public Criteria andDrugDuringOperIn(List<String> values) {
            addCriterion("\"drug_during_oper\" in", values, "drugDuringOper");
            return (Criteria) this;
        }

        public Criteria andDrugDuringOperNotIn(List<String> values) {
            addCriterion("\"drug_during_oper\" not in", values, "drugDuringOper");
            return (Criteria) this;
        }

        public Criteria andDrugDuringOperBetween(String value1, String value2) {
            addCriterion("\"drug_during_oper\" between", value1, value2, "drugDuringOper");
            return (Criteria) this;
        }

        public Criteria andDrugDuringOperNotBetween(String value1, String value2) {
            addCriterion("\"drug_during_oper\" not between", value1, value2, "drugDuringOper");
            return (Criteria) this;
        }

        public Criteria andTransfusionReactionSignsIsNull() {
            addCriterion("\"transfusion_reaction_signs\" is null");
            return (Criteria) this;
        }

        public Criteria andTransfusionReactionSignsIsNotNull() {
            addCriterion("\"transfusion_reaction_signs\" is not null");
            return (Criteria) this;
        }

        public Criteria andTransfusionReactionSignsEqualTo(String value) {
            addCriterion("\"transfusion_reaction_signs\" =", value, "transfusionReactionSigns");
            return (Criteria) this;
        }

        public Criteria andTransfusionReactionSignsNotEqualTo(String value) {
            addCriterion("\"transfusion_reaction_signs\" <>", value, "transfusionReactionSigns");
            return (Criteria) this;
        }

        public Criteria andTransfusionReactionSignsGreaterThan(String value) {
            addCriterion("\"transfusion_reaction_signs\" >", value, "transfusionReactionSigns");
            return (Criteria) this;
        }

        public Criteria andTransfusionReactionSignsGreaterThanOrEqualTo(String value) {
            addCriterion("\"transfusion_reaction_signs\" >=", value, "transfusionReactionSigns");
            return (Criteria) this;
        }

        public Criteria andTransfusionReactionSignsLessThan(String value) {
            addCriterion("\"transfusion_reaction_signs\" <", value, "transfusionReactionSigns");
            return (Criteria) this;
        }

        public Criteria andTransfusionReactionSignsLessThanOrEqualTo(String value) {
            addCriterion("\"transfusion_reaction_signs\" <=", value, "transfusionReactionSigns");
            return (Criteria) this;
        }

        public Criteria andTransfusionReactionSignsLike(String value) {
            addCriterion("\"transfusion_reaction_signs\" like", value, "transfusionReactionSigns");
            return (Criteria) this;
        }

        public Criteria andTransfusionReactionSignsNotLike(String value) {
            addCriterion("\"transfusion_reaction_signs\" not like", value, "transfusionReactionSigns");
            return (Criteria) this;
        }

        public Criteria andTransfusionReactionSignsIn(List<String> values) {
            addCriterion("\"transfusion_reaction_signs\" in", values, "transfusionReactionSigns");
            return (Criteria) this;
        }

        public Criteria andTransfusionReactionSignsNotIn(List<String> values) {
            addCriterion("\"transfusion_reaction_signs\" not in", values, "transfusionReactionSigns");
            return (Criteria) this;
        }

        public Criteria andTransfusionReactionSignsBetween(String value1, String value2) {
            addCriterion("\"transfusion_reaction_signs\" between", value1, value2, "transfusionReactionSigns");
            return (Criteria) this;
        }

        public Criteria andTransfusionReactionSignsNotBetween(String value1, String value2) {
            addCriterion("\"transfusion_reaction_signs\" not between", value1, value2, "transfusionReactionSigns");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorIsNull() {
            addCriterion("\"surgeon_doctor\" is null");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorIsNotNull() {
            addCriterion("\"surgeon_doctor\" is not null");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorEqualTo(String value) {
            addCriterion("\"surgeon_doctor\" =", value, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorNotEqualTo(String value) {
            addCriterion("\"surgeon_doctor\" <>", value, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorGreaterThan(String value) {
            addCriterion("\"surgeon_doctor\" >", value, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorGreaterThanOrEqualTo(String value) {
            addCriterion("\"surgeon_doctor\" >=", value, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorLessThan(String value) {
            addCriterion("\"surgeon_doctor\" <", value, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorLessThanOrEqualTo(String value) {
            addCriterion("\"surgeon_doctor\" <=", value, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorLike(String value) {
            addCriterion("\"surgeon_doctor\" like", value, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorNotLike(String value) {
            addCriterion("\"surgeon_doctor\" not like", value, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorIn(List<String> values) {
            addCriterion("\"surgeon_doctor\" in", values, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorNotIn(List<String> values) {
            addCriterion("\"surgeon_doctor\" not in", values, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorBetween(String value1, String value2) {
            addCriterion("\"surgeon_doctor\" between", value1, value2, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorNotBetween(String value1, String value2) {
            addCriterion("\"surgeon_doctor\" not between", value1, value2, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantIsNull() {
            addCriterion("\"first_assistant\" is null");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantIsNotNull() {
            addCriterion("\"first_assistant\" is not null");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantEqualTo(String value) {
            addCriterion("\"first_assistant\" =", value, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantNotEqualTo(String value) {
            addCriterion("\"first_assistant\" <>", value, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantGreaterThan(String value) {
            addCriterion("\"first_assistant\" >", value, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantGreaterThanOrEqualTo(String value) {
            addCriterion("\"first_assistant\" >=", value, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantLessThan(String value) {
            addCriterion("\"first_assistant\" <", value, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantLessThanOrEqualTo(String value) {
            addCriterion("\"first_assistant\" <=", value, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantLike(String value) {
            addCriterion("\"first_assistant\" like", value, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantNotLike(String value) {
            addCriterion("\"first_assistant\" not like", value, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantIn(List<String> values) {
            addCriterion("\"first_assistant\" in", values, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantNotIn(List<String> values) {
            addCriterion("\"first_assistant\" not in", values, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantBetween(String value1, String value2) {
            addCriterion("\"first_assistant\" between", value1, value2, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantNotBetween(String value1, String value2) {
            addCriterion("\"first_assistant\" not between", value1, value2, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantIsNull() {
            addCriterion("\"second_assistant\" is null");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantIsNotNull() {
            addCriterion("\"second_assistant\" is not null");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantEqualTo(String value) {
            addCriterion("\"second_assistant\" =", value, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantNotEqualTo(String value) {
            addCriterion("\"second_assistant\" <>", value, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantGreaterThan(String value) {
            addCriterion("\"second_assistant\" >", value, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantGreaterThanOrEqualTo(String value) {
            addCriterion("\"second_assistant\" >=", value, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantLessThan(String value) {
            addCriterion("\"second_assistant\" <", value, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantLessThanOrEqualTo(String value) {
            addCriterion("\"second_assistant\" <=", value, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantLike(String value) {
            addCriterion("\"second_assistant\" like", value, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantNotLike(String value) {
            addCriterion("\"second_assistant\" not like", value, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantIn(List<String> values) {
            addCriterion("\"second_assistant\" in", values, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantNotIn(List<String> values) {
            addCriterion("\"second_assistant\" not in", values, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantBetween(String value1, String value2) {
            addCriterion("\"second_assistant\" between", value1, value2, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantNotBetween(String value1, String value2) {
            addCriterion("\"second_assistant\" not between", value1, value2, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaCodeIsNull() {
            addCriterion("\"anesthesia_code\" is null");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaCodeIsNotNull() {
            addCriterion("\"anesthesia_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaCodeEqualTo(String value) {
            addCriterion("\"anesthesia_code\" =", value, "anesthesiaCode");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaCodeNotEqualTo(String value) {
            addCriterion("\"anesthesia_code\" <>", value, "anesthesiaCode");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaCodeGreaterThan(String value) {
            addCriterion("\"anesthesia_code\" >", value, "anesthesiaCode");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"anesthesia_code\" >=", value, "anesthesiaCode");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaCodeLessThan(String value) {
            addCriterion("\"anesthesia_code\" <", value, "anesthesiaCode");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaCodeLessThanOrEqualTo(String value) {
            addCriterion("\"anesthesia_code\" <=", value, "anesthesiaCode");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaCodeLike(String value) {
            addCriterion("\"anesthesia_code\" like", value, "anesthesiaCode");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaCodeNotLike(String value) {
            addCriterion("\"anesthesia_code\" not like", value, "anesthesiaCode");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaCodeIn(List<String> values) {
            addCriterion("\"anesthesia_code\" in", values, "anesthesiaCode");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaCodeNotIn(List<String> values) {
            addCriterion("\"anesthesia_code\" not in", values, "anesthesiaCode");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaCodeBetween(String value1, String value2) {
            addCriterion("\"anesthesia_code\" between", value1, value2, "anesthesiaCode");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaCodeNotBetween(String value1, String value2) {
            addCriterion("\"anesthesia_code\" not between", value1, value2, "anesthesiaCode");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodIsNull() {
            addCriterion("\"anesthesia_method\" is null");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodIsNotNull() {
            addCriterion("\"anesthesia_method\" is not null");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodEqualTo(String value) {
            addCriterion("\"anesthesia_method\" =", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodNotEqualTo(String value) {
            addCriterion("\"anesthesia_method\" <>", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodGreaterThan(String value) {
            addCriterion("\"anesthesia_method\" >", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodGreaterThanOrEqualTo(String value) {
            addCriterion("\"anesthesia_method\" >=", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodLessThan(String value) {
            addCriterion("\"anesthesia_method\" <", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodLessThanOrEqualTo(String value) {
            addCriterion("\"anesthesia_method\" <=", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodLike(String value) {
            addCriterion("\"anesthesia_method\" like", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodNotLike(String value) {
            addCriterion("\"anesthesia_method\" not like", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodIn(List<String> values) {
            addCriterion("\"anesthesia_method\" in", values, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodNotIn(List<String> values) {
            addCriterion("\"anesthesia_method\" not in", values, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodBetween(String value1, String value2) {
            addCriterion("\"anesthesia_method\" between", value1, value2, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodNotBetween(String value1, String value2) {
            addCriterion("\"anesthesia_method\" not between", value1, value2, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorIsNull() {
            addCriterion("\"anaesthesia_doctor\" is null");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorIsNotNull() {
            addCriterion("\"anaesthesia_doctor\" is not null");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorEqualTo(String value) {
            addCriterion("\"anaesthesia_doctor\" =", value, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorNotEqualTo(String value) {
            addCriterion("\"anaesthesia_doctor\" <>", value, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorGreaterThan(String value) {
            addCriterion("\"anaesthesia_doctor\" >", value, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorGreaterThanOrEqualTo(String value) {
            addCriterion("\"anaesthesia_doctor\" >=", value, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorLessThan(String value) {
            addCriterion("\"anaesthesia_doctor\" <", value, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorLessThanOrEqualTo(String value) {
            addCriterion("\"anaesthesia_doctor\" <=", value, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorLike(String value) {
            addCriterion("\"anaesthesia_doctor\" like", value, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorNotLike(String value) {
            addCriterion("\"anaesthesia_doctor\" not like", value, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorIn(List<String> values) {
            addCriterion("\"anaesthesia_doctor\" in", values, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorNotIn(List<String> values) {
            addCriterion("\"anaesthesia_doctor\" not in", values, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorBetween(String value1, String value2) {
            addCriterion("\"anaesthesia_doctor\" between", value1, value2, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorNotBetween(String value1, String value2) {
            addCriterion("\"anaesthesia_doctor\" not between", value1, value2, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationIsNull() {
            addCriterion("\"diag_preoperation\" is null");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationIsNotNull() {
            addCriterion("\"diag_preoperation\" is not null");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationEqualTo(String value) {
            addCriterion("\"diag_preoperation\" =", value, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationNotEqualTo(String value) {
            addCriterion("\"diag_preoperation\" <>", value, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationGreaterThan(String value) {
            addCriterion("\"diag_preoperation\" >", value, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationGreaterThanOrEqualTo(String value) {
            addCriterion("\"diag_preoperation\" >=", value, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationLessThan(String value) {
            addCriterion("\"diag_preoperation\" <", value, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationLessThanOrEqualTo(String value) {
            addCriterion("\"diag_preoperation\" <=", value, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationLike(String value) {
            addCriterion("\"diag_preoperation\" like", value, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationNotLike(String value) {
            addCriterion("\"diag_preoperation\" not like", value, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationIn(List<String> values) {
            addCriterion("\"diag_preoperation\" in", values, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationNotIn(List<String> values) {
            addCriterion("\"diag_preoperation\" not in", values, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationBetween(String value1, String value2) {
            addCriterion("\"diag_preoperation\" between", value1, value2, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPreoperationNotBetween(String value1, String value2) {
            addCriterion("\"diag_preoperation\" not between", value1, value2, "diagPreoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationIsNull() {
            addCriterion("\"diag_perioperation\" is null");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationIsNotNull() {
            addCriterion("\"diag_perioperation\" is not null");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationEqualTo(String value) {
            addCriterion("\"diag_perioperation\" =", value, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationNotEqualTo(String value) {
            addCriterion("\"diag_perioperation\" <>", value, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationGreaterThan(String value) {
            addCriterion("\"diag_perioperation\" >", value, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationGreaterThanOrEqualTo(String value) {
            addCriterion("\"diag_perioperation\" >=", value, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationLessThan(String value) {
            addCriterion("\"diag_perioperation\" <", value, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationLessThanOrEqualTo(String value) {
            addCriterion("\"diag_perioperation\" <=", value, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationLike(String value) {
            addCriterion("\"diag_perioperation\" like", value, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationNotLike(String value) {
            addCriterion("\"diag_perioperation\" not like", value, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationIn(List<String> values) {
            addCriterion("\"diag_perioperation\" in", values, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationNotIn(List<String> values) {
            addCriterion("\"diag_perioperation\" not in", values, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationBetween(String value1, String value2) {
            addCriterion("\"diag_perioperation\" between", value1, value2, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationNotBetween(String value1, String value2) {
            addCriterion("\"diag_perioperation\" not between", value1, value2, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationIsNull() {
            addCriterion("\"diag_postoperation\" is null");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationIsNotNull() {
            addCriterion("\"diag_postoperation\" is not null");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationEqualTo(String value) {
            addCriterion("\"diag_postoperation\" =", value, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationNotEqualTo(String value) {
            addCriterion("\"diag_postoperation\" <>", value, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationGreaterThan(String value) {
            addCriterion("\"diag_postoperation\" >", value, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationGreaterThanOrEqualTo(String value) {
            addCriterion("\"diag_postoperation\" >=", value, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationLessThan(String value) {
            addCriterion("\"diag_postoperation\" <", value, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationLessThanOrEqualTo(String value) {
            addCriterion("\"diag_postoperation\" <=", value, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationLike(String value) {
            addCriterion("\"diag_postoperation\" like", value, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationNotLike(String value) {
            addCriterion("\"diag_postoperation\" not like", value, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationIn(List<String> values) {
            addCriterion("\"diag_postoperation\" in", values, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationNotIn(List<String> values) {
            addCriterion("\"diag_postoperation\" not in", values, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationBetween(String value1, String value2) {
            addCriterion("\"diag_postoperation\" between", value1, value2, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andDiagPostoperationNotBetween(String value1, String value2) {
            addCriterion("\"diag_postoperation\" not between", value1, value2, "diagPostoperation");
            return (Criteria) this;
        }

        public Criteria andSurgicalProcessIsNull() {
            addCriterion("\"surgical_process\" is null");
            return (Criteria) this;
        }

        public Criteria andSurgicalProcessIsNotNull() {
            addCriterion("\"surgical_process\" is not null");
            return (Criteria) this;
        }

        public Criteria andSurgicalProcessEqualTo(String value) {
            addCriterion("\"surgical_process\" =", value, "surgicalProcess");
            return (Criteria) this;
        }

        public Criteria andSurgicalProcessNotEqualTo(String value) {
            addCriterion("\"surgical_process\" <>", value, "surgicalProcess");
            return (Criteria) this;
        }

        public Criteria andSurgicalProcessGreaterThan(String value) {
            addCriterion("\"surgical_process\" >", value, "surgicalProcess");
            return (Criteria) this;
        }

        public Criteria andSurgicalProcessGreaterThanOrEqualTo(String value) {
            addCriterion("\"surgical_process\" >=", value, "surgicalProcess");
            return (Criteria) this;
        }

        public Criteria andSurgicalProcessLessThan(String value) {
            addCriterion("\"surgical_process\" <", value, "surgicalProcess");
            return (Criteria) this;
        }

        public Criteria andSurgicalProcessLessThanOrEqualTo(String value) {
            addCriterion("\"surgical_process\" <=", value, "surgicalProcess");
            return (Criteria) this;
        }

        public Criteria andSurgicalProcessLike(String value) {
            addCriterion("\"surgical_process\" like", value, "surgicalProcess");
            return (Criteria) this;
        }

        public Criteria andSurgicalProcessNotLike(String value) {
            addCriterion("\"surgical_process\" not like", value, "surgicalProcess");
            return (Criteria) this;
        }

        public Criteria andSurgicalProcessIn(List<String> values) {
            addCriterion("\"surgical_process\" in", values, "surgicalProcess");
            return (Criteria) this;
        }

        public Criteria andSurgicalProcessNotIn(List<String> values) {
            addCriterion("\"surgical_process\" not in", values, "surgicalProcess");
            return (Criteria) this;
        }

        public Criteria andSurgicalProcessBetween(String value1, String value2) {
            addCriterion("\"surgical_process\" between", value1, value2, "surgicalProcess");
            return (Criteria) this;
        }

        public Criteria andSurgicalProcessNotBetween(String value1, String value2) {
            addCriterion("\"surgical_process\" not between", value1, value2, "surgicalProcess");
            return (Criteria) this;
        }

        public Criteria andPostoperationCompliteIsNull() {
            addCriterion("\"postoperation_complite\" is null");
            return (Criteria) this;
        }

        public Criteria andPostoperationCompliteIsNotNull() {
            addCriterion("\"postoperation_complite\" is not null");
            return (Criteria) this;
        }

        public Criteria andPostoperationCompliteEqualTo(String value) {
            addCriterion("\"postoperation_complite\" =", value, "postoperationComplite");
            return (Criteria) this;
        }

        public Criteria andPostoperationCompliteNotEqualTo(String value) {
            addCriterion("\"postoperation_complite\" <>", value, "postoperationComplite");
            return (Criteria) this;
        }

        public Criteria andPostoperationCompliteGreaterThan(String value) {
            addCriterion("\"postoperation_complite\" >", value, "postoperationComplite");
            return (Criteria) this;
        }

        public Criteria andPostoperationCompliteGreaterThanOrEqualTo(String value) {
            addCriterion("\"postoperation_complite\" >=", value, "postoperationComplite");
            return (Criteria) this;
        }

        public Criteria andPostoperationCompliteLessThan(String value) {
            addCriterion("\"postoperation_complite\" <", value, "postoperationComplite");
            return (Criteria) this;
        }

        public Criteria andPostoperationCompliteLessThanOrEqualTo(String value) {
            addCriterion("\"postoperation_complite\" <=", value, "postoperationComplite");
            return (Criteria) this;
        }

        public Criteria andPostoperationCompliteLike(String value) {
            addCriterion("\"postoperation_complite\" like", value, "postoperationComplite");
            return (Criteria) this;
        }

        public Criteria andPostoperationCompliteNotLike(String value) {
            addCriterion("\"postoperation_complite\" not like", value, "postoperationComplite");
            return (Criteria) this;
        }

        public Criteria andPostoperationCompliteIn(List<String> values) {
            addCriterion("\"postoperation_complite\" in", values, "postoperationComplite");
            return (Criteria) this;
        }

        public Criteria andPostoperationCompliteNotIn(List<String> values) {
            addCriterion("\"postoperation_complite\" not in", values, "postoperationComplite");
            return (Criteria) this;
        }

        public Criteria andPostoperationCompliteBetween(String value1, String value2) {
            addCriterion("\"postoperation_complite\" between", value1, value2, "postoperationComplite");
            return (Criteria) this;
        }

        public Criteria andPostoperationCompliteNotBetween(String value1, String value2) {
            addCriterion("\"postoperation_complite\" not between", value1, value2, "postoperationComplite");
            return (Criteria) this;
        }

        public Criteria andFocusDescriptionIsNull() {
            addCriterion("\"focus_description\" is null");
            return (Criteria) this;
        }

        public Criteria andFocusDescriptionIsNotNull() {
            addCriterion("\"focus_description\" is not null");
            return (Criteria) this;
        }

        public Criteria andFocusDescriptionEqualTo(String value) {
            addCriterion("\"focus_description\" =", value, "focusDescription");
            return (Criteria) this;
        }

        public Criteria andFocusDescriptionNotEqualTo(String value) {
            addCriterion("\"focus_description\" <>", value, "focusDescription");
            return (Criteria) this;
        }

        public Criteria andFocusDescriptionGreaterThan(String value) {
            addCriterion("\"focus_description\" >", value, "focusDescription");
            return (Criteria) this;
        }

        public Criteria andFocusDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("\"focus_description\" >=", value, "focusDescription");
            return (Criteria) this;
        }

        public Criteria andFocusDescriptionLessThan(String value) {
            addCriterion("\"focus_description\" <", value, "focusDescription");
            return (Criteria) this;
        }

        public Criteria andFocusDescriptionLessThanOrEqualTo(String value) {
            addCriterion("\"focus_description\" <=", value, "focusDescription");
            return (Criteria) this;
        }

        public Criteria andFocusDescriptionLike(String value) {
            addCriterion("\"focus_description\" like", value, "focusDescription");
            return (Criteria) this;
        }

        public Criteria andFocusDescriptionNotLike(String value) {
            addCriterion("\"focus_description\" not like", value, "focusDescription");
            return (Criteria) this;
        }

        public Criteria andFocusDescriptionIn(List<String> values) {
            addCriterion("\"focus_description\" in", values, "focusDescription");
            return (Criteria) this;
        }

        public Criteria andFocusDescriptionNotIn(List<String> values) {
            addCriterion("\"focus_description\" not in", values, "focusDescription");
            return (Criteria) this;
        }

        public Criteria andFocusDescriptionBetween(String value1, String value2) {
            addCriterion("\"focus_description\" between", value1, value2, "focusDescription");
            return (Criteria) this;
        }

        public Criteria andFocusDescriptionNotBetween(String value1, String value2) {
            addCriterion("\"focus_description\" not between", value1, value2, "focusDescription");
            return (Criteria) this;
        }

        public Criteria andRecordTimeIsNull() {
            addCriterion("\"record_time\" is null");
            return (Criteria) this;
        }

        public Criteria andRecordTimeIsNotNull() {
            addCriterion("\"record_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andRecordTimeEqualTo(Date value) {
            addCriterion("\"record_time\" =", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeNotEqualTo(Date value) {
            addCriterion("\"record_time\" <>", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeGreaterThan(Date value) {
            addCriterion("\"record_time\" >", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"record_time\" >=", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeLessThan(Date value) {
            addCriterion("\"record_time\" <", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"record_time\" <=", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeIn(List<Date> values) {
            addCriterion("\"record_time\" in", values, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeNotIn(List<Date> values) {
            addCriterion("\"record_time\" not in", values, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeBetween(Date value1, Date value2) {
            addCriterion("\"record_time\" between", value1, value2, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"record_time\" not between", value1, value2, "recordTime");
            return (Criteria) this;
        }

        public Criteria andDoctorSignIsNull() {
            addCriterion("\"doctor_sign\" is null");
            return (Criteria) this;
        }

        public Criteria andDoctorSignIsNotNull() {
            addCriterion("\"doctor_sign\" is not null");
            return (Criteria) this;
        }

        public Criteria andDoctorSignEqualTo(String value) {
            addCriterion("\"doctor_sign\" =", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignNotEqualTo(String value) {
            addCriterion("\"doctor_sign\" <>", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignGreaterThan(String value) {
            addCriterion("\"doctor_sign\" >", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignGreaterThanOrEqualTo(String value) {
            addCriterion("\"doctor_sign\" >=", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignLessThan(String value) {
            addCriterion("\"doctor_sign\" <", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignLessThanOrEqualTo(String value) {
            addCriterion("\"doctor_sign\" <=", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignLike(String value) {
            addCriterion("\"doctor_sign\" like", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignNotLike(String value) {
            addCriterion("\"doctor_sign\" not like", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignIn(List<String> values) {
            addCriterion("\"doctor_sign\" in", values, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignNotIn(List<String> values) {
            addCriterion("\"doctor_sign\" not in", values, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignBetween(String value1, String value2) {
            addCriterion("\"doctor_sign\" between", value1, value2, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignNotBetween(String value1, String value2) {
            addCriterion("\"doctor_sign\" not between", value1, value2, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNull() {
            addCriterion("\"source_path\" is null");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNotNull() {
            addCriterion("\"source_path\" is not null");
            return (Criteria) this;
        }

        public Criteria andSourcePathEqualTo(String value) {
            addCriterion("\"source_path\" =", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotEqualTo(String value) {
            addCriterion("\"source_path\" <>", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThan(String value) {
            addCriterion("\"source_path\" >", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThanOrEqualTo(String value) {
            addCriterion("\"source_path\" >=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThan(String value) {
            addCriterion("\"source_path\" <", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThanOrEqualTo(String value) {
            addCriterion("\"source_path\" <=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLike(String value) {
            addCriterion("\"source_path\" like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotLike(String value) {
            addCriterion("\"source_path\" not like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathIn(List<String> values) {
            addCriterion("\"source_path\" in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotIn(List<String> values) {
            addCriterion("\"source_path\" not in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathBetween(String value1, String value2) {
            addCriterion("\"source_path\" between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotBetween(String value1, String value2) {
            addCriterion("\"source_path\" not between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNull() {
            addCriterion("\"pk_id\" is null");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNotNull() {
            addCriterion("\"pk_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkIdEqualTo(String value) {
            addCriterion("\"pk_id\" =", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotEqualTo(String value) {
            addCriterion("\"pk_id\" <>", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThan(String value) {
            addCriterion("\"pk_id\" >", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" >=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThan(String value) {
            addCriterion("\"pk_id\" <", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" <=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLike(String value) {
            addCriterion("\"pk_id\" like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotLike(String value) {
            addCriterion("\"pk_id\" not like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdIn(List<String> values) {
            addCriterion("\"pk_id\" in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotIn(List<String> values) {
            addCriterion("\"pk_id\" not in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdBetween(String value1, String value2) {
            addCriterion("\"pk_id\" between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotBetween(String value1, String value2) {
            addCriterion("\"pk_id\" not between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNull() {
            addCriterion("\"data_state\" is null");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNotNull() {
            addCriterion("\"data_state\" is not null");
            return (Criteria) this;
        }

        public Criteria andDataStateEqualTo(String value) {
            addCriterion("\"data_state\" =", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotEqualTo(String value) {
            addCriterion("\"data_state\" <>", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThan(String value) {
            addCriterion("\"data_state\" >", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThanOrEqualTo(String value) {
            addCriterion("\"data_state\" >=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThan(String value) {
            addCriterion("\"data_state\" <", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThanOrEqualTo(String value) {
            addCriterion("\"data_state\" <=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLike(String value) {
            addCriterion("\"data_state\" like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotLike(String value) {
            addCriterion("\"data_state\" not like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateIn(List<String> values) {
            addCriterion("\"data_state\" in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotIn(List<String> values) {
            addCriterion("\"data_state\" not in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateBetween(String value1, String value2) {
            addCriterion("\"data_state\" between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotBetween(String value1, String value2) {
            addCriterion("\"data_state\" not between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andFullTextIsNull() {
            addCriterion("\"full_text\" is null");
            return (Criteria) this;
        }

        public Criteria andFullTextIsNotNull() {
            addCriterion("\"full_text\" is not null");
            return (Criteria) this;
        }

        public Criteria andFullTextEqualTo(String value) {
            addCriterion("\"full_text\" =", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextNotEqualTo(String value) {
            addCriterion("\"full_text\" <>", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextGreaterThan(String value) {
            addCriterion("\"full_text\" >", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextGreaterThanOrEqualTo(String value) {
            addCriterion("\"full_text\" >=", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextLessThan(String value) {
            addCriterion("\"full_text\" <", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextLessThanOrEqualTo(String value) {
            addCriterion("\"full_text\" <=", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextLike(String value) {
            addCriterion("\"full_text\" like", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextNotLike(String value) {
            addCriterion("\"full_text\" not like", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextIn(List<String> values) {
            addCriterion("\"full_text\" in", values, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextNotIn(List<String> values) {
            addCriterion("\"full_text\" not in", values, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextBetween(String value1, String value2) {
            addCriterion("\"full_text\" between", value1, value2, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextNotBetween(String value1, String value2) {
            addCriterion("\"full_text\" not between", value1, value2, "fullText");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIsNull() {
            addCriterion("\"patient_sn_org\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIsNotNull() {
            addCriterion("\"patient_sn_org\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgEqualTo(String value) {
            addCriterion("\"patient_sn_org\" =", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotEqualTo(String value) {
            addCriterion("\"patient_sn_org\" <>", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgGreaterThan(String value) {
            addCriterion("\"patient_sn_org\" >", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn_org\" >=", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLessThan(String value) {
            addCriterion("\"patient_sn_org\" <", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn_org\" <=", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLike(String value) {
            addCriterion("\"patient_sn_org\" like", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotLike(String value) {
            addCriterion("\"patient_sn_org\" not like", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIn(List<String> values) {
            addCriterion("\"patient_sn_org\" in", values, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotIn(List<String> values) {
            addCriterion("\"patient_sn_org\" not in", values, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgBetween(String value1, String value2) {
            addCriterion("\"patient_sn_org\" between", value1, value2, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn_org\" not between", value1, value2, "patientSnOrg");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}