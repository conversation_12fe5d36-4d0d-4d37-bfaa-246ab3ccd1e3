package com.haoys.rdr.domain.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
public class PatientFileExport implements Serializable {
    private String id;

    @ApiModelProperty(value = "文件名称")
    private String exprotName;

    @ApiModelProperty(value = "文件类型:1.excel，2.pdf ,3.sas")
    private String exportType;

    @ApiModelProperty(value = "文件大小")
    private String size;

    @ApiModelProperty(value = "导出进度")
    private String exportPress;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

    @ApiModelProperty(value = "文件下载地址")
    private String downloadUrl;

    @ApiModelProperty(value = "文件导出的id")
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    private String exId;

}
