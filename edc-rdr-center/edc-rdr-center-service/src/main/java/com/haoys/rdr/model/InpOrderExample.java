package com.haoys.rdr.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class InpOrderExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public InpOrderExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andHospitalCodeIsNull() {
            addCriterion("\"hospital_code\" is null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIsNotNull() {
            addCriterion("\"hospital_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeEqualTo(String value) {
            addCriterion("\"hospital_code\" =", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotEqualTo(String value) {
            addCriterion("\"hospital_code\" <>", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThan(String value) {
            addCriterion("\"hospital_code\" >", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" >=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThan(String value) {
            addCriterion("\"hospital_code\" <", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" <=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLike(String value) {
            addCriterion("\"hospital_code\" like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotLike(String value) {
            addCriterion("\"hospital_code\" not like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIn(List<String> values) {
            addCriterion("\"hospital_code\" in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotIn(List<String> values) {
            addCriterion("\"hospital_code\" not in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" not between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andTpatnoIsNull() {
            addCriterion("\"tpatno\" is null");
            return (Criteria) this;
        }

        public Criteria andTpatnoIsNotNull() {
            addCriterion("\"tpatno\" is not null");
            return (Criteria) this;
        }

        public Criteria andTpatnoEqualTo(String value) {
            addCriterion("\"tpatno\" =", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotEqualTo(String value) {
            addCriterion("\"tpatno\" <>", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoGreaterThan(String value) {
            addCriterion("\"tpatno\" >", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoGreaterThanOrEqualTo(String value) {
            addCriterion("\"tpatno\" >=", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLessThan(String value) {
            addCriterion("\"tpatno\" <", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLessThanOrEqualTo(String value) {
            addCriterion("\"tpatno\" <=", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLike(String value) {
            addCriterion("\"tpatno\" like", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotLike(String value) {
            addCriterion("\"tpatno\" not like", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoIn(List<String> values) {
            addCriterion("\"tpatno\" in", values, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotIn(List<String> values) {
            addCriterion("\"tpatno\" not in", values, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoBetween(String value1, String value2) {
            addCriterion("\"tpatno\" between", value1, value2, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotBetween(String value1, String value2) {
            addCriterion("\"tpatno\" not between", value1, value2, "tpatno");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("\"order_no\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("\"order_no\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("\"order_no\" =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("\"order_no\" <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("\"order_no\" >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("\"order_no\" >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("\"order_no\" <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("\"order_no\" <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("\"order_no\" like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("\"order_no\" not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("\"order_no\" in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("\"order_no\" not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("\"order_no\" between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("\"order_no\" not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderSubNoIsNull() {
            addCriterion("\"order_sub_no\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderSubNoIsNotNull() {
            addCriterion("\"order_sub_no\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderSubNoEqualTo(String value) {
            addCriterion("\"order_sub_no\" =", value, "orderSubNo");
            return (Criteria) this;
        }

        public Criteria andOrderSubNoNotEqualTo(String value) {
            addCriterion("\"order_sub_no\" <>", value, "orderSubNo");
            return (Criteria) this;
        }

        public Criteria andOrderSubNoGreaterThan(String value) {
            addCriterion("\"order_sub_no\" >", value, "orderSubNo");
            return (Criteria) this;
        }

        public Criteria andOrderSubNoGreaterThanOrEqualTo(String value) {
            addCriterion("\"order_sub_no\" >=", value, "orderSubNo");
            return (Criteria) this;
        }

        public Criteria andOrderSubNoLessThan(String value) {
            addCriterion("\"order_sub_no\" <", value, "orderSubNo");
            return (Criteria) this;
        }

        public Criteria andOrderSubNoLessThanOrEqualTo(String value) {
            addCriterion("\"order_sub_no\" <=", value, "orderSubNo");
            return (Criteria) this;
        }

        public Criteria andOrderSubNoLike(String value) {
            addCriterion("\"order_sub_no\" like", value, "orderSubNo");
            return (Criteria) this;
        }

        public Criteria andOrderSubNoNotLike(String value) {
            addCriterion("\"order_sub_no\" not like", value, "orderSubNo");
            return (Criteria) this;
        }

        public Criteria andOrderSubNoIn(List<String> values) {
            addCriterion("\"order_sub_no\" in", values, "orderSubNo");
            return (Criteria) this;
        }

        public Criteria andOrderSubNoNotIn(List<String> values) {
            addCriterion("\"order_sub_no\" not in", values, "orderSubNo");
            return (Criteria) this;
        }

        public Criteria andOrderSubNoBetween(String value1, String value2) {
            addCriterion("\"order_sub_no\" between", value1, value2, "orderSubNo");
            return (Criteria) this;
        }

        public Criteria andOrderSubNoNotBetween(String value1, String value2) {
            addCriterion("\"order_sub_no\" not between", value1, value2, "orderSubNo");
            return (Criteria) this;
        }

        public Criteria andOrderClassIsNull() {
            addCriterion("\"order_class\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderClassIsNotNull() {
            addCriterion("\"order_class\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderClassEqualTo(String value) {
            addCriterion("\"order_class\" =", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassNotEqualTo(String value) {
            addCriterion("\"order_class\" <>", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassGreaterThan(String value) {
            addCriterion("\"order_class\" >", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassGreaterThanOrEqualTo(String value) {
            addCriterion("\"order_class\" >=", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassLessThan(String value) {
            addCriterion("\"order_class\" <", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassLessThanOrEqualTo(String value) {
            addCriterion("\"order_class\" <=", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassLike(String value) {
            addCriterion("\"order_class\" like", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassNotLike(String value) {
            addCriterion("\"order_class\" not like", value, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassIn(List<String> values) {
            addCriterion("\"order_class\" in", values, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassNotIn(List<String> values) {
            addCriterion("\"order_class\" not in", values, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassBetween(String value1, String value2) {
            addCriterion("\"order_class\" between", value1, value2, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderClassNotBetween(String value1, String value2) {
            addCriterion("\"order_class\" not between", value1, value2, "orderClass");
            return (Criteria) this;
        }

        public Criteria andOrderTextIsNull() {
            addCriterion("\"order_text\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderTextIsNotNull() {
            addCriterion("\"order_text\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTextEqualTo(String value) {
            addCriterion("\"order_text\" =", value, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTextNotEqualTo(String value) {
            addCriterion("\"order_text\" <>", value, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTextGreaterThan(String value) {
            addCriterion("\"order_text\" >", value, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTextGreaterThanOrEqualTo(String value) {
            addCriterion("\"order_text\" >=", value, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTextLessThan(String value) {
            addCriterion("\"order_text\" <", value, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTextLessThanOrEqualTo(String value) {
            addCriterion("\"order_text\" <=", value, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTextLike(String value) {
            addCriterion("\"order_text\" like", value, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTextNotLike(String value) {
            addCriterion("\"order_text\" not like", value, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTextIn(List<String> values) {
            addCriterion("\"order_text\" in", values, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTextNotIn(List<String> values) {
            addCriterion("\"order_text\" not in", values, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTextBetween(String value1, String value2) {
            addCriterion("\"order_text\" between", value1, value2, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTextNotBetween(String value1, String value2) {
            addCriterion("\"order_text\" not between", value1, value2, "orderText");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNull() {
            addCriterion("\"order_type\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNotNull() {
            addCriterion("\"order_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeEqualTo(String value) {
            addCriterion("\"order_type\" =", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotEqualTo(String value) {
            addCriterion("\"order_type\" <>", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThan(String value) {
            addCriterion("\"order_type\" >", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThanOrEqualTo(String value) {
            addCriterion("\"order_type\" >=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThan(String value) {
            addCriterion("\"order_type\" <", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThanOrEqualTo(String value) {
            addCriterion("\"order_type\" <=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLike(String value) {
            addCriterion("\"order_type\" like", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotLike(String value) {
            addCriterion("\"order_type\" not like", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIn(List<String> values) {
            addCriterion("\"order_type\" in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotIn(List<String> values) {
            addCriterion("\"order_type\" not in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeBetween(String value1, String value2) {
            addCriterion("\"order_type\" between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotBetween(String value1, String value2) {
            addCriterion("\"order_type\" not between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andBrandNameIsNull() {
            addCriterion("\"brand_name\" is null");
            return (Criteria) this;
        }

        public Criteria andBrandNameIsNotNull() {
            addCriterion("\"brand_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andBrandNameEqualTo(String value) {
            addCriterion("\"brand_name\" =", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotEqualTo(String value) {
            addCriterion("\"brand_name\" <>", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameGreaterThan(String value) {
            addCriterion("\"brand_name\" >", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"brand_name\" >=", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLessThan(String value) {
            addCriterion("\"brand_name\" <", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLessThanOrEqualTo(String value) {
            addCriterion("\"brand_name\" <=", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLike(String value) {
            addCriterion("\"brand_name\" like", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotLike(String value) {
            addCriterion("\"brand_name\" not like", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameIn(List<String> values) {
            addCriterion("\"brand_name\" in", values, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotIn(List<String> values) {
            addCriterion("\"brand_name\" not in", values, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameBetween(String value1, String value2) {
            addCriterion("\"brand_name\" between", value1, value2, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotBetween(String value1, String value2) {
            addCriterion("\"brand_name\" not between", value1, value2, "brandName");
            return (Criteria) this;
        }

        public Criteria andGenericNameIsNull() {
            addCriterion("\"generic_name\" is null");
            return (Criteria) this;
        }

        public Criteria andGenericNameIsNotNull() {
            addCriterion("\"generic_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andGenericNameEqualTo(String value) {
            addCriterion("\"generic_name\" =", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameNotEqualTo(String value) {
            addCriterion("\"generic_name\" <>", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameGreaterThan(String value) {
            addCriterion("\"generic_name\" >", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"generic_name\" >=", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameLessThan(String value) {
            addCriterion("\"generic_name\" <", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameLessThanOrEqualTo(String value) {
            addCriterion("\"generic_name\" <=", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameLike(String value) {
            addCriterion("\"generic_name\" like", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameNotLike(String value) {
            addCriterion("\"generic_name\" not like", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameIn(List<String> values) {
            addCriterion("\"generic_name\" in", values, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameNotIn(List<String> values) {
            addCriterion("\"generic_name\" not in", values, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameBetween(String value1, String value2) {
            addCriterion("\"generic_name\" between", value1, value2, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameNotBetween(String value1, String value2) {
            addCriterion("\"generic_name\" not between", value1, value2, "genericName");
            return (Criteria) this;
        }

        public Criteria andDrugSpecIsNull() {
            addCriterion("\"drug_spec\" is null");
            return (Criteria) this;
        }

        public Criteria andDrugSpecIsNotNull() {
            addCriterion("\"drug_spec\" is not null");
            return (Criteria) this;
        }

        public Criteria andDrugSpecEqualTo(String value) {
            addCriterion("\"drug_spec\" =", value, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDrugSpecNotEqualTo(String value) {
            addCriterion("\"drug_spec\" <>", value, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDrugSpecGreaterThan(String value) {
            addCriterion("\"drug_spec\" >", value, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDrugSpecGreaterThanOrEqualTo(String value) {
            addCriterion("\"drug_spec\" >=", value, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDrugSpecLessThan(String value) {
            addCriterion("\"drug_spec\" <", value, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDrugSpecLessThanOrEqualTo(String value) {
            addCriterion("\"drug_spec\" <=", value, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDrugSpecLike(String value) {
            addCriterion("\"drug_spec\" like", value, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDrugSpecNotLike(String value) {
            addCriterion("\"drug_spec\" not like", value, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDrugSpecIn(List<String> values) {
            addCriterion("\"drug_spec\" in", values, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDrugSpecNotIn(List<String> values) {
            addCriterion("\"drug_spec\" not in", values, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDrugSpecBetween(String value1, String value2) {
            addCriterion("\"drug_spec\" between", value1, value2, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDrugSpecNotBetween(String value1, String value2) {
            addCriterion("\"drug_spec\" not between", value1, value2, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDosageIsNull() {
            addCriterion("\"dosage\" is null");
            return (Criteria) this;
        }

        public Criteria andDosageIsNotNull() {
            addCriterion("\"dosage\" is not null");
            return (Criteria) this;
        }

        public Criteria andDosageEqualTo(Double value) {
            addCriterion("\"dosage\" =", value, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageNotEqualTo(Double value) {
            addCriterion("\"dosage\" <>", value, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageGreaterThan(Double value) {
            addCriterion("\"dosage\" >", value, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageGreaterThanOrEqualTo(Double value) {
            addCriterion("\"dosage\" >=", value, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageLessThan(Double value) {
            addCriterion("\"dosage\" <", value, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageLessThanOrEqualTo(Double value) {
            addCriterion("\"dosage\" <=", value, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageIn(List<Double> values) {
            addCriterion("\"dosage\" in", values, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageNotIn(List<Double> values) {
            addCriterion("\"dosage\" not in", values, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageBetween(Double value1, Double value2) {
            addCriterion("\"dosage\" between", value1, value2, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageNotBetween(Double value1, Double value2) {
            addCriterion("\"dosage\" not between", value1, value2, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsIsNull() {
            addCriterion("\"dosage_units\" is null");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsIsNotNull() {
            addCriterion("\"dosage_units\" is not null");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsEqualTo(String value) {
            addCriterion("\"dosage_units\" =", value, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsNotEqualTo(String value) {
            addCriterion("\"dosage_units\" <>", value, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsGreaterThan(String value) {
            addCriterion("\"dosage_units\" >", value, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsGreaterThanOrEqualTo(String value) {
            addCriterion("\"dosage_units\" >=", value, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsLessThan(String value) {
            addCriterion("\"dosage_units\" <", value, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsLessThanOrEqualTo(String value) {
            addCriterion("\"dosage_units\" <=", value, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsLike(String value) {
            addCriterion("\"dosage_units\" like", value, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsNotLike(String value) {
            addCriterion("\"dosage_units\" not like", value, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsIn(List<String> values) {
            addCriterion("\"dosage_units\" in", values, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsNotIn(List<String> values) {
            addCriterion("\"dosage_units\" not in", values, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsBetween(String value1, String value2) {
            addCriterion("\"dosage_units\" between", value1, value2, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andDosageUnitsNotBetween(String value1, String value2) {
            addCriterion("\"dosage_units\" not between", value1, value2, "dosageUnits");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteIsNull() {
            addCriterion("\"administration_route\" is null");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteIsNotNull() {
            addCriterion("\"administration_route\" is not null");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteEqualTo(String value) {
            addCriterion("\"administration_route\" =", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteNotEqualTo(String value) {
            addCriterion("\"administration_route\" <>", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteGreaterThan(String value) {
            addCriterion("\"administration_route\" >", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteGreaterThanOrEqualTo(String value) {
            addCriterion("\"administration_route\" >=", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteLessThan(String value) {
            addCriterion("\"administration_route\" <", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteLessThanOrEqualTo(String value) {
            addCriterion("\"administration_route\" <=", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteLike(String value) {
            addCriterion("\"administration_route\" like", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteNotLike(String value) {
            addCriterion("\"administration_route\" not like", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteIn(List<String> values) {
            addCriterion("\"administration_route\" in", values, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteNotIn(List<String> values) {
            addCriterion("\"administration_route\" not in", values, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteBetween(String value1, String value2) {
            addCriterion("\"administration_route\" between", value1, value2, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteNotBetween(String value1, String value2) {
            addCriterion("\"administration_route\" not between", value1, value2, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andDurationIsNull() {
            addCriterion("\"duration\" is null");
            return (Criteria) this;
        }

        public Criteria andDurationIsNotNull() {
            addCriterion("\"duration\" is not null");
            return (Criteria) this;
        }

        public Criteria andDurationEqualTo(String value) {
            addCriterion("\"duration\" =", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotEqualTo(String value) {
            addCriterion("\"duration\" <>", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationGreaterThan(String value) {
            addCriterion("\"duration\" >", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationGreaterThanOrEqualTo(String value) {
            addCriterion("\"duration\" >=", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLessThan(String value) {
            addCriterion("\"duration\" <", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLessThanOrEqualTo(String value) {
            addCriterion("\"duration\" <=", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLike(String value) {
            addCriterion("\"duration\" like", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotLike(String value) {
            addCriterion("\"duration\" not like", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationIn(List<String> values) {
            addCriterion("\"duration\" in", values, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotIn(List<String> values) {
            addCriterion("\"duration\" not in", values, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationBetween(String value1, String value2) {
            addCriterion("\"duration\" between", value1, value2, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotBetween(String value1, String value2) {
            addCriterion("\"duration\" not between", value1, value2, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationUnitsIsNull() {
            addCriterion("\"duration_units\" is null");
            return (Criteria) this;
        }

        public Criteria andDurationUnitsIsNotNull() {
            addCriterion("\"duration_units\" is not null");
            return (Criteria) this;
        }

        public Criteria andDurationUnitsEqualTo(String value) {
            addCriterion("\"duration_units\" =", value, "durationUnits");
            return (Criteria) this;
        }

        public Criteria andDurationUnitsNotEqualTo(String value) {
            addCriterion("\"duration_units\" <>", value, "durationUnits");
            return (Criteria) this;
        }

        public Criteria andDurationUnitsGreaterThan(String value) {
            addCriterion("\"duration_units\" >", value, "durationUnits");
            return (Criteria) this;
        }

        public Criteria andDurationUnitsGreaterThanOrEqualTo(String value) {
            addCriterion("\"duration_units\" >=", value, "durationUnits");
            return (Criteria) this;
        }

        public Criteria andDurationUnitsLessThan(String value) {
            addCriterion("\"duration_units\" <", value, "durationUnits");
            return (Criteria) this;
        }

        public Criteria andDurationUnitsLessThanOrEqualTo(String value) {
            addCriterion("\"duration_units\" <=", value, "durationUnits");
            return (Criteria) this;
        }

        public Criteria andDurationUnitsLike(String value) {
            addCriterion("\"duration_units\" like", value, "durationUnits");
            return (Criteria) this;
        }

        public Criteria andDurationUnitsNotLike(String value) {
            addCriterion("\"duration_units\" not like", value, "durationUnits");
            return (Criteria) this;
        }

        public Criteria andDurationUnitsIn(List<String> values) {
            addCriterion("\"duration_units\" in", values, "durationUnits");
            return (Criteria) this;
        }

        public Criteria andDurationUnitsNotIn(List<String> values) {
            addCriterion("\"duration_units\" not in", values, "durationUnits");
            return (Criteria) this;
        }

        public Criteria andDurationUnitsBetween(String value1, String value2) {
            addCriterion("\"duration_units\" between", value1, value2, "durationUnits");
            return (Criteria) this;
        }

        public Criteria andDurationUnitsNotBetween(String value1, String value2) {
            addCriterion("\"duration_units\" not between", value1, value2, "durationUnits");
            return (Criteria) this;
        }

        public Criteria andStartDateTimeIsNull() {
            addCriterion("\"start_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andStartDateTimeIsNotNull() {
            addCriterion("\"start_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andStartDateTimeEqualTo(Date value) {
            addCriterion("\"start_date_time\" =", value, "startDateTime");
            return (Criteria) this;
        }

        public Criteria andStartDateTimeNotEqualTo(Date value) {
            addCriterion("\"start_date_time\" <>", value, "startDateTime");
            return (Criteria) this;
        }

        public Criteria andStartDateTimeGreaterThan(Date value) {
            addCriterion("\"start_date_time\" >", value, "startDateTime");
            return (Criteria) this;
        }

        public Criteria andStartDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"start_date_time\" >=", value, "startDateTime");
            return (Criteria) this;
        }

        public Criteria andStartDateTimeLessThan(Date value) {
            addCriterion("\"start_date_time\" <", value, "startDateTime");
            return (Criteria) this;
        }

        public Criteria andStartDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"start_date_time\" <=", value, "startDateTime");
            return (Criteria) this;
        }

        public Criteria andStartDateTimeIn(List<Date> values) {
            addCriterion("\"start_date_time\" in", values, "startDateTime");
            return (Criteria) this;
        }

        public Criteria andStartDateTimeNotIn(List<Date> values) {
            addCriterion("\"start_date_time\" not in", values, "startDateTime");
            return (Criteria) this;
        }

        public Criteria andStartDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"start_date_time\" between", value1, value2, "startDateTime");
            return (Criteria) this;
        }

        public Criteria andStartDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"start_date_time\" not between", value1, value2, "startDateTime");
            return (Criteria) this;
        }

        public Criteria andStopDateTimeIsNull() {
            addCriterion("\"stop_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andStopDateTimeIsNotNull() {
            addCriterion("\"stop_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andStopDateTimeEqualTo(Date value) {
            addCriterion("\"stop_date_time\" =", value, "stopDateTime");
            return (Criteria) this;
        }

        public Criteria andStopDateTimeNotEqualTo(Date value) {
            addCriterion("\"stop_date_time\" <>", value, "stopDateTime");
            return (Criteria) this;
        }

        public Criteria andStopDateTimeGreaterThan(Date value) {
            addCriterion("\"stop_date_time\" >", value, "stopDateTime");
            return (Criteria) this;
        }

        public Criteria andStopDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"stop_date_time\" >=", value, "stopDateTime");
            return (Criteria) this;
        }

        public Criteria andStopDateTimeLessThan(Date value) {
            addCriterion("\"stop_date_time\" <", value, "stopDateTime");
            return (Criteria) this;
        }

        public Criteria andStopDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"stop_date_time\" <=", value, "stopDateTime");
            return (Criteria) this;
        }

        public Criteria andStopDateTimeIn(List<Date> values) {
            addCriterion("\"stop_date_time\" in", values, "stopDateTime");
            return (Criteria) this;
        }

        public Criteria andStopDateTimeNotIn(List<Date> values) {
            addCriterion("\"stop_date_time\" not in", values, "stopDateTime");
            return (Criteria) this;
        }

        public Criteria andStopDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"stop_date_time\" between", value1, value2, "stopDateTime");
            return (Criteria) this;
        }

        public Criteria andStopDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"stop_date_time\" not between", value1, value2, "stopDateTime");
            return (Criteria) this;
        }

        public Criteria andFrequencyIsNull() {
            addCriterion("\"frequency\" is null");
            return (Criteria) this;
        }

        public Criteria andFrequencyIsNotNull() {
            addCriterion("\"frequency\" is not null");
            return (Criteria) this;
        }

        public Criteria andFrequencyEqualTo(String value) {
            addCriterion("\"frequency\" =", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyNotEqualTo(String value) {
            addCriterion("\"frequency\" <>", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyGreaterThan(String value) {
            addCriterion("\"frequency\" >", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyGreaterThanOrEqualTo(String value) {
            addCriterion("\"frequency\" >=", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyLessThan(String value) {
            addCriterion("\"frequency\" <", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyLessThanOrEqualTo(String value) {
            addCriterion("\"frequency\" <=", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyLike(String value) {
            addCriterion("\"frequency\" like", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyNotLike(String value) {
            addCriterion("\"frequency\" not like", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyIn(List<String> values) {
            addCriterion("\"frequency\" in", values, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyNotIn(List<String> values) {
            addCriterion("\"frequency\" not in", values, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyBetween(String value1, String value2) {
            addCriterion("\"frequency\" between", value1, value2, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyNotBetween(String value1, String value2) {
            addCriterion("\"frequency\" not between", value1, value2, "frequency");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalIsNull() {
            addCriterion("\"freq_interval\" is null");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalIsNotNull() {
            addCriterion("\"freq_interval\" is not null");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalEqualTo(String value) {
            addCriterion("\"freq_interval\" =", value, "freqInterval");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalNotEqualTo(String value) {
            addCriterion("\"freq_interval\" <>", value, "freqInterval");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalGreaterThan(String value) {
            addCriterion("\"freq_interval\" >", value, "freqInterval");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalGreaterThanOrEqualTo(String value) {
            addCriterion("\"freq_interval\" >=", value, "freqInterval");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalLessThan(String value) {
            addCriterion("\"freq_interval\" <", value, "freqInterval");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalLessThanOrEqualTo(String value) {
            addCriterion("\"freq_interval\" <=", value, "freqInterval");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalLike(String value) {
            addCriterion("\"freq_interval\" like", value, "freqInterval");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalNotLike(String value) {
            addCriterion("\"freq_interval\" not like", value, "freqInterval");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalIn(List<String> values) {
            addCriterion("\"freq_interval\" in", values, "freqInterval");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalNotIn(List<String> values) {
            addCriterion("\"freq_interval\" not in", values, "freqInterval");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalBetween(String value1, String value2) {
            addCriterion("\"freq_interval\" between", value1, value2, "freqInterval");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalNotBetween(String value1, String value2) {
            addCriterion("\"freq_interval\" not between", value1, value2, "freqInterval");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalUnitIsNull() {
            addCriterion("\"freq_interval_unit\" is null");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalUnitIsNotNull() {
            addCriterion("\"freq_interval_unit\" is not null");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalUnitEqualTo(String value) {
            addCriterion("\"freq_interval_unit\" =", value, "freqIntervalUnit");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalUnitNotEqualTo(String value) {
            addCriterion("\"freq_interval_unit\" <>", value, "freqIntervalUnit");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalUnitGreaterThan(String value) {
            addCriterion("\"freq_interval_unit\" >", value, "freqIntervalUnit");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalUnitGreaterThanOrEqualTo(String value) {
            addCriterion("\"freq_interval_unit\" >=", value, "freqIntervalUnit");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalUnitLessThan(String value) {
            addCriterion("\"freq_interval_unit\" <", value, "freqIntervalUnit");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalUnitLessThanOrEqualTo(String value) {
            addCriterion("\"freq_interval_unit\" <=", value, "freqIntervalUnit");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalUnitLike(String value) {
            addCriterion("\"freq_interval_unit\" like", value, "freqIntervalUnit");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalUnitNotLike(String value) {
            addCriterion("\"freq_interval_unit\" not like", value, "freqIntervalUnit");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalUnitIn(List<String> values) {
            addCriterion("\"freq_interval_unit\" in", values, "freqIntervalUnit");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalUnitNotIn(List<String> values) {
            addCriterion("\"freq_interval_unit\" not in", values, "freqIntervalUnit");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalUnitBetween(String value1, String value2) {
            addCriterion("\"freq_interval_unit\" between", value1, value2, "freqIntervalUnit");
            return (Criteria) this;
        }

        public Criteria andFreqIntervalUnitNotBetween(String value1, String value2) {
            addCriterion("\"freq_interval_unit\" not between", value1, value2, "freqIntervalUnit");
            return (Criteria) this;
        }

        public Criteria andFreqDetailIsNull() {
            addCriterion("\"freq_detail\" is null");
            return (Criteria) this;
        }

        public Criteria andFreqDetailIsNotNull() {
            addCriterion("\"freq_detail\" is not null");
            return (Criteria) this;
        }

        public Criteria andFreqDetailEqualTo(String value) {
            addCriterion("\"freq_detail\" =", value, "freqDetail");
            return (Criteria) this;
        }

        public Criteria andFreqDetailNotEqualTo(String value) {
            addCriterion("\"freq_detail\" <>", value, "freqDetail");
            return (Criteria) this;
        }

        public Criteria andFreqDetailGreaterThan(String value) {
            addCriterion("\"freq_detail\" >", value, "freqDetail");
            return (Criteria) this;
        }

        public Criteria andFreqDetailGreaterThanOrEqualTo(String value) {
            addCriterion("\"freq_detail\" >=", value, "freqDetail");
            return (Criteria) this;
        }

        public Criteria andFreqDetailLessThan(String value) {
            addCriterion("\"freq_detail\" <", value, "freqDetail");
            return (Criteria) this;
        }

        public Criteria andFreqDetailLessThanOrEqualTo(String value) {
            addCriterion("\"freq_detail\" <=", value, "freqDetail");
            return (Criteria) this;
        }

        public Criteria andFreqDetailLike(String value) {
            addCriterion("\"freq_detail\" like", value, "freqDetail");
            return (Criteria) this;
        }

        public Criteria andFreqDetailNotLike(String value) {
            addCriterion("\"freq_detail\" not like", value, "freqDetail");
            return (Criteria) this;
        }

        public Criteria andFreqDetailIn(List<String> values) {
            addCriterion("\"freq_detail\" in", values, "freqDetail");
            return (Criteria) this;
        }

        public Criteria andFreqDetailNotIn(List<String> values) {
            addCriterion("\"freq_detail\" not in", values, "freqDetail");
            return (Criteria) this;
        }

        public Criteria andFreqDetailBetween(String value1, String value2) {
            addCriterion("\"freq_detail\" between", value1, value2, "freqDetail");
            return (Criteria) this;
        }

        public Criteria andFreqDetailNotBetween(String value1, String value2) {
            addCriterion("\"freq_detail\" not between", value1, value2, "freqDetail");
            return (Criteria) this;
        }

        public Criteria andPerformScheduleIsNull() {
            addCriterion("\"perform_schedule\" is null");
            return (Criteria) this;
        }

        public Criteria andPerformScheduleIsNotNull() {
            addCriterion("\"perform_schedule\" is not null");
            return (Criteria) this;
        }

        public Criteria andPerformScheduleEqualTo(Date value) {
            addCriterion("\"perform_schedule\" =", value, "performSchedule");
            return (Criteria) this;
        }

        public Criteria andPerformScheduleNotEqualTo(Date value) {
            addCriterion("\"perform_schedule\" <>", value, "performSchedule");
            return (Criteria) this;
        }

        public Criteria andPerformScheduleGreaterThan(Date value) {
            addCriterion("\"perform_schedule\" >", value, "performSchedule");
            return (Criteria) this;
        }

        public Criteria andPerformScheduleGreaterThanOrEqualTo(Date value) {
            addCriterion("\"perform_schedule\" >=", value, "performSchedule");
            return (Criteria) this;
        }

        public Criteria andPerformScheduleLessThan(Date value) {
            addCriterion("\"perform_schedule\" <", value, "performSchedule");
            return (Criteria) this;
        }

        public Criteria andPerformScheduleLessThanOrEqualTo(Date value) {
            addCriterion("\"perform_schedule\" <=", value, "performSchedule");
            return (Criteria) this;
        }

        public Criteria andPerformScheduleIn(List<Date> values) {
            addCriterion("\"perform_schedule\" in", values, "performSchedule");
            return (Criteria) this;
        }

        public Criteria andPerformScheduleNotIn(List<Date> values) {
            addCriterion("\"perform_schedule\" not in", values, "performSchedule");
            return (Criteria) this;
        }

        public Criteria andPerformScheduleBetween(Date value1, Date value2) {
            addCriterion("\"perform_schedule\" between", value1, value2, "performSchedule");
            return (Criteria) this;
        }

        public Criteria andPerformScheduleNotBetween(Date value1, Date value2) {
            addCriterion("\"perform_schedule\" not between", value1, value2, "performSchedule");
            return (Criteria) this;
        }

        public Criteria andOrderDeptIsNull() {
            addCriterion("\"order_dept\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderDeptIsNotNull() {
            addCriterion("\"order_dept\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderDeptEqualTo(String value) {
            addCriterion("\"order_dept\" =", value, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptNotEqualTo(String value) {
            addCriterion("\"order_dept\" <>", value, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptGreaterThan(String value) {
            addCriterion("\"order_dept\" >", value, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptGreaterThanOrEqualTo(String value) {
            addCriterion("\"order_dept\" >=", value, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptLessThan(String value) {
            addCriterion("\"order_dept\" <", value, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptLessThanOrEqualTo(String value) {
            addCriterion("\"order_dept\" <=", value, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptLike(String value) {
            addCriterion("\"order_dept\" like", value, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptNotLike(String value) {
            addCriterion("\"order_dept\" not like", value, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptIn(List<String> values) {
            addCriterion("\"order_dept\" in", values, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptNotIn(List<String> values) {
            addCriterion("\"order_dept\" not in", values, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptBetween(String value1, String value2) {
            addCriterion("\"order_dept\" between", value1, value2, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptNotBetween(String value1, String value2) {
            addCriterion("\"order_dept\" not between", value1, value2, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDoctorIsNull() {
            addCriterion("\"order_doctor\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderDoctorIsNotNull() {
            addCriterion("\"order_doctor\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderDoctorEqualTo(String value) {
            addCriterion("\"order_doctor\" =", value, "orderDoctor");
            return (Criteria) this;
        }

        public Criteria andOrderDoctorNotEqualTo(String value) {
            addCriterion("\"order_doctor\" <>", value, "orderDoctor");
            return (Criteria) this;
        }

        public Criteria andOrderDoctorGreaterThan(String value) {
            addCriterion("\"order_doctor\" >", value, "orderDoctor");
            return (Criteria) this;
        }

        public Criteria andOrderDoctorGreaterThanOrEqualTo(String value) {
            addCriterion("\"order_doctor\" >=", value, "orderDoctor");
            return (Criteria) this;
        }

        public Criteria andOrderDoctorLessThan(String value) {
            addCriterion("\"order_doctor\" <", value, "orderDoctor");
            return (Criteria) this;
        }

        public Criteria andOrderDoctorLessThanOrEqualTo(String value) {
            addCriterion("\"order_doctor\" <=", value, "orderDoctor");
            return (Criteria) this;
        }

        public Criteria andOrderDoctorLike(String value) {
            addCriterion("\"order_doctor\" like", value, "orderDoctor");
            return (Criteria) this;
        }

        public Criteria andOrderDoctorNotLike(String value) {
            addCriterion("\"order_doctor\" not like", value, "orderDoctor");
            return (Criteria) this;
        }

        public Criteria andOrderDoctorIn(List<String> values) {
            addCriterion("\"order_doctor\" in", values, "orderDoctor");
            return (Criteria) this;
        }

        public Criteria andOrderDoctorNotIn(List<String> values) {
            addCriterion("\"order_doctor\" not in", values, "orderDoctor");
            return (Criteria) this;
        }

        public Criteria andOrderDoctorBetween(String value1, String value2) {
            addCriterion("\"order_doctor\" between", value1, value2, "orderDoctor");
            return (Criteria) this;
        }

        public Criteria andOrderDoctorNotBetween(String value1, String value2) {
            addCriterion("\"order_doctor\" not between", value1, value2, "orderDoctor");
            return (Criteria) this;
        }

        public Criteria andEnterDateTimeIsNull() {
            addCriterion("\"enter_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andEnterDateTimeIsNotNull() {
            addCriterion("\"enter_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andEnterDateTimeEqualTo(Date value) {
            addCriterion("\"enter_date_time\" =", value, "enterDateTime");
            return (Criteria) this;
        }

        public Criteria andEnterDateTimeNotEqualTo(Date value) {
            addCriterion("\"enter_date_time\" <>", value, "enterDateTime");
            return (Criteria) this;
        }

        public Criteria andEnterDateTimeGreaterThan(Date value) {
            addCriterion("\"enter_date_time\" >", value, "enterDateTime");
            return (Criteria) this;
        }

        public Criteria andEnterDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"enter_date_time\" >=", value, "enterDateTime");
            return (Criteria) this;
        }

        public Criteria andEnterDateTimeLessThan(Date value) {
            addCriterion("\"enter_date_time\" <", value, "enterDateTime");
            return (Criteria) this;
        }

        public Criteria andEnterDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"enter_date_time\" <=", value, "enterDateTime");
            return (Criteria) this;
        }

        public Criteria andEnterDateTimeIn(List<Date> values) {
            addCriterion("\"enter_date_time\" in", values, "enterDateTime");
            return (Criteria) this;
        }

        public Criteria andEnterDateTimeNotIn(List<Date> values) {
            addCriterion("\"enter_date_time\" not in", values, "enterDateTime");
            return (Criteria) this;
        }

        public Criteria andEnterDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"enter_date_time\" between", value1, value2, "enterDateTime");
            return (Criteria) this;
        }

        public Criteria andEnterDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"enter_date_time\" not between", value1, value2, "enterDateTime");
            return (Criteria) this;
        }

        public Criteria andStopOrderDateTimeIsNull() {
            addCriterion("\"stop_order_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andStopOrderDateTimeIsNotNull() {
            addCriterion("\"stop_order_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andStopOrderDateTimeEqualTo(Date value) {
            addCriterion("\"stop_order_date_time\" =", value, "stopOrderDateTime");
            return (Criteria) this;
        }

        public Criteria andStopOrderDateTimeNotEqualTo(Date value) {
            addCriterion("\"stop_order_date_time\" <>", value, "stopOrderDateTime");
            return (Criteria) this;
        }

        public Criteria andStopOrderDateTimeGreaterThan(Date value) {
            addCriterion("\"stop_order_date_time\" >", value, "stopOrderDateTime");
            return (Criteria) this;
        }

        public Criteria andStopOrderDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"stop_order_date_time\" >=", value, "stopOrderDateTime");
            return (Criteria) this;
        }

        public Criteria andStopOrderDateTimeLessThan(Date value) {
            addCriterion("\"stop_order_date_time\" <", value, "stopOrderDateTime");
            return (Criteria) this;
        }

        public Criteria andStopOrderDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"stop_order_date_time\" <=", value, "stopOrderDateTime");
            return (Criteria) this;
        }

        public Criteria andStopOrderDateTimeIn(List<Date> values) {
            addCriterion("\"stop_order_date_time\" in", values, "stopOrderDateTime");
            return (Criteria) this;
        }

        public Criteria andStopOrderDateTimeNotIn(List<Date> values) {
            addCriterion("\"stop_order_date_time\" not in", values, "stopOrderDateTime");
            return (Criteria) this;
        }

        public Criteria andStopOrderDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"stop_order_date_time\" between", value1, value2, "stopOrderDateTime");
            return (Criteria) this;
        }

        public Criteria andStopOrderDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"stop_order_date_time\" not between", value1, value2, "stopOrderDateTime");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNull() {
            addCriterion("\"order_status\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNotNull() {
            addCriterion("\"order_status\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusEqualTo(String value) {
            addCriterion("\"order_status\" =", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotEqualTo(String value) {
            addCriterion("\"order_status\" <>", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThan(String value) {
            addCriterion("\"order_status\" >", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThanOrEqualTo(String value) {
            addCriterion("\"order_status\" >=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThan(String value) {
            addCriterion("\"order_status\" <", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThanOrEqualTo(String value) {
            addCriterion("\"order_status\" <=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLike(String value) {
            addCriterion("\"order_status\" like", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotLike(String value) {
            addCriterion("\"order_status\" not like", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIn(List<String> values) {
            addCriterion("\"order_status\" in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotIn(List<String> values) {
            addCriterion("\"order_status\" not in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusBetween(String value1, String value2) {
            addCriterion("\"order_status\" between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotBetween(String value1, String value2) {
            addCriterion("\"order_status\" not between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderMemoIsNull() {
            addCriterion("\"order_memo\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderMemoIsNotNull() {
            addCriterion("\"order_memo\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderMemoEqualTo(String value) {
            addCriterion("\"order_memo\" =", value, "orderMemo");
            return (Criteria) this;
        }

        public Criteria andOrderMemoNotEqualTo(String value) {
            addCriterion("\"order_memo\" <>", value, "orderMemo");
            return (Criteria) this;
        }

        public Criteria andOrderMemoGreaterThan(String value) {
            addCriterion("\"order_memo\" >", value, "orderMemo");
            return (Criteria) this;
        }

        public Criteria andOrderMemoGreaterThanOrEqualTo(String value) {
            addCriterion("\"order_memo\" >=", value, "orderMemo");
            return (Criteria) this;
        }

        public Criteria andOrderMemoLessThan(String value) {
            addCriterion("\"order_memo\" <", value, "orderMemo");
            return (Criteria) this;
        }

        public Criteria andOrderMemoLessThanOrEqualTo(String value) {
            addCriterion("\"order_memo\" <=", value, "orderMemo");
            return (Criteria) this;
        }

        public Criteria andOrderMemoLike(String value) {
            addCriterion("\"order_memo\" like", value, "orderMemo");
            return (Criteria) this;
        }

        public Criteria andOrderMemoNotLike(String value) {
            addCriterion("\"order_memo\" not like", value, "orderMemo");
            return (Criteria) this;
        }

        public Criteria andOrderMemoIn(List<String> values) {
            addCriterion("\"order_memo\" in", values, "orderMemo");
            return (Criteria) this;
        }

        public Criteria andOrderMemoNotIn(List<String> values) {
            addCriterion("\"order_memo\" not in", values, "orderMemo");
            return (Criteria) this;
        }

        public Criteria andOrderMemoBetween(String value1, String value2) {
            addCriterion("\"order_memo\" between", value1, value2, "orderMemo");
            return (Criteria) this;
        }

        public Criteria andOrderMemoNotBetween(String value1, String value2) {
            addCriterion("\"order_memo\" not between", value1, value2, "orderMemo");
            return (Criteria) this;
        }

        public Criteria andCancelDateTimeIsNull() {
            addCriterion("\"cancel_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andCancelDateTimeIsNotNull() {
            addCriterion("\"cancel_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andCancelDateTimeEqualTo(Date value) {
            addCriterion("\"cancel_date_time\" =", value, "cancelDateTime");
            return (Criteria) this;
        }

        public Criteria andCancelDateTimeNotEqualTo(Date value) {
            addCriterion("\"cancel_date_time\" <>", value, "cancelDateTime");
            return (Criteria) this;
        }

        public Criteria andCancelDateTimeGreaterThan(Date value) {
            addCriterion("\"cancel_date_time\" >", value, "cancelDateTime");
            return (Criteria) this;
        }

        public Criteria andCancelDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"cancel_date_time\" >=", value, "cancelDateTime");
            return (Criteria) this;
        }

        public Criteria andCancelDateTimeLessThan(Date value) {
            addCriterion("\"cancel_date_time\" <", value, "cancelDateTime");
            return (Criteria) this;
        }

        public Criteria andCancelDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"cancel_date_time\" <=", value, "cancelDateTime");
            return (Criteria) this;
        }

        public Criteria andCancelDateTimeIn(List<Date> values) {
            addCriterion("\"cancel_date_time\" in", values, "cancelDateTime");
            return (Criteria) this;
        }

        public Criteria andCancelDateTimeNotIn(List<Date> values) {
            addCriterion("\"cancel_date_time\" not in", values, "cancelDateTime");
            return (Criteria) this;
        }

        public Criteria andCancelDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"cancel_date_time\" between", value1, value2, "cancelDateTime");
            return (Criteria) this;
        }

        public Criteria andCancelDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"cancel_date_time\" not between", value1, value2, "cancelDateTime");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNull() {
            addCriterion("\"source_path\" is null");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNotNull() {
            addCriterion("\"source_path\" is not null");
            return (Criteria) this;
        }

        public Criteria andSourcePathEqualTo(String value) {
            addCriterion("\"source_path\" =", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotEqualTo(String value) {
            addCriterion("\"source_path\" <>", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThan(String value) {
            addCriterion("\"source_path\" >", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThanOrEqualTo(String value) {
            addCriterion("\"source_path\" >=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThan(String value) {
            addCriterion("\"source_path\" <", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThanOrEqualTo(String value) {
            addCriterion("\"source_path\" <=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLike(String value) {
            addCriterion("\"source_path\" like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotLike(String value) {
            addCriterion("\"source_path\" not like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathIn(List<String> values) {
            addCriterion("\"source_path\" in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotIn(List<String> values) {
            addCriterion("\"source_path\" not in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathBetween(String value1, String value2) {
            addCriterion("\"source_path\" between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotBetween(String value1, String value2) {
            addCriterion("\"source_path\" not between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNull() {
            addCriterion("\"pk_id\" is null");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNotNull() {
            addCriterion("\"pk_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkIdEqualTo(String value) {
            addCriterion("\"pk_id\" =", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotEqualTo(String value) {
            addCriterion("\"pk_id\" <>", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThan(String value) {
            addCriterion("\"pk_id\" >", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" >=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThan(String value) {
            addCriterion("\"pk_id\" <", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" <=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLike(String value) {
            addCriterion("\"pk_id\" like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotLike(String value) {
            addCriterion("\"pk_id\" not like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdIn(List<String> values) {
            addCriterion("\"pk_id\" in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotIn(List<String> values) {
            addCriterion("\"pk_id\" not in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdBetween(String value1, String value2) {
            addCriterion("\"pk_id\" between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotBetween(String value1, String value2) {
            addCriterion("\"pk_id\" not between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNull() {
            addCriterion("\"data_state\" is null");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNotNull() {
            addCriterion("\"data_state\" is not null");
            return (Criteria) this;
        }

        public Criteria andDataStateEqualTo(String value) {
            addCriterion("\"data_state\" =", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotEqualTo(String value) {
            addCriterion("\"data_state\" <>", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThan(String value) {
            addCriterion("\"data_state\" >", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThanOrEqualTo(String value) {
            addCriterion("\"data_state\" >=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThan(String value) {
            addCriterion("\"data_state\" <", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThanOrEqualTo(String value) {
            addCriterion("\"data_state\" <=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLike(String value) {
            addCriterion("\"data_state\" like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotLike(String value) {
            addCriterion("\"data_state\" not like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateIn(List<String> values) {
            addCriterion("\"data_state\" in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotIn(List<String> values) {
            addCriterion("\"data_state\" not in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateBetween(String value1, String value2) {
            addCriterion("\"data_state\" between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotBetween(String value1, String value2) {
            addCriterion("\"data_state\" not between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIsNull() {
            addCriterion("\"patient_sn_org\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIsNotNull() {
            addCriterion("\"patient_sn_org\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgEqualTo(String value) {
            addCriterion("\"patient_sn_org\" =", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotEqualTo(String value) {
            addCriterion("\"patient_sn_org\" <>", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgGreaterThan(String value) {
            addCriterion("\"patient_sn_org\" >", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn_org\" >=", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLessThan(String value) {
            addCriterion("\"patient_sn_org\" <", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn_org\" <=", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLike(String value) {
            addCriterion("\"patient_sn_org\" like", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotLike(String value) {
            addCriterion("\"patient_sn_org\" not like", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIn(List<String> values) {
            addCriterion("\"patient_sn_org\" in", values, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotIn(List<String> values) {
            addCriterion("\"patient_sn_org\" not in", values, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgBetween(String value1, String value2) {
            addCriterion("\"patient_sn_org\" between", value1, value2, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn_org\" not between", value1, value2, "patientSnOrg");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}