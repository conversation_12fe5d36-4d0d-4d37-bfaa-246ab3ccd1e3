package com.haoys.rdr.mapper;

import com.haoys.rdr.model.MrHomepage;
import com.haoys.rdr.model.MrHomepageExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MrHomepageMapper {
    long countByExample(MrHomepageExample example);

    int deleteByExample(MrHomepageExample example);

    int deleteByPrimaryKey(String pkId);

    int insert(MrHomepage record);

    int insertSelective(MrHomepage record);

    List<MrHomepage> selectByExample(MrHomepageExample example);

    MrHomepage selectByPrimaryKey(String pkId);

    int updateByExampleSelective(@Param("record") MrHomepage record, @Param("example") MrHomepageExample example);

    int updateByExample(@Param("record") MrHomepage record, @Param("example") MrHomepageExample example);

    int updateByPrimaryKeySelective(MrHomepage record);

    int updateByPrimaryKey(MrHomepage record);
    
    List<MrHomepage> getPatientMrHomepage(String patientId);
    
    MrHomepage getMrHomepageByPatientId(String patientId);
}
