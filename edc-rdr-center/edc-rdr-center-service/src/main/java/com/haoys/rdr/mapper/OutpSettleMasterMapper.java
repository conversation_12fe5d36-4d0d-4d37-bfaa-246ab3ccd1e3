package com.haoys.rdr.mapper;

import com.haoys.rdr.model.OutpSettleMaster;
import com.haoys.rdr.model.OutpSettleMasterExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface OutpSettleMasterMapper {
    long countByExample(OutpSettleMasterExample example);

    int deleteByExample(OutpSettleMasterExample example);

    int deleteByPrimaryKey(String pkId);

    int insert(OutpSettleMaster record);

    int insertSelective(OutpSettleMaster record);

    List<OutpSettleMaster> selectByExample(OutpSettleMasterExample example);

    OutpSettleMaster selectByPrimaryKey(String pkId);

    int updateByExampleSelective(@Param("record") OutpSettleMaster record, @Param("example") OutpSettleMasterExample example);

    int updateByExample(@Param("record") OutpSettleMaster record, @Param("example") OutpSettleMasterExample example);

    int updateByPrimaryKeySelective(OutpSettleMaster record);

    int updateByPrimaryKey(OutpSettleMaster record);
}