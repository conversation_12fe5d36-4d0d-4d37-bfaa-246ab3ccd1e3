package com.haoys.rdr.service;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.elasticsearch.TreeSearchParam;

import java.io.IOException;
import java.util.Map;

public interface RdrPatientTreeSearchService {
    CommonResult<Map<String, Object>> list(TreeSearchParam param) throws IOException;

    /**
     * 导出功能
     * @param param
     * @return
     */
    CommonResult<Map<String, Object>> export(TreeSearchParam param) throws IOException;

}
