package com.haoys.rdr.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.*;
import co.elastic.clients.elasticsearch.core.*;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.json.JsonData;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.constants.DefineConstant;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.config.RdrDataCenterConfig;
import com.haoys.user.elasticsearch.SearchDto;
import com.haoys.rdr.domain.dto.SearchGroupDto;
import com.haoys.user.elasticsearch.SearchTypeEnum;
import com.haoys.rdr.domain.param.SearchParam;
import com.haoys.rdr.mapper.RdrPatientDataBaseMapper;
import com.haoys.rdr.mapper.RdrPatientDataBaseRecordMapper;
import com.haoys.rdr.mapper.RdrPatientNaPiSearchMapper;
import com.haoys.rdr.model.*;
import com.haoys.rdr.service.RdrPatientNaPiSearchRdrEsService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

@RequiredArgsConstructor(onConstructor = @__(@Lazy))
@DS("disease-rdr")
@Service("search")
public class RdrPatientNaPiSearchRdrEsServiceImpl implements RdrPatientNaPiSearchRdrEsService {

    private final RdrPatientNaPiSearchMapper patientNaPiSearchMapper;
    private final RdrPatientDataBaseMapper patientDataBaseMapper;
    private final RdrDataCenterConfig rdrDataCenterConfig;
    @Autowired
    private RdrPatientDataBaseRecordMapper dataBaseRecordMapper;
    @Resource(name = "clientByPasswd")
    private ElasticsearchClient client;


    /**
     * 病历视图 排序字段
     */
    private static String VISIT_SORT_FIELD="visitSn";

    /**
     * 患者视图 排序字段
     */
    private static String PATIENT_SORT_FIELD="patientSn";

    /**
     * 列表查询要展示的信息的表名称，对个用逗号隔开
     */
    private static String PATIENT_SOURCE="patients,visitInformation,mrHomepage";


    @Override
    public CommonResult list(SearchParam param) throws IOException {
        Map<String, Object> map = new HashMap<>();
        CommonPage<Map<String, Object>> commonPage = new CommonPage<>();

        List<String> ids = new ArrayList<>();
        // 获取数据
        if (StringUtils.isNotEmpty(param.getDataBaseId())) {
            RdrPatientNaPiSearch naPiSearch;
            if (StringUtils.isNotEmpty(param.getDataBaseId())) {
                RdrPatientDataBaseRecordExample example = new RdrPatientDataBaseRecordExample();
                RdrPatientDataBaseRecordExample.Criteria criteria = example.createCriteria();
                criteria.andDataBaseIdEqualTo(param.getDataBaseId());
                List<RdrPatientDataBaseRecord> records = dataBaseRecordMapper.selectByExample(example);
                if(CollectionUtil.isNotEmpty(records)){
                    records.forEach(f->ids.add(f.getPatientSn()));
                }
            }
            if(StringUtils.isNotEmpty(param.getSearchId())){
                naPiSearch = getById(param.getSearchId());
            } else {
                naPiSearch = null;
            }
            // 构建es搜索条件
            SearchRequest request = SearchRequest.of(i -> {
                SearchRequest.Builder builder = i.index(rdrDataCenterConfig.getPatient_join_visit_index())
                        .from((param.getPageNum()-1)* param.getPageSize())
                        .size(param.getPageSize())
                        .source(s->s.filter(f->f.includes(StrUtil.split(PATIENT_SOURCE,","))))
                        .sort(s->s.field(f->f.field(PATIENT_SORT_FIELD).order(SortOrder.Asc)));
                Query query = buildQuery(naPiSearch, ids, "patient");
                builder.query(query);
                return builder;
            });

            CountRequest countRequest = CountRequest.of(i -> {
                CountRequest.Builder builder = i.index(rdrDataCenterConfig.getPatient_join_visit_index());
                Query query = buildQuery(naPiSearch,ids,"patient");
                builder.query(query);
                return builder;
            });

            SearchResponse<JSONObject> response = client.search(request, JSONObject.class);
            CountResponse count = client.count(countRequest);
            List<Hit<JSONObject>> hits = response.hits().hits();

            if (CollectionUtil.isNotEmpty(hits)){
                // 从es数据库中获取到数据后，处理成前端所需要的数据格式
                List<Map<String, Object>> list = buildSearchData(hits);
                commonPage.setList(list);
                commonPage.setTotal(count.count());
            }else {
                commonPage.setList(new ArrayList<>());
                commonPage.setTotal(0l);
            }
        }

        commonPage.setPageNum(param.getPageNum());
        commonPage.setPageSize(param.getPageSize());
        map.put("data", commonPage);
        return CommonResult.success(map);
    }

    @Override
    public CommonResult<Object> countVisit(SearchParam param) {
        // 获取数据

        List<String> ids = new ArrayList<>();

        RdrPatientNaPiSearch naPiSearch;
        if (StringUtils.isNotEmpty(param.getDataBaseId())) {
            RdrPatientDataBaseRecordExample example = new RdrPatientDataBaseRecordExample();
            RdrPatientDataBaseRecordExample.Criteria criteria = example.createCriteria();
            criteria.andDataBaseIdEqualTo(param.getDataBaseId());
            List<RdrPatientDataBaseRecord> records = dataBaseRecordMapper.selectByExample(example);
            if (CollectionUtil.isNotEmpty(records)) {
                records.forEach(f -> ids.add(f.getPatientSn()));
            }
        }
        if (StringUtils.isNotEmpty(param.getSearchId())) {
            naPiSearch = getById(param.getSearchId());
        } else {
            naPiSearch = null;
        }
        // 构建es搜索条件
        CountRequest request = CountRequest.of(i -> {
            CountRequest.Builder builder = i.index(rdrDataCenterConfig.getPatient_join_visit_index());
            buildCountQuery(builder, naPiSearch, ids);
            return builder;
        });
        // 从es中获取数据
        try {
            CountResponse count = client.count(request);
            return CommonResult.success(count.count());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return CommonResult.success(0);
    }

    @Override
    public CommonResult<Object> visitList(SearchParam param) throws IOException {
        Map<String, Object> map = new HashMap<>();
        CommonPage<Map<String, Object>> commonPage = new CommonPage<>();

        List<String> ids = new ArrayList<>();

        // 获取数据
        if (StringUtils.isNotEmpty(param.getDataBaseId())) {
            RdrPatientNaPiSearch naPiSearch;
            if (StringUtils.isNotEmpty(param.getDataBaseId())) {
                RdrPatientDataBaseRecordExample example = new RdrPatientDataBaseRecordExample();
                RdrPatientDataBaseRecordExample.Criteria criteria = example.createCriteria();
                criteria.andDataBaseIdEqualTo(param.getDataBaseId());
                List<RdrPatientDataBaseRecord> records = dataBaseRecordMapper.selectByExample(example);
                if(CollectionUtil.isNotEmpty(records)){
                    records.forEach(f->ids.add(f.getPatientSn()));
                }
            }
            if(StringUtils.isNotEmpty(param.getSearchId())){
                naPiSearch = getById(param.getSearchId());
            } else {
                naPiSearch = null;
            }
            // 构建es搜索条件
            SearchRequest request = SearchRequest.of(i -> {
                SearchRequest.Builder builder = i.index(rdrDataCenterConfig.getPatient_join_visit_index()).from((param.getPageNum()-1)* param.getPageSize())
                        .size(param.getPageSize()).source(s->s.filter(f->f.includes(StrUtil.split(PATIENT_SOURCE,",")))).sort(s->s.field(f->f.field(VISIT_SORT_FIELD).order(SortOrder.Asc)));
                Query query = buildQuery(naPiSearch, ids, "visit");
                builder.query(query);
                return builder;
            });
            // 从es中获取数据
            SearchResponse<JSONObject> response = client.search(request, JSONObject.class);
            List<Hit<JSONObject>> hits = response.hits().hits();

            if (CollectionUtil.isNotEmpty(hits)){
                // 从es数据库中获取到数据后，处理成前端所需要的数据格式
                List<Map<String, Object>> list = buildSearchData(hits);
                commonPage.setList(list);
                commonPage.setTotal(response.hits().total().value());
            }else {
                commonPage.setList(new ArrayList<>());
                commonPage.setTotal(0l);
            }
        }

        commonPage.setPageNum(param.getPageNum());
        commonPage.setPageSize(param.getPageSize());
        map.put("data", commonPage);
        return CommonResult.success(map);
    }

    /**
     * 单次就诊信息
     * @param visitSn 就诊流水号
     * @return
     */
    @Override
    public CommonResult<Object> visitInfo(String visitSn) {
        return null;
    }

    /**
     * 从es数据库中获取到数据后，处理成前端所需要的数据格式
     * @param hits 从es数据库中获取到的数据
     */
    private static List<Map<String, Object>> buildSearchData(List<Hit<JSONObject>> hits) {
        List<Map<String, Object>> list = new ArrayList<>();
        for (Hit<JSONObject> hit : hits) {
            Map<String, Object> dataMap = JSONObject.parseObject(hit.source().toJSONString(), new TypeReference<Map<String, Object>>(){}, new com.alibaba.fastjson.parser.Feature[]{});
            Map<String, Object> patients = (Map<String, Object>) dataMap.get("patients");
            patients.put("selected",false);
            list.add(dataMap);
        }
        return list;
    }


    private Query buildQuery(RdrPatientNaPiSearch naSearch, List<String> ids, String docType) {
        BoolQuery.Builder bd = new BoolQuery.Builder();
        bd.must(qy -> qy.term(t->t.field("docType").value(docType)));
        if (docType.equals("visit")){
            List<FieldValue> values = new ArrayList<>();
            ids.forEach(id->values.add(FieldValue.of(id)));
            bd.must(m -> m.terms(t -> t.field("patientSn").terms(s -> s.value(values))));
        }else {
            if(CollectionUtil.isNotEmpty(ids)){
                bd.must(qy -> qy.ids(id->id.values(ids)));
            }
        }

        if (naSearch != null) {
            // 纳入条件
            List<SearchGroupDto> naSearchList = JSON.parseArray(naSearch.getNaSearch(), SearchGroupDto.class);
            if (CollectionUtil.isNotEmpty(naSearchList)) {


                List<BoolQuery.Builder> boolQueryList = new ArrayList<>();

                for (SearchGroupDto searchGroupDto : naSearchList) {
                    BoolQuery.Builder groupBoolQuery = new BoolQuery.Builder();
                    List<SearchDto> list = searchGroupDto.getList();
                    if (CollectionUtil.isNotEmpty(list)) {
                        // 如果条件是2个以上，则需要循环，ES的条件类型以第二个为准
                        if (list.size() > 1) {
                            SearchDto searchDto2 = list.get(1);
                            for (int i = 0; i < list.size(); i++) {
                                SearchDto searchDto = list.get(i);
                                NestedQuery.Builder nestedQuery = new NestedQuery.Builder();
                                builder(nestedQuery, searchDto);
                                if (SearchTypeEnum.AND.getCode().equals(searchDto2.getSearchType())) {
                                    groupBoolQuery.must(qy -> qy.nested(nested -> nestedQuery));
                                } else {
                                    groupBoolQuery.should(qy -> qy.nested(nested -> nestedQuery));
                                }
                            }
                        } else {
                            // 如果条件是1个ES的条件类型为must
                            SearchDto searchDto = list.get(0);
                            NestedQuery.Builder nestedQuery = new NestedQuery.Builder();
                            builder(nestedQuery, searchDto);
                            groupBoolQuery.must(qy -> qy.nested(nested -> nestedQuery));
                        }
                        boolQueryList.add(groupBoolQuery);
                    }
                }

                if (boolQueryList.size()==1){
                    bd.must(qy -> qy.bool(b -> b.must(boolQueryList.get(0).build()._toQuery())));
                }else {

                    BoolQuery.Builder bd10 = new BoolQuery.Builder();
                    bd10.must(qy -> qy.bool(b -> b.must(boolQueryList.get(0).build()._toQuery())));
                    String type = SearchTypeEnum.AND.getCode();
                    for (int i = 1; i < naSearchList.size(); i++) {
                        int finalI = i;
                        if(naSearchList.get(i).getGroupSearchType().equals(type)){
                            bd10.must(qy -> qy.bool(b -> b.must(boolQueryList.get(finalI).build()._toQuery())));
                        }else {
                            BoolQuery.Builder bd11 = new BoolQuery.Builder();
                            bd11.should(qy -> qy.bool(b -> b.should(boolQueryList.get(finalI).build()._toQuery())));
                            BoolQuery.Builder finalBd1 = bd10;
                            bd11.should(qy -> qy.bool(finalBd1.build()));
                            bd10=bd11;
                        }
                    }
                    BoolQuery.Builder finalBd11 = bd10;
                    bd.must(qy -> qy.bool(finalBd11.build()));
                }

            }
            // 排除条件
            List<SearchGroupDto> exSearchList = JSON.parseArray(naSearch.getExSearch(), SearchGroupDto.class);
            if (CollectionUtil.isNotEmpty(exSearchList)) {
                for (SearchGroupDto searchGroupDto : exSearchList) {
                    // 获取排除条件组
                    List<SearchDto> list = searchGroupDto.getList();
                    if (CollectionUtil.isNotEmpty(list)) {

                        list.forEach(searchDto -> {
                            NestedQuery.Builder nestedQuery = new NestedQuery.Builder();
                            builder(nestedQuery, searchDto);
                            bd.mustNot(qy -> qy.nested(nested -> nestedQuery));
                        });
                    }
                }
            }
        }
        return bd.build()._toQuery();
    }





    private void builder(NestedQuery.Builder builder, SearchDto searchDto) {
        String formCode = StrUtil.toCamelCase(searchDto.getFormCode());
        builder.path(formCode);

        final String filed = formCode + "." + StrUtil.toCamelCase(searchDto.getFieldCode());

//        if (searchDto.getVariableType().contains("bool")){
//        if ("是".equals(searchDto.getSearchValue())||"否".equals(searchDto.getSearchValue())){
//            // bool类型
//            searchBool(searchDto, builder, filed);
//        } else
        if (DefineConstant.CONTAINS_CODE.equals(searchDto.getValueType())) {
            // 包含搜索条件构建
            searchLike(searchDto, builder, filed);
        } else if (DefineConstant.NOT_CONTAINS_CODE.equals(searchDto.getValueType())) {
            // 不包含搜索条件构建
            searchNotLike(searchDto, builder, filed);
        } else if (DefineConstant.LESS_THAN_CODE.equals(searchDto.getValueType())) {
            // 小于搜索条件构建
            searchLt(searchDto, builder, filed);
        } else if (DefineConstant.GREATER_THAN_CODE.equals(searchDto.getValueType())) {
            // 大于搜索条件构建
            searchGt(searchDto, builder, filed);
        } else if (DefineConstant.EQUAL_CODE.equals(searchDto.getValueType())) {
            // 精确匹配搜索条件构建
            searchEqual(searchDto, filed, builder);
        } else if (DefineConstant.NOT_EQUAL_CODE.equals(searchDto.getValueType())) {
            // 不等于精确匹配搜索条件构建
            searchNotEqual(searchDto, filed, builder);
        } else if (DefineConstant.LESS_THAN_EQUAL_CODE.equals(searchDto.getValueType())) {
            // 小于等于搜索条件构建
            searchLte(searchDto, builder, filed);
        } else if (DefineConstant.GREATER_THAN_EQUAL_CODE.equals(searchDto.getValueType())) {
            // 大于等于搜索条件构建
            searchGte(searchDto, builder, filed);
        }
    }



    /**
     * bool 类型的条件搜索
     * @param searchDto
     * @param builder
     * @param filed
     */
    private static void searchBool(SearchDto searchDto, NestedQuery.Builder builder, String filed) {
        boolean flag;
        if ("是".equals(searchDto.getSearchValue())||"true".equals(searchDto.getSearchValue())){
            flag = true;
        } else {
            flag = false;
        }

        if (SearchTypeEnum.AND.getCode().equals(searchDto.getSearchType())) {
            if (DefineConstant.EMPTY.equals(searchDto.getValueType())){
                builder.query(nestedQuery -> nestedQuery.bool(
                                bool -> bool
                                        .mustNot(m -> m.exists(e->e.field(filed)))
                        )
                );
            }else if (DefineConstant.NOT_EMPTY.equals(searchDto.getValueType())){
                builder.query(nestedQuery -> nestedQuery.bool(
                                bool -> bool
                                        .must(m -> m.exists(e->e.field(filed)))
                        )
                );
            }else if (DefineConstant.EQUAL_CODE.equals(searchDto.getValueType())) {
                // 等于搜索条件构建
                builder.query(nestedQuery -> nestedQuery.bool(
                                bool -> bool
                                        .must(m -> m.term(t->t.field(filed).value(flag)))
                        )
                );

            } else if (DefineConstant.NOT_EQUAL_CODE.equals(searchDto.getValueType())) {
                // 不等于搜索条件构建
                builder.query(nestedQuery -> nestedQuery.bool(
                                bool -> bool
                                        .mustNot(m -> m.term(t->t.field(filed).value(flag)))
                        )
                );
            }

        } else if (SearchTypeEnum.OR.getCode().equals(searchDto.getSearchType())) {
            if (DefineConstant.EMPTY.equals(searchDto.getValueType())){
                builder.query(nestedQuery -> nestedQuery.bool(
                        bool -> bool.should(s->s.bool(sb->sb
                                        .mustNot(m -> m.exists(e->e.field(filed)))
                                )
                        )));
            }else if (DefineConstant.NOT_EMPTY.equals(searchDto.getValueType())){

                builder.query(nestedQuery -> nestedQuery.bool(
                        bool -> bool.should(s->s.bool(sb->sb
                                        .must(m -> m.exists(e->e.field(filed)))
                                )
                        )));
            }else if (DefineConstant.EQUAL_CODE.equals(searchDto.getValueType())) {
                // 等于搜索条件构建
                builder.query(nestedQuery -> nestedQuery.bool(
                        bool -> bool.should(s->s.bool(sb->sb
                                        .must(m -> m.term(t->t.field(filed).value(true)))
                                )
                        )));
            } else if (DefineConstant.NOT_EQUAL_CODE.equals(searchDto.getValueType())) {
                // 不等于搜索条件构建
                builder.query(nestedQuery -> nestedQuery.bool(
                        bool -> bool.should(s->s.bool(sb->sb
                                        .must(m -> m.term(t->t.field(filed).value(false)))
                                )
                        )));
            }
        } else if (SearchTypeEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            if (DefineConstant.EMPTY.equals(searchDto.getValueType())){
                builder.query(nestedQuery -> nestedQuery.bool(
                                bool -> bool
                                        .must(m -> m.exists(e->e.field(filed)))
                        )
                );
            }else if (DefineConstant.NOT_EMPTY.equals(searchDto.getValueType())){
                builder.query(nestedQuery -> nestedQuery.bool(
                                bool -> bool
                                        .mustNot(m -> m.exists(e->e.field(filed)))
                        )
                );
            }else if (DefineConstant.EQUAL_CODE.equals(searchDto.getValueType())) {
                // 等于搜索条件构建
                builder.query(nestedQuery -> nestedQuery.bool(
                                bool -> bool
                                        .mustNot(m -> m.term(t->t.field(filed).value(true)))
                        )
                );

            } else if (DefineConstant.NOT_EQUAL_CODE.equals(searchDto.getValueType())) {
                // 不等于搜索条件构建
                builder.query(nestedQuery -> nestedQuery.bool(
                                bool -> bool
                                        .must(m -> m.term(t->t.field(filed).value(false)))
                        )
                );
            }
        }
    }

    private void buildCountQuery(CountRequest.Builder builder, RdrPatientNaPiSearch naSearch,List<String> ids) {

        BoolQuery.Builder bd = new BoolQuery.Builder();
        if(CollectionUtil.isNotEmpty(ids)){
            List<FieldValue> values = new ArrayList<>();
            ids.forEach(f->values.add(FieldValue.of(f)));
            bd.must(m -> m.terms(t -> t.field("patientSn").terms(s -> s.value(values))));
        }
        bd.must(qy -> qy.term(t->t.field("docType").value("visit")));
        if (naSearch != null) {
            // 纳入条件
            List<SearchGroupDto> naSearchList = JSON.parseArray(naSearch.getNaSearch(), SearchGroupDto.class);
            if (CollectionUtil.isNotEmpty(naSearchList)) {
                for (SearchGroupDto searchGroupDto : naSearchList) {
                    // 获取纳排搜索条件组
                    List<SearchDto> list = searchGroupDto.getList();
                    if (CollectionUtil.isNotEmpty(list)) {
                        // 如果分组条件是并且
                        if (SearchTypeEnum.AND.getCode().equals(searchGroupDto.getGroupSearchType())) {
                            list.forEach(searchDto -> {
                                NestedQuery.Builder nestedQuery = new NestedQuery.Builder();
                                ElasticsearchParamBuild.builder(nestedQuery, searchDto);
                                bd.must(qy -> qy.nested(nested -> nestedQuery));
                            });
                        }
                        if (SearchTypeEnum.OR.getCode().equals(searchGroupDto.getGroupSearchType())) {
                            list.forEach(searchDto -> {
                                NestedQuery.Builder nestedQuery = new NestedQuery.Builder();
                                ElasticsearchParamBuild.builder(nestedQuery, searchDto);
                                bd.should(qy -> qy.nested(nested -> nestedQuery));
                            });
                        }
                    }
                }
            }
            // 排除条件
            List<SearchGroupDto> exSearchList = JSON.parseArray(naSearch.getExSearch(), SearchGroupDto.class);
            if (CollectionUtil.isNotEmpty(exSearchList)) {
                for (SearchGroupDto searchGroupDto : exSearchList) {
                    // 获取排除条件组
                    List<SearchDto> list = searchGroupDto.getList();
                    if (CollectionUtil.isNotEmpty(list)) {

                        list.forEach(searchDto -> {
                            NestedQuery.Builder nestedQuery = new NestedQuery.Builder();
                            ElasticsearchParamBuild.builder(nestedQuery, searchDto);
                            bd.mustNot(qy -> qy.nested(nested -> nestedQuery));
                        });
                    }
                }
            }
        }
        builder.query(bd.build()._toQuery());
    }


    private static String formatDateType(String formatType) {
        if (StrUtil.isNotBlank(formatType)) {
            if (formatType.indexOf("d") > 0) {
                return "yyyy-MM-dd";
            } else if (formatType.indexOf("m") > 0) {
                return "yyyy-MM";
            } else {
                return "yyyy";
            }
        }
        return "";
    }

    /**
     * 大于等于搜索条件构建
     * @param searchDto
     * @param builder
     * @param filed
     */
    private static void searchGte(SearchDto searchDto, NestedQuery.Builder builder, String filed) {

        RangeQuery.Builder rangeQuery = new RangeQuery.Builder();
        rangeQuery.field(filed).gte(JsonData.of(searchDto.getSearchValue()));
        String dataFormat = formatDateType(searchDto.getFormatType());
        if (StrUtil.isNotBlank(dataFormat)){
            rangeQuery.format(dataFormat);
        }

        if (SearchTypeEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(rangeQuery.build()))
                    )
            );
        } else if (SearchTypeEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.should(should -> should.range(rangeQuery.build()))
                    )
            );
        } else if (SearchTypeEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.mustNot(mustNot -> mustNot.range(rangeQuery.build()))
                    )
            );
        }
    }

    /**
     * 小于等于搜索条件构建
     * @param searchDto
     * @param builder
     * @param filed
     */
    private static void searchLte(SearchDto searchDto, NestedQuery.Builder builder, String filed) {

        RangeQuery.Builder rangeQuery = new RangeQuery.Builder();
        rangeQuery.field(filed).lte(JsonData.of(searchDto.getSearchValue()));
        String dataFormat = formatDateType(searchDto.getFormatType());
        if (StrUtil.isNotBlank(dataFormat)){
            rangeQuery.format(dataFormat);
        }


        if (SearchTypeEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .must(must -> must.range(rangeQuery.build()))
                    )
            );
        } else if (SearchTypeEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .should(should -> should.range(rangeQuery.build()))
                    )
            );
        } else if (SearchTypeEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .mustNot(mustNot -> mustNot.range(rangeQuery.build()))
                    )
            );
        }
    }

    /**
     * 不等于搜索条件构建
     * @param searchDto
     * @param filed
     * @param builder
     */
    private static void searchNotEqual(SearchDto searchDto, String filed, NestedQuery.Builder builder) {

        String dataFormat = formatDateType(searchDto.getFormatType());
        if (StrUtil.isNotBlank(dataFormat)){
            // 是日期类型
            searchNotEQDate(searchDto,builder,filed);
            return;
        }

        String filedValue = filed;

        if ("varchar".equals(searchDto.getVariableType())
                || "date".equals(searchDto.getVariableType())
                || "timestamp".equals(searchDto.getVariableType())) {
            filedValue += ".keyword";
        }
        String finalFiledValue = filedValue;


        TermQuery.Builder query = new TermQuery.Builder();
        query.field(finalFiledValue).value(searchDto.getSearchValue());

        if (SearchTypeEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .mustNot(mustNot -> mustNot.term(query.build()))
                    )
            );
        } else if (SearchTypeEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .should(should -> should.term(query.build()))
                    )
            );
        } else if (SearchTypeEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .must(must -> must.term(query.build()))
                    )
            );
        }
    }

    /**
     * 针对日期类型的搜索条件进行处理
     * @param searchDto
     * @param builder
     * @param filed
     */
    private static void searchEQDate(SearchDto searchDto, NestedQuery.Builder builder, String filed) {
        RangeQuery.Builder rangeQuery = new RangeQuery.Builder();
        String formatType = searchDto.getFormatType();
        if (StrUtil.isNotBlank(formatType)){
            if (formatType.contains("d")) {
                // 获取某一天
                rangeQuery.field(filed).gte(JsonData.of(searchDto.getSearchValue())).lte(JsonData.of(searchDto.getSearchValue()));
            }else if (formatType.contains("m")) {
                // 获取当月的第一天
                String startDate = DateUtil.format(DateUtil.beginOfMonth(DateUtil.parse(searchDto.getSearchValue() + "-01")), "yyyy-MM-dd");
                // 获取当月的最后一天
                String endDate = DateUtil.format(DateUtil.endOfMonth(DateUtil.parse(searchDto.getSearchValue() + "-01")), "yyyy-MM-dd");

                rangeQuery.field(filed).gte(JsonData.of(startDate)).lte(JsonData.of(endDate));

            }else if (formatType.contains("Y")) {
                // 获取当年的第一天
                String startDate = DateUtil.format(DateUtil.beginOfYear(DateUtil.parse(searchDto.getSearchValue() + "-01-01")), "yyyy-MM-dd");
                // 获取当年的最后一天
                String endDate = DateUtil.format(DateUtil.endOfYear(DateUtil.parse(searchDto.getSearchValue() + "-01-01")), "yyyy-MM-dd");
                rangeQuery.field(filed).gte(JsonData.of(startDate)).lte(JsonData.of(endDate));

            }
        }
        rangeQuery.format("yyyy-MM-dd");

        if (SearchTypeEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .must(must -> must.range(rangeQuery.build()))
                    )
            );
        } else if (SearchTypeEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .should(should -> should.range(rangeQuery.build()))
                    )
            );
        } else if (SearchTypeEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .mustNot(mustNot -> mustNot.range(rangeQuery.build()))
                    )
            );
        }
    }


    /**
     * 针对日期类型的搜索条件进行处理
     * @param searchDto
     * @param builder
     * @param filed
     */
    private static void searchNotEQDate(SearchDto searchDto, NestedQuery.Builder builder, String filed) {
        RangeQuery.Builder rangeQuery = new RangeQuery.Builder();
        String formatType = searchDto.getFormatType();
        if (StrUtil.isNotBlank(formatType)){
            if (formatType.contains("d")) {
                // 获取某一天
                rangeQuery.field(filed).gte(JsonData.of(searchDto.getSearchValue())).lte(JsonData.of(searchDto.getSearchValue()));
            }else if (formatType.contains("m")) {
                // 获取当月的第一天
                String startDate = DateUtil.format(DateUtil.beginOfMonth(DateUtil.parse(searchDto.getSearchValue() + "-01")), "yyyy-MM-dd");
                // 获取当月的最后一天
                String endDate = DateUtil.format(DateUtil.endOfMonth(DateUtil.parse(searchDto.getSearchValue() + "-01")), "yyyy-MM-dd");

                rangeQuery.field(filed).gte(JsonData.of(startDate)).lte(JsonData.of(endDate));

            }else if (formatType.contains("Y")) {
                // 获取当年的第一天
                String startDate = DateUtil.format(DateUtil.beginOfYear(DateUtil.parse(searchDto.getSearchValue() + "-01-01")), "yyyy-MM-dd");
                // 获取当年的最后一天
                String endDate = DateUtil.format(DateUtil.endOfYear(DateUtil.parse(searchDto.getSearchValue() + "-01-01")), "yyyy-MM-dd");
                rangeQuery.field(filed).gte(JsonData.of(startDate)).lte(JsonData.of(endDate));

            }
        }
        rangeQuery.format("yyyy-MM-dd");

        if (SearchTypeEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .mustNot(must -> must.range(rangeQuery.build()))
                    )
            );
        } else if (SearchTypeEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .should(should -> should.range(rangeQuery.build()))
                    )
            );
        } else if (SearchTypeEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .must(mustNot -> mustNot.range(rangeQuery.build()))
                    )
            );
        }
    }

    /**
     * 精确匹配搜索条件构建
     *
     * @param searchDto
     * @param filed
     * @param builder
     */
    private static void searchEqual(SearchDto searchDto, String filed, NestedQuery.Builder builder) {

        String dataFormat = formatDateType(searchDto.getFormatType());
        if (StrUtil.isNotBlank(dataFormat)){
            // 是日期类型
            searchEQDate(searchDto,builder,filed);
            return;
        }

        String filedValue = filed;

        if ("varchar".equals(searchDto.getVariableType())
                || "date".equals(searchDto.getVariableType())
                || "timestamp".equals(searchDto.getVariableType())) {
            filedValue += ".keyword";
        }
        String finalFiledValue = filedValue;


        TermQuery.Builder query = new TermQuery.Builder();
        query.field(finalFiledValue).value(searchDto.getSearchValue());


        if (SearchTypeEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .must(must -> must.term(query.build()))
                    )
            );
        } else if (SearchTypeEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .should(should -> should.term(query.build()))
                    )
            );
        } else if (SearchTypeEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .mustNot(mustNot -> mustNot.term(query.build()))
                    )
            );
        }
    }

    /**
     * 大于搜索条件构建
     * @param searchDto
     * @param builder
     * @param filed
     */
    private static void searchGt(SearchDto searchDto, NestedQuery.Builder builder, String filed) {

        RangeQuery.Builder query = new RangeQuery.Builder();
        query.field(filed).gt(JsonData.of(searchDto.getSearchValue()));
        String dataFormat = formatDateType(searchDto.getFormatType());
        if (StrUtil.isNotBlank(dataFormat)){
            query.format(dataFormat);
        }

        if (SearchTypeEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .must(must -> must.range(query.build()))
                    )
            );
        } else if (SearchTypeEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .should(should -> should.range(query.build()))
                    )
            );
        } else if (SearchTypeEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .mustNot(mustNot -> mustNot.range(query.build()))
                    )
            );
        }
    }

    /**
     * 小于搜索条件构建
     * @param searchDto
     * @param builder
     * @param filed
     */
    private static void searchLt(SearchDto searchDto, NestedQuery.Builder builder, String filed) {


        RangeQuery.Builder query = new RangeQuery.Builder();
        query.field(filed).lt(JsonData.of(searchDto.getSearchValue()));
        String dataFormat = formatDateType(searchDto.getFormatType());
        if (StrUtil.isNotBlank(dataFormat)){
            query.format(dataFormat);
        }

        if (SearchTypeEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .must(must -> must.range(query.build()))
                    )
            );
        } else if (SearchTypeEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .should(should -> should.range(query.build()))
                    )
            );
        } else if (SearchTypeEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .mustNot(mustNot -> mustNot.range(query.build()))
                    )
            );
        }
    }


    /**
     * 不包含搜索条件构建
     * @param searchDto
     * @param builder
     * @param filed
     */
    private static void searchNotLike(SearchDto searchDto, NestedQuery.Builder builder, String filed) {
        if (SearchTypeEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .mustNot(mustNot -> mustNot.wildcard(w -> w.field(filed).wildcard("*"+searchDto.getSearchValue()+"*")))
                    )
            );
        } else if (SearchTypeEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .should(should -> should.wildcard(w -> w.wildcard(filed).wildcard("*"+searchDto.getSearchValue()+"*")))
                    )
            );
        } else if (SearchTypeEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .must(must -> must.wildcard(w -> w.field(filed).wildcard("*"+searchDto.getSearchValue()+"*")))
                    )
            );
        }

    }

    /**
     * 包含搜索条件构建
     *
     * @param searchDto
     * @param builder
     * @param filed
     */
    private static void searchLike(SearchDto searchDto, NestedQuery.Builder builder, String filed) {
        if (SearchTypeEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool.must(must -> must.wildcard(w -> w.field(filed).wildcard("*"+searchDto.getSearchValue()+"*")))));
        } else if (SearchTypeEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool.should(should -> should.wildcard(w -> w.field(filed).wildcard("*"+searchDto.getSearchValue()+"*")))));
        } else if (SearchTypeEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(bool -> bool.mustNot(mustNot -> mustNot.wildcard(w -> w.field(filed).wildcard("*"+searchDto.getSearchValue()+"*")))));
        }
    }


    public RdrPatientNaPiSearch getById(String id) {
        if (StringUtils.isNotBlank(id)) {
            RdrPatientNaPiSearchExample example = new RdrPatientNaPiSearchExample();
            RdrPatientNaPiSearchExample.Criteria criteria = example.createCriteria();
            criteria.andIdEqualTo(id);
            List<RdrPatientNaPiSearch> list = patientNaPiSearchMapper.selectByExample(example);
            if (CollectionUtil.isNotEmpty(list)) {
                return list.get(0);
            }
        }
        return null;
    }

}
