package com.haoys.rdr.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ExamReportExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ExamReportExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andHospitalCodeIsNull() {
            addCriterion("\"hospital_code\" is null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIsNotNull() {
            addCriterion("\"hospital_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeEqualTo(String value) {
            addCriterion("\"hospital_code\" =", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotEqualTo(String value) {
            addCriterion("\"hospital_code\" <>", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThan(String value) {
            addCriterion("\"hospital_code\" >", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" >=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThan(String value) {
            addCriterion("\"hospital_code\" <", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" <=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLike(String value) {
            addCriterion("\"hospital_code\" like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotLike(String value) {
            addCriterion("\"hospital_code\" not like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIn(List<String> values) {
            addCriterion("\"hospital_code\" in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotIn(List<String> values) {
            addCriterion("\"hospital_code\" not in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" not between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgIsNull() {
            addCriterion("\"visit_sn_org\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgIsNotNull() {
            addCriterion("\"visit_sn_org\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgEqualTo(String value) {
            addCriterion("\"visit_sn_org\" =", value, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgNotEqualTo(String value) {
            addCriterion("\"visit_sn_org\" <>", value, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgGreaterThan(String value) {
            addCriterion("\"visit_sn_org\" >", value, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn_org\" >=", value, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgLessThan(String value) {
            addCriterion("\"visit_sn_org\" <", value, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn_org\" <=", value, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgLike(String value) {
            addCriterion("\"visit_sn_org\" like", value, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgNotLike(String value) {
            addCriterion("\"visit_sn_org\" not like", value, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgIn(List<String> values) {
            addCriterion("\"visit_sn_org\" in", values, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgNotIn(List<String> values) {
            addCriterion("\"visit_sn_org\" not in", values, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgBetween(String value1, String value2) {
            addCriterion("\"visit_sn_org\" between", value1, value2, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andVisitSnOrgNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn_org\" not between", value1, value2, "visitSnOrg");
            return (Criteria) this;
        }

        public Criteria andReportNoIsNull() {
            addCriterion("\"report_no\" is null");
            return (Criteria) this;
        }

        public Criteria andReportNoIsNotNull() {
            addCriterion("\"report_no\" is not null");
            return (Criteria) this;
        }

        public Criteria andReportNoEqualTo(String value) {
            addCriterion("\"report_no\" =", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotEqualTo(String value) {
            addCriterion("\"report_no\" <>", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoGreaterThan(String value) {
            addCriterion("\"report_no\" >", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoGreaterThanOrEqualTo(String value) {
            addCriterion("\"report_no\" >=", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLessThan(String value) {
            addCriterion("\"report_no\" <", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLessThanOrEqualTo(String value) {
            addCriterion("\"report_no\" <=", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLike(String value) {
            addCriterion("\"report_no\" like", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotLike(String value) {
            addCriterion("\"report_no\" not like", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoIn(List<String> values) {
            addCriterion("\"report_no\" in", values, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotIn(List<String> values) {
            addCriterion("\"report_no\" not in", values, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoBetween(String value1, String value2) {
            addCriterion("\"report_no\" between", value1, value2, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotBetween(String value1, String value2) {
            addCriterion("\"report_no\" not between", value1, value2, "reportNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("\"order_no\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("\"order_no\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("\"order_no\" =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("\"order_no\" <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("\"order_no\" >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("\"order_no\" >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("\"order_no\" <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("\"order_no\" <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("\"order_no\" like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("\"order_no\" not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("\"order_no\" in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("\"order_no\" not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("\"order_no\" between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("\"order_no\" not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("\"name\" is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("\"name\" is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("\"name\" =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("\"name\" <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("\"name\" >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"name\" >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("\"name\" <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("\"name\" <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("\"name\" like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("\"name\" not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("\"name\" in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("\"name\" not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("\"name\" between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("\"name\" not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andGenderIsNull() {
            addCriterion("\"gender\" is null");
            return (Criteria) this;
        }

        public Criteria andGenderIsNotNull() {
            addCriterion("\"gender\" is not null");
            return (Criteria) this;
        }

        public Criteria andGenderEqualTo(String value) {
            addCriterion("\"gender\" =", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotEqualTo(String value) {
            addCriterion("\"gender\" <>", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderGreaterThan(String value) {
            addCriterion("\"gender\" >", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderGreaterThanOrEqualTo(String value) {
            addCriterion("\"gender\" >=", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderLessThan(String value) {
            addCriterion("\"gender\" <", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderLessThanOrEqualTo(String value) {
            addCriterion("\"gender\" <=", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderLike(String value) {
            addCriterion("\"gender\" like", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotLike(String value) {
            addCriterion("\"gender\" not like", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderIn(List<String> values) {
            addCriterion("\"gender\" in", values, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotIn(List<String> values) {
            addCriterion("\"gender\" not in", values, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderBetween(String value1, String value2) {
            addCriterion("\"gender\" between", value1, value2, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotBetween(String value1, String value2) {
            addCriterion("\"gender\" not between", value1, value2, "gender");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthIsNull() {
            addCriterion("\"date_of_birth\" is null");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthIsNotNull() {
            addCriterion("\"date_of_birth\" is not null");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthEqualTo(Date value) {
            addCriterion("\"date_of_birth\" =", value, "dateOfBirth");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthNotEqualTo(Date value) {
            addCriterion("\"date_of_birth\" <>", value, "dateOfBirth");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthGreaterThan(Date value) {
            addCriterion("\"date_of_birth\" >", value, "dateOfBirth");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthGreaterThanOrEqualTo(Date value) {
            addCriterion("\"date_of_birth\" >=", value, "dateOfBirth");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthLessThan(Date value) {
            addCriterion("\"date_of_birth\" <", value, "dateOfBirth");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthLessThanOrEqualTo(Date value) {
            addCriterion("\"date_of_birth\" <=", value, "dateOfBirth");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthIn(List<Date> values) {
            addCriterion("\"date_of_birth\" in", values, "dateOfBirth");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthNotIn(List<Date> values) {
            addCriterion("\"date_of_birth\" not in", values, "dateOfBirth");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthBetween(Date value1, Date value2) {
            addCriterion("\"date_of_birth\" between", value1, value2, "dateOfBirth");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthNotBetween(Date value1, Date value2) {
            addCriterion("\"date_of_birth\" not between", value1, value2, "dateOfBirth");
            return (Criteria) this;
        }

        public Criteria andPatientSourceIsNull() {
            addCriterion("\"patient_source\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSourceIsNotNull() {
            addCriterion("\"patient_source\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSourceEqualTo(String value) {
            addCriterion("\"patient_source\" =", value, "patientSource");
            return (Criteria) this;
        }

        public Criteria andPatientSourceNotEqualTo(String value) {
            addCriterion("\"patient_source\" <>", value, "patientSource");
            return (Criteria) this;
        }

        public Criteria andPatientSourceGreaterThan(String value) {
            addCriterion("\"patient_source\" >", value, "patientSource");
            return (Criteria) this;
        }

        public Criteria andPatientSourceGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_source\" >=", value, "patientSource");
            return (Criteria) this;
        }

        public Criteria andPatientSourceLessThan(String value) {
            addCriterion("\"patient_source\" <", value, "patientSource");
            return (Criteria) this;
        }

        public Criteria andPatientSourceLessThanOrEqualTo(String value) {
            addCriterion("\"patient_source\" <=", value, "patientSource");
            return (Criteria) this;
        }

        public Criteria andPatientSourceLike(String value) {
            addCriterion("\"patient_source\" like", value, "patientSource");
            return (Criteria) this;
        }

        public Criteria andPatientSourceNotLike(String value) {
            addCriterion("\"patient_source\" not like", value, "patientSource");
            return (Criteria) this;
        }

        public Criteria andPatientSourceIn(List<String> values) {
            addCriterion("\"patient_source\" in", values, "patientSource");
            return (Criteria) this;
        }

        public Criteria andPatientSourceNotIn(List<String> values) {
            addCriterion("\"patient_source\" not in", values, "patientSource");
            return (Criteria) this;
        }

        public Criteria andPatientSourceBetween(String value1, String value2) {
            addCriterion("\"patient_source\" between", value1, value2, "patientSource");
            return (Criteria) this;
        }

        public Criteria andPatientSourceNotBetween(String value1, String value2) {
            addCriterion("\"patient_source\" not between", value1, value2, "patientSource");
            return (Criteria) this;
        }

        public Criteria andClinSympIsNull() {
            addCriterion("\"clin_symp\" is null");
            return (Criteria) this;
        }

        public Criteria andClinSympIsNotNull() {
            addCriterion("\"clin_symp\" is not null");
            return (Criteria) this;
        }

        public Criteria andClinSympEqualTo(String value) {
            addCriterion("\"clin_symp\" =", value, "clinSymp");
            return (Criteria) this;
        }

        public Criteria andClinSympNotEqualTo(String value) {
            addCriterion("\"clin_symp\" <>", value, "clinSymp");
            return (Criteria) this;
        }

        public Criteria andClinSympGreaterThan(String value) {
            addCriterion("\"clin_symp\" >", value, "clinSymp");
            return (Criteria) this;
        }

        public Criteria andClinSympGreaterThanOrEqualTo(String value) {
            addCriterion("\"clin_symp\" >=", value, "clinSymp");
            return (Criteria) this;
        }

        public Criteria andClinSympLessThan(String value) {
            addCriterion("\"clin_symp\" <", value, "clinSymp");
            return (Criteria) this;
        }

        public Criteria andClinSympLessThanOrEqualTo(String value) {
            addCriterion("\"clin_symp\" <=", value, "clinSymp");
            return (Criteria) this;
        }

        public Criteria andClinSympLike(String value) {
            addCriterion("\"clin_symp\" like", value, "clinSymp");
            return (Criteria) this;
        }

        public Criteria andClinSympNotLike(String value) {
            addCriterion("\"clin_symp\" not like", value, "clinSymp");
            return (Criteria) this;
        }

        public Criteria andClinSympIn(List<String> values) {
            addCriterion("\"clin_symp\" in", values, "clinSymp");
            return (Criteria) this;
        }

        public Criteria andClinSympNotIn(List<String> values) {
            addCriterion("\"clin_symp\" not in", values, "clinSymp");
            return (Criteria) this;
        }

        public Criteria andClinSympBetween(String value1, String value2) {
            addCriterion("\"clin_symp\" between", value1, value2, "clinSymp");
            return (Criteria) this;
        }

        public Criteria andClinSympNotBetween(String value1, String value2) {
            addCriterion("\"clin_symp\" not between", value1, value2, "clinSymp");
            return (Criteria) this;
        }

        public Criteria andPhysSignIsNull() {
            addCriterion("\"phys_sign\" is null");
            return (Criteria) this;
        }

        public Criteria andPhysSignIsNotNull() {
            addCriterion("\"phys_sign\" is not null");
            return (Criteria) this;
        }

        public Criteria andPhysSignEqualTo(String value) {
            addCriterion("\"phys_sign\" =", value, "physSign");
            return (Criteria) this;
        }

        public Criteria andPhysSignNotEqualTo(String value) {
            addCriterion("\"phys_sign\" <>", value, "physSign");
            return (Criteria) this;
        }

        public Criteria andPhysSignGreaterThan(String value) {
            addCriterion("\"phys_sign\" >", value, "physSign");
            return (Criteria) this;
        }

        public Criteria andPhysSignGreaterThanOrEqualTo(String value) {
            addCriterion("\"phys_sign\" >=", value, "physSign");
            return (Criteria) this;
        }

        public Criteria andPhysSignLessThan(String value) {
            addCriterion("\"phys_sign\" <", value, "physSign");
            return (Criteria) this;
        }

        public Criteria andPhysSignLessThanOrEqualTo(String value) {
            addCriterion("\"phys_sign\" <=", value, "physSign");
            return (Criteria) this;
        }

        public Criteria andPhysSignLike(String value) {
            addCriterion("\"phys_sign\" like", value, "physSign");
            return (Criteria) this;
        }

        public Criteria andPhysSignNotLike(String value) {
            addCriterion("\"phys_sign\" not like", value, "physSign");
            return (Criteria) this;
        }

        public Criteria andPhysSignIn(List<String> values) {
            addCriterion("\"phys_sign\" in", values, "physSign");
            return (Criteria) this;
        }

        public Criteria andPhysSignNotIn(List<String> values) {
            addCriterion("\"phys_sign\" not in", values, "physSign");
            return (Criteria) this;
        }

        public Criteria andPhysSignBetween(String value1, String value2) {
            addCriterion("\"phys_sign\" between", value1, value2, "physSign");
            return (Criteria) this;
        }

        public Criteria andPhysSignNotBetween(String value1, String value2) {
            addCriterion("\"phys_sign\" not between", value1, value2, "physSign");
            return (Criteria) this;
        }

        public Criteria andRelevantLabTestIsNull() {
            addCriterion("\"relevant_lab_test\" is null");
            return (Criteria) this;
        }

        public Criteria andRelevantLabTestIsNotNull() {
            addCriterion("\"relevant_lab_test\" is not null");
            return (Criteria) this;
        }

        public Criteria andRelevantLabTestEqualTo(String value) {
            addCriterion("\"relevant_lab_test\" =", value, "relevantLabTest");
            return (Criteria) this;
        }

        public Criteria andRelevantLabTestNotEqualTo(String value) {
            addCriterion("\"relevant_lab_test\" <>", value, "relevantLabTest");
            return (Criteria) this;
        }

        public Criteria andRelevantLabTestGreaterThan(String value) {
            addCriterion("\"relevant_lab_test\" >", value, "relevantLabTest");
            return (Criteria) this;
        }

        public Criteria andRelevantLabTestGreaterThanOrEqualTo(String value) {
            addCriterion("\"relevant_lab_test\" >=", value, "relevantLabTest");
            return (Criteria) this;
        }

        public Criteria andRelevantLabTestLessThan(String value) {
            addCriterion("\"relevant_lab_test\" <", value, "relevantLabTest");
            return (Criteria) this;
        }

        public Criteria andRelevantLabTestLessThanOrEqualTo(String value) {
            addCriterion("\"relevant_lab_test\" <=", value, "relevantLabTest");
            return (Criteria) this;
        }

        public Criteria andRelevantLabTestLike(String value) {
            addCriterion("\"relevant_lab_test\" like", value, "relevantLabTest");
            return (Criteria) this;
        }

        public Criteria andRelevantLabTestNotLike(String value) {
            addCriterion("\"relevant_lab_test\" not like", value, "relevantLabTest");
            return (Criteria) this;
        }

        public Criteria andRelevantLabTestIn(List<String> values) {
            addCriterion("\"relevant_lab_test\" in", values, "relevantLabTest");
            return (Criteria) this;
        }

        public Criteria andRelevantLabTestNotIn(List<String> values) {
            addCriterion("\"relevant_lab_test\" not in", values, "relevantLabTest");
            return (Criteria) this;
        }

        public Criteria andRelevantLabTestBetween(String value1, String value2) {
            addCriterion("\"relevant_lab_test\" between", value1, value2, "relevantLabTest");
            return (Criteria) this;
        }

        public Criteria andRelevantLabTestNotBetween(String value1, String value2) {
            addCriterion("\"relevant_lab_test\" not between", value1, value2, "relevantLabTest");
            return (Criteria) this;
        }

        public Criteria andClinDiagIsNull() {
            addCriterion("\"clin_diag\" is null");
            return (Criteria) this;
        }

        public Criteria andClinDiagIsNotNull() {
            addCriterion("\"clin_diag\" is not null");
            return (Criteria) this;
        }

        public Criteria andClinDiagEqualTo(String value) {
            addCriterion("\"clin_diag\" =", value, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andClinDiagNotEqualTo(String value) {
            addCriterion("\"clin_diag\" <>", value, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andClinDiagGreaterThan(String value) {
            addCriterion("\"clin_diag\" >", value, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andClinDiagGreaterThanOrEqualTo(String value) {
            addCriterion("\"clin_diag\" >=", value, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andClinDiagLessThan(String value) {
            addCriterion("\"clin_diag\" <", value, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andClinDiagLessThanOrEqualTo(String value) {
            addCriterion("\"clin_diag\" <=", value, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andClinDiagLike(String value) {
            addCriterion("\"clin_diag\" like", value, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andClinDiagNotLike(String value) {
            addCriterion("\"clin_diag\" not like", value, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andClinDiagIn(List<String> values) {
            addCriterion("\"clin_diag\" in", values, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andClinDiagNotIn(List<String> values) {
            addCriterion("\"clin_diag\" not in", values, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andClinDiagBetween(String value1, String value2) {
            addCriterion("\"clin_diag\" between", value1, value2, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andClinDiagNotBetween(String value1, String value2) {
            addCriterion("\"clin_diag\" not between", value1, value2, "clinDiag");
            return (Criteria) this;
        }

        public Criteria andRelevantDiagIsNull() {
            addCriterion("\"relevant_diag\" is null");
            return (Criteria) this;
        }

        public Criteria andRelevantDiagIsNotNull() {
            addCriterion("\"relevant_diag\" is not null");
            return (Criteria) this;
        }

        public Criteria andRelevantDiagEqualTo(String value) {
            addCriterion("\"relevant_diag\" =", value, "relevantDiag");
            return (Criteria) this;
        }

        public Criteria andRelevantDiagNotEqualTo(String value) {
            addCriterion("\"relevant_diag\" <>", value, "relevantDiag");
            return (Criteria) this;
        }

        public Criteria andRelevantDiagGreaterThan(String value) {
            addCriterion("\"relevant_diag\" >", value, "relevantDiag");
            return (Criteria) this;
        }

        public Criteria andRelevantDiagGreaterThanOrEqualTo(String value) {
            addCriterion("\"relevant_diag\" >=", value, "relevantDiag");
            return (Criteria) this;
        }

        public Criteria andRelevantDiagLessThan(String value) {
            addCriterion("\"relevant_diag\" <", value, "relevantDiag");
            return (Criteria) this;
        }

        public Criteria andRelevantDiagLessThanOrEqualTo(String value) {
            addCriterion("\"relevant_diag\" <=", value, "relevantDiag");
            return (Criteria) this;
        }

        public Criteria andRelevantDiagLike(String value) {
            addCriterion("\"relevant_diag\" like", value, "relevantDiag");
            return (Criteria) this;
        }

        public Criteria andRelevantDiagNotLike(String value) {
            addCriterion("\"relevant_diag\" not like", value, "relevantDiag");
            return (Criteria) this;
        }

        public Criteria andRelevantDiagIn(List<String> values) {
            addCriterion("\"relevant_diag\" in", values, "relevantDiag");
            return (Criteria) this;
        }

        public Criteria andRelevantDiagNotIn(List<String> values) {
            addCriterion("\"relevant_diag\" not in", values, "relevantDiag");
            return (Criteria) this;
        }

        public Criteria andRelevantDiagBetween(String value1, String value2) {
            addCriterion("\"relevant_diag\" between", value1, value2, "relevantDiag");
            return (Criteria) this;
        }

        public Criteria andRelevantDiagNotBetween(String value1, String value2) {
            addCriterion("\"relevant_diag\" not between", value1, value2, "relevantDiag");
            return (Criteria) this;
        }

        public Criteria andExamClassIsNull() {
            addCriterion("\"exam_class\" is null");
            return (Criteria) this;
        }

        public Criteria andExamClassIsNotNull() {
            addCriterion("\"exam_class\" is not null");
            return (Criteria) this;
        }

        public Criteria andExamClassEqualTo(String value) {
            addCriterion("\"exam_class\" =", value, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamClassNotEqualTo(String value) {
            addCriterion("\"exam_class\" <>", value, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamClassGreaterThan(String value) {
            addCriterion("\"exam_class\" >", value, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamClassGreaterThanOrEqualTo(String value) {
            addCriterion("\"exam_class\" >=", value, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamClassLessThan(String value) {
            addCriterion("\"exam_class\" <", value, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamClassLessThanOrEqualTo(String value) {
            addCriterion("\"exam_class\" <=", value, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamClassLike(String value) {
            addCriterion("\"exam_class\" like", value, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamClassNotLike(String value) {
            addCriterion("\"exam_class\" not like", value, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamClassIn(List<String> values) {
            addCriterion("\"exam_class\" in", values, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamClassNotIn(List<String> values) {
            addCriterion("\"exam_class\" not in", values, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamClassBetween(String value1, String value2) {
            addCriterion("\"exam_class\" between", value1, value2, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamClassNotBetween(String value1, String value2) {
            addCriterion("\"exam_class\" not between", value1, value2, "examClass");
            return (Criteria) this;
        }

        public Criteria andExamPositionIsNull() {
            addCriterion("\"exam_position\" is null");
            return (Criteria) this;
        }

        public Criteria andExamPositionIsNotNull() {
            addCriterion("\"exam_position\" is not null");
            return (Criteria) this;
        }

        public Criteria andExamPositionEqualTo(String value) {
            addCriterion("\"exam_position\" =", value, "examPosition");
            return (Criteria) this;
        }

        public Criteria andExamPositionNotEqualTo(String value) {
            addCriterion("\"exam_position\" <>", value, "examPosition");
            return (Criteria) this;
        }

        public Criteria andExamPositionGreaterThan(String value) {
            addCriterion("\"exam_position\" >", value, "examPosition");
            return (Criteria) this;
        }

        public Criteria andExamPositionGreaterThanOrEqualTo(String value) {
            addCriterion("\"exam_position\" >=", value, "examPosition");
            return (Criteria) this;
        }

        public Criteria andExamPositionLessThan(String value) {
            addCriterion("\"exam_position\" <", value, "examPosition");
            return (Criteria) this;
        }

        public Criteria andExamPositionLessThanOrEqualTo(String value) {
            addCriterion("\"exam_position\" <=", value, "examPosition");
            return (Criteria) this;
        }

        public Criteria andExamPositionLike(String value) {
            addCriterion("\"exam_position\" like", value, "examPosition");
            return (Criteria) this;
        }

        public Criteria andExamPositionNotLike(String value) {
            addCriterion("\"exam_position\" not like", value, "examPosition");
            return (Criteria) this;
        }

        public Criteria andExamPositionIn(List<String> values) {
            addCriterion("\"exam_position\" in", values, "examPosition");
            return (Criteria) this;
        }

        public Criteria andExamPositionNotIn(List<String> values) {
            addCriterion("\"exam_position\" not in", values, "examPosition");
            return (Criteria) this;
        }

        public Criteria andExamPositionBetween(String value1, String value2) {
            addCriterion("\"exam_position\" between", value1, value2, "examPosition");
            return (Criteria) this;
        }

        public Criteria andExamPositionNotBetween(String value1, String value2) {
            addCriterion("\"exam_position\" not between", value1, value2, "examPosition");
            return (Criteria) this;
        }

        public Criteria andExamModeIsNull() {
            addCriterion("\"exam_mode\" is null");
            return (Criteria) this;
        }

        public Criteria andExamModeIsNotNull() {
            addCriterion("\"exam_mode\" is not null");
            return (Criteria) this;
        }

        public Criteria andExamModeEqualTo(String value) {
            addCriterion("\"exam_mode\" =", value, "examMode");
            return (Criteria) this;
        }

        public Criteria andExamModeNotEqualTo(String value) {
            addCriterion("\"exam_mode\" <>", value, "examMode");
            return (Criteria) this;
        }

        public Criteria andExamModeGreaterThan(String value) {
            addCriterion("\"exam_mode\" >", value, "examMode");
            return (Criteria) this;
        }

        public Criteria andExamModeGreaterThanOrEqualTo(String value) {
            addCriterion("\"exam_mode\" >=", value, "examMode");
            return (Criteria) this;
        }

        public Criteria andExamModeLessThan(String value) {
            addCriterion("\"exam_mode\" <", value, "examMode");
            return (Criteria) this;
        }

        public Criteria andExamModeLessThanOrEqualTo(String value) {
            addCriterion("\"exam_mode\" <=", value, "examMode");
            return (Criteria) this;
        }

        public Criteria andExamModeLike(String value) {
            addCriterion("\"exam_mode\" like", value, "examMode");
            return (Criteria) this;
        }

        public Criteria andExamModeNotLike(String value) {
            addCriterion("\"exam_mode\" not like", value, "examMode");
            return (Criteria) this;
        }

        public Criteria andExamModeIn(List<String> values) {
            addCriterion("\"exam_mode\" in", values, "examMode");
            return (Criteria) this;
        }

        public Criteria andExamModeNotIn(List<String> values) {
            addCriterion("\"exam_mode\" not in", values, "examMode");
            return (Criteria) this;
        }

        public Criteria andExamModeBetween(String value1, String value2) {
            addCriterion("\"exam_mode\" between", value1, value2, "examMode");
            return (Criteria) this;
        }

        public Criteria andExamModeNotBetween(String value1, String value2) {
            addCriterion("\"exam_mode\" not between", value1, value2, "examMode");
            return (Criteria) this;
        }

        public Criteria andDeviceIsNull() {
            addCriterion("\"device\" is null");
            return (Criteria) this;
        }

        public Criteria andDeviceIsNotNull() {
            addCriterion("\"device\" is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceEqualTo(String value) {
            addCriterion("\"device\" =", value, "device");
            return (Criteria) this;
        }

        public Criteria andDeviceNotEqualTo(String value) {
            addCriterion("\"device\" <>", value, "device");
            return (Criteria) this;
        }

        public Criteria andDeviceGreaterThan(String value) {
            addCriterion("\"device\" >", value, "device");
            return (Criteria) this;
        }

        public Criteria andDeviceGreaterThanOrEqualTo(String value) {
            addCriterion("\"device\" >=", value, "device");
            return (Criteria) this;
        }

        public Criteria andDeviceLessThan(String value) {
            addCriterion("\"device\" <", value, "device");
            return (Criteria) this;
        }

        public Criteria andDeviceLessThanOrEqualTo(String value) {
            addCriterion("\"device\" <=", value, "device");
            return (Criteria) this;
        }

        public Criteria andDeviceLike(String value) {
            addCriterion("\"device\" like", value, "device");
            return (Criteria) this;
        }

        public Criteria andDeviceNotLike(String value) {
            addCriterion("\"device\" not like", value, "device");
            return (Criteria) this;
        }

        public Criteria andDeviceIn(List<String> values) {
            addCriterion("\"device\" in", values, "device");
            return (Criteria) this;
        }

        public Criteria andDeviceNotIn(List<String> values) {
            addCriterion("\"device\" not in", values, "device");
            return (Criteria) this;
        }

        public Criteria andDeviceBetween(String value1, String value2) {
            addCriterion("\"device\" between", value1, value2, "device");
            return (Criteria) this;
        }

        public Criteria andDeviceNotBetween(String value1, String value2) {
            addCriterion("\"device\" not between", value1, value2, "device");
            return (Criteria) this;
        }

        public Criteria andExamParaIsNull() {
            addCriterion("\"exam_para\" is null");
            return (Criteria) this;
        }

        public Criteria andExamParaIsNotNull() {
            addCriterion("\"exam_para\" is not null");
            return (Criteria) this;
        }

        public Criteria andExamParaEqualTo(String value) {
            addCriterion("\"exam_para\" =", value, "examPara");
            return (Criteria) this;
        }

        public Criteria andExamParaNotEqualTo(String value) {
            addCriterion("\"exam_para\" <>", value, "examPara");
            return (Criteria) this;
        }

        public Criteria andExamParaGreaterThan(String value) {
            addCriterion("\"exam_para\" >", value, "examPara");
            return (Criteria) this;
        }

        public Criteria andExamParaGreaterThanOrEqualTo(String value) {
            addCriterion("\"exam_para\" >=", value, "examPara");
            return (Criteria) this;
        }

        public Criteria andExamParaLessThan(String value) {
            addCriterion("\"exam_para\" <", value, "examPara");
            return (Criteria) this;
        }

        public Criteria andExamParaLessThanOrEqualTo(String value) {
            addCriterion("\"exam_para\" <=", value, "examPara");
            return (Criteria) this;
        }

        public Criteria andExamParaLike(String value) {
            addCriterion("\"exam_para\" like", value, "examPara");
            return (Criteria) this;
        }

        public Criteria andExamParaNotLike(String value) {
            addCriterion("\"exam_para\" not like", value, "examPara");
            return (Criteria) this;
        }

        public Criteria andExamParaIn(List<String> values) {
            addCriterion("\"exam_para\" in", values, "examPara");
            return (Criteria) this;
        }

        public Criteria andExamParaNotIn(List<String> values) {
            addCriterion("\"exam_para\" not in", values, "examPara");
            return (Criteria) this;
        }

        public Criteria andExamParaBetween(String value1, String value2) {
            addCriterion("\"exam_para\" between", value1, value2, "examPara");
            return (Criteria) this;
        }

        public Criteria andExamParaNotBetween(String value1, String value2) {
            addCriterion("\"exam_para\" not between", value1, value2, "examPara");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("\"description\" is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("\"description\" is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("\"description\" =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("\"description\" <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("\"description\" >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("\"description\" >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("\"description\" <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("\"description\" <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("\"description\" like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("\"description\" not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("\"description\" in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("\"description\" not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("\"description\" between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("\"description\" not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andConclusionIsNull() {
            addCriterion("\"conclusion\" is null");
            return (Criteria) this;
        }

        public Criteria andConclusionIsNotNull() {
            addCriterion("\"conclusion\" is not null");
            return (Criteria) this;
        }

        public Criteria andConclusionEqualTo(String value) {
            addCriterion("\"conclusion\" =", value, "conclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionNotEqualTo(String value) {
            addCriterion("\"conclusion\" <>", value, "conclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionGreaterThan(String value) {
            addCriterion("\"conclusion\" >", value, "conclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionGreaterThanOrEqualTo(String value) {
            addCriterion("\"conclusion\" >=", value, "conclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionLessThan(String value) {
            addCriterion("\"conclusion\" <", value, "conclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionLessThanOrEqualTo(String value) {
            addCriterion("\"conclusion\" <=", value, "conclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionLike(String value) {
            addCriterion("\"conclusion\" like", value, "conclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionNotLike(String value) {
            addCriterion("\"conclusion\" not like", value, "conclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionIn(List<String> values) {
            addCriterion("\"conclusion\" in", values, "conclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionNotIn(List<String> values) {
            addCriterion("\"conclusion\" not in", values, "conclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionBetween(String value1, String value2) {
            addCriterion("\"conclusion\" between", value1, value2, "conclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionNotBetween(String value1, String value2) {
            addCriterion("\"conclusion\" not between", value1, value2, "conclusion");
            return (Criteria) this;
        }

        public Criteria andIsAbnormalIsNull() {
            addCriterion("\"is_abnormal\" is null");
            return (Criteria) this;
        }

        public Criteria andIsAbnormalIsNotNull() {
            addCriterion("\"is_abnormal\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsAbnormalEqualTo(String value) {
            addCriterion("\"is_abnormal\" =", value, "isAbnormal");
            return (Criteria) this;
        }

        public Criteria andIsAbnormalNotEqualTo(String value) {
            addCriterion("\"is_abnormal\" <>", value, "isAbnormal");
            return (Criteria) this;
        }

        public Criteria andIsAbnormalGreaterThan(String value) {
            addCriterion("\"is_abnormal\" >", value, "isAbnormal");
            return (Criteria) this;
        }

        public Criteria andIsAbnormalGreaterThanOrEqualTo(String value) {
            addCriterion("\"is_abnormal\" >=", value, "isAbnormal");
            return (Criteria) this;
        }

        public Criteria andIsAbnormalLessThan(String value) {
            addCriterion("\"is_abnormal\" <", value, "isAbnormal");
            return (Criteria) this;
        }

        public Criteria andIsAbnormalLessThanOrEqualTo(String value) {
            addCriterion("\"is_abnormal\" <=", value, "isAbnormal");
            return (Criteria) this;
        }

        public Criteria andIsAbnormalLike(String value) {
            addCriterion("\"is_abnormal\" like", value, "isAbnormal");
            return (Criteria) this;
        }

        public Criteria andIsAbnormalNotLike(String value) {
            addCriterion("\"is_abnormal\" not like", value, "isAbnormal");
            return (Criteria) this;
        }

        public Criteria andIsAbnormalIn(List<String> values) {
            addCriterion("\"is_abnormal\" in", values, "isAbnormal");
            return (Criteria) this;
        }

        public Criteria andIsAbnormalNotIn(List<String> values) {
            addCriterion("\"is_abnormal\" not in", values, "isAbnormal");
            return (Criteria) this;
        }

        public Criteria andIsAbnormalBetween(String value1, String value2) {
            addCriterion("\"is_abnormal\" between", value1, value2, "isAbnormal");
            return (Criteria) this;
        }

        public Criteria andIsAbnormalNotBetween(String value1, String value2) {
            addCriterion("\"is_abnormal\" not between", value1, value2, "isAbnormal");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeIsNull() {
            addCriterion("\"req_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeIsNotNull() {
            addCriterion("\"req_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeEqualTo(Date value) {
            addCriterion("\"req_date_time\" =", value, "reqDateTime");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeNotEqualTo(Date value) {
            addCriterion("\"req_date_time\" <>", value, "reqDateTime");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeGreaterThan(Date value) {
            addCriterion("\"req_date_time\" >", value, "reqDateTime");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"req_date_time\" >=", value, "reqDateTime");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeLessThan(Date value) {
            addCriterion("\"req_date_time\" <", value, "reqDateTime");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"req_date_time\" <=", value, "reqDateTime");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeIn(List<Date> values) {
            addCriterion("\"req_date_time\" in", values, "reqDateTime");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeNotIn(List<Date> values) {
            addCriterion("\"req_date_time\" not in", values, "reqDateTime");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"req_date_time\" between", value1, value2, "reqDateTime");
            return (Criteria) this;
        }

        public Criteria andReqDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"req_date_time\" not between", value1, value2, "reqDateTime");
            return (Criteria) this;
        }

        public Criteria andReqDeptIsNull() {
            addCriterion("\"req_dept\" is null");
            return (Criteria) this;
        }

        public Criteria andReqDeptIsNotNull() {
            addCriterion("\"req_dept\" is not null");
            return (Criteria) this;
        }

        public Criteria andReqDeptEqualTo(String value) {
            addCriterion("\"req_dept\" =", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptNotEqualTo(String value) {
            addCriterion("\"req_dept\" <>", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptGreaterThan(String value) {
            addCriterion("\"req_dept\" >", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptGreaterThanOrEqualTo(String value) {
            addCriterion("\"req_dept\" >=", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptLessThan(String value) {
            addCriterion("\"req_dept\" <", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptLessThanOrEqualTo(String value) {
            addCriterion("\"req_dept\" <=", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptLike(String value) {
            addCriterion("\"req_dept\" like", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptNotLike(String value) {
            addCriterion("\"req_dept\" not like", value, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptIn(List<String> values) {
            addCriterion("\"req_dept\" in", values, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptNotIn(List<String> values) {
            addCriterion("\"req_dept\" not in", values, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptBetween(String value1, String value2) {
            addCriterion("\"req_dept\" between", value1, value2, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDeptNotBetween(String value1, String value2) {
            addCriterion("\"req_dept\" not between", value1, value2, "reqDept");
            return (Criteria) this;
        }

        public Criteria andReqDoctorIsNull() {
            addCriterion("\"req_doctor\" is null");
            return (Criteria) this;
        }

        public Criteria andReqDoctorIsNotNull() {
            addCriterion("\"req_doctor\" is not null");
            return (Criteria) this;
        }

        public Criteria andReqDoctorEqualTo(String value) {
            addCriterion("\"req_doctor\" =", value, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andReqDoctorNotEqualTo(String value) {
            addCriterion("\"req_doctor\" <>", value, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andReqDoctorGreaterThan(String value) {
            addCriterion("\"req_doctor\" >", value, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andReqDoctorGreaterThanOrEqualTo(String value) {
            addCriterion("\"req_doctor\" >=", value, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andReqDoctorLessThan(String value) {
            addCriterion("\"req_doctor\" <", value, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andReqDoctorLessThanOrEqualTo(String value) {
            addCriterion("\"req_doctor\" <=", value, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andReqDoctorLike(String value) {
            addCriterion("\"req_doctor\" like", value, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andReqDoctorNotLike(String value) {
            addCriterion("\"req_doctor\" not like", value, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andReqDoctorIn(List<String> values) {
            addCriterion("\"req_doctor\" in", values, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andReqDoctorNotIn(List<String> values) {
            addCriterion("\"req_doctor\" not in", values, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andReqDoctorBetween(String value1, String value2) {
            addCriterion("\"req_doctor\" between", value1, value2, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andReqDoctorNotBetween(String value1, String value2) {
            addCriterion("\"req_doctor\" not between", value1, value2, "reqDoctor");
            return (Criteria) this;
        }

        public Criteria andNoticeIsNull() {
            addCriterion("\"notice\" is null");
            return (Criteria) this;
        }

        public Criteria andNoticeIsNotNull() {
            addCriterion("\"notice\" is not null");
            return (Criteria) this;
        }

        public Criteria andNoticeEqualTo(String value) {
            addCriterion("\"notice\" =", value, "notice");
            return (Criteria) this;
        }

        public Criteria andNoticeNotEqualTo(String value) {
            addCriterion("\"notice\" <>", value, "notice");
            return (Criteria) this;
        }

        public Criteria andNoticeGreaterThan(String value) {
            addCriterion("\"notice\" >", value, "notice");
            return (Criteria) this;
        }

        public Criteria andNoticeGreaterThanOrEqualTo(String value) {
            addCriterion("\"notice\" >=", value, "notice");
            return (Criteria) this;
        }

        public Criteria andNoticeLessThan(String value) {
            addCriterion("\"notice\" <", value, "notice");
            return (Criteria) this;
        }

        public Criteria andNoticeLessThanOrEqualTo(String value) {
            addCriterion("\"notice\" <=", value, "notice");
            return (Criteria) this;
        }

        public Criteria andNoticeLike(String value) {
            addCriterion("\"notice\" like", value, "notice");
            return (Criteria) this;
        }

        public Criteria andNoticeNotLike(String value) {
            addCriterion("\"notice\" not like", value, "notice");
            return (Criteria) this;
        }

        public Criteria andNoticeIn(List<String> values) {
            addCriterion("\"notice\" in", values, "notice");
            return (Criteria) this;
        }

        public Criteria andNoticeNotIn(List<String> values) {
            addCriterion("\"notice\" not in", values, "notice");
            return (Criteria) this;
        }

        public Criteria andNoticeBetween(String value1, String value2) {
            addCriterion("\"notice\" between", value1, value2, "notice");
            return (Criteria) this;
        }

        public Criteria andNoticeNotBetween(String value1, String value2) {
            addCriterion("\"notice\" not between", value1, value2, "notice");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeIsNull() {
            addCriterion("\"exam_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeIsNotNull() {
            addCriterion("\"exam_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeEqualTo(Date value) {
            addCriterion("\"exam_date_time\" =", value, "examDateTime");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeNotEqualTo(Date value) {
            addCriterion("\"exam_date_time\" <>", value, "examDateTime");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeGreaterThan(Date value) {
            addCriterion("\"exam_date_time\" >", value, "examDateTime");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"exam_date_time\" >=", value, "examDateTime");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeLessThan(Date value) {
            addCriterion("\"exam_date_time\" <", value, "examDateTime");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"exam_date_time\" <=", value, "examDateTime");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeIn(List<Date> values) {
            addCriterion("\"exam_date_time\" in", values, "examDateTime");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeNotIn(List<Date> values) {
            addCriterion("\"exam_date_time\" not in", values, "examDateTime");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"exam_date_time\" between", value1, value2, "examDateTime");
            return (Criteria) this;
        }

        public Criteria andExamDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"exam_date_time\" not between", value1, value2, "examDateTime");
            return (Criteria) this;
        }

        public Criteria andPerformDeptIsNull() {
            addCriterion("\"perform_dept\" is null");
            return (Criteria) this;
        }

        public Criteria andPerformDeptIsNotNull() {
            addCriterion("\"perform_dept\" is not null");
            return (Criteria) this;
        }

        public Criteria andPerformDeptEqualTo(String value) {
            addCriterion("\"perform_dept\" =", value, "performDept");
            return (Criteria) this;
        }

        public Criteria andPerformDeptNotEqualTo(String value) {
            addCriterion("\"perform_dept\" <>", value, "performDept");
            return (Criteria) this;
        }

        public Criteria andPerformDeptGreaterThan(String value) {
            addCriterion("\"perform_dept\" >", value, "performDept");
            return (Criteria) this;
        }

        public Criteria andPerformDeptGreaterThanOrEqualTo(String value) {
            addCriterion("\"perform_dept\" >=", value, "performDept");
            return (Criteria) this;
        }

        public Criteria andPerformDeptLessThan(String value) {
            addCriterion("\"perform_dept\" <", value, "performDept");
            return (Criteria) this;
        }

        public Criteria andPerformDeptLessThanOrEqualTo(String value) {
            addCriterion("\"perform_dept\" <=", value, "performDept");
            return (Criteria) this;
        }

        public Criteria andPerformDeptLike(String value) {
            addCriterion("\"perform_dept\" like", value, "performDept");
            return (Criteria) this;
        }

        public Criteria andPerformDeptNotLike(String value) {
            addCriterion("\"perform_dept\" not like", value, "performDept");
            return (Criteria) this;
        }

        public Criteria andPerformDeptIn(List<String> values) {
            addCriterion("\"perform_dept\" in", values, "performDept");
            return (Criteria) this;
        }

        public Criteria andPerformDeptNotIn(List<String> values) {
            addCriterion("\"perform_dept\" not in", values, "performDept");
            return (Criteria) this;
        }

        public Criteria andPerformDeptBetween(String value1, String value2) {
            addCriterion("\"perform_dept\" between", value1, value2, "performDept");
            return (Criteria) this;
        }

        public Criteria andPerformDeptNotBetween(String value1, String value2) {
            addCriterion("\"perform_dept\" not between", value1, value2, "performDept");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeIsNull() {
            addCriterion("\"report_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeIsNotNull() {
            addCriterion("\"report_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeEqualTo(Date value) {
            addCriterion("\"report_date_time\" =", value, "reportDateTime");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeNotEqualTo(Date value) {
            addCriterion("\"report_date_time\" <>", value, "reportDateTime");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeGreaterThan(Date value) {
            addCriterion("\"report_date_time\" >", value, "reportDateTime");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"report_date_time\" >=", value, "reportDateTime");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeLessThan(Date value) {
            addCriterion("\"report_date_time\" <", value, "reportDateTime");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"report_date_time\" <=", value, "reportDateTime");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeIn(List<Date> values) {
            addCriterion("\"report_date_time\" in", values, "reportDateTime");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeNotIn(List<Date> values) {
            addCriterion("\"report_date_time\" not in", values, "reportDateTime");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"report_date_time\" between", value1, value2, "reportDateTime");
            return (Criteria) this;
        }

        public Criteria andReportDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"report_date_time\" not between", value1, value2, "reportDateTime");
            return (Criteria) this;
        }

        public Criteria andReportDoctorIsNull() {
            addCriterion("\"report_doctor\" is null");
            return (Criteria) this;
        }

        public Criteria andReportDoctorIsNotNull() {
            addCriterion("\"report_doctor\" is not null");
            return (Criteria) this;
        }

        public Criteria andReportDoctorEqualTo(String value) {
            addCriterion("\"report_doctor\" =", value, "reportDoctor");
            return (Criteria) this;
        }

        public Criteria andReportDoctorNotEqualTo(String value) {
            addCriterion("\"report_doctor\" <>", value, "reportDoctor");
            return (Criteria) this;
        }

        public Criteria andReportDoctorGreaterThan(String value) {
            addCriterion("\"report_doctor\" >", value, "reportDoctor");
            return (Criteria) this;
        }

        public Criteria andReportDoctorGreaterThanOrEqualTo(String value) {
            addCriterion("\"report_doctor\" >=", value, "reportDoctor");
            return (Criteria) this;
        }

        public Criteria andReportDoctorLessThan(String value) {
            addCriterion("\"report_doctor\" <", value, "reportDoctor");
            return (Criteria) this;
        }

        public Criteria andReportDoctorLessThanOrEqualTo(String value) {
            addCriterion("\"report_doctor\" <=", value, "reportDoctor");
            return (Criteria) this;
        }

        public Criteria andReportDoctorLike(String value) {
            addCriterion("\"report_doctor\" like", value, "reportDoctor");
            return (Criteria) this;
        }

        public Criteria andReportDoctorNotLike(String value) {
            addCriterion("\"report_doctor\" not like", value, "reportDoctor");
            return (Criteria) this;
        }

        public Criteria andReportDoctorIn(List<String> values) {
            addCriterion("\"report_doctor\" in", values, "reportDoctor");
            return (Criteria) this;
        }

        public Criteria andReportDoctorNotIn(List<String> values) {
            addCriterion("\"report_doctor\" not in", values, "reportDoctor");
            return (Criteria) this;
        }

        public Criteria andReportDoctorBetween(String value1, String value2) {
            addCriterion("\"report_doctor\" between", value1, value2, "reportDoctor");
            return (Criteria) this;
        }

        public Criteria andReportDoctorNotBetween(String value1, String value2) {
            addCriterion("\"report_doctor\" not between", value1, value2, "reportDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeIsNull() {
            addCriterion("\"review_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeIsNotNull() {
            addCriterion("\"review_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeEqualTo(Date value) {
            addCriterion("\"review_date_time\" =", value, "reviewDateTime");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeNotEqualTo(Date value) {
            addCriterion("\"review_date_time\" <>", value, "reviewDateTime");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeGreaterThan(Date value) {
            addCriterion("\"review_date_time\" >", value, "reviewDateTime");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"review_date_time\" >=", value, "reviewDateTime");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeLessThan(Date value) {
            addCriterion("\"review_date_time\" <", value, "reviewDateTime");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"review_date_time\" <=", value, "reviewDateTime");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeIn(List<Date> values) {
            addCriterion("\"review_date_time\" in", values, "reviewDateTime");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeNotIn(List<Date> values) {
            addCriterion("\"review_date_time\" not in", values, "reviewDateTime");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"review_date_time\" between", value1, value2, "reviewDateTime");
            return (Criteria) this;
        }

        public Criteria andReviewDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"review_date_time\" not between", value1, value2, "reviewDateTime");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorIsNull() {
            addCriterion("\"review_doctor\" is null");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorIsNotNull() {
            addCriterion("\"review_doctor\" is not null");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorEqualTo(String value) {
            addCriterion("\"review_doctor\" =", value, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorNotEqualTo(String value) {
            addCriterion("\"review_doctor\" <>", value, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorGreaterThan(String value) {
            addCriterion("\"review_doctor\" >", value, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorGreaterThanOrEqualTo(String value) {
            addCriterion("\"review_doctor\" >=", value, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorLessThan(String value) {
            addCriterion("\"review_doctor\" <", value, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorLessThanOrEqualTo(String value) {
            addCriterion("\"review_doctor\" <=", value, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorLike(String value) {
            addCriterion("\"review_doctor\" like", value, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorNotLike(String value) {
            addCriterion("\"review_doctor\" not like", value, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorIn(List<String> values) {
            addCriterion("\"review_doctor\" in", values, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorNotIn(List<String> values) {
            addCriterion("\"review_doctor\" not in", values, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorBetween(String value1, String value2) {
            addCriterion("\"review_doctor\" between", value1, value2, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andReviewDoctorNotBetween(String value1, String value2) {
            addCriterion("\"review_doctor\" not between", value1, value2, "reviewDoctor");
            return (Criteria) this;
        }

        public Criteria andImageNoIsNull() {
            addCriterion("\"image_no\" is null");
            return (Criteria) this;
        }

        public Criteria andImageNoIsNotNull() {
            addCriterion("\"image_no\" is not null");
            return (Criteria) this;
        }

        public Criteria andImageNoEqualTo(String value) {
            addCriterion("\"image_no\" =", value, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImageNoNotEqualTo(String value) {
            addCriterion("\"image_no\" <>", value, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImageNoGreaterThan(String value) {
            addCriterion("\"image_no\" >", value, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImageNoGreaterThanOrEqualTo(String value) {
            addCriterion("\"image_no\" >=", value, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImageNoLessThan(String value) {
            addCriterion("\"image_no\" <", value, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImageNoLessThanOrEqualTo(String value) {
            addCriterion("\"image_no\" <=", value, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImageNoLike(String value) {
            addCriterion("\"image_no\" like", value, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImageNoNotLike(String value) {
            addCriterion("\"image_no\" not like", value, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImageNoIn(List<String> values) {
            addCriterion("\"image_no\" in", values, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImageNoNotIn(List<String> values) {
            addCriterion("\"image_no\" not in", values, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImageNoBetween(String value1, String value2) {
            addCriterion("\"image_no\" between", value1, value2, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImageNoNotBetween(String value1, String value2) {
            addCriterion("\"image_no\" not between", value1, value2, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImagePathIsNull() {
            addCriterion("\"image_path\" is null");
            return (Criteria) this;
        }

        public Criteria andImagePathIsNotNull() {
            addCriterion("\"image_path\" is not null");
            return (Criteria) this;
        }

        public Criteria andImagePathEqualTo(String value) {
            addCriterion("\"image_path\" =", value, "imagePath");
            return (Criteria) this;
        }

        public Criteria andImagePathNotEqualTo(String value) {
            addCriterion("\"image_path\" <>", value, "imagePath");
            return (Criteria) this;
        }

        public Criteria andImagePathGreaterThan(String value) {
            addCriterion("\"image_path\" >", value, "imagePath");
            return (Criteria) this;
        }

        public Criteria andImagePathGreaterThanOrEqualTo(String value) {
            addCriterion("\"image_path\" >=", value, "imagePath");
            return (Criteria) this;
        }

        public Criteria andImagePathLessThan(String value) {
            addCriterion("\"image_path\" <", value, "imagePath");
            return (Criteria) this;
        }

        public Criteria andImagePathLessThanOrEqualTo(String value) {
            addCriterion("\"image_path\" <=", value, "imagePath");
            return (Criteria) this;
        }

        public Criteria andImagePathLike(String value) {
            addCriterion("\"image_path\" like", value, "imagePath");
            return (Criteria) this;
        }

        public Criteria andImagePathNotLike(String value) {
            addCriterion("\"image_path\" not like", value, "imagePath");
            return (Criteria) this;
        }

        public Criteria andImagePathIn(List<String> values) {
            addCriterion("\"image_path\" in", values, "imagePath");
            return (Criteria) this;
        }

        public Criteria andImagePathNotIn(List<String> values) {
            addCriterion("\"image_path\" not in", values, "imagePath");
            return (Criteria) this;
        }

        public Criteria andImagePathBetween(String value1, String value2) {
            addCriterion("\"image_path\" between", value1, value2, "imagePath");
            return (Criteria) this;
        }

        public Criteria andImagePathNotBetween(String value1, String value2) {
            addCriterion("\"image_path\" not between", value1, value2, "imagePath");
            return (Criteria) this;
        }

        public Criteria andCommentIsNull() {
            addCriterion("\"comment\" is null");
            return (Criteria) this;
        }

        public Criteria andCommentIsNotNull() {
            addCriterion("\"comment\" is not null");
            return (Criteria) this;
        }

        public Criteria andCommentEqualTo(String value) {
            addCriterion("\"comment\" =", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotEqualTo(String value) {
            addCriterion("\"comment\" <>", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentGreaterThan(String value) {
            addCriterion("\"comment\" >", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentGreaterThanOrEqualTo(String value) {
            addCriterion("\"comment\" >=", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentLessThan(String value) {
            addCriterion("\"comment\" <", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentLessThanOrEqualTo(String value) {
            addCriterion("\"comment\" <=", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentLike(String value) {
            addCriterion("\"comment\" like", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotLike(String value) {
            addCriterion("\"comment\" not like", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentIn(List<String> values) {
            addCriterion("\"comment\" in", values, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotIn(List<String> values) {
            addCriterion("\"comment\" not in", values, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentBetween(String value1, String value2) {
            addCriterion("\"comment\" between", value1, value2, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotBetween(String value1, String value2) {
            addCriterion("\"comment\" not between", value1, value2, "comment");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNull() {
            addCriterion("\"source_path\" is null");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNotNull() {
            addCriterion("\"source_path\" is not null");
            return (Criteria) this;
        }

        public Criteria andSourcePathEqualTo(String value) {
            addCriterion("\"source_path\" =", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotEqualTo(String value) {
            addCriterion("\"source_path\" <>", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThan(String value) {
            addCriterion("\"source_path\" >", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThanOrEqualTo(String value) {
            addCriterion("\"source_path\" >=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThan(String value) {
            addCriterion("\"source_path\" <", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThanOrEqualTo(String value) {
            addCriterion("\"source_path\" <=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLike(String value) {
            addCriterion("\"source_path\" like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotLike(String value) {
            addCriterion("\"source_path\" not like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathIn(List<String> values) {
            addCriterion("\"source_path\" in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotIn(List<String> values) {
            addCriterion("\"source_path\" not in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathBetween(String value1, String value2) {
            addCriterion("\"source_path\" between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotBetween(String value1, String value2) {
            addCriterion("\"source_path\" not between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNull() {
            addCriterion("\"pk_id\" is null");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNotNull() {
            addCriterion("\"pk_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkIdEqualTo(String value) {
            addCriterion("\"pk_id\" =", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotEqualTo(String value) {
            addCriterion("\"pk_id\" <>", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThan(String value) {
            addCriterion("\"pk_id\" >", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" >=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThan(String value) {
            addCriterion("\"pk_id\" <", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" <=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLike(String value) {
            addCriterion("\"pk_id\" like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotLike(String value) {
            addCriterion("\"pk_id\" not like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdIn(List<String> values) {
            addCriterion("\"pk_id\" in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotIn(List<String> values) {
            addCriterion("\"pk_id\" not in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdBetween(String value1, String value2) {
            addCriterion("\"pk_id\" between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotBetween(String value1, String value2) {
            addCriterion("\"pk_id\" not between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNull() {
            addCriterion("\"data_state\" is null");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNotNull() {
            addCriterion("\"data_state\" is not null");
            return (Criteria) this;
        }

        public Criteria andDataStateEqualTo(String value) {
            addCriterion("\"data_state\" =", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotEqualTo(String value) {
            addCriterion("\"data_state\" <>", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThan(String value) {
            addCriterion("\"data_state\" >", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThanOrEqualTo(String value) {
            addCriterion("\"data_state\" >=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThan(String value) {
            addCriterion("\"data_state\" <", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThanOrEqualTo(String value) {
            addCriterion("\"data_state\" <=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLike(String value) {
            addCriterion("\"data_state\" like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotLike(String value) {
            addCriterion("\"data_state\" not like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateIn(List<String> values) {
            addCriterion("\"data_state\" in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotIn(List<String> values) {
            addCriterion("\"data_state\" not in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateBetween(String value1, String value2) {
            addCriterion("\"data_state\" between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotBetween(String value1, String value2) {
            addCriterion("\"data_state\" not between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andExamNameIsNull() {
            addCriterion("\"exam_name\" is null");
            return (Criteria) this;
        }

        public Criteria andExamNameIsNotNull() {
            addCriterion("\"exam_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andExamNameEqualTo(String value) {
            addCriterion("\"exam_name\" =", value, "examName");
            return (Criteria) this;
        }

        public Criteria andExamNameNotEqualTo(String value) {
            addCriterion("\"exam_name\" <>", value, "examName");
            return (Criteria) this;
        }

        public Criteria andExamNameGreaterThan(String value) {
            addCriterion("\"exam_name\" >", value, "examName");
            return (Criteria) this;
        }

        public Criteria andExamNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"exam_name\" >=", value, "examName");
            return (Criteria) this;
        }

        public Criteria andExamNameLessThan(String value) {
            addCriterion("\"exam_name\" <", value, "examName");
            return (Criteria) this;
        }

        public Criteria andExamNameLessThanOrEqualTo(String value) {
            addCriterion("\"exam_name\" <=", value, "examName");
            return (Criteria) this;
        }

        public Criteria andExamNameLike(String value) {
            addCriterion("\"exam_name\" like", value, "examName");
            return (Criteria) this;
        }

        public Criteria andExamNameNotLike(String value) {
            addCriterion("\"exam_name\" not like", value, "examName");
            return (Criteria) this;
        }

        public Criteria andExamNameIn(List<String> values) {
            addCriterion("\"exam_name\" in", values, "examName");
            return (Criteria) this;
        }

        public Criteria andExamNameNotIn(List<String> values) {
            addCriterion("\"exam_name\" not in", values, "examName");
            return (Criteria) this;
        }

        public Criteria andExamNameBetween(String value1, String value2) {
            addCriterion("\"exam_name\" between", value1, value2, "examName");
            return (Criteria) this;
        }

        public Criteria andExamNameNotBetween(String value1, String value2) {
            addCriterion("\"exam_name\" not between", value1, value2, "examName");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIsNull() {
            addCriterion("\"patient_sn_org\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIsNotNull() {
            addCriterion("\"patient_sn_org\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgEqualTo(String value) {
            addCriterion("\"patient_sn_org\" =", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotEqualTo(String value) {
            addCriterion("\"patient_sn_org\" <>", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgGreaterThan(String value) {
            addCriterion("\"patient_sn_org\" >", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn_org\" >=", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLessThan(String value) {
            addCriterion("\"patient_sn_org\" <", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn_org\" <=", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLike(String value) {
            addCriterion("\"patient_sn_org\" like", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotLike(String value) {
            addCriterion("\"patient_sn_org\" not like", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIn(List<String> values) {
            addCriterion("\"patient_sn_org\" in", values, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotIn(List<String> values) {
            addCriterion("\"patient_sn_org\" not in", values, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgBetween(String value1, String value2) {
            addCriterion("\"patient_sn_org\" between", value1, value2, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn_org\" not between", value1, value2, "patientSnOrg");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}