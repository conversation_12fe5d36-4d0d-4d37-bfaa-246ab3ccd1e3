package com.haoys.rdr.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.bean.BeanUtils;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.constants.Constants;
import com.haoys.rdr.PatientAgeDefine;
import com.haoys.rdr.domain.bigscreen.AreaDistributeDataView;
import com.haoys.rdr.domain.bigscreen.PatientMedicalDataView;
import com.haoys.rdr.domain.bigscreen.PatientVisitCount;
import com.haoys.rdr.domain.bigscreen.PatientVisitDepartment;
import com.haoys.rdr.domain.bigscreen.RdrPatientDataCenterVo;
import com.haoys.rdr.domain.emums.DatSetCodeEnum;
import com.haoys.rdr.domain.emums.PatientSexEnum;
import com.haoys.rdr.domain.param.CountPatientToAgeParam;
import com.haoys.rdr.domain.param.CountPatientToAgeWrapperParam;
import com.haoys.rdr.domain.vo.CountPatientDiseaseTypeVo;
import com.haoys.rdr.domain.vo.CountPatientToAgeVo;
import com.haoys.rdr.domain.vo.CountPatientToAreaVo;
import com.haoys.rdr.domain.vo.DataOverViewVo;
import com.haoys.rdr.mapper.InpOrderMapper;
import com.haoys.rdr.mapper.InpVisitMapper;
import com.haoys.rdr.mapper.MrHomepageOperMapper;
import com.haoys.rdr.mapper.OutpVisitMapper;
import com.haoys.rdr.mapper.PatientsMapper;
import com.haoys.rdr.mapper.RdrPatientDataCenterMapper;
import com.haoys.rdr.mapper.VisitDiagMapper;
import com.haoys.rdr.mapper.VisitInformationMapper;
import com.haoys.rdr.service.RdrDataOverviewService;
import com.haoys.rdr.service.RdrPatientRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
@DS("disease-rdr")
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class RdrDataOverviewServiceImpl implements RdrDataOverviewService {

    private final PatientsMapper patientsMapper;
    private final InpVisitMapper inpVisitMapper;
    private final OutpVisitMapper outpVisitMapper;

    private final VisitInformationMapper visitInformationMapper;
    private final RdrPatientRecordService rdrPatientRecordService;
    private final VisitDiagMapper visitDiagMapper;
    private final MrHomepageOperMapper mrHomepageOperMapper;
    private final InpOrderMapper inpOrderMapper;
    private final RdrPatientDataCenterMapper rdrPatientDataCenterMapper;

    @Override
    public CommonResult<DataOverViewVo> getTotalPatientOverView(String databaseId) {
        DataOverViewVo overViewVo = new DataOverViewVo();
        // 统计入库总病例人数
        long countPatient = patientsMapper.count(databaseId);
        overViewVo.setCountPatient(countPatient);
        // 统计住院就诊人数
        long inpVisitCount = inpVisitMapper.count(databaseId);
        overViewVo.setCountVisits(inpVisitCount);
        // 统计门诊就诊人数
        long outpVisitCount = outpVisitMapper.count(databaseId);
        overViewVo.setCountVisits(outpVisitCount);
        // 统计患者性别信息
        DataOverViewVo.PatientSexCount genderCount = getPatientSexCount(countPatient, databaseId);
        overViewVo.setPatientSexCount(genderCount);
        return CommonResult.success(overViewVo);
    }

    private DataOverViewVo.PatientSexCount getPatientSexCount(long countPatient,String databaseId) {
        // 统计患者性别信息
        DataOverViewVo.PatientSexCount sexCount = new DataOverViewVo.PatientSexCount();
        // 1.获取男性的数量
        long countManPatient = rdrPatientRecordService.countPatient(PatientSexEnum.MAN.getCode(), databaseId);
        sexCount.setCountManPatient(countManPatient);
//         2.获取女性的数量 总数减去男性的数量
        if (countManPatient==0){
            sexCount.setCountWomanPatient(0L);
        }else {
            sexCount.setCountWomanPatient(countPatient-countManPatient);
        }
        // 3.男性的占比
        BigDecimal manPatientProportion = BigDecimal.ZERO;
        // 4.女性的占比
        BigDecimal womanPatientProportion =  BigDecimal.ZERO;
        if (countPatient >0 && countManPatient>0){
            // 男性患者数量乘100除以总数，求的占比
            manPatientProportion= new BigDecimal(countManPatient*100).divide(new BigDecimal(countPatient), 2, RoundingMode.HALF_UP);
        }
        if (countPatient >0){

            // 100-男性的占比，为女性的占比
            womanPatientProportion= new BigDecimal(100).subtract(manPatientProportion);
        }
        sexCount.setManPatientProportion(manPatientProportion);
        sexCount.setWomanPatientProportion(womanPatientProportion);
        return sexCount;
    }

    @Override
    public List<CountPatientDiseaseTypeVo> countPatientToDiseaseType(String databaseId) {
        // 统计病种icd10 诊断人次
        /*List<CountPatientDiseaseTypeVo> diagnosisList = patientDiagnosisService.countPatientToDiseaseType();
        if (CollectionUtil.isNotEmpty(diagnosisList)){
            // 统计病种icd10 患者数量
            List<CountPatientDiseaseTypeVo> list = patientBaseInfoService.countPatientToDiseaseType();
            Map<String, Long> map =
                    list.stream().collect(Collectors.toMap(CountPatientDiseaseTypeVo::getCode, CountPatientDiseaseTypeVo::getPatientNum));
            diagnosisList.forEach(d-> d.setPatientNum(map.get(d.getCode())));
        }
        return diagnosisList;*/
        return null;
    }

    @Override
    public CommonResult<List<CountPatientToAgeParam>> countPatientToAge(CountPatientToAgeWrapperParam countPatientToAgeWrapperParam) {
        List<CountPatientToAgeParam> params = countPatientToAgeWrapperParam.getParams();
        if (CollectionUtil.isNotEmpty(params)){
            List<CountPatientToAgeVo> list = patientsMapper.countPgSqlPatientToAge(countPatientToAgeWrapperParam.getDataBaseId());
            if (CollectionUtil.isNotEmpty(list)){
                Long total = 0L;
                for (CountPatientToAgeVo countPatientToAgeVo : list) {
                    total+=countPatientToAgeVo.getNum();
                }
                for (CountPatientToAgeVo vo : list) {
                    if (vo.getAge()!=null){
                        int age = vo.getAge();
                        for (CountPatientToAgeParam param : params) {
                            if (age>=param.getStartAge() && age<= param.getEndAge()){
                                param.setNum(param.getNum()+vo.getNum());
                                break;
                            }
                        }
                    }
                }
                if (total>0){
                    Long finalTotal = total;
                    params.forEach(p->p.setProportion(new BigDecimal(p.getNum()*100).divide(new BigDecimal(finalTotal), 2, RoundingMode.HALF_UP)));
                }
            }
            return CommonResult.success(params);
        }
        return CommonResult.success(new ArrayList<>());
    }

    /**
     * 季节性患者
     * @return
     */
    @Override
    public CommonResult<DataOverViewVo.QuarterPatientCount> countQuarterPatient() {

        DataOverViewVo.QuarterPatientCount quarterPatientCount = new DataOverViewVo.QuarterPatientCount();
        // 以阳历3～5月为春季,6～8月为夏季,9～11月为秋季,12月～来年2月为冬季
        // 春季
        List<Integer> springMonths = new ArrayList<>();
        springMonths.add(3);
        springMonths.add(4);
        springMonths.add(5);
        Long springCount = 0L;
        springCount = inpVisitMapper.countPgSqlQuarter(springMonths);
        quarterPatientCount.setSpring(springCount);
        // 夏季
        List<Integer> summerMonths = new ArrayList<>();
        summerMonths.add(6);
        summerMonths.add(7);
        summerMonths.add(8);

        Long summerCount = 0L;
        summerCount = inpVisitMapper.countPgSqlQuarter(summerMonths);
        quarterPatientCount.setSummer(summerCount);
        // 秋季
        List<Integer> autumnMonths = new ArrayList<>();
        autumnMonths.add(9);
        autumnMonths.add(10);
        autumnMonths.add(11);

        Long autumnCount = 0L;
        autumnCount = inpVisitMapper.countPgSqlQuarter(autumnMonths);
        quarterPatientCount.setAutumn(autumnCount);

        // 秋季
        List<Integer> winterMonths = new ArrayList<>();
        winterMonths.add(12);
        winterMonths.add(1);
        winterMonths.add(2);

        Long winterCount = 0L;
        winterCount = inpVisitMapper.countPgSqlQuarter(winterMonths);
        quarterPatientCount.setWinter(winterCount);
        return CommonResult.success(quarterPatientCount);
    }

    @Override
    public DataOverViewVo.CountPatientToYearVo countPatientToYear(String databaseId,String year) {
        DataOverViewVo.CountPatientToYearVo vo = new DataOverViewVo.CountPatientToYearVo();
        // 就诊人次
        List<Long> visitData = new ArrayList<>(12);

//        Map<Integer, Map<String, Long>> map0 = inpVisitMapper.countPgSqlVisitInfoByYear(year,null);
        Map<Integer, Map<String, Long>> map0 = new HashMap<>();

        extracted(visitData, map0);
        vo.setVisitData(visitData);
        // 门诊人次
        List<Long> inHospData = new ArrayList<>(12);

//      Map<Integer, Map<String, Long>> map1= inpVisitMapper.countPgSqlVisitInfoByYear(year,ConstantsModel.PATIENT_VISIT_CODE_02);
        Map<Integer, Map<String, Long>> map1= new HashMap<>();

        extracted(inHospData, map1);
        vo.setInHospData(inHospData);

        // 患者人数
        List<Long> patientData = new ArrayList<>(12);
//        Map<Integer, Map<String, Long>> map3 = inpVisitMapper.countPgSqlPatientByYear(year);
        Map<Integer, Map<String, Long>> map3 = new HashMap<>();
        extracted(patientData, map3);
        vo.setPatientData(patientData);
        return vo;
    }

    /**
     * 按照一年12月组装数据
     * @param data
     * @param map
     */
    private static void extracted(List<Long> data, Map<Integer, Map<String, Long>> map) {
        for (int i = 1; i <13 ; i++) {
            Map<String, Long> map1 = map.get(i);
            if (map1==null){
                data.add(0L);
            }else {
                data.add(map1.get("num"));
            }
        }
    }

    @Override
    public CommonResult<List<CountPatientToAreaVo>> countPatientToAre() {
        List<CountPatientToAreaVo> list = new ArrayList<>();
        return CommonResult.success(list);
    }

    @Override
    public CommonResult<PatientVisitCount> getTotalPatientWrapperCount(String year, String databaseId) {
        long patientsCount = patientsMapper.count(databaseId);
        PatientVisitCount.GenderPercent genderPercent = new PatientVisitCount.GenderPercent();
        PatientVisitCount patientVisitCount = visitInformationMapper.countPatientVisitCount(year,databaseId);
        List<PatientVisitCount.PatientGenderCount> patientGenderCountList = visitInformationMapper.countPatientGenderPercent(year,databaseId);
        for (PatientVisitCount.PatientGenderCount patientGenderCount : patientGenderCountList) {
            if(BusinessConfig.LAB_CONFIG_GENDER_MALE.equals(patientGenderCount.getGender())){
                genderPercent.setMaleCount(patientGenderCount.getCount());
            }
            if(BusinessConfig.LAB_CONFIG_GENDER_FEMALE.equals(patientGenderCount.getGender())){
                genderPercent.setFemaleCount(patientGenderCount.getCount());
            }
        }
        patientVisitCount.setPatientsCount(patientsCount);
        patientVisitCount.setGenderPercent(genderPercent);
        return CommonResult.success(patientVisitCount);
    }

    @Override
    public CommonResult<List<PatientMedicalDataView>> getMedicalDataView(String dataSetCode, String year, String department, String visitType, String databaseId) {
        if(DatSetCodeEnum.DATASET_01.getCode().equals(dataSetCode)){
            List<PatientMedicalDataView> visitDiagnosisList = visitDiagMapper.countPatientVisitDiagnosis(year, department, visitType,databaseId);
            return CommonResult.success(visitDiagnosisList);
        }
        if(DatSetCodeEnum.DATASET_02.getCode().equals(dataSetCode)){
            List<PatientMedicalDataView> patientVisitOperList = mrHomepageOperMapper.countPatientVisitOper(year, department, visitType,databaseId);
            return CommonResult.success(patientVisitOperList);
        }
        if(DatSetCodeEnum.DATASET_03.getCode().equals(dataSetCode)){
            List<PatientMedicalDataView> countPatientVisitOrderList = inpOrderMapper.countPatientVisitOrder(year, department, visitType,databaseId);
            return CommonResult.success(countPatientVisitOrderList);
        }
        if(DatSetCodeEnum.DATASET_04.getCode().equals(dataSetCode)){
            List<PatientMedicalDataView> countPatientVisitInfoList = visitInformationMapper.countPatientVisitDepartment(year, department, visitType,databaseId);
            return CommonResult.success(countPatientVisitInfoList);
        }
        return null;
    }

    @Override
    public CommonResult<AreaDistributeDataView> getVisitAreaDataViewForProvinceLevel(String code, String databaseId) {
        AreaDistributeDataView areaDistributeDataView = new AreaDistributeDataView();
        List<AreaDistributeDataView.RegionDataView> regionDataViewList = visitInformationMapper.countPatientVisitAreaForProvinceLevel(code,databaseId);
        areaDistributeDataView.setDataList(regionDataViewList);
        return CommonResult.success(areaDistributeDataView);
    }

    @Override
    public CommonResult<AreaDistributeDataView> countPatientVisitAreaForCityLevel(String code, String databaseId) {
        AreaDistributeDataView areaDistributeDataView = new AreaDistributeDataView();
        List<AreaDistributeDataView.RegionDataView> regionDataViewList = visitInformationMapper.countPatientVisitAreaForCityLevel(code,databaseId);
        areaDistributeDataView.setDataList(regionDataViewList);
        return CommonResult.success(areaDistributeDataView);
    }

    @Override
    public List<PatientVisitCount> getPatientVisitCountByMonth(String year, String department, String databaseId) {
        return visitInformationMapper.getPatientVisitCountByMonth(year, department,databaseId);
    }

    @Override
    public CommonResult<Map<String, PatientMedicalDataView>> countPatientAgeDataView(String year, String department, String visitType, String databaseId) {
        Map<String, PatientMedicalDataView> dataMap = new HashMap<>();
        List<PatientMedicalDataView> male = visitInformationMapper.countPatientAgeDataView(Constants.GENDER_MALE_X, year, department, visitType,databaseId);
        List<PatientMedicalDataView> female = visitInformationMapper.countPatientAgeDataView(Constants.GENDER_FEMALE_X, year, department, visitType,databaseId);


        for (String ageGroup : PatientAgeDefine.ageGroup) {
            for (PatientMedicalDataView medicalDataView : male) {
                if (ageGroup.equals(medicalDataView.getName())) {
                    PatientMedicalDataView patientMedical = dataMap.get(ageGroup);
                    if (patientMedical !=null){
                        patientMedical.setMaleCount(medicalDataView.getCount());
                        dataMap.put(ageGroup, patientMedical);
                    }else {
                        medicalDataView.setMaleCount(medicalDataView.getCount());
                        dataMap.put(ageGroup, medicalDataView);
                    }
                }
            }
            for (PatientMedicalDataView medicalDataView : female) {
                if (ageGroup.equals(medicalDataView.getName())) {
                    PatientMedicalDataView patientMedical = dataMap.get(ageGroup);
                    if (patientMedical !=null){
                        patientMedical.setFemaleCount(medicalDataView.getCount());
                        dataMap.put(ageGroup, patientMedical);
                    }else {
                        medicalDataView.setFemaleCount(medicalDataView.getCount());
                        dataMap.put(ageGroup, medicalDataView);
                    }
                }
            }
        }
        return CommonResult.success(dataMap);
    }

    @Override
    public CommonResult<List<PatientVisitDepartment>> getVisitDepartmentList(String databaseId) {
        List<PatientVisitDepartment> patientVisitDepartments = visitInformationMapper.getVisitDepartmentList(databaseId);
        return CommonResult.success(patientVisitDepartments);
    }

    @Override
    public CommonResult<List<RdrPatientDataCenterVo>> getPatientResourceDataForDataSetCode(String dataSetCode, String year, String department, String visitType) {
        List<RdrPatientDataCenterVo> dataList = new ArrayList<>();
        List<RdrPatientDataCenterVo> patientResourceDataList = rdrPatientDataCenterMapper.getPatientResourceDataForDataSetCode(dataSetCode, year, department, visitType);
        if(CollectionUtil.isNotEmpty(patientResourceDataList)){
            for (RdrPatientDataCenterVo rdrPatientDataCenterVo : patientResourceDataList) {
                RdrPatientDataCenterVo data = new RdrPatientDataCenterVo();
                BeanUtils.copyProperties(rdrPatientDataCenterVo, data);
                HashMap<String, Object> dataMap = JSON.parseObject(rdrPatientDataCenterVo.getDatasetResult(), new TypeReference<HashMap<String, Object>>(){}.getType());
                data.setResultJonsValue(dataMap);
                data.setDatasetResult(null);
                dataList.add(data);
            }
        }
        return CommonResult.success(dataList);
    }

}
