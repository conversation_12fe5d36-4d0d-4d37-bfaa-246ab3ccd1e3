package com.haoys.rdr.mapper;

import com.haoys.rdr.model.LabResult;
import com.haoys.rdr.model.LabResultExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface LabResultMapper {
    long countByExample(LabResultExample example);

    int deleteByExample(LabResultExample example);

    int deleteByPrimaryKey(String pkId);

    int insert(LabResult record);

    int insertSelective(LabResult record);

    List<LabResult> selectByExample(LabResultExample example);

    LabResult selectByPrimaryKey(String pkId);

    int updateByExampleSelective(@Param("record") LabResult record, @Param("example") LabResultExample example);

    int updateByExample(@Param("record") LabResult record, @Param("example") LabResultExample example);

    int updateByPrimaryKeySelective(LabResult record);

    int updateByPrimaryKey(LabResult record);
    
    List<LabResult> getLabResultList(String patientSn, String visitSn);
    
}
