package com.haoys.rdr.mapper;

import com.haoys.rdr.model.EmrDeathRecord;
import com.haoys.rdr.model.EmrDeathRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface EmrDeathRecordMapper {
    long countByExample(EmrDeathRecordExample example);

    int deleteByExample(EmrDeathRecordExample example);

    int deleteByPrimaryKey(String pkId);

    int insert(EmrDeathRecord record);

    int insertSelective(EmrDeathRecord record);

    List<EmrDeathRecord> selectByExample(EmrDeathRecordExample example);

    EmrDeathRecord selectByPrimaryKey(String pkId);

    int updateByExampleSelective(@Param("record") EmrDeathRecord record, @Param("example") EmrDeathRecordExample example);

    int updateByExample(@Param("record") EmrDeathRecord record, @Param("example") EmrDeathRecordExample example);

    int updateByPrimaryKeySelective(EmrDeathRecord record);

    int updateByPrimaryKey(EmrDeathRecord record);
}
