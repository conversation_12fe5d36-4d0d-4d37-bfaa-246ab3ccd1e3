package com.haoys.rdr.mapper;

import com.haoys.rdr.model.TransferHistories;
import com.haoys.rdr.model.TransferHistoriesExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TransferHistoriesMapper {
    long countByExample(TransferHistoriesExample example);

    int deleteByExample(TransferHistoriesExample example);

    int deleteByPrimaryKey(String pkId);

    int insert(TransferHistories record);

    int insertSelective(TransferHistories record);

    List<TransferHistories> selectByExample(TransferHistoriesExample example);

    TransferHistories selectByPrimaryKey(String pkId);

    int updateByExampleSelective(@Param("record") TransferHistories record, @Param("example") TransferHistoriesExample example);

    int updateByExample(@Param("record") TransferHistories record, @Param("example") TransferHistoriesExample example);

    int updateByPrimaryKeySelective(TransferHistories record);

    int updateByPrimaryKey(TransferHistories record);
}
