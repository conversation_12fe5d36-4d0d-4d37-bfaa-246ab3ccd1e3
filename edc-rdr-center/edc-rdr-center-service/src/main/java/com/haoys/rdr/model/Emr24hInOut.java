package com.haoys.rdr.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class Emr24hInOut implements Serializable {
    @ApiModelProperty(value = "医疗机构代码")
    private String hospitalCode;

    @ApiModelProperty(value = "患者id")
    private String patientSn;

    @ApiModelProperty(value = "住院号")
    private String visitSn;

    @ApiModelProperty(value = "病案号")
    private String tpatno;

    @ApiModelProperty(value = "病人姓名")
    private String name;

    @ApiModelProperty(value = "入院日期")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date admissionDateTime;

    @ApiModelProperty(value = "住院天数")
    private Integer inDays;

    @ApiModelProperty(value = "入院情况")
    private String admissionStatus;

    @ApiModelProperty(value = "入院诊断")
    private String admissionDiagnosis;

    @ApiModelProperty(value = "诊疗经过")
    private String treatProcess;

    @ApiModelProperty(value = "主诉")
    private String chiefComplaint;

    @ApiModelProperty(value = "出院情况/出院症状与体征/出院小结")
    private String dischargeStatus;

    @ApiModelProperty(value = "出院诊断")
    private String dischargeDiagnosis;

    @ApiModelProperty(value = "出院医嘱")
    private String dischargeOrder;

    @ApiModelProperty(value = "出院时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dischargeDateTime;

    @ApiModelProperty(value = "记录时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date recordTime;

    @ApiModelProperty(value = "医师签名")
    private String doctorSign;

    @ApiModelProperty(value = "溯源路径")
    private String sourcePath;

    @ApiModelProperty(value = "院内唯一id")
    private String pkId;

    @ApiModelProperty(value = "数据状态")
    private String dataState;

    @ApiModelProperty(value = "病例原文")
    private String fullText;

    @ApiModelProperty(value = "原始患者ID")
    private String patientSnOrg;

    private static final long serialVersionUID = 1L;

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }

    public String getVisitSn() {
        return visitSn;
    }

    public void setVisitSn(String visitSn) {
        this.visitSn = visitSn;
    }

    public String getTpatno() {
        return tpatno;
    }

    public void setTpatno(String tpatno) {
        this.tpatno = tpatno;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getAdmissionDateTime() {
        return admissionDateTime;
    }

    public void setAdmissionDateTime(Date admissionDateTime) {
        this.admissionDateTime = admissionDateTime;
    }

    public Integer getInDays() {
        return inDays;
    }

    public void setInDays(Integer inDays) {
        this.inDays = inDays;
    }

    public String getAdmissionStatus() {
        return admissionStatus;
    }

    public void setAdmissionStatus(String admissionStatus) {
        this.admissionStatus = admissionStatus;
    }

    public String getAdmissionDiagnosis() {
        return admissionDiagnosis;
    }

    public void setAdmissionDiagnosis(String admissionDiagnosis) {
        this.admissionDiagnosis = admissionDiagnosis;
    }

    public String getTreatProcess() {
        return treatProcess;
    }

    public void setTreatProcess(String treatProcess) {
        this.treatProcess = treatProcess;
    }

    public String getChiefComplaint() {
        return chiefComplaint;
    }

    public void setChiefComplaint(String chiefComplaint) {
        this.chiefComplaint = chiefComplaint;
    }

    public String getDischargeStatus() {
        return dischargeStatus;
    }

    public void setDischargeStatus(String dischargeStatus) {
        this.dischargeStatus = dischargeStatus;
    }

    public String getDischargeDiagnosis() {
        return dischargeDiagnosis;
    }

    public void setDischargeDiagnosis(String dischargeDiagnosis) {
        this.dischargeDiagnosis = dischargeDiagnosis;
    }

    public String getDischargeOrder() {
        return dischargeOrder;
    }

    public void setDischargeOrder(String dischargeOrder) {
        this.dischargeOrder = dischargeOrder;
    }

    public Date getDischargeDateTime() {
        return dischargeDateTime;
    }

    public void setDischargeDateTime(Date dischargeDateTime) {
        this.dischargeDateTime = dischargeDateTime;
    }

    public Date getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(Date recordTime) {
        this.recordTime = recordTime;
    }

    public String getDoctorSign() {
        return doctorSign;
    }

    public void setDoctorSign(String doctorSign) {
        this.doctorSign = doctorSign;
    }

    public String getSourcePath() {
        return sourcePath;
    }

    public void setSourcePath(String sourcePath) {
        this.sourcePath = sourcePath;
    }

    public String getPkId() {
        return pkId;
    }

    public void setPkId(String pkId) {
        this.pkId = pkId;
    }

    public String getDataState() {
        return dataState;
    }

    public void setDataState(String dataState) {
        this.dataState = dataState;
    }

    public String getFullText() {
        return fullText;
    }

    public void setFullText(String fullText) {
        this.fullText = fullText;
    }

    public String getPatientSnOrg() {
        return patientSnOrg;
    }

    public void setPatientSnOrg(String patientSnOrg) {
        this.patientSnOrg = patientSnOrg;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", hospitalCode=").append(hospitalCode);
        sb.append(", patientSn=").append(patientSn);
        sb.append(", visitSn=").append(visitSn);
        sb.append(", tpatno=").append(tpatno);
        sb.append(", name=").append(name);
        sb.append(", admissionDateTime=").append(admissionDateTime);
        sb.append(", inDays=").append(inDays);
        sb.append(", admissionStatus=").append(admissionStatus);
        sb.append(", admissionDiagnosis=").append(admissionDiagnosis);
        sb.append(", treatProcess=").append(treatProcess);
        sb.append(", chiefComplaint=").append(chiefComplaint);
        sb.append(", dischargeStatus=").append(dischargeStatus);
        sb.append(", dischargeDiagnosis=").append(dischargeDiagnosis);
        sb.append(", dischargeOrder=").append(dischargeOrder);
        sb.append(", dischargeDateTime=").append(dischargeDateTime);
        sb.append(", recordTime=").append(recordTime);
        sb.append(", doctorSign=").append(doctorSign);
        sb.append(", sourcePath=").append(sourcePath);
        sb.append(", pkId=").append(pkId);
        sb.append(", dataState=").append(dataState);
        sb.append(", fullText=").append(fullText);
        sb.append(", patientSnOrg=").append(patientSnOrg);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}