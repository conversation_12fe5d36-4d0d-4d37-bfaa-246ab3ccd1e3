package com.haoys.rdr.mapper;

import com.haoys.rdr.model.OutpRegister;
import com.haoys.rdr.model.OutpRegisterExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface OutpRegisterMapper {
    long countByExample(OutpRegisterExample example);

    int deleteByExample(OutpRegisterExample example);

    int deleteByPrimaryKey(String pkId);

    int insert(OutpRegister record);

    int insertSelective(OutpRegister record);

    List<OutpRegister> selectByExample(OutpRegisterExample example);

    OutpRegister selectByPrimaryKey(String pkId);

    int updateByExampleSelective(@Param("record") OutpRegister record, @Param("example") OutpRegisterExample example);

    int updateByExample(@Param("record") OutpRegister record, @Param("example") OutpRegisterExample example);

    int updateByPrimaryKeySelective(OutpRegister record);

    int updateByPrimaryKey(OutpRegister record);
}
