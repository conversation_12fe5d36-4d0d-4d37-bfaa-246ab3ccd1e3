package com.haoys.rdr.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.NestedQuery;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.json.JsonData;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.constants.DefineConstant;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.elasticsearch.SearchDto;
import com.haoys.rdr.domain.dto.CustomSearchDto;
import com.haoys.rdr.domain.dto.CustomSearchHeadDto;
import com.haoys.user.elasticsearch.SearchTypeEnum;
import com.haoys.rdr.domain.param.SearchParam;
import com.haoys.rdr.mapper.RdrPatientDataBaseMapper;
import com.haoys.rdr.mapper.RdrPatientNaPiSearchMapper;
import com.haoys.rdr.service.RdrModelDefineService;
import com.haoys.rdr.service.RdrPatientCustomEsSearchService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

@RequiredArgsConstructor(onConstructor = @__(@Lazy))
@DS("disease-rdr")
@Service
public class RdrPatientCustomEsSearchServiceImpl implements RdrPatientCustomEsSearchService {

    private final RdrModelDefineService rdrModelDefineService;
    private final RdrPatientNaPiSearchMapper patientNaPiSearchMapper;
    private final RdrPatientDataBaseMapper patientDataBaseMapper;


    /**
     * 左括号
     */
    private String RIGHT_BRACKET = ")";
    /**
     * 左括号
     */
    private String LEFT_BRACKET = "(";


    @Resource(name = "clientByPasswd")
    private ElasticsearchClient client;

    private static String ES_INDEX = "research_analysis_disease_data_index";

    @Override
    public CommonResult list(SearchParam param) throws IOException {
         Map<String, Object> map = new HashMap<>();
        CommonPage<Map<String, Object>> commonPage = new CommonPage<>();
        // 处理那排搜索表头
        map.put("head", param.getHeadList());
        // 获取数据
        // 根据搜索id获取纳排搜索信息
        if (StringUtils.isNotEmpty(param.getDataBaseId())){
            if (CollectionUtil.isNotEmpty(param.getSearchList())){
                List<CustomSearchDto> searchDtoList=param.getSearchList();

                LinkedHashMap<Integer,BoolQuery.Builder> builderMap = new LinkedHashMap<>();
                BoolQuery.Builder bd = new BoolQuery.Builder();
                builderMap.put(0,bd);

                int k = 0;
                for (CustomSearchDto customSearchDto : searchDtoList) {
                    if (LEFT_BRACKET.equals(customSearchDto.getBracket())) {
                        BoolQuery.Builder bd2 = new BoolQuery.Builder();
                        k++;
                        builderMap.put(k, bd2);
                        buildNestedQuery(customSearchDto, builderMap, k);
                    } else if (RIGHT_BRACKET.equals(customSearchDto.getBracket())) {
                        buildNestedQuery(customSearchDto, builderMap, k);
                        int finalK = k;
                        builderMap.get(k - 1).must(blq -> blq.bool(builderMap.get(finalK).build()));
                        k--;
                    } else {
                        buildNestedQuery(customSearchDto, builderMap, k);
                    }
                }

                // 构建es搜索条件
                SearchRequest request = SearchRequest.of(i -> {
                    SearchRequest.Builder builder = i.index(ES_INDEX).from((param.getPageNum()-1)* param.getPageSize())
                            .size(param.getPageSize()).query(bd.build()._toQuery());
                    return builder;
                });

                // 从es中获取数据
                SearchResponse<JSONObject> response = client.search(request, JSONObject.class);
                List<Hit<JSONObject>> hits = response.hits().hits();

                if (CollectionUtil.isNotEmpty(hits)){
                    // 从es数据库中获取到数据后，处理成前端所需要的数据格式
                    List<Map<String, Object>> list = buildSearchData(hits, param.getHeadList());
                    commonPage.setList(list);
                    commonPage.setTotal(response.hits().total().value());
                }
            }
        }
        commonPage.setPageNum(param.getPageNum());
        commonPage.setPageSize(param.getPageSize());
        map.put("data", commonPage);
        return CommonResult.success(map);
    }

    private void buildNestedQuery(CustomSearchDto customSearchDto, LinkedHashMap<Integer, BoolQuery.Builder> builderMap, int k) {
        NestedQuery.Builder nestedQuery = new NestedQuery.Builder();
        builder(nestedQuery, customSearchDto);
        builderMap.get(k).must(qy -> qy.nested(nested -> nestedQuery));
    }



    /**
     * 从es数据库中获取到数据后，处理成前端所需要的数据格式
     * @param hits 从es数据库中获取到的数据
     * @param head 前端要显示的表头信息
     */
    private static List<Map<String, Object>> buildSearchData(List<Hit<JSONObject>> hits,
                                                             List<CustomSearchHeadDto> head) {

        List<Map<String, Object>> list = new ArrayList<>();
        for (Hit hit : hits) {
            JSONObject source = (JSONObject) hit.source();
            Map<String,Object> data = new HashMap<>();

            Object patientsObj = source.get(StrUtil.toCamelCase("patients"));
            if (patientsObj!=null){
                LinkedHashMap<?, ?> patients = (LinkedHashMap<?, ?>) patientsObj;
                data.put("patient_sn", patients.get("patientSn"));
                data.put("gender",patients.get("gender"));
                data.put("date_of_birth",patients.get("dateOfBirth"));
                data.put("nation",patients.get("nation"));
                data.put("year_of_birth",patients.get("yearOfBirth"));
                data.put("age",patients.get("age"));
            }
            head.forEach(var->{
                Object obj = source.get(StrUtil.toCamelCase(var.getFormCode()));
                if (obj!=null){
                    if (obj instanceof LinkedHashMap ){
                        Object val = ((LinkedHashMap<?, ?>) obj).get(StrUtil.toCamelCase(var.getFieldCode()));
                        data.put(var.getFieldCode(),val);
                    }else if (obj instanceof ArrayList){
                        ArrayList<?> obj1 = (ArrayList<?>) obj;
                        if (CollectionUtil.isNotEmpty(obj1)){
                            Object o = obj1.get(0);
                            if (o instanceof LinkedHashMap ){
                                Object val = ((LinkedHashMap<?, ?>) o).get(StrUtil.toCamelCase(var.getFieldCode()));
                                data.put(var.getFieldCode(),val);
                            }
                        }
                    }
                }
            });
            list.add(data);
        }
        return list;
    }


    private void builder(NestedQuery.Builder builder, SearchDto searchDto) {
        String formCode = StrUtil.toCamelCase(searchDto.getFormCode());
        builder.path(formCode);

        final String filed = formCode + "." + StrUtil.toCamelCase(searchDto.getFieldCode());

        if (DefineConstant.CONTAINS_CODE.equals(searchDto.getValueType())) {
            // 包含搜索条件构建
            searchLike(searchDto, builder, filed);
        } else if (DefineConstant.NOT_CONTAINS_CODE.equals(searchDto.getValueType())) {
            // 不包含搜索条件构建
            searchNotLike(searchDto, builder, filed);
        } else if (DefineConstant.LESS_THAN_CODE.equals(searchDto.getValueType())) {
            // 小于搜索条件构建
            searchLt(searchDto, builder, filed);
        } else if (DefineConstant.GREATER_THAN_CODE.equals(searchDto.getValueType())) {
            // 大于搜索条件构建
            searchGt(searchDto, builder, filed);
        } else if (DefineConstant.EQUAL_CODE.equals(searchDto.getValueType())) {
            // 精确匹配搜索条件构建
            searchEqual(searchDto, filed, builder);
        } else if (DefineConstant.NOT_EQUAL_CODE.equals(searchDto.getValueType())) {
            // 不等于精确匹配搜索条件构建
            searchNotEqual(searchDto, filed, builder);
        } else if (DefineConstant.LESS_THAN_EQUAL_CODE.equals(searchDto.getValueType())) {
            // 小于等于搜索条件构建
            searchLte(searchDto, builder, filed);
        } else if (DefineConstant.GREATER_THAN_EQUAL_CODE.equals(searchDto.getValueType())) {
            // 大于等于搜索条件构建
            searchGte(searchDto, builder, filed);
        }
    }

    /**
     * 大于等于搜索条件构建
     *
     * @param searchDto
     * @param builder
     * @param filed
     */
    private static void searchGte(SearchDto searchDto, NestedQuery.Builder builder, String filed) {
        if (SearchTypeEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .must(must -> must.range(range -> range.field(filed).gte(JsonData.of(searchDto.getSearchValue()))))
                    )
            );
        } else if (SearchTypeEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .should(should -> should.range(range -> range.field(filed).gte(JsonData.of(searchDto.getSearchValue()))))
                    )
            );
        } else if (SearchTypeEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .mustNot(mustNot -> mustNot.range(range -> range.field(filed).gte(JsonData.of(searchDto.getSearchValue()))))
                    )
            );
        }
    }

    /**
     * 小于等于搜索条件构建
     *
     * @param searchDto
     * @param builder
     * @param filed
     */
    private static void searchLte(SearchDto searchDto, NestedQuery.Builder builder, String filed) {
        if (SearchTypeEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .must(must -> must.range(range -> range.field(filed).lte(JsonData.of(searchDto.getSearchValue()))))
                    )
            );
        } else if (SearchTypeEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .should(should -> should.range(range -> range.field(filed).lte(JsonData.of(searchDto.getSearchValue()))))
                    )
            );
        } else if (SearchTypeEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .mustNot(mustNot -> mustNot.range(range -> range.field(filed).lte(JsonData.of(searchDto.getSearchValue()))))
                    )
            );
        }
    }

    /**
     * 不等于搜索条件构建
     *
     * @param searchDto
     * @param filed
     * @param builder
     */
    private static void searchNotEqual(SearchDto searchDto, String filed, NestedQuery.Builder builder) {
        String filedValue = filed;

        if ("varchar".equals(searchDto.getVariableType())
                || "date".equals(searchDto.getVariableType())
                || "timestamp".equals(searchDto.getVariableType())) {
            filedValue += ".keyword";
        }
        String finalFiledValue = filedValue;

        if (SearchTypeEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .mustNot(mustNot -> mustNot.term(term -> term.field(finalFiledValue).value(searchDto.getSearchValue())))
                    )
            );
        } else if (SearchTypeEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .should(should -> should.term(term -> term.field(finalFiledValue).value(searchDto.getSearchValue())))
                    )
            );
        } else if (SearchTypeEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .must(must -> must.term(term -> term.field(finalFiledValue).value(searchDto.getSearchValue())))
                    )
            );
        }
    }

    /**
     * 精确匹配搜索条件构建
     *
     * @param searchDto
     * @param filed
     * @param builder
     */
    private static void searchEqual(SearchDto searchDto, String filed, NestedQuery.Builder builder) {
        String filedValue = filed;

        if ("varchar".equals(searchDto.getVariableType())
                || "date".equals(searchDto.getVariableType())
                || "timestamp".equals(searchDto.getVariableType())) {
            filedValue += ".keyword";
        }

        String finalFiledValue = filedValue;

        if (SearchTypeEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .must(must -> must.term(term -> term.field(finalFiledValue).value(searchDto.getSearchValue())))
                    )
            );
        } else if (SearchTypeEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .should(should -> should.term(term -> term.field(finalFiledValue).value(searchDto.getSearchValue())))
                    )
            );
        } else if (SearchTypeEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .mustNot(mustNot -> mustNot.term(term -> term.field(finalFiledValue).value(searchDto.getSearchValue())))
                    )
            );
        }
    }

    /**
     * 大于搜索条件构建
     *
     * @param searchDto
     * @param builder
     * @param filed
     */
    private static void searchGt(SearchDto searchDto, NestedQuery.Builder builder, String filed) {
        if (SearchTypeEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .must(must -> must.range(range -> range.field(filed).gt(JsonData.of(searchDto.getSearchValue()))))
                    )
            );
        } else if (SearchTypeEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .should(should -> should.range(range -> range.field(filed).gt(JsonData.of(searchDto.getSearchValue()))))
                    )
            );
        } else if (SearchTypeEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .mustNot(mustNot -> mustNot.range(range -> range.field(filed).gt(JsonData.of(searchDto.getSearchValue()))))
                    )
            );
        }
    }

    /**
     * 小于搜索条件构建
     *
     * @param searchDto
     * @param builder
     * @param filed
     */
    private static void searchLt(SearchDto searchDto, NestedQuery.Builder builder, String filed) {
        if (SearchTypeEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .must(must -> must.range(range -> range.field(filed).lt(JsonData.of(searchDto.getSearchValue()))))
                    )
            );
        } else if (SearchTypeEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .should(should -> should.range(range -> range.field(filed).lt(JsonData.of(searchDto.getSearchValue()))))
                    )
            );
        } else if (SearchTypeEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .mustNot(mustNot -> mustNot.range(range -> range.field(filed).lt(JsonData.of(searchDto.getSearchValue()))))
                    )
            );
        }
    }

    /**
     * 不包含搜索条件构建
     *
     * @param searchDto
     * @param builder
     * @param filed
     */
    private static void searchNotLike(SearchDto searchDto, NestedQuery.Builder builder, String filed) {

        if (SearchTypeEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .mustNot(mustNot -> mustNot.wildcard(w -> w.field(filed).wildcard("*"+searchDto.getSearchValue()+"*")))
                    )
            );
        } else if (SearchTypeEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .should(should -> should.wildcard(w -> w.wildcard(filed).wildcard("*"+searchDto.getSearchValue()+"*")))
                    )
            );
        } else if (SearchTypeEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .must(must -> must.wildcard(w -> w.field(filed).wildcard("*"+searchDto.getSearchValue()+"*")))
                    )
            );
        }

    }

    /**
     * 包含搜索条件构建
     *
     * @param searchDto
     * @param builder
     * @param filed
     */
    private static void searchLike(SearchDto searchDto, NestedQuery.Builder builder, String filed) {
        if (SearchTypeEnum.AND.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .must(must -> must.wildcard(w -> w.field(filed).wildcard("*"+searchDto.getSearchValue()+"*")))
                    )
            );
        } else if (SearchTypeEnum.OR.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .should(should -> should.wildcard(w -> w.wildcard(filed).wildcard("*"+searchDto.getSearchValue()+"*")))
                    )
            );
        } else if (SearchTypeEnum.NOT.getCode().equals(searchDto.getSearchType())) {
            builder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool
                                    .mustNot(mustNot -> mustNot.wildcard(w -> w.field(filed).wildcard("*"+searchDto.getSearchValue()+"*")))
                    )
            );
        }
    }

}
