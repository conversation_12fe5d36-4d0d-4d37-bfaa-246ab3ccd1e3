package com.haoys.rdr.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class InpVisitDiagExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public InpVisitDiagExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPkIdIsNull() {
            addCriterion("\"pk_id\" is null");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNotNull() {
            addCriterion("\"pk_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkIdEqualTo(String value) {
            addCriterion("\"pk_id\" =", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotEqualTo(String value) {
            addCriterion("\"pk_id\" <>", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThan(String value) {
            addCriterion("\"pk_id\" >", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" >=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThan(String value) {
            addCriterion("\"pk_id\" <", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" <=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLike(String value) {
            addCriterion("\"pk_id\" like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotLike(String value) {
            addCriterion("\"pk_id\" not like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdIn(List<String> values) {
            addCriterion("\"pk_id\" in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotIn(List<String> values) {
            addCriterion("\"pk_id\" not in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdBetween(String value1, String value2) {
            addCriterion("\"pk_id\" between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotBetween(String value1, String value2) {
            addCriterion("\"pk_id\" not between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIsNull() {
            addCriterion("\"hospital_code\" is null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIsNotNull() {
            addCriterion("\"hospital_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeEqualTo(String value) {
            addCriterion("\"hospital_code\" =", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotEqualTo(String value) {
            addCriterion("\"hospital_code\" <>", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThan(String value) {
            addCriterion("\"hospital_code\" >", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" >=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThan(String value) {
            addCriterion("\"hospital_code\" <", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" <=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLike(String value) {
            addCriterion("\"hospital_code\" like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotLike(String value) {
            addCriterion("\"hospital_code\" not like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIn(List<String> values) {
            addCriterion("\"hospital_code\" in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotIn(List<String> values) {
            addCriterion("\"hospital_code\" not in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" not between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andTpatnoIsNull() {
            addCriterion("\"tpatno\" is null");
            return (Criteria) this;
        }

        public Criteria andTpatnoIsNotNull() {
            addCriterion("\"tpatno\" is not null");
            return (Criteria) this;
        }

        public Criteria andTpatnoEqualTo(String value) {
            addCriterion("\"tpatno\" =", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotEqualTo(String value) {
            addCriterion("\"tpatno\" <>", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoGreaterThan(String value) {
            addCriterion("\"tpatno\" >", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoGreaterThanOrEqualTo(String value) {
            addCriterion("\"tpatno\" >=", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLessThan(String value) {
            addCriterion("\"tpatno\" <", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLessThanOrEqualTo(String value) {
            addCriterion("\"tpatno\" <=", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLike(String value) {
            addCriterion("\"tpatno\" like", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotLike(String value) {
            addCriterion("\"tpatno\" not like", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoIn(List<String> values) {
            addCriterion("\"tpatno\" in", values, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotIn(List<String> values) {
            addCriterion("\"tpatno\" not in", values, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoBetween(String value1, String value2) {
            addCriterion("\"tpatno\" between", value1, value2, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotBetween(String value1, String value2) {
            addCriterion("\"tpatno\" not between", value1, value2, "tpatno");
            return (Criteria) this;
        }

        public Criteria andDiagnosisTypeIsNull() {
            addCriterion("\"diagnosis_type\" is null");
            return (Criteria) this;
        }

        public Criteria andDiagnosisTypeIsNotNull() {
            addCriterion("\"diagnosis_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andDiagnosisTypeEqualTo(String value) {
            addCriterion("\"diagnosis_type\" =", value, "diagnosisType");
            return (Criteria) this;
        }

        public Criteria andDiagnosisTypeNotEqualTo(String value) {
            addCriterion("\"diagnosis_type\" <>", value, "diagnosisType");
            return (Criteria) this;
        }

        public Criteria andDiagnosisTypeGreaterThan(String value) {
            addCriterion("\"diagnosis_type\" >", value, "diagnosisType");
            return (Criteria) this;
        }

        public Criteria andDiagnosisTypeGreaterThanOrEqualTo(String value) {
            addCriterion("\"diagnosis_type\" >=", value, "diagnosisType");
            return (Criteria) this;
        }

        public Criteria andDiagnosisTypeLessThan(String value) {
            addCriterion("\"diagnosis_type\" <", value, "diagnosisType");
            return (Criteria) this;
        }

        public Criteria andDiagnosisTypeLessThanOrEqualTo(String value) {
            addCriterion("\"diagnosis_type\" <=", value, "diagnosisType");
            return (Criteria) this;
        }

        public Criteria andDiagnosisTypeLike(String value) {
            addCriterion("\"diagnosis_type\" like", value, "diagnosisType");
            return (Criteria) this;
        }

        public Criteria andDiagnosisTypeNotLike(String value) {
            addCriterion("\"diagnosis_type\" not like", value, "diagnosisType");
            return (Criteria) this;
        }

        public Criteria andDiagnosisTypeIn(List<String> values) {
            addCriterion("\"diagnosis_type\" in", values, "diagnosisType");
            return (Criteria) this;
        }

        public Criteria andDiagnosisTypeNotIn(List<String> values) {
            addCriterion("\"diagnosis_type\" not in", values, "diagnosisType");
            return (Criteria) this;
        }

        public Criteria andDiagnosisTypeBetween(String value1, String value2) {
            addCriterion("\"diagnosis_type\" between", value1, value2, "diagnosisType");
            return (Criteria) this;
        }

        public Criteria andDiagnosisTypeNotBetween(String value1, String value2) {
            addCriterion("\"diagnosis_type\" not between", value1, value2, "diagnosisType");
            return (Criteria) this;
        }

        public Criteria andDiagnosisClassIsNull() {
            addCriterion("\"diagnosis_class\" is null");
            return (Criteria) this;
        }

        public Criteria andDiagnosisClassIsNotNull() {
            addCriterion("\"diagnosis_class\" is not null");
            return (Criteria) this;
        }

        public Criteria andDiagnosisClassEqualTo(String value) {
            addCriterion("\"diagnosis_class\" =", value, "diagnosisClass");
            return (Criteria) this;
        }

        public Criteria andDiagnosisClassNotEqualTo(String value) {
            addCriterion("\"diagnosis_class\" <>", value, "diagnosisClass");
            return (Criteria) this;
        }

        public Criteria andDiagnosisClassGreaterThan(String value) {
            addCriterion("\"diagnosis_class\" >", value, "diagnosisClass");
            return (Criteria) this;
        }

        public Criteria andDiagnosisClassGreaterThanOrEqualTo(String value) {
            addCriterion("\"diagnosis_class\" >=", value, "diagnosisClass");
            return (Criteria) this;
        }

        public Criteria andDiagnosisClassLessThan(String value) {
            addCriterion("\"diagnosis_class\" <", value, "diagnosisClass");
            return (Criteria) this;
        }

        public Criteria andDiagnosisClassLessThanOrEqualTo(String value) {
            addCriterion("\"diagnosis_class\" <=", value, "diagnosisClass");
            return (Criteria) this;
        }

        public Criteria andDiagnosisClassLike(String value) {
            addCriterion("\"diagnosis_class\" like", value, "diagnosisClass");
            return (Criteria) this;
        }

        public Criteria andDiagnosisClassNotLike(String value) {
            addCriterion("\"diagnosis_class\" not like", value, "diagnosisClass");
            return (Criteria) this;
        }

        public Criteria andDiagnosisClassIn(List<String> values) {
            addCriterion("\"diagnosis_class\" in", values, "diagnosisClass");
            return (Criteria) this;
        }

        public Criteria andDiagnosisClassNotIn(List<String> values) {
            addCriterion("\"diagnosis_class\" not in", values, "diagnosisClass");
            return (Criteria) this;
        }

        public Criteria andDiagnosisClassBetween(String value1, String value2) {
            addCriterion("\"diagnosis_class\" between", value1, value2, "diagnosisClass");
            return (Criteria) this;
        }

        public Criteria andDiagnosisClassNotBetween(String value1, String value2) {
            addCriterion("\"diagnosis_class\" not between", value1, value2, "diagnosisClass");
            return (Criteria) this;
        }

        public Criteria andDiagnosisOrderNoIsNull() {
            addCriterion("\"diagnosis_order_no\" is null");
            return (Criteria) this;
        }

        public Criteria andDiagnosisOrderNoIsNotNull() {
            addCriterion("\"diagnosis_order_no\" is not null");
            return (Criteria) this;
        }

        public Criteria andDiagnosisOrderNoEqualTo(Integer value) {
            addCriterion("\"diagnosis_order_no\" =", value, "diagnosisOrderNo");
            return (Criteria) this;
        }

        public Criteria andDiagnosisOrderNoNotEqualTo(Integer value) {
            addCriterion("\"diagnosis_order_no\" <>", value, "diagnosisOrderNo");
            return (Criteria) this;
        }

        public Criteria andDiagnosisOrderNoGreaterThan(Integer value) {
            addCriterion("\"diagnosis_order_no\" >", value, "diagnosisOrderNo");
            return (Criteria) this;
        }

        public Criteria andDiagnosisOrderNoGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"diagnosis_order_no\" >=", value, "diagnosisOrderNo");
            return (Criteria) this;
        }

        public Criteria andDiagnosisOrderNoLessThan(Integer value) {
            addCriterion("\"diagnosis_order_no\" <", value, "diagnosisOrderNo");
            return (Criteria) this;
        }

        public Criteria andDiagnosisOrderNoLessThanOrEqualTo(Integer value) {
            addCriterion("\"diagnosis_order_no\" <=", value, "diagnosisOrderNo");
            return (Criteria) this;
        }

        public Criteria andDiagnosisOrderNoIn(List<Integer> values) {
            addCriterion("\"diagnosis_order_no\" in", values, "diagnosisOrderNo");
            return (Criteria) this;
        }

        public Criteria andDiagnosisOrderNoNotIn(List<Integer> values) {
            addCriterion("\"diagnosis_order_no\" not in", values, "diagnosisOrderNo");
            return (Criteria) this;
        }

        public Criteria andDiagnosisOrderNoBetween(Integer value1, Integer value2) {
            addCriterion("\"diagnosis_order_no\" between", value1, value2, "diagnosisOrderNo");
            return (Criteria) this;
        }

        public Criteria andDiagnosisOrderNoNotBetween(Integer value1, Integer value2) {
            addCriterion("\"diagnosis_order_no\" not between", value1, value2, "diagnosisOrderNo");
            return (Criteria) this;
        }

        public Criteria andDiagnosisNameIsNull() {
            addCriterion("\"diagnosis_name\" is null");
            return (Criteria) this;
        }

        public Criteria andDiagnosisNameIsNotNull() {
            addCriterion("\"diagnosis_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andDiagnosisNameEqualTo(String value) {
            addCriterion("\"diagnosis_name\" =", value, "diagnosisName");
            return (Criteria) this;
        }

        public Criteria andDiagnosisNameNotEqualTo(String value) {
            addCriterion("\"diagnosis_name\" <>", value, "diagnosisName");
            return (Criteria) this;
        }

        public Criteria andDiagnosisNameGreaterThan(String value) {
            addCriterion("\"diagnosis_name\" >", value, "diagnosisName");
            return (Criteria) this;
        }

        public Criteria andDiagnosisNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"diagnosis_name\" >=", value, "diagnosisName");
            return (Criteria) this;
        }

        public Criteria andDiagnosisNameLessThan(String value) {
            addCriterion("\"diagnosis_name\" <", value, "diagnosisName");
            return (Criteria) this;
        }

        public Criteria andDiagnosisNameLessThanOrEqualTo(String value) {
            addCriterion("\"diagnosis_name\" <=", value, "diagnosisName");
            return (Criteria) this;
        }

        public Criteria andDiagnosisNameLike(String value) {
            addCriterion("\"diagnosis_name\" like", value, "diagnosisName");
            return (Criteria) this;
        }

        public Criteria andDiagnosisNameNotLike(String value) {
            addCriterion("\"diagnosis_name\" not like", value, "diagnosisName");
            return (Criteria) this;
        }

        public Criteria andDiagnosisNameIn(List<String> values) {
            addCriterion("\"diagnosis_name\" in", values, "diagnosisName");
            return (Criteria) this;
        }

        public Criteria andDiagnosisNameNotIn(List<String> values) {
            addCriterion("\"diagnosis_name\" not in", values, "diagnosisName");
            return (Criteria) this;
        }

        public Criteria andDiagnosisNameBetween(String value1, String value2) {
            addCriterion("\"diagnosis_name\" between", value1, value2, "diagnosisName");
            return (Criteria) this;
        }

        public Criteria andDiagnosisNameNotBetween(String value1, String value2) {
            addCriterion("\"diagnosis_name\" not between", value1, value2, "diagnosisName");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCodeIsNull() {
            addCriterion("\"diagnosis_code\" is null");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCodeIsNotNull() {
            addCriterion("\"diagnosis_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCodeEqualTo(String value) {
            addCriterion("\"diagnosis_code\" =", value, "diagnosisCode");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCodeNotEqualTo(String value) {
            addCriterion("\"diagnosis_code\" <>", value, "diagnosisCode");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCodeGreaterThan(String value) {
            addCriterion("\"diagnosis_code\" >", value, "diagnosisCode");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"diagnosis_code\" >=", value, "diagnosisCode");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCodeLessThan(String value) {
            addCriterion("\"diagnosis_code\" <", value, "diagnosisCode");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCodeLessThanOrEqualTo(String value) {
            addCriterion("\"diagnosis_code\" <=", value, "diagnosisCode");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCodeLike(String value) {
            addCriterion("\"diagnosis_code\" like", value, "diagnosisCode");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCodeNotLike(String value) {
            addCriterion("\"diagnosis_code\" not like", value, "diagnosisCode");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCodeIn(List<String> values) {
            addCriterion("\"diagnosis_code\" in", values, "diagnosisCode");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCodeNotIn(List<String> values) {
            addCriterion("\"diagnosis_code\" not in", values, "diagnosisCode");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCodeBetween(String value1, String value2) {
            addCriterion("\"diagnosis_code\" between", value1, value2, "diagnosisCode");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCodeNotBetween(String value1, String value2) {
            addCriterion("\"diagnosis_code\" not between", value1, value2, "diagnosisCode");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCode2IsNull() {
            addCriterion("\"diagnosis_code2\" is null");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCode2IsNotNull() {
            addCriterion("\"diagnosis_code2\" is not null");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCode2EqualTo(String value) {
            addCriterion("\"diagnosis_code2\" =", value, "diagnosisCode2");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCode2NotEqualTo(String value) {
            addCriterion("\"diagnosis_code2\" <>", value, "diagnosisCode2");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCode2GreaterThan(String value) {
            addCriterion("\"diagnosis_code2\" >", value, "diagnosisCode2");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCode2GreaterThanOrEqualTo(String value) {
            addCriterion("\"diagnosis_code2\" >=", value, "diagnosisCode2");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCode2LessThan(String value) {
            addCriterion("\"diagnosis_code2\" <", value, "diagnosisCode2");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCode2LessThanOrEqualTo(String value) {
            addCriterion("\"diagnosis_code2\" <=", value, "diagnosisCode2");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCode2Like(String value) {
            addCriterion("\"diagnosis_code2\" like", value, "diagnosisCode2");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCode2NotLike(String value) {
            addCriterion("\"diagnosis_code2\" not like", value, "diagnosisCode2");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCode2In(List<String> values) {
            addCriterion("\"diagnosis_code2\" in", values, "diagnosisCode2");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCode2NotIn(List<String> values) {
            addCriterion("\"diagnosis_code2\" not in", values, "diagnosisCode2");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCode2Between(String value1, String value2) {
            addCriterion("\"diagnosis_code2\" between", value1, value2, "diagnosisCode2");
            return (Criteria) this;
        }

        public Criteria andDiagnosisCode2NotBetween(String value1, String value2) {
            addCriterion("\"diagnosis_code2\" not between", value1, value2, "diagnosisCode2");
            return (Criteria) this;
        }

        public Criteria andIsMainIsNull() {
            addCriterion("\"is_main\" is null");
            return (Criteria) this;
        }

        public Criteria andIsMainIsNotNull() {
            addCriterion("\"is_main\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsMainEqualTo(String value) {
            addCriterion("\"is_main\" =", value, "isMain");
            return (Criteria) this;
        }

        public Criteria andIsMainNotEqualTo(String value) {
            addCriterion("\"is_main\" <>", value, "isMain");
            return (Criteria) this;
        }

        public Criteria andIsMainGreaterThan(String value) {
            addCriterion("\"is_main\" >", value, "isMain");
            return (Criteria) this;
        }

        public Criteria andIsMainGreaterThanOrEqualTo(String value) {
            addCriterion("\"is_main\" >=", value, "isMain");
            return (Criteria) this;
        }

        public Criteria andIsMainLessThan(String value) {
            addCriterion("\"is_main\" <", value, "isMain");
            return (Criteria) this;
        }

        public Criteria andIsMainLessThanOrEqualTo(String value) {
            addCriterion("\"is_main\" <=", value, "isMain");
            return (Criteria) this;
        }

        public Criteria andIsMainLike(String value) {
            addCriterion("\"is_main\" like", value, "isMain");
            return (Criteria) this;
        }

        public Criteria andIsMainNotLike(String value) {
            addCriterion("\"is_main\" not like", value, "isMain");
            return (Criteria) this;
        }

        public Criteria andIsMainIn(List<String> values) {
            addCriterion("\"is_main\" in", values, "isMain");
            return (Criteria) this;
        }

        public Criteria andIsMainNotIn(List<String> values) {
            addCriterion("\"is_main\" not in", values, "isMain");
            return (Criteria) this;
        }

        public Criteria andIsMainBetween(String value1, String value2) {
            addCriterion("\"is_main\" between", value1, value2, "isMain");
            return (Criteria) this;
        }

        public Criteria andIsMainNotBetween(String value1, String value2) {
            addCriterion("\"is_main\" not between", value1, value2, "isMain");
            return (Criteria) this;
        }

        public Criteria andDiagnosisDateIsNull() {
            addCriterion("\"diagnosis_date\" is null");
            return (Criteria) this;
        }

        public Criteria andDiagnosisDateIsNotNull() {
            addCriterion("\"diagnosis_date\" is not null");
            return (Criteria) this;
        }

        public Criteria andDiagnosisDateEqualTo(Date value) {
            addCriterion("\"diagnosis_date\" =", value, "diagnosisDate");
            return (Criteria) this;
        }

        public Criteria andDiagnosisDateNotEqualTo(Date value) {
            addCriterion("\"diagnosis_date\" <>", value, "diagnosisDate");
            return (Criteria) this;
        }

        public Criteria andDiagnosisDateGreaterThan(Date value) {
            addCriterion("\"diagnosis_date\" >", value, "diagnosisDate");
            return (Criteria) this;
        }

        public Criteria andDiagnosisDateGreaterThanOrEqualTo(Date value) {
            addCriterion("\"diagnosis_date\" >=", value, "diagnosisDate");
            return (Criteria) this;
        }

        public Criteria andDiagnosisDateLessThan(Date value) {
            addCriterion("\"diagnosis_date\" <", value, "diagnosisDate");
            return (Criteria) this;
        }

        public Criteria andDiagnosisDateLessThanOrEqualTo(Date value) {
            addCriterion("\"diagnosis_date\" <=", value, "diagnosisDate");
            return (Criteria) this;
        }

        public Criteria andDiagnosisDateIn(List<Date> values) {
            addCriterion("\"diagnosis_date\" in", values, "diagnosisDate");
            return (Criteria) this;
        }

        public Criteria andDiagnosisDateNotIn(List<Date> values) {
            addCriterion("\"diagnosis_date\" not in", values, "diagnosisDate");
            return (Criteria) this;
        }

        public Criteria andDiagnosisDateBetween(Date value1, Date value2) {
            addCriterion("\"diagnosis_date\" between", value1, value2, "diagnosisDate");
            return (Criteria) this;
        }

        public Criteria andDiagnosisDateNotBetween(Date value1, Date value2) {
            addCriterion("\"diagnosis_date\" not between", value1, value2, "diagnosisDate");
            return (Criteria) this;
        }

        public Criteria andDoctorIsNull() {
            addCriterion("\"doctor\" is null");
            return (Criteria) this;
        }

        public Criteria andDoctorIsNotNull() {
            addCriterion("\"doctor\" is not null");
            return (Criteria) this;
        }

        public Criteria andDoctorEqualTo(String value) {
            addCriterion("\"doctor\" =", value, "doctor");
            return (Criteria) this;
        }

        public Criteria andDoctorNotEqualTo(String value) {
            addCriterion("\"doctor\" <>", value, "doctor");
            return (Criteria) this;
        }

        public Criteria andDoctorGreaterThan(String value) {
            addCriterion("\"doctor\" >", value, "doctor");
            return (Criteria) this;
        }

        public Criteria andDoctorGreaterThanOrEqualTo(String value) {
            addCriterion("\"doctor\" >=", value, "doctor");
            return (Criteria) this;
        }

        public Criteria andDoctorLessThan(String value) {
            addCriterion("\"doctor\" <", value, "doctor");
            return (Criteria) this;
        }

        public Criteria andDoctorLessThanOrEqualTo(String value) {
            addCriterion("\"doctor\" <=", value, "doctor");
            return (Criteria) this;
        }

        public Criteria andDoctorLike(String value) {
            addCriterion("\"doctor\" like", value, "doctor");
            return (Criteria) this;
        }

        public Criteria andDoctorNotLike(String value) {
            addCriterion("\"doctor\" not like", value, "doctor");
            return (Criteria) this;
        }

        public Criteria andDoctorIn(List<String> values) {
            addCriterion("\"doctor\" in", values, "doctor");
            return (Criteria) this;
        }

        public Criteria andDoctorNotIn(List<String> values) {
            addCriterion("\"doctor\" not in", values, "doctor");
            return (Criteria) this;
        }

        public Criteria andDoctorBetween(String value1, String value2) {
            addCriterion("\"doctor\" between", value1, value2, "doctor");
            return (Criteria) this;
        }

        public Criteria andDoctorNotBetween(String value1, String value2) {
            addCriterion("\"doctor\" not between", value1, value2, "doctor");
            return (Criteria) this;
        }

        public Criteria andDeptNameIsNull() {
            addCriterion("\"dept_name\" is null");
            return (Criteria) this;
        }

        public Criteria andDeptNameIsNotNull() {
            addCriterion("\"dept_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andDeptNameEqualTo(String value) {
            addCriterion("\"dept_name\" =", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameNotEqualTo(String value) {
            addCriterion("\"dept_name\" <>", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameGreaterThan(String value) {
            addCriterion("\"dept_name\" >", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"dept_name\" >=", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameLessThan(String value) {
            addCriterion("\"dept_name\" <", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameLessThanOrEqualTo(String value) {
            addCriterion("\"dept_name\" <=", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameLike(String value) {
            addCriterion("\"dept_name\" like", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameNotLike(String value) {
            addCriterion("\"dept_name\" not like", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameIn(List<String> values) {
            addCriterion("\"dept_name\" in", values, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameNotIn(List<String> values) {
            addCriterion("\"dept_name\" not in", values, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameBetween(String value1, String value2) {
            addCriterion("\"dept_name\" between", value1, value2, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameNotBetween(String value1, String value2) {
            addCriterion("\"dept_name\" not between", value1, value2, "deptName");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNull() {
            addCriterion("\"source_path\" is null");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNotNull() {
            addCriterion("\"source_path\" is not null");
            return (Criteria) this;
        }

        public Criteria andSourcePathEqualTo(String value) {
            addCriterion("\"source_path\" =", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotEqualTo(String value) {
            addCriterion("\"source_path\" <>", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThan(String value) {
            addCriterion("\"source_path\" >", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThanOrEqualTo(String value) {
            addCriterion("\"source_path\" >=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThan(String value) {
            addCriterion("\"source_path\" <", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThanOrEqualTo(String value) {
            addCriterion("\"source_path\" <=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLike(String value) {
            addCriterion("\"source_path\" like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotLike(String value) {
            addCriterion("\"source_path\" not like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathIn(List<String> values) {
            addCriterion("\"source_path\" in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotIn(List<String> values) {
            addCriterion("\"source_path\" not in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathBetween(String value1, String value2) {
            addCriterion("\"source_path\" between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotBetween(String value1, String value2) {
            addCriterion("\"source_path\" not between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNull() {
            addCriterion("\"data_state\" is null");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNotNull() {
            addCriterion("\"data_state\" is not null");
            return (Criteria) this;
        }

        public Criteria andDataStateEqualTo(String value) {
            addCriterion("\"data_state\" =", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotEqualTo(String value) {
            addCriterion("\"data_state\" <>", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThan(String value) {
            addCriterion("\"data_state\" >", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThanOrEqualTo(String value) {
            addCriterion("\"data_state\" >=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThan(String value) {
            addCriterion("\"data_state\" <", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThanOrEqualTo(String value) {
            addCriterion("\"data_state\" <=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLike(String value) {
            addCriterion("\"data_state\" like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotLike(String value) {
            addCriterion("\"data_state\" not like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateIn(List<String> values) {
            addCriterion("\"data_state\" in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotIn(List<String> values) {
            addCriterion("\"data_state\" not in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateBetween(String value1, String value2) {
            addCriterion("\"data_state\" between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotBetween(String value1, String value2) {
            addCriterion("\"data_state\" not between", value1, value2, "dataState");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}