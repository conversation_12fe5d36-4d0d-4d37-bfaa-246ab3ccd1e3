package com.haoys.rdr.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class EmrFirstCourseAfterOper implements Serializable {
    @ApiModelProperty(value = "医疗机构代码")
    private String hospitalCode;

    @ApiModelProperty(value = "患者ID")
    private String patientSn;

    @ApiModelProperty(value = "住院号")
    private String visitSn;

    @ApiModelProperty(value = "病案号")
    private String tpatno;

    @ApiModelProperty(value = "手术时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operationTime;

    @ApiModelProperty(value = "手术持续时间")
    private String operationDuration;

    @ApiModelProperty(value = "术中诊断")
    private String diagPerioperation;

    @ApiModelProperty(value = "麻醉方式")
    private String anesthesiaMethod;

    @ApiModelProperty(value = "手术方式")
    private String operationApproach;

    @ApiModelProperty(value = "手术经过")
    private String operationProcess;

    @ApiModelProperty(value = "术后处置")
    private String postOperation;

    @ApiModelProperty(value = "术后注意事项")
    private String postOpConsideration;

    @ApiModelProperty(value = "记录时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date recordTime;

    @ApiModelProperty(value = "医师签名")
    private String doctorSign;

    @ApiModelProperty(value = "溯源路径")
    private String sourcePath;

    @ApiModelProperty(value = "院内唯一id")
    private String pkId;

    @ApiModelProperty(value = "数据状态")
    private String dataState;

    @ApiModelProperty(value = "病例原文")
    private String fullText;

    @ApiModelProperty(value = "原始患者ID")
    private String patientSnOrg;

    private static final long serialVersionUID = 1L;

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }

    public String getVisitSn() {
        return visitSn;
    }

    public void setVisitSn(String visitSn) {
        this.visitSn = visitSn;
    }

    public String getTpatno() {
        return tpatno;
    }

    public void setTpatno(String tpatno) {
        this.tpatno = tpatno;
    }

    public Date getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(Date operationTime) {
        this.operationTime = operationTime;
    }

    public String getOperationDuration() {
        return operationDuration;
    }

    public void setOperationDuration(String operationDuration) {
        this.operationDuration = operationDuration;
    }

    public String getDiagPerioperation() {
        return diagPerioperation;
    }

    public void setDiagPerioperation(String diagPerioperation) {
        this.diagPerioperation = diagPerioperation;
    }

    public String getAnesthesiaMethod() {
        return anesthesiaMethod;
    }

    public void setAnesthesiaMethod(String anesthesiaMethod) {
        this.anesthesiaMethod = anesthesiaMethod;
    }

    public String getOperationApproach() {
        return operationApproach;
    }

    public void setOperationApproach(String operationApproach) {
        this.operationApproach = operationApproach;
    }

    public String getOperationProcess() {
        return operationProcess;
    }

    public void setOperationProcess(String operationProcess) {
        this.operationProcess = operationProcess;
    }

    public String getPostOperation() {
        return postOperation;
    }

    public void setPostOperation(String postOperation) {
        this.postOperation = postOperation;
    }

    public String getPostOpConsideration() {
        return postOpConsideration;
    }

    public void setPostOpConsideration(String postOpConsideration) {
        this.postOpConsideration = postOpConsideration;
    }

    public Date getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(Date recordTime) {
        this.recordTime = recordTime;
    }

    public String getDoctorSign() {
        return doctorSign;
    }

    public void setDoctorSign(String doctorSign) {
        this.doctorSign = doctorSign;
    }

    public String getSourcePath() {
        return sourcePath;
    }

    public void setSourcePath(String sourcePath) {
        this.sourcePath = sourcePath;
    }

    public String getPkId() {
        return pkId;
    }

    public void setPkId(String pkId) {
        this.pkId = pkId;
    }

    public String getDataState() {
        return dataState;
    }

    public void setDataState(String dataState) {
        this.dataState = dataState;
    }

    public String getFullText() {
        return fullText;
    }

    public void setFullText(String fullText) {
        this.fullText = fullText;
    }

    public String getPatientSnOrg() {
        return patientSnOrg;
    }

    public void setPatientSnOrg(String patientSnOrg) {
        this.patientSnOrg = patientSnOrg;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", hospitalCode=").append(hospitalCode);
        sb.append(", patientSn=").append(patientSn);
        sb.append(", visitSn=").append(visitSn);
        sb.append(", tpatno=").append(tpatno);
        sb.append(", operationTime=").append(operationTime);
        sb.append(", operationDuration=").append(operationDuration);
        sb.append(", diagPerioperation=").append(diagPerioperation);
        sb.append(", anesthesiaMethod=").append(anesthesiaMethod);
        sb.append(", operationApproach=").append(operationApproach);
        sb.append(", operationProcess=").append(operationProcess);
        sb.append(", postOperation=").append(postOperation);
        sb.append(", postOpConsideration=").append(postOpConsideration);
        sb.append(", recordTime=").append(recordTime);
        sb.append(", doctorSign=").append(doctorSign);
        sb.append(", sourcePath=").append(sourcePath);
        sb.append(", pkId=").append(pkId);
        sb.append(", dataState=").append(dataState);
        sb.append(", fullText=").append(fullText);
        sb.append(", patientSnOrg=").append(patientSnOrg);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}