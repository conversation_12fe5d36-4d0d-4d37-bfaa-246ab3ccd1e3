package com.haoys.rdr.domain.wrapper;

import com.haoys.rdr.domain.vo.PatientDataViewVo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
public class PatientDataViewWrapper {

    private List<PatientDataViewWrapper.TableHeadVo> tableHeadVoList = new ArrayList<>();

    //private List<List<PatientDataViewVo>> dataList = new ArrayList<>();
    private List<Map<String,Object>> dataList = new ArrayList<>();

    @Data
    public static class TableHeadVo{
        private String labelName;
        private String labelCode;
    }

}
