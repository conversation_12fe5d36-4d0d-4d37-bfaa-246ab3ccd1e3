package com.haoys.rdr.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.rdr.domain.param.ExportPatientAnalysisDataParam;
import com.haoys.rdr.domain.param.RdrPatientAnalysisDatasetParam;
import com.haoys.rdr.domain.param.RdrPatientAnalysisRecordParam;
import com.haoys.rdr.domain.vo.RdrPatientAnalysisDatasetVo;

import java.util.List;

public interface RdrPatientAnalysisDataService {


    CustomResult savePatientAnalysisDataSet(RdrPatientAnalysisDatasetParam patientAnalysisDatasetParam);

    CustomResult savePatientAnalysisRecordJoinDataSet(RdrPatientAnalysisRecordParam patientAnalysisRecordParam);

    CustomResult modifyPatientAnalysisDataSet(String dataSetId, String enabled);

    CustomResult removePatientAnalysisDataSet(String dataSetId);

    CustomResult removePatientAnalysisRecord(String dataSetId, List<String> recordIds);

    CommonPage<RdrPatientAnalysisDatasetVo> getPatientAnalysisDataSetForPage(String dataBaseId, String code, String enabled, Integer pageNum, Integer pageSize);

    CommonResult<Object> exportPatientAnalysisData(ExportPatientAnalysisDataParam param);

}
