package com.haoys.rdr.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.*;
import co.elastic.clients.elasticsearch.core.CountRequest;
import co.elastic.clients.elasticsearch.core.CountResponse;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.HighlightField;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.json.JsonData;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.bussiness.RedisKeyContants;
import com.haoys.user.common.constants.CommomDataModelConstants;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.constants.DefineConstant;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.common.sql.HumpToLineUtil;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.config.RdrDataCenterConfig;
import com.haoys.user.elasticsearch.*;
import com.haoys.user.storge.cloud.OssStorageConfig;
import com.haoys.rdr.RdrConstants;
import com.haoys.rdr.domain.param.RdrPatientSearchParam;
import com.haoys.rdr.domain.vo.RdrPatientModelDefineVo;
import com.haoys.rdr.mapper.RdrPatientDataBaseRecordMapper;
import com.haoys.rdr.mapper.RdrPatientModelVariableMapper;
import com.haoys.rdr.mapper.RdrPatientNaPiSearchMapper;
import com.haoys.rdr.model.*;
import com.haoys.rdr.service.RdrModelDefineService;
import com.haoys.rdr.service.RdrPatientTreeSearchService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@RequiredArgsConstructor(onConstructor = @__(@Lazy))
@DS("disease-rdr")
@Service()
public class RdrPatientTreeSearchServiceImpl extends BaseService implements RdrPatientTreeSearchService {

    private final RdrDataCenterConfig rdrDataCenterConfig;
    private final RedisTemplateService redisTemplateService;
    private final OssStorageConfig storageConfig;
    private final RdrModelDefineService modelDefineService;
    private final RdrPatientModelVariableMapper variableMapper;
    private final RdrPatientNaPiSearchMapper patientNaPiSearchMapper;
    @Resource(name = "clientByPasswd")
    private ElasticsearchClient client;


    /**
     * 病历视图 排序字段
     */
    private static String VISIT_SORT_FIELD = "visitSn";

    /**
     * 患者视图 排序字段
     */
    private static String PATIENT_SORT_FIELD = "patientSn";

    /**
     * 列表查询要展示的信息的表名称，对个用逗号隔开
     */
    private static String PATIENT_SOURCE = "patients,visitInformation";

    @Autowired
    private RdrPatientDataBaseRecordMapper dataBaseRecordMapper;

    @Override
    public CommonResult<Map<String, Object>> list(TreeSearchParam param) throws IOException {
        Map<String, Object> map = new HashMap<>();
        CommonPage<Map<String, Object>> commonPage = new CommonPage<>();

        // 构建高亮显示的字段
        Map<String, Object> heightMap = ElasticTreeSearchUtil.buildHeightField(param.getInclude());

        // 构建es搜索条件
        SearchRequest request = SearchRequest.of(i -> {
            SearchRequest.Builder builder = i.index(rdrDataCenterConfig.getPatient_join_visit_index()).from((param.getPageNum() - 1) * param.getPageSize())
                    .size(param.getPageSize()).source(s -> s.filter(f -> f.includes(StrUtil.split(PATIENT_SOURCE, ","))))
                    .sort(s -> s.field(f -> f.field(PATIENT_SORT_FIELD).order(SortOrder.Asc)))
                    .highlight(h -> h.fields((Map<String, HighlightField>) heightMap.get("fieldMap")));

            if (CommomDataModelConstants.PATIENT_VIEW.equals(param.getSearchView())) {
                Query query = buildViewPatientQuery(param);
                builder.query(query);
            } else {
                Query query = buildViewVisitQuery(param);
                builder.query(query);
            }
            return builder;
        });


        CountRequest countRequest = CountRequest.of(i -> {
            CountRequest.Builder builder = i.index(rdrDataCenterConfig.getPatient_join_visit_index());
            if (CommomDataModelConstants.PATIENT_VIEW.equals(param.getSearchView())) {
                Query query = buildViewPatientQuery(param);
                builder.query(query);
            } else {
                Query query = buildViewVisitQuery(param);
                builder.query(query);
            }
            return builder;
        });

        CountResponse count = client.count(countRequest);
        // 从es中获取数据
        SearchResponse<JSONObject> response = client.search(request, JSONObject.class);
        List<Hit<JSONObject>> hits = response.hits().hits();

        if (CollectionUtil.isNotEmpty(hits)) {
            // 从es数据库中获取到数据后，处理成前端所需要的数据格式
            List<Map<String, Object>> list = new ArrayList<>();
            for (Hit<JSONObject> hit : hits) {
                Map<String, Object> dataMap = JSONObject.parseObject(hit.source().toJSONString(), new TypeReference<Map<String, Object>>() {
                });
                dataMap.put("height", hit.highlight());
                Object patientObject = dataMap.get("patients");
                if (patientObject instanceof JSONObject) {
                    JSONObject demographyInformationObject = (JSONObject) patientObject;
                    Patients demographyInformationData = JSON.parseObject(patientObject.toString(), Patients.class);
                    Set<Map.Entry<String, Object>> entrySet = demographyInformationObject.entrySet();
                    for (Map.Entry<String, Object> entry : entrySet) {
                        String entryKey = entry.getKey();
                        if (RdrConstants.PATIENT_NAME.equals(entryKey)) {
                            demographyInformationData.setName(DesensitizedUtil.chineseName(demographyInformationData.getName()));
                        }
                        if (RdrConstants.PATIENT_CONTACT_PHONE.equals(entryKey)) {
                            demographyInformationData.setMobilePhone(DesensitizedUtil.mobilePhone(demographyInformationData.getMobilePhone()));
                        }
                        if (RdrConstants.PATIENT_ID_NO.equals(entryKey)) {
                            demographyInformationData.setIdNo(DesensitizedUtil.idCardNum(demographyInformationData.getIdNo(), 1, 2));
                        }
                        if (RdrConstants.PATIENT_HOME_ADDRESS.equals(entryKey)) {
                            demographyInformationData.setHomeAdress(DesensitizedUtil.address(demographyInformationData.getHomeAdress(), 10));
                        }
                    }
                    dataMap.put("patients", demographyInformationData);
                }
                list.add(dataMap);
            }
            commonPage.setList(list);
            commonPage.setData(heightMap);
            commonPage.setTotal(count.count());
        } else {
            commonPage.setList(new ArrayList<>());
            commonPage.setTotal(0l);
            commonPage.setData(heightMap.get("fieldMap"));
        }

        commonPage.setPageNum(param.getPageNum());
        commonPage.setPageSize(param.getPageSize());
        map.put("data", commonPage);
        return CommonResult.success(map);
    }

    @Override
    public CommonResult<Map<String, Object>> export(TreeSearchParam param) throws IOException {
        // 保存参与者导出信息(生成中...)
        String fileName = "患者信息-" + new Date().getTime();
        Long uuid = saveDownsInfo(fileName);
        // 存放到redis中
        redisTemplateService.lPush(RedisKeyContants.USER_DOWN + SecurityUtils.getUserId(), uuid);
        ExecutorService executorService = Executors.newFixedThreadPool(1);
        executorService.execute(() -> {
            DynamicDataSourceContextHolder.push("disease-rdr");
//            exportMysql(param, fileName, uuid);
            exportEs(param, fileName, uuid);
        });
        executorService.shutdown();
        return CommonResult.success(null);
    }

    private void exportMysql(TreeSearchParam param, String fileName, Long uuid) {
        // 构建es搜索条件
        SearchRequest request = SearchRequest.of(i -> {
            SearchRequest.Builder builder = i.index(rdrDataCenterConfig.getPatient_join_visit_index()).size(10000)
                    .source(s -> s.filter(f -> f.includes("patientSn")));
            Query query = buildViewVisitQuery(param);
            builder.query(query);
            return builder;
        });
        // 从es中获取数据
        SearchResponse<JSONObject> response = null;
        try {
            response = client.search(request, JSONObject.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        List<Hit<JSONObject>> hits = response.hits().hits();
        List<String> visitIds = new ArrayList<>();
        Set<String> patientIdsSet = new TreeSet<>();
        if (CollectionUtil.isNotEmpty(hits)) {
            hits.forEach(hit -> {
                visitIds.add(hit.id());
                patientIdsSet.add(hit.source().get("patientSn").toString());
            });
        }
        List<String> patientIds = new ArrayList<>(patientIdsSet);

        String basePath = storageConfig.getUploadFolder().concat("rdr");
        String targetPath = basePath.concat("/").concat(fileName + ".xls");
        OutputStream out = null;
        try {
            if (!new File(basePath).exists()) {
                new File(basePath).mkdirs();
            }
            File file = new File(targetPath);
            out = new FileOutputStream(file);
            if (CollectionUtil.isNotEmpty(visitIds)) {

                List<RdrPatientModelDefineVo> models = new ArrayList<>();
                // 导出全部
                List<RdrPatientModelDefineVo> modelDefineVos = modelDefineService.getModelVariableTreeConfigList("patient");
                Map<String, List<String>> map = new HashMap<>();

                List<TreeExport> exportFields = param.getExportFields();
                if (CollectionUtil.isNotEmpty(exportFields)) {
                    for (TreeExport exportField : exportFields) {
                        map.put(exportField.getFormCode(), exportField.getVariables());
                    }
                    modelDefineVos.forEach(modelDefineVo -> {
                        if (map.get(modelDefineVo.getVariableCode()) != null) {
                            models.add(modelDefineVo);
                        }
                    });
                } else {
                    models.addAll(modelDefineVos);
                }
                // 获取就诊表定义信息
                BigExcelWriter writer = ExcelUtil.getBigWriter();
                // 设置第一个sheet为就诊表头信息
                writer.renameSheet(0, models.get(0).getVariableName());
                // 患者id拼接处id串，然后拼接sql用in查询查数据
                List<String> visitIdsStrs = new ArrayList<>();
                int batchSize = 1000;
                for (int i = 0; i < visitIds.size(); i += batchSize) {
                    int end = Math.min(i + batchSize, visitIds.size());
                    List<String> subList = visitIds.subList(i, end);
                    StringBuilder idsStr = new StringBuilder();
                    for (String s : subList) {
                        idsStr.append("'").append(s).append("',");
                    }
                    idsStr.deleteCharAt(idsStr.length() - 1);
                    visitIdsStrs.add(idsStr.toString());
                }

                List<String> patientIdsStrs = new ArrayList<>();
                for (int i = 0; i < patientIds.size(); i += batchSize) {
                    int end = Math.min(i + batchSize, patientIds.size());
                    List<String> subList = patientIds.subList(i, end);
                    StringBuilder idsStr = new StringBuilder();
                    for (String s : subList) {
                        idsStr.append("'").append(s).append("',");
                    }
                    idsStr.deleteCharAt(idsStr.length() - 1);
                    patientIdsStrs.add(idsStr.toString());
                }
                Map<String, List<RdrPatientModelVariable>> variableMap = new HashMap<>();

                for (RdrPatientModelDefineVo modelDefineVo : models) {

                    List<RdrPatientModelVariable> variables = variableMap.get(modelDefineVo.getVariableCode());
                    if (CollectionUtil.isEmpty(variables)) {
                        variables = new ArrayList<>();
                        List<RdrPatientModelVariable> patientModelVariables = getPatientModelVariables(modelDefineVo.getVariableCode(), true);
                        List<String> variables1 = map.get(modelDefineVo.getVariableCode());
                        if (variables1 != null) {
                            Map<String, String> map1 = new HashMap<>();
                            variables1.forEach(variableCode -> map1.put(variableCode, variableCode));
                            for (RdrPatientModelVariable patientModelVariable : patientModelVariables) {
                                if (map1.get(patientModelVariable.getVariableCode()) != null) {
                                    variables.add(patientModelVariable);
                                }
                            }
                        } else {
                            variables.addAll(patientModelVariables);
                        }
                        variableMap.put(modelDefineVo.getVariableCode(), variables);
                    }

                    writer.setSheet(modelDefineVo.getVariableName());
                    writer.disableDefaultStyle();
                    // 设置表头
                    for (RdrPatientModelVariable variable : variables) {
                        writer.addHeaderAlias(StrUtil.toCamelCase(variable.getVariableCode()), variable.getVariableName());
                    }
                    if (modelDefineVo.getVariableCode().equals("patients")) {
                        // 获取患者信息
                        // 就诊id获取数据
                        for (String idStr : patientIdsStrs) {
                            // 获取就诊数据
                            List<Map<String, Object>> visitData = getData(idStr, null, variables);
                            writer.write(visitData);
                            writer.autoSizeColumnAll();
                        }
                    } else {
                        // 就诊id获取数据
                        for (String idStr : visitIdsStrs) {
                            // 获取就诊数据
                            List<Map<String, Object>> visitData = getData(null, idStr, variables);
                            writer.write(visitData);
                            writer.autoSizeColumnAll();
                        }
                    }
                }
                writer.flush(out);
                writer.close();
                out.close();
                updateDownsInfo(uuid, fileName);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void exportEs(TreeSearchParam param, String fileName, Long uuid) {
        StringBuilder source = new StringBuilder("patients");
        List<TreeExport> exportFields = param.getExportFields();
        if (CollectionUtil.isNotEmpty(exportFields)) {
            for (TreeExport exportField : exportFields) {
                String formCode = exportField.getFormCode();
                List<String> variables = exportField.getVariables();
                if (CollectionUtil.isNotEmpty(variables)) {
                    for (String variable : variables) {
                        source.append(",").append(formCode).append(".").append(variable);
                    }
                }
            }

        }
        StringBuilder exclude = new StringBuilder("inp_order.data_state");
        List<RdrPatientModelVariable> variableList = getPatientModelVariables(null, false);
        if (CollectionUtil.isNotEmpty(variableList)) {
            for (RdrPatientModelVariable variable : variableList) {
                exclude.append(",").append(StrUtil.toCamelCase(variable.getModelSourceCode()) + "." + StrUtil.toCamelCase(variable.getVariableCode()));
            }
        }

        List<String> ids = new ArrayList<>();
        if (StringUtils.isNotEmpty(param.getDataBaseId())) {
            List<String> records = dataBaseRecordMapper.getPatientsByDatabaseId(param.getDataBaseId());
            if (CollectionUtil.isNotEmpty(records)) {
                ids.addAll(records);
            }
        }

        Map<String, List<Object>> dataMap = new HashMap<>();
        List<RdrPatientModelDefineVo> models = new ArrayList<>();
        Map<String, List<RdrPatientModelVariable>> variableMap = new HashMap<>();
        Map<String, String> modelMap = new HashMap<>();

        // 要导出的字段信息
        exportModel(models, variableMap, param, modelMap);

        String basePath = storageConfig.getUploadFolder().concat("rdr");
        String targetPath = basePath.concat("/").concat(fileName + ".xls");
        OutputStream out = null;

        try {
            if (!new File(basePath).exists()) {
                new File(basePath).mkdirs();
            }
            File file = new File(targetPath);
            out = Files.newOutputStream(file.toPath());

            for (int pageNo = 1; pageNo < 1000; pageNo++) {
                int size=0;
                if (CommomDataModelConstants.PATIENT_SEARCH_TYPE_1.equals(param.getSearchType())) {
                    size = getExportDatafullSearch(param, exportFields, source, exclude, modelMap, dataMap, pageNo,ids);
                }else if (CommomDataModelConstants.PATIENT_SEARCH_TYPE_3.equals(param.getSearchType())) {
                    size = getExportDataHeightSearch(param, exportFields, source, exclude, modelMap, dataMap, pageNo,ids);
                }else {
                    size = getExportData(param, exportFields, source, exclude, modelMap, dataMap, pageNo,ids);
                }
                if(size<10){
                    break;
                }
            }
            // 获取数据

            BigExcelWriter writer = ExcelUtil.getBigWriter();
            writer.renameSheet(0, models.get(0).getVariableName());
            for (RdrPatientModelDefineVo model : models) {

                // 设置sheet
                writer.setSheet(model.getVariableName());
                // 获取表头
                List<RdrPatientModelVariable> variables = variableMap.get(StrUtil.toCamelCase(model.getVariableCode()));
                for (RdrPatientModelVariable variable : variables) {
                    writer.addHeaderAlias(StrUtil.toCamelCase(variable.getVariableCode()), variable.getVariableName());
                }
                // 获取数据
                List<Object> objects = dataMap.get(StrUtil.toCamelCase(model.getVariableCode()));
                if (CollectionUtil.isEmpty(objects)){
                    objects = new ArrayList<>();
                }
                writer.disableDefaultStyle();
                if(StrUtil.toCamelCase(model.getVariableCode()).equals("patients")){
                    Set<Object> set = new HashSet<>(objects);
                    writer.write(set);
                }else {
                    writer.write(objects);
                }
                writer.autoSizeColumnAll();
            }

            writer.flush(out);
            out.close();
            writer.close();
            updateDownsInfo(uuid, fileName);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                assert out != null;
                out.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
    private int getExportDatafullSearch(TreeSearchParam patientMedicalRecordSearchParam, List<TreeExport> exportFields, StringBuilder source, StringBuilder exclude, Map<String, String> modelMap, Map<String, List<Object>> dataMap, int pageNo, List<String> ids) {
        if (StringUtils.isBlank(patientMedicalRecordSearchParam.getSortField())) {
            patientMedicalRecordSearchParam.setSortField(RdrConstants.COMMON_SORT_FIELD);
        }
        if (StringUtils.isBlank(patientMedicalRecordSearchParam.getSortType())) {
            patientMedicalRecordSearchParam.setSortType(RdrConstants.COMMON_SORT_TYPE_ASC);
        }
        String startDate = patientMedicalRecordSearchParam.getStartDate();
        String endDate = patientMedicalRecordSearchParam.getEndDate();
        if (CommomDataModelConstants.PATIENT_QUERY_SEGMENT_0.equals(patientMedicalRecordSearchParam.getQuerySegment())) {
            startDate = "now-10y";
            endDate = "now";
        }
        if (RdrConstants.PATIENT_QUERY_SEGMENT_1.equals(patientMedicalRecordSearchParam.getQuerySegment())) {
            startDate = "now-1y";
            endDate = "now";
        }
        if (RdrConstants.PATIENT_QUERY_SEGMENT_3.equals(patientMedicalRecordSearchParam.getQuerySegment())) {
            startDate = "now-3y";
            endDate = "now";
        }
        if (RdrConstants.PATIENT_QUERY_SEGMENT_5.equals(patientMedicalRecordSearchParam.getQuerySegment())) {
            startDate = "now-5y";
            endDate = "now";
        }
        String searchWord = "\"" + patientMedicalRecordSearchParam.getSearchWord() + "\"";
        String finalStartDate = startDate;
        String finalEndDate = endDate;
        String searchVisitTime="visitInformation.visitOrAdmissionDatetime";
        SearchRequest request;
        Integer pageNum = (patientMedicalRecordSearchParam.getPageNum() - 1) * patientMedicalRecordSearchParam.getPageSize();
        Integer pageSize = patientMedicalRecordSearchParam.getPageSize();
        String searchView = patientMedicalRecordSearchParam.getSearchView();
        if (StringUtils.isEmpty(patientMedicalRecordSearchParam.getModelSourceCode())) {
            String searchIndexName = getSearchIndexName(patientMedicalRecordSearchParam);
            if(StringUtils.isEmpty(patientMedicalRecordSearchParam.getSearchWord())){
                request = SearchRequest.of(i -> {
                    SearchRequest.Builder builder = i.index(searchIndexName).from(pageNum).size(pageSize);
                    builder.sort(s -> s.field(f -> f.field(patientMedicalRecordSearchParam.getSortField()).order(SortOrder.Asc)));
                    if (CollectionUtil.isNotEmpty(ids)){
                        BoolQuery boolQuery = builderQuery(ids, patientMedicalRecordSearchParam.getSearchView());
                        if (boolQuery!=null){
                            BoolQuery.Builder boolBuilder = new BoolQuery.Builder();
                            boolBuilder.must(b -> b.bool(boolQuery));
                            boolBuilder.must(m -> m.matchAll(matchAll -> matchAll.boost(1.0f)))
                                    .must(m -> m.range(qs -> qs.field(searchVisitTime).gte(JsonData.of(finalStartDate)).lte(JsonData.of(finalEndDate))));
                            builder.query(q -> q.bool(boolBuilder.build()));
                        }
                    }else {
                        builder.query(q -> q.bool(b -> b.must(m -> m.matchAll(matchAll -> matchAll.boost(1.0f)))
                                .must(m -> m.range(qs -> qs.field(searchVisitTime).gte(JsonData.of(finalStartDate)).lte(JsonData.of(finalEndDate))))));
                    }
                    if (CollectionUtil.isNotEmpty(exportFields)) {
                        builder.source(s -> s.filter(f -> f.includes(StrUtil.split(source, ",")).excludes(StrUtil.split(exclude, ","))));
                    } else {
                        builder.source(s -> s.filter(f -> f.excludes(StrUtil.split(exclude, ","))));
                    }
                    return builder;
                });
            }else{
                request = SearchRequest.of(i -> {
                    SearchRequest.Builder builder = i.index(searchIndexName).from(pageNum).size(pageSize);
                    builder.sort(s -> s.field(f -> f.field(patientMedicalRecordSearchParam.getSortField()).order(SortOrder.Asc)));
                    if (CollectionUtil.isNotEmpty(ids)){

                        BoolQuery boolQuery = builderQuery(ids, patientMedicalRecordSearchParam.getSearchView());
                        if (boolQuery!=null){
                            BoolQuery.Builder boolBuilder = new BoolQuery.Builder();
                            boolBuilder.must(b -> b.bool(boolQuery));
                            boolBuilder.must(m -> m.queryString(qs -> qs.query(searchWord)))
                                    .must(m -> m.range(qs -> qs.field(searchVisitTime).gte(JsonData.of(finalStartDate)).lte(JsonData.of(finalEndDate))));
                            builder.query(q -> q.bool(boolBuilder.build()));
                        }
                    }else {
                        builder.query(q -> q.bool(b -> b.must(m -> m.queryString(qs -> qs.query(searchWord)))
                                .must(m -> m.range(qs ->qs.field(searchVisitTime).gte(JsonData.of(finalStartDate)).lte(JsonData.of(finalEndDate))))));
                    }

                    if (CollectionUtil.isNotEmpty(exportFields)) {
                        builder.source(s -> s.filter(f -> f.includes(StrUtil.split(source, ",")).excludes(StrUtil.split(exclude, ","))));
                    } else {
                        builder.source(s -> s.filter(f -> f.excludes(StrUtil.split(exclude, ","))));
                    }
                    return builder;
                });
            }
        } else {
            request = SearchRequest.of(i -> {
                SearchRequest.Builder builder = i.index(rdrDataCenterConfig.getPatient_join_visit_index()).from(pageNum).size(pageSize)
                     .sort(s -> s.field(f -> f.field(patientMedicalRecordSearchParam.getSortField()).order(SortOrder.Asc)));
                if (CollectionUtil.isNotEmpty(exportFields)) {
                    builder.source(s -> s.filter(f -> f.includes(StrUtil.split(source, ",")).excludes(StrUtil.split(exclude, ","))));
                } else {
                    builder.source(s -> s.filter(f -> f.excludes(StrUtil.split(exclude, ","))));
                }
                if (CollectionUtil.isNotEmpty(ids)){
                    BoolQuery boolQuery = builderQuery(ids, patientMedicalRecordSearchParam.getSearchView());
                    BoolQuery.Builder boolBuilder = new BoolQuery.Builder();
                    boolBuilder.must(b -> b.bool(boolQuery));
                    boolBuilder.must(m -> m.queryString(qs -> qs.query(searchWord)))
                            .must(m -> m.range(qs -> qs.field(searchVisitTime).gte(JsonData.of(finalStartDate)).lte(JsonData.of(finalEndDate))));
                    builder.query(q -> q.bool(boolBuilder.build()));
                }else {
                    builder.query(q -> q.bool(b -> b
                            .must(m -> m.queryString(qs -> qs.query(searchWord)))
                            .must(m -> m.range(qs -> qs.field(searchVisitTime)
                                    .gte(JsonData.of(finalStartDate))
                                    .lte(JsonData.of(finalEndDate))))));
                }
                String modelSourceCode = HumpToLineUtil.lineToHump(patientMedicalRecordSearchParam.getModelSourceCode());
                builderQuery(builder, searchView, modelSourceCode, searchWord, finalStartDate, finalEndDate);
                if (CollectionUtil.isNotEmpty(exportFields)) {
                    builder.source(s -> s.filter(f -> f.includes(StrUtil.split(source, ",")).excludes(StrUtil.split(exclude, ","))));
                } else {
                    builder.source(s -> s.filter(f -> f.excludes(StrUtil.split(exclude, ","))));
                }
                return builder;
            });
        }
        SearchResponse<JSONObject> response;
        try {
            response = client.search(request, JSONObject.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        List<Hit<JSONObject>> hits = response.hits().hits();

        // 设置数据
        for (Hit<JSONObject> hit : hits) {
            JSONObject object = hit.source();
            Set<String> keySet = object.keySet();
            for (String key : keySet) {
                String sheetName = modelMap.get(key);
                if (StringUtils.isBlank(sheetName)) {
                    continue;
                }
                Object o = object.get(key);
                List<Object> list = new ArrayList();
                if (o instanceof List) {
                    list.addAll((List) o);
                } else if (o instanceof Map) {
                    list.add(o);
                }
                List<Object> objects = dataMap.get(key);
                if (objects != null) {
                    objects.addAll(list);
                } else {
                    dataMap.put(key, list);
                }
            }
        }
        return hits.size();
    }

    private SearchRequest.Builder builderQuery(SearchRequest.Builder searchBuilder, String searchView, String modelSourceCodeValue, String searchWord, String finalStartDate, String finalEndDate) {
        BoolQuery.Builder boolBuilder = new BoolQuery.Builder();
        String[] modelSourceCodeArray = modelSourceCodeValue.split(",");
        BoolQuery.Builder boolMultiNestedBuilder = new BoolQuery.Builder();
        for (int i = 0; i < modelSourceCodeArray.length; i++) {
            String modelSourceCode = modelSourceCodeArray[i];
            NestedQuery.Builder nestedQuery = new NestedQuery.Builder();
            nestedQuery.path(modelSourceCode);
            nestedQuery.query(q -> q.bool(b -> b.must(m -> m.queryString(qs -> qs.query(searchWord)))));
            boolMultiNestedBuilder.should(q -> q.nested(nested -> nestedQuery));
        }
        boolBuilder.must(q -> q.bool(b -> b.must(m -> m.bool(boolMultiNestedBuilder.build()))));
        boolBuilder.must(q -> q.term(t -> t.field("docType").value("visit")));
        NestedQuery.Builder nestedQuery = new NestedQuery.Builder();
        nestedQuery.path("visitInformation");
        nestedQuery.query(q -> q.bool(b -> b.must(m -> m.range(qs -> qs.field("visitInformation.visitOrAdmissionDatetime").gte(JsonData.of(finalStartDate)).lte(JsonData.of(finalEndDate))))));
        boolBuilder.must(q -> q.nested(nested -> nestedQuery));
        searchBuilder.query(boolBuilder.build()._toQuery());
        return searchBuilder;
    }

    private BoolQuery builderQuery(List<String> ids,String searchView) {
        if(CollectionUtil.isNotEmpty(ids)){
            BoolQuery.Builder boolBuilder = new BoolQuery.Builder();
            if(CommomDataModelConstants.PATIENT_VIEW.equals(searchView)){
                boolBuilder.must(m->m.ids(s->s.values(ids)));
                return boolBuilder.build();
            }
            if(CommomDataModelConstants.VISIT_VIEW.equals(searchView)){
                // 设置查询类型为Terms查询
                List<FieldValue> vs = new ArrayList<>();
                ids.forEach(id -> vs.add(FieldValue.of(id)));
                boolBuilder.must(m->m.terms(t->t.field("patientSn").terms(ts->ts.value(vs))));
                return boolBuilder.build();
            }
        }
        return null;
    }
    private String getSearchIndexName(TreeSearchParam patientMedicalRecordSearchParam) {
        String searchIndexName = rdrDataCenterConfig.getPatient_full_text_index();
        String searchView = patientMedicalRecordSearchParam.getSearchView();
        if(CommomDataModelConstants.PATIENT_VIEW.equals(searchView)){
            searchIndexName = rdrDataCenterConfig.getPatient_full_text_index();
        }
        if(CommomDataModelConstants.VISIT_VIEW.equals(searchView)){
            searchIndexName = rdrDataCenterConfig.getPatient_visit_full_text_index();
        }
        return searchIndexName;
    }
    private int getExportDataHeightSearch(TreeSearchParam param, List<TreeExport> exportFields, StringBuilder source, StringBuilder exclude, Map<String, String> modelMap, Map<String, List<Object>> dataMap, int pageNo, List<String> ids) {
        SearchRequest request = SearchRequest.of(i -> {
            SearchRequest.Builder builder = i.index(rdrDataCenterConfig.getPatient_join_visit_index())  .from((pageNo - 1) *10)
                    .size(10);
            if (CollectionUtil.isNotEmpty(exportFields)) {
                builder.source(s -> s.filter(f -> f.includes(StrUtil.split(source, ",")).excludes(StrUtil.split(exclude, ","))));
            } else {
                builder.source(s -> s.filter(f -> f.excludes(StrUtil.split(exclude, ","))));
            }
            Query query = buildQuery(param, ids);
            builder.query(query);
            return builder;
        });



        SearchResponse<JSONObject> response;
        try {
            response = client.search(request, JSONObject.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        List<Hit<JSONObject>> hits = response.hits().hits();

        // 设置数据
        for (Hit<JSONObject> hit : hits) {
            JSONObject object = hit.source();
            Set<String> keySet = object.keySet();
            for (String key : keySet) {
                String sheetName = modelMap.get(key);
                if (StringUtils.isBlank(sheetName)) {
                    continue;
                }
                Object o = object.get(key);
                List<Object> list = new ArrayList();
                if (o instanceof List) {
                    list.addAll((List) o);
                } else if (o instanceof Map) {
                    list.add(o);
                }
                List<Object> objects = dataMap.get(key);
                if (objects != null) {
                    objects.addAll(list);
                } else {
                    dataMap.put(key, list);
                }
            }
        }
        return hits.size();
    }

    private Query buildQuery( TreeSearchParam patientRecordSearchParam, List<String> ids) {
        List<PatientRecordSearchParam.SearcherModeRule> searcherModeRuleList = patientRecordSearchParam.getDataList();
        BoolQuery.Builder boolBuilder = new BoolQuery.Builder();
        // 设置查询类型为Terms查询
        boolBuilder.must(q -> q.term(t -> t.field("docType").value("visit")));
        if (StringUtils.isNotEmpty(patientRecordSearchParam.getDataBaseId())){
            List<FieldValue> vs = new ArrayList<>();
            ids.forEach(id -> vs.add(FieldValue.of(id)));
            boolBuilder.must(m->m.terms(t->t.field("patientSn").terms(ts->ts.value(vs))));
        }
        for (PatientRecordSearchParam.SearcherModeRule searcherModeRule : searcherModeRuleList) {
            String searchWord = searcherModeRule.getSearchWord();
            RdrPatientSearchParam rdrPatientSearchParam = new RdrPatientSearchParam();
            BeanUtils.copyProperties(searcherModeRule, rdrPatientSearchParam);
            rdrPatientSearchParam.setSearchValue(searcherModeRule.getSearchWord());
            rdrPatientSearchParam.setSearchType(searcherModeRule.getOperatorValue());
            rdrPatientSearchParam.setOperatorType(searcherModeRule.getQueryConfig());
            NestedQuery.Builder nestedQueryBuilder = new NestedQuery.Builder();
            nestedQueryBuilderWrapper(nestedQueryBuilder, rdrPatientSearchParam, searchWord);
            boolBuilder.must(q -> q.nested(nested -> nestedQueryBuilder));
        }
        return boolBuilder.build()._toQuery();
    }
    private void nestedQueryBuilderWrapper(NestedQuery.Builder builder, RdrPatientSearchParam rdrPatientSearchParam, String searchValue) {
        String modelSourceCode = rdrPatientSearchParam.getModelSourceCode();
        String variableCode = rdrPatientSearchParam.getVariableCode();
        String variableType = rdrPatientSearchParam.getVariableType();
        modelSourceCode = StrUtil.toCamelCase(modelSourceCode);
        builder.path(modelSourceCode);
        String filedName = modelSourceCode + "." + StrUtil.toCamelCase(variableCode);
        // 构建搜索条件的字段
        if ("varchar".equals(variableType)||"text".equals(variableType)){
            // 字符串
            buildString(rdrPatientSearchParam, builder, filedName, searchValue);
        }else  if (variableType.contains("int") || variableType.contains("float")){
            // 数字类型
            buildNumber(rdrPatientSearchParam, builder, filedName, searchValue);
        }else  if (variableType.contains("timestamp") || variableType.contains("date")|| variableType.contains("datetime")){
            // 日期类型
            String searchStartDate = rdrPatientSearchParam.getSearchStartDate();
            String searchEndDate = rdrPatientSearchParam.getSearchEndDate();
            if(rdrPatientSearchParam.getSearchType().equals(DefineConstant.BETWEEN)){
                List<String> dateList = JSON.parseArray(searchValue, String.class);
                if (CollectionUtil.isNotEmpty(dateList)) {
                    searchStartDate = dateList.get(0);
                    searchEndDate = dateList.get(1);
                }
            }
            buildDate(rdrPatientSearchParam, builder, filedName, searchValue, searchStartDate, searchEndDate);
        }else  if (variableType.contains("bool")){
            // bool类型
            buildBool(rdrPatientSearchParam, builder, filedName, searchValue);
        }
    }

    private void buildBool(RdrPatientSearchParam diseaseSearchParam, NestedQuery.Builder searchBuilder, String filedName, String searchValue) {
        boolean isBool;
        if ("是".equals(searchValue)||"true".equals(searchValue)){
            isBool = true;
        } else {
            isBool = false;
        }
        if (DefineConstant.EMPTY.equals(diseaseSearchParam.getOperatorType())){
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.exists(exists -> exists.field(filedName)))
                    )
            );
        }else if (DefineConstant.NOT_EMPTY.equals(diseaseSearchParam.getOperatorType())){
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.mustNot(mustNot -> mustNot.exists(exists -> exists.field(filedName)))
                    )
            );
        }else if (DefineConstant.EQUAL_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 等于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.term(t -> t.field(filedName).value(isBool)))
                    )
            );
        } else if (DefineConstant.NOT_EQUAL_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 不等于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.mustNot(mustNot -> mustNot.term(t -> t.field(filedName).value(isBool)))
                    )
            );
        }
    }

    private void buildDate(RdrPatientSearchParam diseaseSearchParam, NestedQuery.Builder searchBuilder, String filedName, String searchValue, String startDate, String endDate) {
        if (DefineConstant.GREATER_THAN_CODE.equals(diseaseSearchParam.getOperatorType())) {
            //  大于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(term -> term.field(filedName).gt(JsonData.of(searchValue))))
                    )
            );
        } else if (DefineConstant.LESS_THAN_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 小于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(term -> term.field(filedName).lt(JsonData.of(searchValue))))
                    )
            );
        }else if (DefineConstant.EQUAL_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 等于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(term -> term.field(filedName).gte(JsonData.of(searchValue)).lte(JsonData.of(searchValue)).format("yyyy-MM-dd")))
                    )
            );
        } else if (DefineConstant.GREATER_THAN_EQUAL_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 大于等于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(term -> term.field(filedName).gte(JsonData.of(searchValue))))
                    )
            );
        } else if (DefineConstant.LESS_THAN_EQUAL_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 小于等于条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(term -> term.field(filedName).lte(JsonData.of(searchValue))))
                    )
            );
        }else if (DefineConstant.BETWEEN.equals(diseaseSearchParam.getOperatorType())) {
            // 范围区间搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(term -> term.field(filedName)
                                    .gte(JsonData.of(startDate))
                                    .lte(JsonData.of(endDate))))
                    )
            );
        } else if (DefineConstant.EMPTY.equals(diseaseSearchParam.getOperatorType())) {
            // 空值搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.mustNot(mustNot -> mustNot.wildcard(w -> w.field(filedName).wildcard(searchValue)))
                    )
            );
        }else if (DefineConstant.NOT_EMPTY.equals(diseaseSearchParam.getOperatorType())) {
            // 非空搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.wildcard(w -> w.field(filedName).wildcard(searchValue)))
                    )
            );
        }
    }

    private void buildNumber(RdrPatientSearchParam diseaseSearchParam, NestedQuery.Builder searchBuilder, String filedName, String searchValue) {
        if (DefineConstant.GREATER_THAN_CODE.equals(diseaseSearchParam.getOperatorType())) {
            //  大于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(term -> term.field(filedName).gt(JsonData.of(searchValue))))
                    )
            );
        } else if (DefineConstant.LESS_THAN_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 小于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(term -> term.field(filedName).lt(JsonData.of(searchValue))))
                    )
            );
        }else if (DefineConstant.EQUAL_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 等于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.term(term -> term.field(filedName).value(searchValue)))
                    )
            );
        } else if (DefineConstant.NOT_CONTAINS_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 大于等于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(term -> term.field(filedName).gte(JsonData.of(searchValue))))
                    )
            );
        } else if (DefineConstant.GREATER_THAN_EQUAL_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 小于等于条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(term -> term.field(filedName).lte(JsonData.of(searchValue))))
                    )
            );
        }else if (DefineConstant.BETWEEN.equals(diseaseSearchParam.getOperatorType())) {
            // 范围区间搜索条件构建
            String[] searchValueArray = searchValue.split(",");
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.range(term -> term.field(filedName)
                                    .gte(JsonData.of(searchValueArray[0]))
                                    .lte(JsonData.of(searchValueArray[1]))))
                    )
            );
        } else if (DefineConstant.EMPTY.equals(diseaseSearchParam.getOperatorType())) {
            // 空值搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.mustNot(mustNot -> mustNot.wildcard(w -> w.field(filedName).wildcard(searchValue)))
                    )
            );
        }else if (DefineConstant.NOT_EMPTY.equals(diseaseSearchParam.getOperatorType())) {
            // 非空搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.wildcard(w -> w.field(filedName).wildcard(searchValue)))
                    )
            );
        }
    }

    private void buildString(RdrPatientSearchParam diseaseSearchParam, NestedQuery.Builder searchBuilder, String filedName, String searchValue) {
        if (DefineConstant.EQUAL_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 等于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.term(term -> term.field(filedName).value(searchValue)))
                    )
            );
        } else if (DefineConstant.NOT_EQUAL_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 不等于搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.mustNot(mustNot -> mustNot.term(term -> term.field(filedName).value(searchValue)))
                    )
            );
        }else if (DefineConstant.CONTAINS_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 包含搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.should(should -> should.match(match -> match.field(filedName).query(searchValue)))
                    )
            );
        } else if (DefineConstant.NOT_CONTAINS_CODE.equals(diseaseSearchParam.getOperatorType())) {
            // 不包含搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.mustNot(mustNot -> mustNot.match(match -> match.field(filedName).query(searchValue)))
                    )
            );
        } else if (DefineConstant.EMPTY.equals(diseaseSearchParam.getOperatorType())) {
            // 空值搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.mustNot(mustNot -> mustNot.wildcard(w -> w.field(filedName).wildcard(searchValue)))
                    )
            );
        }else if (DefineConstant.NOT_EMPTY.equals(diseaseSearchParam.getOperatorType())) {
            // 非空搜索条件构建
            searchBuilder.query(nestedQuery -> nestedQuery.bool(
                            bool -> bool.must(must -> must.wildcard(w -> w.field(filedName).wildcard(searchValue)))
                    )
            );
        }
    }

    private int getExportData(TreeSearchParam param, List<TreeExport> exportFields, StringBuilder source, StringBuilder exclude, Map<String, String> modelMap, Map<String, List<Object>> dataMap, int pageNo, List<String> ids) {
        // 构建es搜索条件
        SearchRequest request = SearchRequest.of(i -> {
            SearchRequest.Builder builder = i.index(rdrDataCenterConfig.getPatient_join_visit_index())
                    .from((pageNo - 1) *10)
                    .size(10);

            if (CollectionUtil.isNotEmpty(exportFields)) {
                builder.source(s -> s.filter(f -> f.includes(StrUtil.split(source, ",")).excludes(StrUtil.split(exclude, ","))));
            } else {
                builder.source(s -> s.filter(f -> f.excludes(StrUtil.split(exclude, ","))));
            }
            Query query = buildViewVisitQuery(param);
            builder.query(query);
            return builder;
        });
//         从es中获取数据
        SearchResponse<JSONObject> response = null;
        try {
            response = client.search(request, JSONObject.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        List<Hit<JSONObject>> hits = response.hits().hits();

        // 设置数据
        for (Hit<JSONObject> hit : hits) {
            JSONObject object = hit.source();
            Set<String> keySet = object.keySet();
            for (String key : keySet) {
                String sheetName = modelMap.get(key);
                if (StringUtils.isBlank(sheetName)) {
                    continue;
                }
                Object o = object.get(key);
                List<Object> list = new ArrayList();
                if (o instanceof List) {
                    list.addAll((List) o);
                } else if (o instanceof Map) {
                    list.add(o);
                }
                List<Object> objects = dataMap.get(key);
                if (objects != null) {
                    objects.addAll(list);
                } else {
                    dataMap.put(key, list);
                }
            }
        }
        return hits.size();
    }

    private void exportModel(List<RdrPatientModelDefineVo> models, Map<String, List<RdrPatientModelVariable>> variableMap,TreeSearchParam param, Map<String, String> modelMap) {
        // 获取就诊表定义信息

        List<RdrPatientModelDefineVo> modelDefineVos = modelDefineService.getModelVariableTreeConfigList("patient");
        Map<String, List<String>> map = new HashMap<>();

        if (CollectionUtil.isNotEmpty(param.getExportFields())) {
            for (TreeExport exportField : param.getExportFields()) {
                map.put(exportField.getFormCode(), exportField.getVariables());
            }
            modelDefineVos.forEach(modelDefineVo -> {
                if (map.get(modelDefineVo.getVariableCode()) != null) {
                    models.add(modelDefineVo);
                }
            });
        } else {
            models.addAll(modelDefineVos);
        }
        // 设置第一个sheet为就诊表头信息
        for (RdrPatientModelDefineVo modelDefineVo : models) {
            List<RdrPatientModelVariable> variables = variableMap.get(modelDefineVo.getVariableCode());
            if (CollectionUtil.isEmpty(variables)) {
                variables = new ArrayList<>();
                List<RdrPatientModelVariable> patientModelVariables = getPatientModelVariables(modelDefineVo.getVariableCode(), true);
                List<String> variables1 = map.get(modelDefineVo.getVariableCode());
                if (variables1 != null) {
                    Map<String, String> map1 = new HashMap<>();
                    variables1.forEach(variableCode -> map1.put(variableCode, variableCode));
                    for (RdrPatientModelVariable patientModelVariable : patientModelVariables) {
                        if (map1.get(patientModelVariable.getVariableCode()) != null) {
                            variables.add(patientModelVariable);
                        }
                    }
                } else {
                    variables.addAll(patientModelVariables);
                }
                variableMap.put(StrUtil.toCamelCase(modelDefineVo.getVariableCode()), variables);
            }
            modelMap.put(StrUtil.toCamelCase(modelDefineVo.getVariableCode()), modelDefineVo.getVariableName());
        }
    }


    private Long saveDownsInfo(String fileName) {
        DynamicDataSourceContextHolder.push("master");
        Long uuid = SnowflakeIdWorker.getUuid();
        String sql = new StringBuilder()
                .append("insert into project_testee_export (id,project_id,task_name,create_time,description,export_file_type,operator,status,download_url,tenant_id,platform_id,export_status)")
                .append(" values(")
                .append("'").append(uuid).append("',")
                .append("'").append("1").append("',")
                .append("'").append(fileName).append("',")
                .append("").append(" now() ").append(",")
                .append("'").append("患者信息").append("',")
                .append("'").append("1").append("',")
                .append("'").append(SecurityUtils.getUserId()).append("',")
                .append("'").append(BusinessConfig.VALID_STATUS).append("',")
                .append("'").append("").append("',")
                .append("'").append(SecurityUtils.getSystemTenantId()).append("',")
                .append("'").append(SecurityUtils.getSystemPlatformId()).append("',")
                .append("'").append(BusinessConfig.TESTEE_EXPORT_STATUS_1).append("'")
                .append(" )")
                .toString();
        patientNaPiSearchMapper.selectList(sql.toString());
        DynamicDataSourceContextHolder.clear();
        return uuid;
    }

    private void updateDownsInfo(Long uuid, String fileName) {
        String fileUrl = storageConfig.getViewUrl() + Constants.SYSTEM_VIEW_PATH + "rdr/" + fileName + ".xls";
        DynamicDataSourceContextHolder.push("master");
        String sql = new StringBuilder()
                .append("update project_testee_export set ")
                .append(" download_url ='").append(fileUrl).append("',")
                .append(" export_status='").append(BusinessConfig.TESTEE_EXPORT_STATUS_0).append("' ")
                .append(" where id='").append(uuid).append("'")
                .toString();
        patientNaPiSearchMapper.selectList(sql.toString());
        DynamicDataSourceContextHolder.clear();
    }

    private List<RdrPatientModelVariable> getPatientModelVariables(String modelSourceCode, boolean flg) {
        RdrPatientModelVariableExample example = new RdrPatientModelVariableExample();
        RdrPatientModelVariableExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(modelSourceCode)) {
            criteria.andModelSourceCodeEqualTo(modelSourceCode);
        }
        criteria.andCustomVariableEqualTo(false);
        criteria.andDefaultQueryEqualTo(flg);
        example.setOrderByClause("sort asc");
        List<RdrPatientModelVariable> variables = variableMapper.selectByExample(example);
        return variables;
    }

    public List<Map<String, Object>> getData(String ids, String visitSn, List<RdrPatientModelVariable> variables) {
        StringBuilder sql = new StringBuilder("select");
        for (RdrPatientModelVariable v : variables) {
            if (!v.getCustomVariable()) {
                if (v.getVariableType().contains("timestamp") || v.getVariableType().contains("date")) {
                    sql.append(" to_char(" + v.getVariableCode() + ", 'yyyy-MM-dd HH:mm:ss')").append(" as \"").append(StrUtil.toCamelCase(v.getVariableCode())).append("\" ,");
                } else {
                    sql.append(" ").append(v.getVariableCode()).append(" as \"").append(StrUtil.toCamelCase(v.getVariableCode())).append("\" ,");
                }
            }
        }
        sql = new StringBuilder(sql.substring(0, sql.length() - 1));
        sql.append(" from ").append(variables.get(0).getModelSourceCode()).append(" where 1=1 ");
        if (StringUtils.isNotEmpty(ids)) {
            sql.append(" and patient_sn in (").append(ids).append(") ");
            sql.append(" order by patient_sn");
        }
        if (StringUtils.isNotEmpty(visitSn)) {
            sql.append(" and visit_sn in (").append(visitSn).append(") ");
        }
        // 根据sql 查询数据返回一个map
        return patientNaPiSearchMapper.selectList(sql.toString());
    }

    private Query buildViewPatientQuery(TreeSearchParam param) {
        List<String> ids = new ArrayList<>();
        if (StringUtils.isNotEmpty(param.getDataBaseId())) {
            RdrPatientDataBaseRecordExample example = new RdrPatientDataBaseRecordExample();
            RdrPatientDataBaseRecordExample.Criteria criteria = example.createCriteria();
            criteria.andDataBaseIdEqualTo(param.getDataBaseId());
            List<RdrPatientDataBaseRecord> records = dataBaseRecordMapper.selectByExample(example);
            if (CollectionUtil.isNotEmpty(records)) {
                records.forEach(f -> ids.add(f.getPatientSn()));
            }
        }

        BoolQuery.Builder bd = new BoolQuery.Builder();
        if (CollectionUtil.isNotEmpty(ids)) {
            bd.must(qy -> qy.ids(id -> id.values(ids)));
        }

        bd.must(qy -> qy.term(t -> t.field("docType").value("patient")));
        // 纳入条件
        SearchDto include = param.getInclude();

        if (include != null) {
            if (StringUtils.isNotEmpty(include.getFieldCode())) {
                // 说明树形搜索条件只有一个分支
                // 构建嵌套条件
                List<String> searchValues = include.getSearchValues();
                if (CollectionUtil.isNotEmpty(searchValues)) {
                    // 说明是同患者
                    if (include.getIndexType().equals("patient")) {
                        NestedQuery.Builder query = patientNestedQuery(include, searchValues);
                        bd.must(b -> b.nested(query.build()));
                    } else if (include.getIndexType().equals("visit")) {
                        // 说明是同病历
                        HasChildQuery.Builder query = visitChildQuery(include, searchValues);
                        bd.must(b -> b.hasChild(query.build()));
                    }
                }
            } else {

                // 说明树形搜索条件有多个分支
                buildPatientQuery(include.getChildren().get(0), bd, null);
            }
        }
        return bd.build()._toQuery();
    }


    private Query buildViewVisitQuery(TreeSearchParam param) {
        List<FieldValue> ids = new ArrayList<>();
        if (StringUtils.isNotEmpty(param.getDataBaseId())) {
            RdrPatientDataBaseRecordExample example = new RdrPatientDataBaseRecordExample();
            RdrPatientDataBaseRecordExample.Criteria criteria = example.createCriteria();
            criteria.andDataBaseIdEqualTo(param.getDataBaseId());
            List<RdrPatientDataBaseRecord> records = dataBaseRecordMapper.selectByExample(example);
            if (CollectionUtil.isNotEmpty(records)) {
                records.forEach(f -> ids.add(FieldValue.of(f.getPatientSn())));

            }
        }
        BoolQuery.Builder bd = new BoolQuery.Builder();
        if (CollectionUtil.isNotEmpty(ids)) {
            bd.must(m -> m.terms(t -> t.field("patientSn").terms(s -> s.value(ids))));
        }
        bd.must(qy -> qy.term(t -> t.field("docType").value("visit")));
        // 纳入条件
        SearchDto include = param.getInclude();

        if (include != null) {
            if (StringUtils.isNotEmpty(include.getFieldCode())) {
                // 说明树形搜索条件只有一个分支
                // 构建嵌套条件
                NestedQuery.Builder nestedQuery = extractedVisit(include, include);
                if (nestedQuery != null) {
                    bd.must(b -> b.nested(nestedQuery.build()));
                }
            } else {
                // 说明树形搜索条件有多个分支
                buildVisitQuery(include, bd);
            }
        }
        return bd.build()._toQuery();
    }


    private static void buildPatientQuery(SearchDto include, BoolQuery.Builder bd, HasChildQuery.Builder hcq) {
        List<SearchDto> childList = include.getChildren();
        if (CollectionUtil.isEmpty(childList) && StringUtils.isNotEmpty(include.getFieldCode())) {
            // 说明只有一层
            NestedQuery.Builder query = patientNestedQuery(include, include.getSearchValues());
            bd.must(b -> b.nested(query.build()));
        } else {
            // 如果分支之间是并且的关系，那么构建条件是must
            if (include.getSearchType().equals(SearchTypeEnum.AND.getCode())) {
                // 循环条件

                if (include.getIndexType().equals("patient")) {
                    BoolQuery.Builder bq = new BoolQuery.Builder();
                    for (SearchDto child : childList) {
                        // 创建一个子的bool条件,每个子条件都需要加入到总的bool条件中
                        if (StringUtils.isNotEmpty(child.getFieldCode())) {
                            List<String> searchValues = child.getSearchValues();
                            if (CollectionUtil.isNotEmpty(searchValues)) {
                                // 说明是同患者
                                NestedQuery.Builder builder = patientNestedQuery(child, searchValues);
                                bq.must(b -> b.nested(builder.build()));
                            }
                        } else {
                            // 说明是有下级分支，需要进行循环
                            buildPatientQuery(child, bq, null);
                        }
                    }
                    bd.must(b -> b.bool(bq.build()));
                    // 说明是同患者
                } else if (include.getIndexType().equals("visit")) {
                    // 说明是同病历
                    boolean flg = false;
                    if (hcq == null) {
                        hcq = new HasChildQuery.Builder();
                        flg = true;
                    }
                    hcq.type("visit");
                    BoolQuery.Builder bq = new BoolQuery.Builder();

                    for (SearchDto child : childList) {
                        // 创建一个子的bool条件,每个子条件都需要加入到总的bool条件中
                        if (StringUtils.isNotEmpty(child.getFieldCode())) {
                            List<String> searchValues = child.getSearchValues();
                            if (CollectionUtil.isNotEmpty(searchValues)) {
                                // 说明是同病历
                                NestedQuery.Builder builder = patientNestedQuery(child, searchValues);
                                bq.must(b -> b.nested(builder.build()));
                            }
                        } else {
                            // 说明是有下级分支，需要进行循环
                            buildPatientQuery(child, bq, hcq);
                        }
                    }
                    if (flg) {
                        hcq.query(c -> c.bool(bq.build()));
                        HasChildQuery.Builder finalHcq = hcq;
                        bd.must(b -> b.hasChild(finalHcq.build()));
                    } else {
                        bd.must(b -> b.bool(bq.build()));
                    }
                }
            } else if (include.getSearchType().equals(SearchTypeEnum.OR.getCode())) {
                if (include.getIndexType().equals("patient")) {
                    BoolQuery.Builder bq = new BoolQuery.Builder();
                    for (SearchDto child : childList) {
                        // 创建一个子的bool条件,每个子条件都需要加入到总的bool条件中
                        if (StringUtils.isNotEmpty(child.getFieldCode())) {
                            List<String> searchValues = child.getSearchValues();
                            if (CollectionUtil.isNotEmpty(searchValues)) {
                                // 说明是同患者
                                NestedQuery.Builder builder = patientNestedQuery(child, searchValues);
                                bq.should(b -> b.nested(builder.build()));
                            }
                        } else {
                            // 说明是有下级分支，需要进行循环
                            buildPatientQuery(child, bq, null);
                        }
                    }
                    bd.should(b -> b.bool(bq.build()));
                    // 说明是同患者
                } else if (include.getIndexType().equals("visit")) {
                    // 说明是同病历
                    boolean flg = false;
                    if (hcq == null) {
                        hcq = new HasChildQuery.Builder();
                        flg = true;
                    }
                    hcq.type("visit");
                    BoolQuery.Builder bq = new BoolQuery.Builder();

                    for (SearchDto child : childList) {
                        // 创建一个子的bool条件,每个子条件都需要加入到总的bool条件中
                        if (StringUtils.isNotEmpty(child.getFieldCode())) {
                            List<String> searchValues = child.getSearchValues();
                            if (CollectionUtil.isNotEmpty(searchValues)) {
                                // 说明是同病历
                                NestedQuery.Builder builder = patientNestedQuery(child, searchValues);
                                bq.should(b -> b.nested(builder.build()));
                            }
                        } else {
                            // 说明是有下级分支，需要进行循环
                            buildPatientQuery(child, bq, hcq);
                        }
                    }
                    if (flg) {
                        hcq.query(c -> c.bool(bq.build()));
                        HasChildQuery.Builder finalHcq = hcq;
                        bd.should(b -> b.hasChild(finalHcq.build()));
                    } else {
                        bd.should(b -> b.bool(bq.build()));
                    }
                }
            } else if (include.getSearchType().equals(SearchTypeEnum.NOT.getCode())) {


                if (include.getIndexType().equals("patient")) {
                    BoolQuery.Builder bq = new BoolQuery.Builder();
                    for (SearchDto child : childList) {
                        // 创建一个子的bool条件,每个子条件都需要加入到总的bool条件中
                        if (StringUtils.isNotEmpty(child.getFieldCode())) {
                            List<String> searchValues = child.getSearchValues();
                            if (CollectionUtil.isNotEmpty(searchValues)) {
                                // 说明是同患者
                                NestedQuery.Builder builder = patientNestedQuery(child, searchValues);
                                bq.mustNot(b -> b.nested(builder.build()));
                            }
                        } else {
                            // 说明是有下级分支，需要进行循环
                            buildPatientQuery(child, bq, null);
                        }
                    }
                    bd.mustNot(b -> b.bool(bq.build()));
                    // 说明是同患者
                } else if (include.getIndexType().equals("visit")) {
                    // 说明是同病历
                    boolean flg = false;
                    if (hcq == null) {
                        hcq = new HasChildQuery.Builder();
                        flg = true;
                    }
                    hcq.type("visit");
                    BoolQuery.Builder bq = new BoolQuery.Builder();

                    for (SearchDto child : childList) {
                        // 创建一个子的bool条件,每个子条件都需要加入到总的bool条件中
                        if (StringUtils.isNotEmpty(child.getFieldCode())) {
                            List<String> searchValues = child.getSearchValues();
                            if (CollectionUtil.isNotEmpty(searchValues)) {
                                // 说明是同病历
                                NestedQuery.Builder builder = patientNestedQuery(child, searchValues);
                                bq.mustNot(b -> b.nested(builder.build()));
                            }
                        } else {
                            // 说明是有下级分支，需要进行循环
                            buildPatientQuery(child, bq, hcq);
                        }
                    }
                    if (flg) {
                        hcq.query(c -> c.bool(bq.build()));
                        HasChildQuery.Builder finalHcq = hcq;
                        bd.mustNot(b -> b.hasChild(finalHcq.build()));
                    } else {
                        bd.mustNot(b -> b.bool(bq.build()));
                    }
                }
            }
        }
    }

    private static void buildVisitQuery(SearchDto include, BoolQuery.Builder bd) {
        // 获取下层级分支
        List<SearchDto> childList = include.getChildren();

        if (CollectionUtil.isEmpty(childList) && StringUtils.isNotEmpty(include.getFieldCode())) {
            // 说明只有一层
            NestedQuery.Builder query = patientNestedQuery(include, include.getSearchValues());
            bd.must(b -> b.nested(query.build()));
        } else {
            // 如果分支之间是并且的关系，那么构建条件是must
            if (include.getSearchType().equals(SearchTypeEnum.AND.getCode())) {
                // 循环条件
                for (SearchDto child : childList) {
                    // 创建一个子的bool条件,每个子条件都需要加入到总的bool条件中
                    if (StringUtils.isNotEmpty(child.getFieldCode())) {
                        NestedQuery.Builder nestedQuery = extractedVisit(include, child);
                        if (nestedQuery != null) {
                            bd.must(b -> b.nested(nestedQuery.build()));
                        }
                    } else {
                        BoolQuery.Builder childBoolQuery = new BoolQuery.Builder();
                        // 说明是有下级分支，需要进行循环
                        buildVisitQuery(child, childBoolQuery);
                        bd.must(b -> b.bool(childBoolQuery.build()));
                    }
                }
            } else if (include.getSearchType().equals(SearchTypeEnum.OR.getCode())) {
                // 循环条件
                for (SearchDto child : childList) {
                    // 创建一个子的bool条件,每个子条件都需要加入到总的bool条件中
                    if (StringUtils.isNotEmpty(child.getFieldCode())) {
                        // 说明是同患者
                        NestedQuery.Builder nestedQuery = extractedVisit(include, child);
                        if (nestedQuery != null) {
                            bd.should(b -> b.nested(nestedQuery.build()));
                        }
                    } else {
                        BoolQuery.Builder childBoolQuery = new BoolQuery.Builder();
                        // 说明是有下级分支，需要进行循环
                        buildVisitQuery(child, bd);
                        bd.should(b -> b.bool(childBoolQuery.build()));
                    }
                }
            } else if (include.getSearchType().equals(SearchTypeEnum.NOT.getCode())) {
                // 循环条件
                for (SearchDto child : childList) {
                    // 创建一个子的bool条件,每个子条件都需要加入到总的bool条件中
                    if (StringUtils.isNotEmpty(child.getFieldCode())) {
                        // 说明是同患者
                        NestedQuery.Builder nestedQuery = extractedVisit(include, child);
                        if (nestedQuery != null) {
                            bd.mustNot(b -> b.nested(nestedQuery.build()));
                        }
                    } else {
                        BoolQuery.Builder childBoolQuery = new BoolQuery.Builder();
                        // 说明是有下级分支，需要进行循环
                        buildVisitQuery(child, bd);
                        bd.mustNot(b -> b.bool(childBoolQuery.build()));
                    }
                }
            }
        }
    }


    private static HasChildQuery.Builder visitChildQuery(SearchDto child, List<String> searchValues) {
        // 说明是同病历
        HasChildQuery.Builder hasChildQuery = new HasChildQuery.Builder();
        hasChildQuery.type("visit");
        // 如果搜索条件不为空，添加path
        NestedQuery.Builder nestedQuery = new NestedQuery.Builder();
        String formCode = StrUtil.toCamelCase(child.getFormCode());
        nestedQuery.path(formCode);
        // 循环值
        BoolQuery.Builder searchQuery = new BoolQuery.Builder();
        // 构建搜索条件的字段
        final String filed = formCode + "." + StrUtil.toCamelCase(child.getFieldCode());

        ElasticTreeSearchUtil.buildQuery(child, searchValues, searchQuery, filed);

        nestedQuery.query(q -> q.bool(searchQuery.build()));
        hasChildQuery.query(q -> q.nested(nestedQuery.build()));
        return hasChildQuery;
    }

    private static NestedQuery.Builder patientNestedQuery(SearchDto child, List<String> searchValues) {

        NestedQuery.Builder nestedQuery = new NestedQuery.Builder();

        String formCode = StrUtil.toCamelCase(child.getFormCode());
        nestedQuery.path(formCode);
        // 循环值
        BoolQuery.Builder searchQuery = new BoolQuery.Builder();
        // 构建搜索条件的字段
        final String filed = formCode + "." + StrUtil.toCamelCase(child.getFieldCode());

        ElasticTreeSearchUtil.buildQuery(child, searchValues, searchQuery, filed);

        nestedQuery.query(q -> q.bool(searchQuery.build()));

        return nestedQuery;
    }


    private static NestedQuery.Builder extractedVisit(SearchDto include, SearchDto child) {


        List<String> searchValues = child.getSearchValues();

        if (CollectionUtil.isNotEmpty(searchValues)) {
            NestedQuery.Builder nestedQuery = new NestedQuery.Builder();
            // 说明是同病历
            if (include.getIndexType().equals("visit")) {
                // 如果搜索条件不为空，添加path
                String formCode = StrUtil.toCamelCase(child.getFormCode());
                nestedQuery.path(formCode);
                // 循环值
                BoolQuery.Builder searchQuery = new BoolQuery.Builder();
                // 构建搜索条件的字段
                final String filed = formCode + "." + StrUtil.toCamelCase(child.getFieldCode());
                ElasticTreeSearchUtil.buildQuery(child, searchValues, searchQuery, filed);
                nestedQuery.query(q -> q.bool(searchQuery.build()));
            } else if (include.getIndexType().equals("patient")) {
                // 说明是同患者
                HasParentQuery.Builder hasParentQuery = new HasParentQuery.Builder();
                hasParentQuery.parentType("patient");
                // 如果搜索条件不为空，添加path
                String formCode = StrUtil.toCamelCase(child.getFormCode());
                nestedQuery.path(formCode);
                // 循环值
                BoolQuery.Builder searchQuery = new BoolQuery.Builder();
                // 构建搜索条件的字段
                final String filed = formCode + "." + StrUtil.toCamelCase(child.getFieldCode());
                ElasticTreeSearchUtil.buildQuery(child, searchValues, searchQuery, filed);
                nestedQuery.query(q -> q.bool(searchQuery.build()));
            }
            return nestedQuery;
        }
        return null;
    }
}
