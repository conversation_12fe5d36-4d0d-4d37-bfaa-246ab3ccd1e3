package com.haoys.rdr.domain.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class RdrPatientAnalysisRecordParam {

    @ApiModelProperty(value = "主键id")
    private String dataSetId;

    @NotEmpty(message = "数据库id不能为空")
    @ApiModelProperty(value = "数据库id")
    private String dataBaseId;

    @ApiModelProperty(value = "数据集code")
    private String batchCode;

    @NotEmpty(message = "患者id不能为空")
    @ApiModelProperty(value = "患者id")
    private List<String> patientIds = new ArrayList<>();

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
