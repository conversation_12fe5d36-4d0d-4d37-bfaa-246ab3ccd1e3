package com.haoys.rdr.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.sql.HumpToLineUtil;
import com.haoys.user.common.util.DateUtil;
import com.haoys.user.common.util.StringUtils;
import com.haoys.rdr.domain.vo.PatientDataVo;
import com.haoys.rdr.domain.vo.RdrPatientVariableRecordVo;
import com.haoys.rdr.domain.wrapper.PatientDataViewWrapper;
import com.haoys.rdr.mapper.RdrPatientModelDefineMapper;
import com.haoys.rdr.mapper.RdrPatientModelRecordMapper;
import com.haoys.rdr.model.RdrPatientModelRecord;
import com.haoys.rdr.model.RdrPatientModelRecordExample;
import com.haoys.rdr.model.RdrPatientModelVariable;
import com.haoys.rdr.service.RdrPatientModelRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor(onConstructor = @__(@Lazy))
@DS("disease-rdr")
@Service
public class RdrPatientModelRecordServiceImpl implements RdrPatientModelRecordService {

    private final RdrPatientModelRecordMapper rdrPatientModelRecordMapper;
    private final RdrPatientModelDefineMapper rdrPatientModelDefineMapper;

    @Override
    public  List<PatientDataVo> getPatientRecord(String patientId, String modelSourceCode, String groupBy) {

        // 获取变量信息
        List<RdrPatientModelVariable> variables = rdrPatientModelDefineMapper.getPatientModelConfigByModelCode(modelSourceCode);

        // 查询数据
        RdrPatientModelRecordExample example = new RdrPatientModelRecordExample();
        RdrPatientModelRecordExample.Criteria criteria = example.createCriteria();
        criteria.andPatientIdEqualTo(patientId);
        criteria.andModelSourceCodeEqualTo(modelSourceCode);
        List<RdrPatientModelRecord> list = rdrPatientModelRecordMapper.selectByExample(example);

        List<PatientDataVo> patientDataList = new ArrayList<>();

        List<JSONObject> data = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)){
            // 获取json 数据存放到list 中类型是JSONObject
            list.forEach(record->data.add(JSON.parseObject(record.getContent().toString())));
            // 根据groupBy 字段进行分组
            if (StringUtils.isNotEmpty(groupBy)){
                LinkedHashMap<Date, List<JSONObject>> dataMap =
                        data.stream().filter(obj-> Objects.nonNull(obj.get(groupBy))).collect(Collectors.groupingBy(obj->(DateUtil.getAutoParseDate(DateUtil.formatDate((Long)obj.get(groupBy)), DateUtil.DEFAULTFORMAT)), LinkedHashMap::new, Collectors.toList()));
                dataMap.forEach((k,v)-> {
                    // 转成要返回的数据类型
                    PatientDataVo vo = new PatientDataVo<>();
                    vo.setDate(k);
                    List<List<PatientDataVo.PatientData>> listObj = new ArrayList<>();

                    for (JSONObject jsonObject : v) {
                        List<PatientDataVo.PatientData> listMap = new ArrayList<>();
                        for (RdrPatientModelVariable variable : variables) {
                            String variableCode = HumpToLineUtil.lineToHump(variable.getVariableCode());
                            Object o = jsonObject.get(variableCode);
                            if (variable.getVariableType().equals("timestamp")){
                                if (o!=null){
                                    Date date = DateUtil.getAutoParseDate(DateUtil.formatDate((Long) o));
                                    o=DateUtil.formatDate2String(date, DateUtil.DEFAULTFORMAT);
                                }
                            }
                            PatientDataVo.PatientData patientData = new PatientDataVo.PatientData();
                            patientData.setLabel(variable.getVariableName());
                            patientData.setValue(o);
                            listMap.add(patientData);
                        }
                        listObj.add(listMap);
                    }
                    vo.setList(listObj);
                    patientDataList.add(vo);
                });
            }
        }
        return patientDataList;
    }

    @Override
    public PatientDataViewWrapper getPatientRecordViewDetail(String patientId, String modelSourceCode) {
        PatientDataViewWrapper patientDataViewWrapper = new PatientDataViewWrapper();
        List<Map<String,Object>> dataList = new ArrayList<>();
        List<PatientDataViewWrapper.TableHeadVo> tableHeadVoList = new ArrayList<>();
        List<RdrPatientModelVariable> rdrPatientModelVariableList = rdrPatientModelDefineMapper.getPatientModelConfigByModelCode(modelSourceCode);
        rdrPatientModelVariableList.forEach(variable -> {
            PatientDataViewWrapper.TableHeadVo tableHeadVo = new PatientDataViewWrapper.TableHeadVo();
            tableHeadVo.setLabelName(variable.getVariableName());
            String variableCode = HumpToLineUtil.lineToHump(variable.getVariableCode());
            tableHeadVo.setLabelCode(variableCode);
            tableHeadVoList.add(tableHeadVo);
        });
        patientDataViewWrapper.setTableHeadVoList(tableHeadVoList);
        RdrPatientModelRecordExample rdrPatientModelRecordExample = new RdrPatientModelRecordExample();
        RdrPatientModelRecordExample.Criteria criteria = rdrPatientModelRecordExample.createCriteria();
        criteria.andPatientIdEqualTo(patientId);
        criteria.andModelSourceCodeEqualTo(modelSourceCode);
        List<RdrPatientModelRecord> rdrPatientModelRecords = rdrPatientModelRecordMapper.selectByExample(rdrPatientModelRecordExample);
        for (RdrPatientModelRecord rdrPatientModelRecord : rdrPatientModelRecords) {
            Map<String,Object> dataMap = new HashMap<>();
            List<RdrPatientVariableRecordVo> patientModelVariableRecord = rdrPatientModelRecordMapper.getPatientModelVariableRecordViewDetail(patientId, modelSourceCode, rdrPatientModelRecord.getId());
            for (RdrPatientVariableRecordVo rdrPatientVariableRecordVo : patientModelVariableRecord) {
                rdrPatientModelVariableList.forEach(data->{
                    String variableCode = HumpToLineUtil.lineToHump(data.getVariableCode());
                    if(variableCode.equals(rdrPatientVariableRecordVo.getKey())){
                        dataMap.put(rdrPatientVariableRecordVo.getKey(),rdrPatientVariableRecordVo.getValue());
                    }
                });
            }
            dataList.add(dataMap);
        }
        patientDataViewWrapper.setDataList(dataList);
        return patientDataViewWrapper;
    }

    @Override
    public CommonResult<List<Map<String, Object>>> getPatientVisitListForRdr(String patientSn) {
        List<Map<String, Object>> list = rdrPatientModelRecordMapper.getPatientVisitListForRdr(patientSn);
        return CommonResult.success(list);
    }
    
}
