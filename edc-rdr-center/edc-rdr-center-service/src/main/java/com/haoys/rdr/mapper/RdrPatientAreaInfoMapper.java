package com.haoys.rdr.mapper;

import com.haoys.rdr.model.RdrPatientAreaInfo;
import com.haoys.rdr.model.RdrPatientAreaInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RdrPatientAreaInfoMapper {
    long countByExample(RdrPatientAreaInfoExample example);

    int deleteByExample(RdrPatientAreaInfoExample example);

    int deleteByPrimaryKey(String adcode);

    int insert(RdrPatientAreaInfo record);

    int insertSelective(RdrPatientAreaInfo record);

    List<RdrPatientAreaInfo> selectByExample(RdrPatientAreaInfoExample example);

    RdrPatientAreaInfo selectByPrimaryKey(String adcode);

    int updateByExampleSelective(@Param("record") RdrPatientAreaInfo record, @Param("example") RdrPatientAreaInfoExample example);

    int updateByExample(@Param("record") RdrPatientAreaInfo record, @Param("example") RdrPatientAreaInfoExample example);

    int updateByPrimaryKeySelective(RdrPatientAreaInfo record);

    int updateByPrimaryKey(RdrPatientAreaInfo record);
}