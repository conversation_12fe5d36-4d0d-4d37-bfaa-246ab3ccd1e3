# EDC医学研究数据管理系统

[![Java](https://img.shields.io/badge/Java-8-orange.svg)](https://www.oracle.com/java/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.7.18-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![Maven](https://img.shields.io/badge/Maven-3.6+-blue.svg)](https://maven.apache.org/)
[![MySQL](https://img.shields.io/badge/MySQL-8.0+-blue.svg)](https://www.mysql.com/)
[![License](https://img.shields.io/badge/License-Proprietary-red.svg)]()

## 📋 项目简介

EDC (Electronic Data Capture) 医学研究数据管理系统是一个基于Spring Boot的企业级医学研究数据采集和管理平台。系统提供完整的研究数据生命周期管理，包括数据采集、存储、分析、导出和安全管理等功能。

### 🎯 核心功能

- **🏥 研究中心管理** - 多中心研究项目管理和协调
- **👥 用户权限管理** - 基于角色的细粒度权限控制
- **📊 数据采集** - 标准化的医学数据采集表单
- **🗄️ 疾病数据库** - 专业的医学疾病信息管理
- **📈 RDR中心** - 研究数据仓库和分析中心
- **📄 PDF导出** - 高质量的研究报告生成
- **🔒 安全审计** - 完整的操作日志和安全监控
- **🌐 实时通信** - WebSocket实时数据推送

## 🏗️ 系统架构

### 模块结构

```
edc-research-master/
├── edc-research-center/           # 研究中心核心模块
│   ├── edc-research-api/          # REST API服务
│   ├── edc-research-common/       # 公共组件
│   └── edc-research-quartz/       # 定时任务调度
├── edc-user-center/               # 用户管理中心
│   ├── edc-user-api/              # 用户API服务
│   └── edc-user-common/           # 用户公共组件
├── edc-disease-xinjiang/          # 新疆疾病数据库
├── edc-rdr-center/                # 研究数据仓库
├── docs/                          # 项目文档
├── scripts/                       # 运维脚本
└── pom.xml                        # Maven父项目配置
```

### 技术栈

| 技术 | 版本 | 用途 |
|------|------|------|
| **Java** | 8 | 核心开发语言 |
| **Spring Boot** | 2.7.18 | 应用框架 |
| **Spring Security** | 5.7.14 | 安全框架 |
| **Spring WebSocket** | 2.7.18 | 实时通信 |
| **MyBatis Plus** | 3.5.3 | ORM框架 |
| **MySQL** | 8.0+ | 主数据库 |
| **Redis** | 6.0+ | 缓存和会话存储 |
| **Quartz** | 2.3.2 | 任务调度 |
| **Swagger** | 3.0.0 | API文档 |
| **Maven** | 3.6+ | 项目构建 |

## 🚀 快速开始

### 环境要求

- **Java**: JDK 8 或更高版本
- **Maven**: 3.6.0 或更高版本
- **MySQL**: 8.0 或更高版本
- **Redis**: 6.0 或更高版本 (可选)
- **Node.js**: 14+ (用于前端测试工具)

### 安装步骤

#### 1. 克隆项目
```bash
git clone <repository-url>
cd edc-research-master
```

#### 2. 数据库配置
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE edc_research CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入初始数据 (如果有SQL文件)
mysql -u root -p edc_research < docs/sql/init.sql
```

#### 3. 配置文件设置
```bash
# 复制配置模板
cp edc-research-center/edc-research-api/src/main/resources/application-dev.yml.template \
   edc-research-center/edc-research-api/src/main/resources/application-dev.yml

# 编辑数据库连接信息
vim edc-research-center/edc-research-api/src/main/resources/application-dev.yml
```

#### 4. 编译项目
```bash
# 全项目编译
mvn clean compile

# 运行测试 (可选)
mvn test
```

#### 5. 启动应用
```bash
# 启动主应用
cd edc-research-center/edc-research-api
mvn spring-boot:run -Dspring.profiles.active=dev

# 或者使用JAR包方式
mvn clean package -DskipTests
java -jar target/edc-research-api-1.0.0.jar --spring.profiles.active=dev
```

### 验证安装

访问以下URL验证系统是否正常运行：

- **应用主页**: http://localhost:8095
- **API文档**: http://localhost:8095/swagger-ui/index.html
- **健康检查**: http://localhost:8095/actuator/health
- **日志管理**: http://localhost:8095/api/api/log-management/viewer/page?accessToken=edc-log-viewer-2025-dev

## 📖 使用指南

### 配置文件说明

系统支持多环境配置：

- `application-dev.yml` - 开发环境配置
- `application-feature.yml` - 功能测试环境配置
- `application-test.yml` - 测试环境配置
- `application-prod.yml` - 生产环境配置

### 核心功能使用

#### 1. 用户管理
```bash
# 创建用户
POST /api/users
Content-Type: application/json

{
  "username": "researcher01",
  "password": "password123",
  "email": "<EMAIL>",
  "role": "RESEARCHER"
}
```

#### 2. 数据采集
```bash
# 提交研究数据
POST /api/research/data
Content-Type: application/json

{
  "patientId": "P001",
  "studyId": "STUDY001",
  "data": {
    "age": 45,
    "gender": "M",
    "diagnosis": "Hypertension"
  }
}
```

#### 3. 文件上传
```bash
# 分块文件上传
POST /api/files/upload/chunk
Content-Type: multipart/form-data

# 查询文件MD5
GET /api/files/md5/{fileId}
```

#### 4. 实时日志查看
```javascript
// WebSocket连接
const ws = new WebSocket('ws://localhost:8095/api/websocket/logs/client-001');
ws.onmessage = function(event) {
    const logData = JSON.parse(event.data);
    console.log(logData);
};
```

## 🧪 测试

### 测试结构

```
scripts/testing/
├── api-tests/              # API端点测试
├── websocket-tests/        # WebSocket功能测试
├── database-tests/         # 数据库连接测试
├── security-tests/         # 安全功能测试
├── integration-tests/      # 集成测试
├── verification-scripts/   # 功能验证脚本
└── utilities/             # 测试工具
```

### 运行测试

```bash
# 运行全部测试
./scripts/testing/integration-tests/run-tests.sh

# 运行特定类型测试
./scripts/testing/api-tests/test-endpoints.sh
./scripts/testing/websocket-tests/websocket-test.html
./scripts/testing/security-tests/test-csrf-fix.sh

# 数据库连接测试
cd scripts/testing/database-tests
javac TestMySQLConnection.java
java TestMySQLConnection
```

### 性能测试

```bash
# 全局编译和测试
./scripts/testing/integration-tests/global-compile-and-test.sh

# 验证在线访问
./scripts/testing/verification-scripts/verify-online-access.sh
```

## 📚 API文档

### Swagger UI
访问 http://localhost:8095/swagger-ui/index.html 查看完整的API文档。

### 主要API端点

| 模块 | 端点 | 描述 |
|------|------|------|
| **用户管理** | `/api/users/*` | 用户CRUD操作 |
| **研究数据** | `/api/research/*` | 研究数据管理 |
| **文件管理** | `/api/files/*` | 文件上传下载 |
| **日志管理** | `/api/log-management/*` | 系统日志查看 |
| **系统监控** | `/actuator/*` | 系统健康监控 |

### 认证方式

系统支持多种认证方式：
- **JWT Token**: 用于API访问
- **Session**: 用于Web界面
- **AccessToken**: 用于特殊功能访问

## 🔧 开发指南

### 代码规范

- **Java版本**: 严格使用Java 8语法
- **编码风格**: 遵循阿里巴巴Java开发手册
- **注释要求**: 所有公共方法必须有JavaDoc
- **日志规范**: 使用Lombok @Slf4j注解

### 开发环境设置

```bash
# 设置Java版本
export JAVA_HOME=/path/to/jdk8
export PATH=$JAVA_HOME/bin:$PATH

# Maven配置
export MAVEN_OPTS="-Xmx2048m -XX:MaxPermSize=512m"

# 开发模式启动
mvn spring-boot:run -Dspring.profiles.active=dev -Dmaven.test.skip=true
```

### 新功能开发流程

1. **创建功能分支**: `git checkout -b feature/new-feature`
2. **编写代码**: 遵循项目代码规范
3. **编写测试**: 确保测试覆盖率
4. **文档更新**: 更新相关文档
5. **提交代码**: 提交前运行全项目编译测试

### 常用开发脚本

```bash
# 快速编译
./scripts/maintenance/quick-compile.sh

# 代码格式化
./scripts/maintenance/format-code.sh

# 依赖检查
./scripts/maintenance/check-dependencies.sh
```

## 🚀 部署指南

### 生产环境部署

#### 1. 环境准备
```bash
# 服务器环境
- CentOS 7+ / Ubuntu 18.04+
- Java 8
- MySQL 8.0+
- Redis 6.0+ (可选)
- Nginx (反向代理)
```

#### 2. 应用配置
```yaml
# application-prod.yml
server:
  port: 8095
  servlet:
    context-path: /

spring:
  datasource:
    url: **********************************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}

  redis:
    host: ***************
    port: 6379
    password: ${REDIS_PASSWORD}

logging:
  file:
    name: /webserver/lib/logs/edc-research.log
  level:
    com.haoys: INFO
    root: WARN
```

#### 3. 部署脚本
```bash
# 构建生产包
mvn clean package -Pprod -DskipTests

# 部署到服务器
scp target/edc-research-api-1.0.0.jar user@***************:/webserver/lib/

# 启动服务
ssh user@***************
cd /webserver/lib
nohup java -jar edc-research-api-1.0.0.jar --spring.profiles.active=prod > app.log 2>&1 &
```

#### 4. Nginx配置
```nginx
server {
    listen 80;
    server_name edc.example.com;

    location / {
        proxy_pass http://localhost:8095;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    location /api/websocket/ {
        proxy_pass http://localhost:8095;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

### Docker部署

```dockerfile
# Dockerfile
FROM openjdk:8-jre-alpine

WORKDIR /app
COPY target/edc-research-api-1.0.0.jar app.jar

EXPOSE 8095

ENTRYPOINT ["java", "-jar", "app.jar", "--spring.profiles.active=prod"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  edc-app:
    build: .
    ports:
      - "8095:8095"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_USERNAME=edc_user
      - DB_PASSWORD=password
    depends_on:
      - mysql
      - redis

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: edc_research
      MYSQL_USER: edc_user
      MYSQL_PASSWORD: password
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:6.2-alpine
    command: redis-server --requirepass password

volumes:
  mysql_data:
```

## 🔍 监控和维护

### 系统监控

#### 1. 应用监控
- **健康检查**: `/actuator/health`
- **指标监控**: `/actuator/metrics`
- **日志查看**: `/api/log-management/viewer/page`

#### 2. 数据库监控
```sql
-- 连接数监控
SHOW STATUS LIKE 'Threads_connected';

-- 慢查询监控
SHOW STATUS LIKE 'Slow_queries';

-- 表空间使用
SELECT table_schema,
       ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'DB Size in MB'
FROM information_schema.tables
GROUP BY table_schema;
```

#### 3. 日志监控
```bash
# 实时错误日志
tail -f /webserver/lib/logs/edc-research.log | grep ERROR

# 日志统计
grep -c "ERROR" /webserver/lib/logs/edc-research.log

# 性能日志分析
awk '/SLOW QUERY/ {print $0}' /webserver/lib/logs/edc-research.log
```

### 维护脚本

```bash
# 日志清理
./scripts/maintenance/cleanup-logs.sh

# 数据库备份
./scripts/maintenance/backup-database.sh

# 系统健康检查
./scripts/maintenance/health-check.sh

# 性能优化
./scripts/maintenance/performance-tuning.sh
```

## 🐛 故障排除

### 常见问题

#### 1. 应用启动失败
```bash
# 检查端口占用
lsof -i :8095

# 检查Java版本
java -version

# 检查配置文件
cat edc-research-center/edc-research-api/src/main/resources/application-dev.yml
```

#### 2. 数据库连接问题
```bash
# 测试数据库连接
mysql -h localhost -u root -p -e "SELECT 1"

# 检查MySQL配置
cat /etc/mysql/mysql.conf.d/mysqld.cnf | grep sql_mode

# 修复sql_mode问题
./scripts/maintenance/mysql-sql-mode-fix.sh
```

#### 3. WebSocket连接失败
```bash
# 检查WebSocket端点
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" \
     http://localhost:8095/api/websocket/logs/test

# 测试WebSocket功能
open scripts/testing/websocket-tests/websocket-test.html
```

#### 4. 文件上传问题
```bash
# 检查文件权限
ls -la /tmp/upload/

# 测试文件上传API
./scripts/testing/api-tests/test-chunked-upload-md5-api.sh
```

### 性能优化

#### 1. JVM调优
```bash
# 开发环境JVM参数（支持大文件上传）
java -Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 \
     -XX:+PrintGCDetails -XX:+PrintGCTimeStamps \
     -jar edc-research-api-1.0.0.jar

# 生产环境JVM参数（推荐配置）
java -Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 \
     -XX:+PrintGCDetails -XX:+PrintGCTimeStamps \
     -XX:+HeapDumpOnOutOfMemoryError \
     -XX:HeapDumpPath=/app/logs/heapdump.hprof \
     -Xloggc:/app/logs/gc.log \
     -jar edc-research-api-1.0.0.jar
```

#### 2. 数据库优化
```sql
-- 索引优化
SHOW INDEX FROM research_data;

-- 查询优化
EXPLAIN SELECT * FROM research_data WHERE patient_id = 'P001';

-- 连接池优化
SHOW VARIABLES LIKE 'max_connections';
```

#### 3. 缓存优化
```yaml
# Redis缓存配置
spring:
  cache:
    type: redis
    redis:
      time-to-live: 600000
      cache-null-values: false
```

## 📄 许可证

本项目为专有软件，版权归EDC开发团队所有。未经授权不得复制、分发或修改。

## 🤝 贡献指南

### 开发团队

- **项目负责人**: EDC开发团队
- **技术架构**: Spring Boot + MySQL + Redis
- **代码维护**: Augment Agent

### 贡献流程

1. Fork项目到个人仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

### 代码审查标准

- ✅ 代码符合项目规范
- ✅ 包含完整的单元测试
- ✅ 通过所有CI/CD检查
- ✅ 文档更新完整
- ✅ 性能影响评估

## 📞 支持与联系

### 技术支持

- **文档中心**: `docs/` 目录
- **API文档**: http://localhost:8095/swagger-ui/index.html
- **问题反馈**: 通过项目Issue系统

### 更新日志

查看 `docs/CHANGELOG.md` 了解版本更新历史。

### 相关链接

- **Spring Boot官方文档**: https://spring.io/projects/spring-boot
- **MySQL官方文档**: https://dev.mysql.com/doc/
- **Maven官方文档**: https://maven.apache.org/guides/

---

**最后更新**: 2025-07-05
**文档版本**: v2.0.0
**项目版本**: v1.0.0